{"name": "bakedbot", "private": true, "workspaces": ["apps/*"], "devDependencies": {"lerna": "~6.6.1"}, "scripts": {"start": "lerna run start", "build": "lerna run build", "lint": "lerna run lint", "test": "lerna run test", "docker:build": "lerna run docker:build", "docker:build:push": "lerna run docker:build:push", "analyze:ui:bundle": "lerna run build && npx source-map-explorer './apps/ui/build/static/js/*.js'", "package:build": "lerna run package:build", "package:publish": "lerna run package:publish", "dev": "docker compose -f docker-compose.yml -f docker-compose.dev.yml up", "dev:build": "docker compose -f docker-compose.yml -f docker-compose.dev.yml up --build"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "react-hook-form": "^7.54.0", "zod": "^3.23.8"}}