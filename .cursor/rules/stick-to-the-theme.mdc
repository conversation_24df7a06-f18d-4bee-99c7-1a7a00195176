---
description: 
globs: *.tsx
alwaysApply: false
---
we already have a robust theme with font colors, backgrounds and light/dark mode. any changes to the UI should obey these and not make up new stuff or inline css unless absolutley required or requested.

[index.css](mdc:apps/ui/src/index.css) [AppLayout.css](mdc:apps/ui/src/views/layouts/AppLayout.css) 

also there are many ui components in our components folder. Always use those first before creating new ones.