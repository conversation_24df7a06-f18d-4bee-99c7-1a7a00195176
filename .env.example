APP_SECRET=Ck7R453k4j5k43ddddddd
NODE_ENV=development

DB_CLIENT=mysql2
DB_HOST=mysql
DB_USERNAME=root
DB_PASSWORD=bakedbot
DB_PORT=3306
DB_DATABASE=password

QUEUE_DRIVER=redis
REDIS_HOST=redis
REDIS_PORT=6379

GOOGLE_MAPS_API_KEY=

STORAGE_DRIVER=firebase
STORAGE_BASE_URL=https://firebasestorage.googleapis.com/v0/b/bakedbot-agents.appspot.com/o

AUTH_DRIVER=firebase
AUTH_BASIC_EMAIL=*******
AUTH_BASIC_PASSWORD=password
AUTH_BASIC_NAME=Login

AUTH_FIREBASE_NAME=Firebase
AUTH_FIREBASE_CREDENTIALS='{"type":"service_account","project_id":"bakedbot-agents","private_key_id":"dd","private_key":"-----<PERSON><PERSON><PERSON> PRIVATE KEY----- Mcxxxxx= -----<PERSON><PERSON> PRIVATE KEY-----","client_email":"*******","client_id":"101165613703949997253","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-raxn3%40bakedbot-agents.iam.gserviceaccount.com"}'

FIREBASE_PRIVATE_KEY_ID=""
FIREBASE_PRIVATE_KEY="-"
FIREBASE_CLIENT_EMAIL=*******
FIREBASE_AUTH_DOMAIN=bakedbot-agents.firebaseapp.com
FIREBASE_PROJECT_ID=bakedbot-agents
FIREBASE_STORAGE_BUCKET=bakedbot-agents.appspot.com
FIREBASE_MESSAGING_SENDER_ID=************
FIREBASE_APP_ID=1:************:web:f92bba3616f4e96cfb5313
FIREBASE_MEASUREMENT_ID=G-XNRELY75L4
REACT_APP_FIREBASE_RECAPTCHA_SITE_KEY="#*rBHYM7Rg3*gggh&GKtq"
REACT_APP_RECAPTCHA_SITE_KEY=your_recaptcha_site_key
REACT_APP_GOOGLE_MAPS_API_KEY=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M
BASE_URL=https://cannabis-marketing-chatbot-224bde0578da.herokuapp.com/api/v1

TWILIO_AUTH_TOKEN=a5445b06c4d5a8b90a7fb05eda604e73
TWILIO_ACCOUNT_SID=**********************************
TWILIO_PHONE_NUMBER=+***********
SENDGRID_API_KEY=*********************************************************************

OPENAI_API_KEY=sxxxxx
PINECONE_API_KEY=pcsk_48ooNQ_P4WkPtNoehtQB8VeYhj8qJ3AbwNjbCuhcYPYKyUM8XMH1QJQNJdWVsDcHtaU3r7
REACT_APP_AWS_ACCESS_KEY_ID=AKxxxxxx
REACT_APP_AWS_SECRET_ACCESS_KEY=4326TN+FWUA4dZ/cxcccc/Wra
REACT_APP_AWS_REGION=eu-north-1
REACT_APP_FIREBASE_APP_CHECK_DEBUG_TOKEN=true
SUPABASE_PASSWORD=Lf6LBAK$pA76dRw
SUPABASE_KEY=eyJxxxxxx
SUPABASE_URL=https://nixatetkmouteapspmth.supabase.co

# DATABASE_URL="postgresql://postgres:Lf6LBAK$*******:5432/postgres"
# DIRECT_URL="postgresql://postgres:Lf6LBAK$*******:5432/postgres"

DATABASE_URL="postgresql://postgres:postgres@127.0.0.1:54322/postgres"
DIRECT_URL="postgresql://postgres:postgres@127.0.0.1:54322/postgres"

REACT_APP_FACEBOOK_APP_ID=
REACT_APP_LINKEDIN_CLIENT_ID=
REACT_APP_STRIPE_PRICING_TABLE_ID=
REACT_APP_STRIPE_PUBLISHABLE_KEY=

STRIPE_ENDPOINT_SECRET=
STRIPE_SECRET_KEY=
# Stall interval in minutes (default: 45)
REDIS_STALLED_INTERVAL=45

# Max stall count before job fails (default: 1)  
REDIS_MAX_STALLED_COUNT=1

