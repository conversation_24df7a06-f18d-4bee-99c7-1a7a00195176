# Agent-Based Dynamic Question Generation

This system dynamically generates conversation starter questions based on the available agents and their data capabilities, instead of using hardcoded questions.

## Overview

The system analyzes:

1. **Available Agents**: Which AI agents are unlocked and available for the current location
2. **Data Availability**: What types of data each agent has access to (POS data, customer data, product data, etc.)
3. **Agent Capabilities**: What each agent specializes in (sales analysis, marketing, customer relations, etc.)

Based on this analysis, it generates contextual questions that users can actually get answers to.

## Key Components

### 1. `useAgentBasedQuestions` Hook

**Location**: `apps/ui/src/hooks/useAgentBasedQuestions.ts`

This hook:

- Fetches agent availability data from the API
- Maps available data sources to relevant question templates
- Returns personalized questions with agent attribution
- Handles loading and error states

### 2. Agent Question Templates

Each agent has predefined question templates organized by data requirements:

```typescript
{
  basic: [...],           // Always available questions
  withPosData: [...],     // Questions requiring sales data
  withCustomerData: [...], // Questions requiring customer data
  withProductData: [...],  // Questions requiring product catalog
  withCompetitorData: [...], // Questions requiring competitor data
  withCampaignData: [...]  // Questions requiring campaign data
}
```

### 3. Agent Profiles

#### SMOKEY (AI Budtender & Customer Experience)

- **Focus**: Product recommendations, customer service
- **Data**: Products, POS sales, customer preferences
- **Example Questions**:
  - "What are your most popular products this month?"
  - "Show me your best-selling edibles"

#### CRAIG (Marketing Automation)

- **Focus**: Campaign creation, marketing strategy
- **Data**: POS data, customer segments, campaign history
- **Example Questions**:
  - "Create a campaign for our top-selling products"
  - "Help me segment customers for targeted campaigns"

#### POPS (Business Intelligence & Strategy)

- **Focus**: Sales analysis, performance tracking
- **Data**: POS transactions, revenue data, customer behavior
- **Example Questions**:
  - "What are our sales trends over the last quarter?"
  - "Which days of the week are most profitable?"

#### MONEY MIKE (Financial Analytics)

- **Focus**: Profit analysis, financial planning
- **Data**: POS data, product costs, revenue metrics
- **Example Questions**:
  - "What are our profit margins by category?"
  - "Which products are most profitable?"

#### MRS. PARKER (Customer Relations)

- **Focus**: Customer loyalty, VIP management
- **Data**: Customer profiles, purchase history, engagement data
- **Example Questions**:
  - "Who are our highest-value customers?"
  - "Which customers are at risk of churning?"

#### EZAL (Market Intelligence)

- **Focus**: Competitive analysis, market trends
- **Data**: Competitor data, market pricing, product comparisons
- **Example Questions**:
  - "How do our prices compare to nearby dispensaries?"
  - "Analyze the competitive landscape in our area"

#### DEEBO (Security, Compliance & Quality Assurance)

- **Focus**: Compliance monitoring, quality control
- **Data**: Regulatory data, product testing, compliance records
- **Example Questions**:
  - "Are all our products properly tested?"
  - "Help me verify product quality standards"

## Implementation

### Step 1: Update Chat Components

Replace hardcoded question generation with the new hook:

```typescript
// Old approach
const [thoughtBubbles] = useState(generateQuestions);

// New approach
const { questions: thoughtBubbles, loading: questionsLoading } =
  useAgentBasedQuestions();
```

### Step 2: Update UI Templates

Enhanced question bubbles with agent attribution:

```jsx
{
  questionsLoading ? (
    <div className="thought-bubble loading">
      Loading personalized questions...
    </div>
  ) : (
    thoughtBubbles.map((bubble, index) => (
      <button
        key={index}
        className="thought-bubble"
        onClick={() => handleWelcomeOptionClick(bubble.prompt)}
        title={bubble.description}
      >
        <span className="bubble-text">{bubble.prompt}</span>
        {bubble.agent && (
          <span className="bubble-agent">via {bubble.agent}</span>
        )}
      </button>
    ))
  );
}
```

### Step 3: Enhanced Styling

New CSS classes support agent attribution and loading states:

- `.bubble-text` - Main question text
- `.bubble-agent` - Agent attribution
- `.loading` - Loading state styling
- Grid layouts for responsive design

## Benefits

### 1. **Contextual Relevance**

Questions are only shown if the user has the necessary data to get meaningful answers.

### 2. **Agent Discovery**

Users learn about different AI agents and their capabilities through question attribution.

### 3. **Data-Driven UX**

The interface adapts based on what data sources are connected and available.

### 4. **Reduced Frustration**

Eliminates the problem of showing questions about features the user can't access.

### 5. **Progressive Enhancement**

- Fully available agents show all question types
- Partially available agents show limited questions
- Unavailable agents are excluded

## API Dependencies

### Agent Availability Endpoint

```
GET /admin/locations/{locationId}/agents/availability
```

Returns detailed information about:

- Which agents are available/partially available/unavailable
- What data sources each agent has access to
- Current data status (record counts, missing requirements)

### Response Format

```typescript
{
  summary: { total: number, available: number, partial: number, unavailable: number },
  available: AgentAvailabilityData[],
  partial: AgentAvailabilityData[],
  unavailable: AgentAvailabilityData[],
  locationId: number
}
```

## Error Handling

The system gracefully handles various error scenarios:

1. **API Failures**: Falls back to basic generic questions
2. **No Available Agents**: Shows basic assistant questions
3. **Loading States**: Displays loading indicators
4. **Network Issues**: Shows error messages with fallback options

## Future Enhancements

### 1. **Dynamic Question Generation**

Use AI to generate questions based on specific data patterns and business context.

### 2. **Personalization**

Learn from user interactions to surface more relevant questions over time.

### 3. **Business Context**

Factor in business type, size, and industry-specific needs.

### 4. **Seasonal Relevance**

Adjust questions based on time of year, holidays, and business cycles.

### 5. **Performance Analytics**

Track which generated questions lead to successful interactions and insights.

## Usage Examples

### Basic Implementation

```typescript
import { useAgentBasedQuestions } from "./hooks/useAgentBasedQuestions";

function ChatWelcome() {
  const { questions, loading, error } = useAgentBasedQuestions();

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorFallback />;

  return (
    <div className="question-grid">
      {questions.map((q) => (
        <QuestionBubble key={q.prompt} question={q} />
      ))}
    </div>
  );
}
```

### Advanced Implementation with Categories

```typescript
function CategorizedQuestions() {
  const { questions } = useAgentBasedQuestions();

  const questionsByCategory = questions.reduce((acc, q) => {
    acc[q.category] = acc[q.category] || [];
    acc[q.category].push(q);
    return acc;
  }, {});

  return (
    <div>
      {Object.entries(questionsByCategory).map(
        ([category, categoryQuestions]) => (
          <QuestionCategory
            key={category}
            title={category}
            questions={categoryQuestions}
          />
        )
      )}
    </div>
  );
}
```

This system transforms static conversation starters into a dynamic, intelligent system that adapts to each user's specific data and capabilities.
