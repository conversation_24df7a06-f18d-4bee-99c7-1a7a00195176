# BakedBot – Product Requirements Document (PRD)

_Last Updated: May 2025_

---

## Table of Contents

1. [Product Overview](#product-overview)
2. [Target Market & Stakeholders](#target-market--stakeholders)
3. [Product Features](#product-features)
   - 3.1 AI Agents (LangChain-Powered)
   - 3.2 Campaign Management
   - 3.3 Journey Automation
   - 3.4 Data Integration & Normalization
   - 3.5 Chat Interface & Functionality
   - 3.6 Compliance & Security
4. [Technical Requirements](#technical-requirements)
5. [External Data Integration Requirements](#external-data-integration-requirements)
6. [AI Integration & Agent Capabilities](#ai-integration--agent-capabilities)
7. [Data Storage & Management](#data-storage--management)
8. [Additional Development Considerations](#additional-development-considerations)
9. [Timeline & Milestones](#timeline--milestones)
10. [Metrics, Analytics & Success Criteria](#metrics--analytics--success-criteria)
11. [Risks & Mitigation Strategies](#risks--mitigation-strategies)
12. [Appendix: Useful Links & Resources](#appendix-useful-links--resources)

---

## 1. Product Overview

**BakedBot** is an AI-powered commerce engine designed to automate and optimize cannabis retail operations. The system consists of 9 specialized AI agents, each responsible for different aspects of the business, working together to create a fully autonomous commerce engine.

---

## 2. Target Market & Stakeholders

**Target Market**:

- **Primary**: Cannabis dispensaries and retail stores
- **Secondary**: Cannabis brands and manufacturers
- **Tertiary**: Cannabis-adjacent businesses (accessories, CBD products)

**Key Stakeholders**:

- Dispensary owners and managers
- Marketing teams
- Compliance officers
- Customer service representatives
- IT administrators

---

## 3. Product Features

### 3.1 AI Agents (LangChain-Powered)

Each agent capability is built using LangChain and OpenAI models. They interact with users via a chat interface, interpreting natural language queries and dynamically retrieving structured data from integrated sources and internal databases using tools like SQL agents and vector search for RAG. Agents can also trigger specialized workflows like marketing plan generation (using CrewAI) and image generation.

While the underlying system uses a dynamic LangChain agent framework that selects the appropriate tools and logic based on user intent, the following conceptual agent personas guide the development of specific capabilities and represent different functional areas of the platform:

#### 🌿 Smokey - AI Budtender & Customer Experience

- **Purpose**: Interact with customers, offer personalized product recommendations, and deliver educational content.
- **Key Features**:
  - Real-time product recommendations using AI-driven queries.
  - Educational content on product effects.
  - Inventory-aware suggestions with age and location compliance.
- **External Data Requirements**:
  - **POS Product Catalog Export (ProductCatalog.csv)**
    _Minimum Columns_: `ProductID`, `ProductName`, `Category`, `Price`, `InventoryCount`, `ImageURL`, `Description`, `THC_Percentage`, `CBD_Percentage`
  - **POS Sales Transaction Export (POS_Transactions.csv)**
    _Minimum Columns_: `TransactionID`, `CustomerID`, `ProductID`, `TransactionDate`, `Quantity`, `TotalPrice`
  - **Customer Profile API/Export from CRM**
    _Minimum Data_: `CustomerID`, `Name`, `Email`, `Age`, `Location`, `PurchaseHistory`

#### 📈 Craig - Marketing Automation

- **Purpose**: Assist with creating, managing, and optimizing multi-channel marketing campaigns.
- **Key Features**:
  - AI-generated campaign content suggestions with compliance checks.
  - Performance analysis and strategic insights from AI based on queried data.
  - Support for multi-variant testing idea generation.
- **External Data Requirements**:
  - **Campaign Performance Report (CampaignReports.csv)**
    _Minimum Columns_: `CampaignID`, `CampaignName`, `StartDate`, `EndDate`, `Impressions`, `ClickThroughRate`, `ConversionRate`, `Revenue`
  - **Social Media Engagement Export (SocialMediaEngagement.csv)**
    _Minimum Columns_: `PostID`, `Platform`, `Likes`, `Shares`, `Comments`, `EngagementRate`
  - **Email Marketing API/Export**
    _Minimum Data_: `EmailID`, `SendDate`, `OpenRate`, `ClickRate`, `BounceRate`

#### 🛡️ Deebo - Compliance Guardian

- **Purpose**: Answer queries regarding compliance, security, and quality based on ingested data.
- **Key Features**:
  - Queries on compliance and security status.
  - Queries related to risk and quality assessments.
  - Analysis of lab results and license data via AI.
- **External Data Requirements**:
  - **Compliance Reports Export (ComplianceReports.csv)**
    _Minimum Columns_: `ReportID`, `Date`, `ComplianceStatus`, `IssuesFound`, `ResolutionStatus`
  - **Lab Results Export (LabResults.csv)**
    _Minimum Columns_: `TestID`, `ProductID`, `LabName`, `TestDate`, `THCContent`, `CBDContent`, `Contaminants`
  - **Regulatory Data Export (RegulatoryData.csv)**
    _Minimum Columns_: `LicenseID`, `CompanyID`, `IssueDate`, `ExpiryDate`, `Status`, `Violations`

#### 📊 Pops - Business Intelligence & Strategy

- **Purpose**: Analyze sales and operational performance based on ingested data.
- **Key Features**:
  - Sales trend analysis and performance tracking queries.
  - Staff optimization and operational insights queries.
  - AI-powered strategic planning support based on data analysis.
- **External Data Requirements**:
  - **Sales Data Export (SalesData.csv)**
    _Minimum Columns_: `SaleID`, `Date`, `ProductID`, `QuantitySold`, `Revenue`, `CustomerID`
  - **Operational Metrics Export (OperationsMetrics.csv)**
    _Minimum Columns_: `StoreID`, `Date`, `StaffCount`, `AverageTransactionTime`, `TotalSalesVolume`
  - **Financial Metrics API/Export**
    _Minimum Data_: `MetricID`, `Date`, `Value`, `MetricType`

#### 🎯 Ezal - Market Intelligence

- **Purpose**: Provide competitive insights and market trend analysis from ingested data.
- **Key Features**:
  - Competitor analysis and pricing optimization queries.
  - Identification of market trends from data.
  - AI-powered product performance insights.
- **External Data Requirements**:
  - **Competitor Data Export (CompetitorData.csv)**
    _Minimum Columns_: `CompetitorName`, `ProductID`, `Price`, `Promotions`, `MarketShare`
  - **Market Trends Export (MarketTrends.csv)**
    _Minimum Columns_: `Date`, `TrendType`, `Value`, `Region`

#### 💰 Money Mike - Financial Analytics

- **Purpose**: Deliver detailed financial analysis and forecasting based on ingested data.
- **Key Features**:
  - Margin analysis and revenue forecasting queries.
  - Cost optimization recommendations based on data.
  - AI-driven financial planning dashboards support.
- **External Data Requirements**:
  - **Financial Performance Export (FinancialData.csv)**
    _Minimum Columns_: `TransactionID`, `Date`, `Revenue`, `Cost`, `ProfitMargin`, `Category`
  - **Budget and Forecast Export (BudgetForecast.csv)**
    _Minimum Columns_: `Period`, `ForecastRevenue`, `ForecastCost`, `ForecastProfit`

#### 👑 Mrs. Parker - Customer Relations

- **Purpose**: Assist with managing VIP customer interactions and optimizing loyalty programs.
- **Key Features**:
  - VIP customer segmentation and behavior analysis queries.
  - Loyalty program optimization suggestions.
  - Personalized engagement strategies via AI insights on customer data.
- **External Data Requirements**:
  - **VIP Customer Export (VIPCustomers.csv)**
    _Minimum Columns_: `CustomerID`, `Name`, `Email`, `TotalSpend`, `PurchaseFrequency`, `LoyaltyPoints`
  - **Customer Engagement Export (CustomerEngagement.csv)**
    _Minimum Columns_: `CustomerID`, `LastInteractionDate`, `FeedbackScore`, `Preferences`
  - **POS Transaction Data** (for cross-reference)

#### 🌱 Day-Day - Seed-to-Sale & Logistics (Coming Soon)

- **Purpose**: Track cultivation, processing, and logistics end-to-end.
- **Key Features**:
  - Cultivation and harvest tracking.
  - Yield and processing analysis.
  - Logistics and shipment monitoring.
- **External Data Requirements**:
  - **Seed-to-Sale Data Export (SeedToSaleData.csv)**
    _Minimum Columns_: `BatchID`, `CultivationStart`, `HarvestDate`, `Yield`, `ProcessingDate`, `ProductID`
  - **Logistics Data Export (LogisticsData.csv)**
    _Minimum Columns_: `ShipmentID`, `Origin`, `Destination`, `DispatchDate`, `ArrivalDate`, `Quantity`, `Status`

#### 📦 Big Worm - Supply Chain (Coming Soon)

- **Purpose**: Optimize supply chain and inventory management.
- **Key Features**:
  - Real-time inventory management queries.
  - Order and vendor management queries.
  - Supply forecasting through AI-powered insights.
- **External Data Requirements**:
  - **Inventory Data Export (InventoryData.csv)**
    _Minimum Columns_: `ProductID`, `CurrentStock`, `ReorderLevel`, `SupplierID`, `LastRestockDate`
  - **Order and Vendor Data Export (OrderVendorData.csv)**
    _Minimum Columns_: `OrderID`, `ProductID`, `VendorID`, `OrderDate`, `DeliveryDate`, `Quantity`, `OrderStatus`

### 3.2 Campaign Management

- **Purpose:** Create, manage, monitor, and analyze marketing campaigns across different channels (e.g., email, potentially others).
- **Key Features:**
  - **Campaign Creation:** Define campaign goals, target audiences (using lists/segments), messages (using templates), and schedules. Supports both blast and trigger-based campaigns.
  - **Template Editor:** WYSIWYG editor for creating email templates with dynamic variable support.
  - **Delivery Tracking:** Monitor campaign send status (sent, opened, clicked, bounced).
  - **Performance Analytics:** View metrics like open rates, click rates, and potentially link to revenue or conversion data if available.
  - **User Management:** View users targeted by a specific campaign.
  - **Duplication:** Easily duplicate existing campaigns.
  - **AI Suggestions:** Potential for AI-driven campaign idea generation.

### 3.3 Journey Automation

- **Purpose:** Design and execute multi-step customer journeys based on triggers and user behavior.
- **Key Features:**
  - **Visual Editor:** Drag-and-drop interface to build automation flows.
  - **Trigger Steps:** Initiate journeys based on events (e.g., user added to list, specific user action) or schedule.
  - **Action Steps:** Send specific campaigns (emails), update user profiles, add/remove users from lists.
  - **Delay Steps:** Wait for a specified duration before proceeding.
  - **Conditional Steps (Splits):** Route users down different paths based on rules or data checks.
  - **Exit Steps:** Define the end point of a journey or specific path.
  - **Monitoring:** Track user progress through journeys, view entrance logs, and analyze step performance.
  - **AI Suggestions:** Potential for AI-driven journey idea generation.

### 3.4 Data Integration & Normalization

- **Purpose:** Ingest and standardize data from various external sources (POS, CRM, CSV uploads) for use by the platform and AI agents.
- **Key Features:**
  - **Multiple Data Source Types:** Handles POS transactions, customer profiles, product catalogs, COAs (Certificates of Analysis), reviews, and retailer information.
  - **CSV Upload:** Supports manual upload of data files (Users, POS, Products, COAs).
  - **API Integration (Conceptual):** Designed to support direct API connections (e.g., POS systems via providers), though current implementation may vary. Requires OAuth 2.0 and REST API handling.
  - **Data Normalization Service:** Maps incoming data fields (from various source formats/headers) to a standardized internal schema using predefined and AI-generated mappings (`DataNormalizationService.ts`). Handles different data types (string, number, date, boolean) and transformations.
  - **Vectorization:** Embeds relevant data (e.g., products, potentially user data) for efficient similarity search (RAG) using vector databases.
  - **Compliance Focus:** Ensures data handling adheres to regulatory requirements for the cannabis industry.

### 3.5 Chat Interface & Functionality

- **Purpose:** Provide the primary user interface for interacting with AI agents.
- **Key Features:**
  - **Real-time Messaging:** Supports sending and receiving messages with AI agents.
  - **Agent Selection:** Allows users to initiate chats with specific agents, with dynamic routing based on intent or @mentions.
  - **Chat History:** Stores and displays conversation history.
  - **Message Management:** Fetches messages with pagination.
  - **Chat Management:** Create, rename, delete, and archive chats. Chat titles can be auto-generated.
  - **Metadata:** Stores metadata associated with messages (e.g., intent, selected agent) and chats (e.g., message count, rename status).
  - **File Uploads:** Allows users to upload files within a chat context.
  - **Data Display:** Can display structured data (e.g., tables from SQL queries) and generated images within the chat.

### 3.6 Compliance & Security

- **System Architecture**:
  Microservices-based, event-driven design leveraging cloud-native technologies. **LangChain with OpenAI models** serves as the conversational AI engine. Uses **PostgreSQL** for primary data storage and **Redis** for caching and potentially queue management/rate limiting. Frontend built with **React.js**, backend with **Node.js (Koa)**.
- **Integration Requirements**:
  REST APIs, webhooks, OAuth 2.0 for secure connections. Data ingestion via CSV uploads and potentially direct API connections. Uses a **Data Normalization Service** to handle varied input schemas.
- **Performance Requirements**:
  99.9% uptime, API response times <500ms, support for 100K+ customers, real-time data processing for chat, efficient batch processing for data ingestion and vectorization.
- **Security**:
  End-to-end encryption (HTTPS), role-based access control (RBAC) at organization and location levels, secure API key management, regular security audits.

---

## 4. Technical Requirements

- **System Architecture**:  
  Microservices-based, event-driven design leveraging cloud-native technologies. **LangChain with OpenAI models** serves as the conversational AI engine. Uses **PostgreSQL** for primary data storage and **Redis** for caching and potentially queue management/rate limiting. Frontend built with **React.js**, backend with **Node.js (Koa)**.
- **Integration Requirements**:  
  REST APIs, webhooks, OAuth 2.0 for secure connections. Data ingestion via CSV uploads and potentially direct API connections. Uses a **Data Normalization Service** to handle varied input schemas.
- **Performance Requirements**:  
  99.9% uptime, API response times <500ms, support for 100K+ customers, real-time data processing for chat, efficient batch processing for data ingestion and vectorization.
- **Security**:  
  End-to-end encryption, role-based access control, secure API key management, and regular security audits.

---

## 5. External Data Integration Requirements

BakedBot requires specific data feeds (CSV exports or API endpoints) from customer systems (POS, CRM, etc.) to populate its internal data stores and enable agent functionalities. Key data types include:

- **POS/Sales Data:** Transaction details, product IDs, customer IDs, dates, quantities, prices, discounts, taxes.
- **Customer Data:** Profiles with IDs, contact info (email/phone), names, demographics (DOB), purchase history, loyalty status.
- **Product Catalog:** Product details including IDs (SKU), names, descriptions, categories, pricing, inventory levels, THC/CBD content, images.
- **Campaign/Marketing Data:** Performance metrics for external campaigns (if applicable).
- **Compliance Data:** Lab results (COAs), license information, regulatory reports.
- **Competitor Data:** Information on competitor products, pricing, and promotions.
  _(Refer to `DataNormalizationService.ts` for detailed field mappings)_

---

## 6. AI Integration & Agent Capabilities

The platform utilizes LangChain and OpenAI models for its AI capabilities, integrated primarily through `ChatAIServiceWithLangChain.ts`.

### Key AI Components:

- **Language Models:** Primarily uses OpenAI models (e.g., GPT-4, GPT-3.5-turbo) for generation, intent analysis, and tool usage.
- **LangChain Framework:** Orchestrates interactions between LLMs, tools, data sources, and memory.
- **Intent Analysis:** Determines the user's goal (e.g., `marketing_campaign`, `image_generation`, `sql_query`, `vector_search`, `general_query`).
- **Agent Selection/Routing:** Dynamically selects the most appropriate agent/tool based on intent or direct mentions (@agent_name).
- **Tools:**
  - **SQL Agent:** Executes SQL queries against the PostgreSQL database (using `createSqlAgent` or similar LangChain toolkit) to answer data-related questions. Requires careful schema definition and potentially query validation.
  - **Vector Search (RAG):** Performs similarity searches against vectorized data (e.g., products, documents) to retrieve relevant context for answering questions.
  - **CrewAI Integration:** Delegates complex task planning (e.g., marketing campaign generation) to a specialized CrewAI setup.
  - **Image Generation:** Integrates with image generation services (e.g., OpenAI DALL-E, potentially others like Ideogram) via `ImageGenerationService.ts`.
  - **Custom Tools:** Potential for other custom tools to interact with specific platform features or external APIs.
- **Memory:** Uses database storage (PostgreSQL) for long-term conversation history and potentially Redis for short-term session context/caching.

### Code Structure Example (Conceptual):

```typescript
// apps/platform/src/chats/ChatAIServiceWithLangChain.ts

import { OpenAI } from "openai";
import { SqlDatabase } from "langchain/sql_db";
// ... other LangChain imports (Agents, Tools, Chains, Prompts, VectorStores)

export class ChatAIServiceWithLangChain {
  private openai: OpenAI;
  private sqlAgentExecutor?: AgentExecutor; // Example: SQL Agent
  private vectorStore?: VectorStore; // Example: RAG

  constructor(dbConfig: any, vectorStoreConfig: any) {
    this.openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
    // Initialize SQL Database connection
    const db = SqlDatabase.fromKnex(App.main.db);
    // Initialize SQL Agent Executor
    // Initialize Vector Store
  }

  async analyzeIntent(message: string): Promise<IntentAnalysis> {
    // Use LLM or specialized model to classify intent and extract entities
  }

  async selectAgent(
    intent: IntentAnalysis,
    chatId: number,
    availableAgents: Agent[]
  ): Promise<Agent> {
    // Logic to route to the best agent based on intent, history, or mentions
  }

  async generateResponse(
    chat: Chat,
    userMessage: Message,
    agent: Agent
  ): Promise<string> {
    // Retrieve history
    const history = await getChatHistory(chat.chat_id);

    // Determine required tools based on agent/intent
    // Prepare context (RAG, SQL query results)
    const context = await this.getContext(userMessage.content, agent);

    // Invoke the appropriate LangChain chain/agent (e.g., LLMChain, AgentExecutor)
    const response = await this.langchainAgent.run({
      input: userMessage.content,
      chat_history: history,
      context: context,
      // other agent-specific inputs
    });

    return response;
  }

  async getContext(query: string, agent: Agent): Promise<any> {
    // Fetch data using SQL agent or Vector Search based on agent capabilities/query
  }

  async generateTitle(messages: Message[]): Promise<string> {
    // Use LLM to summarize conversation into a title
  }

  // ... other methods like SQL execution, vector search
}
```

---

## 7. Data Storage & Management

### 7.1 Primary Data Store (PostgreSQL)

- **Technology:** PostgreSQL, managed via Knex.js ORM/Query Builder.
- **Usage:**
  - Stores core application data: users, locations, organizations, campaigns, journeys, lists, templates, products, chats, messages, agents, integrations, API keys, etc.
  - Long-term storage for conversation history and metadata.
  - Backend for the LangChain SQL Agent, allowing AI to query structured data.
  - Stores normalized data ingested from external sources (POS, CRM, etc.).
  - Stores COA data, retailer data, review data.

### 7.2 Caching & Short-Term Storage (Redis)

- **Technology:** Redis, accessed via `ioredis` client.
- **Usage:**
  - **Caching:** Frequently accessed data like list counts (`ListService.ts`), potentially chat summaries (`ChatAnalyticsService.ts`).
  - **Rate Limiting:** Used by the rate limiting module (`rateLimit.ts`).
  - **Queue Backend:** Can be configured as the backend for the job queue (`Queue.ts`, `RedisQueueProvider.ts`).
  - **Stats:** Temporary storage for application statistics (`stats.ts`).
  - Potentially short-term conversation state/context (though primary history is in PostgreSQL).

### 7.3 Vector Store

- **Technology:** Specific implementation details need confirmation (e.g., pgvector extension within PostgreSQL, a separate vector database like Pinecone/Weaviate).
- **Usage:** Stores vector embeddings of data (products, documents, potentially user data) to enable efficient semantic search for the RAG (Retrieval-Augmented Generation) pattern used by AI agents. Managed through services like `PosDataAnalyticsController.ts` and potentially `ProductService.ts`.

### 7.4 File Storage

- **Technology:** Local filesystem (`uploads/` directory structured by location/chat) and potentially a cloud storage provider (like Supabase Storage, S3 - needs confirmation based on `StorageProvider` implementation).
- **Usage:** Stores user-uploaded files (e.g., via chat, CSV imports) and potentially AI-generated images (`ImageDownloadService.ts`).

---

## 8. Additional Development Considerations

- **Data Contracts & Schema Documentation**:  
  Define JSON Schema files for all API data exchanges and CSV exports to ensure consistency between customer systems and BakedBot.

- **Error Handling & Logging**:  
  Implement centralized error handlers (e.g., using Winston or Bunyan for Node.js) and integrate monitoring tools like Datadog or Sentry.

- **Security Best Practices**:  
  Use HTTPS, secure API keys via environment variables or secret management (Azure Key Vault, HashiCorp Vault), and perform regular security audits.

- **Testing Strategy**:  
  Create unit tests for backend functions (e.g., file processing, API integration), integration tests for external endpoints, and end-to-end tests for user flows in React.

- **Scalability & Performance**:  
  Use load testing (Artillery, JMeter), caching strategies, and design your services for horizontal scaling (e.g., with Kubernetes or Docker Swarm).

- **CI/CD and Deployment**:  
  Automate builds and deployments with GitHub Actions, Azure DevOps, or Jenkins. Ensure pipelines include tests and security scans.

- **Developer Documentation & Portal**:  
  Maintain comprehensive API documentation (Swagger/OpenAPI) and provide onboarding guides and usage examples.

- **Monitoring & Analytics**:  
  Set up dashboards to monitor API performance, error rates, and user engagement metrics. Utilize tools like Prometheus, Grafana, or Azure Monitor.

---

## 9. Timeline & Milestones

**Development Phases**:

1.  **Core Platform & ~~Genie API~~ LangChain Integration:** (Completed or In Progress)
2.  **Campaign & Journey Feature Development:** (Completed or In Progress)
3.  **Agentic Workflow Enhancement:** (Upcoming - Current Focus)
4.  **Data Ingestion Optimization:** (Upcoming - Current Focus)
5.  Beta Testing & Feedback Integration
6.  Public Launch
7.  Ongoing Enhancements

**Key Milestones**: _(Adjust dates as needed)_

- **Initial LangChain Implementation:** QX 202X
- **Campaign/Journey Features MVP:** QX 202X
- **Agentic Workflow Refactor:** QX 202X
- **Data Ingestion Improvements:** QX 202X
- **Beta Launch:** TBD
- **Public Release:** TBD

---

## 10. Metrics, Analytics & Success Criteria

**Business Metrics**:

- Increase customer engagement by 30%
- Reduce campaign creation time by 50%
- Improve conversion rates by 25%
- Maintain 100% compliance rate

**Technical Metrics**:

- 99.9% system uptime
- API response times <500ms (excluding long AI generations)
- Zero critical security breaches
- High data accuracy for ingested and normalized data
- Efficient AI response times and query execution (SQL/Vector Search)
- Scalability of data ingestion and vectorization processes

---

## 11. Risks & Mitigation Strategies

**Technical Risks**:

- Data security breaches
- Scalability issues (Database, AI Models, Data Ingestion)
- **Dependency on OpenAI API performance and availability**
- **Complexity of LangChain agent/tool management**
- **Accuracy and reliability of SQL Agent and RAG results**
- Real-time processing bottlenecks (Chat, Journey processing)
- Data normalization errors for new/unseen data formats
- Vector database performance and cost

**Business Risks**:

- Regulatory changes in cannabis marketing
- Increased market competition
- User adoption challenges
- Compliance violations

**Mitigation Strategies**:

- Regular security audits and continuous monitoring
- Scalable, cloud-native architecture (monitor DB/Redis/Service performance)
- Comprehensive testing (Unit, Integration, E2E) including AI evaluation
- **Fallback mechanisms for AI service failures**
- **Robust error handling and monitoring for LangChain agents/tools**
- **Fine-tuning prompts and agent logic for accuracy**
- Optimize data ingestion pipelines and vectorization strategies
- **Consider alternative LLM providers or self-hosting options**
- Close collaboration with data source providers

---

## 12. Appendix: Useful Links & Resources

- **LangChain Documentation:** [https://python.langchain.com/docs/get_started/introduction](https://python.langchain.com/docs/get_started/introduction)
- **OpenAI API Documentation:** [https://platform.openai.com/docs](https://platform.openai.com/docs)
- **CrewAI Documentation:** [https://docs.crewai.com/](https://docs.crewai.com/)
- **PostgreSQL Documentation:** [https://www.postgresql.org/docs/](https://www.postgresql.org/docs/)
- **Redis Documentation:** [https://redis.io/docs/](https://redis.io/docs/)
- **KoaJS Documentation:** [https://koajs.com/](https://koajs.com/)
- **ReactJS Documentation:** [https://react.dev/](https://react.dev/)
- **KnexJS Documentation:** [https://knexjs.org/](https://knexjs.org/)
- **Internal API Documentation:** (Link to Swagger/OpenAPI if available)
- **Node.js error logging:** [Winston](https://github.com/winstonjs/winston)
- **React state management:** [Redux](https://redux.js.org/), [Context API](https://reactjs.org/docs/context.html), Zustand (confirm usage)

_(Removed outdated Genie API/Supabase-specific links)_
