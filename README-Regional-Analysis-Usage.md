# Regional Market Analysis Tools - Usage Guide

## Overview

The system now includes powerful regional market analysis capabilities that extend beyond individual competitor analysis to provide comprehensive geographic market intelligence. These tools help users understand market conditions across cities, states, and radius-based geographic areas.

## Available Tools & Integration Points

### 1. Chat Interface (AI Tools)

The regional analysis tools are integrated into the ReactAgentService and available through conversational AI:

#### RegionalMarketAnalysisByLocationTool

**Usage in Chat:**

- "Analyze the Detroit cannabis market"
- "What's the market like in California?"
- "Show me pricing trends in Michigan"
- "Compare the Denver market to my current pricing"

**Capabilities:**

- City-wide or state-wide market analysis
- Regional pricing trends across categories
- Competitor density analysis
- Market opportunities identification
- Geographic market positioning insights

#### RegionalMarketAnalysisByRadiusTool

**Usage in Chat:**

- "Find competitors within 25 miles of me"
- "Analyze the market within 50 miles of coordinates 42.3314, -83.0458"
- "What's the competitive landscape within a 30-mile radius?"
- "Show me distance-based pricing analysis"

**Capabilities:**

- Distance-based competitive analysis
- Proximity insights and geographic density patterns
- Location-specific market opportunities
- Distance-aware pricing strategies

### 2. API Endpoints (Direct Access)

#### Location-Based Analysis

```http
POST /api/misc/regional-analysis/location
Content-Type: application/json

{
  "city": "Detroit",
  "state": "Michigan",
  "userRetailerId": "your_retailer_id",
  "maxRetailers": 50
}
```

#### Radius-Based Analysis

```http
POST /api/misc/regional-analysis/radius
Content-Type: application/json

{
  "lat": 42.3314,
  "lng": -83.0458,
  "radius": 30,
  "userRetailerId": "your_retailer_id",
  "maxRetailers": 50
}
```

### 3. Service Layer Integration

```typescript
// Through CompetitorService
const competitorService = new CompetitorService();

// Location-based analysis
const locationAnalysis =
  await competitorService.getRegionalMarketAnalysisByLocation(
    "Detroit",
    "Michigan",
    "user_retailer_id",
    50
  );

// Radius-based analysis
const radiusAnalysis =
  await competitorService.getRegionalMarketAnalysisByRadius(
    42.3314,
    -83.0458,
    30,
    "user_retailer_id",
    50
  );
```

## Enhanced Chat Experience

### New AI Capabilities

The ReactAgentService now includes enhanced prompts that understand regional analysis queries:

```
REGIONAL MARKET ANALYSIS: You have access to powerful regional analysis tools:
- Use 'RegionalMarketAnalysisByLocationTool' for city/state-wide analysis
- Use 'RegionalMarketAnalysisByRadiusTool' for radius-based analysis
These tools provide regional pricing trends, competitor density, market opportunities, and geographic insights.
```

### Example Conversational Queries

**Geographic Market Intelligence:**

- "How does the Detroit market compare to the broader Michigan market?"
- "What are the pricing trends in California's cannabis market?"
- "Analyze competitor density in my local area"

**Distance-Based Analysis:**

- "Who are my closest competitors and what are they charging?"
- "Find all dispensaries within 15 miles and their pricing strategies"
- "Analyze market saturation in a 25-mile radius"

**Strategic Planning:**

- "Should I expand to the Denver market?"
- "What categories have the most competition in my area?"
- "Where are the market gaps in regional pricing?"

## Data Structure & Response Format

### Location Analysis Response

```json
{
  "analysisType": "regional-location",
  "location": {
    "city": "Detroit",
    "state": "Michigan"
  },
  "competitorCount": 15,
  "hotCategories": [
    {
      "category": "Flower",
      "weight": "3.5g",
      "marketAvg": 35.5,
      "marketMin": 25.0,
      "marketMax": 45.0,
      "retailerCount": 12,
      "productCount": 89
    }
  ],
  "regionStats": {
    "totalRetailers": 15,
    "totalProducts": 1247,
    "citiesAnalyzed": ["Detroit", "Warren", "Sterling Heights"],
    "topRetailers": [
      {
        "retailerId": "123",
        "name": "Green Co",
        "productCount": 89
      }
    ]
  },
  "insights": [
    "Analyzed 1,247 products from 15 retailers across 3 cities in Michigan.",
    "Detroit shows competitive pricing in Flower categories."
  ],
  "recommendations": [
    "Regional analysis provides broader market context for pricing decisions.",
    "Consider the price ranges across different cities for competitive positioning."
  ]
}
```

### Radius Analysis Response

```json
{
  "analysisType": "regional-radius",
  "location": {
    "latitude": 42.3314,
    "longitude": -83.0458,
    "radiusMiles": 30
  },
  "competitorCount": 8,
  "hotCategories": [
    {
      "category": "Vapes",
      "weight": "1g",
      "marketAvg": 45.0,
      "averageDistance": 18.5,
      "closestDistance": 5.2,
      "retailerCount": 6
    }
  ],
  "regionStats": {
    "totalRetailers": 8,
    "totalProducts": 456,
    "averageDistance": 18.5,
    "closestRetailer": {
      "name": "Local Dispensary",
      "distance": 5.2,
      "city": "Ann Arbor"
    },
    "farthestRetailer": {
      "name": "Edge Store",
      "distance": 29.8,
      "city": "Lansing"
    }
  },
  "insights": [
    "Analyzed 456 products from 8 retailers within 30 miles.",
    "Average distance to competitors: 18.5 miles.",
    "Closest competitor: Local Dispensary at 5.2 miles."
  ],
  "recommendations": [
    "Radius-based analysis helps understand your immediate competitive landscape.",
    "Consider both distance and pricing when setting competitive strategies."
  ]
}
```

## Business Value & Use Cases

### Strategic Market Planning

1. **Market Entry Decisions**: Analyze potential markets before expansion
2. **Competitive Positioning**: Understand regional pricing dynamics
3. **Location Planning**: Find optimal locations with distance-based analysis

### Pricing Strategy

1. **Regional Pricing**: Set competitive prices based on local market conditions
2. **Distance Premium**: Adjust pricing based on competitor proximity
3. **Market Gaps**: Identify underserved categories or price points

### Growth Opportunities

1. **Geographic Expansion**: Identify markets with limited competition
2. **Category Focus**: Find categories with strong regional demand
3. **Partnership Opportunities**: Locate potential retail partners

## Performance Optimizations

### Database Optimizations

- Geographic indexing for fast location-based queries
- Optimized distance calculations using Haversine formula
- Query limits and timeouts to prevent performance issues
- Incremental database setup to handle large datasets

### Scalability Features

- Configurable retailer limits (1-100)
- Radius constraints (1-500 miles for location, 1-100 for optimized queries)
- Retry logic and error handling
- Caching support in SupabaseService

## Integration Examples

### Chat-Based Market Research

```
User: "I'm thinking about opening a dispensary in Detroit. What's the market like?"

AI: Using RegionalMarketAnalysisByLocationTool...
"Based on the Detroit market analysis, I found 15 competitors across 3 cities with strong competition in Flower categories ($25-45 range). The market shows healthy diversity with opportunities in premium Concentrates. Would you like me to analyze a specific radius around your proposed location?"
```

### Location-Specific Competitive Intelligence

```
User: "Show me all competitors within 20 miles and their pricing for vapes"

AI: Using RegionalMarketAnalysisByRadiusTool with 20-mile radius...
"I found 8 dispensaries within 20 miles. For vape cartridges (1g), pricing ranges from $35-55 with an average of $45. The closest competitor is 5.2 miles away at $42/cart. Dense competition within 15 miles suggests competitive pricing is important."
```

### Strategic Analysis Combining Tools

```
User: "Compare the Chicago market to competitors within 25 miles of my location"

AI: Using both RegionalMarketAnalysisByLocationTool for Chicago and RegionalMarketAnalysisByRadiusTool for local analysis...
"Chicago shows higher average prices ($50-65 for eighths) due to limited licenses, while your local 25-mile area has more competitive pricing ($30-45). This suggests you're in a more price-sensitive market compared to Chicago's premium positioning."
```

## Error Handling & Validation

### Input Validation

- Geographic coordinates validation (-90 to 90 lat, -180 to 180 lng)
- Radius limits (1-500 miles, optimized versions use smaller limits)
- Retailer count limits (1-100)
- Required parameters checking

### Graceful Degradation

- Empty result handling with helpful suggestions
- Timeout protection with smaller query limits
- Retry logic for database connections
- Fallback responses when data is limited

This comprehensive regional analysis system transforms the platform from competitor-specific analysis to true geographic market intelligence, enabling data-driven decisions about location, pricing, and market strategy.
