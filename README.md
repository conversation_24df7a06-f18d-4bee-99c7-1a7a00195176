# BakedBot - Cannabis Marketing Automation Platform

BakedBot is an intelligent marketing automation platform specifically designed for cannabis businesses. It leverages AI to create personalized customer journeys, analyze POS data, and optimize marketing campaigns.

## Features

- 🤖 AI-Powered Marketing Automation
- 📊 Specialized AI Agents
- 📊 POS Data Integration & Analysis
- 🎯 Customer Segmentation
- ✉️ Multi-Channel Communication (Email, SMS)
- 🛍️ Customer Journey Builder
- 📈 Performance Analytics
- 🔄 Automated Campaign Optimization
- 🔐 Compliance-Focused Design

## AI Agents

BakedBot features a suite of specialized AI agents, each designed for specific aspects of cannabis retail:

### SMOKEY

- **Role**: AI Budtender & Customer Experience
- **Capabilities**:
  - Product recommendations
  - Customer education
  - Real-time product information
  - Discount and promotion awareness
- **Integration**: Direct POS integration for accurate inventory and pricing

### CRAIG

- **Role**: Marketing Automation
- **Capabilities**:
  - Campaign creation and optimization
  - Performance analysis
  - Email content generation
  - Loyalty program optimization
  - Multi-channel campaign orchestration
- **Integration**: POS integration + Social media connections

### POPS

- **Role**: Business Intelligence & Strategy
- **Capabilities**:
  - Sales analysis and trend identification
  - Staff optimization recommendations
  - Performance tracking
  - Strategic planning and insights
- **Integration**: POS integration + Business documents

### EZAL

- **Role**: Market Intelligence
- **Capabilities**:
  - Competitor analysis
  - Market trend identification
  - Price optimization
  - Product performance analysis
  - Market positioning insights
- **Integration**: Competitor data integration

### MONEY MIKE

- **Role**: Financial Analytics
- **Capabilities**:
  - Margin analysis by category
  - Revenue forecasting
  - Cost optimization
  - Financial planning and strategy
- **Integration**: Business documents integration

### MRS. PARKER

- **Role**: Customer Relations
- **Capabilities**:
  - VIP customer analysis and management
  - Loyalty program optimization
  - Customer behavior analysis
  - Personalized engagement strategies
- **Integration**: POS integration + Company information

### DEEBO

- **Role**: Security, Compliance & Quality Assurance
- **Capabilities**:
  - Compliance monitoring and verification
  - Security protocol management
  - Quality assurance tracking
  - Risk assessment
  - Lab results analysis
  - License management
- **Integration**: Government database + Lab data integration

### DAY-DAY (Coming Soon)

- **Role**: Seed-to-Sale & Logistics Specialist
- **Capabilities**:
  - Cultivation tracking
  - Harvest management
  - Yield analysis
  - Logistics optimization
  - Batch tracking
- **Status**: Coming Soon

### BIG WORM (Coming Soon)

- **Role**: Supply Chain Management
- **Capabilities**:
  - Inventory management
  - Order optimization
  - Supply forecasting
  - Vendor management
  - Stock level monitoring
- **Status**: Coming Soon

Each agent can be unlocked by connecting relevant data sources and completing specific onboarding steps.

## Tech Stack

- **Frontend**: React, TypeScript, Framer Motion
- **Backend**: Node.js, Express
- **Database**: MySQL, Redis
- **AI/ML**: OpenAI GPT-4, DALL-E 3
- **Cloud**: Firebase (Auth, Storage, App Check)
- **Analytics**: Custom analytics engine
- **Message Delivery**: SendGrid (Email), Twilio (SMS)

## Getting Started

### Prerequisites

- Node.js 18+
- Docker and Docker Compose
- Firebase account
- OpenAI API key
- Twilio account
- SendGrid account

### Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/bakedbot.git
cd bakedbot
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env
```

Edit the `.env` file with your credentials:

- Firebase configuration
- OpenAI API key
- Twilio credentials
- SendGrid API key
- Database credentials

4. Start the development environment:

```bash
docker-compose -f docker-compose.dev.yml up --build

npm run dev
```

5. Access the application:

- Frontend: http://localhost:3000
- API: http://localhost:8080

## Project Structure

```
apps/
├── platform/           # Backend API and core services
│   ├── src/
│   │   ├── auth/      # Authentication services
│   │   ├── core/      # Core utilities and services
│   │   ├── insights/  # AI insights engine
│   │   ├── journey/   # Customer journey engine
│   │   ├── pos/       # POS data integration
│   │   └── supabase/  # Supabase integration for AI agents
│   └── tests/
├── ui/                # Frontend React application
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   ├── views/
│   │   ├── hooks/
│   │   ├── api/       # Deprecated modular API client (use api.ts instead)
│   │   ├── api.ts     # Main API client
│   │   └── ui/
│   └── tests/
```

## API Integration Structure

The application uses a consolidated API client architecture for frontend-backend communication:

### Main API Client

The primary API client is defined in `apps/ui/src/api.ts`. It includes:

- **Authentication**: Login, logout, and session management
- **Location Management**: Create, manage and configure business locations
- **POS Integration**: Connect to point-of-sale systems and import data
- **Supabase Integration**: Upload and process data for AI agent consumption
- **User Management**: Handle user accounts and permissions
- **Campaign Management**: Create and manage marketing campaigns

### Type System

All API-related types are centralized in `apps/ui/src/types.ts`, including:

- **PosConnectionResult**: Response from POS connection attempts
- **SupabaseUploadResult**: Response from Supabase data uploads
- **SearchResult**: Generic paginated search results

### Backend Implementation

The backend services are organized by feature:

- **PosController**: Handles POS system connections and data normalization
- **SupabaseService**: Manages data upload to Supabase for AI processing
- **AIAgentController**: Provides agent-specific endpoints for various business functions

## Code Consolidation

To improve maintainability, we've consolidated duplicate code:

1. Removed redundant API clients from `apps/ui/src/api/` folder that duplicated functionality in the main `api.ts`
2. Centralized type definitions in `types.ts` to avoid inconsistencies
3. Created backwards-compatibility layer in `apps/ui/src/api/index.ts` to avoid breaking existing imports

## Development Guidelines

When adding new API endpoints:

1. Define types in `apps/ui/src/types.ts`
2. Add the endpoint to the appropriate section in `apps/ui/src/api.ts`
3. Implement the corresponding controller in the backend
4. Add appropriate tests

When working with POS or Supabase integrations:

1. Use the consolidated API client through `import api from "../api"`
2. Follow the end-to-end integration patterns established in `OnboardingLocation.tsx`

## Key Features

### AI-Powered Marketing Automation

- Automated customer segmentation
- Personalized campaign generation
- Smart journey optimization
- Content generation with GPT-4
- Image generation with DALL-E 3

### Customer Journey Builder

- Visual journey editor
- Multi-channel campaigns
- A/B testing
- Trigger-based automation
- Dynamic segmentation

### Analytics & Insights

- Real-time performance metrics
- Customer behavior analysis
- Campaign performance tracking
- ROI measurement
- Compliance monitoring

## Development

### Running Tests

```bash
npm run test
```

### Building for Production

```bash
docker-compose up --build


```

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support, email <EMAIL> or join our Slack community.
