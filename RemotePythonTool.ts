import axios from "axios";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolR<PERSON>ult, RequestContext } from "./interfaces";
import { logger } from "../config/logger";

/**
 * RemotePythonTool provides a bridge to the Python microservice for advanced RAG capabilities
 * that are better suited for implementation in Python (specialized ML models, etc)
 */
export class RemotePythonTool implements Tool {
  name = "remote_python.execute";
  description = "Execute advanced RAG operations in the Python microservice";

  private baseUrl: string;

  parameters: ToolParameter[] = [
    {
      name: "operation",
      type: "string",
      description: "Operation to execute (rerank, analyze, etc.)",
      required: true,
      enum: ["rerank", "analyze", "forecast", "segment", "fine_tune"],
    },
    {
      name: "query",
      type: "string",
      description: "The query or text to process",
      required: true,
    },
    {
      name: "context",
      type: "object",
      description: "Additional context information",
      required: false,
    },
    {
      name: "parameters",
      type: "object",
      description: "Operation-specific parameters",
      required: false,
    },
  ];

  constructor(
    baseUrl: string = process.env.RAG_PYTHON_URL || "http://rag-python:8000"
  ) {
    this.baseUrl = baseUrl;
    logger.info(`RemotePythonTool initialized with baseUrl: ${this.baseUrl}`);
  }

  async execute(
    params: Record<string, unknown>,
    ctx?: RequestContext
  ): Promise<ToolResult> {
    try {
      const { operation, query, context = {}, parameters = {} } = params;

      if (!operation) {
        return {
          status: "error",
          error: "operation is required",
        };
      }

      if (!query) {
        return {
          status: "error",
          error: "query is required",
        };
      }

      // Add location context if available
      const enhancedContext = {
        ...(context as object),
        locationId: ctx?.locationId || (context as any)?.locationId,
      };

      // Log the request
      logger.info({
        message: `Calling Python service with operation: ${operation}`,
        operation,
        queryLength: typeof query === "string" ? query.length : "not a string",
        contextKeys: Object.keys(enhancedContext),
        parametersKeys: Object.keys(parameters as object),
      });

      // Make the request to the Python service
      const url = `${this.baseUrl}/api/v1/${operation}`;
      const response = await axios.post(url, {
        query,
        context: enhancedContext,
        parameters,
        locationId: ctx?.locationId,
      });

      // Process the response
      if (response.data.success) {
        return {
          status: "success",
          data: response.data.data,
          metadata: {
            operation,
            pythonService: true,
          },
        };
      } else {
        return {
          status: "error",
          error: response.data.error || "Unknown error from Python service",
        };
      }
    } catch (error) {
      logger.error({
        message: "Error executing RemotePythonTool",
        error,
        params,
      });

      // Handle specific error types
      if (axios.isAxiosError(error)) {
        if (error.code === "ECONNREFUSED") {
          return {
            status: "error",
            error: "Python service is not available. Make sure it's running.",
          };
        }

        const statusCode = error.response?.status;
        const errorMessage = error.response?.data?.error || error.message;

        return {
          status: "error",
          error: `Python service error (${statusCode}): ${errorMessage}`,
        };
      }

      return {
        status: "error",
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  // Convenience method to rerank search results
  async rerank(
    query: string,
    documents: any[],
    options: Record<string, any> = {}
  ): Promise<ToolResult> {
    return this.execute({
      operation: "rerank",
      query,
      parameters: {
        documents,
        top_k: options.top_k || 5,
        score_key: options.score_key || "score",
        content_key: options.content_key || "content",
      },
    });
  }

  // Convenience method for analysis
  async analyze(
    query: string,
    context: Record<string, any> = {},
    parameters: Record<string, any> = {}
  ): Promise<ToolResult> {
    return this.execute({
      operation: "analyze",
      query,
      context,
      parameters,
    });
  }
}
