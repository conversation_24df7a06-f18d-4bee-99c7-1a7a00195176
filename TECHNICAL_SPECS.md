# BakedBot - Technical Specifications

## 1. System Architecture

### 1.1 Overview

BakedBot follows a microservices architecture pattern with event-driven communication between services. The system is designed to be highly scalable, maintainable, and secure.

### 1.2 Core Components

```
BakedBot Platform
├── Frontend (React SPA)
├── Backend Services
│   ├── API Gateway
│   ├── Auth Service
│   ├── Journey Service
│   ├── Analytics Service
│   ├── Messaging Service
│   ├── AI Service
│   │   ├── Agent Manager
│   │   ├── SMOKEY Service
│   │   ├── CRAIG Service
│   │   ├── POPS Service
│   │   ├── EZAL Service
│   │   ├── MONEY MIKE Service
│   │   ├── MRS. PARKER Service
│   │   ├── DEEBO Service
│   │   ├── DAY-DAY Service (Coming Soon)
│   │   └── BIG WORM Service (Coming Soon)
└── External Services
    ├── OpenAI
    ├── Twilio
    └── SendGrid
```

### 1.3 AI Agent Architecture

#### 1.3.1 Agent Manager Service

```typescript
interface AgentConfig {
  id: string;
  name: string;
  role: string;
  description: string;
  examples: string[];
  requirements: {
    required: string[];
    optional: string[];
  };
  unlockCondition: (
    completedSteps: Set<string>,
    connectedData: Set<string>
  ) => "locked" | "partial" | "unlocked";
}

interface AgentContext {
  locationId: number;
  userId: number;
  connectedServices: string[];
  permissions: string[];
  dataAccess: {
    pos: boolean;
    customers: boolean;
    social: boolean;
    analytics: boolean;
  };
}

interface AgentResponse {
  success: boolean;
  data: any;
  metadata: {
    confidence: number;
    source: string;
    compliance: {
      checked: boolean;
      passed: boolean;
      warnings: string[];
    };
  };
}
```

#### 1.3.2 Agent-Specific Services

##### SMOKEY Service

```typescript
interface ProductRecommendation {
  productId: string;
  confidence: number;
  reasoning: string;
  alternatives: string[];
  compliance: ComplianceCheck;
}

interface CustomerQuery {
  intent: string;
  products: string[];
  preferences: Record<string, any>;
  constraints: {
    budget?: number;
    medical?: boolean;
    effects?: string[];
  };
}
```

##### CRAIG Service

```typescript
interface CampaignSuggestion {
  type: "email" | "sms" | "push";
  content: {
    subject?: string;
    body: string;
    images?: string[];
  };
  targeting: {
    segment: string;
    timing: string;
    expectedResponse: number;
  };
  compliance: ComplianceCheck;
}

interface MarketingAnalysis {
  performance: {
    metrics: Record<string, number>;
    trends: string[];
    recommendations: string[];
  };
  optimization: {
    suggestions: string[];
    expectedImpact: number;
  };
}
```

##### POPS Service

```typescript
interface BusinessAnalysis {
  sales: {
    trends: string[];
    topProducts: string[];
    opportunities: string[];
  };
  staffing: {
    recommendations: string[];
    schedule: Record<string, any>;
    efficiency: number;
  };
  performance: {
    metrics: Record<string, number>;
    comparisons: Record<string, number>;
  };
}
```

##### EZAL Service

```typescript
interface MarketIntelligence {
  competitors: {
    analysis: Record<string, any>;
    pricing: Record<string, number>;
    positioning: string[];
  };
  trends: {
    emerging: string[];
    declining: string[];
    opportunities: string[];
  };
  marketShare: {
    overall: number;
    byCategory: Record<string, number>;
  };
}
```

##### MONEY MIKE Service

```typescript
interface FinancialAnalysis {
  margins: {
    byCategory: Record<string, number>;
    trends: string[];
    optimization: string[];
  };
  forecasting: {
    revenue: number;
    growth: number;
    scenarios: Record<string, any>;
  };
  costs: {
    breakdown: Record<string, number>;
    optimization: string[];
  };
}
```

##### MRS. PARKER Service

```typescript
interface CustomerRelations {
  vipAnalysis: {
    segments: Record<string, any>;
    behavior: Record<string, any>;
    preferences: Record<string, any>;
  };
  loyalty: {
    programMetrics: Record<string, number>;
    recommendations: string[];
    engagement: Record<string, any>;
  };
  personalization: {
    strategies: string[];
    targeting: Record<string, any>;
  };
}
```

##### DEEBO Service

```typescript
interface ComplianceManagement {
  security: {
    protocols: Record<string, any>;
    risks: string[];
    recommendations: string[];
  };
  compliance: {
    status: Record<string, boolean>;
    violations: string[];
    actions: string[];
  };
  quality: {
    metrics: Record<string, number>;
    labResults: Record<string, any>;
    certifications: string[];
  };
}
```

##### DAY-DAY Service (Coming Soon)

```typescript
interface CultivationManagement {
  batches: {
    status: Record<string, any>;
    tracking: Record<string, any>;
    yield: Record<string, number>;
  };
  harvests: {
    schedule: Record<string, Date>;
    metrics: Record<string, number>;
    quality: Record<string, any>;
  };
  logistics: {
    planning: Record<string, any>;
    optimization: string[];
  };
}
```

##### BIG WORM Service (Coming Soon)

```typescript
interface SupplyChainManagement {
  inventory: {
    levels: Record<string, number>;
    alerts: string[];
    optimization: string[];
  };
  orders: {
    tracking: Record<string, any>;
    forecasting: Record<string, number>;
    recommendations: string[];
  };
  vendors: {
    performance: Record<string, number>;
    management: Record<string, any>;
  };
}
```

#### 1.3.3 Agent Communication Protocol

```typescript
interface AgentMessage {
  type: "request" | "response" | "event";
  agentId: string;
  sessionId: string;
  timestamp: number;
  payload: {
    action: string;
    parameters: Record<string, any>;
    context: AgentContext;
  };
  metadata: {
    version: string;
    trace_id: string;
  };
}
```

#### 1.3.4 Agent State Management

```typescript
interface AgentState {
  status: "active" | "learning" | "disabled";
  lastSync: Date;
  performance: {
    requests: number;
    successRate: number;
    avgResponseTime: number;
  };
  modelVersion: string;
  dataUpdates: {
    pos: Date;
    customers: Date;
    products: Date;
  };
}
```

## 2. Technology Stack

### 2.1 Frontend

- **Framework**: React 18+
- **Language**: TypeScript 4.9+
- **State Management**: React Context + Hooks
- **UI Components**: Custom component library
- **Styling**: CSS Modules + Tailwind CSS
- **Build Tool**: Vite
- **Testing**: Jest + React Testing Library

### 2.2 Backend

- **Runtime**: Node.js 18+
- **Framework**: Express
- **Language**: TypeScript 4.9+
- **API Style**: REST + WebSocket
- **Authentication**: Firebase Auth + JWT
- **Validation**: Zod
- **Testing**: Jest

### 2.3 Data Storage

- **Primary Database**: MySQL 8.0
- **Caching**: Redis 6.2
- **File Storage**: Firebase Storage
- **Search**: Elasticsearch

### 2.4 Infrastructure

- **Containerization**: Docker
- **Orchestration**: Kubernetes
- **CI/CD**: GitHub Actions
- **Monitoring**: Prometheus + Grafana
- **Logging**: ELK Stack

## 3. Database Schema

### 3.1 Core Tables

```sql
-- Users
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    external_id VARCHAR(255) UNIQUE,
    email VARCHAR(255),
    phone VARCHAR(50),
    timezone VARCHAR(50),
    locale VARCHAR(10),
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Journeys
CREATE TABLE journeys (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    location_id BIGINT,
    name VARCHAR(255),
    description TEXT,
    published BOOLEAN,
    tags JSON,
    stats JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Journey Steps
CREATE TABLE journey_steps (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    journey_id BIGINT,
    type VARCHAR(50),
    name VARCHAR(255),
    external_id VARCHAR(255),
    data JSON,
    data_key VARCHAR(255),
    x INT,
    y INT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

## 4. API Specifications

### 4.1 REST API Endpoints

#### Authentication

```
POST /api/v1/auth/login
POST /api/v1/auth/refresh
POST /api/v1/auth/logout
```

#### Journeys

```
GET    /api/v1/journeys
POST   /api/v1/journeys
GET    /api/v1/journeys/:id
PUT    /api/v1/journeys/:id
DELETE /api/v1/journeys/:id
```

#### Analytics

```
GET /api/v1/analytics/performance
GET /api/v1/analytics/customers
GET /api/v1/analytics/campaigns
```

### 4.2 WebSocket Events

```typescript
interface WebSocketEvents {
  "journey.started": { journeyId: string; userId: string };
  "journey.completed": { journeyId: string; userId: string };
  "step.executed": { stepId: string; userId: string; result: any };
  "message.sent": { messageId: string; channel: string; userId: string };
}
```

## 5. Security Specifications

### 5.1 Authentication

- Firebase Authentication
- JWT token-based API authentication
- Refresh token rotation
- Rate limiting
- App Check verification

### 5.2 Authorization

- Role-based access control (RBAC)
- Resource-level permissions
- API scope restrictions
- IP whitelisting support

### 5.3 Data Security

- End-to-end encryption for sensitive data
- At-rest encryption for databases
- Secure key management
- Regular security audits

## 6. Integration Specifications

### 6.1 Third-Party Services

#### OpenAI Integration

```typescript
interface AIServiceConfig {
  apiKey: string;
  model: "gpt-4" | "gpt-4.1-mini";
  temperature: number;
  maxTokens: number;
}
```

#### Twilio Integration

```typescript
interface SMSConfig {
  accountSid: string;
  authToken: string;
  phoneNumber: string;
  messageOptions: {
    statusCallback: string;
    attemptLimit: number;
  };
}
```

### 6.2 Webhook Specifications

```typescript
interface WebhookPayload {
  event: string;
  timestamp: number;
  data: {
    id: string;
    type: string;
    attributes: Record<string, any>;
  };
  signature: string;
}
```

## 7. Performance Requirements

### 7.1 API Performance

- Response time: < 500ms for 95th percentile
- Throughput: 1000 requests/second
- Error rate: < 0.1%

### 7.2 Scaling Metrics

- Support for 100K+ active users
- 1M+ journey steps per day
- 5M+ messages per day

### 7.3 Data Storage

- Message retention: 90 days
- Analytics data: 12 months
- User data: Indefinite

## 8. Monitoring & Logging

### 8.1 System Metrics

- CPU usage
- Memory utilization
- Network I/O
- Error rates
- Response times

### 8.2 Business Metrics

- Active users
- Message delivery rates
- Journey completion rates
- Conversion rates

### 8.3 Log Levels

```typescript
enum LogLevel {
  ERROR = "error",
  WARN = "warn",
  INFO = "info",
  DEBUG = "debug",
  TRACE = "trace",
}
```

## 9. Deployment Strategy

### 9.1 Environments

- Development
- Staging
- Production
- Disaster Recovery

### 9.2 CI/CD Pipeline

1. Code commit
2. Automated tests
3. Build artifacts
4. Security scan
5. Staging deployment
6. Integration tests
7. Production deployment

### 9.3 Rollback Procedure

1. Automated failure detection
2. Traffic routing to previous version
3. State reconciliation
4. Incident reporting

## 10. Testing Strategy

### 10.1 Test Types

- Unit tests
- Integration tests
- End-to-end tests
- Performance tests
- Security tests

### 10.2 Coverage Requirements

- Unit test coverage: > 80%
- Integration test coverage: > 60%
- Critical path coverage: 100%

### 10.3 Testing Tools

- Jest
- React Testing Library
- Cypress
- k6 for performance testing
- OWASP ZAP for security testing
