{"root": true, "env": {"node": true}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "import"], "extends": ["standard", "plugin:@typescript-eslint/eslint-recommended"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-inferrable-types": "off", "@typescript-eslint/ban-types": "off", "react-hooks/rules-of-hooks": "off", "no-unused-vars": "off", "no-trailing-spaces": "off", "no-empty": "off", "quotes": "off", "semi": "off", "comma-dangle": "off", "camelcase": "off", "space-before-function-paren": "off", "no-use-before-define": "off"}, "globals": {"NodeJS": true}}