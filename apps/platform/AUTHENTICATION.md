# BakedBot API Authentication Guide

This guide explains how to authenticate with the BakedBot API, which supports dual authentication for enhanced functionality.

## Authentication Overview

The BakedBot API uses a **dual authentication system** that supports both anonymous and authenticated access:

1. **API Key Authentication** (Required) - Identifies the location/dispensary
2. **User Authentication** (Optional) - Links actions to user accounts for enhanced features

## Authentication Types

### 1. API Key Authentication (Required)

**Purpose**: Identifies the location/dispensary and provides access to public endpoints.

**Header**: `Authorization: Bearer your_location_api_key`

**Format**:

```
Authorization: Bearer sk_live_abc123def456ghi789...
```

**Required for**: All public endpoints (`/public/products/*`, `/orders/checkout`)

### 2. User Authentication (Optional)

**Purpose**: Links actions to user accounts for enhanced features like chat history and order tracking.

**Header**: `x-user-token: user_jwt_token`

**Format**:

```
x-user-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Benefits**: Chat history persistence, saved chats, order tracking, personalized recommendations

## Usage Examples

### Anonymous Access (API Key Only)

For basic functionality without user account linking:

```bash
curl -H "Authorization: Bearer sk_live_abc123..." \
     -H "Content-Type: application/json" \
     -d '{"message": "I need something for relaxation"}' \
     https://yourdomain.com/api/public/products/chat
```

**Features Available:**

- ✅ Product search and recommendations
- ✅ Chat responses from Smokey AI
- ✅ Order placement (creates user from email/phone)
- ❌ Chat history persistence
- ❌ Saved chat sessions
- ❌ Order tracking across sessions

### Authenticated Access (API Key + User Token)

For full functionality with user account features:

```bash
curl -H "Authorization: Bearer sk_live_abc123..." \
     -H "x-user-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
     -H "Content-Type: application/json" \
     -d '{"message": "Show me my previous orders", "chat_id": "existing_chat_id"}' \
     https://yourdomain.com/api/public/products/chat
```

**Features Available:**

- ✅ All anonymous features
- ✅ Chat history persistence
- ✅ Saved chat sessions
- ✅ Order tracking and history
- ✅ Personalized recommendations
- ✅ Account-linked order management

## Endpoint-Specific Authentication

### Public Product Endpoints

| Endpoint                          | API Key  | User Token   | Description                       |
| --------------------------------- | -------- | ------------ | --------------------------------- |
| `GET /public/products`            | Required | Optional     | Product listing                   |
| `POST /public/products/upload`    | Required | N/A          | Product upload                    |
| `POST /public/products/chat`      | Required | Optional     | AI chat (anonymous vs persistent) |
| `GET /public/products/search`     | Required | N/A          | Product search                    |
| `GET /public/products/chats`      | Required | **Required** | List saved chats                  |
| `POST /public/products/chats`     | Required | **Required** | Create new chat                   |
| `GET /public/products/chats/{id}` | Required | **Required** | Get chat details                  |

### Order Endpoints

| Endpoint                | API Key    | User Token | Description                                                           |
| ----------------------- | ---------- | ---------- | --------------------------------------------------------------------- |
| `POST /orders/checkout` | Required   | Optional   | Create order (guest vs linked) - uses `x-user-token` for user linking |
| `GET /orders`           | Admin Auth | N/A        | List orders (admin only)                                              |
| `GET /orders/{id}`      | Admin Auth | N/A        | Get order details (admin only)                                        |

## Implementation Examples

### JavaScript/TypeScript

```typescript
// Anonymous API call
const apiResponse = await fetch("/api/public/products/chat", {
  method: "POST",
  headers: {
    Authorization: "Bearer sk_live_abc123...",
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    message: "I need help choosing a product",
  }),
});

// Authenticated API call
const authResponse = await fetch("/api/public/products/chat", {
  method: "POST",
  headers: {
    Authorization: "Bearer sk_live_abc123...",
    "x-user-token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    message: "Show me my chat history",
    chat_id: "existing_chat_123",
  }),
});
```

### Python

```python
import requests

# Anonymous request
response = requests.post(
    'https://yourdomain.com/api/public/products/chat',
    headers={
        'Authorization': 'Bearer sk_live_abc123...',
        'Content-Type': 'application/json'
    },
    json={
        'message': 'I need help choosing a product'
    }
)

# Authenticated request
auth_response = requests.post(
    'https://yourdomain.com/api/public/products/chat',
    headers={
        'Authorization': 'Bearer sk_live_abc123...',
        'x-user-token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        'Content-Type': 'application/json'
    },
    json={
        'message': 'Show me my chat history',
        'chat_id': 'existing_chat_123'
    }
)
```

### PHP

```php
// Anonymous request
$response = file_get_contents('https://yourdomain.com/api/public/products/chat', false, stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Authorization: Bearer sk_live_abc123...',
            'Content-Type: application/json'
        ],
        'content' => json_encode([
            'message' => 'I need help choosing a product'
        ])
    ]
]));

// Authenticated request
$auth_response = file_get_contents('https://yourdomain.com/api/public/products/chat', false, stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Authorization: Bearer sk_live_abc123...',
            'x-user-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            'Content-Type: application/json'
        ],
        'content' => json_encode([
            'message' => 'Show me my chat history',
            'chat_id' => 'existing_chat_123'
        ])
    ]
]));
```

### Order Checkout Examples

#### Guest Checkout (API Key Only)

```bash
curl -X POST https://yourdomain.com/api/orders/checkout \
  -H "Authorization: Bearer sk_live_abc123def456..." \
  -H "Content-Type: application/json" \
  -d '{
    "items": [{"product_id": "prod_123", "quantity": 2}],
    "user": {
      "email": "<EMAIL>",
      "phone": "+**********",
      "firstName": "John",
      "lastName": "Doe"
    },
    "shipping_address": {
      "name": "John Doe",
      "line1": "123 Main St",
      "city": "San Francisco",
      "state": "CA",
      "postal_code": "94102",
      "country": "US"
    }
  }'
```

**Result**: Creates a new user account from the provided email/phone, then places the order.

#### Authenticated Checkout (API Key + User Token)

```bash
curl -X POST https://yourdomain.com/api/orders/checkout \
  -H "Authorization: Bearer sk_live_abc123def456..." \
  -H "x-user-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "items": [{"product_id": "prod_123", "quantity": 2}],
    "shipping_address": {
      "name": "John Doe",
      "line1": "123 Main St",
      "city": "San Francisco",
      "state": "CA",
      "postal_code": "94102",
      "country": "US"
    }
  }'
```

**Result**: Links the order to the authenticated user account. User information is pulled from the JWT token.

## Error Responses

### Missing API Key

```json
{
  "error": "API key is required"
}
```

Status: `401 Unauthorized`

### Invalid API Key

```json
{
  "error": "Invalid API key"
}
```

Status: `401 Unauthorized`

### Missing User Authentication (for protected endpoints)

```json
{
  "error": "User authentication required"
}
```

Status: `401 Unauthorized`

### Invalid User Token

```json
{
  "error": "Invalid user token"
}
```

Status: `401 Unauthorized`

## Best Practices

1. **Always include API key**: Every request to public endpoints must include the location API key
2. **Use user tokens when available**: Include user authentication for enhanced features
3. **Handle graceful degradation**: Design your app to work with API key only, then enhance with user features
4. **Secure token storage**: Store user tokens securely (e.g., HTTP-only cookies, secure storage)
5. **Token refresh**: Implement token refresh logic for expired user tokens
6. **Error handling**: Gracefully handle authentication errors and provide fallback options

## Security Considerations

- **API Keys**: Should be kept secure and not exposed in client-side code for production
- **User Tokens**: Should be securely stored and transmitted over HTTPS only
- **Token Expiry**: Implement proper token refresh mechanisms
- **Rate Limiting**: Be aware of rate limits for your location API key
- **CORS**: Configure CORS appropriately for your domain

## Getting API Keys

Contact your BakedBot administrator or check your location settings in the admin panel to obtain your location API key.

## Getting User Tokens

User tokens are typically obtained through your application's authentication flow. These are JWT tokens that identify individual users within your system.
