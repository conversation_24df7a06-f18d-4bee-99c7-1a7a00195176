FROM node:18

WORKDIR /usr/src/app/apps/platform

# Install global packages needed for development
RUN npm install -g nodemon ts-node typescript wait-on

# Create a directory for node_modules that won't be overwritten by volume mount
RUN mkdir -p node_modules

# Create nodemon config for better file watching
RUN echo '{\n\
    "watch": ["src/**/*"],\n\
    "ext": ".ts,.js,.json",\n\
    "ignore": ["src/**/*.spec.ts"],\n\
    "exec": "ts-node --transpile-only ./src/boot.ts"\n\
    }' > nodemon.json

# Create startup script
RUN echo '#!/bin/bash\necho "Installing dependencies..."\nnpm install\necho "Waiting for MySQL..."\nwait-on -t 60000 tcp:mysql:3306\necho "Starting application..."\nnodemon' > start.sh

RUN chmod +x start.sh

EXPOSE 3001
EXPOSE 9229

# Start the application
CMD ["/bin/bash", "./start.sh"]
