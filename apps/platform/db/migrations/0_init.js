exports.up = function (knex) {
  return knex.schema
    .createTable("locations", function (table) {
      table.increments();
      table.string("name", 255).defaultTo("");
      table.string("description", 2048).defaultTo("");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      table.timestamp("deleted_at").nullable();
      table.index("name");
    })
    .createTable("admins", function (table) {
      table.increments();
      table.string("first_name", 255).notNullable();
      table.string("last_name", 255).notNullable();
      table.string("email", 255).notNullable();
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      table.timestamp("deleted_at").nullable();
    })
    .createTable("location_admins", function (table) {
      table.increments();
      table
        .integer("location_id")
        .unsigned()
        .notNullable()
        .references("id")
        .inTable("locations")
        .onDelete("CASCADE");
      table
        .integer("admin_id")
        .unsigned()
        .notNullable()
        .references("id")
        .inTable("admins")
        .onDelete("CASCADE");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      table.timestamp("deleted_at").nullable();
    })
    .createTable("users", function (table) {
      table.increments();
      table
        .integer("location_id")
        .unsigned()
        .notNullable()
        .references("id")
        .inTable("locations")
        .onDelete("CASCADE");
      table.string("anonymous_id");
      table.string("external_id", 255).notNullable();
      table.string("email", 255).nullable();
      table.string("phone", 64).nullable();
      table.json("data");
      table.json("devices");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      table.unique(["location_id", "external_id"]);
      table.unique(["location_id", "anonymous_id"]);
    })
    .createTable("location_api_keys", function (table) {
      table.increments();
      table
        .integer("location_id")
        .unsigned()
        .notNullable()
        .references("id")
        .inTable("locations")
        .onDelete("CASCADE");
      table.string("value", 255).notNullable();
      table.string("scope", 20);
      table.string("name", 255).notNullable();
      table.string("description", 2048).nullable();
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      table.timestamp("deleted_at").nullable();
      table.unique(["value"]);
    })
    .createTable("documents", (table) => {
      table.increments();
      table.string("name").notNullable();
      table.string("type").notNullable();
      table.integer("size").notNullable();
      table.json("metadata").nullable();
      table.integer("location_id").notNullable().index();
      table.string("status").notNullable().defaultTo("pending");
      table.text("data").nullable();
      table.string("storage_path").nullable();
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
      table.timestamp("deleted_at").nullable();
    });
};

exports.down = function (knex) {
  return knex.schema
    .dropTable("users")
    .dropTable("location_admins")
    .dropTable("admins")
    .dropTable("location_api_keys")
    .dropTable("locations")
    .dropTable("documents");
};
