exports.up = function (knex) {
  return knex.schema
    .createTable("subscriptions", function (table) {
      table.increments();
      table
        .integer("location_id")
        .unsigned()
        .notNullable()
        .references("id")
        .inTable("locations")
        .onDelete("CASCADE");
      table.string("name", 255).defaultTo("");
      table.string("channel", 255).notNullable();
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
    })
    .createTable("user_subscription", function (table) {
      table.increments();
      table
        .integer("subscription_id")
        .unsigned()
        .notNullable()
        .references("id")
        .inTable("subscriptions")
        .onDelete("CASCADE");
      table
        .integer("user_id")
        .unsigned()
        .notNullable()
        .references("id")
        .inTable("users")
        .onDelete("CASCADE");
      table.tinyint("state");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
    })
    .createTable("campaigns", function (table) {
      table.increments();
      table
        .integer("location_id")
        .unsigned()
        .notNullable()
        .references("id")
        .inTable("locations")
        .onDelete("CASCADE");
      table
        .integer("list_id")
        .unsigned()
        .references("id")
        .inTable("lists")
        .onDelete("CASCADE");
      table.string("name", 255).defaultTo("");
      table.string("channel", 255).notNullable();
      table
        .integer("subscription_id")
        .unsigned()
        .notNullable()
        .references("id")
        .inTable("subscriptions")
        .onDelete("CASCADE");
      table
        .integer("template_id")
        .unsigned()
        .notNullable()
        .references("id")
        .inTable("templates")
        .onDelete("CASCADE");
      table.timestamp("created_at").defaultTo(knex.fn.now());
      table.timestamp("updated_at").defaultTo(knex.fn.now());
    });
};

exports.down = function (knex) {
  knex.schema
    .dropTable("campaigns")
    .dropTable("user_subscriptions")
    .dropTable("subscriptions");
};
