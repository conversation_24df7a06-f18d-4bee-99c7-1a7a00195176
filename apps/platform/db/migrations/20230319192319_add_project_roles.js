exports.up = function (knex) {
  return knex.schema
    .alterTable("location_admins", function (table) {
      table.string("role", 64).notNullable().defaultTo("support");
    })
    .alterTable("location_api_keys", function (table) {
      table.string("role", 64).notNullable().defaultTo("support");
    });
};

exports.down = function (knex) {
  return knex.schema
    .alterTable("location_admins", function (table) {
      table.dropColumn("role");
    })
    .alterTable("location_api_keys", function (table) {
      table.dropColumn("role");
    });
};
