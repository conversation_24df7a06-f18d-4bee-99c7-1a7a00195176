exports.up = async function (knex) {
  await knex.schema.createTable("location_rule_paths", function (table) {
    table.increments();
    table
      .integer("location_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");
    table.string("path").notNullable();
    table.string("name").nullable();
    table.string("type", 50).notNullable(); // 'user' | 'event'
    table.timestamp("created_at").defaultTo(knex.fn.now());
    table.timestamp("updated_at").defaultTo(knex.fn.now());
  });
};

exports.down = async function (knex) {
  await knex.schema.dropTable("location_rule_paths");
};
