// eslint-disable-next-line @typescript-eslint/no-var-requires
const crypto = require("crypto");

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema
    .table("lists", function (table) {
      table.boolean("is_visible").defaultTo(true);
    })
    .then(async () => {
      // Update all existing journey steps to have a list_id
      const gates = await knex("journey_steps").where("type", "gate");
      for (const gate of gates) {
        const journey = await knex("journeys")
          .where("id", gate.journey_id)
          .first();
        const [id] = await knex("lists").insert({
          location_id: journey.location_id,
          name: crypto.randomUUID(),
          type: "dynamic",
          state: "ready",
          rule: JSON.stringify(gate.rule),
          version: 0,
          is_visible: false,
          users_count: 0,
        });
        await knex("journey_steps")
          .update({ data: JSON.stringify({ list_id: id }) })
          .where("id", gate.id);
      }
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("lists", function (table) {
    table.dropColumn("is_visible");
  });
};
