/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema
    .alterTable("admins", function (table) {
      table.string("role", 64).notNullable().defaultTo("member");
    })
    .then(() => {
      return knex("admins").update({ role: "owner" });
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.alterTable("admins", function (table) {
    table.dropColumn("role");
  });
};
