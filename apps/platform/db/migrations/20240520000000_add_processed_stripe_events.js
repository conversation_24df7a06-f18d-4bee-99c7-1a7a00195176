/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.createTable("processed_stripe_events", (table) => {
    table.increments("id").primary();
    table.string("event_id", 255).notNullable().unique();
    table.string("event_type", 255).notNullable();
    table.timestamp("processed_at").notNullable();
    table.timestamp("created_at").defaultTo(knex.fn.now());
    table.timestamp("updated_at").defaultTo(knex.fn.now());

    // Add index on event_id for faster lookups
    table.index("event_id");
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.dropTable("processed_stripe_events");
};
