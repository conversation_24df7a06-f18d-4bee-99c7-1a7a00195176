/**
 * Add retailer_id to locations table
 */
exports.up = function (knex) {
  return knex.schema.alterTable("locations", (table) => {
    table
      .string("retailer_id")
      .nullable()
      .comment("External retailer ID for integration purposes");
    table.index("retailer_id");
  });
};

exports.down = function (knex) {
  return knex.schema.alterTable("locations", (table) => {
    table.dropColumn("retailer_id");
  });
};
