exports.up = async function (knex) {
  await knex.schema.createTable("pos_data", function (table) {
    table.increments("id").primary();
    table
      .integer("location_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");
    table.string("location_name");
    table.string("master_category");
    table.timestamp("order_date");
    table.string("customer_type");
    table.string("budtender_name");
    table.decimal("gross_sales", 10, 2);
    table.decimal("returned_amount", 10, 2);
    table.decimal("discounted_amount", 10, 2);
    table.decimal("loyalty_as_discount", 10, 2);
    table.decimal("net_sales", 10, 2);
    table.decimal("inventory_cost", 10, 2);
    table.decimal("inventory_profit", 10, 2);
    table.decimal("loyalty_as_payment", 10, 2);
    table.decimal("tax_amount", 10, 2);
    table.decimal("invoice_total", 10, 2);
    table.decimal("amount_paid_in_cash", 10, 2);
    table.decimal("amount_paid_in_debit", 10, 2);
    table.date("birth_date").nullable();
    table.string("customer_name");
    table.string("product_name");
    table.timestamps(true, true);
    table.index(["location_id", "order_date"]);
  });
};

exports.down = async function (knex) {
  await knex.schema.dropTable("pos_data");
};
