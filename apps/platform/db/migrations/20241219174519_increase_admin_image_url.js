exports.up = async function (knex) {
  await knex.schema.alterTable("admins", function (table) {
    // Modify image_url to be a text column which can store much longer URLs
    table.text("image_url").alter();
  });
};

exports.down = async function (knex) {
  await knex.schema.alterTable("admins", function (table) {
    // Revert back to varchar(255) if needed
    table.string("image_url", 255).alter();
  });
};
