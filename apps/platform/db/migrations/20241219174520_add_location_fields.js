exports.up = async function (knex) {
  await knex.schema.alterTable("locations", function (table) {
    // Contact info
    table.string("address");
    table.string("city");
    table.string("state");
    table.string("zip");
    table.string("country");

    // Online presence
    table.string("website");
    table.string("facebook");
    table.string("twitter");
    table.string("instagram");
  });
};

exports.down = async function (knex) {
  await knex.schema.alterTable("locations", function (table) {
    // Contact info
    table.dropColumn("address");
    table.dropColumn("city");
    table.dropColumn("state");
    table.dropColumn("zip");
    table.dropColumn("country");

    // Online presence
    table.dropColumn("website");
    table.dropColumn("facebook");
    table.dropColumn("twitter");
    table.dropColumn("instagram");
  });
};
