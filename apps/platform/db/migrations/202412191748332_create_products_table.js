exports.up = async function (knex) {
  await knex.schema.createTable("products", (table) => {
    table.increments("id").primary();
    table
      .integer("location_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");
    table.string("meta_sku").notNullable();
    table.string("retailer_id").notNullable();
    table.string("cann_sku_id");
    table.string("brand_name");
    table.integer("brand_id");
    table.string("url");
    table.string("image_url");
    table.string("raw_product_name");
    table.string("product_name");
    table.string("raw_weight_string");
    table.string("display_weight");
    table.string("raw_product_category");
    table.string("category");
    table.string("raw_subcategory");
    table.string("subcategory");
    table.jsonb("product_tags");
    table.float("percentage_thc");
    table.float("percentage_cbd");
    table.float("mg_thc");
    table.float("mg_cbd");
    table.integer("quantity_per_package");
    table.boolean("medical").defaultTo(false);
    table.boolean("recreational").defaultTo(false);
    table.float("latest_price");
    table.string("menu_provider");
    table.jsonb("data");
    table.timestamps(true, true);

    table.unique(["location_id", "meta_sku"]);
    table.index(["location_id", "product_name"]);
    table.index(["location_id", "brand_name"]);
    table.index(["location_id", "category"]);
  });
};

exports.down = async function (knex) {
  await knex.schema.dropTable("products");
};
