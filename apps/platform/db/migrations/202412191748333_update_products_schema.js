exports.up = async function (knex) {
  // Check which columns already exist
  const columns = await knex.table("products").columnInfo();

  await knex.schema.alterTable("products", function (table) {
    // Only add columns if they don't already exist
    if (!columns.product_id) {
      table.string("product_id");
    }

    if (!columns.source) {
      table.string("source").notNullable().defaultTo("weedmaps");
    }

    if (!columns.slug) {
      table.string("slug");
    }

    // Add unique constraint on product_id and retailer_id if not already exists
    // Note: This requires a more complex check since knex doesn't have a direct way
    // to check for existing constraints
  });

  // Find duplicate entries before trying to add unique constraint
  const duplicates = await knex.raw(`
    SELECT product_id, retailer_id, COUNT(*) as count
    FROM products
    WHERE product_id IS NOT NULL
    GROUP BY product_id, retailer_id
    HAVING COUNT(*) > 1
  `);

  // If duplicates exist, handle them
  if (duplicates[0] && duplicates[0].length > 0) {
    console.log(
      `Found ${duplicates[0].length} duplicate entries. Handling duplicates...`
    );

    // For each set of duplicates, keep the first one and update others
    for (const dup of duplicates[0]) {
      const { product_id, retailer_id } = dup;

      // Find all duplicate rows except the first one
      const rows = await knex("products")
        .where({ product_id, retailer_id })
        .orderBy("id")
        .select("id");

      // Skip the first row (keep it), update others with suffixed product_id
      for (let i = 1; i < rows.length; i++) {
        await knex("products")
          .where("id", rows[i].id)
          .update({
            product_id: `${product_id}_duplicate_${i}`,
          });
      }
    }
  }

  // Check if constraint exists and add if needed
  const constraintExists = await knex.raw(`
    SELECT COUNT(*) as count 
    FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
    WHERE TABLE_NAME = 'products' 
    AND CONSTRAINT_NAME = 'products_product_id_retailer_id_unique'
  `);

  if (constraintExists[0][0].count === 0) {
    try {
      await knex.schema.alterTable("products", function (table) {
        table.unique(["product_id", "retailer_id"]);
      });
    } catch (error) {
      // If error is not about duplicate constraint, rethrow
      if (!error.message.includes("Duplicate key name")) {
        throw error;
      }
      // Otherwise silently continue
    }
  }
};

exports.down = async function (knex) {
  await knex.schema.alterTable("products", function (table) {
    // Drop columns in reverse order (only attempt if they exist)
    try {
      table.dropUnique(["product_id", "retailer_id"]);
    } catch (e) {
      // Constraint might not exist, ignore error
    }

    try {
      table.dropColumn("slug");
      table.dropColumn("source");
      table.dropColumn("product_id");
    } catch (e) {
      // One of the columns might not exist, ignore error
    }
  });
};
