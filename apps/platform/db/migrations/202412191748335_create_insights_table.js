exports.up = function (knex) {
  return knex.schema.createTable("insights", (table) => {
    table.increments("id").primary();
    table
      .integer("location_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");
    table.string("title").notNullable();
    table.text("description").notNullable();
    table.enum("impact", ["high", "medium", "low"]).notNullable();
    table
      .enum("type", ["general", "automation", "campaign"])
      .notNullable()
      .defaultTo("general");
    table.jsonb("actions").notNullable();
    table.jsonb("plan").nullable();
    table
      .enum("status", ["new", "viewed", "dismissed", "completed"])
      .defaultTo("new");
    table.timestamp("created_at").defaultTo(knex.fn.now());
    table.timestamp("updated_at").defaultTo(knex.fn.now());
    table.timestamp("acted_at").nullable();

    table.index(["location_id", "status"]);
  });
};

exports.down = function (knex) {
  return knex.schema.dropTable("insights");
};
