/* eslint-disable brace-style */
exports.up = async function (knex) {
  await knex.schema.alterTable("insights", async function (table) {
    if (await knex.schema.hasColumn("insights", "delivery_channel")) {
      await table.dropColumn("delivery_channel");
    }
    table.enum("delivery_channel", ["email", "text"]);
  });
};

exports.down = async function (knex) {
  await knex.schema.alterTable("insights", async function (table) {
    if (await knex.schema.hasColumn("insights", "delivery_channel")) {
      table.dropColumn("delivery_channel");
    }
    table.enum("delivery_channel", ["email", "text"]);
  });
};
