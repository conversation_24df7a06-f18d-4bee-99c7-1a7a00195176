exports.up = async function (knex) {
  return knex.schema.table("insights", async function (table) {
    if (await knex.schema.hasColumn("insights", "delivery_channel")) {
      await table.dropColumn("delivery_channel");
    }
    table.enum("delivery_channel", ["email", "text"]).defaultTo("email");
  });
};

exports.down = function (knex) {
  return knex.schema.table("insights", function (table) {
    table.dropColumn("delivery_channel");
  });
};
