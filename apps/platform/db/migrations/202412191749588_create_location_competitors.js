exports.up = async function (knex) {
  await knex.schema.createTable("location_competitors", (table) => {
    table.increments("id").primary();
    table.integer("location_id").unsigned().notNullable();
    table.string("competitor_place_id").notNullable();
    table.string("name").notNullable();
    table.string("address").notNullable();
    table.decimal("latitude", 10, 8).notNullable();
    table.decimal("longitude", 11, 8).notNullable();
    table.decimal("distance_km", 10, 2).notNullable();
    table.timestamps(true, true);

    table
      .foreign("location_id")
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");

    // Ensure a location can't add the same competitor twice
    table.unique(["location_id", "competitor_place_id"]);
  });
};

exports.down = async function (knex) {
  await knex.schema.dropTable("location_competitors");
};
