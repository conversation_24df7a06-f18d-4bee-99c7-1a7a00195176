/**
 * @param {import('knex').Knex} knex
 * @returns {Promise<void>}
 */
exports.up = async function (knex) {
  await knex.schema.createTable("featured_products", (table) => {
    table.increments("id").primary();
    table
      .integer("location_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");
    table
      .integer("product_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("products")
      .onDelete("CASCADE");
    table.boolean("is_top_pick").defaultTo(false);
    table.integer("sort_order").defaultTo(0);
    table.boolean("active").defaultTo(true);
    table.timestamp("created_at").defaultTo(knex.fn.now());
    table.timestamp("updated_at").defaultTo(knex.fn.now());

    // Indexes for efficient querying
    table.index(["location_id", "active"]);
    table.index(["location_id", "is_top_pick"]);
    table.index(["location_id", "sort_order"]);
    table.unique(["location_id", "product_id"]);
  });
};

/**
 * @param {import('knex').Knex} knex
 * @returns {Promise<void>}
 */
exports.down = async function (knex) {
  await knex.schema.dropTableIfExists("featured_products");
};
