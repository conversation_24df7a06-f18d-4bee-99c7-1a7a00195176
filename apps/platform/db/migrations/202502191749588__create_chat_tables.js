/**
 * @param {import('knex').Knex} knex
 * @returns {Promise<void>}
 */
exports.up = async function (knex) {
  // Create chats table
  await knex.schema.createTable("chats", (table) => {
    table.increments("id").primary();
    table.string("chat_id").unique().notNullable();
    table.string("name").notNullable();
    table.timestamp("created_at").defaultTo(knex.fn.now());
    table.timestamp("updated_at").defaultTo(knex.fn.now());
    table.integer("location_id").unsigned().notNullable();
    table.json("agent_ids").notNullable();
    table.enum("status", ["active", "archived", "deleted"]).defaultTo("active");
    table.json("metadata");

    table.index("location_id");
    table.index("status");
    table.unique(["chat_id", "location_id"]);
  });

  // Create messages table
  await knex.schema.createTable("messages", (table) => {
    table.increments("id").primary();
    table.text("content").notNullable();
    table.enum("role", ["user", "assistant"]).notNullable();
    table.timestamp("timestamp").notNullable();
    table.string("chat_id").notNullable();
    table.string("agent_id");
    table.json("metadata");

    table.foreign("chat_id").references("chats.chat_id").onDelete("CASCADE");
    table.index("chat_id");
    table.index("timestamp");
  });

  // Create agents table
  await knex.schema.createTable("agents", (table) => {
    table.string("id").primary();
    table.string("name").notNullable();
    table.string("role").notNullable();
    table.text("description").notNullable();
    table.string("icon").notNullable();
    table.json("capabilities").notNullable();
    table.boolean("disabled").defaultTo(false);
    table.json("metadata");

    table.index("disabled");
  });
};

/**
 * @param {import('knex').Knex} knex
 * @returns {Promise<void>}
 */
exports.down = async function (knex) {
  await knex.schema.dropTableIfExists("messages");
  await knex.schema.dropTableIfExists("chats");
  await knex.schema.dropTableIfExists("agents");
};
