exports.up = function (knex) {
  return knex.schema.alterTable("subscriptions", function (table) {
    // Email related fields
    table.string("from_name").nullable();
    table.string("from_email").nullable();
    table.string("reply_to").nullable();
    table.string("cc").nullable();
    table.string("bcc").nullable();

    // SMS related fields
    table.string("from_phone").nullable();
  });
};

exports.down = function (knex) {
  return knex.schema.alterTable("subscriptions", function (table) {
    table.dropColumn("from_name");
    table.dropColumn("from_email");
    table.dropColumn("reply_to");
    table.dropColumn("cc");
    table.dropColumn("bcc");
    table.dropColumn("from_phone");
  });
};
