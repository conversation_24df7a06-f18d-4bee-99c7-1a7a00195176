exports.up = async function (knex) {
  // Step 1: Add the column without a default value
  await knex.schema.alterTable("locations", function (table) {
    table.jsonb("social_media_accounts");
  });

  // Step 2: Update existing rows with the default value in a separate step
  // This is the workaround for MySQL's limitation on JSON columns with default values
  return knex.raw(`
    UPDATE locations 
    SET social_media_accounts = '{"linkedin":{"accessToken":"","connected":false},"facebook":{"accessToken":"","connected":false},"twitter":{"accessToken":"","connected":false},"instagram":{"accessToken":"","connected":false}}'
    WHERE social_media_accounts IS NULL
  `);
};

exports.down = async function (knex) {
  return knex.schema.alterTable("locations", function (table) {
    table.dropColumn("social_media_accounts");
  });
};
