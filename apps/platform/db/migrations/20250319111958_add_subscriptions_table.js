exports.up = function (knex) {
  return knex.schema.createTable("stripe_subscriptions", function (table) {
    table.increments();
    table
      .integer("organization_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("organizations")
      .onDelete("CASCADE");
    table.string("subscription_id", 100).nullable();
    table.string("customer_id", 100).nullable();
    table.string("price_id", 100).nullable();
    table.string("product_id").nullable();
    table
      .enu("status", [
        "inactive",
        "incomplete",
        "incomplete_expired",
        "trialing",
        "active",
        "past_due",
        "canceled",
        "unpaid",
        "paused",
      ])
      .notNullable()
      .defaultTo("inactive");
    table.timestamp("current_period_start");
    table.timestamp("current_period_end");
    table.timestamp("canceled_at");
    table.timestamp("trial_start");
    table.timestamp("trial_end");
    table.timestamp("created_at");
    table.timestamp("updated_at").defaultTo(knex.fn.now());
  });
};

exports.down = async function (knex) {
  return knex.schema.dropTable("stripe_subscriptions");
};
