exports.up = async function (knex) {
  // Check if the column exists before trying to drop it
  const hasColumn = await knex.schema.hasColumn("chats", "agents");
  if (hasColumn) {
    await knex.schema.alterTable("chats", function (table) {
      table.dropColumn("agents");
    });
  }
};

exports.down = async function (knex) {
  // Re-add the column if needed
  const hasColumn = await knex.schema.hasColumn("chats", "agents");
  if (!hasColumn) {
    await knex.schema.alterTable("chats", function (table) {
      table.json("agents").nullable();
    });
  }
};
