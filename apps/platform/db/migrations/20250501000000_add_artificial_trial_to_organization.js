/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.alterTable("organizations", function (table) {
    table.timestamp("artificial_trial_end").nullable();
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.alterTable("organizations", function (table) {
    table.dropColumn("artificial_trial_end");
  });
};
