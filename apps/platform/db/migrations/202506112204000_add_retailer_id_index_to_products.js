exports.up = async function (knex) {
  // Add index on retailer_id for better query performance
  // This will significantly improve the performance of queries filtering by retailer_id
  await knex.schema.alterTable("products", function (table) {
    table.index("retailer_id", "idx_products_retailer_id");
  });

  // Also add a compound index for retailer_id + location_id since many queries filter by both
  await knex.schema.alterTable("products", function (table) {
    table.index(["retailer_id", "location_id"], "idx_products_retailer_location");
  });

  console.log("✅ Added retailer_id indexes to products table for better query performance");
};

exports.down = async function (knex) {
  // Remove the indexes
  await knex.schema.alterTable("products", function (table) {
    table.dropIndex("retailer_id", "idx_products_retailer_id");
    table.dropIndex(["retailer_id", "location_id"], "idx_products_retailer_location");
  });

  console.log("❌ Removed retailer_id indexes from products table");
};
