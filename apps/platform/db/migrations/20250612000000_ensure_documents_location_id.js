/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  // Check if the location_id column exists
  const hasLocationId = await knex.schema.hasColumn("documents", "location_id");

  // If the column doesn't exist, add it
  if (!hasLocationId) {
    console.log("Adding location_id column to documents table");
    await knex.schema.table("documents", function (table) {
      table.integer("location_id").notNullable().index();
    });
  } else {
    console.log("location_id column already exists in documents table");
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  // No need to drop the column in down migration as it should exist in the schema
  console.log("No action needed for down migration");
};
