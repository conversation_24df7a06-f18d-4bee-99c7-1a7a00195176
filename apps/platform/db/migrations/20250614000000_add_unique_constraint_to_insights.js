/**
 * Add unique constraint to prevent duplicate insights with same title per location
 */
exports.up = async function (knex) {
  console.log(
    "🔍 Finding and removing duplicate insights before adding unique constraint..."
  );

  // First, remove any existing duplicates before adding the constraint
  // This approach is more comprehensive than the previous status-restricted delete
  const duplicates = await knex.raw(`
    SELECT location_id, title, COUNT(*) as count, 
           GROUP_CONCAT(id ORDER BY created_at DESC) as ids
    FROM insights 
    GROUP BY location_id, title 
    HAVING COUNT(*) > 1
  `);

  const rows = duplicates[0] || [];
  let totalRemoved = 0;

  for (const row of rows) {
    const ids = row.ids.split(",").map((id) => parseInt(id));
    const keepId = ids[0]; // Keep the most recent one
    const removeIds = ids.slice(1); // Remove the older ones

    if (removeIds.length > 0) {
      await knex("insights").whereIn("id", removeIds).del();
      totalRemoved += removeIds.length;
      console.log(
        `Removed ${removeIds.length} duplicates for location ${row.location_id}, title: "${row.title}"`
      );
    }
  }

  console.log(`✅ Removed ${totalRemoved} duplicate insights in total`);

  // Add unique constraint on location_id + title combination
  return knex.schema.alterTable("insights", function (table) {
    table.unique(["location_id", "title"], "insights_location_title_unique");
  });
};

exports.down = function (knex) {
  return knex.schema.alterTable("insights", function (table) {
    table.dropUnique(
      ["location_id", "title"],
      "insights_location_title_unique"
    );
  });
};
