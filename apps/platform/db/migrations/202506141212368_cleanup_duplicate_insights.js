exports.up = async function (knex) {
  console.log("🔍 Finding duplicate insights to remove...");

  const duplicates = await knex.raw(`
    SELECT location_id, title, COUNT(*) as count, 
           GROUP_CONCAT(id ORDER BY created_at DESC) as ids
    FROM insights 
    WHERE status IN ('new', 'viewed')
    GROUP BY location_id, title 
    HAVING COUNT(*) > 1
  `);

  const rows = duplicates[0] || [];
  let totalRemoved = 0;

  for (const row of rows) {
    const ids = row.ids.split(',').map(id => parseInt(id));
    const keepId = ids[0];
    const removeIds = ids.slice(1);

    if (removeIds.length > 0) {
      await knex('insights').whereIn('id', removeIds).del();
      totalRemoved += removeIds.length;
    }
  }

  console.log(`✅ Removed ${totalRemoved} duplicate insights`);
};

exports.down = async function (_knex) {
  // No rollback since we cannot recover deleted rows
  console.log("⚠️  No rollback implemented for duplicate insights cleanup");
};
