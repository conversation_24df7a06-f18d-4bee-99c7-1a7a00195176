exports.up = async function (knex) {
  console.log("🚀 Starting template validation fix...");

  // Step 1: Fix from.email → from.address
  const emailFixResult = await knex.raw(`
    UPDATE templates 
    SET data = JSON_SET(
      JSON_REMOVE(data, '$.from.email'),
      '$.from.address', JSON_UNQUOTE(JSON_EXTRACT(data, '$.from.email'))
    )
    WHERE type = 'email' 
      AND JSON_EXTRACT(data, '$.from.email') IS NOT NULL
      AND JSON_EXTRACT(data, '$.from.address') IS NULL
  `);
  console.log(`✅ Fixed from.email → from.address for ${emailFixResult[0].affectedRows} templates`);

  // Step 2: Ensure required fields have default values
  const requiredFieldsResult = await knex.raw(`
    UPDATE templates 
    SET data = JSON_SET(
      data,
      '$.from.name', COALESCE(JSON_UNQUOTE(JSON_EXTRACT(data, '$.from.name')), 'BakedBot Team'),
      '$.reply_to', COALESCE(JSON_UNQUOTE(JSON_EXTRACT(data, '$.reply_to')), '<EMAIL>'),
      '$.editor', COALESCE(JSON_UNQUOTE(JSON_EXTRACT(data, '$.editor')), 'code'),
      '$.name', COALESCE(JSON_UNQUOTE(JSON_EXTRACT(data, '$.name')), CONCAT('Campaign ', campaign_id, ' Template')),
      '$.subject', COALESCE(JSON_UNQUOTE(JSON_EXTRACT(data, '$.subject')), CONCAT('Campaign ', campaign_id, ' - Email')),
      '$.html', COALESCE(JSON_UNQUOTE(JSON_EXTRACT(data, '$.html')), CONCAT('<p>Email content for Campaign ', campaign_id, '</p>')),
      '$.text', COALESCE(JSON_UNQUOTE(JSON_EXTRACT(data, '$.text')), CONCAT('Email content for Campaign ', campaign_id))
    )
    WHERE type = 'email'
  `);
  console.log(`✅ Ensured required fields for ${requiredFieldsResult[0].affectedRows} templates`);

  // Step 3: Ensure from.address fallback exists
  const addressFallbackResult = await knex.raw(`
    UPDATE templates 
    SET data = JSON_SET(
      data,
      '$.from.address', '<EMAIL>'
    )
    WHERE type = 'email' 
      AND (JSON_EXTRACT(data, '$.from.address') IS NULL OR JSON_EXTRACT(data, '$.from.address') = '')
  `);
  console.log(`✅ Set fallback from.address for ${addressFallbackResult[0].affectedRows} templates`);
};

exports.down = async function (_knex) {
  // ⚠️ Irreversible: Changes to JSON structure and defaults can't be rolled back reliably
  console.log("⚠️  No rollback implemented for template validation fix");
};
