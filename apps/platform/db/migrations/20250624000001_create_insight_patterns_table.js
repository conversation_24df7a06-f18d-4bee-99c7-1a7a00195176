exports.up = function (knex) {
  return knex.schema.createTable("insight_patterns", (table) => {
    table.increments("id").primary();
    table.string("pattern").notNullable().comment("Regex pattern to match non-actionable insights");
    table.string("description").notNullable().comment("Human-readable description of what this pattern matches");
    table.enum("type", ["non_actionable", "actionable"]).notNullable().defaultTo("non_actionable");
    table.boolean("is_active").notNullable().defaultTo(true);
    table.integer("priority").notNullable().defaultTo(0).comment("Higher priority patterns are checked first");
    table.text("examples").nullable().comment("JSON array of example phrases this pattern should match");
    table.timestamp("created_at").defaultTo(knex.fn.now());
    table.timestamp("updated_at").defaultTo(knex.fn.now());
    
    // Add indexes for performance
    table.index(["type", "is_active"]);
    table.index("priority");
  });
};

exports.down = function (knex) {
  return knex.schema.dropTable("insight_patterns");
};
