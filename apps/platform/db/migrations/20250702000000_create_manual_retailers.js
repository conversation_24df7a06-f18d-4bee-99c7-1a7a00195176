/**
 * Create manual retailers table for brand-linked products
 */
exports.up = async function (knex) {
  // Check if the manual_retailers table already exists
  const tableExists = await knex.schema.hasTable("manual_retailers");

  if (!tableExists) {
    // Create manual_retailers table for manually added retailers
    await knex.schema.createTable("manual_retailers", (table) => {
      table.increments("id").primary();
      table.string("external_id").comment("External ID if available");

      // Basic info
      table.string("name").notNullable().comment("Retailer name");
      table.string("address").comment("Street address");
      table.string("city").comment("City");
      table.string("state").comment("State or province");
      table.string("zip").comment("Postal code");
      table.string("country").defaultTo("US").comment("Country code");

      // Contact info
      table.string("phone").comment("Phone number");
      table.string("contact_email").comment("Contact email");
      table.string("website").comment("Website URL");

      // Location data
      table.float("latitude").comment("Latitude coordinate");
      table.float("longitude").comment("Longitude coordinate");

      // Ownership and status
      table
        .integer("location_id")
        .unsigned()
        .references("id")
        .inTable("locations")
        .onDelete("CASCADE")
        .comment("Associated location (brand) that added this retailer");

      // Metadata and timestamps
      table.timestamps(true, true);
      table.timestamp("deleted_at").nullable();
    });

    // Create product_retailers table for many-to-many relationship
    await knex.schema.createTable("product_retailers", (table) => {
      table.increments("id").primary();

      // Product reference
      table
        .string("meta_sku")
        .notNullable()
        .comment("Product SKU (not using foreign key to allow flexibility)");

      // Retailer reference - can be either manual or from marketplace
      table
        .integer("manual_retailer_id")
        .unsigned()
        .references("id")
        .inTable("manual_retailers")
        .onDelete("CASCADE")
        .nullable()
        .comment("Reference to manually added retailer (if applicable)");
      table
        .string("marketplace_retailer_id")
        .nullable()
        .comment("External ID for marketplace retailer (if applicable)");

      // Location reference
      table
        .integer("location_id")
        .unsigned()
        .references("id")
        .inTable("locations")
        .onDelete("CASCADE")
        .comment("Associated location (brand)");

      // Pricing information
      table
        .decimal("price", 10, 2)
        .nullable()
        .comment("Price at this retailer");
      table.decimal("wholesale_price", 10, 2).nullable();
      table.decimal("retail_price", 10, 2).nullable();
      table.decimal("msrp", 10, 2).nullable();

      // Timestamps
      table.timestamps(true, true);
    });

    // Add unique constraint with a shorter, custom name
    await knex.schema.raw(`
      ALTER TABLE product_retailers 
      ADD CONSTRAINT prod_retailer_unique 
      UNIQUE (meta_sku, manual_retailer_id, marketplace_retailer_id, location_id)
    `);

    // Add indexes
    await knex.schema.table("product_retailers", (table) => {
      table.index("meta_sku");
      table.index("manual_retailer_id");
      table.index("marketplace_retailer_id");
      table.index("location_id");
    });
  }
};

exports.down = async function (knex) {
  // Drop tables in reverse order
  await knex.schema.dropTableIfExists("product_retailers");
  await knex.schema.dropTableIfExists("manual_retailers");
};
