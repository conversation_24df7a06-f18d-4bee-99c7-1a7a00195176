exports.up = async function (knex) {
  await knex.schema.alterTable("products", function (table) {
    // Add new fields
    table.string("images_urls");
    table.text("review_summary");
    table.float("rating");
    table.integer("reviews_count");
    table.text("product_description");
    table.float("thc");
    table.float("cbd");
    table.jsonb("variants");
    table.string("enhancement_status");
    table.jsonb("mood");
    table.string("estimated_cbd_percentage");
    table.string("estimated_thc_percentage");
    table.jsonb("effects");
    table.string("enhancement_error");
    table.string("external_id"); // This is the 'id' field from the API
  });
};

exports.down = async function (knex) {
  await knex.schema.alterTable("products", function (table) {
    // Drop the new columns in reverse order
    table.dropColumn("external_id");
    table.dropColumn("enhancement_error");
    table.dropColumn("effects");
    table.dropColumn("estimated_thc_percentage");
    table.dropColumn("estimated_cbd_percentage");
    table.dropColumn("mood");
    table.dropColumn("enhancement_status");
    table.dropColumn("variants");
    table.dropColumn("cbd");
    table.dropColumn("thc");
    table.dropColumn("product_description");
    table.dropColumn("reviews_count");
    table.dropColumn("rating");
    table.dropColumn("review_summary");
    table.dropColumn("images_urls");
  });
};
