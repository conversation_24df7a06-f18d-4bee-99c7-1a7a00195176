exports.up = async function (knex) {
  await knex.schema.createTable("reviews", function (table) {
    table.increments("id").primary();
    table.string("product_id").notNullable();
    table.string("retailer_id");
    table.float("rating").notNullable();
    table.text("text");
    table.string("author");
    table.timestamp("review_date");
    table.integer("helpful_votes");
    table.string("title");
    table.boolean("verified_purchase");
    table.jsonb("data");
    table.timestamps(true, true);
  });
};

exports.down = async function (knex) {
  await knex.schema.dropTable("reviews");
};
