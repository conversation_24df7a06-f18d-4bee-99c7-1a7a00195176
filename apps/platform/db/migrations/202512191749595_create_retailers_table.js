exports.up = async function (knex) {
  await knex.schema.createTable("retailers", function (table) {
    table.increments("id").primary();
    table.string("retailer_id").notNullable().unique();
    table.string("name").notNullable();
    table.string("slug");
    table.string("address");
    table.string("city");
    table.string("state");
    table.string("zip_code");
    table.string("country");
    table.string("phone");
    table.string("email");
    table.string("website_url");
    table.string("latitude");
    table.string("longitude");
    table.float("rating");
    table.integer("reviews_count");
    table.text("description");
    table.string("hours");
    table.boolean("is_open");
    table.string("license_type");
    table.boolean("serves_medical_users");
    table.boolean("serves_recreational_users");
    table.jsonb("services");
    table.string("online_ordering");
    table.string("avatar");
    table.boolean("is_active");
    table.jsonb("data");
    table.timestamps(true, true);
  });
};

exports.down = async function (knex) {
  await knex.schema.dropTable("retailers");
};
