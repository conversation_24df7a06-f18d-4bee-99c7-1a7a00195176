exports.up = async function (knex) {
  await knex.schema.createTable("product_test_results", (table) => {
    table.increments("id").primary();
    table.integer("location_id").unsigned().notNullable();
    table.string("product_id").notNullable(); // Could be meta_sku or other product identifier
    table.string("batch_id").notNullable();
    table.string("test_id").notNullable();
    table.string("lab_name").notNullable();
    table.timestamp("test_date").notNullable();
    table.decimal("thc_content", 10, 2);
    table.decimal("cbd_content", 10, 2);
    table.jsonb("cannabinoid_profile");
    table.jsonb("terpene_profile");
    table.boolean("microbiological_pass").notNullable();
    table.boolean("pesticide_pass").notNullable();
    table.boolean("heavy_metals_pass").notNullable();
    table.boolean("residual_solvents_pass").notNullable();
    table.jsonb("contaminants_data");
    table.text("notes");
    table.string("certificate_url");
    table.string("status").defaultTo("pending");
    table.timestamps(true, true);

    table
      .foreign("location_id")
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");

    // Indexes for efficient querying
    table.index(["location_id", "product_id"]);
    table.index(["batch_id"]);
    table.index(["test_date"]);
    table.index(["status"]);
  });

  // Create compliance status table to track overall product compliance
  await knex.schema.createTable("product_compliance_status", (table) => {
    table.increments("id").primary();
    table.integer("location_id").unsigned().notNullable();
    table.string("product_id").notNullable();
    table.string("compliance_status").notNullable(); // compliant, non-compliant, pending
    table.jsonb("compliance_issues");
    table.timestamp("last_verified_at").notNullable();
    table.string("verified_by");
    table.timestamps(true, true);

    table
      .foreign("location_id")
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");

    table.index(["location_id", "product_id"]);
    table.index(["compliance_status"]);
  });
};

exports.down = async function (knex) {
  await knex.schema.dropTable("product_compliance_status");
  await knex.schema.dropTable("product_test_results");
};
