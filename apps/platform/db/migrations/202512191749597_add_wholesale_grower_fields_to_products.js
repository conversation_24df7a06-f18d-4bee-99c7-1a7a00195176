exports.up = async function (knex) {
  await knex.schema.alterTable("products", function (table) {
    // Add wholesale and retail price fields
    table.decimal("wholesale_price", 10, 2);
    table.decimal("retail_price", 10, 2);
    table.decimal("msrp", 10, 2);
    table.decimal("profit_margin", 10, 2);

    // Add grower and cultivar information
    table.string("grower_name");
    table.string("cultivar");
    table.string("batch_number");
    table.timestamp("harvest_date");
    table.string("coa_url");
  });
};

exports.down = async function (knex) {
  await knex.schema.alterTable("products", function (table) {
    // Drop the new columns in reverse order
    table.dropColumn("coa_url");
    table.dropColumn("harvest_date");
    table.dropColumn("batch_number");
    table.dropColumn("cultivar");
    table.dropColumn("grower_name");
    table.dropColumn("profit_margin");
    table.dropColumn("msrp");
    table.dropColumn("retail_price");
    table.dropColumn("wholesale_price");
  });
};
