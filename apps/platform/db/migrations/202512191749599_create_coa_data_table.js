exports.up = async function (knex) {
  await knex.schema.createTable("coa_data", function (table) {
    table.increments("id").primary();
    table
      .integer("location_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");

    // Required fields
    table.string("product_name").notNullable();
    table.string("product_type").notNullable();
    table.string("batch_number").notNullable();
    table.string("sample_id").notNullable();
    table.timestamp("test_date").notNullable();
    table.string("lab_name").notNullable();

    // Add product_id field for direct linking to products
    table.string("product_id").notNullable(); // This will store meta_sku

    // Optional fields
    table.timestamp("production_date");
    table.string("lab_license");
    table.string("coa_url");

    // Cannabinoid profile
    table.decimal("thc_percent", 10, 2);
    table.decimal("thca_percent", 10, 2);
    table.decimal("cbd_percent", 10, 2);
    table.decimal("cbda_percent", 10, 2);
    table.decimal("cbg_percent", 10, 2);
    table.decimal("cbga_percent", 10, 2);
    table.decimal("cbn_percent", 10, 2);
    table.decimal("cbc_percent", 10, 2);
    table.decimal("total_thc", 10, 2);
    table.decimal("total_cbd", 10, 2);
    table.decimal("total_cannabinoids", 10, 2);

    // Terpene profile
    table.decimal("terpene_myrcene", 10, 4);
    table.decimal("terpene_limonene", 10, 4);
    table.decimal("terpene_caryophyllene", 10, 4);
    table.decimal("terpene_linalool", 10, 4);
    table.decimal("terpene_pinene", 10, 4);
    table.decimal("terpene_humulene", 10, 4);
    table.decimal("terpene_terpinolene", 10, 4);
    table.decimal("total_terpenes", 10, 4);

    // Residual solvents
    table.decimal("solvent_butane", 10, 4);
    table.decimal("solvent_propane", 10, 4);
    table.decimal("solvent_ethanol", 10, 4);
    table.boolean("solvents_pass");

    // Heavy metals
    table.decimal("metal_lead", 10, 4);
    table.decimal("metal_mercury", 10, 4);
    table.decimal("metal_arsenic", 10, 4);
    table.decimal("metal_cadmium", 10, 4);
    table.boolean("metals_pass");

    // Microbials
    table.boolean("microbial_ecoli");
    table.boolean("microbial_salmonella");
    table.boolean("microbial_aspergillus");
    table.decimal("yeast_mold_count", 10, 2);
    table.boolean("microbials_pass");

    // Other metrics
    table.decimal("moisture_content", 10, 2);
    table.decimal("water_activity", 10, 4);
    table.boolean("overall_pass");

    // Metadata
    table.jsonb("data");
    table.timestamps(true, true);

    // Indexes for efficient querying
    table.index(["location_id", "batch_number"]);
    table.index(["product_name"]);
    table.index(["test_date"]);
    table.index(["overall_pass"]);
    table.index(["product_id"]); // Add index for product_id for quick lookups
    table.index(["product_id", "batch_number"]); // Add compound index for product+batch queries
  });
};

exports.down = async function (knex) {
  await knex.schema.dropTable("coa_data");
};
