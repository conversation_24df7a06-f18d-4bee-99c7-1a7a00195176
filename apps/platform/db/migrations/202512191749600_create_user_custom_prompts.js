/**
 * Create user_custom_prompts table for storing custom system prompts
 */

exports.up = async function (knex) {
  return knex.schema.createTable("user_custom_prompts", (table) => {
    table.increments("id").primary();
    table.integer("user_id").unsigned().notNullable().index();
    table.string("name", 255).notNullable();
    table.text("content").notNullable();
    table.boolean("is_active").notNullable().defaultTo(true);
    table.timestamp("created_at").notNullable().defaultTo(knex.fn.now());
    table.timestamp("updated_at").notNullable().defaultTo(knex.fn.now());

    // Add foreign key constraint to users table
    table
      .foreign("user_id")
      .references("id")
      .inTable("users")
      .onDelete("CASCADE");
  });
};

exports.down = async function (knex) {
  return knex.schema.dropTable("user_custom_prompts");
};
