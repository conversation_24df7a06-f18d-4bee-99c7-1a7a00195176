/**
 * Fix foreign key in user_custom_prompts table to reference admins table instead of users
 */

exports.up = async function (knex) {
  // First drop the existing foreign key constraint
  await knex.schema.table("user_custom_prompts", function (table) {
    table.dropForeign("user_id");
  });

  // Then rename the column to make it clearer
  await knex.schema.table("user_custom_prompts", function (table) {
    table.renameColumn("user_id", "admin_id");
  });

  // Finally add the correct foreign key constraint
  return knex.schema.table("user_custom_prompts", function (table) {
    table
      .foreign("admin_id")
      .references("id")
      .inTable("admins")
      .onDelete("CASCADE");
  });
};

exports.down = async function (knex) {
  // Reverse the changes in case we need to roll back
  await knex.schema.table("user_custom_prompts", function (table) {
    table.dropForeign("admin_id");
  });

  await knex.schema.table("user_custom_prompts", function (table) {
    table.renameColumn("admin_id", "user_id");
  });

  return knex.schema.table("user_custom_prompts", function (table) {
    table
      .foreign("user_id")
      .references("id")
      .inTable("users")
      .onDelete("CASCADE");
  });
};
