/**
 * Migration to add Pinecone Assistant fields to the locations table
 * This allows each location to have its own dedicated Pinecone Assistant for document handling
 */
exports.up = function (knex) {
  return knex.schema
    .hasColumn("locations", "pinecone_assistant_id")
    .then(function (exists) {
      if (!exists) {
        return knex.schema.table("locations", function (table) {
          // Pinecone Assistant ID from Pinecone API
          table.string("pinecone_assistant_id", 255).nullable();

          // Current status of the Pinecone Assistant (ready, creating, failed)
          table.string("pinecone_assistant_status", 50).nullable();

          // Region where the Pinecone Assistant is deployed (us, eu)
          table.string("pinecone_assistant_region", 10).defaultTo("us");

          // Instructions/customization for the assistant
          table.text("pinecone_assistant_instructions").nullable();

          // Last error message if assistant creation failed
          table.text("pinecone_assistant_error").nullable();

          // When the assistant was last created or updated
          table.timestamp("pinecone_assistant_updated_at").nullable();
        });
      }
      return Promise.resolve();
    });
};

exports.down = function (knex) {
  return knex.schema.table("locations", function (table) {
    table.dropColumn("pinecone_assistant_id");
    table.dropColumn("pinecone_assistant_status");
    table.dropColumn("pinecone_assistant_region");
    table.dropColumn("pinecone_assistant_instructions");
    table.dropColumn("pinecone_assistant_error");
    table.dropColumn("pinecone_assistant_updated_at");
  });
};
