/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.table("documents", function (table) {
    table.string("pinecone_file_id").nullable();
    table.text("pinecone_upload_error").nullable();
    table.timestamp("pinecone_upload_error_time").nullable();
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("documents", function (table) {
    table.dropColumn("pinecone_file_id");
    table.dropColumn("pinecone_upload_error");
    table.dropColumn("pinecone_upload_error_time");
  });
};
