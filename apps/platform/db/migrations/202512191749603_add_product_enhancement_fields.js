/**
 * Migration to add AI enhancement fields to the products table
 */

exports.up = async function (knex) {
  // Check if enhancement_status column exists
  const hasEnhancementStatus = await knex.schema.hasColumn(
    "products",
    "enhancement_status"
  );

  // Check if ai_enhanced_fields column exists
  const hasAiEnhancedFields = await knex.schema.hasColumn(
    "products",
    "ai_enhanced_fields"
  );

  // Check if enhancement_error column exists
  const hasEnhancementError = await knex.schema.hasColumn(
    "products",
    "enhancement_error"
  );

  // Create a transaction to ensure all operations succeed or fail together
  return knex.schema.alterTable("products", (table) => {
    // Add enhancement_status column if it doesn't exist
    if (!hasEnhancementStatus) {
      table.string("enhancement_status").nullable();
    }

    // Add ai_enhanced_fields column if it doesn't exist (stored as JSON array)
    if (!hasAiEnhancedFields) {
      table.json("ai_enhanced_fields").nullable();
    }

    // Add enhancement_error column if it doesn't exist
    if (!hasEnhancementError) {
      table.string("enhancement_error", 1000).nullable();
    }
  });
};

exports.down = async function (knex) {
  return knex.schema.alterTable("products", (table) => {
    table.dropColumn("enhancement_status");
    table.dropColumn("ai_enhanced_fields");
    table.dropColumn("enhancement_error");
  });
};
