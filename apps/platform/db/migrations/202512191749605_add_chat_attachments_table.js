exports.up = async function (knex) {
  return knex.schema.createTable("chat_attachments", function (table) {
    table.increments("id").primary();
    table.string("original_name").notNullable();
    table.string("content_type").notNullable();
    table.bigint("size").notNullable();
    table.string("path").notNullable();
    table.integer("location_id").unsigned().notNullable();
    table.string("chat_id").nullable();
    table.timestamp("uploaded_at").notNullable().defaultTo(knex.fn.now());
    table.integer("uploaded_by").unsigned().nullable();

    // Add indexes
    table.index("location_id");
    table.index("chat_id");

    // Add foreign keys
    table
      .foreign("location_id")
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");
    table
      .foreign("chat_id")
      .references("chat_id")
      .inTable("chats")
      .onDelete("CASCADE");
    table
      .foreign("uploaded_by")
      .references("id")
      .inTable("admins")
      .onDelete("SET NULL");
  });
};

exports.down = async function (knex) {
  return knex.schema.dropTable("chat_attachments");
};
