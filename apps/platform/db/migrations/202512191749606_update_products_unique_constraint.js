exports.up = async function (knex) {
  // First, drop the existing constraint
  try {
    await knex.schema.alterTable("products", function (table) {
      table.dropUnique(["product_id", "retailer_id"]);
    });
  } catch (error) {
    // If error is about constraint not existing, continue
    if (!error.message.includes("Can't DROP")) {
      throw error;
    }
    // Otherwise silently continue
  }

  // Add new constraint that includes location_id
  await knex.schema.alterTable("products", function (table) {
    table.unique(["product_id", "retailer_id", "location_id"]);
  });
};

exports.down = async function (knex) {
  // Revert changes by restoring original constraint
  try {
    await knex.schema.alterTable("products", function (table) {
      table.dropUnique(["product_id", "retailer_id", "location_id"]);
    });
  } catch (error) {
    // Constraint might not exist, ignore error
  }

  await knex.schema.alterTable("products", function (table) {
    table.unique(["product_id", "retailer_id"]);
  });
};
