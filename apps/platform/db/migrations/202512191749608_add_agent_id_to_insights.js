/**
 * Add agent_id and agent_name columns to insights table
 */
exports.up = async function (knex) {
  if (await knex.schema.hasColumn("insights", "agent_id")) {
    return;
  }

  return knex.schema.alterTable("insights", function (table) {
    table
      .string("agent_id")
      .comment("ID of the agent that generated this insight");
    table
      .string("agent_name")
      .comment("Name of the agent that generated this insight");
  });
};

exports.down = async function (knex) {
  if (!(await knex.schema.hasColumn("insights", "agent_id"))) {
    return;
  }

  return knex.schema.alterTable("insights", function (table) {
    table.dropColumn("agent_id");
    table.dropColumn("agent_name");
  });
};
