/**
 * Add auth_id column to users table to store authentication provider UIDs
 */

exports.up = async function (knex) {
  return knex.schema.alterTable("users", (table) => {
    // Add auth_id column with index for faster lookups
    table.string("auth_id", 255).unique().index();
  });
};

exports.down = async function (knex) {
  return knex.schema.alterTable("users", (table) => {
    table.dropColumn("auth_id");
  });
};
