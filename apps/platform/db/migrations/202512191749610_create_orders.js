/**
 * Create orders table for storing user checkout information
 */

exports.up = async function (knex) {
  await knex.schema.createTable("orders", (table) => {
    table.increments("id").primary();
    table.integer("user_id").unsigned().notNullable().index();
    table.integer("location_id").unsigned().notNullable().index();
    table.string("status", 50).notNullable().defaultTo("pending");
    table.decimal("total_amount", 10, 2).notNullable();
    table.string("currency", 3).notNullable().defaultTo("USD");
    table.jsonb("shipping_address").nullable();
    table.jsonb("billing_address").nullable();
    table.string("payment_intent_id").nullable();
    table.string("external_order_id").nullable();
    table.timestamp("created_at").notNullable().defaultTo(knex.fn.now());
    table.timestamp("updated_at").notNullable().defaultTo(knex.fn.now());

    // Add foreign key constraints
    table
      .foreign("user_id")
      .references("id")
      .inTable("users")
      .onDelete("CASCADE");
    table
      .foreign("location_id")
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");
  });

  await knex.schema.createTable("order_items", (table) => {
    table.increments("id").primary();
    table.integer("order_id").unsigned().notNullable().index();
    table.string("product_id").notNullable().index();
    table.integer("quantity").unsigned().notNullable().defaultTo(1);
    table.decimal("unit_price", 10, 2).notNullable();
    table.decimal("total_price", 10, 2).notNullable();
    table.jsonb("product_data").notNullable();
    table.timestamp("created_at").notNullable().defaultTo(knex.fn.now());
    table.timestamp("updated_at").notNullable().defaultTo(knex.fn.now());

    // Add foreign key constraints
    table
      .foreign("order_id")
      .references("id")
      .inTable("orders")
      .onDelete("CASCADE");
  });
};

exports.down = async function (knex) {
  await knex.schema.dropTableIfExists("order_items");
  await knex.schema.dropTableIfExists("orders");
};
