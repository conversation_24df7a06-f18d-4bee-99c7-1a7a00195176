/**
 * Ensure users table ID field is properly configured for auto-increment
 * This migration is safe to run multiple times
 */

exports.up = async function (knex) {
  // Check if we're using MySQL
  const isMySQL =
    knex.client.config.client === "mysql" ||
    knex.client.config.client === "mysql2";

  if (isMySQL) {
    // Ensure the ID column is AUTO_INCREMENT in MySQL
    await knex.raw(`
      ALTER TABLE users 
      MODIFY COLUMN id INT UNSIGNED NOT NULL AUTO_INCREMENT
    `);
  }

  // For other databases, the existing increments() should work fine
  return Promise.resolve();
};

exports.down = async function (knex) {
  // This migration is safe and doesn't need to be reversed
  return Promise.resolve();
};
