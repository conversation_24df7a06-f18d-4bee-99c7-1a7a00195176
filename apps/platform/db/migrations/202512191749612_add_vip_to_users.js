/**
 * Add vip column to users table to mark VIP customers
 */

exports.up = async function (knex) {
  return knex.schema.alterTable("users", (table) => {
    // Add vip boolean column, defaulting to false
    if (!knex.schema.hasColumn("users", "vip")) {
      table.boolean("vip").defaultTo(false).index();
    }
  });
};

exports.down = async function (knex) {
  return knex.schema.alterTable("users", (table) => {
    table.dropColumn("vip");
  });
};
