/**
 * Create unified menu_settings table for location-based menu customization
 * Replaces separate carousel slides and promo content tables
 */
exports.up = async function (knex) {
  if (await knex.schema.hasTable("menu_settings")) {
    return;
  }

  await knex.schema.createTable("menu_settings", (table) => {
    table.increments("id").primary();
    table
      .integer("location_id")
      .unsigned()
      .notNullable()
      .references("id")
      .inTable("locations")
      .onDelete("CASCADE");
    table.string("type").notNullable(); // 'carousel_slide', 'promo_content', 'announcement', etc.
    table.string("title").notNullable();
    table.text("description").nullable();
    table.string("image_url").nullable();
    table.string("link").nullable();
    table.integer("order").unsigned().defaultTo(0);
    table.boolean("active").defaultTo(true);
    table.timestamp("start_date").nullable();
    table.timestamp("end_date").nullable();
    table.jsonb("metadata").nullable(); // Additional flexible data storage
    table.timestamps(true, true);

    // Add indexes for efficient querying
    table.index(["location_id", "type", "active", "order"]);
    table.index("location_id");
    table.index(["start_date", "end_date"]);
  });
};

exports.down = async function (knex) {
  await knex.schema.dropTable("menu_settings");
};
