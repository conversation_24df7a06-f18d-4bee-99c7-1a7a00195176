/**
 * Add inventory tracking fields to products table
 * Adds out_of_stock, inventory_quantity, and last_restocked fields
 */
exports.up = async function (knex) {
  if (await knex.schema.hasColumn("products", "out_of_stock")) {
    return;
  }

  await knex.schema.alterTable("products", function (table) {
    // Add out of stock indicator
    table.boolean("out_of_stock").defaultTo(false);
    table.integer("inventory_quantity").nullable();
    table.timestamp("last_restocked").nullable();
  });

  // Add index for out of stock queries
  await knex.schema.alterTable("products", function (table) {
    table.index(["location_id", "out_of_stock"]);
    table.index(["location_id", "inventory_quantity"]);
  });
};

exports.down = async function (knex) {
  await knex.schema.alterTable("products", function (table) {
    table.dropIndex(["location_id", "out_of_stock"]);
    table.dropIndex(["location_id", "inventory_quantity"]);
    table.dropColumn("out_of_stock");
    table.dropColumn("inventory_quantity");
    table.dropColumn("last_restocked");
  });
};
