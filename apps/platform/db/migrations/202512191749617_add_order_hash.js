/**
 * Add order hash field for secure public order viewing
 */
exports.up = async function (knex) {
  await knex.schema.table("orders", (table) => {
    table.string("order_hash", 64).nullable().unique();
    table.index("order_hash");
  });
};

exports.down = async function (knex) {
  await knex.schema.table("orders", (table) => {
    table.dropIndex("order_hash");
    table.dropColumn("order_hash");
  });
};
