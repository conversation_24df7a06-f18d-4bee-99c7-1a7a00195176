/**
 * Fix users table auth_id constraint to allow same auth_id across different locations
 * Changes from UNIQUE(auth_id) to UNIQUE(location_id, auth_id)
 */
exports.up = async function (knex) {
  // First, check if the constraint exists and drop it
  const hasConstraint = await knex.schema
    .hasTable("users")
    .then(async (exists) => {
      if (!exists) return false;

      // Check if the constraint exists by trying to get table info
      try {
        const constraintExists = await knex.raw(`
        SELECT CONSTRAINT_NAME 
        FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'users' 
        AND CONSTRAINT_NAME = 'users_auth_id_unique'
      `);
        return constraintExists[0].length > 0;
      } catch (error) {
        // If we can't check, assume it exists for MySQL compatibility
        return true;
      }
    });

  if (hasConstraint) {
    // Drop the existing global unique constraint on auth_id
    await knex.raw("ALTER TABLE users DROP INDEX users_auth_id_unique");
  }

  // Add the new composite unique constraint on (location_id, auth_id)
  // Note: We use raw SQL to ensure the constraint name is consistent
  await knex.raw(
    "ALTER TABLE users ADD UNIQUE KEY users_location_auth_id_unique (location_id, auth_id)"
  );
};

exports.down = async function (knex) {
  // Drop the composite constraint
  await knex.raw("ALTER TABLE users DROP INDEX users_location_auth_id_unique");

  // Restore the original global unique constraint
  // Note: This may fail if there are duplicate auth_ids across locations
  try {
    await knex.raw(
      "ALTER TABLE users ADD UNIQUE KEY users_auth_id_unique (auth_id)"
    );
  } catch (error) {
    console.warn(
      "Warning: Could not restore original auth_id unique constraint. This may be due to duplicate auth_ids across locations."
    );
    console.warn(
      "Manual cleanup may be required before running this rollback."
    );
    throw error;
  }
};
