exports.seed = async function(knex) {
  // Deletes ALL existing entries
  await knex('insight_patterns').del();
  
  // Insert default non-actionable patterns
  await knex('insight_patterns').insert([
    {
      pattern: 'implement.*analysis',
      description: 'Phrases about implementing analysis',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Implement Product Category Analysis',
        'Implement Customer Analysis',
        'Implement Market Analysis'
      ])
    },
    {
      pattern: 'develop.*strateg',
      description: 'Phrases about developing strategies',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Develop Age-Specific Marketing Strategies',
        'Develop Customer Retention Strategy',
        'Develop Pricing Strategy'
      ])
    },
    {
      pattern: 'create.*framework',
      description: 'Phrases about creating frameworks',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Create Analytics Framework',
        'Create Reporting Framework',
        'Create Performance Framework'
      ])
    },
    {
      pattern: 'establish.*process',
      description: 'Phrases about establishing processes',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Establish Review Process',
        'Establish Quality Process',
        'Establish Monitoring Process'
      ])
    },
    {
      pattern: 'build.*system',
      description: 'Phrases about building systems',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Build Tracking System',
        'Build Reporting System',
        'Build Analytics System'
      ])
    },
    {
      pattern: 'design.*approach',
      description: 'Phrases about designing approaches',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Design Marketing Approach',
        'Design Customer Approach',
        'Design Sales Approach'
      ])
    },
    {
      pattern: 'conduct.*research',
      description: 'Phrases about conducting research',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Conduct Market Research',
        'Conduct Customer Research',
        'Conduct Competitive Research'
      ])
    },
    {
      pattern: 'perform.*audit',
      description: 'Phrases about performing audits',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Perform Security Audit',
        'Perform Process Audit',
        'Perform System Audit'
      ])
    },
    {
      pattern: 'review.*policies',
      description: 'Phrases about reviewing policies',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Review Privacy Policies',
        'Review Security Policies',
        'Review Business Policies'
      ])
    },
    {
      pattern: 'analyze.*trends',
      description: 'Phrases about analyzing trends',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Analyze Market Trends',
        'Analyze Customer Trends',
        'Analyze Sales Trends'
      ])
    },
    {
      pattern: 'consider.*implementing',
      description: 'Phrases about considering implementation',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Consider implementing new features',
        'Consider implementing changes',
        'Consider implementing improvements'
      ])
    },
    {
      pattern: 'should.*analyze',
      description: 'Phrases suggesting analysis should be done',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Should analyze customer behavior',
        'Should analyze market conditions',
        'Should analyze performance metrics'
      ])
    },
    {
      pattern: 'need.*to.*study',
      description: 'Phrases about needing to study something',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Need to study market conditions',
        'Need to study customer preferences',
        'Need to study competitor strategies'
      ])
    },
    {
      pattern: 'recommend.*reviewing',
      description: 'Phrases recommending reviews',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Recommend reviewing current processes',
        'Recommend reviewing performance metrics',
        'Recommend reviewing customer feedback'
      ])
    },
    {
      pattern: 'develop.*presence',
      description: 'Phrases about developing presence (social media, market, etc.)',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify([
        'Develop a Social Media Presence',
        'Develop Market Presence',
        'Develop Online Presence'
      ])
    }
  ]);
};
