/**
 * Seed file for insight_patterns table
 * Populates the table with default non-actionable patterns from insight-actionability-enhancement.md
 */

exports.seed = async function(knex) {
  // Delete existing entries (optional - remove if you want to preserve existing data)
  await knex('insight_patterns').del();

  // Insert default non-actionable patterns
  const patterns = [
    {
      pattern: 'implement.*analysis',
      description: 'Phrases about implementing analysis',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Implement Product Category Analysis', 'Implement customer analysis', 'Implement sales analysis'])
    },
    {
      pattern: 'develop.*strateg',
      description: 'Phrases about developing strategies',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Develop Age-Specific Marketing Strategies', 'Develop pricing strategy', 'Develop content strategy'])
    },
    {
      pattern: 'develop.*presence',
      description: 'Phrases about developing presence',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Develop a Social Media Presence', 'Develop online presence', 'Develop brand presence'])
    },
    {
      pattern: 'create.*framework',
      description: 'Phrases about creating frameworks',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Create Analytics Framework', 'Create testing framework', 'Create evaluation framework'])
    },
    {
      pattern: 'establish.*process',
      description: 'Phrases about establishing processes',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Establish Review Process', 'Establish approval process', 'Establish workflow process'])
    },
    {
      pattern: 'build.*system',
      description: 'Phrases about building systems',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Build Tracking System', 'Build monitoring system', 'Build reporting system'])
    },
    {
      pattern: 'design.*approach',
      description: 'Phrases about designing approaches',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Design Marketing Approach', 'Design customer approach', 'Design sales approach'])
    },
    {
      pattern: 'conduct.*research',
      description: 'Phrases about conducting research',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Conduct Market Research', 'Conduct customer research', 'Conduct competitive research'])
    },
    {
      pattern: 'perform.*audit',
      description: 'Phrases about performing audits',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Perform Security Audit', 'Perform compliance audit', 'Perform process audit'])
    },
    {
      pattern: 'review.*policies',
      description: 'Phrases about reviewing policies',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Review Privacy Policies', 'Review security policies', 'Review operational policies'])
    },
    {
      pattern: 'analyze.*trends',
      description: 'Phrases about analyzing trends',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Analyze Market Trends', 'Analyze customer trends', 'Analyze sales trends'])
    },
    {
      pattern: 'consider.*implementing',
      description: 'Phrases about considering implementation',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Consider implementing new features', 'Consider implementing changes', 'Consider implementing improvements'])
    },
    {
      pattern: 'should.*analyze',
      description: 'Phrases suggesting analysis should be done',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Should analyze customer behavior', 'Should analyze performance', 'Should analyze results'])
    },
    {
      pattern: 'need.*to.*study',
      description: 'Phrases about needing to study',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Need to study market conditions', 'Need to study customer preferences', 'Need to study competition'])
    },
    {
      pattern: 'recommend.*reviewing',
      description: 'Phrases recommending reviews',
      type: 'non_actionable',
      is_active: true,
      priority: 10,
      examples: JSON.stringify(['Recommend reviewing current processes', 'Recommend reviewing strategies', 'Recommend reviewing policies'])
    }
  ];

  // Insert the patterns
  await knex('insight_patterns').insert(patterns);

  console.log(`✅ Seeded ${patterns.length} default insight patterns`);
};
