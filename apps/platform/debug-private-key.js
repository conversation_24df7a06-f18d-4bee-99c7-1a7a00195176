require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: '../../.env' });

console.log('Raw FIREBASE_PRIVATE_KEY value:');
console.log('Length:', process.env.FIREBASE_PRIVATE_KEY?.length || 0);
console.log('First 100 chars:', process.env.FIREBASE_PRIVATE_KEY?.substring(0, 100) || '(missing)');
console.log('Last 100 chars:', process.env.FIREBASE_PRIVATE_KEY?.substring(process.env.FIREBASE_PRIVATE_KEY.length - 100) || '(missing)');

console.log('\nAfter replace(/\\\\n/g, "\\n"):');
const processedKey = process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n');
console.log('Length:', processedKey?.length || 0);
console.log('First 100 chars:', processedKey?.substring(0, 100) || '(missing)');
console.log('Last 100 chars:', processedKey?.substring(processedKey.length - 100) || '(missing)');
