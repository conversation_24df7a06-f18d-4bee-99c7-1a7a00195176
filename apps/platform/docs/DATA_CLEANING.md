# Data Cleaning Service

The Data Cleaning Service provides robust handling of empty, null, and invalid fields from external data sources. It automatically applies appropriate default values and transformations to ensure data integrity.

## Overview

When importing data from external sources (CSV files, APIs, etc.), it's common to encounter:
- Empty strings (`""`)
- Null values (`null`, `undefined`)
- String representations of null (`"NULL"`, `"null"`, `"undefined"`)
- Invalid data formats
- Inconsistent data types

The Data Cleaning Service handles these cases by:
1. **Detecting empty/invalid values**
2. **Applying appropriate default values**
3. **Transforming data to correct formats**
4. **Validating data integrity**

## Usage

### Backend (Node.js/TypeScript)

```typescript
import { DataCleaningService } from "../core/DataCleaningService";

// Clean a single field
const cleanedName = DataCleaningService.cleanField("first_name", ""); // Returns "Unknown"
const cleanedEmail = DataCleaningService.cleanField("email", "  <EMAIL>  "); // Returns "<EMAIL>"

// Clean an entire row/object
const dirtyData = {
  first_name: "",
  last_name: null,
  email: "  <EMAIL>  ",
  phone: "1234567890",
  price: "invalid"
};

const cleanedData = DataCleaningService.cleanRow(dirtyData);
// Result:
// {
//   first_name: "Unknown",
//   last_name: "User", 
//   email: "<EMAIL>",
//   phone: "+1234567890",
//   price: 0
// }

// Clean multiple rows
const cleanedRows = DataCleaningService.cleanRows(arrayOfDirtyData);

// Get cleaning statistics
const stats = DataCleaningService.getCleaningStats(originalRows, cleanedRows);
console.log(stats.fieldsWithDefaults); // Shows which fields got default values
```

### Frontend (React/TypeScript)

```typescript
import { cleanFormData, getDisplayValue, useFormDataCleaner } from "../../utils/dataCleaningUtils";

// Clean form data before submission
const handleSubmit = (formData) => {
  const cleanedData = cleanFormData(formData);
  // Submit cleanedData...
};

// Get display values for form fields
const displayName = getDisplayValue("full_name", user.full_name); // Shows "Unknown User" if empty

// Use the hook for React components
const { cleanField, cleanFormData, validateRequiredFields } = useFormDataCleaner();
```

## Default Field Configurations

### User Fields
| Field | Default Value | Required | Transformation |
|-------|---------------|----------|----------------|
| `first_name` | "Unknown" | Yes | Trim whitespace |
| `last_name` | "User" | Yes | Trim whitespace |
| `full_name` | "Unknown User" | Yes | Trim whitespace |
| `email` | "" | No | Lowercase, trim, validate format |
| `phone` | "" | No | Add + prefix if missing |
| `address` | "Unknown Address" | No | Trim whitespace |
| `city` | "Unknown City" | No | Trim whitespace |
| `state` | "Unknown State" | No | Uppercase, trim |
| `zip_code` | "00000" | No | Trim whitespace |
| `country` | "US" | No | Uppercase, trim |

### Product Fields
| Field | Default Value | Required | Transformation |
|-------|---------------|----------|----------------|
| `product_name` | "Unknown Product" | Yes | Trim whitespace |
| `brand` | "Unknown Brand" | No | Trim whitespace |
| `category` | "Uncategorized" | No | Trim whitespace |
| `description` | "" | No | Trim whitespace |

### Numeric Fields
| Field | Default Value | Required | Transformation |
|-------|---------------|----------|----------------|
| `price` | 0 | No | Convert to number, validate |
| `quantity` | 0 | No | Convert to number, validate |

### Date Fields
| Field | Default Value | Required | Transformation |
|-------|---------------|----------|----------------|
| `created_at` | null | No | Parse to Date object |
| `updated_at` | null | No | Parse to Date object |

## Custom Field Configurations

You can add custom field configurations:

```typescript
import { DataCleaningService, FieldCleaningConfig } from "../core/DataCleaningService";

const customConfig: FieldCleaningConfig = {
  field: "custom_field",
  defaultValue: "custom_default",
  required: true,
  validator: (value) => value.length > 0,
  transformer: (value) => value.toUpperCase(),
};

DataCleaningService.addFieldConfig(customConfig);

// Or add multiple configurations
DataCleaningService.addFieldConfigs([config1, config2, config3]);
```

## Integration Points

### User Import Service
The `UserImport.ts` service automatically uses the Data Cleaning Service:

```typescript
// In UserImport.ts
const cleanedRow = DataCleaningService.cleanRow(row);
const { external_id, email, phone, timezone, locale, created_at, ...data } = cleanedRow;
```

### Template Validation
The `AutomationCreator.tsx` automatically handles empty template fields:

```typescript
// Automatically sets defaults for empty template fields
if (!from.name || from.name.trim() === "") {
  from.name = "Unknown Sender";
}
if (!from.address || from.address.trim() === "") {
  from.address = "<EMAIL>";
}
```

### Form Handling
React forms automatically clean data before submission:

```typescript
const cleanedData = cleanFormData({
  email: formData.email,
  phone: formData.phone,
  full_name: formData.full_name,
});
```

## Error Handling

The service includes comprehensive error handling:

1. **Validation Errors**: Invalid data falls back to default values
2. **Transformation Errors**: Caught and logged, defaults applied
3. **Type Errors**: Automatic type conversion with fallbacks

## Logging and Monitoring

The service provides detailed logging:

```typescript
// Cleaning statistics are logged during import
console.log("User import cleaning statistics:", {
  totalRows: stats.totalRows,
  fieldsWithDefaults: stats.fieldsWithDefaults,
  emptyFieldsFound: stats.emptyFieldsFound,
});
```

## Best Practices

1. **Always clean external data** before processing
2. **Use appropriate defaults** for your business logic
3. **Validate critical fields** with custom validators
4. **Monitor cleaning statistics** to identify data quality issues
5. **Test with real-world data** to ensure proper handling

## Testing

Comprehensive tests are available in `DataCleaningService.test.ts`:

```bash
npm test DataCleaningService.test.ts
```

The tests cover:
- Empty value detection
- Default value application
- Data transformation
- Validation logic
- Real-world scenarios
- Statistics generation

## Migration from Old Cleaning Logic

The old `cleanRow` and `cleanCell` functions in `UserImport.ts` have been replaced with the new `DataCleaningService`. The new service provides:

- **More comprehensive** empty value detection
- **Configurable defaults** per field type
- **Better validation** and transformation
- **Statistics and monitoring**
- **Consistent behavior** across frontend and backend
- **Extensible configuration** for custom fields

## Performance Considerations

The Data Cleaning Service is optimized for:
- **Batch processing** of large datasets
- **Memory efficiency** with streaming data
- **Fast field lookups** using Map-based configurations
- **Minimal overhead** for clean data
