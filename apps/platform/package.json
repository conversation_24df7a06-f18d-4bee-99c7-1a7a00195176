{"name": "@bakedBot/platform", "version": "0.1.0", "main": "build/index.js", "types": "build/index.d.ts", "dependencies": {"@aws-sdk/client-s3": "^3.171.0", "@aws-sdk/client-ses": "^3.121.0", "@aws-sdk/client-sns": "^3.121.0", "@aws-sdk/client-sqs": "^3.171.0", "@aws-sdk/lib-storage": "^3.171.0", "@bugsnag/js": "^7.20.0", "@bugsnag/plugin-koa": "^7.19.0", "@koa/cors": "^5.0.0", "@koa/router": "^11.0.1", "@ladjs/country-language": "^1.0.3", "@langchain/community": "^0.3.42", "@langchain/core": "^0.3.51", "@langchain/langgraph": "^0.2.67", "@node-saml/node-saml": "^4.0.5", "@pinecone-database/pinecone": "^5.1.1", "@prisma/client": "^6.3.0", "@segment/analytics-node": "^1.0.0-beta.23", "@sendgrid/client": "^8.1.4", "@sendgrid/mail": "^8.1.5", "@sentry/node": "^7.46.0", "@sentry/utils": "^7.46.0", "@supabase/mcp-server-supabase": "^0.4.0", "@supabase/supabase-js": "^2.49.1", "@types/pdf-parse": "^1.1.4", "@types/qrcode": "^1.5.5", "@types/ws": "^8.5.14", "ajv": "^8.11.0", "ajv-errors": "^3.0.0", "ajv-formats": "^2.1.1", "bullmq": "^5.12.14", "busboy": "^1.6.0", "child-process-promise": "^2.2.1", "csv-parse": "^5.3.3", "csv-parser": "^3.0.0", "date-fns": "^2.29.2", "date-fns-tz": "^1.3.7", "dotenv": "^16.0.1", "eslint-plugin-import": "^2.29.0", "eventemitter2": "^6.4.9", "firebase-admin": "12.1.1", "handlebars": "^4.7.7", "handlebars-utils": "^1.0.6", "hashids": "^2.2.10", "html-to-text": "^9.0.4", "ioredis": "^5.3.1", "jsonpath": "^1.1.1", "jsonwebtoken": "^9.0.0", "knex": "^3.1.0", "koa": "^2.13.4", "koa-body": "5.0.0", "koa-bodyparser": "^4.4.1", "koa-jwt": "^4.0.3", "koa-send": "^5.0.1", "koa-static": "^5.0.0", "koa2-swagger-ui": "^5.11.0", "langchain": "^0.3.24", "libphonenumber-js": "^1.10.24", "mammoth": "^1.6.0", "mysql2": "^3.13.0", "node-pushnotifications": "^3.0.0", "node-schedule": "^2.1.0", "nodemailer": "^6.9.5", "nodemailer-mailgun-transport": "^2.1.5", "nodemailer-sendgrid": "^1.0.3", "openai": "^4.97.0", "openid-client": "^5.2.1", "pdf-parse": "^1.1.1", "pdf-text-extract": "^1.5.0", "pdf2json": "^3.1.6", "pg": "^8.15.6", "pino": "^8.1.0", "pino-pretty": "^8.1.0", "posthog-node": "^3.1.2", "qrcode": "^1.5.4", "qs": "^6.14.0", "raw-body": "^3.0.0", "reflect-metadata": "^0.1.13", "rrule": "2.7.2", "socket.io": "^4.7.2", "sqs-consumer": "^7.0.0", "stripe": "^17.7.0", "swagger-jsdoc": "^6.2.8", "typeorm": "^0.3.22", "ws": "^8.18.0", "xlsx": "^0.18.5", "zod": "^3.24.1", "zod-to-json-schema": "^3.24.5"}, "scripts": {"start": "nodemon", "build": "tsc --build", "lint": "eslint --ext .ts --max-warnings 0 src/", "test": "jest --forceExit --runInBand --testTimeout 10000", "migration:create": "node ./scripts/create-migration.mjs", "package:publish": "npm run build && npm version $npm_config_tag --no-git-tag-version && npm pack && npm publish --tag=latest --access public", "postbuild": "cp -r src/insights/prompts build/insights/", "generate:swagger": "node scripts/generate-swagger.js"}, "devDependencies": {"@types/busboy": "^1.5.0", "@types/html-to-text": "^9.0.0", "@types/jest": "^28.1.6", "@types/js-yaml": "^4.0.9", "@types/jsonpath": "^0.2.0", "@types/jsonwebtoken": "^9.0.5", "@types/koa__cors": "^3.3.0", "@types/koa__router": "^8.0.11", "@types/koa-bodyparser": "^4.3.12", "@types/koa-static": "^4.0.2", "@types/lodash": "^4.17.13", "@types/mime-types": "^2.1.4", "@types/node": "^22.15.3", "@types/node-pushnotifications": "^1.0.4", "@types/node-schedule": "^2.1.0", "@types/nodemailer": "^6.4.4", "@types/nodemailer-mailgun-transport": "^1.4.3", "@types/nodemailer-sendgrid": "^1.0.1", "@types/supertest": "^2.0.12", "@types/swagger-jsdoc": "^6.0.4", "@typescript-eslint/eslint-plugin": "^5.30.5", "@typescript-eslint/parser": "^5.30.5", "eslint": "^8.19.0", "eslint-config-standard": "^17.0.0", "ioredis-mock": "^8.8.3", "jest": "^28.1.3", "nodemon": "^2.0.19", "supertest": "^6.3.3", "ts-jest": "^28.0.7", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "files": ["/build", "/db"]}