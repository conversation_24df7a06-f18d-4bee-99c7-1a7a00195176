{"openapi": "3.0.0", "info": {"title": "BakedBot API", "version": "1.0.0", "description": "API documentation for BakedBot platform"}, "servers": [{"url": "/api", "description": "API Server"}], "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}, "ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Authorization", "description": "Location API key in the format 'Bearer YOUR_API_KEY'. Required for all endpoints."}, "UserAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "x-user-token", "description": "Optional user authentication token for enhanced features like chat history persistence. Use alongside ApiKeyAuth."}}, "schemas": {"Agent": {"type": "object", "required": ["id", "name", "role", "description", "icon", "capabilities"], "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "description": {"type": "string"}, "icon": {"type": "string"}, "capabilities": {"type": "array", "items": {"type": "string"}}}}, "AgentAvailability": {"type": "object", "required": ["id", "isAvailable", "partiallyAvailable", "reasons"], "properties": {"id": {"type": "string"}, "isAvailable": {"type": "boolean"}, "partiallyAvailable": {"type": "boolean"}, "reasons": {"type": "array", "items": {"type": "string"}}}}, "AgentAvailabilityReport": {"type": "object", "properties": {"summary": {"type": "object", "properties": {"total": {"type": "integer"}, "available": {"type": "integer"}, "partial": {"type": "integer"}, "unavailable": {"type": "integer"}}}, "available": {"type": "array", "items": {"$ref": "#/components/schemas/AgentAvailability"}}, "partial": {"type": "array", "items": {"$ref": "#/components/schemas/AgentAvailability"}}, "unavailable": {"type": "array", "items": {"$ref": "#/components/schemas/AgentAvailability"}}, "locationId": {"type": "integer"}}}, "AgentRequirements": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "data_requirements": {"type": "object", "additionalProperties": true}}}, "Admin": {"type": "object", "required": ["id", "organization_id", "email", "role"], "properties": {"id": {"type": "number"}, "organization_id": {"type": "number"}, "email": {"type": "string", "format": "email"}, "role": {"type": "string", "enum": ["admin", "editor", "viewer"]}, "first_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "SegmentEvent": {"type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["identify", "alias", "track"], "description": "The type of Segment event"}, "event": {"type": "string", "nullable": true, "description": "Event name (required for track events)"}, "anonymousId": {"type": "string", "nullable": true, "description": "Anonymous ID of the user"}, "userId": {"type": "string", "nullable": true, "description": "User ID of the user"}, "previousId": {"type": "string", "nullable": true, "description": "Previous ID for alias events"}, "properties": {"type": "object", "nullable": true, "additionalProperties": true, "description": "Event properties (for track events)"}, "traits": {"type": "object", "nullable": true, "additionalProperties": true, "description": "User traits (for identify events)"}, "context": {"type": "object", "nullable": true, "additionalProperties": true, "description": "Event context"}, "timestamp": {"type": "string", "format": "date-time", "description": "Event timestamp"}}, "oneOf": [{"required": ["anonymousId"]}, {"required": ["userId"]}]}, "SegmentEventBatch": {"type": "array", "items": {"$ref": "#/components/schemas/SegmentEvent"}, "minItems": 1, "maxItems": 1000, "description": "Batch of Segment events to process"}, "CoaData": {"type": "object", "properties": {"id": {"type": "integer"}, "location_id": {"type": "integer"}, "product_id": {"type": "integer"}, "test_date": {"type": "string", "format": "date"}, "data": {"type": "object", "additionalProperties": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "CoaImportResponse": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "string"}, "errors": {"type": "array", "items": {"type": "string"}}, "imported": {"type": "integer", "nullable": true}, "skipped": {"type": "integer", "nullable": true}}}, "CoaListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/CoaData"}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}, "pages": {"type": "integer"}}}}}, "CoaProductListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/CoaData"}}, "count": {"type": "integer"}}}, "CoaErrorResponse": {"type": "object", "properties": {"error": {"type": "string"}}}, "DashboardResponse": {"type": "object", "properties": {"hasData": {"type": "boolean"}, "timeframe": {"type": "string"}, "kpi": {"type": "object", "properties": {"revenue": {"type": "number", "nullable": true}, "customers": {"type": "integer"}, "averageOrder": {"type": "number", "nullable": true}, "customerValue": {"type": "number", "nullable": true}, "products": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "sales": {"type": "integer"}, "revenue": {"type": "number"}, "averagePrice": {"type": "number"}}}}}}, "charts": {"type": "object", "properties": {"revenue": {"type": "array", "items": {"type": "object"}}, "sales": {"type": "array", "items": {"type": "object"}}}}, "insights": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "impact": {"type": "string"}, "type": {"type": "string"}, "actions": {"type": "array", "items": {"type": "string"}}}}}, "customerMetrics": {"type": "object", "properties": {"total": {"type": "integer"}, "purchasedInTimeframe": {"type": "integer"}, "new": {"type": "integer"}, "returning": {"type": "integer"}}}, "analytics": {"type": "object", "properties": {"ageDistribution": {"type": "object", "nullable": true, "properties": {"under_30": {"type": "integer"}, "age_30_45": {"type": "integer"}, "age_46_60": {"type": "integer"}, "over_60": {"type": "integer"}, "total_with_age": {"type": "integer"}}}, "budtenderPerformance": {"type": "array", "nullable": true, "items": {"type": "object", "properties": {"budtender_name": {"type": "string"}, "total_sales": {"type": "integer"}, "revenue": {"type": "number"}, "average_sale": {"type": "number"}, "total_profit": {"type": "number"}, "unique_customers": {"type": "integer"}, "profit_margin": {"type": "number"}}}}, "customerTypeAnalysis": {"type": "array", "nullable": true, "items": {"type": "object", "properties": {"customer_type": {"type": "string"}, "total_orders": {"type": "integer"}, "revenue": {"type": "number"}, "average_order": {"type": "number"}, "unique_customers": {"type": "integer"}, "profit_margin": {"type": "number"}}}}, "profitabilityAnalysis": {"type": "object", "nullable": true, "properties": {"total_profit": {"type": "number"}, "total_cost": {"type": "number"}, "total_discounts": {"type": "number"}, "total_loyalty_discounts": {"type": "number"}, "profit_margin": {"type": "number"}, "total_revenue": {"type": "number"}}}}}}}, "InsightGenerateRequest": {"type": "object", "properties": {"model": {"type": "string"}}}, "InsightGenerateResponse": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "impact": {"type": "string"}, "type": {"type": "string"}, "actions": {"type": "array", "items": {"type": "string"}}}}}, "AutomationPlanRequest": {"type": "object", "properties": {"model": {"type": "string"}, "imageQuality": {"type": "string", "enum": ["SD", "HD"], "default": "HD"}}}, "AutomationPlanResponse": {"type": "object", "properties": {"plan": {"type": "object"}, "items": {"type": "object"}}}, "RegenerateStepRequest": {"type": "object", "properties": {"stepIndex": {"type": "integer", "nullable": true}, "itemKey": {"type": "string", "nullable": true}, "model": {"type": "string", "nullable": true}, "prompt": {"type": "string", "nullable": true}, "imageQuality": {"type": "string", "enum": ["SD", "HD"], "default": "HD"}}}, "MarketInsightsResponse": {"type": "object", "properties": {"market_size": {"type": "number", "description": "Estimated market size in dollars"}, "growth_rate": {"type": "number", "description": "Annual growth rate as a percentage"}, "competition_level": {"type": "string", "enum": ["low", "medium", "high"], "description": "Level of competition in the market"}, "demographics": {"type": "object", "properties": {"age_distribution": {"type": "object", "additionalProperties": true}, "income_levels": {"type": "object", "additionalProperties": true}}}, "regulations": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "description": {"type": "string"}, "impact": {"type": "string"}}}}, "trends": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "impact": {"type": "string"}}}}, "recommendations": {"type": "array", "items": {"type": "object", "properties": {"category": {"type": "string"}, "suggestion": {"type": "string"}, "priority": {"type": "string", "enum": ["low", "medium", "high"]}}}}}}, "Journey": {"type": "object", "required": ["id", "name", "location_id", "published"], "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}, "published": {"type": "boolean"}, "location_id": {"type": "integer"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true}}}, "JourneyStep": {"type": "object", "required": ["type", "x", "y"], "properties": {"type": {"type": "string"}, "name": {"type": "string", "nullable": true}, "data": {"type": "object", "nullable": true, "additionalProperties": true}, "data_key": {"type": "string", "nullable": true}, "x": {"type": "number"}, "y": {"type": "number"}, "children": {"type": "array", "nullable": true, "items": {"type": "object", "required": ["external_id"], "properties": {"external_id": {"type": "string"}, "path": {"type": "string", "nullable": true}, "data": {"type": "object", "nullable": true, "additionalProperties": true}}}}}}, "JourneySuggestion": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "published": {"type": "boolean"}, "steps": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/JourneyStep"}}}}, "JourneyUserStep": {"type": "object", "properties": {"id": {"type": "integer"}, "user_id": {"type": "integer"}, "journey_id": {"type": "integer"}, "entrance_id": {"type": "integer", "nullable": true}, "ended_at": {"type": "string", "format": "date-time", "nullable": true}}}, "JourneyEntrance": {"type": "object", "properties": {"journey": {"$ref": "#/components/schemas/Journey"}, "user": {"$ref": "#/components/schemas/User"}, "userSteps": {"type": "array", "items": {"$ref": "#/components/schemas/JourneyUserStep"}}}}, "JourneyTriggerRequest": {"type": "object", "required": ["entrance_id", "user"], "properties": {"entrance_id": {"type": "integer"}, "user": {"type": "object", "required": ["external_id"], "properties": {"external_id": {"type": "string"}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "device_token": {"type": "string", "nullable": true}, "timezone": {"type": "string", "nullable": true}, "locale": {"type": "string", "nullable": true}}}, "event": {"type": "object", "nullable": true, "additionalProperties": true}}}, "Rule": {"type": "object", "required": ["uuid", "type", "group", "path", "operator"], "properties": {"id": {"type": "number", "nullable": true}, "uuid": {"type": "string"}, "root_uuid": {"type": "string", "nullable": true}, "parent_uuid": {"type": "string", "nullable": true}, "type": {"type": "string", "enum": ["wrapper", "string", "number", "boolean", "date", "array"]}, "group": {"type": "string", "enum": ["user", "event", "parent", "pos"]}, "path": {"type": "string"}, "operator": {"type": "string"}, "value": {"oneOf": [{"type": "string"}, {"type": "number"}, {"type": "boolean"}], "nullable": true}, "children": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/Rule"}}}}, "List": {"type": "object", "required": ["id", "name", "type", "location_id"], "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "type": {"type": "string", "enum": ["static", "dynamic"]}, "description": {"type": "string", "nullable": true}, "location_id": {"type": "number"}, "rule": {"$ref": "#/components/schemas/Rule", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}, "is_visible": {"type": "boolean", "nullable": true}, "published": {"type": "boolean", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true}}}, "ListSuggestion": {"type": "object", "required": ["name", "description", "rule"], "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "rule": {"$ref": "#/components/schemas/Rule"}, "confidence": {"type": "number", "format": "float", "minimum": 0, "maximum": 1}}}, "User": {"type": "object", "required": ["id", "email"], "properties": {"id": {"type": "number"}, "email": {"type": "string", "format": "email"}, "first_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "LocationAdmin": {"type": "object", "properties": {"id": {"type": "integer", "description": "Admin ID"}, "location_id": {"type": "integer", "description": "ID of the location"}, "admin_id": {"type": "integer", "description": "ID of the admin user"}, "role": {"type": "string", "description": "Role assigned to the admin in this location"}, "created_at": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}, "admin": {"type": "object", "properties": {"id": {"type": "integer", "description": "Admin user ID"}, "email": {"type": "string", "format": "email", "description": "Admin email address"}, "name": {"type": "string", "description": "<PERSON><PERSON>'s full name"}, "role": {"type": "string", "description": "Admin's global role"}}}}}, "LocationAdminParams": {"type": "object", "required": ["role"], "properties": {"role": {"type": "string", "enum": ["admin", "manager", "staff"], "description": "Role to assign to the admin"}}}, "LocationAdminCreateParams": {"type": "object", "required": ["role", "email"], "properties": {"role": {"type": "string", "enum": ["admin", "manager", "staff"], "description": "Role to assign to the admin"}, "email": {"type": "string", "format": "email", "description": "Email address of the admin to add"}}}, "LocationAdminListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LocationAdmin"}}, "total": {"type": "integer", "description": "Total number of admins"}, "page": {"type": "integer", "description": "Current page number"}, "per_page": {"type": "integer", "description": "Number of items per page"}}}, "LocationApiKey": {"type": "object", "properties": {"id": {"type": "integer", "description": "API key ID"}, "location_id": {"type": "integer", "description": "ID of the location this key belongs to"}, "name": {"type": "string", "description": "Name of the API key"}, "description": {"type": "string", "nullable": true, "description": "Optional description of the API key's purpose"}, "scope": {"type": "string", "enum": ["public", "secret"], "description": "Scope of the API key"}, "role": {"type": "string", "description": "Role assigned to this API key"}, "key": {"type": "string", "description": "The actual API key value"}, "created_at": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true, "description": "Deletion timestamp if revoked"}}}, "LocationApiKeyParams": {"type": "object", "required": ["name"], "properties": {"scope": {"type": "string", "enum": ["public", "secret"], "description": "Scope of the API key"}, "name": {"type": "string", "description": "Name of the API key"}, "description": {"type": "string", "nullable": true, "description": "Optional description of the API key's purpose"}, "role": {"type": "string", "description": "Role assigned to this API key"}}}, "LocationApiKeyListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/LocationApiKey"}}, "total": {"type": "integer", "description": "Total number of API keys"}, "page": {"type": "integer", "description": "Current page number"}, "per_page": {"type": "integer", "description": "Number of items per page"}}}, "Location": {"type": "object", "required": ["id", "name", "locale", "timezone"], "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}, "facebook": {"type": "string", "nullable": true}, "twitter": {"type": "string", "nullable": true}, "instagram": {"type": "string", "nullable": true}, "linkedin": {"type": "string", "nullable": true}, "locale": {"type": "string"}, "timezone": {"type": "string"}, "text_opt_out_message": {"type": "string", "nullable": true}, "text_help_message": {"type": "string", "nullable": true}, "link_wrap_email": {"type": "boolean", "nullable": true}, "link_wrap_push": {"type": "boolean", "nullable": true}, "sender_email": {"type": "string", "nullable": true}, "sender_name": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "zip": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "longitude": {"type": "number", "nullable": true}, "latitude": {"type": "number", "nullable": true}, "retailer_id": {"type": "string", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}, "deleted_at": {"type": "string", "format": "date-time", "nullable": true}}}, "Locale": {"type": "object", "properties": {"id": {"type": "integer", "description": "Locale ID"}, "location_id": {"type": "integer", "description": "ID of the location this locale belongs to"}, "key": {"type": "string", "description": "Unique key identifier for the locale"}, "label": {"type": "string", "description": "Display label for the locale"}, "created_at": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}}, "LocaleParams": {"type": "object", "required": ["key", "label"], "properties": {"key": {"type": "string", "description": "Unique key identifier for the locale"}, "label": {"type": "string", "description": "Display label for the locale"}}}, "LocaleListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Locale"}}, "total": {"type": "integer", "description": "Total number of locales"}, "page": {"type": "integer", "description": "Current page number"}, "per_page": {"type": "integer", "description": "Number of items per page"}}}, "Address": {"type": "object", "required": ["name", "line1", "city", "state", "postal_code", "country"], "properties": {"name": {"type": "string", "description": "Full name for the address"}, "line1": {"type": "string", "description": "Street address line 1"}, "line2": {"type": "string", "description": "Street address line 2 (optional)"}, "city": {"type": "string", "description": "City name"}, "state": {"type": "string", "description": "State/Province/Region"}, "postal_code": {"type": "string", "description": "ZIP or postal code"}, "country": {"type": "string", "description": "Country name"}, "phone": {"type": "string", "description": "Contact phone number (optional)"}}}, "UserInfo": {"type": "object", "required": ["birthdate"], "properties": {"email": {"type": "string", "format": "email", "description": "User's email address"}, "phone": {"type": "string", "description": "User's phone number"}, "firstName": {"type": "string", "description": "User's first name"}, "lastName": {"type": "string", "description": "User's last name"}, "birthdate": {"type": "string", "format": "date", "description": "User's birth date (YYYY-MM-DD format)"}}}, "OrderItem": {"type": "object", "required": ["product_id", "quantity"], "properties": {"product_id": {"type": "string", "description": "Unique identifier for the product"}, "quantity": {"type": "integer", "minimum": 1, "description": "Number of items to order"}}}, "Order": {"type": "object", "properties": {"id": {"type": "integer", "description": "Order unique identifier"}, "user_id": {"type": "integer", "description": "ID of the user who placed the order"}, "total_amount": {"type": "number", "format": "float", "description": "Total order amount"}, "status": {"type": "string", "enum": ["pending", "processing", "completed", "cancelled"], "description": "Current status of the order"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItem"}}, "shipping_address": {"$ref": "#/components/schemas/Address"}, "billing_address": {"$ref": "#/components/schemas/Address"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "PosCredentials": {"type": "object", "required": ["webguid", "subscriptionKey"], "properties": {"webguid": {"type": "string"}, "subscriptionKey": {"type": "string"}}}, "PosConnectionTestRequest": {"type": "object", "required": ["posType", "credentials"], "properties": {"posType": {"type": "string"}, "credentials": {"$ref": "#/components/schemas/PosCredentials"}}}, "PosConnectionTestResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "properties": {"ticketCount": {"type": "integer"}, "productCount": {"type": "integer"}}}, "error": {"type": "string"}}}, "PosConnectResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "productCount": {"type": "integer"}, "transactionCount": {"type": "integer"}, "error": {"type": "string"}}}, "PosSaveNormalizedDataRequest": {"type": "object", "required": ["posSystem", "credentials"], "properties": {"posSystem": {"type": "string"}, "credentials": {"type": "object", "required": ["webguid", "hasSubscriptionKey"], "properties": {"webguid": {"type": "string"}, "hasSubscriptionKey": {"type": "boolean"}}}}}, "PosSaveNormalizedDataResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "dataRefId": {"type": "string"}, "error": {"type": "string"}}}, "PosAnalyticsRequest": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "Natural language query for POS data analysis"}}}, "PosAnalyticsResponse": {"type": "object", "properties": {"analysis": {"type": "string", "description": "AI-generated analysis of the POS data"}, "relevantData": {"type": "array", "items": {"type": "object", "properties": {"metadata": {"type": "object", "additionalProperties": true}}}}, "aggregatedData": {"type": "object", "properties": {"recordCount": {"type": "integer"}, "totalRevenue": {"type": "number"}, "averageRevenue": {"type": "number"}, "totalProfit": {"type": "number"}, "profitMargin": {"type": "number"}, "topProducts": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "sales": {"type": "integer"}, "revenue": {"type": "number"}, "profit": {"type": "number"}, "category": {"type": "string"}}}}, "timeframeSummary": {"type": "string"}}}, "query_parameters": {"type": "object", "properties": {"location_id": {"type": "integer"}, "timeframes": {"type": "object", "properties": {"year": {"type": "integer", "nullable": true}, "month": {"type": "string", "nullable": true}, "period": {"type": "string", "nullable": true}}}}}}}, "PosVectorSearchRequest": {"type": "object", "required": ["query"], "properties": {"query": {"type": "string", "description": "Search query for vector search"}, "limit": {"type": "integer", "description": "Maximum number of results to return", "default": 20}}}, "PosVectorSearchResponse": {"type": "object", "properties": {"query": {"type": "string"}, "results": {"type": "array", "items": {"type": "object", "properties": {"metadata": {"type": "object", "additionalProperties": true}}}}, "count": {"type": "integer"}, "location_id": {"type": "integer"}}}, "PosReVectorizeRequest": {"type": "object", "properties": {"batch_size": {"type": "integer", "description": "Number of records to process in each batch", "default": 100}}}, "PosReVectorizeResponse": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "string"}, "job_id": {"type": "string"}, "job_type": {"type": "string"}}}, "PosVectorizationStatusResponse": {"type": "object", "properties": {"status": {"type": "string", "enum": ["not_started", "processing", "completed", "failed"]}, "job_summary": {"type": "object", "properties": {"isProcessing": {"type": "boolean"}, "total": {"type": "integer"}, "completed": {"type": "integer"}, "failed": {"type": "integer"}, "pending": {"type": "integer"}, "processing": {"type": "integer"}, "jobs": {"type": "array", "items": {"type": "object"}}}}, "data_count": {"type": "integer"}}}, "PosData": {"type": "object", "properties": {"id": {"type": "integer"}, "location_id": {"type": "integer"}, "order_date": {"type": "string", "format": "date-time"}, "data": {"type": "object", "additionalProperties": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "PosImportResponse": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "string"}, "reindexed": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}}, "imported": {"type": "integer", "nullable": true}, "skipped": {"type": "integer", "nullable": true}}}, "PosDataListResponse": {"type": "object", "properties": {"results": {"type": "array", "items": {"$ref": "#/components/schemas/PosData"}}, "nextCursor": {"type": "integer", "nullable": true}, "prevCursor": {"type": "integer", "nullable": true}, "limit": {"type": "integer"}}}, "PosVectorizationResponse": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "string"}, "job_id": {"type": "string"}, "job_type": {"type": "string"}, "clean_start": {"type": "boolean"}}}, "PosVectorizationStatus": {"type": "object", "properties": {"status": {"type": "string"}, "job_status": {"type": "string"}, "job_summary": {"type": "object", "properties": {"isProcessing": {"type": "boolean"}, "total": {"type": "integer"}, "completed": {"type": "integer"}, "failed": {"type": "integer"}, "pending": {"type": "integer"}, "processing": {"type": "integer"}, "jobs": {"type": "array", "items": {"type": "object"}}}}, "namespace": {"type": "string"}, "vector_count": {"type": "integer"}, "db_record_count": {"type": "integer"}, "vector_status": {"type": "string"}, "is_fully_indexed": {"type": "boolean"}, "completion_percentage": {"type": "integer"}, "error": {"type": "string", "nullable": true}}}, "PosIndicesStatus": {"type": "object", "properties": {"location_id": {"type": "integer"}, "indices_status": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "db_count": {"type": "integer"}, "vector_count": {"type": "integer"}, "status": {"type": "string"}, "namespace": {"type": "string"}, "error": {"type": "string", "nullable": true}}}}, "needs_re_vectorization": {"type": "array", "items": {"type": "string"}}, "has_mismatched_counts": {"type": "boolean"}, "total_db_records": {"type": "integer"}, "total_vector_records": {"type": "integer"}}}, "Product": {"type": "object", "properties": {"id": {"type": "string"}, "product_id": {"type": "string"}, "product_name": {"type": "string"}, "description": {"type": "string"}, "category": {"type": "string"}, "price": {"type": "number"}, "latest_price": {"type": "number"}, "original_price": {"type": "number"}, "brand_name": {"type": "string"}, "thc_percentage": {"type": "string"}, "cbd_percentage": {"type": "string"}, "image_url": {"type": "string"}, "weight": {"type": "string"}, "display_weight": {"type": "string"}, "location_id": {"type": "integer"}, "retailer_id": {"type": "string"}}}, "ProductListResponse": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}, "pagination": {"type": "object", "properties": {"totalPages": {"type": "integer"}, "currentPage": {"type": "integer"}, "cursor": {"type": "string", "nullable": true}, "total": {"type": "integer"}, "per_page": {"type": "integer"}}}}}, "ProductUploadRequest": {"type": "object", "properties": {"product_data": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}, "reindex": {"type": "boolean", "description": "Whether to reindex the products after import"}, "enhance_with_ai": {"type": "boolean", "description": "Whether to enhance product data using AI"}}}, "ProductUploadResponse": {"type": "object", "properties": {"message": {"type": "string"}, "imported": {"type": "integer"}, "skipped": {"type": "integer"}, "errors": {"type": "array", "items": {"type": "string"}}}}, "ChatMessageRequest": {"type": "object", "required": ["message"], "properties": {"message": {"type": "string", "description": "The user's message"}, "chat_id": {"type": "string", "nullable": true, "description": "Optional chat ID for continuing a conversation"}}}, "ChatMessageResponse": {"type": "object", "properties": {"type": {"type": "string", "enum": ["ai"]}, "content": {"type": "string"}, "message_id": {"type": "string"}, "chat_id": {"type": "string"}, "data": {"type": "object", "properties": {"suggested_next_questions": {"type": "array", "items": {"type": "string"}}, "products": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}}}}, "AdminProfile": {"type": "object", "properties": {"id": {"type": "integer", "description": "Admin ID"}, "email": {"type": "string", "format": "email", "description": "Admin email address"}, "name": {"type": "string", "description": "<PERSON><PERSON>'s full name"}, "role": {"type": "string", "description": "<PERSON><PERSON>'s role"}, "created_at": {"type": "string", "format": "date-time", "description": "Account creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}, "metadata": {"type": "object", "description": "Additional admin metadata", "additionalProperties": true}}}, "Resource": {"type": "object", "required": ["id", "location_id", "type", "name", "value"], "properties": {"id": {"type": "integer", "description": "The unique identifier of the resource"}, "location_id": {"type": "integer", "description": "The ID of the location this resource belongs to"}, "type": {"type": "string", "enum": ["font", "snippet"], "description": "The type of resource"}, "name": {"type": "string", "description": "The name of the resource"}, "value": {"type": "object", "description": "The resource value object", "additionalProperties": true}}}, "TemplateEmailData": {"type": "object", "properties": {"from": {"type": "object", "nullable": true, "properties": {"name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}}}, "cc": {"type": "string", "nullable": true}, "bcc": {"type": "string", "nullable": true}, "reply_to": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "html": {"type": "string", "nullable": true}, "mjml": {"type": "string", "nullable": true}}, "nullable": true}, "TemplateTextData": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}}, "nullable": true}, "TemplatePushData": {"type": "object", "required": ["title", "body"], "properties": {"title": {"type": "string"}, "body": {"type": "string"}, "custom": {"type": "object", "nullable": true, "additionalProperties": true}}, "nullable": true}, "TemplateWebhookData": {"type": "object", "required": ["method", "endpoint"], "properties": {"method": {"type": "string"}, "endpoint": {"type": "string"}, "body": {"type": "object", "nullable": true, "additionalProperties": true}, "headers": {"type": "object", "nullable": true, "additionalProperties": true}}, "nullable": true}, "Template": {"type": "object", "required": ["id", "location_id", "type", "campaign_id", "locale"], "properties": {"id": {"type": "integer", "description": "The unique identifier of the template"}, "location_id": {"type": "integer", "description": "The ID of the location this template belongs to"}, "type": {"type": "string", "enum": ["email", "text", "push", "webhook"], "description": "The type of template"}, "campaign_id": {"type": "integer", "description": "The ID of the campaign this template belongs to"}, "locale": {"type": "string", "description": "The locale of the template"}, "data": {"oneOf": [{"$ref": "#/components/schemas/TemplateEmailData"}, {"$ref": "#/components/schemas/TemplateTextData"}, {"$ref": "#/components/schemas/TemplatePushData"}, {"$ref": "#/components/schemas/TemplateWebhookData"}]}}}, "SharedAssetUploadResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "filename": {"type": "string"}, "originalName": {"type": "string"}, "url": {"type": "string", "description": "Branded URL for sharing"}, "directUrl": {"type": "string", "description": "Direct storage URL"}, "type": {"type": "string"}, "businessName": {"type": "string"}, "userId": {"type": "integer", "nullable": true}, "imageId": {"type": "integer"}, "extension": {"type": "string"}}}, "SharedAssetErrorResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "error": {"type": "string"}}}, "Image": {"type": "object", "properties": {"id": {"type": "integer"}, "location_id": {"type": "integer"}, "name": {"type": "string"}, "alt": {"type": "string", "nullable": true}, "url": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "ImageListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Image"}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}, "pages": {"type": "integer"}}}}}, "ImageUpdateRequest": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "alt": {"type": "string", "nullable": true}}}, "Subscription": {"type": "object", "properties": {"id": {"type": "integer"}, "location_id": {"type": "integer"}, "name": {"type": "string"}, "channel": {"type": "string", "enum": ["email", "text", "push", "webhook"]}, "from_name": {"type": "string", "nullable": true}, "from_email": {"type": "string", "nullable": true}, "reply_to": {"type": "string", "nullable": true}, "cc": {"type": "string", "nullable": true}, "bcc": {"type": "string", "nullable": true}, "from_phone": {"type": "string", "nullable": true}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "SubscriptionCreateRequest": {"type": "object", "required": ["name", "channel"], "properties": {"name": {"type": "string"}, "channel": {"type": "string", "enum": ["email", "text", "push", "webhook"]}, "from_name": {"type": "string", "nullable": true}, "from_email": {"type": "string", "nullable": true}, "reply_to": {"type": "string", "nullable": true}, "cc": {"type": "string", "nullable": true}, "bcc": {"type": "string", "nullable": true}, "from_phone": {"type": "string", "nullable": true}}}, "SubscriptionUpdateRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "channel": {"type": "string", "enum": ["email", "text", "push", "webhook"], "nullable": true}, "from_name": {"type": "string", "nullable": true}, "from_email": {"type": "string", "nullable": true}, "reply_to": {"type": "string", "nullable": true}, "cc": {"type": "string", "nullable": true}, "bcc": {"type": "string", "nullable": true}, "from_phone": {"type": "string", "nullable": true}}}, "SubscriptionListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Subscription"}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}, "pages": {"type": "integer"}}}}}, "LocationCreationResponse": {"type": "object", "properties": {"canCreate": {"type": "boolean"}, "message": {"type": "string"}}}, "SubscriptionCancelResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "error": {"type": "string"}}}, "SupabaseUploadRequest": {"type": "object", "required": ["data_sources"], "properties": {"data_sources": {"type": "object", "properties": {"documents": {"type": "array", "items": {"type": "object", "properties": {"documentId": {"type": "string"}, "content": {"type": "string"}, "processed": {"type": "boolean"}, "ingestionTimestamp": {"type": "string", "format": "date-time"}, "documentIndex": {"type": "integer"}}}}}}}}, "SupabaseUploadResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "tableName": {"type": "string"}, "recordCount": {"type": "integer"}, "error": {"type": "string"}}}, "SupabaseStatusResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "status": {"type": "object", "additionalProperties": true}, "error": {"type": "string"}}}, "Tag": {"type": "object", "properties": {"id": {"type": "integer"}, "location_id": {"type": "integer"}, "name": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "TagListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "pagination": {"type": "object", "properties": {"total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}, "pages": {"type": "integer"}}}}}, "TagCreateRequest": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}}, "TagUpdateRequest": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}}, "CustomPrompt": {"type": "object", "properties": {"id": {"type": "integer", "description": "Prompt ID"}, "admin_id": {"type": "integer", "description": "ID of the admin who owns this prompt"}, "name": {"type": "string", "description": "Name of the prompt"}, "content": {"type": "string", "description": "Content of the prompt"}, "is_active": {"type": "boolean", "description": "Whether this prompt is currently active"}, "created_at": {"type": "string", "format": "date-time", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "description": "Last update timestamp"}}}, "CustomPromptPayload": {"type": "object", "required": ["name", "content", "is_active"], "properties": {"name": {"type": "string", "description": "Name of the prompt"}, "content": {"type": "string", "description": "Content of the prompt"}, "is_active": {"type": "boolean", "description": "Whether this prompt should be active"}}}, "CustomPromptListResponse": {"type": "array", "items": {"$ref": "#/components/schemas/CustomPrompt"}}}, "examples": {"DualAuthentication": {"summary": "Using both API key and user authentication", "description": "Example of headers when using both API key (required) and user authentication (optional)", "value": {"Authorization": "Bearer your_location_api_key_here", "x-user-token": "user_jwt_token_here"}}, "ApiKeyOnly": {"summary": "Using only API key (anonymous access)", "description": "Example of headers when using only API key for anonymous access", "value": {"Authorization": "Bearer your_location_api_key_here"}}}}, "security": [{"bearerAuth": []}], "paths": {"/agents/definitions": {"get": {"summary": "Get all agent definitions", "tags": ["Agents"], "responses": {"200": {"description": "List of agent definitions", "content": {"application/json": {"schema": {"type": "object", "properties": {"agents": {"type": "array", "items": {"$ref": "#/components/schemas/Agent"}}, "total": {"type": "integer"}}}}}}, "500": {"description": "Error retrieving agent definitions"}}}}, "/agents/available": {"get": {"summary": "Get available agents for a location", "tags": ["Agents"], "responses": {"200": {"description": "List of available agents", "content": {"application/json": {"schema": {"type": "object", "properties": {"agents": {"type": "array", "items": {"$ref": "#/components/schemas/Agent"}}, "total": {"type": "integer"}, "locationId": {"type": "integer"}}}}}}, "500": {"description": "Error retrieving available agents"}}}}, "/agents/availability": {"get": {"summary": "Get detailed agent availability report", "tags": ["Agents"], "responses": {"200": {"description": "Agent availability report", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentAvailabilityReport"}}}}, "500": {"description": "Error retrieving agent availability report"}}}}, "/agents/{id}": {"get": {"summary": "Get single agent details and availability", "tags": ["Agents"], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Agent ID"}], "responses": {"200": {"description": "Agent details and availability", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "description": {"type": "string"}, "icon": {"type": "string"}, "capabilities": {"type": "array", "items": {"type": "string"}}, "availability": {"$ref": "#/components/schemas/AgentAvailability"}}}}}}, "404": {"description": "Agent not found"}, "500": {"description": "Error retrieving agent details"}}}}, "/agents/{id}/requirements": {"get": {"summary": "Get data requirements for a specific agent", "tags": ["Agents"], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Agent ID"}], "responses": {"200": {"description": "Agent data requirements", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentRequirements"}}}}, "404": {"description": "Agent not found"}, "500": {"description": "Error retrieving agent requirements"}}}}, "/analysis/sales-trends": {"post": {"summary": "Analyze Sales Trends", "description": "Analyze sales trends for a location within a date range", "tags": ["DataAnalysis"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}}}}}}, "responses": {"200": {"description": "Sales trends analysis result", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}, "400": {"description": "startDate and endDate are required"}, "500": {"description": "Failed to analyze sales trends"}}}}, "/analysis/customer-behavior": {"post": {"summary": "Analyze Customer Behavior", "description": "Analyze customer behavior for a location within a date range", "tags": ["DataAnalysis"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}}}}}}, "responses": {"200": {"description": "Customer behavior analysis result", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}, "400": {"description": "startDate and endDate are required"}, "500": {"description": "Failed to analyze customer behavior"}}}}, "/analysis/product-performance": {"post": {"summary": "Analyze Product Performance", "description": "Analyze product performance for a location within a date range", "tags": ["DataAnalysis"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}}}}}}, "responses": {"200": {"description": "Product performance analysis result", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}, "400": {"description": "startDate and endDate are required"}, "500": {"description": "Failed to analyze product performance"}}}}, "/analysis/sales-trends-time-series": {"post": {"summary": "Analyze Sales Trends with Time Series", "description": "Analyze sales trends with time series analysis for a location within a date range", "tags": ["DataAnalysis"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date"}, "endDate": {"type": "string", "format": "date"}}}}}}, "responses": {"200": {"description": "Sales trends time series analysis result", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}, "400": {"description": "startDate and endDate are required"}, "500": {"description": "Failed to analyze sales trends with time series"}}}}, "/organizations/admins": {"get": {"summary": "Get paged list of organization admins", "tags": ["Organization Admins"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of organization admins", "content": {"application/json": {"schema": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Admin"}}, "total": {"type": "integer"}}}}}}}}, "post": {"summary": "Create a new organization admin", "tags": ["Organization Admins"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["role", "email"], "properties": {"email": {"type": "string", "format": "email"}, "role": {"type": "string", "enum": ["admin", "editor", "viewer"]}, "first_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "Created admin", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin"}}}}}}}, "/organizations/admins/{adminId}": {"get": {"summary": "Get a specific organization admin", "tags": ["Organization Admins"], "parameters": [{"in": "path", "name": "adminId", "required": true, "schema": {"type": "integer"}, "description": "Admin ID"}], "responses": {"200": {"description": "Admin details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin"}}}}, "404": {"description": "Admin not found"}}}, "patch": {"summary": "Update an organization admin", "tags": ["Organization Admins"], "parameters": [{"in": "path", "name": "adminId", "required": true, "schema": {"type": "integer"}, "description": "Admin ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["role", "email"], "properties": {"email": {"type": "string", "format": "email"}, "role": {"type": "string", "enum": ["admin", "editor", "viewer"]}, "first_name": {"type": "string", "nullable": true}, "last_name": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "Updated admin", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Admin"}}}}, "404": {"description": "Admin not found"}}}, "delete": {"summary": "Delete an organization admin", "tags": ["Organization Admins"], "parameters": [{"in": "path", "name": "adminId", "required": true, "schema": {"type": "integer"}, "description": "Admin ID"}], "responses": {"200": {"description": "Admin deleted successfully"}, "404": {"description": "Admin not found"}}}}, "/auth/methods": {"get": {"summary": "Get Available Auth Methods", "description": "Retrieves the list of available authentication methods", "tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "List of available authentication methods", "content": {"application/json": {"schema": {"type": "object", "properties": {"methods": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "/auth/check": {"post": {"summary": "Check Authentication Status", "description": "Verifies if the current session is authenticated", "tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Authentication status", "content": {"application/json": {"schema": {"type": "object", "properties": {"authenticated": {"type": "boolean"}, "user": {"type": "object", "nullable": true}}}}}}}}}, "/auth/login/{driver}": {"get": {"summary": "Initiate <PERSON><PERSON><PERSON>", "description": "Initiates the OAuth login flow for the specified provider", "tags": ["<PERSON><PERSON>"], "parameters": [{"in": "path", "name": "driver", "required": true, "schema": {"type": "string"}, "description": "OAuth provider (e.g., google, linkedin)"}], "responses": {"302": {"description": "Redirect to OAuth provider"}, "400": {"description": "Invalid provider"}}}, "post": {"summary": "Login with Provider", "description": "Handles login with the specified provider", "tags": ["<PERSON><PERSON>"], "parameters": [{"in": "path", "name": "driver", "required": true, "schema": {"type": "string"}, "description": "OAuth provider (e.g., google, linkedin)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string"}}}}}}, "responses": {"200": {"description": "Login successful"}, "400": {"description": "Invalid credentials"}}}}, "/auth/login/{driver}/callback": {"get": {"summary": "<PERSON><PERSON><PERSON>", "description": "Handles the OAuth callback from the provider", "tags": ["<PERSON><PERSON>"], "parameters": [{"in": "path", "name": "driver", "required": true, "schema": {"type": "string"}, "description": "OAuth provider (e.g., google, linkedin)"}, {"in": "query", "name": "code", "required": true, "schema": {"type": "string"}, "description": "Authorization code from provider"}], "responses": {"302": {"description": "Redirect to success/failure page"}}}, "post": {"summary": "<PERSON><PERSON>", "description": "Processes the OAuth callback data", "tags": ["<PERSON><PERSON>"], "parameters": [{"in": "path", "name": "driver", "required": true, "schema": {"type": "string"}, "description": "OAuth provider (e.g., google, linkedin)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string"}}}}}}, "responses": {"200": {"description": "Authentication successful"}, "400": {"description": "Invalid callback data"}}}}, "/auth/logout": {"post": {"summary": "Logout User", "description": "Logs out the current user", "tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Logout successful"}}}, "get": {"summary": "Logout User (GET)", "description": "Logs out the current user via GET request", "tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "Logout successful"}}}}, "/campaigns": {"get": {"summary": "List Campaigns", "description": "Retrieves a paginated list of campaigns for the current location", "tags": ["Campaign"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}, {"in": "query", "name": "sort", "schema": {"type": "string"}, "description": "Field to sort by"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"]}, "description": "Sort direction"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Campaign"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}}}}}}}}, "post": {"summary": "Create Campaign", "description": "Creates a new campaign in the current location", "tags": ["Campaign"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CampaignCreateParams"}}}}, "responses": {"200": {"description": "Campaign created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Campaign"}}}}, "400": {"description": "Invalid input"}}}}, "/campaigns/{campaignId}": {"get": {"summary": "Get Campaign by ID", "description": "Retrieves a specific campaign by its identifier", "tags": ["Campaign"], "parameters": [{"in": "path", "name": "campaignId", "required": true, "schema": {"type": "integer"}, "description": "Campaign identifier"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Campaign"}}}}, "404": {"description": "Campaign not found"}}}, "patch": {"summary": "Update Campaign", "description": "Updates an existing campaign", "tags": ["Campaign"], "parameters": [{"in": "path", "name": "campaignId", "required": true, "schema": {"type": "integer"}, "description": "Campaign identifier"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CampaignUpdateParams"}}}}, "responses": {"200": {"description": "Campaign updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Campaign"}}}}, "404": {"description": "Campaign not found"}}}, "delete": {"summary": "Delete Campaign", "description": "Deletes a campaign", "tags": ["Campaign"], "parameters": [{"in": "path", "name": "campaignId", "required": true, "schema": {"type": "integer"}, "description": "Campaign identifier"}], "responses": {"204": {"description": "Campaign deleted successfully"}, "404": {"description": "Campaign not found"}}}}, "/campaigns/{campaignId}/users": {"get": {"summary": "Get Campaign Users", "description": "Retrieves users associated with a campaign", "tags": ["Campaign"], "parameters": [{"in": "path", "name": "campaignId", "required": true, "schema": {"type": "integer"}, "description": "Campaign identifier"}], "responses": {"200": {"description": "Campaign users retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}, "404": {"description": "Campaign not found"}}}}, "/campaigns/{campaignId}/duplicate": {"post": {"summary": "Duplicate Campaign", "description": "Creates a copy of an existing campaign", "tags": ["Campaign"], "parameters": [{"in": "path", "name": "campaignId", "required": true, "schema": {"type": "integer"}, "description": "Campaign identifier"}], "responses": {"200": {"description": "Campaign duplicated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Campaign"}}}}, "404": {"description": "Campaign not found"}}}}, "/campaigns/{campaignId}/preview": {"get": {"summary": "Preview Campaign", "description": "Generates a preview of the campaign", "tags": ["Campaign"], "parameters": [{"in": "path", "name": "campaignId", "required": true, "schema": {"type": "integer"}, "description": "Campaign identifier"}], "responses": {"200": {"description": "Campaign preview generated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"preview": {"type": "string"}}}}}}, "404": {"description": "Campaign not found"}}}}, "/campaigns/{campaignId}/trigger": {"post": {"summary": "<PERSON>gger Campaign Send", "description": "Triggers the sending of a campaign", "tags": ["Campaign"], "parameters": [{"in": "path", "name": "campaignId", "required": true, "schema": {"type": "integer"}, "description": "Campaign identifier"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CampaignTriggerSchema"}}}}, "responses": {"200": {"description": "Campaign send triggered successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}}}}}}, "404": {"description": "Campaign not found"}}}}, "/client/alias": {"post": {"summary": "Create Use<PERSON>", "description": "Creates an alias for a user to track across different identifiers", "tags": ["Client"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"previous_id": {"type": "string"}, "user_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Alias created successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}}}}}}, "400": {"description": "Invalid input"}}}}, "/client/identify": {"post": {"summary": "Identify User", "description": "Identifies a user with their traits and properties", "tags": ["Client"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "string"}, "traits": {"type": "object"}}}}}}, "responses": {"200": {"description": "User identified successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}}}}}}, "400": {"description": "Invalid input"}}}}, "/client/devices": {"post": {"summary": "Register Device", "description": "Registers a new device for a user", "tags": ["Client"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "string"}, "device_id": {"type": "string"}, "platform": {"type": "string"}}}}}}, "responses": {"200": {"description": "Device registered successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}}}}}}, "400": {"description": "Invalid input"}}}}, "/client/events": {"post": {"summary": "Track Event", "description": "Tracks a user event with optional properties", "tags": ["Client"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user_id": {"type": "string"}, "event": {"type": "string"}, "properties": {"type": "object"}}}}}}, "responses": {"200": {"description": "Event tracked successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}}}}}}, "400": {"description": "Invalid input"}}}}, "/client/segment": {"post": {"summary": "Process Segment Events", "description": "Accepts a batch of Segment events (identify, alias, track) for processing. Events are queued for asynchronous processing.", "tags": ["Segment"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SegmentEventBatch"}, "example": [{"type": "identify", "userId": "user123", "traits": {"email": "<EMAIL>", "phone": "+1234567890"}, "context": {"timezone": "America/New_York", "locale": "en-US"}, "timestamp": "2024-03-20T10:00:00Z"}, {"type": "track", "userId": "user123", "event": "Product Viewed", "properties": {"productId": "prod_123", "price": 99.99}, "context": {"page": "/products/123"}, "timestamp": "2024-03-20T10:01:00Z"}, {"type": "alias", "userId": "user123", "previousId": "anon_456", "timestamp": "2024-03-20T10:02:00Z"}]}}}, "responses": {"204": {"description": "Events successfully queued for processing"}, "400": {"description": "Invalid request body", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "401": {"description": "Unauthorized - Location not found or invalid", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/coa/import": {"post": {"summary": "Import COA data (JSON or file upload)", "tags": ["COA"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"coa_data": {"type": "array", "items": {"type": "object", "additionalProperties": true}}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "COA data import started", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoaImportResponse"}}}}, "400": {"description": "Import failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoaErrorResponse"}}}}}}}, "/coa/product/{product_id}": {"get": {"summary": "Get COAs for a specific product", "tags": ["COA"], "parameters": [{"in": "path", "name": "product_id", "required": true, "schema": {"type": "integer"}, "description": "Product ID"}], "responses": {"200": {"description": "List of COAs for the product", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoaProductListResponse"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoaErrorResponse"}}}}, "500": {"description": "Failed to get COA data for product", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoaErrorResponse"}}}}}}}, "/coa/{id}": {"get": {"summary": "Get a COA by ID", "tags": ["COA"], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "COA ID"}], "responses": {"200": {"description": "COA details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoaData"}}}}, "400": {"description": "Invalid COA ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoaErrorResponse"}}}}, "404": {"description": "COA not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoaErrorResponse"}}}}, "500": {"description": "Failed to get COA", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoaErrorResponse"}}}}}}}, "/coa": {"get": {"summary": "List COAs (paginated)", "tags": ["COA"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}, {"in": "query", "name": "sort", "schema": {"type": "string"}, "description": "Sort field"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"]}, "description": "Sort direction"}], "responses": {"200": {"description": "Paginated list of COAs", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoaListResponse"}}}}, "500": {"description": "Failed to list COA data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CoaErrorResponse"}}}}}}}, "/competitors": {"get": {"summary": "List Competitors", "description": "Retrieves a paginated list of competitors for the current location", "tags": ["Competitor"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}, {"in": "query", "name": "sort", "schema": {"type": "string"}, "description": "Field to sort by"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"]}, "description": "Sort direction"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Competitor"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}}}}}}}}, "post": {"summary": "Create Competitor", "description": "Creates a new competitor for the current location", "tags": ["Competitor"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompetitorCreateParams"}}}}, "responses": {"200": {"description": "Competitor created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Competitor"}}}}, "400": {"description": "Invalid input"}}}}, "/competitors/{competitorId}": {"get": {"summary": "Get Competitor by ID", "description": "Retrieves a specific competitor by its identifier", "tags": ["Competitor"], "parameters": [{"in": "path", "name": "competitorId", "required": true, "schema": {"type": "integer"}, "description": "Competitor identifier"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Competitor"}}}}, "404": {"description": "Competitor not found"}}}, "patch": {"summary": "Update Competitor", "description": "Updates an existing competitor", "tags": ["Competitor"], "parameters": [{"in": "path", "name": "competitorId", "required": true, "schema": {"type": "integer"}, "description": "Competitor identifier"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompetitorUpdateParams"}}}}, "responses": {"200": {"description": "Competitor updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Competitor"}}}}, "404": {"description": "Competitor not found"}}}, "delete": {"summary": "Delete Competitor", "description": "Deletes a competitor", "tags": ["Competitor"], "parameters": [{"in": "path", "name": "competitorId", "required": true, "schema": {"type": "integer"}, "description": "Competitor identifier"}], "responses": {"204": {"description": "Competitor deleted successfully"}, "404": {"description": "Competitor not found"}}}}, "/competitors/price-comparison/{productId}": {"get": {"summary": "Get Product Price Comparison", "description": "Retrieves price comparison for a product across competitors.", "tags": ["Competitor"], "parameters": [{"in": "path", "name": "productId", "required": true, "schema": {"type": "string"}, "description": "Product ID"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object"}}}}, "500": {"description": "Failed to get price comparison"}}}}, "/competitors/market-analysis/{category}": {"get": {"summary": "Get Market Analysis", "description": "Retrieves market analysis for a category across competitors.", "tags": ["Competitor"], "parameters": [{"in": "path", "name": "category", "required": true, "schema": {"type": "string"}, "description": "Category name"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object"}}}}, "500": {"description": "Failed to get market analysis"}}}}, "/competitors/nearby-retailers": {"get": {"summary": "Search Nearby Retailers", "description": "Searches for nearby retailers to add as competitors.", "tags": ["Competitor"], "parameters": [{"in": "query", "name": "latitude", "required": true, "schema": {"type": "string"}, "description": "Latitude"}, {"in": "query", "name": "longitude", "required": true, "schema": {"type": "string"}, "description": "Longitude"}, {"in": "query", "name": "radius", "required": false, "schema": {"type": "string"}, "description": "Search radius in miles"}, {"in": "query", "name": "page", "required": false, "schema": {"type": "string"}, "description": "Page number"}, {"in": "query", "name": "pageSize", "required": false, "schema": {"type": "string"}, "description": "Page size"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object"}}}}, "400": {"description": "Invalid or missing parameters"}, "500": {"description": "Failed to search nearby retailers"}}}}, "/competitors/{competitorId}/analysis": {"get": {"summary": "Get Competitor Analysis", "description": "Retrieves analysis data for a specific competitor", "tags": ["Competitor"], "parameters": [{"in": "path", "name": "competitorId", "required": true, "schema": {"type": "integer"}, "description": "Competitor identifier"}], "responses": {"200": {"description": "Analysis data retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"analysis": {"type": "object", "additionalProperties": true}}}}}}, "404": {"description": "Competitor not found"}}}}, "/swagger.json": {"get": {"summary": "Get the OpenAPI/Swagger specification", "tags": ["API Documentation"], "description": "Returns the complete OpenAPI specification in JSON format", "responses": {"200": {"description": "OpenAPI specification", "content": {"application/json": {"schema": {"type": "object", "description": "The complete OpenAPI specification"}}}}}}}, "/api-docs": {"get": {"summary": "Get the Swagger UI interface", "tags": ["API Documentation"], "description": "Returns the Swagger UI interface for exploring the API", "responses": {"200": {"description": "Swagger UI HTML page", "content": {"text/html": {"schema": {"type": "string", "description": "The Swagger UI interface HTML"}}}}}}}, "/dashboard": {"get": {"summary": "Get dashboard data", "tags": ["Dashboard"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "timeframe", "schema": {"type": "string", "enum": ["7d", "30d", "90d", "1y", "all"], "default": "30d"}, "description": "Time period for dashboard data"}], "responses": {"200": {"description": "Dashboard data retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DashboardResponse"}}}}, "500": {"description": "Failed to fetch dashboard data"}}}}, "/dashboard/insights/generate": {"post": {"summary": "Generate insights", "tags": ["Dashboard"], "security": [{"bearerAuth": []}], "parameters": [{"in": "query", "name": "timeframe", "schema": {"type": "string", "enum": ["7d", "30d", "90d", "1y", "all"], "default": "30d"}, "description": "Time period for insights"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsightGenerateRequest"}}}}, "responses": {"200": {"description": "Insights generated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsightGenerateResponse"}}}}, "500": {"description": "Failed to generate insights"}}}}, "/dashboard/insights": {"get": {"summary": "Get all insights", "tags": ["Dashboard"], "responses": {"200": {"description": "List of insights retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsightGenerateResponse"}}}}}}}, "/dashboard/insights/{id}/automation-plan": {"post": {"summary": "Generate automation plan for an insight", "tags": ["Dashboard"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "Insight ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AutomationPlanRequest"}}}}, "responses": {"200": {"description": "Automation plan generated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AutomationPlanResponse"}}}}, "500": {"description": "Failed to generate automation plan"}}}}, "/dashboard/insights/{id}/regenerate-step": {"post": {"summary": "Regenerate a step in the automation plan", "tags": ["Dashboard"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "Insight ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegenerateStepRequest"}}}}, "responses": {"200": {"description": "Step regenerated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AutomationPlanResponse"}}}}, "400": {"description": "Invalid request parameters"}, "404": {"description": "Insight not found"}, "500": {"description": "Failed to regenerate step"}}}}, "/documents/upload": {"post": {"summary": "Upload Document", "description": "Uploads a document for processing and analysis", "tags": ["Document"], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "Document uploaded successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"document_id": {"type": "string"}, "status": {"type": "string"}}}}}}, "400": {"description": "Invalid file or upload failed"}}}}, "/documents/{document_id}/analysis": {"get": {"summary": "Get Document Analysis", "description": "Retrieves the analysis results for a specific document", "tags": ["Document"], "parameters": [{"in": "path", "name": "document_id", "required": true, "schema": {"type": "string"}, "description": "Document identifier"}], "responses": {"200": {"description": "Document analysis results", "content": {"application/json": {"schema": {"type": "object", "properties": {"analysis": {"type": "object"}}}}}}, "404": {"description": "Document not found"}}}}, "/documents": {"get": {"summary": "List Documents", "description": "Retrieves a list of documents with optional filtering", "tags": ["Document"], "parameters": [{"in": "query", "name": "status", "schema": {"type": "string"}, "description": "Filter by document status"}, {"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of documents", "content": {"application/json": {"schema": {"type": "object", "properties": {"documents": {"type": "array", "items": {"type": "object"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}}}}}}}}}, "/documents/{document_id}/download": {"get": {"summary": "Download Document", "description": "Downloads a specific document", "tags": ["Document"], "parameters": [{"in": "path", "name": "document_id", "required": true, "schema": {"type": "string"}, "description": "Document identifier"}], "responses": {"200": {"description": "Document file", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}, "404": {"description": "Document not found"}}}}, "/documents/{document_id}": {"delete": {"summary": "Delete Document", "description": "Deletes a specific document", "tags": ["Document"], "parameters": [{"in": "path", "name": "document_id", "required": true, "schema": {"type": "string"}, "description": "Document identifier"}], "responses": {"204": {"description": "Document deleted successfully"}, "404": {"description": "Document not found"}}}}, "/documents/{document_id}/reprocess": {"post": {"summary": "Reprocess Document", "description": "Triggers reprocessing of a document", "tags": ["Document"], "parameters": [{"in": "path", "name": "document_id", "required": true, "schema": {"type": "string"}, "description": "Document identifier"}], "responses": {"200": {"description": "Document reprocessing started", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}}}}}}, "404": {"description": "Document not found"}}}}, "/events": {"get": {"summary": "Get Events for Location", "description": "Get all events for a specific location with optional filtering", "tags": ["Events"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 20}, "description": "Items per page"}, {"in": "query", "name": "search", "schema": {"type": "string"}, "description": "Search query for event name"}, {"in": "query", "name": "future_only", "schema": {"type": "boolean", "default": true}, "description": "Only show future events"}], "responses": {"200": {"description": "List of events", "content": {"application/json": {"schema": {"type": "object", "properties": {"events": {"type": "array", "items": {"$ref": "#/components/schemas/Event"}}, "total_count": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}, "total_pages": {"type": "integer"}}}}}}}}, "post": {"summary": "Create Event", "description": "Create a new event for the specified location", "tags": ["Events"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEventRequest"}}}}, "responses": {"201": {"description": "Event created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Event"}}}}, "400": {"description": "Invalid request data"}}}}, "/events/{eventId}": {"get": {"summary": "Get Event Details", "description": "Get details of a specific event", "tags": ["Events"], "parameters": [{"in": "path", "name": "eventId", "required": true, "schema": {"type": "string"}, "description": "Event ID"}], "responses": {"200": {"description": "Event details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Event"}}}}, "404": {"description": "Event not found"}}}, "put": {"summary": "Update Event", "description": "Update an existing event (only events owned by the location)", "tags": ["Events"], "parameters": [{"in": "path", "name": "eventId", "required": true, "schema": {"type": "string"}, "description": "Event ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEventRequest"}}}}, "responses": {"200": {"description": "Event updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Event"}}}}, "404": {"description": "Event not found or not owned by location"}}}, "delete": {"summary": "Delete Event", "description": "Delete an event (only events owned by the location)", "tags": ["Events"], "parameters": [{"in": "path", "name": "eventId", "required": true, "schema": {"type": "string"}, "description": "Event ID"}], "responses": {"200": {"description": "Event deleted successfully"}, "404": {"description": "Event not found or not owned by location"}}}}, "/insights/market": {"get": {"summary": "Get Market Insights", "description": "Retrieve market insights for a given city and optional location parameters", "tags": ["Insights"], "parameters": [{"in": "query", "name": "locationName", "schema": {"type": "string"}, "description": "Name of the location (optional)"}, {"in": "query", "name": "city", "required": true, "schema": {"type": "string"}, "description": "City name"}, {"in": "query", "name": "state", "schema": {"type": "string"}, "description": "State abbreviation (optional)"}, {"in": "query", "name": "zip", "schema": {"type": "string"}, "description": "Zip code (optional)"}, {"in": "query", "name": "latitude", "schema": {"type": "number"}, "description": "Latitude (optional)"}, {"in": "query", "name": "longitude", "schema": {"type": "number"}, "description": "Longitude (optional)"}], "responses": {"200": {"description": "Market insights retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarketInsightsResponse"}, "example": {"market_size": 150000000, "growth_rate": 12.5, "competition_level": "medium", "demographics": {"age_distribution": {"18-24": 15, "25-34": 35, "35-44": 25, "45-54": 15, "55+": 10}, "income_levels": {"low": 20, "medium": 45, "high": 35}}, "regulations": [{"type": "licensing", "description": "Required state licensing", "impact": "high"}], "trends": [{"name": "Online ordering", "description": "Growing preference for online orders", "impact": "positive"}], "recommendations": [{"category": "Marketing", "suggestion": "Focus on digital presence", "priority": "high"}]}}}}, "400": {"description": "Bad Request - Missing city parameter", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}, "example": {"error": "Missing required parameter: city"}}}}, "500": {"description": "Failed to retrieve market insights", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}}, "example": {"error": "Failed to retrieve market insights", "details": "Error connecting to data provider"}}}}}}}, "/automations": {"get": {"summary": "Get paged journeys", "tags": ["Journeys"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of journeys", "content": {"application/json": {"schema": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Journey"}}, "total": {"type": "integer"}}}}}}}}, "post": {"summary": "Create a new journey", "tags": ["Journeys"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "published": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "Created journey", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Journey"}}}}}}}, "/automations/entrances/{entranceId}": {"get": {"summary": "Get journey entrance details", "tags": ["Journeys"], "parameters": [{"in": "path", "name": "entranceId", "required": true, "schema": {"type": "integer"}, "description": "Entrance ID"}], "responses": {"200": {"description": "Journey entrance details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JourneyEntrance"}}}}, "404": {"description": "Entrance not found"}}}}, "/automations/{journeyId}/steps": {"get": {"summary": "Get journey step map", "tags": ["Journeys"], "parameters": [{"in": "path", "name": "journeyId", "required": true, "schema": {"type": "integer"}, "description": "Journey ID"}], "responses": {"200": {"description": "Journey step map", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/JourneyStep"}}}}}}}, "put": {"summary": "Update journey step map", "tags": ["Journeys"], "parameters": [{"in": "path", "name": "journeyId", "required": true, "schema": {"type": "integer"}, "description": "Journey ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/JourneyStep"}}}}}, "responses": {"200": {"description": "Updated journey step map", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/JourneyStep"}}}}}}}}, "/automations/{journeyId}/entrances": {"get": {"summary": "Get paged entrances for a journey", "tags": ["Journeys"], "parameters": [{"in": "path", "name": "journeyId", "required": true, "schema": {"type": "integer"}, "description": "Journey ID"}, {"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of entrances", "content": {"application/json": {"schema": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/JourneyEntrance"}}, "total": {"type": "integer"}}}}}}}}}, "/automations/{journeyId}/entrances/{entranceId}/users/{userId}": {"delete": {"summary": "Exit user from journey entrance", "tags": ["Journeys"], "parameters": [{"in": "path", "name": "journeyId", "required": true, "schema": {"type": "integer"}, "description": "Journey ID"}, {"in": "path", "name": "entranceId", "required": true, "schema": {"type": "integer"}, "description": "Entrance ID"}, {"in": "path", "name": "userId", "required": true, "schema": {"type": "integer"}, "description": "User ID"}], "responses": {"200": {"description": "User exited successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"exits": {"type": "integer"}}}}}}, "404": {"description": "User not found"}}}}, "/automations/{journeyId}/steps/{stepId}/users": {"get": {"summary": "Get paged users in a journey step", "tags": ["Journeys"], "parameters": [{"in": "path", "name": "journeyId", "required": true, "schema": {"type": "integer"}, "description": "Journey ID"}, {"in": "path", "name": "stepId", "required": true, "schema": {"type": "integer"}, "description": "Step ID"}, {"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of users in step", "content": {"application/json": {"schema": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "total": {"type": "integer"}}}}}}, "404": {"description": "Step not found"}}}}, "/automations/{journeyId}/users/{userId}": {"delete": {"summary": "Exit user from journey", "tags": ["Journeys"], "parameters": [{"in": "path", "name": "journeyId", "required": true, "schema": {"type": "integer"}, "description": "Journey ID"}, {"in": "path", "name": "userId", "required": true, "schema": {"type": "integer"}, "description": "User ID"}], "responses": {"200": {"description": "User exited successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"exits": {"type": "integer"}}}}}}, "404": {"description": "User not found"}}}}, "/automations/{journeyId}/trigger": {"post": {"summary": "Manually trigger a journey entrance", "tags": ["Journeys"], "parameters": [{"in": "path", "name": "journeyId", "required": true, "schema": {"type": "integer"}, "description": "Journey ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JourneyTriggerRequest"}}}}, "responses": {"200": {"description": "Journey triggered successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}}}}}}}}}, "/automations/suggestions": {"get": {"summary": "Get AI-generated journey suggestions", "tags": ["Journeys"], "responses": {"200": {"description": "List of journey suggestions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/JourneySuggestion"}}}}}}}}, "/automations/create": {"post": {"summary": "Create a new journey with steps", "tags": ["Journeys"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "published": {"type": "boolean"}, "steps": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/JourneyStep"}}}}}}}, "responses": {"200": {"description": "Created journey", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Journey"}}}}}}}, "/lists": {"get": {"summary": "Get paged lists", "tags": ["Lists"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of lists", "content": {"application/json": {"schema": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/List"}}, "total": {"type": "integer"}}}}}}}}, "post": {"summary": "Create a new list", "tags": ["Lists"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"type": "object", "required": ["name", "type", "rule"], "properties": {"name": {"type": "string"}, "type": {"type": "string", "enum": ["dynamic"]}, "rule": {"$ref": "#/components/schemas/Rule"}, "tags": {"type": "array", "items": {"type": "string"}}, "is_visible": {"type": "boolean"}}}, {"type": "object", "required": ["name", "type"], "properties": {"name": {"type": "string"}, "type": {"type": "string", "enum": ["static"]}, "tags": {"type": "array", "items": {"type": "string"}}, "is_visible": {"type": "boolean"}, "user_ids": {"type": "array", "items": {"type": "integer"}}}}]}}}}, "responses": {"200": {"description": "Created list", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/List"}}}}}}}, "/lists/{listId}": {"get": {"summary": "Get a specific list", "tags": ["Lists"], "parameters": [{"in": "path", "name": "listId", "required": true, "schema": {"type": "integer"}, "description": "List ID"}], "responses": {"200": {"description": "List details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/List"}}}}, "404": {"description": "List not found"}}}, "patch": {"summary": "Update a list", "tags": ["Lists"], "parameters": [{"in": "path", "name": "listId", "required": true, "schema": {"type": "integer"}, "description": "List ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "rule": {"$ref": "#/components/schemas/Rule"}, "tags": {"type": "array", "items": {"type": "string"}}, "published": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "Updated list", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/List"}}}}, "404": {"description": "List not found"}}}, "delete": {"summary": "Delete or archive a list", "tags": ["Lists"], "parameters": [{"in": "path", "name": "listId", "required": true, "schema": {"type": "integer"}, "description": "List ID"}], "responses": {"200": {"description": "List deleted/archived successfully"}, "404": {"description": "List not found"}}}}, "/lists/{listId}/users": {"get": {"summary": "Get users in a list", "tags": ["Lists"], "parameters": [{"in": "path", "name": "listId", "required": true, "schema": {"type": "integer"}, "description": "List ID"}, {"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of users", "content": {"application/json": {"schema": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "total": {"type": "integer"}}}}}}, "404": {"description": "List not found"}}}, "post": {"summary": "Import users to a list", "tags": ["Lists"], "parameters": [{"in": "path", "name": "listId", "required": true, "schema": {"type": "integer"}, "description": "List ID"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"204": {"description": "Users imported successfully"}, "404": {"description": "List not found"}}}}, "/lists/suggestions": {"post": {"summary": "Generate list suggestions", "tags": ["Lists"], "responses": {"200": {"description": "List suggestions generated successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ListSuggestion"}}}}}, "500": {"description": "Error generating suggestions"}}}}, "/lists/{listId}/bulk-add-users": {"post": {"summary": "Bulk add users to a list", "tags": ["Lists"], "parameters": [{"in": "path", "name": "listId", "required": true, "schema": {"type": "integer"}, "description": "List ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["user_ids"], "properties": {"user_ids": {"type": "array", "items": {"type": "integer"}}}}}}}, "responses": {"204": {"description": "Users added successfully"}, "400": {"description": "Invalid request body"}, "404": {"description": "List not found"}}}}, "/locations/{locationId}/admins": {"get": {"summary": "List Location Admins", "description": "Retrieve a paginated list of administrators for a location", "tags": ["Location Admins"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Page number for pagination"}, {"in": "query", "name": "per_page", "schema": {"type": "integer", "default": 20}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of admins retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAdminListResponse"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}}}, "post": {"summary": "Add Location Admin", "description": "Add a new administrator to a location", "tags": ["Location Admins"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAdminCreateParams"}}}}, "responses": {"200": {"description": "Admin added successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAdmin"}}}}, "400": {"description": "Invalid request body or cannot add self as admin"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}}}}, "/locations/{locationId}/admins/{adminId}": {"get": {"summary": "Get Location Admin", "description": "Retrieve details of a specific location administrator", "tags": ["Location Admins"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}, {"in": "path", "name": "adminId", "required": true, "schema": {"type": "integer"}, "description": "ID of the admin"}], "responses": {"200": {"description": "Admin details retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAdmin"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "404": {"description": "Admin not found"}}}, "put": {"summary": "Update Location Admin", "description": "Update the role of a location administrator", "tags": ["Location Admins"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}, {"in": "path", "name": "adminId", "required": true, "schema": {"type": "integer"}, "description": "ID of the admin"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAdminParams"}}}}, "responses": {"200": {"description": "Admin role updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationAdmin"}}}}, "400": {"description": "Invalid request body or cannot update self"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "404": {"description": "Admin not found"}}}, "delete": {"summary": "Remove Location Admin", "description": "Remove an administrator from a location", "tags": ["Location Admins"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}, {"in": "path", "name": "adminId", "required": true, "schema": {"type": "integer"}, "description": "ID of the admin"}], "responses": {"200": {"description": "Admin removed successfully", "content": {"application/json": {"schema": {"type": "boolean", "example": true}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "404": {"description": "Admin not found"}}}}, "/locations/{locationId}/keys": {"get": {"summary": "List Location API Keys", "description": "Retrieve a paginated list of API keys for a location", "tags": ["Location API Keys"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Page number for pagination"}, {"in": "query", "name": "per_page", "schema": {"type": "integer", "default": 20}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of API keys retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationApiKeyListResponse"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}}}, "post": {"summary": "Create Location API Key", "description": "Create a new API key for a location", "tags": ["Location API Keys"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationApiKeyParams"}}}}, "responses": {"200": {"description": "API key created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationApiKey"}}}}, "400": {"description": "Invalid request body"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}}}}, "/locations/{locationId}/keys/{keyId}": {"get": {"summary": "Get Location API Key", "description": "Retrieve details of a specific API key", "tags": ["Location API Keys"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}, {"in": "path", "name": "keyId", "required": true, "schema": {"type": "integer"}, "description": "ID of the API key"}], "responses": {"200": {"description": "API key details retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationApiKey"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "404": {"description": "API key not found"}}}, "patch": {"summary": "Update Location API Key", "description": "Update an existing API key", "tags": ["Location API Keys"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}, {"in": "path", "name": "keyId", "required": true, "schema": {"type": "integer"}, "description": "ID of the API key"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationApiKeyParams"}}}}, "responses": {"200": {"description": "API key updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationApiKey"}}}}, "400": {"description": "Invalid request body"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "404": {"description": "API key not found"}}}, "delete": {"summary": "Revoke Location API Key", "description": "Revoke (delete) an API key", "tags": ["Location API Keys"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}, {"in": "path", "name": "keyId", "required": true, "schema": {"type": "integer"}, "description": "ID of the API key"}], "responses": {"200": {"description": "API key revoked successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationApiKey"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "404": {"description": "API key not found"}}}}, "/locations": {"get": {"summary": "List Locations", "description": "Retrieves a paginated list of locations for the current admin", "tags": ["Location"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}, {"in": "query", "name": "sort", "schema": {"type": "string"}, "description": "Field to sort by"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"]}, "description": "Sort direction"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Location"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}}}}}}}}, "post": {"summary": "Create Location", "description": "Creates a new location in the current organization", "tags": ["Location"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationParams"}}}}, "responses": {"200": {"description": "Location created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Location"}}}}, "400": {"description": "Invalid input"}, "403": {"description": "Subscription limit reached or admin not found"}}}}, "/locations/all": {"get": {"summary": "List All Locations", "description": "Retrieves all locations for the current admin without pagination", "tags": ["Location"], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Location"}}}}}}}}, "/locations/{locationId}/import/products": {"post": {"summary": "Import products for a location", "description": "Imports products from various sources for a location", "tags": ["Location"], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["source"], "properties": {"source": {"type": "string", "enum": ["pos", "marketplace", "file"]}, "file": {"type": "string", "format": "binary", "description": "Required if source is 'file'"}}}}}}, "responses": {"200": {"description": "Products imported successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "count": {"type": "integer"}}}}}}, "400": {"description": "Invalid request body"}, "404": {"description": "Location not found"}}}}, "/locations/{locationId}/import/pos": {"post": {"summary": "Import POS data for a location", "description": "Imports point of sale data for a location", "tags": ["Location"], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "POS data file"}}}}}}, "responses": {"200": {"description": "POS data imported successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "count": {"type": "integer"}}}}}}, "400": {"description": "Invalid request body"}, "404": {"description": "Location not found"}}}}, "/locations/{locationId}/competitors": {"post": {"summary": "Add competitors to a location", "description": "Adds competitor information to a location", "tags": ["Location"], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["competitors"], "properties": {"competitors": {"type": "array", "items": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}, "website": {"type": "string"}, "description": {"type": "string"}}}}}}}}}, "responses": {"200": {"description": "Competitors added successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "count": {"type": "integer"}}}}}}, "400": {"description": "Invalid request body"}, "404": {"description": "Location not found"}}}}, "/locations/{locationId}/documents": {"post": {"summary": "Upload documents for a location", "description": "Uploads and processes documents for a location", "tags": ["Location"], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}, "description": "Document files to upload"}}}}}}, "responses": {"200": {"description": "Documents uploaded successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "count": {"type": "integer"}}}}}}, "400": {"description": "Invalid request body"}, "404": {"description": "Location not found"}}}}, "/locations/{locationId}/locales": {"get": {"summary": "List Location Locales", "description": "Retrieve a paginated list of locales for a location", "tags": ["Location Locales"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}, {"in": "query", "name": "page", "schema": {"type": "integer", "default": 1}, "description": "Page number for pagination"}, {"in": "query", "name": "per_page", "schema": {"type": "integer", "default": 20}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of locales retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocaleListResponse"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}}}, "post": {"summary": "Create Location Locale", "description": "Create a new locale for a location", "tags": ["Location Locales"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocaleParams"}, "example": {"key": "en-US", "label": "English (United States)"}}}}, "responses": {"200": {"description": "Locale created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Locale"}}}}, "400": {"description": "Invalid request body"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}}}}, "/locations/{locationId}/locales/{keyId}": {"delete": {"summary": "Delete Location Locale", "description": "Delete a locale from a location", "tags": ["Location Locales"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "locationId", "required": true, "schema": {"type": "integer"}, "description": "ID of the location"}, {"in": "path", "name": "keyId", "required": true, "schema": {"type": "integer"}, "description": "ID of the locale to delete"}], "responses": {"200": {"description": "Locale deleted successfully", "content": {"application/json": {"schema": {"type": "boolean", "example": true}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "404": {"description": "Locale not found"}}}}, "/misc/retailers/search": {"get": {"summary": "Search Retailers", "description": "Search for retailers based on query parameters", "tags": ["Misc"], "parameters": [{"in": "query", "name": "query", "schema": {"type": "string"}, "description": "Search query string"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Maximum number of results to return"}], "responses": {"200": {"description": "List of matching retailers", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "address": {"type": "string"}}}}}}}}}}, "/misc/retailers/nearby": {"get": {"summary": "Find Nearby Retailers", "description": "Find retailers near a specific location", "tags": ["Misc"], "parameters": [{"in": "query", "name": "lat", "required": true, "schema": {"type": "number"}, "description": "Latitude coordinate"}, {"in": "query", "name": "lng", "required": true, "schema": {"type": "number"}, "description": "Longitude coordinate"}, {"in": "query", "name": "radius", "schema": {"type": "number"}, "description": "Search radius in kilometers"}], "responses": {"200": {"description": "List of nearby retailers", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "distance": {"type": "number"}}}}}}}}}}, "/misc/retailers/{retailerId}": {"get": {"summary": "Get Retailer Details", "description": "Get detailed information about a specific retailer", "tags": ["Misc"], "parameters": [{"in": "path", "name": "retailerId", "required": true, "schema": {"type": "string"}, "description": "Retailer identifier"}], "responses": {"200": {"description": "Retailer details", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "address": {"type": "string"}, "contact": {"type": "object"}}}}}}, "404": {"description": "Retailer not found"}}}}, "/misc/retailers/{retailerId}/products": {"get": {"summary": "Get Retailer Products", "description": "Get list of products available at a specific retailer", "tags": ["Misc"], "parameters": [{"in": "path", "name": "retailerId", "required": true, "schema": {"type": "string"}, "description": "Retailer identifier"}, {"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of products", "content": {"application/json": {"schema": {"type": "object", "properties": {"products": {"type": "array", "items": {"type": "object"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}}}}}}}}}, "/misc/subscriptions/can-create-location/{organizationId}": {"get": {"summary": "Check Location Creation Permission", "description": "Check if an organization can create a new location based on their subscription", "tags": ["Misc"], "parameters": [{"in": "path", "name": "organizationId", "required": true, "schema": {"type": "string"}, "description": "Organization identifier"}], "responses": {"200": {"description": "Permission check result", "content": {"application/json": {"schema": {"type": "object", "properties": {"can_create": {"type": "boolean"}, "reason": {"type": "string"}}}}}}}}}, "/misc/invite-codes/validate": {"post": {"summary": "Validate Invite Code", "description": "Validate an invitation code for organization access", "tags": ["Misc"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string"}}}}}}, "responses": {"200": {"description": "Validation result", "content": {"application/json": {"schema": {"type": "object", "properties": {"valid": {"type": "boolean"}, "organization": {"type": "object"}}}}}}, "400": {"description": "Invalid code"}}}}, "/misc/webhook/stripe": {"post": {"summary": "Stripe Webhook Handler", "description": "Handle incoming webhooks from Stripe payment system", "tags": ["Misc"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Webhook processed successfully"}, "400": {"description": "Invalid webhook data"}}}}, "/misc/subscription/upgrade-with-addons": {"post": {"summary": "Upgrade Subscription with Addons", "description": "Upgrade an organization's subscription with additional addons", "tags": ["Misc"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"organization_id": {"type": "string"}, "addons": {"type": "array", "items": {"type": "string"}}}}}}}, "responses": {"200": {"description": "Subscription upgraded successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"subscription_id": {"type": "string"}}}}}}, "400": {"description": "Invalid request"}}}}, "/misc/oauth/linkedin": {"post": {"summary": "LinkedIn OAuth", "description": "Handle LinkedIn OAuth authentication", "tags": ["Misc"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string"}}}}}}, "responses": {"200": {"description": "OAuth successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"access_token": {"type": "string"}}}}}}, "400": {"description": "Invalid OAuth request"}}}}, "/misc/oauth/linkedin/userinfo": {"post": {"summary": "Get LinkedIn User Info", "description": "Retrieve user information from LinkedIn OAuth", "tags": ["Misc"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"access_token": {"type": "string"}}}}}}, "responses": {"200": {"description": "User information retrieved", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}}}}}}, "400": {"description": "Invalid token"}}}}, "/misc/geocode": {"post": {"summary": "Geocode Address", "description": "Convert address to geographic coordinates", "tags": ["Misc"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"address": {"type": "string"}}}}}}, "responses": {"200": {"description": "Geocoding result", "content": {"application/json": {"schema": {"type": "object", "properties": {"lat": {"type": "number"}, "lng": {"type": "number"}}}}}}, "400": {"description": "Invalid address"}}}}, "/misc/places/search": {"post": {"summary": "Search Places", "description": "Search for places using Google Places API", "tags": ["Misc"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string"}}}}}}, "responses": {"200": {"description": "List of matching places", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"place_id": {"type": "string"}, "name": {"type": "string"}, "address": {"type": "string"}}}}}}}}}}, "/misc/places/search-nearby": {"post": {"summary": "Search Nearby Places", "description": "Search for places near a specific location", "tags": ["Misc"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"lat": {"type": "number"}, "lng": {"type": "number"}, "radius": {"type": "number"}}}}}}, "responses": {"200": {"description": "List of nearby places", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"place_id": {"type": "string"}, "name": {"type": "string"}, "distance": {"type": "number"}}}}}}}}}}, "/misc/complete-onboarding": {"post": {"summary": "Complete Onboarding", "description": "Mark organization onboarding as complete", "tags": ["Misc"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"organization_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Onboarding completed", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}}}}}}}}}, "/misc/onboarding/market-analysis": {"post": {"summary": "Generate Market Analysis", "description": "Generate market analysis during onboarding", "tags": ["Misc"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"organization_id": {"type": "string"}, "location_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "Analysis started", "content": {"application/json": {"schema": {"type": "object", "properties": {"job_id": {"type": "string"}}}}}}}}}, "/misc/subscriptions/{organizationId}": {"get": {"summary": "Get Organization Subscription", "description": "Get subscription details for an organization", "tags": ["Misc"], "parameters": [{"in": "path", "name": "organizationId", "required": true, "schema": {"type": "string"}, "description": "Organization identifier"}], "responses": {"200": {"description": "Subscription details", "content": {"application/json": {"schema": {"type": "object", "properties": {"plan": {"type": "string"}, "status": {"type": "string"}, "addons": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "/misc/events/search": {"post": {"summary": "Search Events", "description": "Search for events by name, city, and/or state. If city/state provided without query, shows local events first (minimum 5 events), supplementing with events from other locations if needed. If query provided, searches across all fields.", "tags": ["Misc"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string", "description": "Search query for event name, city, or state"}, "city": {"type": "string", "description": "User's current city for local event filtering"}, "state": {"type": "string", "description": "User's current state for local event filtering"}, "limit": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20, "description": "Number of results to return per page"}, "page": {"type": "integer", "minimum": 1, "default": 1, "description": "Page number for pagination"}}}}}}, "responses": {"200": {"description": "List of matching events", "content": {"application/json": {"schema": {"type": "object", "properties": {"events": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "event_id": {"type": "string"}, "event_name": {"type": "string"}, "category": {"type": "array", "items": {"type": "string"}}, "start_time": {"type": "string"}, "timezone": {"type": "string"}, "host": {"type": "string"}, "starting_price": {"type": "string"}, "address": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "postal_code": {"type": "string"}, "image": {"type": "string"}, "url": {"type": "string"}, "source": {"type": "string"}}}}, "total_count": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}, "total_pages": {"type": "integer"}, "search_context": {"type": "string", "description": "Indicates whether results are \"local\", \"local_with_supplements\", \"all_upcoming\", or \"query_search\""}}}}}}, "400": {"description": "Invalid request parameters"}, "500": {"description": "Server error"}}}}, "/orders/checkout": {"post": {"summary": "Create Order (Checkout)", "description": "Create a new order with the specified items. Requires location API key for access.\n\n**Authentication Options:**\n- **API Key Only**: Provide API key in Authorization header. User will be created from email/phone in request.\n- **API Key + User Token**: Provide both headers to link order to authenticated user account.\n\n**Headers Required:**\n- `Authorization: Bearer your_location_api_key` (Required - for location identification)\n- `x-user-token: user_jwt_token` (Optional - for user account linking)\n", "tags": ["Order"], "security": [{"ApiKeyAuth": []}, {"UserAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["items", "user"], "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItem"}, "minItems": 1, "description": "Items to order"}, "user": {"$ref": "#/components/schemas/UserInfo"}, "shipping_address": {"$ref": "#/components/schemas/Address"}, "billing_address": {"$ref": "#/components/schemas/Address"}}}}}}, "responses": {"200": {"description": "Order created successfully", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/Order"}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/OrderItem"}}}}]}}}}, "400": {"description": "Invalid request data"}, "401": {"description": "API key is required or invalid"}, "404": {"description": "Product not found"}, "500": {"description": "Internal server error"}}}}, "/orders": {"get": {"summary": "Get all orders with pagination", "description": "Retrieves a paginated list of orders for the location", "tags": ["Order"], "security": [{"ApiKeyAuth": []}], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 1, "default": 1}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}, "description": "Number of items per page"}, {"in": "query", "name": "status", "schema": {"type": "string", "enum": ["pending", "processing", "completed", "cancelled"]}, "description": "Filter orders by status"}], "responses": {"200": {"description": "List of orders", "content": {"application/json": {"schema": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/components/schemas/Order"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "totalPages": {"type": "integer"}}}}}}}}}, "/orders/{id}": {"get": {"summary": "Get order by ID", "description": "Retrieves detailed information about a specific order", "tags": ["Order"], "security": [{"ApiKeyAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "Order ID"}], "responses": {"200": {"description": "Order details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Order"}}}}}}}, "/orders/{id}/status": {"put": {"summary": "Update order status", "description": "Updates the status of a specific order", "tags": ["Order"], "security": [{"ApiKeyAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "Order ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string", "enum": ["pending", "processing", "completed", "cancelled"]}}}}}}, "responses": {"200": {"description": "Order status updated successfully"}}}}, "/orders/{id}/cancel": {"post": {"summary": "Cancel an order", "description": "Cancels a specific order if it's not already completed", "tags": ["Order"], "security": [{"ApiKeyAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "Order ID"}], "responses": {"200": {"description": "Order cancelled successfully"}}}}, "/orders/user/{userId}": {"get": {"summary": "Get user order history", "description": "Retrieves order history for a specific user with pagination", "tags": ["Order"], "security": [{"ApiKeyAuth": []}], "parameters": [{"in": "path", "name": "userId", "required": true, "schema": {"type": "integer"}, "description": "User ID"}, {"in": "query", "name": "page", "schema": {"type": "integer", "minimum": 1, "default": 1}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}, "description": "Number of items per page"}], "responses": {"200": {"description": "User's order history"}}}}, "/organizations": {"get": {"summary": "List Organizations", "description": "Get a list of organizations with optional filtering", "tags": ["Organization"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of organizations", "content": {"application/json": {"schema": {"type": "object", "properties": {"organizations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "status": {"type": "string"}}}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}}}}}}}}, "delete": {"summary": "Delete Organization", "description": "Delete an organization and all its associated data", "tags": ["Organization"], "responses": {"204": {"description": "Organization deleted successfully"}, "400": {"description": "Cannot delete organization with active subscriptions"}}}}, "/organizations/invite-codes": {"get": {"summary": "List Invite Codes", "description": "Get a list of active invite codes for the organization", "tags": ["Organization"], "responses": {"200": {"description": "List of invite codes", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "expires_at": {"type": "string", "format": "date-time"}}}}}}}}}, "post": {"summary": "Create Invite Code", "description": "Generate a new invite code for the organization", "tags": ["Organization"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"expires_at": {"type": "string", "format": "date-time"}}}}}}, "responses": {"200": {"description": "Invite code created", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "string"}, "expires_at": {"type": "string", "format": "date-time"}}}}}}}}}, "/organizations/invite-codes/{id}": {"delete": {"summary": "Delete Invite Code", "description": "Delete an existing invite code", "tags": ["Organization"], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Invite code identifier"}], "responses": {"204": {"description": "Invite code deleted successfully"}, "404": {"description": "Invite code not found"}}}}, "/organizations/performance/queue": {"get": {"summary": "Get Performance Queue", "description": "Get the current performance queue status", "tags": ["Organization"], "responses": {"200": {"description": "Queue status", "content": {"application/json": {"schema": {"type": "object", "properties": {"active": {"type": "integer"}, "waiting": {"type": "integer"}, "completed": {"type": "integer"}}}}}}}}}, "/organizations/performance/jobs": {"get": {"summary": "List Performance Jobs", "description": "Get a list of performance-related jobs", "tags": ["Organization"], "responses": {"200": {"description": "List of jobs", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}}}}}}}}}}, "/organizations/performance/jobs/{job}": {"get": {"summary": "Get Job Details", "description": "Get detailed information about a specific job", "tags": ["Organization"], "parameters": [{"in": "path", "name": "job", "required": true, "schema": {"type": "string"}, "description": "Job identifier"}], "responses": {"200": {"description": "Job details", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "status": {"type": "string"}, "progress": {"type": "number"}, "result": {"type": "object"}}}}}}, "404": {"description": "Job not found"}}}}, "/organizations/performance/failed": {"get": {"summary": "List Failed Jobs", "description": "Get a list of failed performance jobs", "tags": ["Organization"], "responses": {"200": {"description": "List of failed jobs", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "error": {"type": "string"}, "failed_at": {"type": "string", "format": "date-time"}}}}}}}}}}, "/organizations/integrations": {"get": {"summary": "List Integrations", "description": "Get a list of active integrations for the organization", "tags": ["Organization"], "responses": {"200": {"description": "List of integrations", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "status": {"type": "string"}, "config": {"type": "object"}}}}}}}}}}, "/organizations/{id}": {"patch": {"summary": "Update Organization", "description": "Update organization details", "tags": ["Organization"], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Organization identifier"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"name": {"type": "string"}, "settings": {"type": "object"}}}}}}, "responses": {"200": {"description": "Organization updated", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "updated_at": {"type": "string", "format": "date-time"}}}}}}, "404": {"description": "Organization not found"}}}}, "/organizations/verify-sender": {"post": {"summary": "<PERSON><PERSON><PERSON>", "description": "Verify an email sender for the organization", "tags": ["Organization"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"email": {"type": "string", "format": "email"}}}}}}, "responses": {"200": {"description": "Verification email sent", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}, "verification_id": {"type": "string"}}}}}}}}}, "/pos/test-connection": {"post": {"summary": "Test connection to a POS system", "tags": ["POS Integration"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosConnectionTestRequest"}}}}, "responses": {"200": {"description": "Connection test result", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosConnectionTestResponse"}}}}}}}, "/pos/connect": {"post": {"summary": "Connect to a POS system and retrieve data", "tags": ["POS Integration"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosConnectionTestRequest"}}}}, "responses": {"200": {"description": "Connection and data retrieval result", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosConnectResponse"}}}}}}}, "/pos/save-normalized-data": {"post": {"summary": "Save normalized POS data", "tags": ["POS Integration"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosSaveNormalizedDataRequest"}}}}, "responses": {"200": {"description": "Data saving result", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosSaveNormalizedDataResponse"}}}}}}}, "/pos/analytics/analyze": {"post": {"summary": "Analyze POS data using natural language query", "tags": ["POS Analytics"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosAnalyticsRequest"}}}}, "responses": {"200": {"description": "Analysis results", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosAnalyticsResponse"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Analysis failed", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}}}}}}}}, "/pos/analytics/vector-search": {"post": {"summary": "Search POS data using vector similarity", "tags": ["POS Analytics"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosVectorSearchRequest"}}}}, "responses": {"200": {"description": "Search results", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosVectorSearchResponse"}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Search failed", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}}}}}}}}, "/pos/analytics/re-vectorize": {"post": {"summary": "Start re-vectorization of POS data", "tags": ["POS Analytics"], "requestBody": {"required": false, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosReVectorizeRequest"}}}}, "responses": {"200": {"description": "Re-vectorization started", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosReVectorizeResponse"}}}}, "500": {"description": "Failed to start re-vectorization", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}}}}}}}}, "/pos/analytics/vectorization-status": {"get": {"summary": "Get vectorization status for POS data", "tags": ["POS Analytics"], "responses": {"200": {"description": "Vectorization status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosVectorizationStatusResponse"}}}}, "500": {"description": "Failed to get vectorization status", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}}}}}}}}, "/pos/import": {"post": {"summary": "Import POS data (JSON, file upload, or POS provider integration)", "tags": ["POS"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"pos_data": {"type": "array", "items": {"type": "object", "additionalProperties": true}}, "pos_provider": {"type": "string"}, "reindex": {"type": "boolean"}}}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "reindex": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "POS data import started", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosImportResponse"}}}}, "400": {"description": "Import failed", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/pos/data": {"get": {"summary": "Get POS data records (paginated)", "tags": ["POS"], "parameters": [{"in": "query", "name": "sort", "schema": {"type": "string"}, "description": "Sort field"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"]}, "description": "Sort direction"}, {"in": "query", "name": "cursor", "schema": {"type": "integer"}, "description": "Cursor for pagination"}, {"in": "query", "name": "page", "schema": {"type": "string", "enum": ["prev", "next"]}, "description": "Page direction"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "Paginated list of POS data records", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosDataListResponse"}}}}, "500": {"description": "Failed to retrieve POS data", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/pos/re-vectorize": {"post": {"summary": "Start vectorization job for POS data", "tags": ["POS"], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"batch_size": {"type": "integer", "default": 100}, "clean_start": {"type": "boolean", "default": true}}}}}}, "responses": {"200": {"description": "Vectorization job started", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosVectorizationResponse"}}}}, "500": {"description": "Failed to start vectorization", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}}}}}}}}, "/pos/vectorization-status": {"get": {"summary": "Get vectorization status for POS data", "tags": ["POS"], "responses": {"200": {"description": "Vectorization status details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosVectorizationStatus"}}}}, "500": {"description": "Failed to get vectorization status", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}}}}}}}}, "/pos/check-indices-status": {"get": {"summary": "Check status of all vector indices (admin only)", "tags": ["POS"], "responses": {"200": {"description": "Status of all vector indices", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PosIndicesStatus"}}}}, "500": {"description": "Failed to check indices status", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}, "details": {"type": "string"}}}}}}}}}, "/products": {"get": {"summary": "List Products", "description": "Retrieves a paginated list of products for the current location with search and filter capabilities", "tags": ["Product"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}, {"in": "query", "name": "sort", "schema": {"type": "string"}, "description": "Field to sort by"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"]}, "description": "Sort direction"}, {"in": "query", "name": "q", "schema": {"type": "string"}, "description": "Search query for product name, brand, category, etc."}, {"in": "query", "name": "filter", "schema": {"type": "object"}, "description": "Filter object for specific field filtering"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}}}}}}}}}, "/products/upload": {"post": {"summary": "Upload Products", "description": "Uploads products from a file or JSON data", "tags": ["Product"], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "CSV or JSON file containing product data"}, "reindex": {"type": "boolean", "description": "Whether to reindex the products after import"}, "enhance_with_ai": {"type": "boolean", "description": "Whether to enhance products with AI"}}}}, "application/json": {"schema": {"type": "object", "properties": {"product_data": {"type": "array", "items": {"$ref": "#/components/schemas/ProductParams"}}, "reindex": {"type": "boolean"}, "enhance_with_ai": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "Products uploaded successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "imported": {"type": "integer"}, "errors": {"type": "array", "items": {"type": "string"}}}}}}}, "400": {"description": "Invalid input"}}}}, "/products/enhance": {"post": {"summary": "Enhance Products with AI", "description": "Enhances product data using AI and optionally reindexes", "tags": ["Product"], "parameters": [{"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of products to process"}, {"in": "query", "name": "skip", "schema": {"type": "integer"}, "description": "Number of products to skip"}, {"in": "query", "name": "meta_sku", "schema": {"type": "string"}, "description": "Specific product SKU to enhance"}, {"in": "query", "name": "async", "schema": {"type": "boolean"}, "description": "Whether to run enhancement asynchronously"}], "responses": {"200": {"description": "Products enhanced successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "enhanced": {"type": "integer"}, "errors": {"type": "array", "items": {"type": "string"}}}}}}}, "400": {"description": "Invalid input"}}}}, "/products/re-vectorize": {"post": {"summary": "Re-vectorize Products", "description": "Triggers re-vectorization of product data", "tags": ["Product"], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"batch_size": {"type": "integer", "description": "Number of products to process in each batch"}, "clean_start": {"type": "boolean", "description": "Whether to delete existing vectors before re-vectorization"}}}}}}, "responses": {"200": {"description": "Re-vectorization started successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "string"}, "job_id": {"type": "string"}, "job_type": {"type": "string"}, "clean_start": {"type": "boolean"}}}}}}, "500": {"description": "Internal server error"}}}}, "/products/vectorization-status": {"get": {"summary": "Get Vectorization Status", "description": "Retrieves the status of product data vectorization", "tags": ["Product"], "responses": {"200": {"description": "Vectorization status retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"isProcessing": {"type": "boolean"}, "total": {"type": "integer"}, "completed": {"type": "integer"}, "failed": {"type": "integer"}, "pending": {"type": "integer"}, "processing": {"type": "integer"}, "jobs": {"type": "array", "items": {"type": "object", "properties": {"jobType": {"type": "string"}, "status": {"type": "string"}, "startedAt": {"type": "string", "format": "date-time"}, "completedAt": {"type": "string", "format": "date-time"}, "error": {"type": "string"}}}}}}}}}, "500": {"description": "Internal server error"}}}}, "/public/products": {"get": {"summary": "List Public Products (Grouped by MetaSKU)", "description": "Get a paginated list of products grouped by metaSKU with variants for ecommerce. Use ?grouped=false for individual products.", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}], "parameters": [{"in": "query", "name": "per_page", "schema": {"type": "integer", "default": 25}, "description": "Number of items per page"}, {"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number (for compatibility)"}, {"in": "query", "name": "sort", "schema": {"type": "string", "default": "created_at", "enum": ["product_name", "brand_name", "base_price", "created_at"]}, "description": "Field to sort by (for grouped products)"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"], "default": "desc"}, "description": "Sort direction"}, {"in": "query", "name": "q", "schema": {"type": "string"}, "description": "Search query for product name, brand, category, etc."}, {"in": "query", "name": "brand", "schema": {"type": "string"}, "description": "Filter by brand name"}, {"in": "query", "name": "category", "schema": {"type": "string"}, "description": "Filter by category"}, {"in": "query", "name": "subcategory", "schema": {"type": "string"}, "description": "Filter by subcategory"}, {"in": "query", "name": "grouped", "schema": {"type": "boolean", "default": true}, "description": "Whether to group products by metaSKU (default true for ecommerce)"}], "responses": {"200": {"description": "List of grouped products with variants (default) or individual products", "content": {"application/json": {"schema": {"type": "object", "properties": {"products": {"type": "array", "items": {"oneOf": [{"$ref": "#/components/schemas/Product"}, {"type": "object", "properties": {"meta_sku": {"type": "string"}, "product_name": {"type": "string"}, "brand_name": {"type": "string"}, "category": {"type": "string"}, "subcategory": {"type": "string"}, "variants": {"type": "array", "items": {"type": "object"}}, "base_price": {"type": "number"}, "price_range": {"type": "object"}}}]}}, "pagination": {"type": "object"}}}}}}, "401": {"description": "API key missing or invalid", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/public/products/filters": {"get": {"summary": "Get Available Product Filters", "description": "Get all available filter options for brands, categories, and subcategories", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Available filter options", "content": {"application/json": {"schema": {"type": "object", "properties": {"brands": {"type": "array", "items": {"type": "string"}}, "categories": {"type": "array", "items": {"type": "string"}}, "subcategories": {"type": "array", "items": {"type": "string"}}}}}}}, "401": {"description": "API key missing or invalid"}, "404": {"description": "Location not found"}, "500": {"description": "Internal server error"}}}}, "/public/products/upload": {"post": {"summary": "Upload Public Products", "description": "Upload product data for a location (API key required)", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductUploadRequest"}}, "multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "reindex": {"type": "boolean"}, "enhance_with_ai": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "Product data imported successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProductUploadResponse"}}}}, "400": {"description": "Failed to import product data"}, "401": {"description": "API key missing or invalid"}, "500": {"description": "Internal server error"}}}}, "/public/products/chat": {"post": {"summary": "Chat with <PERSON><PERSON> for Product Recommendations", "description": "Chat with Smokey AI for product recommendations. Requires location API key for access.\n\n**Authentication Options:**\n- **API Key Only (Anonymous)**: Just provide the API key in Authorization header. Chat won't be saved.\n- **API Key + User Token (Authenticated)**: Provide both headers for full functionality including chat history.\n\n**Headers Required:**\n- `Authorization: Bearer your_location_api_key` (Required)\n- `x-user-token: user_jwt_token` (Optional - for chat persistence)\n", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}, {"UserAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageRequest"}}}}, "responses": {"200": {"description": "<PERSON><PERSON>'s response with product recommendations", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatMessageResponse"}}}}, "400": {"description": "Invalid request"}, "401": {"description": "API key missing or invalid"}, "404": {"description": "Cha<PERSON> not found"}, "500": {"description": "Internal server error"}}}}, "/public/products/search": {"get": {"summary": "Search Products Directly", "description": "Search products without chat interface using semantic search", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}], "parameters": [{"in": "query", "name": "q", "required": true, "schema": {"type": "string"}, "description": "Search query"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 10, "maximum": 50}, "description": "Number of products to return"}], "responses": {"200": {"description": "Product search results", "content": {"application/json": {"schema": {"type": "object", "properties": {"products": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}, "search_metadata": {"type": "object", "properties": {"query": {"type": "string"}, "total_found": {"type": "integer"}, "search_method": {"type": "string"}}}}}}}}, "400": {"description": "Missing or invalid search query"}, "500": {"description": "Internal server error"}}}}, "/public/products/status": {"get": {"summary": "API Status Check", "description": "Check if the public API is working and get location info", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "API status and location information", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}, "location": {"type": "object"}, "smokey_available": {"type": "boolean"}, "product_search_available": {"type": "boolean"}}}}}}, "401": {"description": "API key missing or invalid"}, "500": {"description": "Internal server error"}}}}, "/public/products/chats": {"get": {"summary": "List Chats (Authenticated Users Only)", "description": "Retrieve list of chats for authenticated users", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}, {"UserAuth": []}], "responses": {"200": {"description": "List of chats retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "chat_id": {"type": "string"}, "name": {"type": "string"}, "created_at": {"type": "string"}, "updated_at": {"type": "string"}, "location_id": {"type": "string"}, "status": {"type": "string"}}}}}}}, "401": {"description": "Authentication required"}, "500": {"description": "Internal server error"}}}, "post": {"summary": "Create New Chat (Authenticated Users Only)", "description": "Create a new chat for authenticated users", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}, {"UserAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["message", "agent_id"], "properties": {"message": {"type": "string"}, "agent_id": {"type": "string"}}}}}}, "responses": {"200": {"description": "<PERSON><PERSON> created successfully"}, "400": {"description": "Invalid request"}, "401": {"description": "Authentication required"}, "500": {"description": "Internal server error"}}}}, "/public/products/chats/{id}": {"get": {"summary": "Get Chat Details (Authenticated Users Only)", "description": "Retrieve details of a specific chat for authenticated users", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}, {"UserAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Chat ID"}], "responses": {"200": {"description": "Chat details retrieved successfully"}, "401": {"description": "Authentication required"}, "404": {"description": "Cha<PERSON> not found"}, "500": {"description": "Internal server error"}}}, "put": {"summary": "Update/<PERSON><PERSON> (Authenticated Users Only)", "description": "Update chat name for authenticated users", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}, {"UserAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Chat ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string"}}}}}}, "responses": {"200": {"description": "Chat updated successfully"}, "401": {"description": "Authentication required"}, "404": {"description": "Cha<PERSON> not found"}, "500": {"description": "Internal server error"}}}, "delete": {"summary": "Delete Chat (Authenticated Users Only)", "description": "Delete a chat for authenticated users", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}, {"UserAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Chat ID"}], "responses": {"204": {"description": "<PERSON><PERSON> deleted successfully"}, "401": {"description": "Authentication required"}, "404": {"description": "Cha<PERSON> not found"}, "500": {"description": "Internal server error"}}}}, "/public/products/chats/{id}/archive": {"post": {"summary": "Archive Chat (Authenticated Users Only)", "description": "Archive a chat for authenticated users", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}, {"UserAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Chat ID"}], "responses": {"204": {"description": "Chat archived successfully"}, "401": {"description": "Authentication required"}, "404": {"description": "Cha<PERSON> not found"}, "500": {"description": "Internal server error"}}}}, "/public/products/chats/{id}/messages": {"get": {"summary": "Get Chat Messages (Authenticated Users Only)", "description": "Retrieve messages for a specific chat", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}, {"UserAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Chat ID"}, {"in": "query", "name": "before", "schema": {"type": "string"}, "description": "Get messages before this timestamp"}, {"in": "query", "name": "limit", "schema": {"type": "integer", "default": 50}, "description": "Number of messages to return"}], "responses": {"200": {"description": "Messages retrieved successfully"}, "401": {"description": "Authentication required"}, "404": {"description": "Cha<PERSON> not found"}, "500": {"description": "Internal server error"}}}}, "/public/products/chats/agents": {"get": {"summary": "Get Available Agents", "description": "Get list of available chat agents for the location", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}], "responses": {"200": {"description": "Available agents retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "role": {"type": "string"}, "description": {"type": "string"}, "icon": {"type": "string"}, "capabilities": {"type": "array", "items": {"type": "string"}}, "unlocked": {"type": "boolean"}}}}}}}, "500": {"description": "Internal server error"}}}}, "/public/products/chats/{id}/metadata": {"put": {"summary": "Update <PERSON><PERSON> (Authenticated Users Only)", "description": "Update metadata for a specific chat", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}, {"UserAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "description": "Chat ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}, "responses": {"200": {"description": "Metadata updated successfully"}, "400": {"description": "Invalid metadata"}, "401": {"description": "Authentication required"}, "404": {"description": "Cha<PERSON> not found"}, "500": {"description": "Internal server error"}}}}, "/public/products/chats/generate-plan": {"post": {"summary": "Generate Marketing Plan from Chat", "description": "Generate an automation plan based on chat message", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["message"], "properties": {"message": {"type": "string"}, "chat_id": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "Plan generated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "plan": {"type": "object"}, "message": {"type": "string"}}}}}}, "400": {"description": "Invalid request"}, "500": {"description": "Internal server error"}}}}, "/public/products/chats/uploads": {"post": {"summary": "Upload File to Chat (Authenticated Users Only)", "description": "Upload a file for use in chat conversations", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}, {"UserAuth": []}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "required": ["file"], "properties": {"file": {"type": "string", "format": "binary"}, "chat_id": {"type": "string", "nullable": true}}}}}}, "responses": {"200": {"description": "File uploaded successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "type": {"type": "string"}, "size": {"type": "integer"}, "url": {"type": "string"}}}}}}, "400": {"description": "No file uploaded or invalid request"}, "401": {"description": "Authentication required"}, "500": {"description": "Internal server error"}}}}, "/public/products/chats/uploads/{id}": {"get": {"summary": "Get Uploaded File", "description": "Retrieve an uploaded file by ID", "tags": ["PublicProduct"], "security": [{"ApiKeyAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "File ID"}], "responses": {"200": {"description": "File content", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}, "400": {"description": "Invalid file ID"}, "403": {"description": "Access denied"}, "404": {"description": "File not found"}, "500": {"description": "Internal server error"}}}}, "/profile": {"get": {"summary": "Get Admin Profile", "description": "Retrieve the profile information of the currently authenticated admin", "tags": ["Profile"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Admin profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AdminProfile"}}}}, "401": {"description": "Unauthorized - <PERSON>min not authenticated", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "Unauthorized"}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string"}}}}}}}}}, "/c": {"get": {"summary": "Track link clicks and redirect", "tags": ["Links"], "parameters": [{"in": "query", "name": "r", "schema": {"type": "string"}, "description": "Encoded redirect URL"}], "responses": {"200": {"description": "Default page when no redirect URL is provided", "content": {"text/plain": {"schema": {"type": "string"}}}}, "303": {"description": "Redirect to the target URL", "headers": {"Location": {"schema": {"type": "string"}, "description": "The URL to redirect to"}}}}}}, "/o": {"get": {"summary": "Track link opens", "tags": ["Links"], "parameters": [{"in": "query", "name": "r", "schema": {"type": "string"}, "description": "Encoded redirect URL"}], "responses": {"204": {"description": "Link open event tracked successfully"}}}}, "/.well-known/{file}": {"get": {"summary": "Get well-known file from organization's tracking deeplink mirror", "tags": ["Links"], "parameters": [{"in": "path", "name": "file", "required": true, "schema": {"type": "string"}, "description": "Name of the well-known file to retrieve"}], "responses": {"200": {"description": "Well-known file content", "content": {"application/json": {"schema": {"type": "object"}}}}, "404": {"description": "Organization not found or no tracking deeplink mirror URL configured"}}}}, "/resources": {"get": {"summary": "Get all resources for a location", "tags": ["Resources"], "parameters": [{"in": "query", "name": "type", "schema": {"type": "string", "enum": ["font", "snippet"]}, "description": "Filter resources by type"}], "responses": {"200": {"description": "List of resources", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Resource"}}}}}}}, "post": {"summary": "Create a new resource", "tags": ["Resources"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["type", "name", "value"], "properties": {"type": {"type": "string", "enum": ["font", "snippet"], "description": "The type of resource"}, "name": {"type": "string", "description": "The name of the resource"}, "value": {"type": "object", "description": "The resource value object", "additionalProperties": true}}}}}}, "responses": {"200": {"description": "The created resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Resource"}}}}}}}, "/resources/{resourceId}": {"delete": {"summary": "Delete a resource", "tags": ["Resources"], "parameters": [{"in": "path", "name": "resourceId", "required": true, "schema": {"type": "integer"}, "description": "The ID of the resource to delete"}], "responses": {"200": {"description": "Resource successfully deleted", "content": {"application/json": {"schema": {"type": "boolean", "example": true}}}}, "404": {"description": "Resource not found"}}}}, "/templates": {"get": {"summary": "Get paged list of templates", "tags": ["Templates"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}], "responses": {"200": {"description": "List of templates", "content": {"application/json": {"schema": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}}}}}}}}, "post": {"summary": "Create a new template", "tags": ["Templates"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"type": "object", "required": ["type", "campaign_id", "locale"], "properties": {"type": {"type": "string", "enum": ["email"]}, "campaign_id": {"type": "integer"}, "locale": {"type": "string"}, "data": {"$ref": "#/components/schemas/TemplateEmailData"}}}, {"type": "object", "required": ["type", "campaign_id", "locale"], "properties": {"type": {"type": "string", "enum": ["text"]}, "campaign_id": {"type": "integer"}, "locale": {"type": "string"}, "data": {"$ref": "#/components/schemas/TemplateTextData"}}}, {"type": "object", "required": ["type", "campaign_id", "locale"], "properties": {"type": {"type": "string", "enum": ["push"]}, "campaign_id": {"type": "integer"}, "locale": {"type": "string"}, "data": {"$ref": "#/components/schemas/TemplatePushData"}}}, {"type": "object", "required": ["type", "campaign_id", "locale"], "properties": {"type": {"type": "string", "enum": ["webhook"]}, "campaign_id": {"type": "integer"}, "locale": {"type": "string"}, "data": {"$ref": "#/components/schemas/TemplateWebhookData"}}}]}}}}, "responses": {"200": {"description": "The created template", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}}}}, "/templates/{templateId}": {"get": {"summary": "Get a specific template", "tags": ["Templates"], "parameters": [{"in": "path", "name": "templateId", "required": true, "schema": {"type": "integer"}, "description": "The ID of the template to retrieve"}], "responses": {"200": {"description": "The requested template", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "404": {"description": "Temp<PERSON> not found"}}}, "patch": {"summary": "Update a template", "tags": ["Templates"], "parameters": [{"in": "path", "name": "templateId", "required": true, "schema": {"type": "integer"}, "description": "The ID of the template to update"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"type": "object", "required": ["type", "data"], "properties": {"type": {"type": "string", "enum": ["email"]}, "data": {"$ref": "#/components/schemas/TemplateEmailData"}}}, {"type": "object", "required": ["type", "data"], "properties": {"type": {"type": "string", "enum": ["text"]}, "data": {"$ref": "#/components/schemas/TemplateTextData"}}}, {"type": "object", "required": ["type", "data"], "properties": {"type": {"type": "string", "enum": ["push"]}, "data": {"$ref": "#/components/schemas/TemplatePushData"}}}, {"type": "object", "required": ["type", "data"], "properties": {"type": {"type": "string", "enum": ["webhook"]}, "data": {"$ref": "#/components/schemas/TemplateWebhookData"}}}]}}}}, "responses": {"200": {"description": "The updated template", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}, "404": {"description": "Temp<PERSON> not found"}}}, "delete": {"summary": "Delete a template", "tags": ["Templates"], "parameters": [{"in": "path", "name": "templateId", "required": true, "schema": {"type": "integer"}, "description": "The ID of the template to delete"}], "responses": {"200": {"description": "Template successfully deleted", "content": {"application/json": {"schema": {"type": "boolean", "example": true}}}}, "404": {"description": "Temp<PERSON> not found"}}}}, "/templates/{templateId}/preview": {"post": {"summary": "Preview a template with variables", "tags": ["Templates"], "parameters": [{"in": "path", "name": "templateId", "required": true, "schema": {"type": "integer"}, "description": "The ID of the template to preview"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"user": {"type": "object", "description": "User data for template variables"}, "event": {"type": "object", "description": "Event data for template variables"}, "context": {"type": "object", "description": "Additional context data for template variables"}}}}}}, "responses": {"200": {"description": "The compiled template preview", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}, "404": {"description": "Temp<PERSON> not found"}}}}, "/templates/{templateId}/proof": {"post": {"summary": "Send a proof of the template", "tags": ["Templates"], "parameters": [{"in": "path", "name": "templateId", "required": true, "schema": {"type": "integer"}, "description": "The ID of the template to send proof for"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["recipient"], "properties": {"variables": {"type": "object", "nullable": true, "additionalProperties": true, "description": "Variables to use in the template"}, "recipient": {"type": "string", "description": "Email address to send the proof to"}}}}}}, "responses": {"200": {"description": "<PERSON><PERSON> sent successfully", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true}}}}, "404": {"description": "Temp<PERSON> not found"}}}}, "/retailers": {"get": {"summary": "List Retailers", "description": "Retrieves a paginated list of retailers for the current location", "tags": ["Retailer"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}, {"in": "query", "name": "sort", "schema": {"type": "string"}, "description": "Field to sort by"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"]}, "description": "Sort direction"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/Retailer"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}}}}}}}}, "post": {"summary": "Create Retailer", "description": "Creates a new retailer for the current location", "tags": ["Retailer"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RetailerCreateParams"}}}}, "responses": {"200": {"description": "Retailer created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Retailer"}}}}, "400": {"description": "Invalid input"}}}}, "/retailers/{retailerId}": {"get": {"summary": "Get Retailer by ID", "description": "Retrieves a specific retailer by its identifier", "tags": ["Retailer"], "parameters": [{"in": "path", "name": "retailerId", "required": true, "schema": {"type": "integer"}, "description": "Retailer identifier"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Retailer"}}}}, "404": {"description": "Retailer not found"}}}, "patch": {"summary": "Update Retailer", "description": "Updates an existing retailer", "tags": ["Retailer"], "parameters": [{"in": "path", "name": "retailerId", "required": true, "schema": {"type": "integer"}, "description": "Retailer identifier"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RetailerUpdateParams"}}}}, "responses": {"200": {"description": "Retailer updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Retailer"}}}}, "404": {"description": "Retailer not found"}}}, "delete": {"summary": "Delete Retailer", "description": "Deletes a retailer", "tags": ["Retailer"], "parameters": [{"in": "path", "name": "retailerId", "required": true, "schema": {"type": "integer"}, "description": "Retailer identifier"}], "responses": {"204": {"description": "Retailer deleted successfully"}, "404": {"description": "Retailer not found"}}}}, "/retailers/import": {"post": {"summary": "Import Retailer Data", "description": "Imports retailer data from file, API, or direct JSON.", "tags": ["RetailerData"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Retailer data imported successfully"}, "400": {"description": "Failed to import retailer data"}}}}, "/retailers/data": {"get": {"summary": "List Retailer Data", "description": "Retrieves a paginated list of retailer data for the current location", "tags": ["RetailerData"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}, {"in": "query", "name": "sort", "schema": {"type": "string"}, "description": "Field to sort by"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"]}, "description": "Sort direction"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/RetailerData"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}}}}}}, "500": {"description": "Failed to retrieve retailer data"}}}}, "/retailers/search": {"post": {"summary": "Search Retailer Data", "description": "Searches retailer data using vector search.", "tags": ["RetailerData"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string"}, "filters": {"type": "object"}, "limit": {"type": "integer"}}}}}}, "responses": {"200": {"description": "Search results", "content": {"application/json": {"schema": {"type": "object"}}}}, "400": {"description": "Search query is required"}, "500": {"description": "Error searching retailer data"}}}}, "/retailers/data/re-vectorize": {"post": {"summary": "Re-vectorize Retailer Data", "description": "Triggers re-vectorization of retailer data", "tags": ["RetailerData"], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"batch_size": {"type": "integer", "description": "Number of records to process in each batch"}, "clean_start": {"type": "boolean", "description": "Whether to delete existing vectors before re-vectorization"}}}}}}, "responses": {"200": {"description": "Re-vectorization started successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "string"}, "job_id": {"type": "string"}, "job_type": {"type": "string"}, "clean_start": {"type": "boolean"}}}}}}, "500": {"description": "Internal server error"}}}}, "/retailers/data/vectorization-status": {"get": {"summary": "Get Vectorization Status", "description": "Retrieves the status of retailer data vectorization", "tags": ["RetailerData"], "responses": {"200": {"description": "Vectorization status retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"isProcessing": {"type": "boolean"}, "total": {"type": "integer"}, "completed": {"type": "integer"}, "failed": {"type": "integer"}, "pending": {"type": "integer"}, "processing": {"type": "integer"}, "jobs": {"type": "array", "items": {"type": "object", "properties": {"jobType": {"type": "string"}, "status": {"type": "string"}, "startedAt": {"type": "string", "format": "date-time"}, "completedAt": {"type": "string", "format": "date-time"}, "error": {"type": "string"}}}}}}}}}, "500": {"description": "Internal server error"}}}}, "/reviews/import": {"post": {"summary": "Import Review Data", "description": "Imports review data from file, product data, or direct JSON.", "tags": ["ReviewData"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object"}}}}, "responses": {"200": {"description": "Review data imported successfully"}, "400": {"description": "Failed to import review data"}}}}, "/reviews/data": {"get": {"summary": "List Review Data", "description": "Retrieves a list of review data for the current location.", "tags": ["ReviewData"], "responses": {"200": {"description": "List of review data retrieved successfully", "content": {"application/json": {"schema": {"type": "object"}}}}, "500": {"description": "Failed to retrieve review data"}}}}, "/reviews/search": {"post": {"summary": "Search Review Data", "description": "Searches review data using vector search.", "tags": ["ReviewData"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"query": {"type": "string"}, "filters": {"type": "object"}, "limit": {"type": "integer"}}}}}}, "responses": {"200": {"description": "Search results", "content": {"application/json": {"schema": {"type": "object"}}}}, "400": {"description": "Search query is required"}, "500": {"description": "Error searching review data"}}}}, "/reviews/stats/product/{productId}": {"get": {"summary": "Get Product Review Statistics", "description": "Retrieves review statistics for a specific product.", "tags": ["ReviewData"], "parameters": [{"in": "path", "name": "productId", "required": true, "schema": {"type": "string"}, "description": "Product ID"}], "responses": {"200": {"description": "Product review statistics", "content": {"application/json": {"schema": {"type": "object"}}}}, "400": {"description": "Product ID is required"}}}}, "/reviews/re-vectorize": {"post": {"summary": "Re-vectorize Review Data", "description": "Re-vectorizes all review data for the current location.", "tags": ["ReviewData"], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"clean_start": {"type": "boolean"}}}}}}, "responses": {"200": {"description": "Review data re-vectorization completed", "content": {"application/json": {"schema": {"type": "object"}}}}, "500": {"description": "Failed to re-vectorize review data"}}}}, "/reviews/vectorization-status": {"get": {"summary": "Get Review Data Vectorization Status", "description": "Retrieves the status of review data vectorization for the current location.", "tags": ["ReviewData"], "responses": {"200": {"description": "Vectorization status", "content": {"application/json": {"schema": {"type": "object"}}}}, "500": {"description": "Failed to retrieve vectorization status"}}}}, "/shared-assets/upload": {"post": {"summary": "Upload a shared image asset", "tags": ["Shared Assets"], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "businessName": {"type": "string"}, "folder": {"type": "string"}, "type": {"type": "string"}, "locationId": {"type": "integer"}}}}}}, "responses": {"200": {"description": "Image uploaded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SharedAssetUploadResponse"}}}}, "500": {"description": "File upload failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SharedAssetErrorResponse"}}}}}}}, "/shared-assets/image/{imageId}.{ext}": {"get": {"summary": "Serve an image by ID and extension (branded URL)", "tags": ["Shared Assets"], "parameters": [{"in": "path", "name": "imageId", "required": true, "schema": {"type": "integer"}, "description": "Image ID"}, {"in": "path", "name": "ext", "required": true, "schema": {"type": "string"}, "description": "File extension (e.g., png, jpg)"}, {"in": "query", "name": "locationId", "schema": {"type": "integer"}, "description": "Location ID (optional)"}], "responses": {"200": {"description": "The image file", "content": {"image/png": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}, "image/gif": {"schema": {"type": "string", "format": "binary"}}, "image/webp": {"schema": {"type": "string", "format": "binary"}}}}, "302": {"description": "Redirect to the direct image URL if proxy fails"}, "400": {"description": "Invalid image ID"}, "404": {"description": "Image not found"}, "500": {"description": "Failed to serve file"}}}}, "/shared-assets/image/{imageId}": {"get": {"summary": "Serve an image by ID (extensionless, browser-aware)", "tags": ["Shared Assets"], "parameters": [{"in": "path", "name": "imageId", "required": true, "schema": {"type": "integer"}, "description": "Image ID"}, {"in": "query", "name": "locationId", "schema": {"type": "integer"}, "description": "Location ID (optional)"}, {"in": "query", "name": "direct", "schema": {"type": "string"}, "description": "If 'true', always redirect to direct URL"}], "responses": {"200": {"description": "The image file (proxied for browsers)", "content": {"image/png": {"schema": {"type": "string", "format": "binary"}}, "image/jpeg": {"schema": {"type": "string", "format": "binary"}}, "image/gif": {"schema": {"type": "string", "format": "binary"}}, "image/webp": {"schema": {"type": "string", "format": "binary"}}}}, "302": {"description": "Redirect to the direct image URL"}, "400": {"description": "Invalid image ID"}, "404": {"description": "Image not found"}, "500": {"description": "Failed to serve file"}}}}, "/shared-assets/{imageId}": {"get": {"summary": "Serve an image by ID (legacy endpoint)", "tags": ["Shared Assets"], "parameters": [{"in": "path", "name": "imageId", "required": true, "schema": {"type": "integer"}, "description": "Image ID"}, {"in": "query", "name": "locationId", "schema": {"type": "integer"}, "description": "Location ID (optional)"}], "responses": {"302": {"description": "Redirect to the direct image URL"}, "400": {"description": "Invalid image ID"}, "404": {"description": "Image not found"}, "500": {"description": "Failed to serve file"}}}}, "/images": {"post": {"summary": "Upload a new image", "tags": ["Images"], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "Image uploaded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Image"}}}}, "400": {"description": "Invalid file format or size"}}}, "get": {"summary": "List images (paginated)", "tags": ["Images"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}, {"in": "query", "name": "sort", "schema": {"type": "string"}, "description": "Sort field"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"]}, "description": "Sort direction"}], "responses": {"200": {"description": "Paginated list of images", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImageListResponse"}}}}}}}, "/images/{imageId}": {"get": {"summary": "Get image by ID", "tags": ["Images"], "parameters": [{"in": "path", "name": "imageId", "required": true, "schema": {"type": "integer"}, "description": "Image ID"}], "responses": {"200": {"description": "Image details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Image"}}}}, "404": {"description": "Image not found"}}}, "patch": {"summary": "Update image metadata", "tags": ["Images"], "parameters": [{"in": "path", "name": "imageId", "required": true, "schema": {"type": "integer"}, "description": "Image ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImageUpdateRequest"}}}}, "responses": {"200": {"description": "Image updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Image"}}}}, "404": {"description": "Image not found"}}}}, "/subscriptions/{organizationId}": {"get": {"summary": "Get subscription by organization ID", "tags": ["Subscriptions"], "parameters": [{"in": "path", "name": "organizationId", "required": true, "schema": {"type": "integer"}, "description": "Organization ID"}], "responses": {"200": {"description": "Subscription details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Subscription"}}}}, "404": {"description": "Subscription not found"}, "500": {"description": "Failed to retrieve subscription data"}}}}, "/subscriptions": {"get": {"summary": "List subscriptions (paginated)", "tags": ["Subscriptions"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}, {"in": "query", "name": "sort", "schema": {"type": "string"}, "description": "Sort field"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"]}, "description": "Sort direction"}], "responses": {"200": {"description": "Paginated list of subscriptions", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionListResponse"}}}}}}, "post": {"summary": "Create a new subscription", "tags": ["Subscriptions"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionCreateRequest"}}}}, "responses": {"200": {"description": "Subscription created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Subscription"}}}}, "401": {"description": "Unauthorized - Admin role required"}}}}, "/subscriptions/{subscriptionId}": {"get": {"summary": "Get subscription by ID", "tags": ["Subscriptions"], "parameters": [{"in": "path", "name": "subscriptionId", "required": true, "schema": {"type": "integer"}, "description": "Subscription ID"}], "responses": {"200": {"description": "Subscription details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Subscription"}}}}, "404": {"description": "Subscription not found"}}}, "patch": {"summary": "Update subscription", "tags": ["Subscriptions"], "parameters": [{"in": "path", "name": "subscriptionId", "required": true, "schema": {"type": "integer"}, "description": "Subscription ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionUpdateRequest"}}}}, "responses": {"200": {"description": "Subscription updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Subscription"}}}}, "404": {"description": "Subscription not found"}}}}, "/subscriptions/can-create-location/{organizationId}": {"get": {"summary": "Check if organization can create more locations", "tags": ["Subscriptions"], "parameters": [{"in": "path", "name": "organizationId", "required": true, "schema": {"type": "integer"}, "description": "Organization ID"}], "responses": {"200": {"description": "Location creation eligibility check result", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationCreationResponse"}}}}, "400": {"description": "Invalid organization ID"}, "403": {"description": "Unauthorized access to organization"}, "500": {"description": "Failed to check location creation eligibility"}}}}, "/subscriptions/cancel/{subscriptionId}": {"post": {"summary": "Cancel a subscription", "tags": ["Subscriptions"], "parameters": [{"in": "path", "name": "subscriptionId", "required": true, "schema": {"type": "string"}, "description": "Stripe subscription ID"}], "responses": {"200": {"description": "Subscription cancellation initiated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionCancelResponse"}}}}, "400": {"description": "Subscription ID is required"}, "403": {"description": "Unauthorized to cancel subscription"}, "500": {"description": "Failed to cancel subscription"}}}}, "/supabase/upload": {"post": {"summary": "Upload data to Supabase", "tags": ["Supabase"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SupabaseUploadRequest"}}}}, "responses": {"200": {"description": "Data uploaded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SupabaseUploadResponse"}}}}, "401": {"description": "Unauthorized - Admin role required"}, "500": {"description": "Failed to upload data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SupabaseUploadResponse"}}}}}}}, "/supabase/status": {"get": {"summary": "Get Supabase integration status", "tags": ["Supabase"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "Integration status retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SupabaseStatusResponse"}}}}, "401": {"description": "Unauthorized - Admin role required"}, "500": {"description": "Failed to get integration status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SupabaseStatusResponse"}}}}}}}, "/tags": {"get": {"summary": "List tags (paginated)", "tags": ["Tags"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}, {"in": "query", "name": "sort", "schema": {"type": "string"}, "description": "Sort field"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"]}, "description": "Sort direction"}], "responses": {"200": {"description": "Paginated list of tags", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagListResponse"}}}}}}, "post": {"summary": "Create a new tag", "tags": ["Tags"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagCreateRequest"}}}}, "responses": {"200": {"description": "Tag created or retrieved if already exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tag"}}}}, "401": {"description": "Unauthorized - Editor role required"}}}}, "/tags/all": {"get": {"summary": "List all tags (non-paginated)", "tags": ["Tags"], "responses": {"200": {"description": "List of all tags", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}}}}}}}, "/tags/used/{entity}": {"get": {"summary": "Get used tags for a specific entity", "tags": ["Tags"], "parameters": [{"in": "path", "name": "entity", "required": true, "schema": {"type": "string"}, "description": "Entity type (e.g., 'products', 'users')"}], "responses": {"200": {"description": "List of used tags for the entity", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}}}}}}}, "/tags/{tagId}": {"get": {"summary": "Get tag by ID", "tags": ["Tags"], "parameters": [{"in": "path", "name": "tagId", "required": true, "schema": {"type": "integer"}, "description": "Tag ID"}], "responses": {"200": {"description": "Tag details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tag"}}}}, "404": {"description": "Tag not found"}}}, "patch": {"summary": "Update tag", "tags": ["Tags"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "tagId", "required": true, "schema": {"type": "integer"}, "description": "Tag ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagUpdateRequest"}}}}, "responses": {"200": {"description": "Tag updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tag"}}}}, "401": {"description": "Unauthorized - Editor role required"}, "404": {"description": "Tag not found"}}}, "delete": {"summary": "Delete tag", "tags": ["Tags"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "tagId", "required": true, "schema": {"type": "integer"}, "description": "Tag ID"}], "responses": {"200": {"description": "Tag deleted successfully", "content": {"application/json": {"schema": {"type": "boolean"}}}}, "401": {"description": "Unauthorized - Editor role required"}, "404": {"description": "Tag not found"}}}}, "/custom-prompts": {"get": {"summary": "List Custom Prompts", "description": "Retrieve all custom prompts for the current admin", "tags": ["Custom Prompts"], "security": [{"bearerAuth": []}], "responses": {"200": {"description": "List of custom prompts retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomPromptListResponse"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "500": {"description": "Internal server error"}}}, "post": {"summary": "Create Custom Prompt", "description": "Create a new custom prompt. If the prompt is set as active, all other prompts will be deactivated.", "tags": ["Custom Prompts"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomPromptPayload"}, "example": {"name": "Product Recommendation", "content": "Recommend products based on user preferences", "is_active": true}}}}, "responses": {"201": {"description": "Custom prompt created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomPrompt"}}}}, "400": {"description": "Invalid request body"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "500": {"description": "Internal server error"}}}}, "/custom-prompts/{id}": {"get": {"summary": "Get Custom Prompt", "description": "Retrieve a specific custom prompt by ID", "tags": ["Custom Prompts"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the custom prompt"}], "responses": {"200": {"description": "Custom prompt retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomPrompt"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "404": {"description": "Custom prompt not found"}, "500": {"description": "Internal server error"}}}, "put": {"summary": "Update Custom Prompt", "description": "Update an existing custom prompt. If the prompt is set as active, all other prompts will be deactivated.", "tags": ["Custom Prompts"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the custom prompt"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomPromptPayload"}}}}, "responses": {"200": {"description": "Custom prompt updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomPrompt"}}}}, "400": {"description": "Invalid request body"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "404": {"description": "Custom prompt not found"}, "500": {"description": "Internal server error"}}}, "delete": {"summary": "Delete Custom Prompt", "description": "Delete a custom prompt", "tags": ["Custom Prompts"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the custom prompt"}], "responses": {"204": {"description": "Custom prompt deleted successfully"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "404": {"description": "Custom prompt not found"}, "500": {"description": "Internal server error"}}}}, "/custom-prompts/{id}/toggle": {"post": {"summary": "Toggle Custom Prompt", "description": "Toggle the active state of a custom prompt. If activated, all other prompts will be deactivated.", "tags": ["Custom Prompts"], "security": [{"bearerAuth": []}], "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "integer"}, "description": "ID of the custom prompt"}], "responses": {"200": {"description": "Custom prompt toggled successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomPrompt"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden - Insufficient permissions"}, "404": {"description": "Custom prompt not found"}, "500": {"description": "Internal server error"}}}}, "/users": {"get": {"summary": "List Users", "description": "Retrieves a paginated list of users for the current location with search and filter capabilities", "tags": ["User"], "parameters": [{"in": "query", "name": "page", "schema": {"type": "integer"}, "description": "Page number for pagination"}, {"in": "query", "name": "limit", "schema": {"type": "integer"}, "description": "Number of items per page"}, {"in": "query", "name": "sort", "schema": {"type": "string"}, "description": "Field to sort by"}, {"in": "query", "name": "direction", "schema": {"type": "string", "enum": ["asc", "desc"]}, "description": "Sort direction"}, {"in": "query", "name": "q", "schema": {"type": "string"}, "description": "Search query for user name, email, phone, external_id"}, {"in": "query", "name": "filter", "schema": {"type": "object"}, "description": "Filter object for specific field filtering"}], "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}, "total": {"type": "integer"}, "page": {"type": "integer"}, "limit": {"type": "integer"}}}}}}}}, "patch": {"summary": "Update Users", "description": "Updates multiple users in the current location", "tags": ["User"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserParams"}}}}}, "responses": {"204": {"description": "Users updated successfully"}, "400": {"description": "Invalid input"}}}, "delete": {"summary": "Delete Users", "description": "Deletes multiple users from the current location", "tags": ["User"], "parameters": [{"in": "query", "name": "user_id", "schema": {"type": "array", "items": {"type": "string"}}, "description": "Array of user IDs to delete"}], "responses": {"204": {"description": "Users deleted successfully"}, "400": {"description": "Invalid input"}}}}, "/users/re-vectorize": {"post": {"summary": "Re-vectorize User Data", "description": "Triggers re-vectorization of user data for the current location", "tags": ["User"], "requestBody": {"required": false, "content": {"application/json": {"schema": {"type": "object", "properties": {"batch_size": {"type": "integer", "description": "Number of users to process in each batch"}, "clean_start": {"type": "boolean", "description": "Whether to delete existing vectors before re-vectorization"}}}}}}, "responses": {"200": {"description": "Re-vectorization started successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string"}, "status": {"type": "string"}, "job_id": {"type": "string"}, "job_type": {"type": "string"}, "clean_start": {"type": "boolean"}}}}}}, "500": {"description": "Internal server error"}}}}, "/users/vectorization-status": {"get": {"summary": "Get Vectorization Status", "description": "Retrieves the status of user data vectorization for the current location", "tags": ["User"], "responses": {"200": {"description": "Vectorization status retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"isProcessing": {"type": "boolean"}, "total": {"type": "integer"}, "completed": {"type": "integer"}, "failed": {"type": "integer"}, "pending": {"type": "integer"}, "processing": {"type": "integer"}, "jobs": {"type": "array", "items": {"type": "object", "properties": {"jobType": {"type": "string"}, "status": {"type": "string"}, "startedAt": {"type": "string", "format": "date-time"}, "completedAt": {"type": "string", "format": "date-time"}, "error": {"type": "string"}}}}}}}}}, "500": {"description": "Internal server error"}}}}, "/users/{userId}": {"patch": {"summary": "Update User", "description": "Updates a single user by ID", "tags": ["User"], "parameters": [{"in": "path", "name": "userId", "required": true, "schema": {"type": "string"}, "description": "User ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserParams"}}}}, "responses": {"200": {"description": "User updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "400": {"description": "Invalid input"}, "404": {"description": "User not found"}}}}}, "tags": [{"name": "Agents", "description": "AI agent management and availability endpoints"}, {"name": "DataAnalysis", "description": "Data analysis endpoints for sales, customer, and product insights"}, {"name": "Organization Admins", "description": "Organization admin management endpoints"}, {"name": "<PERSON><PERSON>", "description": "Authentication and authorization endpoints"}, {"name": "Campaign", "description": "Campaign management endpoints"}, {"name": "Client", "description": "Client-side interaction endpoints"}, {"name": "Segment", "description": "Segment event processing endpoints"}, {"name": "COA", "description": "Certificate of Analysis (COA) data import and retrieval endpoints"}, {"name": "Competitor", "description": "Competitor management endpoints"}, {"name": "API Documentation", "description": "API documentation and Swagger UI endpoints"}, {"name": "Dashboard", "description": "Dashboard and insights management endpoints"}, {"name": "Document", "description": "Document management and analysis endpoints"}, {"name": "Events", "description": "Event management endpoints for location-specific events"}, {"name": "Insights", "description": "Market and business insights endpoints"}, {"name": "Journeys", "description": "Customer journey automation endpoints"}, {"name": "Lists", "description": "List management endpoints"}, {"name": "Location Admins", "description": "Endpoints for managing location administrators"}, {"name": "Location API Keys", "description": "Endpoints for managing location API keys"}, {"name": "Location", "description": "Location management endpoints"}, {"name": "Location Locales", "description": "Endpoints for managing location locales"}, {"name": "Misc", "description": "Miscellaneous endpoints for various functionalities"}, {"name": "Order", "description": "Order management endpoints with dual authentication support.\n\n## Checkout Authentication\n\nThe checkout endpoint `/orders/checkout` supports two authentication modes:\n\n### API Key Only (Anonymous Orders)\n- **Header**: `Authorization: Bearer your_location_api_key`\n- **User Creation**: User will be created from email/phone in request body\n- **Best for**: Guest checkout, one-time orders\n\n### API Key + User Token (Linked Orders)\n- **Headers**:\n  - `Authorization: Bearer your_location_api_key`\n  - `Authorization: Bearer user_jwt_token` (in same header, user token will override API key for user identification)\n- **User Linking**: Order will be linked to authenticated user account\n- **Best for**: Registered users, order history tracking\n\n## Other Order Endpoints\n\nAll other order management endpoints require location role-based authentication (admin/staff access).\n"}, {"name": "Organization", "description": "Organization management endpoints"}, {"name": "POS Integration", "description": "Point of Sale (POS) system integration endpoints"}, {"name": "POS Analytics", "description": "Point of Sale (POS) data analytics and vector search endpoints"}, {"name": "POS", "description": "Point of Sale (POS) data management endpoints"}, {"name": "Product", "description": "Product management endpoints"}, {"name": "PublicProduct", "description": "Public endpoints for product data and chat with dual authentication support.\n\n## Authentication Overview\n\nAll endpoints require a location API key for access. User authentication is optional but provides enhanced features.\n\n### API Key Authentication (Required)\n- **Header**: `Authorization: Bearer your_location_api_key`\n- **Purpose**: Identifies the location/dispensary\n- **Required for**: All endpoints\n\n### User Authentication (Optional)\n- **Header**: `x-user-token: user_jwt_token`\n- **Purpose**: Links actions to user account\n- **Benefits**: Chat history persistence, order tracking, personalized recommendations\n\n## Usage Examples\n\n### Anonymous Access (API Key Only)\n```\ncurl -H \"Authorization: Bearer sk_live_abc123...\" /public/products/chat\n```\n\n### Authenticated Access (API Key + User Token)\n```\ncurl -H \"Authorization: Bearer sk_live_abc123...\" \\\n     -H \"x-user-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\" \\\n     /public/products/chat\n```\n\n## Feature Comparison\n\n| Feature | Anonymous | Authenticated |\n|---------|-----------|---------------|\n| Product search | ✅ | ✅ |\n| Product recommendations | ✅ | ✅ |\n| Chat responses | ✅ | ✅ |\n| Chat history | ❌ | ✅ |\n| Saved chats | ❌ | ✅ |\n| Order placement | ✅ | ✅ |\n| Order tracking | ❌ | ✅ |\n"}, {"name": "Profile", "description": "Admin profile management endpoints"}, {"name": "Links", "description": "Link tracking and redirection endpoints"}, {"name": "Resources", "description": "Resource management endpoints for fonts and snippets"}, {"name": "Templates", "description": "Template management endpoints for email, text, push, and webhook templates"}, {"name": "Retailer", "description": "Retailer management endpoints"}, {"name": "RetailerData", "description": "Retailer data management endpoints"}, {"name": "Review", "description": "Review data management endpoints"}, {"name": "Shared Assets", "description": "Endpoints for uploading and serving shared images/assets"}, {"name": "Images", "description": "Image management endpoints"}, {"name": "Subscriptions", "description": "Subscription management endpoints"}, {"name": "Supabase", "description": "Supabase integration endpoints"}, {"name": "Tags", "description": "Tag management endpoints"}, {"name": "Custom Prompts", "description": "Endpoints for managing custom prompts"}, {"name": "User", "description": "User management endpoints"}]}