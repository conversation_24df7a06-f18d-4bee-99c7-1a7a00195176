const swaggerJSDoc = require("swagger-jsdoc");
const fs = require("fs");
const path = require("path");

// Swagger definition - matching your current config
const swaggerDefinition = {
  openapi: "3.0.0",
  info: {
    title: "BakedBot API",
    version: "1.0.0",
    description: "API documentation for BakedBot platform",
  },
  servers: [
    {
      url: "/api",
      description: "API Server",
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
      },
    },
  },
  security: [
    {
      bearerAuth: [],
    },
  ],
};

// Options for the swagger docs
const options = {
  swaggerDefinition,
  // Update path to look in the src directory relative to this script
  apis: [path.join(__dirname, "../src/**/*.ts")],
};

// Generate swagger specification
const swaggerSpec = swaggerJSDoc(options);

// Ensure the public directory exists
const publicDir = path.join(__dirname, "../public");
if (!fs.existsSync(publicDir)) {
  fs.mkdirSync(publicDir, { recursive: true });
}

// Write the swagger.json file
fs.writeFileSync(
  path.join(publicDir, "swagger.json"),
  JSON.stringify(swaggerSpec, null, 2)
);

console.log("✨ swagger.json generated successfully!");
