#!/usr/bin/env node

/**
 * Standalone script to populate insight_patterns table with default data
 * Can be run independently: node scripts/populate_insight_patterns.js
 */

const path = require('path');
const knex = require('knex');

// Database configuration using environment variables
const config = {
  client: 'mysql2',
  connection: {
    host: process.env.DB_HOST || 'mysql',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
  },
};

async function populateInsightPatterns() {
  const db = knex(config);
  
  try {
    console.log('🔄 Connecting to database...');
    
    // Check if table exists
    const tableExists = await db.schema.hasTable('insight_patterns');
    if (!tableExists) {
      console.error('❌ Table "insight_patterns" does not exist. Please run migrations first.');
      process.exit(1);
    }
    
    console.log('📊 Populating insight_patterns table...');
    
    // Check if data already exists
    const existingCount = await db('insight_patterns').count('id as count').first();
    if (existingCount.count > 0) {
      console.log(`⚠️  Found ${existingCount.count} existing patterns. Clearing table...`);
      await db('insight_patterns').del();
    }

    // Default non-actionable patterns from insight-actionability-enhancement.md
    const patterns = [
      {
        pattern: 'implement.*analysis',
        description: 'Phrases about implementing analysis',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Implement Product Category Analysis', 'Implement customer analysis', 'Implement sales analysis'])
      },
      {
        pattern: 'develop.*strateg',
        description: 'Phrases about developing strategies',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Develop Age-Specific Marketing Strategies', 'Develop pricing strategy', 'Develop content strategy'])
      },
      {
        pattern: 'develop.*presence',
        description: 'Phrases about developing presence',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Develop a Social Media Presence', 'Develop online presence', 'Develop brand presence'])
      },
      {
        pattern: 'create.*framework',
        description: 'Phrases about creating frameworks',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Create Analytics Framework', 'Create testing framework', 'Create evaluation framework'])
      },
      {
        pattern: 'establish.*process',
        description: 'Phrases about establishing processes',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Establish Review Process', 'Establish approval process', 'Establish workflow process'])
      },
      {
        pattern: 'build.*system',
        description: 'Phrases about building systems',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Build Tracking System', 'Build monitoring system', 'Build reporting system'])
      },
      {
        pattern: 'design.*approach',
        description: 'Phrases about designing approaches',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Design Marketing Approach', 'Design customer approach', 'Design sales approach'])
      },
      {
        pattern: 'conduct.*research',
        description: 'Phrases about conducting research',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Conduct Market Research', 'Conduct customer research', 'Conduct competitive research'])
      },
      {
        pattern: 'perform.*audit',
        description: 'Phrases about performing audits',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Perform Security Audit', 'Perform compliance audit', 'Perform process audit'])
      },
      {
        pattern: 'review.*policies',
        description: 'Phrases about reviewing policies',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Review Privacy Policies', 'Review security policies', 'Review operational policies'])
      },
      {
        pattern: 'analyze.*trends',
        description: 'Phrases about analyzing trends',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Analyze Market Trends', 'Analyze customer trends', 'Analyze sales trends'])
      },
      {
        pattern: 'consider.*implementing',
        description: 'Phrases about considering implementation',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Consider implementing new features', 'Consider implementing changes', 'Consider implementing improvements'])
      },
      {
        pattern: 'should.*analyze',
        description: 'Phrases suggesting analysis should be done',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Should analyze customer behavior', 'Should analyze performance', 'Should analyze results'])
      },
      {
        pattern: 'need.*to.*study',
        description: 'Phrases about needing to study',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Need to study market conditions', 'Need to study customer preferences', 'Need to study competition'])
      },
      {
        pattern: 'recommend.*reviewing',
        description: 'Phrases recommending reviews',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Recommend reviewing current processes', 'Recommend reviewing strategies', 'Recommend reviewing policies'])
      },
      {
        pattern: 'develop a.*plan for',
        description: 'Phrases about developing a plan',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Develop a Marketing Plan for Future Campaigns'])
      },
      {
        pattern: 'prepare.*data',
        description: 'Phrases about preparing data',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['Prepare for a data collection'])
      },
      {
        pattern: 'implement a system',
        description: 'Phrases about implementing a system',
        type: 'non_actionable',
        is_active: true,
        priority: 10,
        examples: JSON.stringify(['To effectively analyze product performance, we need to implement a system for tracking sales data through POS analytics.'])
      }
    ];

    // Insert the patterns
    await db('insight_patterns').insert(patterns);
    
    console.log(`✅ Successfully seeded ${patterns.length} default insight patterns!`);
    console.log('');
    console.log('📋 Patterns added:');
    patterns.forEach((pattern, index) => {
      console.log(`   ${index + 1}. ${pattern.pattern} - ${pattern.description}`);
    });
  } catch (error) {
    console.error('❌ Error populating insight patterns:', error);
    process.exit(1);
  } finally {
    await db.destroy();
  }
}

// Run the script
if (require.main === module) {
  populateInsightPatterns()
    .then(() => {
      console.log('🎉 Script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { populateInsightPatterns };
