// This script identifies all router endpoints defined in controller files
const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// Get list of all controller files
const controllerPaths = execSync('find src -name "*Controller.ts"', {
  encoding: "utf8",
})
  .trim()
  .split("\n")
  .filter(Boolean);

const allEndpoints = [];

// Regex to match router method calls (e.g., router.get, router.post, etc.)
const routerMethodRegex =
  /router\.(get|post|put|delete|patch)\s*\(\s*["']([^"']+)["']/g;

controllerPaths.forEach((controllerPath) => {
  try {
    const content = fs.readFileSync(
      path.join(process.cwd(), controllerPath),
      "utf8"
    );

    // Extract the prefix from router definition if any
    let prefix = "";
    const prefixMatch = content.match(
      /new Router\s*\(\s*{[^}]*prefix\s*:\s*["']([^"']+)["']/
    );
    if (prefixMatch) {
      prefix = prefixMatch[1];
    }

    // Find all router method calls
    let match;
    while ((match = routerMethodRegex.exec(content)) !== null) {
      const method = match[1].toUpperCase();
      const route = match[2];

      // Combine prefix and route to get full path
      const fullPath = `${prefix}${route}`;

      // Check if the endpoint already has swagger documentation
      const startPosition = content.lastIndexOf("/**", match.index);
      const hasSwagger =
        startPosition > -1 &&
        content.substring(startPosition, match.index).includes("@swagger");

      allEndpoints.push({
        controller: controllerPath,
        method,
        route: fullPath,
        hasSwagger,
        lineNumber: content.substring(0, match.index).split("\n").length,
      });
    }
  } catch (error) {
    console.error(`Error processing ${controllerPath}:`, error.message);
  }
});

// Output results
console.log("Controllers to be documented:");
console.log("============================");

// Group by controller
const groupedByController = allEndpoints.reduce((acc, endpoint) => {
  if (!acc[endpoint.controller]) {
    acc[endpoint.controller] = [];
  }
  acc[endpoint.controller].push(endpoint);
  return acc;
}, {});

for (const [controller, endpoints] of Object.entries(groupedByController)) {
  console.log(`\n${controller}:`);

  endpoints.forEach((endpoint) => {
    const status = endpoint.hasSwagger ? "✅" : "❌";
    console.log(
      `  ${status} ${endpoint.method} ${endpoint.route} (line ${endpoint.lineNumber})`
    );
  });
}

// Summary
const totalEndpoints = allEndpoints.length;
const documentedEndpoints = allEndpoints.filter((e) => e.hasSwagger).length;
const undocumentedEndpoints = totalEndpoints - documentedEndpoints;

console.log("\nSummary:");
console.log("========");
console.log(`Total endpoints: ${totalEndpoints}`);
console.log(`Documented endpoints: ${documentedEndpoints}`);
console.log(`Undocumented endpoints: ${undocumentedEndpoints}`);
console.log(
  `Progress: ${Math.round((documentedEndpoints / totalEndpoints) * 100)}%`
);
