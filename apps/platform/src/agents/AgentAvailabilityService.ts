import { Knex } from "knex";
import { logger } from "../config/logger";
import App from "../app";
import * as agentsConfig from "./agents.json";
import { Document } from "../documents/Document";
import { DocumentAnalysisService } from "../documents/DocumentAnalysisService";
import { SupabaseService } from "../supabase/SupabaseService";

/**
 * @deprecated This service is being replaced by the simpler IntegrationService.getAvailableAgents()
 *
 * The enhanced IntegrationService provides the same functionality with:
 * - 90% less code complexity
 * - Faster performance (no complex Supabase routing)
 * - Better maintainability
 * - Consistent with actual chat functionality
 *
 * Please use: import { getAvailableAgents } from "../integrations/IntegrationService"
 *
 * This file will be removed in a future version.
 */

interface AgentDataRequirement {
  description: string;
  min_records?: number;
  tables?: string[];
  columns?: string[];
  vector_service?: string;
  status?: string;
}

interface DocumentRequirement {
  types: string[];
  min_documents: number;
}

// Define an interface for Agent from the agents.json file
interface AgentConfig {
  name: string;
  role: string;
  description: string;
  icon: string;
  systemPrompt?: string;
  capabilities: string[];
  disabled?: boolean;
  comingSoon?: boolean;
  requirements?: {
    required: string[];
    optional?: string[];
  };
  data_requirements?: {
    [key: string]: AgentDataRequirement | DocumentRequirement;
  };
  promptTemplates?: {
    [key: string]: {
      template: string;
      required_data: string[];
    };
  };
}

interface AgentsConfig {
  version: string;
  agents: {
    [key: string]: AgentConfig;
  };
}

interface AgentAvailabilityResult {
  agentId: string;
  name: string;
  isAvailable: boolean;
  missingRequirements: string[];
  partiallyAvailable: boolean;
  availabilityPercentage: number;
  dataStatus: {
    [key: string]: {
      available: boolean;
      current_records?: number;
      min_records?: number;
      missing_columns?: string[];
    };
  };
}

export class AgentAvailabilityService {
  /**
   * Check availability of all agents for a specific location
   */
  public static async getAvailableAgents(
    locationId: number
  ): Promise<AgentAvailabilityResult[]> {
    const results: AgentAvailabilityResult[] = [];
    const agents = (agentsConfig as AgentsConfig).agents;

    for (const [agentId, agent] of Object.entries(agents)) {
      // Skip agents marked as disabled or coming soon
      if (agent.disabled || agent.comingSoon) {
        continue;
      }

      // Check availability for this agent
      const availability = await this.checkAgentAvailability(
        agentId,
        locationId,
        agent
      );
      results.push(availability);
    }

    // Log summary of agent availability
    const availableAgents = results
      .filter((r) => r.isAvailable)
      .map((r) => r.name);
    const partialAgents = results
      .filter((r) => !r.isAvailable && r.partiallyAvailable)
      .map((r) => r.name);

    logger.info(
      {
        location_id: locationId,
        available_agents: availableAgents,
        partial_agents: partialAgents,
        unavailable_count:
          results.length - availableAgents.length - partialAgents.length,
      },
      "Agent availability determined"
    );

    return results;
  }

  /**
   * Check if a specific agent is available for a location
   */
  public static async checkAgentAvailability(
    agentId: string,
    locationId: number,
    agent: AgentConfig
  ): Promise<AgentAvailabilityResult> {
    if (!agent.data_requirements) {
      // If no specific data requirements, the agent is available
      return {
        agentId,
        name: agent.name,
        isAvailable: true,
        missingRequirements: [],
        partiallyAvailable: true,
        availabilityPercentage: 100,
        dataStatus: {},
      };
    }

    const dataRequirements = agent.data_requirements;
    const missingRequirements: string[] = [];
    const dataStatus: any = {};
    let requirementsMet = 0;
    let totalRequirements = 0;

    // Process each data requirement for this agent
    for (const [key, requirement] of Object.entries(dataRequirements)) {
      if (key === "documents") {
        // Handle document requirements separately
        const docRequirement = requirement as DocumentRequirement;
        const docStatus = await this.checkDocumentRequirements(
          locationId,
          docRequirement
        );

        dataStatus.documents = docStatus;

        if (!docStatus.available) {
          missingRequirements.push(
            `Missing required documents: ${docRequirement.types.join(", ")}`
          );
        } else {
          requirementsMet++;
        }
        totalRequirements++;
        continue;
      }

      // Skip requirements marked as coming soon
      const dataReq = requirement as AgentDataRequirement;
      if (dataReq.status === "coming_soon") {
        continue;
      }

      // Check the data requirement
      const status = await this.checkDataRequirement(
        locationId,
        key,
        dataReq,
        agentId
      );
      dataStatus[key] = status;

      if (!status.available) {
        const message = `Missing ${dataReq.description}: found ${
          status.current_records || 0
        }/${dataReq.min_records || 0} records`;
        if (status.missing_columns && status.missing_columns.length > 0) {
          missingRequirements.push(
            `${message} (missing columns: ${status.missing_columns.join(", ")})`
          );
        } else {
          missingRequirements.push(message);
        }
      } else {
        requirementsMet++;
      }

      totalRequirements++;
    }

    // Calculate availability percentage
    const availabilityPercentage =
      totalRequirements > 0
        ? Math.round((requirementsMet / totalRequirements) * 100)
        : 100;

    return {
      agentId,
      name: agent.name,
      isAvailable: missingRequirements.length === 0,
      missingRequirements,
      partiallyAvailable: requirementsMet > 0,
      availabilityPercentage,
      dataStatus,
    };
  }

  /**
   * Check if a specific data requirement is satisfied
   */
  private static async checkDataRequirement(
    locationId: number,
    requirementKey: string,
    requirement: AgentDataRequirement,
    agentId: string
  ): Promise<{
    available: boolean;
    current_records?: number;
    min_records?: number;
    missing_columns?: string[];
  }> {
    // Default to no records for safety
    let totalRequirementRecordCount = 0;
    const allMissingColumnsForRequirement: string[] = [];

    if (!requirement.tables || requirement.tables.length === 0) {
      return {
        available: false,
        current_records: 0,
        min_records: requirement.min_records,
      };
    }

    const supabaseService = AgentAvailabilityService.getSupabaseService();
    const db = App.main.db;

    for (const tableNameFromReq of requirement.tables) {
      let currentTableRecords = 0;
      const missingColsForThisTable: string[] = [];
      let isSupabasePath = false;

      // === Start of Supabase specific routing for Agent EZAL ('4') ===
      if (agentId === "4") {
        try {
          if (
            requirementKey === "retailer_data" &&
            tableNameFromReq === "retailers"
          ) {
            isSupabasePath = true;
            const competitorsInLocation = await db("location_competitors")
              .where({ location_id: locationId })
              .select("competitor_place_id");

            let foundSupabaseRetailers = 0;
            if (competitorsInLocation.length > 0) {
              for (const comp of competitorsInLocation) {
                const retailer = await supabaseService.getRetailerById(
                  comp.competitor_place_id
                );
                if (retailer) {
                  foundSupabaseRetailers++;
                  if (requirement.columns) {
                    for (const col of requirement.columns) {
                      // Basic check for top-level properties
                      if (!(col in retailer) || retailer[col] == null) {
                        if (!missingColsForThisTable.includes(col)) {
                          missingColsForThisTable.push(col);
                        }
                      }
                    }
                  }
                }
              }
            }
            currentTableRecords = foundSupabaseRetailers;
          } else if (
            requirementKey === "product_data" &&
            tableNameFromReq === "products"
          ) {
            isSupabasePath = true;
            const competitorsInLocation = await db("location_competitors")
              .where({ location_id: locationId })
              .select("competitor_place_id");

            let totalSupabaseProducts = 0;
            for (const comp of competitorsInLocation) {
              const spResponse = await supabaseService.getRetailerProducts(
                comp.competitor_place_id
              );
              if (spResponse && spResponse.products) {
                totalSupabaseProducts += spResponse.products.length;
                if (requirement.columns && spResponse.products.length > 0) {
                  const sampleProduct = spResponse.products[0];
                  for (const col of requirement.columns) {
                    if (!(col in sampleProduct) || sampleProduct[col] == null) {
                      if (!missingColsForThisTable.includes(col)) {
                        missingColsForThisTable.push(col);
                      }
                    }
                  }
                }
              }
            }
            currentTableRecords = totalSupabaseProducts;
          } else if (
            requirementKey === "review_data" &&
            tableNameFromReq === "reviews"
          ) {
            isSupabasePath = true;
            logger.warn(
              `Supabase 'reviews' check for agent ${agentId} (requirement ${requirementKey}) is not implemented due to unknown Supabase schema/methods. Reporting 0 records.`
            );
            currentTableRecords = 0; // Placeholder
            if (requirement.columns) {
              // Mark all required columns as missing
              for (const col of requirement.columns) {
                if (!missingColsForThisTable.includes(col)) {
                  missingColsForThisTable.push(col);
                }
              }
            }
          } else if (
            requirementKey === "competitor_data" &&
            tableNameFromReq === "competitor_prices" // Conceptual table name
          ) {
            isSupabasePath = true;
            const competitorsInLocation = await db("location_competitors")
              .where({ location_id: locationId })
              .select("competitor_place_id");

            let totalSupabaseProducts = 0;
            for (const comp of competitorsInLocation) {
              const spResponse = await supabaseService.getRetailerProducts(
                comp.competitor_place_id
              );
              if (spResponse && spResponse.products) {
                totalSupabaseProducts += spResponse.products.length;
                if (requirement.columns && spResponse.products.length > 0) {
                  const sampleProduct = spResponse.products[0];
                  for (const col of requirement.columns) {
                    if (!(col in sampleProduct) || sampleProduct[col] == null) {
                      if (!missingColsForThisTable.includes(col)) {
                        missingColsForThisTable.push(col);
                      }
                    }
                  }
                }
              }
            }
            currentTableRecords = totalSupabaseProducts;
          }
        } catch (error) {
          logger.error(
            `Error processing Supabase requirement for agent ${agentId}, key ${requirementKey}, table ${tableNameFromReq}: ${error}`
          );
          currentTableRecords = 0; // Error means 0 records for this Supabase part
          // If columns were required, mark them as missing.
          if (requirement.columns) {
            requirement.columns.forEach((col) => {
              if (!missingColsForThisTable.includes(col)) {
                missingColsForThisTable.push(col);
              }
            });
          }
        }
      }
      // === End of Supabase specific routing ===

      if (isSupabasePath) {
        totalRequirementRecordCount += currentTableRecords;
        missingColsForThisTable.forEach((mc) => {
          if (!allMissingColumnsForRequirement.includes(mc)) {
            allMissingColumnsForRequirement.push(mc);
          }
        });
      } else {
        // === Original MySQL Path ===
        const table = tableNameFromReq; // Use the table name from the loop for MySQL

        // Check if table exists before querying (MySQL specific)
        const tableExists = await this.tableExists(table);
        if (!tableExists) {
          logger.warn(
            `Table ${table} doesn't exist, skipping requirement check for ${requirementKey}`
          );
          if (requirement.columns) {
            requirement.columns.forEach((rc) => {
              if (!allMissingColumnsForRequirement.includes(rc)) {
                allMissingColumnsForRequirement.push(rc);
              }
            });
          }
          continue; // to next table in requirement.tables
        }

        let query = db(table);

        // Handle tables that don't have location_id but are related to location through other tables
        const globalTables = ["reviews", "retailers"];
        const tableRelationships: Record<
          string,
          {
            joinTable: string;
            localKey: string;
            foreignKey: string;
            locationColumn: string;
          }
        > = {
          campaign_sends: {
            joinTable: "campaigns",
            localKey: "campaign_id",
            foreignKey: "id",
            locationColumn: "location_id",
          },
        };

        if (globalTables.includes(table)) {
          // No location filtering for global tables
        } else if (tableRelationships[table]) {
          // Handle tables that need to be joined to get location_id
          const rel = tableRelationships[table];
          query = query
            .join(
              rel.joinTable,
              `${table}.${rel.localKey}`,
              `${rel.joinTable}.${rel.foreignKey}`
            )
            .where(`${rel.joinTable}.${rel.locationColumn}`, locationId);
        } else {
          // Default case: table has location_id column
          query = query.where({ location_id: locationId });
        }

        const tempMissingColsForMySqlTable: string[] = [];
        if (requirement.columns && requirement.columns.length > 0) {
          const columnInfo = await db(table).columnInfo();
          for (const column of requirement.columns) {
            if (column.includes(".")) {
              const [jsonField, nestedField] = column.split(".");
              if (!columnInfo[jsonField]) {
                tempMissingColsForMySqlTable.push(column);
              } else {
                try {
                  if (table === "users" && jsonField === "data") {
                    query = query.whereNotNull(jsonField);
                  } else {
                    query = query.whereRaw(
                      `JSON_EXTRACT(??, '$."${nestedField}"') IS NOT NULL`,
                      [jsonField]
                    );
                  }
                } catch (jsonError) {
                  logger.warn(
                    `Error setting up JSON field check for ${column}: ${jsonError}`
                  );
                  query = query.whereNotNull(jsonField);
                }
              }
            } else if (!columnInfo[column]) {
              tempMissingColsForMySqlTable.push(column);
            } else {
              query = query.whereNotNull(column);
            }
          }
        }

        // Use table-specific id column to avoid ambiguity when there are joins
        const countColumn = tableRelationships[table] ? `${table}.id` : "id";
        const result = await query.count(`${countColumn} as count`).first();
        const mySqlTableCount = result ? parseInt(result.count as string) : 0;

        totalRequirementRecordCount += mySqlTableCount;
        tempMissingColsForMySqlTable.forEach((mc) => {
          if (!allMissingColumnsForRequirement.includes(mc)) {
            allMissingColumnsForRequirement.push(mc);
          }
        });
      }
    } // End for (const tableNameFromReq of requirement.tables)

    const minRecordsForRequirement = requirement.min_records || 1;
    const meetsMinRecords =
      totalRequirementRecordCount >= minRecordsForRequirement;
    const hasNoMissingColumns = allMissingColumnsForRequirement.length === 0;

    return {
      available: meetsMinRecords && hasNoMissingColumns,
      current_records: totalRequirementRecordCount,
      min_records: minRecordsForRequirement,
      missing_columns:
        allMissingColumnsForRequirement.length > 0
          ? allMissingColumnsForRequirement
          : undefined,
    };
  }

  /**
   * Check if a table exists in the database
   */
  private static async tableExists(tableName: string): Promise<boolean> {
    try {
      const db = App.main.db;

      // Different approach based on database type
      // For MySQL/MariaDB
      const result = await db.raw(
        `
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = ?
      `,
        [tableName]
      );

      return result[0][0].count > 0;
    } catch (error) {
      logger.error(`Error checking if table ${tableName} exists:`, error);
      return false;
    }
  }

  /**
   * Check if document requirements are satisfied
   */
  private static async checkDocumentRequirements(
    locationId: number,
    requirement: DocumentRequirement
  ): Promise<{
    available: boolean;
    current_documents: number;
    min_documents: number;
    missing_types: string[];
  }> {
    try {
      // Get documents for this location
      const documents = await Document.query().where({
        location_id: locationId,
        status: "completed",
      });

      // If no documents found
      if (!documents || documents.length === 0) {
        return {
          available: false,
          current_documents: 0,
          min_documents: requirement.min_documents,
          missing_types: [...requirement.types],
        };
      }

      // Check document types based on analysis
      const documentTypes = new Set<string>();
      const missingTypes = [...requirement.types];

      for (const doc of documents) {
        if (doc.data?.analysis?.agent_contributions) {
          // Use document analysis to determine type
          const analysis = doc.data.analysis;

          // Each document can satisfy multiple types based on its content
          for (const type of requirement.types) {
            // Check if document content suggests it matches this type
            const matchesType = this.documentMatchesType(doc, type);

            if (matchesType) {
              documentTypes.add(type);
              // Remove from missing types
              const typeIndex = missingTypes.indexOf(type);
              if (typeIndex !== -1) {
                missingTypes.splice(typeIndex, 1);
              }
            }
          }
        }
      }

      // Requirement is available if:
      // 1. We have enough documents overall
      // 2. We have at least one of each required type (if specified)
      const hasEnoughDocuments = documents.length >= requirement.min_documents;
      const hasRequiredTypes = missingTypes.length === 0;

      return {
        available: hasEnoughDocuments && hasRequiredTypes,
        current_documents: documents.length,
        min_documents: requirement.min_documents,
        missing_types: missingTypes,
      };
    } catch (error) {
      logger.error(
        {
          error,
          location_id: locationId,
        },
        "Error checking document requirements"
      );

      return {
        available: false,
        current_documents: 0,
        min_documents: requirement.min_documents,
        missing_types: [...requirement.types],
      };
    }
  }

  /**
   * Determine if a document matches a required type based on analysis
   */
  private static documentMatchesType(document: any, type: string): boolean {
    if (!document.data?.analysis?.agent_contributions) {
      return false;
    }

    // Map document types to relevant agents and keywords
    const typeToKeywords: Record<string, string[]> = {
      product_info: ["product", "strain", "thc", "cbd", "cannabis"],
      educational_content: ["education", "learn", "guide", "tutorial"],
      campaign_results: ["campaign", "results", "performance", "marketing"],
      marketing_materials: ["marketing", "promotion", "advertisement"],
      financial_reports: ["financial", "report", "revenue", "profit"],
      performance_metrics: ["performance", "metrics", "kpi", "dashboard"],
      market_research: ["market", "research", "analysis", "trend"],
      competitor_analysis: ["competitor", "competition", "compare"],
      financial_statements: ["statement", "balance", "income", "cash flow"],
      budget_reports: ["budget", "allocation", "spending", "projection"],
      forecasts: ["forecast", "projection", "predict", "future"],
      customer_feedback: ["feedback", "survey", "review", "satisfaction"],
      loyalty_programs: ["loyalty", "reward", "program", "point"],
      compliance_reports: ["compliance", "regulation", "regulatory", "audit"],
      regulatory_documents: ["regulation", "license", "legal", "requirement"],
      lab_results: ["lab", "test", "result", "analysis", "potency"],
      cultivation_logs: ["cultivation", "grow", "plant", "harvest"],
      batch_records: ["batch", "record", "tracking", "processing"],
      shipping_manifests: ["shipping", "manifest", "transport", "transfer"],
      inventory_reports: ["inventory", "stock", "level", "count"],
      purchase_orders: ["purchase", "order", "buy", "vendor"],
      vendor_contracts: ["vendor", "contract", "agreement", "supplier"],
    };

    // Check if document content contains relevant keywords for this type
    const keywords = typeToKeywords[type] || [];

    if (keywords.length === 0) {
      return false; // Unknown type
    }

    // Check document content and metadata for keywords
    const documentText = JSON.stringify(document.data).toLowerCase();
    return keywords.some((keyword) =>
      documentText.includes(keyword.toLowerCase())
    );
  }

  /**
   * Helper to get SupabaseService instance
   */
  private static getSupabaseService(): SupabaseService {
    // Consistent with CompetitorAnalysisService instantiation
    return new SupabaseService({
      url: process.env.SUPABASE_URL || "",
      key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
      bucket: process.env.SUPABASE_BUCKET || "location-data",
    });
  }
}
