import { Context, Next } from "koa";
import { AgentAvailabilityService } from "./AgentAvailabilityService";
import { logger } from "../config/logger";

/**
 * Middleware factory that creates a middleware function to check if a specific
 * agent capability is available for the current location
 */
export function requireAgentCapability(capability: string) {
  return async (ctx: Context, next: Next) => {
    try {
      // Get the location ID from context (assumes auth middleware has run)
      const locationId = ctx.state.location?.id;

      if (!locationId) {
        logger.warn(
          "No location ID found in context when checking agent capability"
        );
        ctx.status = 401;
        ctx.body = { error: "Authentication required" };
        return;
      }

      // Get all agents for this location
      const availableAgents = await AgentAvailabilityService.getAvailableAgents(
        locationId
      );

      // Check if any available agent has this capability
      const hasCapability = availableAgents.some((agent) => {
        return (
          agent.isAvailable &&
          agent.agentId &&
          // Check in the config for the capabilities
          // This assumes the agent config has been updated with the capability string
          hasAgentCapability(agent.agentId, capability)
        );
      });

      if (!hasCapability) {
        logger.warn({
          location_id: locationId,
          capability,
          message: "Location does not have required agent capability",
        });

        ctx.status = 403;
        ctx.body = {
          error: "Required capability not available",
          capability,
          message: `This feature requires ${capability} capability, which is not available for your location. Please upload the necessary data first.`,
        };
        return;
      }

      // If we get here, the location has the required capability
      await next();
    } catch (error) {
      logger.error({
        error,
        capability,
        message: "Error checking agent capability",
      });

      ctx.status = 500;
      ctx.body = {
        error: "Error checking capabilities",
        message: error instanceof Error ? error.message : "Unknown error",
      };
    }
  };
}

/**
 * Helper function to check if an agent has a specific capability
 */
function hasAgentCapability(agentId: string, capability: string): boolean {
  try {
    // Dynamic import to avoid circular dependencies
    const agentsConfig = require("./agents.json");
    const agent = agentsConfig.agents[agentId];

    if (!agent) return false;

    return agent.capabilities.some(
      (cap: string) =>
        cap.toLowerCase() === capability.toLowerCase() ||
        cap.toLowerCase().includes(capability.toLowerCase())
    );
  } catch (error) {
    logger.error({
      error,
      message: "Error checking agent capability",
      agent_id: agentId,
      capability,
    });
    return false;
  }
}
