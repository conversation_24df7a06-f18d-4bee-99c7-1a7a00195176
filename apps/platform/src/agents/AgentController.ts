import Router from "@koa/router";
import { locationRoleMiddleware } from "../locations/LocationService";
import { getAvailableAgents } from "../integrations/IntegrationService";
import App from "../app";
import { logger } from "../config/logger";
import * as agentsConfig from "./agents.json";

/**
 * @swagger
 * components:
 *   schemas:
 *     Agent:
 *       type: object
 *       required: [id, name, role, description, icon, capabilities]
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 *         role:
 *           type: string
 *         description:
 *           type: string
 *         icon:
 *           type: string
 *         capabilities:
 *           type: array
 *           items:
 *             type: string
 *     AgentAvailability:
 *       type: object
 *       required: [id, unlocked, missingRequirements]
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 *         unlocked:
 *           type: boolean
 *         missingRequirements:
 *           type: array
 *           items:
 *             type: string
 *         metadata:
 *           type: object
 *           properties:
 *             recordCounts:
 *               type: object
 *               additionalProperties:
 *                 type: integer
 *     AgentAvailabilityReport:
 *       type: object
 *       properties:
 *         summary:
 *           type: object
 *           properties:
 *             total:
 *               type: integer
 *             available:
 *               type: integer
 *             unavailable:
 *               type: integer
 *         available:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/AgentAvailability'
 *         unavailable:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/AgentAvailability'
 *         locationId:
 *           type: integer
 *     AgentRequirements:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *         name:
 *           type: string
 *         requirements:
 *           type: object
 *           properties:
 *             required:
 *               type: array
 *               items:
 *                 type: string
 */

/**
 * @swagger
 * tags:
 *   name: Agents
 *   description: AI agent management and availability endpoints
 */

const router = new Router({
  prefix: "/agents",
});

/**
 * @swagger
 * /agents/definitions:
 *   get:
 *     summary: Get all agent definitions
 *     tags: [Agents]
 *     responses:
 *       200:
 *         description: List of agent definitions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 agents:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Agent'
 *                 total:
 *                   type: integer
 *       500:
 *         description: Error retrieving agent definitions
 */
router.get("/definitions", locationRoleMiddleware("support"), async (ctx) => {
  try {
    // Filter out disabled agents
    const agents = Object.entries(agentsConfig.agents)
      .filter(([_, agent]) => !(agent as any).disabled)
      .map(([id, agent]) => ({
        id,
        name: agent.name,
        role: agent.role,
        description: agent.description,
        icon: agent.icon,
        capabilities: agent.capabilities,
      }));

    ctx.body = {
      agents,
      total: agents.length,
    };
  } catch (error) {
    logger.error("Error getting agent definitions:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Error retrieving agent definitions",
      message: error instanceof Error ? error.message : "Unknown error",
    };
  }
});

/**
 * @swagger
 * /agents/available:
 *   get:
 *     summary: Get available agents for a location
 *     tags: [Agents]
 *     responses:
 *       200:
 *         description: List of available agents
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 agents:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/AgentAvailability'
 *                 total:
 *                   type: integer
 *                 locationId:
 *                   type: integer
 *       500:
 *         description: Error retrieving available agents
 */
router.get("/available", locationRoleMiddleware("support"), async (ctx) => {
  try {
    const locationId = ctx.state.location.id;

    if (!App.main.db) {
      throw new Error("Database is not initialized");
    }

    // Use the unified approach from IntegrationService
    const agentResults = await getAvailableAgents(
      App.main.db,
      locationId.toString()
    );

    // Filter to only unlocked agents for the simple endpoint
    const availableAgents = agentResults.filter((agent) => agent.unlocked);

    ctx.body = {
      agents: availableAgents,
      total: availableAgents.length,
      locationId,
    };
  } catch (error) {
    logger.error("Error getting available agents:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Error retrieving available agents",
      message: error instanceof Error ? error.message : "Unknown error",
    };
  }
});

/**
 * @swagger
 * /agents/availability:
 *   get:
 *     summary: Get detailed agent availability report
 *     tags: [Agents]
 *     responses:
 *       200:
 *         description: Agent availability report
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentAvailabilityReport'
 *       500:
 *         description: Error retrieving agent availability report
 */
router.get("/availability", locationRoleMiddleware("admin"), async (ctx) => {
  try {
    const locationId = ctx.state.location.id;

    if (!App.main.db) {
      throw new Error("Database is not initialized");
    }

    // Use the unified approach from IntegrationService
    const allAgents = await getAvailableAgents(
      App.main.db,
      locationId.toString()
    );

    // Group by availability status
    const available = allAgents.filter((a) => a.unlocked);
    const unavailable = allAgents.filter((a) => !a.unlocked);

    ctx.body = {
      summary: {
        total: allAgents.length,
        available: available.length,
        unavailable: unavailable.length,
      },
      available,
      unavailable,
      locationId,
    };
  } catch (error) {
    logger.error("Error getting agent availability report:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Error retrieving agent availability report",
      message: error instanceof Error ? error.message : "Unknown error",
    };
  }
});

/**
 * @swagger
 * /agents/{id}:
 *   get:
 *     summary: Get single agent details and availability
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       200:
 *         description: Agent details and availability
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentAvailability'
 *       404:
 *         description: Agent not found
 *       500:
 *         description: Error retrieving agent details
 */
router.get("/:id", locationRoleMiddleware("support"), async (ctx) => {
  try {
    const agentId = ctx.params.id;
    const locationId = ctx.state.location.id;

    // Get agent definition
    const agentConfig =
      agentsConfig.agents[agentId as keyof typeof agentsConfig.agents];
    if (!agentConfig) {
      ctx.status = 404;
      ctx.body = { error: "Agent not found" };
      return;
    }

    if (!App.main.db) {
      throw new Error("Database is not initialized");
    }

    // Get availability for this specific agent
    const allAgents = await getAvailableAgents(
      App.main.db,
      locationId.toString()
    );
    const agentAvailability = allAgents.find((a) => a.id === agentId);

    if (!agentAvailability) {
      ctx.status = 404;
      ctx.body = { error: "Agent availability not found" };
      return;
    }

    ctx.body = agentAvailability;
  } catch (error) {
    logger.error("Error getting agent details:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Error retrieving agent details",
      message: error instanceof Error ? error.message : "Unknown error",
    };
  }
});

/**
 * @swagger
 * /agents/{id}/requirements:
 *   get:
 *     summary: Get data requirements for a specific agent
 *     tags: [Agents]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Agent ID
 *     responses:
 *       200:
 *         description: Agent data requirements
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentRequirements'
 *       404:
 *         description: Agent not found
 *       500:
 *         description: Error retrieving agent requirements
 */
router.get(
  "/:id/requirements",
  locationRoleMiddleware("support"),
  async (ctx) => {
    try {
      const agentId = ctx.params.id;

      // Get agent definition
      const agentConfig =
        agentsConfig.agents[agentId as keyof typeof agentsConfig.agents];
      if (!agentConfig) {
        ctx.status = 404;
        ctx.body = { error: "Agent not found" };
        return;
      }

      ctx.body = {
        id: agentId,
        name: agentConfig.name,
        requirements: agentConfig.requirements || { required: [] },
      };
    } catch (error) {
      logger.error("Error getting agent requirements:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Error retrieving agent requirements",
        message: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
);

export default router;
