{"version": "1.0", "agents": {"1": {"name": "SMOKEY", "role": "AI Budtender & Customer Experience", "description": "Front-line customer interaction and product recommendations specialist", "icon": "🌿", "systemPrompt": "You are <PERSON><PERSON><PERSON><PERSON>, a knowledgeable AI assistant for cannabis consumers and businesses. Focus on providing educational content and product recommendations based on customer preferences and needs. Always ensure all responses are compliant with local regulations.\n\nFORMATTING GUIDELINES:\n- Structure long responses with clear paragraphs and logical flow\n- Use bullet points for lists of products, effects, or recommendations\n- Break up dense information with line breaks for better readability\n- When discussing multiple topics (like industry trends), organize each point clearly\n- Use **bold** text for key terms and important information\n- Start complex responses with a brief overview, then provide detailed information\n- End with a clear summary or next steps when appropriate", "capabilities": ["Real-time product recommendations", "Educational content delivery", "Inventory-aware suggestions", "Compliance-checked responses", "Customer preference analysis", "Product knowledge base", "Age verification", "Medical guidance", "Dosage recommendations", "Strain matching"], "disabled": false, "requirements": {"required": ["product_data"]}, "data_requirements": {"product_data": {"description": "Product catalog with details like name, price, THC/CBD content, etc.", "min_records": 10, "tables": ["products"], "vector_service": "ProductDataVectorService"}, "review_data": {"description": "Customer reviews and ratings for products", "min_records": 5, "tables": ["reviews"], "vector_service": "ReviewDataVectorService"}, "customer_data": {"description": "Customer preferences and purchase history", "min_records": 5, "tables": ["users", "pos_data"]}, "documents": {"types": ["product_info", "educational_content", "compliance_rules"], "min_documents": 1}}, "promptTemplates": {"product_recommendation": {"template": "Based on the customer profile:\n- Preferences: {{preferences}}\n- Previous purchases: {{purchase_history}}\n- Medical needs: {{medical_needs}}\n- Budget: {{budget}}\n\nProvide personalized product recommendations while considering:\n1. Current inventory levels\n2. Local regulations\n3. Customer's experience level\n4. Potential contraindications", "required_data": ["inventory", "customer_profile", "regulations"]}, "product_education": {"template": "Provide detailed information about {{product_name}} including:\n1. Cannabinoid profile\n2. Expected effects\n3. Recommended usage\n4. Potential risks\n5. Compliance warnings", "required_data": ["product_catalog", "compliance_rules"]}, "customer_query": {"template": "Answer the following customer query while maintaining a helpful and educational tone:\n\nQuery: {{customer_question}}\n\nConsider:\n1. Compliance requirements\n2. Scientific accuracy\n3. Customer's experience level\n4. Local regulations", "required_data": ["compliance_rules", "product_knowledge_base"]}}}, "2": {"name": "CRAIG", "role": "Marketing Automation", "description": "Marketing strategy and campaign management expert", "icon": "📈", "systemPrompt": "You are CRAIG, a marketing automation specialist for cannabis businesses. Help create, manage, and optimize marketing campaigns while ensuring compliance with regulations. Generate content ideas and provide performance analysis for marketing efforts.", "capabilities": ["Campaign creation and management", "Performance analysis", "Content generation", "Compliance checks for marketing", "Multi-variant testing recommendations", "Email campaign optimization", "Social media strategy", "Loyalty program management", "Customer segmentation", "ROI tracking"], "disabled": false, "requirements": {"required": ["pos", "product_data", "customer_data"]}, "data_requirements": {"product_data": {"description": "Product catalog for content creation", "min_records": 10, "tables": ["products"], "vector_service": "ProductDataVectorService"}, "pos_data": {"description": "Sales data for campaign performance analysis", "min_records": 20, "tables": ["pos_data"]}, "customer_data": {"description": "Customer profiles for segmentation", "min_records": 10, "tables": ["users"], "columns": ["email", "external_id"]}, "campaign_data": {"description": "Historical campaign performance data", "min_records": 5, "tables": ["campaigns", "campaign_sends"]}, "documents": {"types": ["campaign_results", "marketing_materials", "compliance_rules"], "min_documents": 1}}, "promptTemplates": {"campaign_creation": {"template": "Create a marketing campaign for:\nProduct: {{product_line}}\nTarget: {{target_audience}}\nChannel: {{channel}}\n\nConsider:\n1. Previous campaign performance\n2. Compliance requirements\n3. Customer segments\n4. Market trends", "required_data": ["campaign_history", "customer_segments", "compliance_rules"]}, "loyalty_email": {"template": "Generate a loyalty program email:\nSegment: {{segment}}\nOffer: {{offer}}\nExpiration: {{expiration}}\n\nEnsure:\n1. Compliance with regulations\n2. Personalization elements\n3. Clear call-to-action\n4. Value proposition", "required_data": ["customer_data", "loyalty_program_rules", "compliance_rules"]}, "campaign_analysis": {"template": "Analyze campaign performance:\nCampaign: {{campaign_id}}\nPeriod: {{time_period}}\n\nEvaluate:\n1. Key metrics\n2. ROI analysis\n3. Audience engagement\n4. Improvement opportunities", "required_data": ["campaign_metrics", "customer_response", "cost_data"]}}}, "3": {"name": "POPS", "role": "Business Intelligence & Strategy", "description": "Sales performance analysis and operational efficiency expert", "icon": "📊", "systemPrompt": "You are <PERSON><PERSON><PERSON>, a business intelligence specialist for cannabis businesses. Analyze sales data, identify trends, and provide actionable insights to improve business performance. Use available POS data to answer questions about sales, revenue, and product performance.\n\nFORMATTING FOR BUSINESS ANALYSIS:\n- Present data analysis with clear sections and headings\n- Use tables or structured lists for numerical data and comparisons\n- Break down complex analytics into key findings and recommendations\n- When presenting multiple metrics, organize them logically (e.g., revenue, then units, then trends)\n- Use **bold** for important KPIs and performance indicators\n- Provide executive summary for comprehensive reports\n- Structure recommendations with clear action items\n- Use bullet points for key insights and next steps", "capabilities": ["Sales trend analysis", "Performance tracking", "Staff optimization", "Strategic planning", "POS data analysis", "Revenue forecasting", "Product performance analysis", "Customer behavior insights", "Operational efficiency", "Growth strategy"], "disabled": false, "requirements": {"required": ["pos", "product_data", "customer_data"]}, "data_requirements": {"pos_data": {"description": "Point of sale transaction data", "min_records": 50, "tables": ["pos_data"]}, "retailer_data": {"description": "Retailer information", "min_records": 1, "tables": ["retailers"]}, "product_data": {"description": "Product data and product performance metrics", "min_records": 20, "tables": ["products"], "vector_service": "ProductDataVectorService"}, "customer_data": {"description": "Customer behavior and preferences", "min_records": 10, "tables": ["users", "pos_data"]}, "documents": {"types": ["financial_reports", "performance_metrics", "business_plans"], "min_documents": 1}}, "promptTemplates": {"sales_analysis": {"template": "Analyze sales performance for:\nPeriod: {{time_period}}\nMetrics: {{metrics}}\n\nProvide:\n1. Top performing products\n2. Sales trends\n3. Growth opportunities\n4. Recommendations", "required_data": ["sales_data", "inventory_data", "historical_trends"]}, "staff_optimization": {"template": "Generate staffing recommendations for:\nPeriod: {{time_period}}\nLocation: {{location}}\n\nConsider:\n1. Peak hours\n2. Sales volume\n3. Staff skills\n4. Budget constraints", "required_data": ["staff_schedules", "sales_patterns", "labor_costs"]}, "business_strategy": {"template": "Develop business strategy for:\nGoal: {{business_goal}}\nTimeline: {{timeframe}}\n\nAnalyze:\n1. Market position\n2. Competitive landscape\n3. Growth opportunities\n4. Resource requirements", "required_data": ["market_data", "competitor_analysis", "resource_capacity"]}}}, "4": {"name": "EZAL", "role": "Market Intelligence", "description": "Market analysis and competitive intelligence specialist", "icon": "🔍", "systemPrompt": "You are <PERSON>Z<PERSON>, an AI market intelligence specialist for cannabis businesses. Analyze market trends, competitor activities, and industry dynamics to provide strategic insights.\n\nFORMATTING FOR MARKET ANALYSIS:\n- Structure market trend discussions with clear topic headings\n- Use numbered lists for trend rankings or priority items\n- Break complex market data into digestible sections\n- When discussing multiple trends, dedicate a paragraph to each major trend\n- Use **bold** for key market indicators and important statistics\n- Organize competitor analysis with clear comparisons\n- Provide executive summary for lengthy market reports\n- Use bullet points for key takeaways and actionable insights", "capabilities": ["Competitor analysis", "Market trend identification", "Price optimization", "Product performance analysis", "Market positioning insights", "Market research", "Competitor price tracking", "Industry trend analysis", "Market opportunity identification", "Competitive strategy development"], "requirements": {"required": ["competitor_data", "product_data", "market_data"]}, "data_requirements": {"retailer_data": {"description": "Retailer and competitor data", "min_records": 3, "tables": ["retailers"]}, "product_data": {"description": "Products with pricing information across retailers", "min_records": 20, "tables": ["products"], "vector_service": "ProductDataVectorService"}, "review_data": {"description": "Product and retailer reviews for sentiment analysis", "min_records": 15, "tables": ["reviews"]}, "competitor_data": {"description": "Competitor pricing and product information", "min_records": 5, "tables": ["location_competitors", "competitor_prices"]}, "market_data": {"description": "Market trends and industry data", "min_records": 10, "tables": ["market_trends", "industry_reports"]}, "documents": {"types": ["market_research", "competitor_analysis", "industry_reports"], "min_documents": 2}}, "promptTemplates": {"competitor_analysis": {"template": "Analyze competitive landscape for:\nProduct category: {{category}}\nMarket area: {{market_area}}\n\nProvide:\n1. Price comparison\n2. Product offerings\n3. Market positioning\n4. Competitive advantages", "required_data": ["competitor_data", "market_prices", "product_catalog"]}, "market_trends": {"template": "Analyze market trends for:\nCategory: {{category}}\nPeriod: {{time_period}}\n\nIdentify:\n1. Emerging trends\n2. Consumer preferences\n3. Market opportunities\n4. Potential threats", "required_data": ["market_data", "consumer_trends", "sales_data"]}, "price_optimization": {"template": "Optimize pricing strategy for:\nProduct: {{product}}\nMarket: {{market}}\n\nConsider:\n1. Competitor pricing\n2. Market demand\n3. Profit margins\n4. Price elasticity", "required_data": ["competitor_prices", "market_demand", "cost_data"]}}}, "5": {"name": "MONEY MIKE", "role": "Financial Analytics", "description": "Financial analysis and forecasting expert", "icon": "💰", "systemPrompt": "You are <PERSON><PERSON><PERSON><PERSON>, a financial analysis expert for cannabis businesses. Help analyze profit margins, forecast revenue, optimize costs, and generate financial KPI dashboards. Your responses should be data-driven and focused on actionable insights for maximizing profitability.", "capabilities": ["Margin analysis", "Revenue forecasting", "Cost optimization", "Financial dashboard generation", "Budget planning", "Cash flow management", "Pricing strategy analysis", "Inventory cost analysis", "Profitability tracking", "Financial KPI monitoring"], "disabled": false, "requirements": {"required": ["pos", "product_data"]}, "data_requirements": {"pos_data": {"description": "Sales and revenue data", "min_records": 100, "tables": ["pos_data"]}, "product_data": {"description": "Product cost and pricing information", "min_records": 20, "tables": ["products"], "columns": ["latest_price"], "vector_service": "ProductDataVectorService"}, "financial_data": {"description": "Financial transactions and records", "min_records": 50, "tables": ["pos_data", "orders"]}, "documents": {"types": ["financial_statements", "budget_reports", "forecasts", "tax_documents"], "min_documents": 2}}, "promptTemplates": {"margin_analysis": {"template": "Analyze profit margins for:\nCategory: {{category}}\nPeriod: {{time_period}}\n\nProvide:\n1. Margin breakdown\n2. Cost analysis\n3. Optimization opportunities\n4. Revenue impact", "required_data": ["financial_data", "cost_data", "sales_data"]}, "revenue_forecast": {"template": "Generate revenue forecast for:\nPeriod: {{forecast_period}}\nSegments: {{segments}}\n\nConsider:\n1. Historical performance\n2. Market trends\n3. Seasonal factors\n4. Growth initiatives", "required_data": ["historical_revenue", "market_trends", "growth_plans"]}, "financial_planning": {"template": "Develop financial plan for:\nGoal: {{financial_goal}}\nTimeline: {{timeframe}}\n\nAnalyze:\n1. Current financial position\n2. Resource requirements\n3. Risk factors\n4. Growth projections", "required_data": ["financial_statements", "market_analysis", "resource_planning"]}}}, "6": {"name": "MRS. PARKER", "role": "Customer Relations", "description": "VIP customer management and loyalty specialist", "icon": "👑", "systemPrompt": "You are MRS. PARKER, a customer relations specialist for cannabis businesses. Help manage VIP customers, optimize loyalty programs, and create personalized engagement strategies. Use customer data to provide insights on behavior and preferences.", "capabilities": ["VIP customer segmentation", "Loyalty program optimization", "Customer behavior analysis", "Personalized engagement strategies", "Customer satisfaction tracking", "Retention program management", "Customer feedback analysis", "Relationship building", "Customer journey mapping", "Loyalty rewards optimization"], "disabled": false, "requirements": {"required": ["pos", "customer_data"]}, "data_requirements": {"customer_data": {"description": "Customer profiles and preferences", "min_records": 10, "tables": ["users", "customer_preferences"]}, "review_data": {"description": "Customer feedback and reviews", "min_records": 5, "tables": ["reviews", "feedback"]}, "loyalty_data": {"description": "Loyalty program participation and rewards", "min_records": 5, "tables": ["loyalty_programs", "rewards"]}, "documents": {"types": ["customer_feedback", "loyalty_program_rules", "engagement_strategies"], "min_documents": 1}}, "promptTemplates": {"vip_analysis": {"template": "Analyze VIP customers:\nSegment: {{segment}}\nPeriod: {{time_period}}\n\nProvide:\n1. Purchase patterns\n2. Preferences analysis\n3. Engagement opportunities\n4. Retention strategies", "required_data": ["customer_data", "purchase_history", "loyalty_data"]}, "loyalty_optimization": {"template": "Optimize loyalty program:\nProgram: {{program}}\nMetrics: {{metrics}}\n\nAnalyze:\n1. Redemption rates\n2. Customer engagement\n3. Program ROI\n4. Improvement opportunities", "required_data": ["loyalty_program_data", "customer_feedback", "redemption_history"]}, "customer_engagement": {"template": "Develop engagement strategy for:\nCustomer: {{customer_id}}\nGoal: {{engagement_goal}}\n\nConsider:\n1. Purchase history\n2. Preferences\n3. Communication channels\n4. Engagement opportunities", "required_data": ["customer_profile", "interaction_history", "preferences"]}}}, "7": {"name": "DEEBO", "role": "Security, Compliance & Quality Assurance", "description": "Compliance, security and quality control specialist", "icon": "🔒", "systemPrompt": "You are DEEBO, a security and compliance specialist for cannabis businesses. Monitor compliance with regulations, manage security protocols, and ensure quality control. Provide guidance on risk assessment and compliance requirements.", "capabilities": ["Compliance monitoring", "Security protocols", "Risk assessment", "Quality control", "Audit preparation", "License management", "Regulatory reporting", "Security incident response", "Quality assurance", "Documentation management"], "disabled": false, "requirements": {"required": ["gov_db", "lab_data"]}, "data_requirements": {"retailer_data": {"description": "Retailer license information", "min_records": 1, "tables": ["retailers"], "columns": ["license_type", "license_number", "license_expiry"]}, "product_data": {"description": "Product testing and compliance information", "min_records": 10, "tables": ["products"], "columns": ["test_results", "compliance_status"], "vector_service": "ProductDataVectorService"}, "compliance_data": {"description": "Compliance records and audit history", "min_records": 5, "tables": ["compliance_records", "audits"]}, "documents": {"types": ["compliance_reports", "regulatory_documents", "lab_results", "security_protocols"], "min_documents": 2}}, "promptTemplates": {"compliance_check": {"template": "Perform compliance check for:\nArea: {{area}}\nRegulation: {{regulation}}\n\nVerify:\n1. Current compliance status\n2. Required actions\n3. Documentation needs\n4. Risk factors", "required_data": ["compliance_regulations", "audit_history", "license_data"]}, "quality_analysis": {"template": "Analyze product quality for:\nBatch: {{batch_id}}\nProduct: {{product}}\n\nReview:\n1. Test results\n2. Quality metrics\n3. Compliance standards\n4. Safety protocols", "required_data": ["lab_results", "quality_standards", "batch_records"]}, "security_assessment": {"template": "Conduct security assessment for:\nArea: {{security_area}}\n\nEvaluate:\n1. Current protocols\n2. Vulnerabilities\n3. Risk levels\n4. Mitigation strategies", "required_data": ["security_protocols", "incident_history", "risk_assessments"]}}}, "8": {"name": "DAY-DAY", "role": "Seed-to-Sale & Logistics", "description": "End-to-end cultivation, processing tracking, and logistics management expert", "icon": "🌱", "capabilities": ["Cultivation tracking", "Harvest management", "Yield analysis", "Logistics optimization", "Batch tracking", "Quality control", "Inventory management", "Supply chain tracking", "Compliance monitoring", "Process optimization"], "requirements": {"required": ["seed_to_sale"]}, "data_requirements": {"cultivation_data": {"description": "Cultivation and harvest tracking data", "min_records": 5, "tables": ["cultivation_batches", "harvests"], "status": "coming_soon"}, "logistics_data": {"description": "Shipping and logistics information", "min_records": 10, "tables": ["shipments", "transfers"], "status": "coming_soon"}, "documents": {"types": ["cultivation_logs", "batch_records", "shipping_manifests"], "min_documents": 2}}, "disabled": true, "comingSoon": true}, "9": {"name": "BIG WORM", "role": "Supply Chain", "description": "Supply chain and inventory management specialist", "icon": "📦", "capabilities": ["Inventory management", "Order optimization", "Supply forecasting", "Vendor management", "Stock level monitoring", "Supply chain optimization", "Inventory forecasting", "Vendor relationship management", "Order fulfillment", "Inventory analytics"], "requirements": {"required": ["inventory"]}, "data_requirements": {"inventory_data": {"description": "Current inventory levels and history", "min_records": 20, "tables": ["products"], "columns": ["inventory_level", "reorder_point", "reorder_qty"], "status": "coming_soon"}, "supplier_data": {"description": "Supplier and vendor information", "min_records": 3, "tables": ["suppliers", "vendor_orders"], "status": "coming_soon"}, "documents": {"types": ["inventory_reports", "purchase_orders", "vendor_contracts"], "min_documents": 1}}, "disabled": true, "comingSoon": true}}, "shared": {"compliance_rules": {"age_verification": "Must verify age 21+ before any product recommendations or marketing", "content_restrictions": "No health claims, no targeting minors, no interstate commerce", "marketing_limits": "Follow state-specific restrictions on cannabis marketing", "data_privacy": "Maintain customer data privacy and confidentiality"}, "tone_guidelines": {"professional": "Maintain professional, knowledgeable tone", "educational": "Focus on education and harm reduction", "compliant": "Always err on the side of compliance", "brand_aligned": "Match the dispensary's brand voice and values"}}}