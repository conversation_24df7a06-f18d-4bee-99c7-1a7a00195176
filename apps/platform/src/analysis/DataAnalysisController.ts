/**
 * @swagger
 * tags:
 *   name: DataAnalysis
 *   description: Data analysis endpoints for sales, customer, and product insights
 */

import Router from "@koa/router";
import { locationRoleMiddleware } from "../locations/LocationService";
import { DataAnalysisService } from "./DataAnalysisService";
import { logger } from "../config/logger";

const router = new Router({
  prefix: "/analysis",
});

/**
 * @swagger
 * /analysis/sales-trends:
 *   post:
 *     summary: Analyze Sales Trends
 *     description: Analyze sales trends for a location within a date range
 *     tags: [DataAnalysis]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *     responses:
 *       200:
 *         description: Sales trends analysis result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               additionalProperties: true
 *       400:
 *         description: startDate and endDate are required
 *       500:
 *         description: Failed to analyze sales trends
 */
router.post("/sales-trends", locationRoleMiddleware("support"), async (ctx) => {
  try {
    const { startDate, endDate } = ctx.request.body;
    const locationId = ctx.state.location.id;

    if (!startDate || !endDate) {
      ctx.status = 400;
      ctx.body = {
        error: "startDate and endDate are required",
      };
      return;
    }

    const analysis = await DataAnalysisService.analyzeSalesTrends(
      locationId,
      new Date(startDate),
      new Date(endDate)
    );

    ctx.body = analysis;
  } catch (error) {
    logger.error("Error in sales trends analysis:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to analyze sales trends",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /analysis/customer-behavior:
 *   post:
 *     summary: Analyze Customer Behavior
 *     description: Analyze customer behavior for a location within a date range
 *     tags: [DataAnalysis]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *     responses:
 *       200:
 *         description: Customer behavior analysis result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               additionalProperties: true
 *       400:
 *         description: startDate and endDate are required
 *       500:
 *         description: Failed to analyze customer behavior
 */
router.post(
  "/customer-behavior",
  locationRoleMiddleware("support"),
  async (ctx) => {
    try {
      const { startDate, endDate } = ctx.request.body;
      const locationId = ctx.state.location.id;

      if (!startDate || !endDate) {
        ctx.status = 400;
        ctx.body = {
          error: "startDate and endDate are required",
        };
        return;
      }

      const analysis = await DataAnalysisService.analyzeCustomerBehavior(
        locationId,
        new Date(startDate),
        new Date(endDate)
      );

      ctx.body = analysis;
    } catch (error) {
      logger.error("Error in customer behavior analysis:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to analyze customer behavior",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /analysis/product-performance:
 *   post:
 *     summary: Analyze Product Performance
 *     description: Analyze product performance for a location within a date range
 *     tags: [DataAnalysis]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *     responses:
 *       200:
 *         description: Product performance analysis result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               additionalProperties: true
 *       400:
 *         description: startDate and endDate are required
 *       500:
 *         description: Failed to analyze product performance
 */
router.post(
  "/product-performance",
  locationRoleMiddleware("support"),
  async (ctx) => {
    try {
      const { startDate, endDate } = ctx.request.body;
      const locationId = ctx.state.location.id;

      if (!startDate || !endDate) {
        ctx.status = 400;
        ctx.body = {
          error: "startDate and endDate are required",
        };
        return;
      }

      const analysis = await DataAnalysisService.analyzeProductPerformance(
        locationId,
        new Date(startDate),
        new Date(endDate)
      );

      ctx.body = analysis;
    } catch (error) {
      logger.error("Error in product performance analysis:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to analyze product performance",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /analysis/sales-trends-time-series:
 *   post:
 *     summary: Analyze Sales Trends with Time Series
 *     description: Analyze sales trends with time series analysis for a location within a date range
 *     tags: [DataAnalysis]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               startDate:
 *                 type: string
 *                 format: date
 *               endDate:
 *                 type: string
 *                 format: date
 *     responses:
 *       200:
 *         description: Sales trends time series analysis result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               additionalProperties: true
 *       400:
 *         description: startDate and endDate are required
 *       500:
 *         description: Failed to analyze sales trends with time series
 */
router.post(
  "/sales-trends-time-series",
  locationRoleMiddleware("support"),
  async (ctx) => {
    try {
      const { startDate, endDate } = ctx.request.body;
      const locationId = ctx.state.location.id;

      if (!startDate || !endDate) {
        ctx.status = 400;
        ctx.body = {
          error: "startDate and endDate are required",
        };
        return;
      }

      const analysis =
        await DataAnalysisService.analyzeSalesTrendsWithTimeSeries(
          locationId,
          new Date(startDate),
          new Date(endDate)
        );

      ctx.body = analysis;
    } catch (error) {
      logger.error("Error in sales trends time series analysis:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to analyze sales trends with time series",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

export default router;
