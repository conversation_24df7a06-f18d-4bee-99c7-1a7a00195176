/* eslint-disable indent */
import { logger } from "../config/logger";
import { PosData } from "../pos/PosData";
import Campaign from "../campaigns/Campaign";
import { Review } from "../reviews/Review";
import { Product } from "../products/Product";

export interface AnalysisResult {
  type: string;
  data: any;
  insights: string[];
  recommendations: string[];
}

export interface TimeSeriesData {
  date: string;
  value: number;
}

export interface RegressionResult {
  slope: number;
  intercept: number;
  rSquared: number;
  predictions: number[];
}

export interface ClusterResult {
  clusters: number[][];
  centroids: number[][];
  labels: number[];
}

export interface TimeSeriesAnalysis {
  trend: {
    direction: "up" | "down" | "stable";
    magnitude: number;
    confidence: number;
  };
  seasonality: {
    detected: boolean;
    period?: number;
    strength?: number;
  };
  forecast: {
    nextPeriod: number;
    confidence: number;
  };
}

export class DataAnalysisService {
  /**
   * Perform regression analysis on time series data
   */
  static async performRegressionAnalysis(
    data: TimeSeriesData[]
  ): Promise<RegressionResult> {
    try {
      const x = data.map((_, i) => i);
      const y = data.map((d) => d.value);

      // Calculate means
      const xMean = x.reduce((a, b) => a + b, 0) / x.length;
      const yMean = y.reduce((a, b) => a + b, 0) / y.length;

      // Calculate slope and intercept
      let numerator = 0;
      let denominator = 0;
      for (let i = 0; i < x.length; i++) {
        numerator += (x[i] - xMean) * (y[i] - yMean);
        denominator += Math.pow(x[i] - xMean, 2);
      }
      const slope = numerator / denominator;
      const intercept = yMean - slope * xMean;

      // Calculate R-squared
      const predictions = x.map((xi) => slope * xi + intercept);
      const ssTotal = y.reduce((sum, yi) => sum + Math.pow(yi - yMean, 2), 0);
      const ssResidual = y.reduce(
        (sum, yi, i) => sum + Math.pow(yi - predictions[i], 2),
        0
      );
      const rSquared = 1 - ssResidual / ssTotal;

      return {
        slope,
        intercept,
        rSquared,
        predictions,
      };
    } catch (error) {
      logger.error("Error in regression analysis:", error);
      throw error;
    }
  }

  /**
   * Perform clustering analysis on numerical data
   */
  static async performClustering(
    data: number[][],
    k: number = 3
  ): Promise<ClusterResult> {
    try {
      // Initialize centroids randomly
      const centroids = Array.from(
        { length: k },
        () => data[Math.floor(Math.random() * data.length)]
      );

      let labels: number[] = [];
      let oldLabels: number[] = [];
      let iterations = 0;
      const maxIterations = 100;

      // K-means clustering
      while (iterations < maxIterations) {
        // Assign points to nearest centroid
        labels = data.map((point) => {
          const distances = centroids.map((centroid) =>
            Math.sqrt(
              point.reduce(
                (sum, val, i) => sum + Math.pow(val - centroid[i], 2),
                0
              )
            )
          );
          return distances.indexOf(Math.min(...distances));
        });

        // Check for convergence
        if (
          labels.every((label, i) => label === oldLabels[i]) &&
          oldLabels.length > 0
        ) {
          break;
        }

        // Update centroids
        for (let i = 0; i < k; i++) {
          const clusterPoints = data.filter((_, j) => labels[j] === i);
          if (clusterPoints.length > 0) {
            centroids[i] = clusterPoints[0].map(
              (_, j) =>
                clusterPoints.reduce((sum, point) => sum + point[j], 0) /
                clusterPoints.length
            );
          }
        }

        oldLabels = [...labels];
        iterations++;
      }

      return {
        clusters: data.map((point, i) => [...point, labels[i]]),
        centroids,
        labels,
      };
    } catch (error) {
      logger.error("Error in clustering analysis:", error);
      throw error;
    }
  }

  /**
   * Perform time series analysis on data
   */
  static async analyzeTimeSeries(
    data: TimeSeriesData[]
  ): Promise<TimeSeriesAnalysis> {
    try {
      // Calculate basic statistics
      const values = data.map((d) => d.value);
      const mean = values.reduce((a, b) => a + b, 0) / values.length;
      const stdDev = Math.sqrt(
        values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) /
          values.length
      );

      // Detect trend using linear regression
      const regressionResult = await this.performRegressionAnalysis(data);

      // Determine trend direction and magnitude
      const trend = {
        direction:
          regressionResult.slope > 0
            ? ("up" as const)
            : regressionResult.slope < 0
            ? ("down" as const)
            : ("stable" as const),
        magnitude: Math.abs(regressionResult.slope),
        confidence: regressionResult.rSquared,
      };

      // Detect seasonality using autocorrelation
      const seasonality = this.detectSeasonality(values);

      // Generate forecast
      const forecast = this.generateForecast(
        data,
        regressionResult,
        seasonality
      );

      return {
        trend,
        seasonality,
        forecast,
      };
    } catch (error) {
      logger.error("Error in time series analysis:", error);
      throw error;
    }
  }

  /**
   * Detect seasonality in time series data
   */
  private static detectSeasonality(values: number[]): {
    detected: boolean;
    period?: number;
    strength?: number;
  } {
    try {
      // Calculate autocorrelation
      const maxLag = Math.min(20, Math.floor(values.length / 2));
      const autocorr = [];

      for (let lag = 1; lag <= maxLag; lag++) {
        let numerator = 0;
        let denominator = 0;
        const mean = values.reduce((a, b) => a + b, 0) / values.length;

        for (let i = 0; i < values.length - lag; i++) {
          numerator += (values[i] - mean) * (values[i + lag] - mean);
          denominator += Math.pow(values[i] - mean, 2);
        }

        autocorr.push(numerator / denominator);
      }

      // Find peaks in autocorrelation
      const peaks = [];
      for (let i = 1; i < autocorr.length - 1; i++) {
        if (autocorr[i] > autocorr[i - 1] && autocorr[i] > autocorr[i + 1]) {
          peaks.push({ lag: i + 1, value: autocorr[i] });
        }
      }

      // Sort peaks by value
      peaks.sort((a, b) => b.value - a.value);

      // Check if we have significant seasonality
      if (peaks.length > 0 && peaks[0].value > 0.5) {
        return {
          detected: true,
          period: peaks[0].lag,
          strength: peaks[0].value,
        };
      }

      return { detected: false };
    } catch (error) {
      logger.error("Error detecting seasonality:", error);
      return { detected: false };
    }
  }

  /**
   * Generate forecast for next period
   */
  private static generateForecast(
    data: TimeSeriesData[],
    regressionResult: RegressionResult,
    seasonality: { detected: boolean; period?: number; strength?: number }
  ): { nextPeriod: number; confidence: number } {
    try {
      const lastIndex = data.length - 1;
      const lastValue = data[lastIndex].value;

      // Base forecast on trend
      let forecast = regressionResult.slope + regressionResult.intercept;

      // Adjust for seasonality if detected
      if (seasonality.detected && seasonality.period) {
        const seasonalIndex = (lastIndex + 1) % seasonality.period;
        const seasonalValue = data[seasonalIndex].value;
        const seasonalAdjustment =
          (seasonalValue - lastValue) * (seasonality.strength || 0.5);
        forecast += seasonalAdjustment;
      }

      // Calculate confidence based on R-squared and seasonality strength
      const confidence =
        regressionResult.rSquared *
        (seasonality.detected ? seasonality.strength || 0.5 : 1);

      return {
        nextPeriod: forecast,
        confidence,
      };
    } catch (error) {
      logger.error("Error generating forecast:", error);
      throw error;
    }
  }

  /**
   * Analyze sales trends over time
   */
  static async analyzeSalesTrends(
    locationId: number,
    startDate: Date,
    endDate: Date
  ): Promise<AnalysisResult> {
    try {
      const salesData = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          PosData.raw("DATE(order_date) as date"),
          PosData.raw("SUM(net_sales) as daily_sales"),
        ])
        .groupBy("date")
        .orderBy("date");

      const timeSeriesData = salesData.map((record: any) => ({
        date: record.date,
        value: record.daily_sales,
      }));

      const regressionResult = await this.performRegressionAnalysis(
        timeSeriesData
      );

      // Generate insights
      const insights: string[] = [];
      if (regressionResult.slope > 0) {
        insights.push(
          `Sales are trending upward with an average daily increase of $${regressionResult.slope.toFixed(
            2
          )}`
        );
      } else {
        insights.push(
          `Sales are trending downward with an average daily decrease of $${Math.abs(
            regressionResult.slope
          ).toFixed(2)}`
        );
      }

      if (regressionResult.rSquared > 0.7) {
        insights.push(
          `The trend is strong with an R-squared value of ${regressionResult.rSquared.toFixed(
            2
          )}`
        );
      }

      // Generate recommendations
      const recommendations: string[] = [];
      if (regressionResult.slope < 0) {
        recommendations.push(
          "Consider implementing promotional campaigns to boost sales"
        );
        recommendations.push(
          "Review pricing strategy and product mix to identify opportunities"
        );
      }

      return {
        type: "sales_trends",
        data: {
          timeSeriesData,
          regressionResult,
        },
        insights,
        recommendations,
      };
    } catch (error) {
      logger.error("Error analyzing sales trends:", error);
      throw error;
    }
  }

  /**
   * Analyze customer behavior patterns
   */
  static async analyzeCustomerBehavior(
    locationId: number,
    startDate: Date,
    endDate: Date
  ): Promise<AnalysisResult> {
    try {
      const customerData = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          "customer_name",
          PosData.raw("COUNT(*) as visit_count"),
          PosData.raw("AVG(net_sales) as avg_spend"),
          PosData.raw("SUM(net_sales) as total_spend"),
        ])
        .groupBy("customer_name");

      // Prepare data for clustering
      const clusteringData = customerData.map((record: any) => [
        record.visit_count,
        record.avg_spend,
      ]);

      const clusterResult = await this.performClustering(clusteringData, 3);

      // Generate insights
      const insights: string[] = [];
      const customerSegments = clusterResult.labels.reduce(
        (acc: Record<number, number>, label) => {
          acc[label] = (acc[label] || 0) + 1;
          return acc;
        },
        {}
      );

      Object.entries(customerSegments).forEach(([segment, count]) => {
        insights.push(
          `Segment ${segment} contains ${count} customers with similar behavior patterns`
        );
      });

      // Generate recommendations
      const recommendations: string[] = [];
      if (Object.keys(customerSegments).length > 1) {
        recommendations.push(
          "Develop targeted marketing strategies for each customer segment"
        );
        recommendations.push(
          "Create loyalty programs tailored to different customer segments"
        );
      }

      return {
        type: "customer_behavior",
        data: {
          customerData,
          clusterResult,
        },
        insights,
        recommendations,
      };
    } catch (error) {
      logger.error("Error analyzing customer behavior:", error);
      throw error;
    }
  }

  /**
   * Analyze product performance
   */
  static async analyzeProductPerformance(
    locationId: number,
    startDate: Date,
    endDate: Date
  ): Promise<AnalysisResult> {
    try {
      const productData = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          "product_name",
          "master_category",
          PosData.raw("COUNT(*) as units_sold"),
          PosData.raw("SUM(net_sales) as revenue"),
          PosData.raw("AVG(net_sales) as avg_price"),
          PosData.raw("SUM(inventory_profit) as profit"),
        ])
        .groupBy("product_name", "master_category")
        .orderBy("revenue", "desc");

      // Calculate additional metrics
      const totalRevenue = productData.reduce(
        (sum: number, record: any) => sum + record.revenue,
        0
      );
      const totalProfit = productData.reduce(
        (sum: number, record: any) => sum + record.profit,
        0
      );

      // Generate insights
      const insights: string[] = [];
      insights.push(
        `Top performing product: ${
          productData[0]?.product_name
        } with $${productData[0]?.revenue.toFixed(2)} in revenue`
      );
      insights.push(
        `Overall profit margin: ${((totalProfit / totalRevenue) * 100).toFixed(
          2
        )}%`
      );

      // Generate recommendations
      const recommendations: string[] = [];
      const lowPerformingProducts = productData.filter(
        (record: any) => record.revenue < totalRevenue / productData.length
      );
      if (lowPerformingProducts.length > 0) {
        recommendations.push(
          `Consider reviewing pricing or marketing strategy for ${lowPerformingProducts.length} underperforming products`
        );
      }

      return {
        type: "product_performance",
        data: {
          productData,
          totalRevenue,
          totalProfit,
        },
        insights,
        recommendations,
      };
    } catch (error) {
      logger.error("Error analyzing product performance:", error);
      throw error;
    }
  }

  /**
   * Analyze sales trends with time series analysis
   */
  static async analyzeSalesTrendsWithTimeSeries(
    locationId: number,
    startDate: Date,
    endDate: Date
  ): Promise<AnalysisResult> {
    try {
      const salesData = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          PosData.raw("DATE(order_date) as date"),
          PosData.raw("SUM(net_sales) as daily_sales"),
        ])
        .groupBy("date")
        .orderBy("date");

      const timeSeriesData = salesData.map((record: any) => ({
        date: record.date,
        value: record.daily_sales,
      }));

      const timeSeriesAnalysis = await this.analyzeTimeSeries(timeSeriesData);

      // Generate insights
      const insights: string[] = [];
      insights.push(
        `Sales are trending ${timeSeriesAnalysis.trend.direction} with ${
          timeSeriesAnalysis.trend.confidence > 0.7 ? "strong" : "moderate"
        } confidence`
      );

      if (timeSeriesAnalysis.seasonality.detected) {
        insights.push(
          `Detected ${
            timeSeriesAnalysis.seasonality.period
          }-day seasonality with ${
            timeSeriesAnalysis.seasonality.strength &&
            timeSeriesAnalysis.seasonality.strength > 0.7
              ? "strong"
              : "moderate"
          } pattern strength`
        );
      }

      insights.push(
        `Forecast for next period: $${timeSeriesAnalysis.forecast.nextPeriod.toFixed(
          2
        )} with ${(timeSeriesAnalysis.forecast.confidence * 100).toFixed(
          1
        )}% confidence`
      );

      // Generate recommendations
      const recommendations: string[] = [];
      if (timeSeriesAnalysis.trend.direction === "down") {
        recommendations.push(
          "Consider implementing promotional campaigns to boost sales"
        );
      }

      if (timeSeriesAnalysis.seasonality.detected) {
        recommendations.push(
          `Plan inventory and staffing based on the ${timeSeriesAnalysis.seasonality.period}-day seasonal pattern`
        );
      }

      return {
        type: "sales_trends_time_series",
        data: {
          timeSeriesData,
          timeSeriesAnalysis,
        },
        insights,
        recommendations,
      };
    } catch (error) {
      logger.error("Error analyzing sales trends with time series:", error);
      throw error;
    }
  }
}
