import Koa from "koa";
import koaBody from "koa-body";
import bodyParser from "koa-bodyparser";

import cors from "@koa/cors";
import serve from "koa-static";
import controllers, { SubRouter, register } from "./config/controllers";
import { RequestError } from "./core/errors";
import { logger } from "./config/logger";
import Router from "@koa/router";

export default class Api extends Koa {
  router = new Router();
  controllers?: Record<string, SubRouter>;

  constructor(public app: import("./app").default) {
    console.log("\n\n🚀 API INITIALIZING 🚀\n\n");
    super();

    logger.info("bakedBot:api initializing");
    this.proxy = process.env.NODE_ENV !== "development";

    app.error.attach(this);
    this.use(async (ctx, next) => {
      try {
        await next();
      } catch (error: any) {
        logger.error({ error, ctx }, "error");
        if (error instanceof RequestError) {
          ctx.status = error.statusCode ?? 400;
          ctx.body = error;
          return;
        } else if (error.status === 404) {
          return;
        } else {
          ctx.status = 400;
          ctx.body =
            process.env.NODE_ENV === "production"
              ? {
                  status: "error",
                  error: "An error occurred with this request.",
                }
              : {
                  status: "error",
                  error: {
                    message: error.message,
                    stack: error.stack,
                  },
                };
        }

        ctx.app.emit("error", error, ctx);
      }
    });

    this.keys = [app.env.secret];
    this.use(bodyParser({ enableTypes: ["json"] }))
      .use(cors())
      .use(
        serve("./public", {
          hidden: true,
          defer: !app.env.config.monoDocker,
        })
      );

    if (process.env.NODE_ENV === "development") {
      logger.info("bakedBot:api development found");

      const debugRouter = new Router();
      debugRouter.get("/debug/routes", (ctx) => {
        const routes: string[] = [];
        this.router.stack.forEach((layer) => {
          if (layer.methods.length > 0) {
            routes.push(`${layer.methods.join(",")} ${layer.path}`);
          }
        });
        ctx.body = {
          routes: routes.sort(),
          total: routes.length,
        };
      });
      this.use(debugRouter.routes());
    }

    this.registerControllers();
  }

  getControllers() {
    console.log("\n📦 Getting controllers...\n");
    logger.info("bakedBot:api getting controllers");
    const getControllers = controllers;
    const result = getControllers(this.app);
    console.log(`Found controllers: ${Object.keys(result).join(", ")}\n`);
    return result;
  }

  registerControllers() {
    console.log("\n🔌 Registering controllers...\n");
    logger.info("bakedBot:api registering controllers");
    this.controllers = this.getControllers();
    if (this.controllers) {
      this.register(...Object.values(this.controllers));
    }
    console.log("✅ Controllers registered\n\n");
  }

  register(...routers: SubRouter[]) {
    console.log("\n🛠️  Setting up API routes...\n");
    logger.info("bakedBot:api setting up API routes");
    const apiRouter = new Router({ prefix: "/api" });
    for (const router of routers.filter((r) => !r.global)) {
      console.log(
        `Registering router with prefix: ${
          router.opts?.prefix || "no prefix"
        }\n`
      );
      register(apiRouter, router);
    }
    register(this.router, apiRouter);
    for (const router of routers.filter((r) => r.global)) {
      console.log(
        `Registering global router with prefix: ${
          router.opts?.prefix || "no prefix"
        }\n`
      );
      register(this.router, router);
    }
    this.use(this.router.routes()).use(this.router.allowedMethods());
    console.log("✅ API routes setup complete\n\n");
    logger.info("bakedBot:api API routes setup complete");
  }
}
