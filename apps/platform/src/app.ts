import loadDatabase, { Database } from "./config/database";
import loadQueue from "./config/queue";
import loadStorage from "./config/storage";
import loadError, { logger } from "./config/logger";
import loadRateLimit, { RateLimiter } from "./config/rateLimit";
import loadStats, { Stats } from "./config/stats";
import type { Env } from "./config/env";
import type Queue from "./queue";
import Storage from "./storage";
import { uuid } from "./utilities";
import Api from "./api";
import Worker from "./worker";
import ErrorHandler from "./error/ErrorHandler";
import { DefaultRedis, Redis } from "./config/redis";
import EventEmitter from "eventemitter2";
import { PosDataVectorService } from "./pos/PosDataVectorService";
import { Server } from "http";
import { ChatWebSocket } from "./chats/ChatWebSocket";
import { ChatEvent } from "./chats/models/types";
import { SmokeyAIService } from "./chats/SmokeyAIService";
import { ChatSearchService } from "./chats/ChatSearchService";
import { ChatAnalyticsService } from "./chats/ChatAnalyticsService";
import { OpenAI } from "openai";
import retailerRoutes from "./retailers/RetailerController";
import SharedAssetsController from "./shared/SharedAssetsController";
import { VectorService } from "./core/VectorService";

export default class App {
  private static $main: App;
  static get main() {
    if (!App.$main) {
      throw new Error("Instance not setup");
    }
    return App.$main;
  }

  static async init<T extends typeof App>(
    this: T,
    env: Env
  ): Promise<InstanceType<T>> {
    logger.info("bakedBot initializing");

    // Boot up error tracking
    const error = await loadError(env.error);

    // Load & migrate database
    const database = await loadDatabase(env.db);

    // Load queue
    const queue = loadQueue(env.queue);

    // Load storage
    const storage = loadStorage(env.storage);

    // Setup app
    const app = new this(env, database, queue, storage, error) as any;

    // Initialize Pinecone service
    PosDataVectorService.initialize().catch(console.error);

    return this.setMain(app);
  }

  static setMain<T extends typeof App>(this: T, app: InstanceType<T>) {
    this.$main = app;
    return app;
  }

  uuid = uuid();
  api?: Api;
  worker?: Worker;
  rateLimiter: RateLimiter;
  redis: Redis;
  stats: Stats;
  events = new EventEmitter({ wildcard: true, delimiter: ":" });
  #registered: { [key: string | number]: unknown };
  private server?: Server;
  private chatWs?: ChatWebSocket;
  private chatAI: SmokeyAIService;
  private chatSearch: ChatSearchService;
  private chatAnalytics: ChatAnalyticsService;

  constructor(
    public env: Env,
    public db: Database,
    public queue: Queue,
    public storage: Storage,
    public error: ErrorHandler
  ) {
    this.#registered = {};
    this.rateLimiter = loadRateLimit(env.redis);
    this.redis = DefaultRedis(env.redis);
    this.stats = loadStats(env.redis);
    this.unhandledErrorListener();

    // Initialize VectorService and try to get Pinecone client
    const vectorService = VectorService.getInstance();
    let pineconeClient;

    try {
      // Try to get an initialized Pinecone client
      pineconeClient = vectorService.getPineconeClient();
      logger.info("Using existing initialized Pinecone client");
    } catch (error) {
      logger.warn(
        "Pinecone client not yet initialized, will be initialized later"
      );
      pineconeClient = null;
    }

    this.chatAI = new SmokeyAIService(
      new OpenAI({ apiKey: env.openai?.apiKey }),
      this.db,
      {
        defaultModel: env.openai?.model || "gpt-4.1-mini",
        defaultTemperature:
          env.openai?.temperature !== undefined
            ? parseFloat(env.openai.temperature)
            : 0.7,
        pineconeClient,
        pineconeIndexName: "cannabis-data",
      }
    );
    this.chatSearch = new ChatSearchService(this);
    this.chatAnalytics = new ChatAnalyticsService(this);

    // Initialize VectorService asynchronously in the background
    this.initializeVectorService(vectorService);
  }

  private async initializeVectorService(vectorService: VectorService) {
    try {
      await vectorService.initialize();
      logger.info("VectorService initialized successfully in background");
    } catch (error) {
      logger.error("Failed to initialize VectorService in background:", error);
    }
  }

  start() {
    const runners = this.env.runners;
    if (runners.includes("api")) {
      this.startApi();
    }
    if (runners.includes("worker")) {
      this.startWorker();
    }
    return this;
  }

  startApi(api?: Api) {
    this.api = api ?? new Api(this);

    // Create a single HTTP server instance
    this.server = this.api.listen(this.env.port);
    this.server.keepAliveTimeout = 65000;
    this.server.requestTimeout = 180000; // 180 second request timeout (3 minutes)
    this.server.timeout = 180000; // 180 second socket timeout

    // Initialize WebSocket with the HTTP server
    if (this.server) {
      this.chatWs = new ChatWebSocket(this.server);
    }

    logger.info("bakedBot:api ready");
    logger.info(
      "bakedBot:api routes",
      this.api?.router.stack.map((layer) => ({
        path: layer.path,
        methods: layer.methods,
      }))
    );

    // Add retailer routes
    this.api.router.use(
      "/api/retailers",
      retailerRoutes.routes(),
      retailerRoutes.allowedMethods()
    );

    // Add shared assets route
    this.api.router.use("/api/shared-assets", (ctx, next) =>
      SharedAssetsController.routes()(ctx, next)
    );
    this.api.router.use("/api/shared-assets", (ctx, next) =>
      SharedAssetsController.allowedMethods()(ctx, next)
    );
  }

  startWorker(worker?: Worker) {
    this.worker = worker ?? new Worker(this);
    this.worker?.run();
    logger.info("bakedBot:worker ready");
  }

  async close() {
    await this.worker?.close();
    await this.db.destroy();
    await this.queue.close();
    await this.rateLimiter.close();
    if (this.chatWs) {
      this.chatWs.cleanup();
    }
    if (this.server) {
      this.server.close();
    }
  }

  get<T>(key: number | string): T {
    return this.#registered[key] as T;
  }

  set(key: number | string, value: unknown) {
    this.#registered[key] = value;
  }

  remove(key: number | string) {
    delete this.#registered[key];
  }

  unhandledErrorListener() {
    ["exit", "SIGINT", "SIGUSR1", "SIGUSR2", "SIGTERM"].forEach((eventType) => {
      process.on(eventType, async () => {
        await this.close();
        process.exit();
      });
    });
    process.on("uncaughtException", async (error) => {
      logger.error(error, "uncaught error");
      await this.close();
      process.exit();
    });
  }

  broadcastChatEvent(event: ChatEvent) {
    if (this.chatWs) {
      (this.chatWs as any).broadcast(event);
    }
  }

  get chatAIService() {
    return this.chatAI;
  }

  get chatSearchService() {
    return this.chatSearch;
  }

  get chatAnalyticsService() {
    return this.chatAnalytics;
  }

  getWebSocketServer() {
    return this.chatWs?.wss;
  }
}
