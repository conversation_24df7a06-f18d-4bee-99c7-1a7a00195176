/**
 * @swagger
 * tags:
 *   name: Auth
 *   description: Authentication and authorization endpoints
 */

import Router from "@koa/router";
import { getTokenCookies, revokeAccessToken } from "./TokenRepository";
import { getOrganizationByEmail } from "../organizations/OrganizationService";
import Organization from "../organizations/Organization";
import { authMethods, checkAuth, startAuth, validateAuth } from "./Auth";

const router = new Router<{
  organization?: Organization;
}>({
  prefix: "/auth",
});

/**
 * @swagger
 * /auth/methods:
 *   get:
 *     summary: Get Available Auth Methods
 *     description: Retrieves the list of available authentication methods
 *     tags: [Auth]
 *     responses:
 *       200:
 *         description: List of available authentication methods
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 methods:
 *                   type: array
 *                   items:
 *                     type: string
 */
router.get("/methods", async (ctx) => {
  ctx.body = await authMethods(ctx.state.organization);
});

/**
 * @swagger
 * /auth/check:
 *   post:
 *     summary: Check Authentication Status
 *     description: Verifies if the current session is authenticated
 *     tags: [Auth]
 *     responses:
 *       200:
 *         description: Authentication status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 authenticated:
 *                   type: boolean
 *                 user:
 *                   type: object
 *                   nullable: true
 */
router.post("/check", async (ctx) => {
  const email = ctx.query.email || ctx.request.body.email;
  const organization = await getOrganizationByEmail(email);
  ctx.body = checkAuth(organization);
});

/**
 * @swagger
 * /auth/login/{driver}:
 *   get:
 *     summary: Initiate OAuth Login
 *     description: Initiates the OAuth login flow for the specified provider
 *     tags: [Auth]
 *     parameters:
 *       - in: path
 *         name: driver
 *         required: true
 *         schema:
 *           type: string
 *         description: OAuth provider (e.g., google, linkedin)
 *     responses:
 *       302:
 *         description: Redirect to OAuth provider
 *       400:
 *         description: Invalid provider
 */
router.get("/login/:driver", async (ctx) => {
  ctx.status = 204;
  await startAuth(ctx);
});

/**
 * @swagger
 * /auth/login/{driver}:
 *   post:
 *     summary: Login with Provider
 *     description: Handles login with the specified provider
 *     tags: [Auth]
 *     parameters:
 *       - in: path
 *         name: driver
 *         required: true
 *         schema:
 *           type: string
 *         description: OAuth provider (e.g., google, linkedin)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       400:
 *         description: Invalid credentials
 */
router.post("/login/:driver", async (ctx) => {
  ctx.status = 204;
  await startAuth(ctx);
});

/**
 * @swagger
 * /auth/login/{driver}/callback:
 *   get:
 *     summary: OAuth Callback
 *     description: Handles the OAuth callback from the provider
 *     tags: [Auth]
 *     parameters:
 *       - in: path
 *         name: driver
 *         required: true
 *         schema:
 *           type: string
 *         description: OAuth provider (e.g., google, linkedin)
 *       - in: query
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *         description: Authorization code from provider
 *     responses:
 *       302:
 *         description: Redirect to success/failure page
 */
router.get("/login/:driver/callback", async (ctx) => {
  ctx.status = 204;
  await validateAuth(ctx);
});

/**
 * @swagger
 * /auth/login/{driver}/callback:
 *   post:
 *     summary: Handle OAuth Callback
 *     description: Processes the OAuth callback data
 *     tags: [Auth]
 *     parameters:
 *       - in: path
 *         name: driver
 *         required: true
 *         schema:
 *           type: string
 *         description: OAuth provider (e.g., google, linkedin)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               code:
 *                 type: string
 *     responses:
 *       200:
 *         description: Authentication successful
 *       400:
 *         description: Invalid callback data
 */
router.post("/login/:driver/callback", async (ctx) => {
  ctx.status = 204;
  await validateAuth(ctx);
});

/**
 * @swagger
 * /auth/logout:
 *   post:
 *     summary: Logout User
 *     description: Logs out the current user
 *     tags: [Auth]
 *     responses:
 *       200:
 *         description: Logout successful
 */
router.post("/logout", async (ctx) => {
  const oauth = getTokenCookies(ctx);
  if (oauth) {
    await revokeAccessToken(oauth.access_token, ctx);
  }
  ctx.redirect("/");
});

/**
 * @swagger
 * /auth/logout:
 *   get:
 *     summary: Logout User (GET)
 *     description: Logs out the current user via GET request
 *     tags: [Auth]
 *     responses:
 *       200:
 *         description: Logout successful
 */
router.get("/logout", async (ctx) => {
  const oauth = getTokenCookies(ctx);
  if (oauth) {
    await revokeAccessToken(oauth.access_token, ctx);
  }
  ctx.redirect("/");
});

export default router;
