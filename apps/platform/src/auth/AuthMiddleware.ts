import jwt from "jsonwebtoken";
import { Context } from "koa";
import App from "../app";
import { RequestError } from "../core/errors";
import Location, { LocationRole } from "../locations/Location";
import { LocationApiKey } from "../locations/LocationApiKey";
import { getLocationApiKey } from "../locations/LocationService";
import AuthError from "./AuthError";
import { getTokenCookies, isAccessTokenRevoked } from "./TokenRepository";
import { OrganizationRole } from "../organizations/Organization";
import StripeSubscription from "../subscriptions/StripeSubscription";

export interface JwtAdmin {
  id: number;
  organization_id: number;
  role: OrganizationRole;
}

export interface State {
  app: App;
}

type AuthScope = "admin" | "public" | "secret";
export interface AuthState {
  scope: AuthScope;
  admin?: JwtAdmin;
  key?: LocationApiKey;
}

export interface LocationState extends AuthState {
  location: Location;
  locationRole: LocationRole;
}

const parseAuth = async (ctx: Context) => {
  const token = getBearerToken(ctx);
  if (!token) {
    throw new RequestError(AuthError.AuthorizationError);
  }

  if (token.startsWith("pk_")) {
    // Public key
    return {
      scope: "public",
      key: await getLocationApiKey(token),
    };
  } else if (token.startsWith("sk_")) {
    // Secret key
    return {
      scope: "secret",
      key: await getLocationApiKey(token),
    };
  } else {
    // Try to parse as admin JWT
    try {
      const admin = (await verify(token)) as JwtAdmin;
      if (await isAccessTokenRevoked(token)) {
        throw new RequestError(AuthError.AccessDenied);
      }
      // Admin JWT
      return {
        scope: "admin",
        admin,
      };
    } catch (error) {
      throw new RequestError(AuthError.AuthorizationError);
    }
  }
};

export async function authMiddleware(ctx: Context, next: () => void) {
  try {
    const state = await parseAuth(ctx);
    ctx.state = { ...ctx.state, ...state };
  } catch (error) {
    throw new RequestError(AuthError.AuthorizationError);
  }

  // Check if organization is set by organizationMiddleware
  if (!ctx.state.organization) {
    throw new RequestError(AuthError.AuthorizationError);
  }

  const activeStates = ["trailing", "active", "past_due"];
  let subscriptionObj = await StripeSubscription.first((qb) =>
    qb.where("organization_id", ctx.state.organization.id)
  );

  // Create subscription Instance if non-existent
  if (!subscriptionObj) {
    subscriptionObj = await StripeSubscription.insertAndFetch({
      organization_id: ctx.state.organization.id,
    });
  }
  if (!activeStates.includes(subscriptionObj.status)) {
    // throw new RequestError(AuthError.SubscriptionInActive);
  }

  return next();
}

export const scopeMiddleware = (scope: string | string[]) => {
  const scopes = Array.isArray(scope) ? scope : [scope];
  return async function authMiddleware(ctx: Context, next: () => void) {
    if (!scopes.includes(ctx.state.scope)) {
      throw new RequestError(AuthError.AccessDenied);
    }
    return next();
  };
};

export const verify = async (token: string) => {
  return new Promise((resolve, reject) => {
    jwt.verify(token, App.main.env.secret, (error, decoded) => {
      error ? reject(error) : resolve(decoded);
    });
  });
};

const getBearerToken = (ctx: Context): string | undefined => {
  const authHeader = String(ctx.request.headers.authorization || "");
  if (authHeader.startsWith("Bearer ")) {
    return authHeader.substring(7, authHeader.length);
  }
  return getTokenCookies(ctx)?.access_token;
};
