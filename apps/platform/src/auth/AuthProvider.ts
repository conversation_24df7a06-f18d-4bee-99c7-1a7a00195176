import { Context } from "koa";
import App from "../app";
import Admin, { AdminParams, AuthAdminParams } from "./Admin";
import { getAdminByEmail } from "./AdminRepository";
import {
  generateAccessToken,
  OAuthResponse,
  setTokenCookies,
} from "./TokenRepository";
import Organization from "../organizations/Organization";
import { State } from "./AuthMiddleware";
import {
  createOrganization,
  getDefaultOrganization,
  getOrganizationByEmail,
  getOrganizationByDomain,
} from "../organizations/OrganizationService";
import StripeSubscription from "../subscriptions/StripeSubscription";

type OrgState = State & { organization?: Organization };
export type AuthContext = Context & { state: OrgState };

export default abstract class AuthProvider {
  abstract start(ctx: AuthContext): Promise<void>;
  abstract validate(ctx: AuthContext): Promise<void>;

  async loadAuthOrganization(
    ctx: AuthContext,
    email: string
  ): Promise<{ organization: Organization; isNew: boolean }> {
    // If we have an organization or can find one by domain
    // we use that to start
    const isOrgOwner = ctx.state.organization?.domain === email;
    const organization =
      ctx.state.organization && isOrgOwner
        ? ctx.state.organization
        : await getOrganizationByDomain(email);

    if (organization) return { organization, isNew: false };

    // If we are not in multi-org mode we always fall back to
    // a single organization
    // if (!App.main.env.config.multiOrg) {
    //   organization = await getDefaultOrganization();
    // }
    // if (organization) return { organization, isNew: false };

    // If there is no organization at all or are in multi-org mode
    // and have no org for the user, create one

    return {
      organization: await createOrganization(email),
      isNew: true,
    };
  }

  async login(
    { domain, role, ...params }: AuthAdminParams,
    ctx: AuthContext,
    redirect?: string
  ): Promise<OAuthResponse> {
    // Check for existing, otherwise create one
    let admin = await getAdminByEmail(params.email);
    const { organization, isNew } = await this.loadAuthOrganization(
      ctx,
      params.email
    );

    if (!admin) {
      admin = await Admin.insertAndFetch({
        ...params,
        organization_id: organization.id,
        role: role || (isNew ? "owner" : "member"),
      });
    }

    const subscriptionObj = await StripeSubscription.first((qb) =>
      qb.where("organization_id", organization.id)
    );

    // Create subscription Instance if non-existent
    if (!subscriptionObj) {
      const sub = await StripeSubscription.insertAndFetch({
        organization_id: organization.id,
      });
    }

    return await this.generateOauth(admin, ctx, redirect);
  }

  private async generateOauth(
    admin: Admin,
    ctx?: AuthContext,
    redirect?: string
  ) {
    const oauth = await generateAccessToken(admin, ctx);

    if (ctx) {
      setTokenCookies(ctx, oauth);
      ctx.redirect(redirect || App.main.env.baseUrl);
    }
    return oauth;
  }

  async logout(params: Pick<AdminParams, "email">, ctx: AuthContext) {
    console.log(params, ctx);
    // not sure how we find the refresh token for a given session atm
    // revokeRefreshToken()
  }
}
