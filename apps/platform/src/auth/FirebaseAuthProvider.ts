import { AuthTypeConfig } from "./Auth";
import AuthProvider, { AuthContext } from "./AuthProvider";
import { RequestError } from "../core/errors";
import AuthError from "./AuthError";
import * as admin from "firebase-admin";
import { getAdminByEmail } from "./AdminRepository";

export interface FirebaseConfig extends AuthTypeConfig {
  driver: "firebase";
  name: string;
  credentials: admin.ServiceAccount;
}

export default class FirebaseAuthProvider extends AuthProvider {
  private config: FirebaseConfig;
  private firebaseAdmin: admin.app.App;

  constructor(config: FirebaseConfig) {
    super();
    this.config = config;

    // More detailed credential logging
    console.log("Server Firebase Config (Detailed):", {
      projectId: this.config.credentials.projectId || "(missing)",
      privateKey: this.config.credentials.privateKey
        ? `First 20 chars: ${this.config.credentials.privateKey.substring(
            0,
            20
          )}... Last 20 chars: ...${this.config.credentials.privateKey.substring(
            this.config.credentials.privateKey.length - 20
          )}`
        : "(missing)",
      clientEmail: this.config.credentials.clientEmail || "(missing)",
    });

    try {
      // Only delete auth-related apps to avoid conflicts with storage apps
      const existingApps = admin.apps.filter((app) => app !== null);
      console.log(`Found ${existingApps.length} existing Firebase apps`);

      for (const app of existingApps) {
        try {
          // Skip storage apps to prevent breaking active storage operations
          if (app?.name?.startsWith('storage-')) {
            console.log(`Skipping storage Firebase app: ${app?.name}`);
            continue;
          }

          console.log(`Deleting Firebase app: ${app?.name}`);
          app?.delete();
        } catch (err) {
          console.error(`Error deleting Firebase app ${app?.name}:`, err);
        }
      }

      // Ensure the credentials object is properly formatted
      const serviceAccount: admin.ServiceAccount = {
        projectId: this.config.credentials.projectId,
        privateKey: this.config.credentials.privateKey,
        clientEmail: this.config.credentials.clientEmail,
      };

      // Initialize a new Firebase Admin SDK instance
      console.log("Initializing new Firebase Admin SDK instance");
      try {
        this.firebaseAdmin = admin.initializeApp({
          credential: admin.credential.cert(serviceAccount),
        });
        console.log(
          "Firebase Admin initialized successfully with default app name"
        );
      } catch (initError) {
        console.error("Failed to initialize default app:", initError);
        // Try with a named app as fallback
        this.firebaseAdmin = admin.initializeApp(
          {
            credential: admin.credential.cert(serviceAccount),
          },
          `admin-${Date.now()}`
        );
        console.log(
          "Firebase Admin initialized successfully with fallback app name:",
          this.firebaseAdmin.name
        );
      }
    } catch (error) {
      console.error("Error initializing Firebase Admin (DETAILED):", error);
      // Log more details about the error
      if (error instanceof Error) {
        console.error("Error name:", error.name);
        console.error("Error message:", error.message);
        console.error("Error stack:", error.stack);
      }
      throw error;
    }
  }

  async start(ctx: AuthContext): Promise<void> {
    // Since Firebase authentication is handled client-side,
    // you can redirect to your login page or simply return.
    ctx.redirect("/login");
  }

  async validate(ctx: AuthContext): Promise<void> {
    const redirect = (ctx.request.query.r || "/") as string;
    try {
      const { idToken } = ctx.request.body;
      if (!idToken) {
        throw new RequestError(AuthError.MissingCredentials);
      }

      console.log(
        "Verifying Firebase ID token... First 20 chars:",
        idToken.substring(0, 20) + "..."
      );

      // Log Firebase app details before verification
      console.log(
        "Using Firebase app for verification:",
        this.firebaseAdmin.name
      );

      // Verify the Firebase ID token with added error handling
      try {
        // Add a safety check for token format
        if (!idToken.includes(".") || idToken.split(".").length !== 3) {
          console.error(
            "Invalid token format: token does not appear to be a proper JWT"
          );
          throw new Error("Invalid token format");
        }

        const decodedToken = await this.firebaseAdmin
          .auth()
          .verifyIdToken(idToken, true); // Setting checkRevoked to true for extra security

        console.log("Token verified successfully, email:", decodedToken.email);
        console.log(
          "Token payload:",
          JSON.stringify({
            uid: decodedToken.uid,
            email: decodedToken.email,
            auth_time: decodedToken.auth_time,
            iat: decodedToken.iat,
            exp: decodedToken.exp,
            iss: decodedToken.iss,
            aud: decodedToken.aud,
            sub: decodedToken.sub,
          })
        );

        // Check if this is the first user (no existing admins)
        const existingAdmin = await getAdminByEmail(decodedToken.email!);
        const isFirstUser = !existingAdmin;

        // Log in the user with the email from Firebase
        await this.login(
          {
            email: decodedToken.email!,
            first_name: decodedToken.name || "User",
            image_url: decodedToken.picture,
            domain: decodedToken.firebase?.sign_in_provider,
            // Set role to 'owner' for first user, 'admin' for others
            role: isFirstUser ? "owner" : "admin",
          },
          ctx,
          redirect
        );
      } catch (verifyError) {
        console.error("Token verification failed:", verifyError);
        if (verifyError instanceof Error) {
          console.error("Error name:", verifyError.name);
          console.error("Error message:", verifyError.message);
          console.error("Error stack:", verifyError.stack);
        }
        throw verifyError;
      }
    } catch (error) {
      console.error("Firebase auth error:", error);
      throw new RequestError(AuthError.InvalidCredentials);
    }
  }
}
