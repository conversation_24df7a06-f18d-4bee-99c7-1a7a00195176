import { logger } from "../config/logger";
import App from "../app";
import Campaign from "./Campaign";
import { campaignProgress } from "./CampaignService";

export interface CampaignAnalysis {
  id: number;
  name: string;
  type: string;
  channel: string;
  state: string;
  metrics: {
    sent: number;
    delivered: number;
    opens: number;
    clicks: number;
    conversions: number;
    open_rate: number;
    click_rate: number;
    conversion_rate: number;
    bounce_rate: number;
  };
  top_links: Array<{
    url: string;
    clicks: number;
    conversion_rate: number;
  }>;
  performance_trend: Array<{
    date: string;
    opens: number;
    clicks: number;
  }>;
  benchmarks: {
    industry_open_rate: number;
    industry_click_rate: number;
    previous_campaigns_open_rate: number;
    previous_campaigns_click_rate: number;
  };
  recommendations: string[];
}

export interface CampaignSummary {
  id: number;
  name: string;
  channel: string;
  sent_at: string;
  metrics: {
    sent: number;
    opens: number;
    clicks: number;
    open_rate: number;
    click_rate: number;
  };
}

export class CampaignAnalyticsService {
  /**
   * Get detailed analysis for a specific campaign
   */
  static async getCampaignResults(
    campaignId: number
  ): Promise<CampaignAnalysis> {
    try {
      // Get the campaign
      const campaign = await Campaign.first((qb) =>
        qb.where({ id: campaignId })
      );

      if (!campaign) {
        throw new Error(`Campaign ${campaignId} not found`);
      }

      // Get campaign progress from CampaignService
      const progress = await campaignProgress(campaignId);

      // Get bounce data - not included in campaignProgress
      const bounceData = await App.main
        .db("campaign_sends")
        .where({ campaign_id: campaignId })
        .count("* as total")
        .select([
          App.main.db.raw(
            "SUM(CASE WHEN state = 'bounced' THEN 1 ELSE 0 END) as bounces"
          ),
        ])
        .first();

      const bounces = Number(bounceData?.bounces) || 0;

      // Calculate rates
      const sent = progress.total;
      const delivered = progress.sent;
      const { opens, clicks } = progress;

      const openRate = delivered > 0 ? opens / delivered : 0;
      const clickRate = opens > 0 ? clicks / opens : 0;
      const bounceRate = sent > 0 ? bounces / sent : 0;

      // Get top links (placeholder - in a real implementation this would query actual link data)
      const topLinks: Array<{
        url: string;
        clicks: number;
        conversion_rate: number;
      }> = [];

      // Get performance trend (placeholder)
      const performanceTrend: Array<{
        date: string;
        opens: number;
        clicks: number;
      }> = [];

      // Get benchmark data (placeholder with realistic values)
      const benchmarks = {
        industry_open_rate: 0.18, // 18% is a typical industry average for email
        industry_click_rate: 0.02, // 2% is a typical industry average for email
        previous_campaigns_open_rate: 0.2,
        previous_campaigns_click_rate: 0.025,
      };

      // Generate recommendations based on performance
      const recommendations = [];
      if (openRate < benchmarks.industry_open_rate) {
        recommendations.push(
          "Consider improving subject lines to increase open rates"
        );
      }
      if (clickRate < benchmarks.industry_click_rate) {
        recommendations.push(
          "Review call-to-action placement and messaging to improve click rates"
        );
      }
      if (bounceRate > 0.05) {
        // More than 5% bounce rate is concerning
        recommendations.push("Clean your contact list to reduce bounce rates");
      }

      return {
        id: campaign.id,
        name: campaign.name,
        type: campaign.type,
        channel: campaign.channel,
        state: campaign.state,
        metrics: {
          sent,
          delivered,
          opens,
          clicks,
          conversions: 0, // Placeholder - would need to be calculated from actual conversion data
          open_rate: openRate,
          click_rate: clickRate,
          conversion_rate: 0, // Placeholder
          bounce_rate: bounceRate,
        },
        top_links: topLinks,
        performance_trend: performanceTrend,
        benchmarks,
        recommendations,
      };
    } catch (error) {
      logger.error("Error getting campaign results:", error);
      throw error;
    }
  }

  /**
   * Get summary of recent campaign performance
   */
  static async getRecentCampaignPerformance(
    locationId: number
  ): Promise<CampaignSummary[]> {
    try {
      // Get the most recent campaigns for this location
      const campaigns = await Campaign.query()
        .where({
          location_id: locationId,
        })
        .where("state", "in", ["finished", "aborted"])
        .orderBy("created_at", "desc")
        .limit(5);

      // Map to summary format
      const summaries = await Promise.all(
        campaigns.map(async (campaign: Campaign) => {
          // Get campaign progress from CampaignService
          const progress = await campaignProgress(campaign.id);

          // Calculate rates
          const sent = progress.total;
          const { opens, clicks } = progress;
          const openRate = sent > 0 ? opens / sent : 0;
          const clickRate = opens > 0 ? clicks / opens : 0;

          return {
            id: campaign.id,
            name: campaign.name,
            channel: campaign.channel,
            sent_at: campaign.send_at?.toString() || "",
            metrics: {
              sent,
              opens,
              clicks,
              open_rate: openRate,
              click_rate: clickRate,
            },
          };
        })
      );

      return summaries;
    } catch (error) {
      logger.error("Error getting recent campaign performance:", error);
      return [];
    }
  }

  /**
   * Format campaign results for agent consumption
   */
  static formatCampaignResultsForAgent(campaignId: number): Promise<string> {
    return this.getCampaignResults(campaignId)
      .then((results) => {
        return `
Campaign: ${results.name} (${results.channel})
Status: ${results.state}
Performance:
- Sent: ${results.metrics.sent}
- Opens: ${results.metrics.opens} (${(results.metrics.open_rate * 100).toFixed(
          1
        )}%)
- Clicks: ${results.metrics.clicks} (${(
          results.metrics.click_rate * 100
        ).toFixed(1)}%)
- Bounce Rate: ${(results.metrics.bounce_rate * 100).toFixed(1)}%

Benchmarks:
- Industry Open Rate: ${(results.benchmarks.industry_open_rate * 100).toFixed(
          1
        )}%
- Your Open Rate: ${(results.metrics.open_rate * 100).toFixed(1)}%
- Industry Click Rate: ${(results.benchmarks.industry_click_rate * 100).toFixed(
          1
        )}%
- Your Click Rate: ${(results.metrics.click_rate * 100).toFixed(1)}%

Recommendations:
${results.recommendations.map((r) => `- ${r}`).join("\n")}
        `;
      })
      .catch((error) => {
        logger.error("Error formatting campaign results for agent:", error);
        return "Unable to retrieve campaign results at this time.";
      });
  }
}
