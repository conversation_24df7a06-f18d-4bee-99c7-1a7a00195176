import Router from "@koa/router";
import { JSONSchemaType, validate } from "../core/validate";
import Campaign, {
  CampaignCreateParams,
  CampaignUpdateParams,
} from "./Campaign";
import {
  archiveCampaign,
  campaignPreview,
  createCampaign,
  deleteCampaign,
  duplicateCampaign,
  getCampaign,
  getCampaignUsers,
  pagedCampaigns,
  updateCampaign,
} from "./CampaignService";
import { searchParamsSchema, SearchSchema } from "../core/searchParams";
import { extractQueryParams } from "../utilities";
import { LocationState } from "../auth/AuthMiddleware";
import { locationRoleMiddleware } from "../locations/LocationService";
import { Context, Next } from "koa";
import CampaignTriggerSendJob, {
  CampaignTriggerSendParams,
} from "./CampaignTriggerSendJob";

const router = new Router<LocationState & { campaign?: Campaign }>({
  prefix: "/campaigns",
});

const checkCampaignId = async (value: string, ctx: Context, next: Next) => {
  ctx.state.campaign = await getCampaign(
    parseInt(value, 10),
    ctx.state.location.id
  );
  if (!ctx.state.campaign) {
    ctx.throw(404);
  }
  return await next();
};

router.use(locationRoleMiddleware("editor"));

/**
 * @swagger
 * tags:
 *   name: Campaign
 *   description: Campaign management endpoints
 */

/**
 * @swagger
 * /campaigns:
 *   get:
 *     summary: List Campaigns
 *     description: Retrieves a paginated list of campaigns for the current location
 *     tags: [Campaign]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort direction
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Campaign'
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 */
router.get("/", async (ctx) => {
  const searchSchema = SearchSchema("campaignSearchSchema", {
    sort: "id",
    direction: "desc",
  });
  const params = extractQueryParams(ctx.query, searchSchema);
  ctx.body = await pagedCampaigns(params, ctx.state.location.id);
});

export const campaignCreateParams: JSONSchemaType<CampaignCreateParams> = {
  $id: "campaignCreate",
  type: "object",
  required: ["type", "subscription_id", "provider_id"],
  properties: {
    type: {
      type: "string",
      enum: ["blast", "trigger"],
    },
    name: {
      type: "string",
    },
    channel: {
      type: "string",
      enum: ["email", "text", "push", "webhook"],
    },
    subscription_id: {
      type: "integer",
    },
    provider_id: {
      type: "integer",
    },
    list_ids: {
      type: "array",
      items: { type: "integer" },
      nullable: true,
    },
    exclusion_list_ids: {
      type: "array",
      items: { type: "integer" },
      nullable: true,
    },
    send_in_user_timezone: {
      type: "boolean",
      nullable: true,
    },
    send_at: {
      type: "string",
      format: "date-time",
      nullable: true,
    },
    tags: {
      type: "array",
      items: {
        type: "string",
      },
      nullable: true,
    },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /campaigns:
 *   post:
 *     summary: Create Campaign
 *     description: Creates a new campaign in the current location
 *     tags: [Campaign]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CampaignCreateParams'
 *     responses:
 *       200:
 *         description: Campaign created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Campaign'
 *       400:
 *         description: Invalid input
 */
router.post("/", async (ctx) => {
  const payload = validate(campaignCreateParams, ctx.request.body);

  // Check if campaign with same name exists
  if (payload.name) {
    const existingCampaign = await Campaign.first((b) =>
      b.where({
        location_id: ctx.state.location.id,
        name: payload.name,
        deleted_at: null,
      })
    );

    if (existingCampaign) {
      ctx.body = existingCampaign;
      return;
    }
  }

  ctx.body = await createCampaign(ctx.state.location.id, payload);
});

router.param("campaignId", checkCampaignId);

/**
 * @swagger
 * /campaigns/{campaignId}:
 *   get:
 *     summary: Get Campaign by ID
 *     description: Retrieves a specific campaign by its identifier
 *     tags: [Campaign]
 *     parameters:
 *       - in: path
 *         name: campaignId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Campaign identifier
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Campaign'
 *       404:
 *         description: Campaign not found
 */
router.get("/:campaignId", async (ctx) => {
  ctx.body = ctx.state.campaign!;
});

const campaignUpdateParams: JSONSchemaType<Partial<CampaignUpdateParams>> = {
  $id: "campaignUpdate",
  type: "object",
  properties: {
    name: {
      type: "string",
      nullable: true,
    },
    subscription_id: {
      type: "integer",
      nullable: true,
    },
    provider_id: {
      type: "integer",
      nullable: true,
    },
    state: {
      type: "string",
      enum: ["draft", "scheduled", "finished", "aborted"],
      nullable: true,
    },
    list_ids: {
      type: "array",
      items: { type: "integer" },
      nullable: true,
    },
    exclusion_list_ids: {
      type: "array",
      items: { type: "integer" },
      nullable: true,
    },
    send_in_user_timezone: {
      type: "boolean",
      nullable: true,
    },
    send_at: {
      type: "string",
      format: "date-time",
      nullable: true,
    },
    tags: {
      type: "array",
      items: {
        type: "string",
      },
      nullable: true,
    },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /campaigns/{campaignId}:
 *   patch:
 *     summary: Update Campaign
 *     description: Updates an existing campaign
 *     tags: [Campaign]
 *     parameters:
 *       - in: path
 *         name: campaignId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Campaign identifier
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CampaignUpdateParams'
 *     responses:
 *       200:
 *         description: Campaign updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Campaign'
 *       404:
 *         description: Campaign not found
 */
router.patch("/:campaignId", async (ctx) => {
  const payload = validate(campaignUpdateParams, ctx.request.body);
  ctx.body = await updateCampaign(
    ctx.state.campaign!.id,
    ctx.state.location.id,
    payload
  );
});

/**
 * @swagger
 * /campaigns/{campaignId}/users:
 *   get:
 *     summary: Get Campaign Users
 *     description: Retrieves users associated with a campaign
 *     tags: [Campaign]
 *     parameters:
 *       - in: path
 *         name: campaignId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Campaign identifier
 *     responses:
 *       200:
 *         description: Campaign users retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/User'
 *       404:
 *         description: Campaign not found
 */
router.get("/:campaignId/users", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await getCampaignUsers(
    ctx.state.campaign!.id,
    params,
    ctx.state.location.id
  );
});

/**
 * @swagger
 * /campaigns/{campaignId}:
 *   delete:
 *     summary: Delete Campaign
 *     description: Deletes a campaign
 *     tags: [Campaign]
 *     parameters:
 *       - in: path
 *         name: campaignId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Campaign identifier
 *     responses:
 *       204:
 *         description: Campaign deleted successfully
 *       404:
 *         description: Campaign not found
 */
router.delete("/:campaignId", async (ctx) => {
  const { id, location_id, deleted_at } = ctx.state.campaign!;
  if (deleted_at) {
    await deleteCampaign(id, location_id);
  } else {
    await archiveCampaign(id, location_id);
  }
  ctx.body = true;
});

/**
 * @swagger
 * /campaigns/{campaignId}/duplicate:
 *   post:
 *     summary: Duplicate Campaign
 *     description: Creates a copy of an existing campaign
 *     tags: [Campaign]
 *     parameters:
 *       - in: path
 *         name: campaignId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Campaign identifier
 *     responses:
 *       200:
 *         description: Campaign duplicated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Campaign'
 *       404:
 *         description: Campaign not found
 */
router.post("/:campaignId/duplicate", async (ctx) => {
  ctx.body = await duplicateCampaign(ctx.state.campaign!);
});

/**
 * @swagger
 * /campaigns/{campaignId}/preview:
 *   get:
 *     summary: Preview Campaign
 *     description: Generates a preview of the campaign
 *     tags: [Campaign]
 *     parameters:
 *       - in: path
 *         name: campaignId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Campaign identifier
 *     responses:
 *       200:
 *         description: Campaign preview generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 preview:
 *                   type: string
 *       404:
 *         description: Campaign not found
 */
router.get("/:campaignId/preview", async (ctx) => {
  ctx.body = await campaignPreview(ctx.state.location, ctx.state.campaign!);
});

type CampaignTriggerSchema = Omit<
  CampaignTriggerSendParams,
  "location_id" | "campaign_id"
>;

const campaignTriggerParams: JSONSchemaType<CampaignTriggerSchema> = {
  $id: "campaignTrigger",
  type: "object",
  required: ["user", "event"],
  properties: {
    user: {
      type: "object",
      required: ["external_id"],
      properties: {
        external_id: { type: "string" },
        email: { type: "string", nullable: true },
        phone: { type: "string", nullable: true },
        device_token: { type: "string", nullable: true },
        timezone: { type: "string", nullable: true },
        locale: { type: "string", nullable: true },
      },
      additionalProperties: true,
    },
    event: {
      type: "object",
      additionalProperties: true,
    },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /campaigns/{campaignId}/trigger:
 *   post:
 *     summary: Trigger Campaign Send
 *     description: Triggers the sending of a campaign
 *     tags: [Campaign]
 *     parameters:
 *       - in: path
 *         name: campaignId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Campaign identifier
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CampaignTriggerSchema'
 *     responses:
 *       200:
 *         description: Campaign send triggered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       404:
 *         description: Campaign not found
 */
router.post("/:campaignId/trigger", async (ctx) => {
  const location = ctx.state.location;
  const payload = validate(campaignTriggerParams, ctx.request.body);

  await CampaignTriggerSendJob.from({
    ...payload,
    location_id: location.id,
    campaign_id: ctx.state.campaign!.id,
  }).queue();

  ctx.body = { message: "Campaign send triggered successfully" };
});

export default router;
