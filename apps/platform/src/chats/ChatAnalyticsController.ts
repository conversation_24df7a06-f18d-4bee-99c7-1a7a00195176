import { Request, Response, NextFunction } from "express";
import App from "../app";
import { logger } from "../config/logger";

export class ChatAnalyticsController {
  // eslint-disable-next-line no-useless-constructor
  constructor(private app: App) {}

  getChatSummary = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { chatId } = req.params;

      if (!chatId) {
        res.status(400).json({ error: "Chat ID is required" });
        return;
      }

      const summary = await this.app.chatAnalyticsService.generateChatSummary(
        chatId
      );
      res.json(summary);
    } catch (error) {
      logger.error("Error getting chat summary:", error);
      res.status(500).json({ error: "Failed to get chat summary" });
    }
  };

  getChatInsights = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { chatId } = req.params;

      if (!chatId) {
        res.status(400).json({ error: "Chat ID is required" });
        return;
      }

      const insights = await this.app.chatAnalyticsService.generateChatInsights(
        chatId
      );
      res.json(insights);
    } catch (error) {
      logger.error("Error getting chat insights:", error);
      res.status(500).json({ error: "Failed to get chat insights" });
    }
  };

  getLocationInsights = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { locationId } = req.params;
      const parsedLocationId = parseInt(locationId, 10);

      if (isNaN(parsedLocationId)) {
        res.status(400).json({ error: "Valid location ID is required" });
        return;
      }

      const insights = await this.app.chatAnalyticsService.generateBulkInsights(
        parsedLocationId
      );

      // Convert Map to a more JSON-friendly format
      const formattedInsights = Array.from(insights.entries()).map(
        ([chatId, chatInsights]) => ({
          chatId,
          insights: chatInsights,
        })
      );

      res.json(formattedInsights);
    } catch (error) {
      logger.error("Error getting location insights:", error);
      res.status(500).json({ error: "Failed to get location insights" });
    }
  };

  refreshChatAnalytics = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { chatId } = req.params;

      if (!chatId) {
        res.status(400).json({ error: "Chat ID is required" });
        return;
      }

      // Generate both summary and insights in parallel
      const [summary, insights] = await Promise.all([
        this.app.chatAnalyticsService.generateChatSummary(chatId),
        this.app.chatAnalyticsService.generateChatInsights(chatId),
      ]);

      res.json({
        summary,
        insights,
      });
    } catch (error) {
      logger.error("Error refreshing chat analytics:", error);
      res.status(500).json({ error: "Failed to refresh chat analytics" });
    }
  };
}
