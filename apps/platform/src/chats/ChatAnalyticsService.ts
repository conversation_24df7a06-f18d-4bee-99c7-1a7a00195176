import { Message, Chat, ChatSummary, ChatInsight } from "./models/types";
import App from "../app";
import { logger } from "../config/logger";

export class ChatAnalyticsService {
  private readonly SUMMARY_REFRESH_INTERVAL = 1000 * 60 * 60; // 1 hour
  private readonly INSIGHT_BATCH_SIZE = 50;
  // eslint-disable-next-line no-useless-constructor
  constructor(private app: App) {}

  async generateChatSummary(chatId: string): Promise<ChatSummary> {
    try {
      // Check if we have a recent cached summary
      const cachedSummary = await this.getCachedSummary(chatId);
      if (cachedSummary) {
        return cachedSummary;
      }

      // Get all messages for the chat
      const messages = await this.app
        .db("messages")
        .where("chat_id", chatId)
        .orderBy("timestamp", "asc");

      if (!messages.length) {
        throw new Error("No messages found for chat");
      }

      // Generate summary using AI
      const summary = await this.app.chatAIService.generateChatSummary(
        messages
      );

      // Extract key metrics
      const metrics = await this.extractChatMetrics(messages);

      // Create structured summary
      const chatSummary: ChatSummary = {
        title: await this.generateTitle(messages),
        description: summary,
        topics: await this.extractTopics(messages),
        metrics: {
          customerLifetimeValue: metrics.clv,
          campaignROI: metrics.roi,
          retentionRate: metrics.retention,
        },
      };

      // Cache the summary
      await this.cacheSummary(chatId, chatSummary);

      return chatSummary;
    } catch (error) {
      logger.error("Error generating chat summary:", error);
      throw new Error("Failed to generate chat summary");
    }
  }

  async generateChatInsights(chatId: string): Promise<ChatInsight[]> {
    try {
      const messages = await this.app
        .db("messages")
        .where("chat_id", chatId)
        .orderBy("timestamp", "asc");

      if (!messages.length) {
        return [];
      }

      // Process messages in batches for analysis
      const batches = this.chunkArray(messages, this.INSIGHT_BATCH_SIZE);
      const insights: ChatInsight[] = [];

      for (const batch of batches) {
        const batchInsights = await this.analyzeBatch(batch);
        insights.push(...batchInsights);
      }

      // Deduplicate and sort insights by impact
      return this.deduplicateInsights(insights).sort(
        (a, b) => this.getImpactScore(b.impact) - this.getImpactScore(a.impact)
      );
    } catch (error) {
      logger.error("Error generating chat insights:", error);
      throw new Error("Failed to generate chat insights");
    }
  }

  async generateBulkInsights(
    locationId: number
  ): Promise<Map<string, ChatInsight[]>> {
    try {
      // Get all active chats for the location
      const chats = await this.app.db("chats").where({
        location_id: locationId,
        status: "active",
      });

      // Generate insights for each chat in parallel
      const insightPromises = chats.map(async (chat) => ({
        chatId: chat.chat_id,
        insights: await this.generateChatInsights(chat.chat_id),
      }));

      const results = await Promise.all(insightPromises);

      // Convert to Map for easier access
      return new Map(results.map(({ chatId, insights }) => [chatId, insights]));
    } catch (error) {
      logger.error("Error generating bulk insights:", error);
      throw new Error("Failed to generate bulk insights");
    }
  }

  private async getCachedSummary(chatId: string): Promise<ChatSummary | null> {
    try {
      const cached = await this.app.redis.get(`chat:summary:${chatId}`);
      if (!cached) return null;

      const { summary, timestamp } = JSON.parse(cached);

      // Check if summary is still fresh
      if (Date.now() - timestamp < this.SUMMARY_REFRESH_INTERVAL) {
        return summary;
      }

      return null;
    } catch (error) {
      logger.error("Error getting cached summary:", error);
      return null;
    }
  }

  private async cacheSummary(
    chatId: string,
    summary: ChatSummary
  ): Promise<void> {
    try {
      await this.app.redis.set(
        `chat:summary:${chatId}`,
        JSON.stringify({
          summary,
          timestamp: Date.now(),
        }),
        "EX",
        this.SUMMARY_REFRESH_INTERVAL / 1000
      );
    } catch (error) {
      logger.error("Error caching summary:", error);
    }
  }

  private async generateTitle(messages: Message[]): Promise<string> {
    try {
      return await this.app.chatAIService.generateTitle(messages);
    } catch (error) {
      logger.error("Error generating title:", error);
      return "Chat Conversation";
    }
  }

  private async extractTopics(messages: Message[]): Promise<string[]> {
    try {
      return await this.app.chatAIService.extractTopics(messages);
    } catch (error) {
      logger.error("Error extracting topics:", error);
      return [];
    }
  }

  private async extractChatMetrics(messages: Message[]): Promise<{
    clv: number | null;
    roi: number | null;
    retention: number | null;
  }> {
    try {
      return await this.app.chatAIService.extractChatMetrics(messages);
    } catch (error) {
      logger.error("Error extracting metrics:", error);
      return { clv: null, roi: null, retention: null };
    }
  }

  private async analyzeBatch(messages: Message[]): Promise<ChatInsight[]> {
    try {
      return await this.app.chatAIService.analyzeChatSegment(messages);
    } catch (error) {
      logger.error("Error analyzing message batch:", error);
      return [];
    }
  }

  private deduplicateInsights(insights: ChatInsight[]): ChatInsight[] {
    const seen = new Set<string>();
    return insights.filter((insight) => {
      const key = `${insight.title}:${insight.type}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  private getImpactScore(impact: "high" | "medium" | "low"): number {
    const scores = { high: 3, medium: 2, low: 1 };
    return scores[impact];
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}
