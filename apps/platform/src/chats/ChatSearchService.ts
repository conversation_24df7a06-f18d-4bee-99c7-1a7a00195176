import { Message, Chat, SearchParams, SearchResult } from "./models/types";
import App from "../app";
import { logger } from "../config/logger";
import { Database } from "../config/database";

export class ChatSearchService {
  private readonly DEFAULT_PAGE_SIZE = 20;
  private readonly MAX_PAGE_SIZE = 100;

  // eslint-disable-next-line no-useless-constructor
  constructor(private app: App) {}

  async searchMessages(params: SearchParams): Promise<SearchResult> {
    try {
      const {
        query,
        chatId,
        agentId,
        startDate,
        endDate,
        page = 1,
        pageSize = this.DEFAULT_PAGE_SIZE,
        sortBy = "timestamp",
        sortOrder = "desc",
      } = params;

      // Validate and adjust page size
      const validatedPageSize = Math.min(pageSize, this.MAX_PAGE_SIZE);
      const offset = (page - 1) * validatedPageSize;

      // Build base query
      let queryBuilder = this.app
        .db("messages")
        .select("messages.*")
        .where((builder) => {
          if (chatId) {
            builder.where("chat_id", chatId);
          }
          if (agentId) {
            builder.where("agent_id", agentId);
          }
          if (startDate) {
            builder.where("timestamp", ">=", startDate);
          }
          if (endDate) {
            builder.where("timestamp", "<=", endDate);
          }
          if (query) {
            builder.where((subBuilder) => {
              subBuilder
                .whereRaw("LOWER(content) LIKE ?", [`%${query.toLowerCase()}%`])
                .orWhereRaw("LOWER(metadata::text) LIKE ?", [
                  `%${query.toLowerCase()}%`,
                ]);
            });
          }
        });

      // Get total count for pagination
      const [{ count }] = await queryBuilder.clone().count("* as count");
      const total = parseInt(count as string, 10);
      const totalPages = Math.ceil(total / validatedPageSize);

      // Add sorting and pagination
      if (sortBy === "relevance" && query) {
        // For relevance sorting, we use a similarity score
        queryBuilder = this.addRelevanceSort(queryBuilder, query, sortOrder);
      } else {
        queryBuilder.orderBy("timestamp", sortOrder);
      }

      const messages = await queryBuilder
        .limit(validatedPageSize)
        .offset(offset);

      return {
        messages,
        total,
        page,
        pageSize: validatedPageSize,
        totalPages,
        hasMore: page < totalPages,
      };
    } catch (error) {
      logger.error("Error searching messages:", error);
      throw new Error("Failed to search messages");
    }
  }

  private addRelevanceSort(
    queryBuilder: Database.QueryBuilder,
    query: string,
    sortOrder: "asc" | "desc"
  ): Database.QueryBuilder {
    // Calculate similarity score using trigram similarity
    // Note: This requires the pg_trgm extension to be enabled
    return queryBuilder
      .select(
        this.app.db.raw(
          "similarity(LOWER(content), LOWER(?)) as relevance_score",
          [query]
        )
      )
      .orderBy("relevance_score", sortOrder);
  }

  async getMessageContext(
    messageId: number,
    contextSize: number = 5
  ): Promise<Message[]> {
    try {
      const message = await this.app
        .db("messages")
        .where("id", messageId)
        .first();

      if (!message) {
        throw new Error("Message not found");
      }

      // Get messages before and after the target message
      const [beforeMessages, afterMessages] = await Promise.all([
        this.app
          .db("messages")
          .where("chat_id", message.chat_id)
          .where("timestamp", "<", message.timestamp)
          .orderBy("timestamp", "desc")
          .limit(contextSize),

        this.app
          .db("messages")
          .where("chat_id", message.chat_id)
          .where("timestamp", ">", message.timestamp)
          .orderBy("timestamp", "asc")
          .limit(contextSize),
      ]);

      // Combine and sort messages
      return [...beforeMessages.reverse(), message, ...afterMessages];
    } catch (error) {
      logger.error("Error getting message context:", error);
      throw new Error("Failed to get message context");
    }
  }

  async getMessagesByDateRange(
    chatId: string,
    startDate: Date,
    endDate: Date
  ): Promise<Message[]> {
    try {
      return await this.app
        .db("messages")
        .where("chat_id", chatId)
        .whereBetween("timestamp", [startDate, endDate])
        .orderBy("timestamp", "asc");
    } catch (error) {
      logger.error("Error getting messages by date range:", error);
      throw new Error("Failed to get messages by date range");
    }
  }

  async searchAcrossChats(
    query: string,
    locationId: number,
    limit: number = 10
  ): Promise<Array<{ chat: Chat; messages: Message[] }>> {
    try {
      // First, find chats with matching messages
      const matchingChats = await this.app
        .db("chats")
        .distinct("chats.*")
        .join("messages", "chats.chat_id", "messages.chat_id")
        .where("chats.location_id", locationId)
        .whereRaw("LOWER(messages.content) LIKE ?", [
          `%${query.toLowerCase()}%`,
        ])
        .limit(limit);

      // Then, for each chat, get the matching messages
      const results = await Promise.all(
        matchingChats.map(async (chat) => ({
          chat,
          messages: await this.app
            .db("messages")
            .where("chat_id", chat.chat_id)
            .whereRaw("LOWER(content) LIKE ?", [`%${query.toLowerCase()}%`])
            .orderBy("timestamp", "desc")
            .limit(5), // Get up to 5 matching messages per chat
        }))
      );

      return results;
    } catch (error) {
      logger.error("Error searching across chats:", error);
      throw new Error("Failed to search across chats");
    }
  }
}
