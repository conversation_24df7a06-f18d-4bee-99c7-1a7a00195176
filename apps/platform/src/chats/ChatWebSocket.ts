import WebSocket from "ws";
import { WebSocketMessage } from "./models/types";
import { logger } from "../config/logger";
import { Server } from "http";

interface ExtendedWebSocket extends WebSocket {
  locationId?: string;
  userId?: string;
  isAlive: boolean;
}

export class ChatWebSocket {
  private pingInterval: NodeJS.Timeout;
  public wss: WebSocket.Server;

  constructor(server: Server) {
    this.wss = new WebSocket.Server({ server });
    this.setupWebSocketServer();
    this.pingInterval = setInterval(() => this.ping(), 30000);
  }

  private setupWebSocketServer() {
    this.wss.on("connection", (ws: ExtendedWebSocket, req) => {
      // Extract location ID from URL query params
      const url = new URL(req.url!, `http://${req.headers.host}`);
      ws.locationId = url.searchParams.get("locationId") || undefined;
      ws.userId = url.searchParams.get("userId") || undefined;
      ws.isAlive = true;

      if (!ws.locationId) {
        this.sendError(ws, "Location ID not provided");
        ws.close();
        return;
      }

      logger.info(
        { locationId: ws.locationId, userId: ws.userId },
        "WebSocket client connected"
      );

      ws.on("message", async (data: WebSocket.Data) => {
        try {
          const message: WebSocketMessage = JSON.parse(data.toString());
          await this.handleMessage(ws, message);
        } catch (error) {
          logger.error("Error handling WebSocket message:", error);
          this.sendError(ws, "Invalid message format");
        }
      });

      ws.on("pong", () => {
        ws.isAlive = true;
      });

      ws.on("close", () => {
        logger.info(
          { locationId: ws.locationId, userId: ws.userId },
          "WebSocket client disconnected"
        );
      });
    });
  }

  private ping() {
    (this.wss.clients as Set<ExtendedWebSocket>).forEach((ws) => {
      if (!ws.isAlive) {
        logger.info(
          { locationId: ws.locationId, userId: ws.userId },
          "Terminating inactive WebSocket connection"
        );
        return ws.terminate();
      }

      ws.isAlive = false;
      ws.ping();
    });
  }

  private async handleMessage(
    ws: ExtendedWebSocket,
    message: WebSocketMessage
  ) {
    if (!ws.locationId) {
      return this.sendError(ws, "Location ID not provided");
    }

    switch (message.type) {
      case "typing":
        this.broadcast({
          type: "typing",
          chat_id: message.payload.chat_id,
          location_id: Number(ws.locationId),
          timestamp: new Date().toISOString(),
          data: {
            user_id: ws.userId,
            is_typing: true,
          },
        });
        break;

      case "read":
        this.broadcast({
          type: "read",
          chat_id: message.payload.chat_id,
          location_id: Number(ws.locationId),
          timestamp: new Date().toISOString(),
          data: {
            user_id: ws.userId,
            last_read: new Date().toISOString(),
          },
        });
        break;

      default:
        this.sendError(ws, "Unsupported message type");
    }
  }

  private broadcast(message: any) {
    const messageStr = JSON.stringify(message);
    (this.wss.clients as Set<ExtendedWebSocket>).forEach((client) => {
      if (
        client.locationId === message.location_id.toString() &&
        client.readyState === WebSocket.OPEN
      ) {
        client.send(messageStr);
      }
    });
  }

  private sendError(ws: ExtendedWebSocket, error: string) {
    ws.send(
      JSON.stringify({
        type: "error",
        payload: { error },
        timestamp: new Date().toISOString(),
      })
    );
  }

  public cleanup() {
    clearInterval(this.pingInterval);
  }
}
