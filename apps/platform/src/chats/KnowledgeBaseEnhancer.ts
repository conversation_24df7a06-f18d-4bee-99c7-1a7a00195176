/**
 * Knowledge Base Enhancer for <PERSON>y Chat
 * Enhances the vector database with specific Q&A patterns from the Google Sheet analysis
 */

import { OpenAI } from "openai";
import { logger } from "../config/logger";

export interface QAPattern {
  question: string;
  intent_cluster: string;
  primary_pain_point: string;
  desired_outcome: string;
  guideline: string;
  category: string;
  good_answer: string;
  quality_rating: 'green' | 'yellow' | 'red' | 'unrated';
}

export class KnowledgeBaseEnhancer {
  private openai: OpenAI;

  constructor(openai: OpenAI) {
    this.openai = openai;
  }

  /**
   * Enhanced Q&A patterns derived from the Google Sheet analysis
   */
  private getEnhancedQAPatterns(): QAPattern[] {
    return [
      {
        question: "How do I sync my inventory with Metrc without manual double-entry?",
        intent_cluster: "Inventory Sync",
        primary_pain_point: "Manual double-entry; compliance error risk",
        desired_outcome: "Real-time, error-free Metrc integration",
        guideline: "Explain API-level sync; highlight BakedBot POS bridge; offer checklist → API keys → SKU mapping → test sync → alert configuration.",
        category: "Compliance & Ops",
        good_answer: "Metrc can receive inventory updates in real time if your POS or ERP is connected through its HTTPS API. BakedBot AI sits between your POS and Metrc, streaming every *receive*, *adjust*, and *sale* call the moment it happens and writing the Metrc receipt IDs back to your dashboard so you have a clean audit log. That removes double-entry, reduces human error, and keeps your counts and manifests aligned across stores. *(For educational purposes only; confirm all compliance steps with your legal/compliance officer.)*",
        quality_rating: 'green'
      },
      {
        question: "What's the best way to handle FIFO across multiple batches?",
        intent_cluster: "Inventory Mgmt",
        primary_pain_point: "Batch aging; CoA expirations",
        desired_outcome: "Automated FIFO allocation per state rules",
        guideline: "Show batch-age rules; demo BakedBot batch monitor; suggest weekly variance report.",
        category: "Compliance & Ops",
        good_answer: "FIFO works best when every package has a clear 'arrival date' and when the POS enforces pick-logic at the register. BakedBot's Batch Monitor tags each package with its arrival timestamp, locks older lots to the front of the queue, and warns budtenders if they try to scan a fresher batch first. Age rules can be set by days or % of shelf-life so you always move the oldest inventory first. *(For educational purposes only; confirm all compliance steps with your legal/compliance officer.)*",
        quality_rating: 'green'
      },
      {
        question: "Which platforms actually allow cannabis advertising now?",
        intent_cluster: "Channel Selection",
        primary_pain_point: "Restricted reach",
        desired_outcome: "List of compliant channels",
        guideline: "Provide 2025 snapshot: Weedmaps, Leafly, X (Twitter) beta, programmatic CTV; include CPC benchmarks.",
        category: "Marketing & Growth",
        good_answer: "Leafly and Weedmaps still deliver menu eyeballs, but in 2025 we're seeing 3–4× ROI from SEO-optimized headless menus, location-based SMS, and short-form video on Instagram Reels/TikTok-style clips. BakedBot syndicates the same SKU data to all of them so you can test channels without extra work. *(For educational purposes only; confirm all compliance steps with your legal/compliance officer.)*",
        quality_rating: 'green'
      },
      {
        question: "What's the difference between indica and sativa?",
        intent_cluster: "Product Education",
        primary_pain_point: "Customer myths",
        desired_outcome: "Clear, compliant explanation",
        guideline: "Clarify legacy vs chemovar; focus on terpenes/effects; avoid medical claims; keep it friendly.",
        category: "Customer Experience",
        good_answer: "Think 'terpenes and effect language,' not old Sativa/Indica buckets. Customers respond to clear sensory calls like \"citrus-forward limonene – mood-brightening\" or \"myrcene-heavy – evening unwind.\" BakedBot pulls lab data, maps dominant terps to plain-English effects, and auto-generates shelf talkers and site blurbs you can paste anywhere. *(For educational purposes only; confirm all compliance steps with your legal/compliance officer.)*",
        quality_rating: 'green'
      },
      {
        question: "How much should I take (edibles)?",
        intent_cluster: "Dosing Guidance",
        primary_pain_point: "Risk of over-consumption",
        desired_outcome: "Safe starting dose advice",
        guideline: "\"Start low, go slow\" 5–10 mg baseline; reference local serving limits; disclaim medical advice.",
        category: "Customer Experience",
        good_answer: "Start at 2.5 mg THC (¼ of a 10 mg gummy), wait 2 hrs, then decide. Start low, go slow; follow local laws. Why it matters: 90–120 min minimum; digestion is slow-motion. Re-dosing sooner risks an 'uh-oh' evening. ➡️ Next step: Ask me for our micro-dose menu—everything ≤5 mg per piece.",
        quality_rating: 'green'
      },
      {
        question: "How do I calculate Illinois excise tax on infused beverages?",
        intent_cluster: "Tax Compliance",
        primary_pain_point: "Complex tax calculations",
        desired_outcome: "Accurate tax computation",
        guideline: "Multiply total mg THC × $0.03. Example: 100 mg drink → $3.00 excise, plus regular sales tax.",
        category: "Tech Consulting",
        good_answer: "Multiply total mg THC × $0.03. Example: 100 mg drink → $3.00 excise, plus regular sales tax. ➡️ Next step: Ask for our free tax-calc sheet that handles multi-jurisdiction subtotals.",
        quality_rating: 'green'
      },
      {
        question: "Which POS handles split tenders and stackable discounts best?",
        intent_cluster: "POS Technology",
        primary_pain_point: "Complex transaction processing",
        desired_outcome: "Optimal POS selection",
        guideline: "Flowhub and Greenbits lead: real-time split cash/debit + multi-promo stacking with tax recalculation.",
        category: "Tech Consulting",
        good_answer: "Flowhub and Greenbits lead: real-time split cash/debit + multi-promo stacking with tax recalculation. ➡️ Next step: Let's book a live demo; I'll bring your exact BOGO scenario to test.",
        quality_rating: 'green'
      }
    ];
  }

  /**
   * Generate embeddings for Q&A patterns to enhance vector search
   */
  async generateQAEmbeddings(): Promise<Array<{
    question: string;
    answer: string;
    category: string;
    embedding: number[];
    metadata: any;
  }>> {
    const patterns = this.getEnhancedQAPatterns();
    const embeddings = [];

    for (const pattern of patterns) {
      try {
        // Create embedding for the question
        const questionEmbedding = await this.openai.embeddings.create({
          model: "text-embedding-ada-002",
          input: pattern.question
        });

        // Create embedding for the combined question + context
        const contextualInput = `${pattern.question} ${pattern.intent_cluster} ${pattern.primary_pain_point} ${pattern.category}`;
        const contextualEmbedding = await this.openai.embeddings.create({
          model: "text-embedding-ada-002",
          input: contextualInput
        });

        embeddings.push({
          question: pattern.question,
          answer: pattern.good_answer,
          category: pattern.category,
          embedding: questionEmbedding.data[0].embedding,
          metadata: {
            intent_cluster: pattern.intent_cluster,
            primary_pain_point: pattern.primary_pain_point,
            desired_outcome: pattern.desired_outcome,
            guideline: pattern.guideline,
            quality_rating: pattern.quality_rating,
            contextual_embedding: contextualEmbedding.data[0].embedding
          }
        });

        logger.info(`Generated embedding for: ${pattern.question}`);
      } catch (error) {
        logger.error(`Error generating embedding for pattern: ${pattern.question}`, error);
      }
    }

    return embeddings;
  }

  /**
   * Create training examples for fine-tuning (if needed)
   */
  generateTrainingExamples(): Array<{
    messages: Array<{ role: string; content: string }>;
  }> {
    const patterns = this.getEnhancedQAPatterns();
    
    return patterns.map(pattern => ({
      messages: [
        {
          role: "system",
          content: `You are SMOKEY, an expert cannabis industry consultant. Follow these guidelines: ${pattern.guideline}. Always be specific, actionable, and include compliance disclaimers when appropriate.`
        },
        {
          role: "user",
          content: pattern.question
        },
        {
          role: "assistant",
          content: pattern.good_answer
        }
      ]
    }));
  }

  /**
   * Generate semantic variations of questions for better matching
   */
  async generateQuestionVariations(originalQuestion: string): Promise<string[]> {
    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "Generate 5 semantic variations of the given cannabis industry question. Keep the core intent the same but vary the wording, formality, and specific terms used."
          },
          {
            role: "user",
            content: originalQuestion
          }
        ],
        temperature: 0.7
      });

      const variations = response.choices[0]?.message?.content?.split('\n')
        .filter(line => line.trim().length > 0)
        .map(line => line.replace(/^\d+\.\s*/, '').trim()) || [];

      return variations;
    } catch (error) {
      logger.error("Error generating question variations:", error);
      return [];
    }
  }

  /**
   * Analyze response quality based on the guidelines
   */
  analyzeResponseQuality(response: string, questionType: string): {
    score: number;
    feedback: string[];
    improvements: string[];
  } {
    const feedback: string[] = [];
    const improvements: string[] = [];
    let score = 0;

    // Check for specific formatting requirements
    if (response.includes("•") || response.includes("-")) {
      score += 10;
      feedback.push("✓ Uses bullet points for structure");
    } else {
      improvements.push("Add bullet points (•) for better structure");
    }

    if (response.includes("Why it matters:")) {
      score += 15;
      feedback.push("✓ Includes 'Why it matters' explanation");
    } else {
      improvements.push("Add 'Why it matters:' explanation");
    }

    if (response.includes("➡️ Next step:")) {
      score += 15;
      feedback.push("✓ Provides actionable next steps");
    } else {
      improvements.push("Add '➡️ Next step:' recommendations");
    }

    // Check for industry-specific terms
    const industryTerms = ["BakedBot", "Metrc", "POS", "API", "compliance", "cannabis"];
    const hasIndustryTerms = industryTerms.some(term => 
      response.toLowerCase().includes(term.toLowerCase())
    );
    
    if (hasIndustryTerms) {
      score += 10;
      feedback.push("✓ Uses industry-specific terminology");
    } else {
      improvements.push("Include specific cannabis industry terms and tools");
    }

    // Check for compliance disclaimers
    if (response.includes("*(For educational purposes") || response.includes("consult")) {
      score += 10;
      feedback.push("✓ Includes compliance disclaimer");
    } else if (questionType.includes("compliance") || questionType.includes("tax")) {
      improvements.push("Add compliance disclaimer");
    }

    // Check for specific numbers/data
    if (/\d+%|\$\d+|\d+\.\d+/.test(response)) {
      score += 10;
      feedback.push("✓ Includes specific numbers/percentages");
    } else {
      improvements.push("Add specific numbers, percentages, or dollar amounts");
    }

    return {
      score: Math.min(score, 100),
      feedback,
      improvements
    };
  }
}
