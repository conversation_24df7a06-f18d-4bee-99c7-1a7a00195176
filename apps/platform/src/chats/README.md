# Smokey Chat AI Service

This directory contains the implementation of <PERSON><PERSON>, a conversational assistant that answers questions for cannabis dispensary operators about sales, compliance, marketing, inventory, and customer experience.

## Recent Enhancements (Based on Q&A Guidelines Analysis)

The Smokey Chat functionality has been significantly improved based on analysis of Q&A patterns from the Google Sheet guidelines. The enhancements focus on transforming generic responses into expert-level, cannabis industry-specific guidance.

### Key Improvements

1. **Response Enhancement Service** (`ResponseEnhancementService.ts`)
   - Automatically enhances responses to follow industry best practices
   - Adds specific formatting (bullet points, "Why it matters", "Next steps")
   - Includes cannabis-specific terminology and compliance disclaimers

2. **Enhanced Prompt Templates** (`ResponseTemplates.ts`, `SmokeyPromptConfig.ts`)
   - Question-type specific response structures
   - Industry-specific terminology and examples
   - Compliance-focused guidance patterns

3. **Knowledge Base Enhancement** (`KnowledgeBaseEnhancer.ts`)
   - Q&A patterns derived from successful responses
   - Quality analysis and scoring system
   - Training examples for consistent responses

4. **Testing Framework** (`testSmokeyEnhancements.ts`)
   - Automated testing of response quality
   - Question type detection validation
   - Performance metrics and recommendations

## Architecture

The SmokeyAIService is built around a core set of tools that implement a Retrieval-Augmented Generation (RAG) pattern:

1. **Intent Router**: Analyzes user queries to determine intent and which tools to use
2. **Tool Gateway**: Manages a set of specialized tools for different data sources
3. **LLM Integration**: Communicates with OpenAI for both intent classification and final response generation
4. **Response Enhancement**: Post-processes responses to ensure quality and industry-specific formatting

## Response Quality Standards

Based on the Google Sheet analysis, responses now follow these quality standards:

### Formatting Requirements
- **Bullet Points**: Use • for lists and key information
- **Explanations**: Include "Why it matters:" for technical recommendations
- **Action Items**: Add "➡️ Next step:" for actionable recommendations
- **Specificity**: Include exact numbers, percentages, dollar amounts
- **Industry Terms**: Reference specific tools (BakedBot, Metrc, Flowhub, etc.)
- **Compliance**: Include appropriate disclaimers

### Response Categories

1. **Technical Integration** (Inventory Sync, POS Technology)
   - API-level technical solutions with implementation details
   - Real-time vs batch processing explanations
   - Specific system comparisons with performance data

2. **Product Education** (Strain Information, Effects)
   - Scientific explanations with practical analogies
   - Terpene profiles and chemovar science
   - Customer-friendly language with technical accuracy

3. **Compliance & Operations** (Regulations, Audits)
   - Specific operational solutions with compliance focus
   - Implementation steps with required tools
   - Mandatory compliance disclaimers

4. **Marketing & Growth** (Advertising, Campaigns)
   - Platform-specific guidance with current policies
   - Performance metrics (CTR, CPM, ROI)
   - Compliance requirements for cannabis advertising

5. **Customer Experience** (Dosing, Product Selection)
   - Safety-first guidance with specific recommendations
   - Timing protocols and safety considerations
   - Medical disclaimers where appropriate

6. **Financial & Tax** (Calculations, Compliance)
   - Exact formulas with specific examples
   - State-specific rates and requirements
   - 280E implications and automation suggestions

## Example Response Transformation

**Before (Generic)**:
```
To sync your inventory with Metrc without manual double-entry, you need to integrate your point-of-sale (POS) or inventory management system directly with Metrc's API. Many cannabis retail systems offer built-in Metrc integration.
```

**After (Enhanced)**:
```
Metrc can receive inventory updates in real time if your POS or ERP is connected through its HTTPS API. BakedBot AI sits between your POS and Metrc, streaming every *receive*, *adjust*, and *sale* call the moment it happens and writing the Metrc receipt IDs back to your dashboard so you have a clean audit log.

Why it matters: That removes double-entry, reduces human error, and keeps your counts and manifests aligned across stores.

➡️ Next step: Explain API-level sync; highlight BakedBot POS bridge; offer checklist → API keys → SKU mapping → test sync → alert configuration.

*(For educational purposes only; confirm all compliance steps with your legal/compliance officer.)*
```

## Testing and Validation

Run the enhancement tests to validate response quality:

```bash
npm run test:smokey-enhancements
```

The testing framework evaluates:
- Question type detection accuracy
- Response formatting compliance
- Industry terminology usage
- Compliance disclaimer inclusion
- Overall response quality scores

```
┌──────────┐     ask        ┌────────────┐  1. intent & tool route
│  Front-end│ ───────────▶ │   Router   │───────────────────┐
└──────────┘                └────────────┘                   │
     ▲                       ▲    ▲                          │
     │ 6. formatted answer   │    │2. function call           ▼
┌──────────┐  ◀──────────────┘┌────────────┐ 3. validated ▶ Tool service
│   LLM    │ 5. reasoning +    │  Tool API  │──────────┐
│ (chat)   │◀──────────────────│  Gateway   │          │
└──────────┘   4. JSON result  └────────────┘◀─────────┘
```

## Core Services ("Tools")

| Name                  | Purpose                                                              | Location                        |
| --------------------- | -------------------------------------------------------------------- | ------------------------------- |
| `data_hub.query`      | SQL/REST wrapper over Supabase & MySQL                               | `src/tools/DataHubTool.ts`      |
| `vector_search.query` | Pinecone hybrid search across docs, regulations, strain encyclopedia | `src/tools/VectorSearchTool.ts` |
| `analysis.run`        | Time-series, clustering, price-elasticity executors                  | `src/tools/AnalysisTool.ts`     |
| `market.fetch`        | Live competitor scrape + public APIs                                 | `src/tools/MarketTool.ts`       |

## Key Components

- **SmokeyAIService**: Main service class that orchestrates the entire process
- **Tool Interface**: Common interface for all tools to implement
- **Intent Analysis**: Determines user intent and routes to appropriate tools

## Getting Started

1. Initialize required environment variables:

   - `OPENAI_API_KEY`
   - `DB_HOST`, `DB_PORT`, `DB_USER`, `DB_PASSWORD`, `DB_NAME`
   - `SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY`
   - `PINECONE_API_KEY` (if using Pinecone for vector search)

2. Initialize the service:

```typescript
import { OpenAI } from "openai";
import knex from "knex";
import { SmokeyAIService } from "./chats/SmokeyAIService";

// Initialize dependencies
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
const db = knex({
  /* database config */
});

// Create service
const smokeyService = new SmokeyAIService(openai, db, {
  defaultModel: "gpt-3.5-turbo",
  // Additional options
});

// Use the service
const agent = smokeyService.getDefaultAgent();
const response = await smokeyService.generateResponse(chat, message, agent);
```

3. Run the test script to verify functionality:

```bash
ts-node src/test/test_smokey_chat.ts
```

## Extending

### Adding a New Tool

1. Create a new class that implements the `Tool` interface (see `src/tools/interfaces.ts`)
2. Register the tool in `SmokeyAIService.initializeTools()`
3. Update the intent analysis prompt to include the new tool functionality

## Performance Considerations

- Vector embeddings are computationally expensive - consider caching results
- Database queries against large tables should be optimized
- Model selection impacts both accuracy and response speed
