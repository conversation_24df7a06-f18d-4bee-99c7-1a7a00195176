import { <PERSON>A<PERSON> } from "openai";
import { logger } from "../config/logger";
import {
  RESPONSE_TEMPLATES,
  COMPLIANCE_DISCLAIMERS,
  enhanceResponse,
} from "./ResponseTemplates";
import {
  SMOKEY_ENHANCED_PROMPTS,
  getEnhancedPromptForQuestionType,
  RESPONSE_EXAMPLES,
} from "./SmokeyPromptConfig";

/**
 * Service to enhance Smokey chat responses based on Q&A guidelines
 * Implements patterns from successful responses in the Google Sheet analysis
 */
export class ResponseEnhancementService {
  private openai: OpenAI;

  constructor(openai: OpenAI) {
    this.openai = openai;
  }

  /**
   * Enhance a response based on question type and industry best practices
   */
  async enhanceResponse(
    originalResponse: string,
    userQuestion: string,
    questionType: string,
    agentName: string = "SMOKEY"
  ): Promise<string> {
    try {
      // Determine if response needs enhancement
      if (this.isAlreadyWellFormatted(originalResponse)) {
        return originalResponse;
      }

      // Get enhancement prompt based on question type
      const enhancementPrompt = this.buildEnhancementPrompt(
        originalResponse,
        userQuestion,
        questionType,
        agentName
      );

      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: this.getSystemPromptForEnhancement(),
          },
          {
            role: "user",
            content: enhancementPrompt,
          },
        ],
        temperature: 0.3,
        max_tokens: 1000,
      });

      const enhancedResponse =
        response.choices[0]?.message?.content || originalResponse;

      // Apply final formatting
      return this.applyFinalFormatting(enhancedResponse, questionType);
    } catch (error) {
      logger.error("Error enhancing response:", error);
      return originalResponse; // Return original if enhancement fails
    }
  }

  /**
   * Check if response is already well-formatted according to guidelines
   */
  private isAlreadyWellFormatted(response: string): boolean {
    const hasStructure =
      response.includes("Why it matters:") ||
      response.includes("➡️ Next step:");
    const hasBullets = response.includes("•") || response.includes("- ");
    const hasSpecifics = /\d+%|\$\d+|\d+\.\d+%/.test(response); // Contains percentages or dollar amounts

    return hasStructure && (hasBullets || hasSpecifics);
  }

  /**
   * Build enhancement prompt based on question type
   */
  private buildEnhancementPrompt(
    originalResponse: string,
    userQuestion: string,
    questionType: string,
    agentName: string
  ): string {
    const template = this.getTemplateForQuestionType(questionType);

    return `
ORIGINAL QUESTION: ${userQuestion}
ORIGINAL RESPONSE: ${originalResponse}
AGENT: ${agentName}
QUESTION TYPE: ${questionType}

ENHANCEMENT REQUIREMENTS:
${template ? `Follow this structure: ${template.structure.join(" → ")}` : ""}

QUESTION-SPECIFIC GUIDANCE:
${getEnhancedPromptForQuestionType(questionType)}

SPECIFIC IMPROVEMENTS NEEDED:
1. Add specific numbers, percentages, or dollar figures where relevant
2. Include "Why it matters:" explanations for technical recommendations
3. Add "➡️ Next step:" actionable recommendations
4. Use bullet points (•) for lists and key information
5. Reference specific cannabis industry tools (BakedBot, Metrc, POS systems)
6. Include compliance disclaimers where appropriate
7. Make responses more specific to cannabis industry context
8. Avoid generic language - be precise and technical

Enhance the original response following these guidelines while maintaining accuracy.`;
  }

  /**
   * Get system prompt for response enhancement
   */
  private getSystemPromptForEnhancement(): string {
    return `${SMOKEY_ENHANCED_PROMPTS.SYSTEM_PROMPT_ENHANCEMENT}

EXAMPLES OF EXCELLENT RESPONSES:
${RESPONSE_EXAMPLES.GOOD_EXAMPLES.map(
  (ex) => `
Q: ${ex.question}
A: ${ex.response}
`
).join("\n")}

AVOID THESE PATTERNS:
${RESPONSE_EXAMPLES.POOR_EXAMPLES.map(
  (ex) => `
Poor example for: ${ex.question}
${ex.response}
`
).join("\n")}

Transform generic responses into expert-level, cannabis industry-specific guidance following the good examples above.`;
  }

  /**
   * Get appropriate template for question type
   */
  private getTemplateForQuestionType(questionType: string): any {
    const typeMapping: Record<string, keyof typeof RESPONSE_TEMPLATES> = {
      inventory_sync: "TECHNICAL_INTEGRATION",
      compliance_question: "COMPLIANCE_OPERATIONS",
      product_education: "PRODUCT_EDUCATION",
      marketing_campaign: "MARKETING_GROWTH",
      customer_dosing: "CUSTOMER_DOSING",
      tax_information: "TAX_FINANCIAL",
      pos_technology: "POS_TECHNOLOGY",
    };

    const templateKey = typeMapping[questionType];
    return templateKey ? RESPONSE_TEMPLATES[templateKey] : null;
  }

  /**
   * Apply final formatting enhancements
   */
  private applyFinalFormatting(response: string, questionType: string): string {
    let formatted = enhanceResponse(response, questionType);

    // Ensure compliance disclaimers are properly formatted
    if (this.needsComplianceDisclaimer(questionType, formatted)) {
      formatted += `\n\n${COMPLIANCE_DISCLAIMERS.GENERAL}`;
    }

    // Ensure proper spacing and formatting
    formatted = formatted.replace(/\n{3,}/g, "\n\n"); // Remove excessive line breaks
    formatted = formatted.trim();

    return formatted;
  }

  /**
   * Determine if response needs compliance disclaimer
   */
  private needsComplianceDisclaimer(
    questionType: string,
    response: string
  ): boolean {
    const needsDisclaimer = [
      "compliance_question",
      "inventory_sync",
      "tax_information",
      "pos_technology",
    ].includes(questionType);

    const hasDisclaimer =
      response.includes("*(For educational purposes") ||
      response.includes("consult") ||
      response.includes("legal/compliance officer");

    return needsDisclaimer && !hasDisclaimer;
  }

  /**
   * Analyze question to determine enhancement type needed
   */
  analyzeQuestionType(question: string): string {
    const patterns = {
      inventory_sync: /sync|metrc|inventory|api|integration|real.?time/i,
      compliance_question: /compliance|regulation|audit|legal|license/i,
      product_education: /terpene|indica|sativa|thc|cbd|strain|effect/i,
      marketing_campaign: /marketing|advertising|campaign|social|google ads/i,
      customer_dosing: /dose|dosing|edible|gummy|mg|start.*low/i,
      tax_information: /tax|excise|280e|calculation|illinois|colorado/i,
      pos_technology: /pos|point.*sale|flowhub|greenbits|cova|square/i,
    };

    for (const [type, pattern] of Object.entries(patterns)) {
      if (pattern.test(question)) {
        return type;
      }
    }

    return "general_inquiry";
  }
}
