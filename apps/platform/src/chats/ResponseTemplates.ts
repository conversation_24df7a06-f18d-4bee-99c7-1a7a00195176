/**
 * Enhanced response templates for <PERSON><PERSON>t based on Q&A guidelines
 * These templates follow the successful patterns from the Google Sheet analysis
 */

export interface ResponseTemplate {
  pattern: string;
  structure: string[];
  examples: string[];
  compliance_note?: string;
}

export const RESPONSE_TEMPLATES = {
  // Technical Integration Questions
  TECHNICAL_INTEGRATION: {
    pattern: "API-level technical solutions with specific implementation details",
    structure: [
      "Direct technical answer with specific tools/APIs",
      "Why it matters: [business impact]",
      "Implementation steps with bullet points",
      "➡️ Next step: [specific actionable item]",
      "Compliance disclaimer"
    ],
    examples: [
      "Metrc can receive inventory updates in real time if your POS or ERP is connected through its HTTPS API. BakedBot AI sits between your POS and Metrc, streaming every *receive*, *adjust*, and *sale* call the moment it happens and writing the Metrc receipt IDs back to your dashboard so you have a clean audit log.",
      "Why it matters: That removes double-entry, reduces human error, and keeps your counts and manifests aligned across stores."
    ],
    compliance_note: "*(For educational purposes only; confirm all compliance steps with your legal/compliance officer.)*"
  },

  // Product Education Questions
  PRODUCT_EDUCATION: {
    pattern: "Clear scientific explanations with practical applications",
    structure: [
      "Simple, accurate scientific explanation",
      "Practical application or analogy",
      "Why it matters: [customer benefit]",
      "➡️ Next step: [helpful resource or action]"
    ],
    examples: [
      "Terpenes are the plant's essential oils—lemony limonene, piney pinene—that shape scent and feel. THC is the engine; terpenes are the steering wheel.",
      "Why it matters: Teaching chemovar beats old indica/sativa myths and drives smarter upsells."
    ]
  },

  // Compliance & Operations
  COMPLIANCE_OPERATIONS: {
    pattern: "Specific operational solutions with compliance focus",
    structure: [
      "Specific solution with technical details",
      "Implementation approach with tools",
      "Why it matters: [compliance/business benefit]",
      "➡️ Next step: [specific implementation step]",
      "Compliance disclaimer"
    ],
    examples: [
      "FIFO works best when every package has a clear 'arrival date' and when the POS enforces pick-logic at the register. BakedBot's Batch Monitor tags each package with its arrival timestamp, locks older lots to the front of the queue, and warns budtenders if they try to scan a fresher batch first.",
      "Why it matters: Age rules can be set by days or % of shelf-life so you always move the oldest inventory first."
    ],
    compliance_note: "*(For educational purposes only; confirm all compliance steps with your legal/compliance officer.)*"
  },

  // Marketing & Growth
  MARKETING_GROWTH: {
    pattern: "Data-driven marketing insights with specific platforms and metrics",
    structure: [
      "Current platform/channel status with specific data",
      "Strategic recommendations with bullet points",
      "Why it matters: [ROI/performance metrics]",
      "➡️ Next step: [specific test or implementation]"
    ],
    examples: [
      "Leafly and Weedmaps still deliver menu eyeballs, but in 2025 we're seeing 3–4× ROI from SEO-optimized headless menus, location-based SMS, and short-form video on Instagram Reels/TikTok-style clips.",
      "Why it matters: BakedBot syndicates the same SKU data to all of them so you can test channels without extra work."
    ]
  },

  // Customer Experience & Dosing
  CUSTOMER_DOSING: {
    pattern: "Safety-first dosing guidance with specific recommendations",
    structure: [
      "Specific dosing recommendation with numbers",
      "Safety protocol with timing",
      "Why it matters: [safety/experience benefit]",
      "➡️ Next step: [helpful resource or tool]",
      "Medical disclaimer"
    ],
    examples: [
      "Start at 2.5 mg THC (¼ of a 10 mg gummy), wait 2 hrs, then decide. Start low, go slow; follow local laws.",
      "Why it matters: 90–120 min minimum; digestion is slow-motion. Re-dosing sooner risks an 'uh-oh' evening."
    ],
    compliance_note: "Start low, go slow; consult a healthcare pro."
  },

  // Tax & Financial
  TAX_FINANCIAL: {
    pattern: "Precise tax calculations with specific examples",
    structure: [
      "Exact calculation formula",
      "Specific example with numbers",
      "Why it matters: [compliance/financial benefit]",
      "➡️ Next step: [tool or resource offer]"
    ],
    examples: [
      "Multiply total mg THC × $0.03. Example: 100 mg drink → $3.00 excise, plus regular sales tax.",
      "Why it matters: Illinois cannabis excise taxes range from 10% on flower to 25% on high-THC infused products, and local municipalities can add up to 3%."
    ]
  },

  // POS & Technology
  POS_TECHNOLOGY: {
    pattern: "Specific POS comparisons with technical capabilities",
    structure: [
      "Direct POS system comparison with specific features",
      "Technical capabilities with uptime/performance data",
      "Why it matters: [business continuity/compliance benefit]",
      "➡️ Next step: [demo or evaluation offer]"
    ],
    examples: [
      "Flowhub and Greenbits lead: real-time split cash/debit + multi-promo stacking with tax recalculation.",
      "Why it matters: Cova historically posts 99.95% uptime on high-traffic days, while Flowhub averages 99.7% but bundles more built-in reporting."
    ]
  }
};

export const INDUSTRY_TERMINOLOGY = {
  // Cannabis-specific terms
  CANNABIS_TERMS: [
    "chemovar", "terpene profile", "cannabinoid", "THC-A", "decarboxylation",
    "FIFO", "batch tracking", "seed-to-sale", "CoA", "potency", "myrcene",
    "limonene", "pinene", "indica", "sativa", "hybrid"
  ],
  
  // Technical/POS terms
  TECHNICAL_TERMS: [
    "Metrc API", "POS integration", "cashless-ATM", "split tender",
    "real-time sync", "API-level", "HTTPS API", "PIN-debit",
    "compliance uptime", "variance alerts", "cycle counts"
  ],
  
  // Business/Financial terms
  BUSINESS_TERMS: [
    "280E", "excise tax", "COGS", "shrink control", "margin analysis",
    "ROI", "CPM", "CTR", "ROAS", "customer lifetime value"
  ],
  
  // Brand/Platform terms
  BRAND_TERMS: [
    "BakedBot", "Metrc", "Flowhub", "Greenbits", "Cova", "MJ Freeway",
    "Weedmaps", "Leafly", "Mantis", "High There!", "MassRoots"
  ]
};

export const COMPLIANCE_DISCLAIMERS = {
  GENERAL: "*(For educational purposes only; confirm all compliance steps with your legal/compliance officer.)*",
  MEDICAL: "Start low, go slow; consult a healthcare pro.",
  DOSING: "Start low, go slow; follow local laws.",
  FINANCIAL: "Consult with your accountant for specific tax situations.",
  TECHNICAL: "Verify compatibility with your current systems before implementation."
};

export const RESPONSE_ENHANCERS = {
  WHY_IT_MATTERS: [
    "Why it matters:",
    "This matters because:",
    "The key benefit:",
    "Impact:"
  ],
  
  NEXT_STEPS: [
    "➡️ Next step:",
    "➡️ Action item:",
    "➡️ Try this:",
    "➡️ Implementation:"
  ],
  
  TECHNICAL_SPECIFICS: [
    "Technical details:",
    "Implementation approach:",
    "System requirements:",
    "Configuration:"
  ]
};

/**
 * Helper function to format responses according to templates
 */
export function formatResponse(
  content: string,
  template: ResponseTemplate,
  includeCompliance: boolean = true
): string {
  let formattedResponse = content;
  
  // Add compliance disclaimer if needed and not already present
  if (includeCompliance && template.compliance_note && !content.includes("*(For educational purposes")) {
    formattedResponse += `\n\n${template.compliance_note}`;
  }
  
  return formattedResponse;
}

/**
 * Helper function to enhance responses with industry-specific formatting
 */
export function enhanceResponse(content: string, questionType: string): string {
  // Add bullet points if not present but content has lists
  if (content.includes('\n-') && !content.includes('•')) {
    content = content.replace(/\n-/g, '\n•');
  }
  
  // Ensure proper spacing around "Why it matters" and "Next step"
  content = content.replace(/Why it matters:/g, '\nWhy it matters:');
  content = content.replace(/➡️ Next step:/g, '\n➡️ Next step:');
  
  return content;
}
