/**
 * Demo script to showcase Smokey Chat enhancements
 * This script demonstrates the before/after improvements based on Google Sheet guidelines
 */

const { OpenAI } = require("openai");

// Mock the enhanced response service for demonstration
class MockResponseEnhancementService {
  analyzeQuestionType(question) {
    const patterns = {
      'inventory_sync': /sync|metrc|inventory|api|integration|real.?time/i,
      'product_education': /terpene|indica|sativa|thc|cbd|strain|effect/i,
      'customer_dosing': /dose|dosing|edible|gummy|mg|start.*low/i,
      'marketing_campaign': /marketing|advertising|campaign|social|google ads/i,
      'tax_information': /tax|excise|280e|calculation|illinois|colorado/i,
      'pos_technology': /pos|point.*sale|flowhub|greenbits|cova|square/i
    };

    for (const [type, pattern] of Object.entries(patterns)) {
      if (pattern.test(question)) {
        return type;
      }
    }
    return 'general_inquiry';
  }

  async enhanceResponse(originalResponse, question, questionType) {
    // Simulate the enhancement logic
    const enhancements = this.getEnhancementsForType(questionType);
    return this.applyEnhancements(originalResponse, question, enhancements);
  }

  getEnhancementsForType(questionType) {
    const enhancementMap = {
      'inventory_sync': {
        addBullets: true,
        addWhyItMatters: true,
        addNextStep: true,
        addIndustryTerms: ['BakedBot', 'Metrc', 'API', 'real-time'],
        addCompliance: true
      },
      'product_education': {
        addBullets: true,
        addWhyItMatters: true,
        addNextStep: true,
        addIndustryTerms: ['terpenes', 'chemovar', 'BakedBot'],
        addCompliance: false
      },
      'customer_dosing': {
        addBullets: false,
        addWhyItMatters: true,
        addNextStep: true,
        addIndustryTerms: ['mg', 'THC'],
        addCompliance: true,
        medicalDisclaimer: true
      },
      'tax_information': {
        addBullets: true,
        addWhyItMatters: false,
        addNextStep: true,
        addIndustryTerms: ['excise tax', 'THC'],
        addCompliance: false
      }
    };

    return enhancementMap[questionType] || enhancementMap['inventory_sync'];
  }

  applyEnhancements(originalResponse, question, enhancements) {
    let enhanced = originalResponse;

    // Add specific enhancements based on question type
    if (question.includes('sync') && question.includes('Metrc')) {
      enhanced = `Metrc can receive inventory updates in real time if your POS or ERP is connected through its HTTPS API. BakedBot AI sits between your POS and Metrc, streaming every *receive*, *adjust*, and *sale* call the moment it happens and writing the Metrc receipt IDs back to your dashboard so you have a clean audit log.

Why it matters: That removes double-entry, reduces human error, and keeps your counts and manifests aligned across stores.

➡️ Next step: Explain API-level sync; highlight BakedBot POS bridge; offer checklist → API keys → SKU mapping → test sync → alert configuration.

*(For educational purposes only; confirm all compliance steps with your legal/compliance officer.)*`;
    }
    
    else if (question.includes('indica') && question.includes('sativa')) {
      enhanced = `Think 'terpenes and effect language,' not old Sativa/Indica buckets. Customers respond to clear sensory calls like "citrus-forward limonene – mood-brightening" or "myrcene-heavy – evening unwind."

Why it matters: BakedBot pulls lab data, maps dominant terps to plain-English effects, and auto-generates shelf talkers and site blurbs you can paste anywhere.

➡️ Next step: Let me print a terpene cheat-card you can clip to the register.

*(For educational purposes only; confirm all compliance steps with your legal/compliance officer.)*`;
    }
    
    else if (question.includes('edible') || question.includes('dosing')) {
      enhanced = `Start at 2.5 mg THC (¼ of a 10 mg gummy), wait 2 hrs, then decide. Start low, go slow; follow local laws.

Why it matters: 90–120 min minimum; digestion is slow-motion. Re-dosing sooner risks an "uh-oh" evening.

➡️ Next step: Ask me for our micro-dose menu—everything ≤5 mg per piece.`;
    }
    
    else if (question.includes('tax') && question.includes('Illinois')) {
      enhanced = `Multiply total mg THC × $0.03. Example: 100 mg drink → $3.00 excise, plus regular sales tax.

➡️ Next step: Ask for our free tax-calc sheet that handles multi-jurisdiction subtotals.`;
    }

    return enhanced;
  }
}

// Demo questions and their generic responses
const DEMO_CASES = [
  {
    question: "How do I sync my inventory with Metrc without manual double-entry?",
    genericResponse: "To sync your inventory with Metrc without manual double-entry, you need to integrate your point-of-sale (POS) or inventory management system directly with Metrc's API. Many cannabis retail systems offer built-in Metrc integration. These systems automatically send sales, inventory adjustments, and transfer data to Metrc in real time or batch mode, eliminating the need for manual entry."
  },
  {
    question: "What's the difference between indica and sativa?",
    genericResponse: "Indica and sativa are two primary types of cannabis plants that differ in their physical characteristics, effects, and typical uses. Indica strains tend to have broader leaves, shorter stature, and denser buds. They are generally associated with relaxing, sedating effects. Sativa strains usually have narrower leaves, taller plants, and lighter buds. Their effects are often described as uplifting, energizing, and cerebral."
  },
  {
    question: "How much should I take for edibles?",
    genericResponse: "For edibles, a common starting dose is usually around 5 to 10 mg of THC, especially if you are new to cannabis or have a low tolerance. It's important to start low and wait at least 1.5 to 2 hours before taking more because edibles take longer to kick in compared to smoking or vaping."
  },
  {
    question: "How do I calculate Illinois excise tax on infused beverages?",
    genericResponse: "In Illinois, the excise tax on cannabis-infused beverages is calculated based on the THC content. The tax rate is $0.03 per milligram of THC contained in the infused beverage. To calculate the excise tax, determine the total milligrams of THC in the beverage and multiply by $0.03."
  }
];

async function runDemo() {
  console.log("🌿 SMOKEY CHAT ENHANCEMENT DEMO");
  console.log("=====================================");
  console.log("Based on Google Sheet Q&A Guidelines Analysis\n");

  const enhancementService = new MockResponseEnhancementService();

  for (let i = 0; i < DEMO_CASES.length; i++) {
    const testCase = DEMO_CASES[i];
    
    console.log(`\n📋 TEST CASE ${i + 1}`);
    console.log(`Question: ${testCase.question}`);
    console.log(`${'='.repeat(60)}`);
    
    // Analyze question type
    const questionType = enhancementService.analyzeQuestionType(testCase.question);
    console.log(`🔍 Detected Type: ${questionType}`);
    
    // Show before response
    console.log(`\n❌ BEFORE (Generic Response):`);
    console.log(`${testCase.genericResponse}`);
    
    // Enhance the response
    const enhancedResponse = await enhancementService.enhanceResponse(
      testCase.genericResponse,
      testCase.question,
      questionType
    );
    
    // Show after response
    console.log(`\n✅ AFTER (Enhanced Response):`);
    console.log(`${enhancedResponse}`);
    
    // Analyze improvements
    const improvements = analyzeImprovements(testCase.genericResponse, enhancedResponse);
    console.log(`\n📈 IMPROVEMENTS:`);
    improvements.forEach(improvement => console.log(`   • ${improvement}`));
    
    console.log(`\n${'='.repeat(80)}`);
  }

  console.log(`\n🎯 SUMMARY OF ENHANCEMENTS:`);
  console.log(`   • Specific industry terminology (BakedBot, Metrc, API)`);
  console.log(`   • Structured formatting with bullet points`);
  console.log(`   • "Why it matters" explanations`);
  console.log(`   • "➡️ Next step" actionable recommendations`);
  console.log(`   • Compliance disclaimers where appropriate`);
  console.log(`   • Exact numbers and specific examples`);
  console.log(`   • Cannabis industry context and expertise`);
}

function analyzeImprovements(before, after) {
  const improvements = [];
  
  if (after.includes('•') && !before.includes('•')) {
    improvements.push('Added bullet point formatting');
  }
  
  if (after.includes('Why it matters:')) {
    improvements.push('Added "Why it matters" explanation');
  }
  
  if (after.includes('➡️ Next step:')) {
    improvements.push('Added actionable next steps');
  }
  
  if (/BakedBot|Metrc|API|real-time/i.test(after) && !/BakedBot|Metrc|API|real-time/i.test(before)) {
    improvements.push('Added specific industry terminology');
  }
  
  if (after.includes('*(For educational purposes')) {
    improvements.push('Added compliance disclaimer');
  }
  
  if (/\d+\.\d+ mg|\$\d+\.\d+|→/.test(after) && !/\d+\.\d+ mg|\$\d+\.\d+|→/.test(before)) {
    improvements.push('Added specific numbers and examples');
  }
  
  if (after.length > before.length * 1.2) {
    improvements.push('Significantly expanded with valuable context');
  }
  
  return improvements;
}

// Run the demo
if (require.main === module) {
  runDemo().catch(console.error);
}

module.exports = { runDemo, DEMO_CASES };
