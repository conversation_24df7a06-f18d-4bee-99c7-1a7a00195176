import Model from "../../core/Model";
import path from "path";

export default class ChatAttachment extends Model {
  static tableName = "chat_attachments";

  declare id: number;
  original_name!: string;
  content_type!: string;
  size!: number;
  path!: string;
  location_id!: number;
  chat_id?: string;
  uploaded_at!: Date;
  uploaded_by?: number;

  static get jsonSchema() {
    return {
      type: "object",
      required: [
        "original_name",
        "content_type",
        "size",
        "path",
        "location_id",
      ],
      properties: {
        id: { type: "string", format: "uuid" },
        original_name: { type: "string", minLength: 1 },
        content_type: { type: "string", minLength: 1 },
        size: { type: "integer", minimum: 0 },
        path: { type: "string", minLength: 1 },
        location_id: { type: "integer" },
        chat_id: { type: ["string", "null"] },
        uploaded_at: { type: "string", format: "date-time" },
        uploaded_by: { type: ["integer", "null"] },
      },
    };
  }

  /**
   * Get the relative URL for accessing this attachment
   */
  getUrl(): string {
    return `/api/admin/locations/${this.location_id}/chats/uploads/${this.id}`;
  }

  /**
   * Get the filesystem path to the attachment file
   */
  getFilePath(baseDir: string): string {
    return path.join(baseDir, this.path);
  }

  /**
   * Determine if the attachment is an image
   */
  isImage(): boolean {
    return this.content_type.startsWith("image/");
  }

  /**
   * Get filename extension from content type
   */
  getExtension(): string {
    const ext = path.extname(this.original_name);
    if (ext) return ext.substring(1);

    // Try to infer from content type if no extension in filename
    const mapping: Record<string, string> = {
      "image/jpeg": "jpg",
      "image/png": "png",
      "image/gif": "gif",
      "application/pdf": "pdf",
      "text/plain": "txt",
      "text/csv": "csv",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        "docx",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        "xlsx",
    };

    return mapping[this.content_type] || "bin";
  }
}
