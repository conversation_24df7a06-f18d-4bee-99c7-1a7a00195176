import { z } from "zod";
import { Database } from "../../config/database";

// Base schemas
export const agentSchema = z.object({
  id: z.string(),
  name: z.string(),
  role: z.string(),
  description: z.string(),
  icon: z.string(),
  capabilities: z.array(z.string()),
  disabled: z.boolean().optional(),
  metadata: z.record(z.unknown()).optional(),
});

export const messageSchema = z.object({
  id: z.number(),
  content: z.string(),
  role: z.enum(["user", "assistant"]),
  timestamp: z.date(),
  chat_id: z.string(),
  agent_id: z.string().optional(),
  metadata: z.record(z.unknown()).optional(),
});

export const chatSchema = z.object({
  id: z.number(),
  chat_id: z.string(),
  name: z.string(),
  created_at: z.date(),
  updated_at: z.date(),
  location_id: z.number(),
  agent_ids: z.array(z.string()),
  status: z.enum(["active", "archived", "deleted"]),
  metadata: z.record(z.unknown()).optional(),
  agents: z.array(agentSchema).optional(),
});

// Derived types
export type Message = z.infer<typeof messageSchema>;
export type Chat = z.infer<typeof chatSchema>;
export type Agent = z.infer<typeof agentSchema>;

// Configuration types
export interface AgentConfig {
  name: string;
  role: string;
  description: string;
  systemPrompt: string;
  capabilities: string[];
  icon?: string;
  disabled?: boolean;
  requirements?: {
    required: string[];
    optional: string[];
  };
  promptTemplates?: Record<string, unknown>;
}

export interface AgentsConfig {
  version: string;
  agents: Record<string, AgentConfig>;
  shared: Record<string, unknown>;
}

// Response types
export interface ChatSummary {
  title: string;
  description: string;
  topics: string[];
  metrics: {
    customerLifetimeValue: number | null;
    campaignROI: number | null;
    retentionRate: number | null;
  };
}

export interface ChatInsight {
  title: string;
  description: string;
  impact: "high" | "medium" | "low";
  type: "automation" | "campaign" | "general";
  metrics?: Record<string, number>;
  recommendations?: string[];
}

// WebSocket types
export type WebSocketMessageType = "message" | "typing" | "read" | "error";
export type ChatEventType =
  | "created"
  | "updated"
  | "deleted"
  | "message"
  | "typing"
  | "read";

export interface WebSocketMessage {
  type: WebSocketMessageType;
  payload: {
    chat_id: number;
    user_id?: number;
    content?: string;
    [key: string]: any;
  };
  timestamp: string;
}

export interface ChatEvent {
  type: ChatEventType;
  chat_id: number;
  location_id: number;
  timestamp: string;
  data?: any;
}

// Search interfaces
export interface SearchParams {
  query?: string;
  chatId?: string;
  agentId?: string;
  startDate?: Date;
  endDate?: Date;
  page?: number;
  pageSize?: number;
  sortBy?: "timestamp" | "relevance";
  sortOrder?: "asc" | "desc";
}

export interface SearchResult {
  messages: Message[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasMore: boolean;
}

// Agent scoring interfaces
export interface AgentScore {
  agent: Agent;
  score: number;
  reasons?: string[];
}

// Intent analysis interface
export interface IntentAnalysis {
  intent: string;
  confidence: number;
  entities: {
    agentType?: string;
    mentionedAgent?: string;
    requiredContextTypes?: string[];
    searchMethod?: "sql" | "vector" | "both";
    [key: string]: any;
  };
}
