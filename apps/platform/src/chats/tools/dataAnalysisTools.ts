import { DataAnalysisService } from "../../analysis/DataAnalysisService";
import { Tool } from "langchain/tools";
import { logger } from "../../config/logger";
import { CompetitorAnalysisService } from "../../competitors/CompetitorAnalysisService";
import { OpenAI } from "openai";
import { Product } from "../../products/Product";

export class SalesTrendsTool extends Tool {
  name = "analyze_sales_trends";
  description =
    "Analyze sales trends over time for a specific location. Input should be a JSON string with locationId, startDate, and endDate.";

  async _call(input: string): Promise<string> {
    try {
      const { locationId, startDate, endDate } = JSON.parse(input);
      const result = await DataAnalysisService.analyzeSalesTrends(
        locationId,
        new Date(startDate),
        new Date(endDate)
      );
      return JSON.stringify(result);
    } catch (error) {
      logger.error("Error in sales trends analysis:", error);
      return `Error analyzing sales trends: ${
        error instanceof Error ? error.message : String(error)
      }`;
    }
  }
}

export class SalesTrendsTimeSeriesTool extends Tool {
  name = "analyze_sales_trends_time_series";
  description =
    "Perform detailed time series analysis of sales trends. Input should be a JSON string with locationId, startDate, and endDate.";

  async _call(input: string): Promise<string> {
    try {
      const { locationId, startDate, endDate } = JSON.parse(input);
      const result = await DataAnalysisService.analyzeSalesTrendsWithTimeSeries(
        locationId,
        new Date(startDate),
        new Date(endDate)
      );
      return JSON.stringify(result);
    } catch (error) {
      logger.error("Error in sales trends time series analysis:", error);
      return `Error analyzing sales trends with time series: ${
        error instanceof Error ? error.message : String(error)
      }`;
    }
  }
}

export class CustomerBehaviorTool extends Tool {
  name = "analyze_customer_behavior";
  description =
    "Analyze customer behavior patterns and segment customers. Input should be a JSON string with locationId, startDate, and endDate.";

  async _call(input: string): Promise<string> {
    try {
      const { locationId, startDate, endDate } = JSON.parse(input);
      const result = await DataAnalysisService.analyzeCustomerBehavior(
        locationId,
        new Date(startDate),
        new Date(endDate)
      );
      return JSON.stringify(result);
    } catch (error) {
      logger.error("Error in customer behavior analysis:", error);
      return `Error analyzing customer behavior: ${
        error instanceof Error ? error.message : String(error)
      }`;
    }
  }
}

export class ProductPerformanceTool extends Tool {
  name = "analyze_product_performance";
  description =
    "Analyze product performance metrics and identify top/bottom performers. Input should be a JSON string with locationId, startDate, and endDate.";

  async _call(input: string): Promise<string> {
    try {
      const { locationId, startDate, endDate } = JSON.parse(input);
      const result = await DataAnalysisService.analyzeProductPerformance(
        locationId,
        new Date(startDate),
        new Date(endDate)
      );
      return JSON.stringify(result);
    } catch (error) {
      logger.error("Error in product performance analysis:", error);
      return `Error analyzing product performance: ${
        error instanceof Error ? error.message : String(error)
      }`;
    }
  }
}

export class ProductGapAnalysisTool extends Tool {
  name = "analyze_product_gaps";
  description =
    "Analyze product gaps and opportunities by comparing our product performance with competitor data. Input should be a JSON string with locationId, startDate, and endDate.";

  async _call(input: string): Promise<string> {
    try {
      const {
        locationId,
        startDate,
        endDate,
        usePosData = true,
      } = JSON.parse(input);

      let ourProductAnalysis;
      let dataSourceDetails = {
        source: "unknown",
        description: "",
      };

      // Try to get our product performance data based on POS data if usePosData flag is true
      if (usePosData) {
        try {
          // Get our product performance data
          ourProductAnalysis =
            await DataAnalysisService.analyzeProductPerformance(
              locationId,
              new Date(startDate),
              new Date(endDate)
            );

          dataSourceDetails = {
            source: "pos_data",
            description: "Analysis based on actual sales performance data",
          };

          logger.info({
            message: "Retrieved our product performance data",
            productCount: ourProductAnalysis?.data?.productData?.length || 0,
            dataSource: ourProductAnalysis?.data?.dataSource || "unknown",
          });
        } catch (posError) {
          logger.warn(
            "Error retrieving POS data, falling back to product catalog data",
            posError
          );
          // If POS data retrieval fails, fall back to product catalog data
          ourProductAnalysis = await this.getProductCatalogAnalysis(locationId);

          dataSourceDetails = {
            source: "product_catalog",
            description:
              "Analysis based on product catalog data only (no sales performance data)",
          };
        }
      } else {
        // Explicitly use the product catalog based analysis when usePosData is false
        ourProductAnalysis = await this.getProductCatalogAnalysis(locationId);

        dataSourceDetails = {
          source: "product_catalog",
          description:
            "Analysis based on product catalog data only (no sales performance data)",
        };

        logger.info({
          message: "Retrieved our product catalog data (non-POS)",
          productCount: ourProductAnalysis?.data?.productData?.length || 0,
          dataSource: ourProductAnalysis?.data?.dataSource || "product_catalog",
        });
      }

      // Get competitor product data from Supabase
      const competitorAnalysis =
        await CompetitorAnalysisService.analyzeCompetitorProducts(
          locationId,
          new Date(startDate),
          new Date(endDate)
        );

      logger.info({
        message: "Retrieved competitor product data",
        productCount: competitorAnalysis?.data?.productData?.length || 0,
        dataSource: competitorAnalysis?.data?.dataSource || "unknown",
      });

      // Analyze gaps and opportunities
      const gaps = await this.identifyProductGaps(
        ourProductAnalysis,
        competitorAnalysis
      );

      // Add data source information to insights if using product catalog
      if (dataSourceDetails.source === "product_catalog") {
        if (
          !gaps.insights.some((insight) =>
            insight.includes("PRODUCT CATALOG DATA")
          )
        ) {
          gaps.insights.unshift(
            `[DATA SOURCE] This analysis uses product catalog data only, not actual sales data. Results reflect product offerings rather than sales performance.`
          );
        }
      }

      // Use LLM to enhance the analysis with additional insights
      const llmEnhancedAnalysis = await this.enhanceAnalysisWithLLM(
        ourProductAnalysis,
        competitorAnalysis,
        gaps
      );

      // Add data source to LLM prompting so it's aware of the source
      if (
        dataSourceDetails.source === "product_catalog" &&
        llmEnhancedAnalysis.insights
      ) {
        if (
          !llmEnhancedAnalysis.insights.some((insight) =>
            insight.includes("product catalog")
          )
        ) {
          llmEnhancedAnalysis.insights.unshift(
            `Note: This analysis is based on product catalog data, not sales performance data.`
          );
        }
      }

      return JSON.stringify({
        type: "product_gap_analysis",
        data: {
          ourProducts: ourProductAnalysis.data,
          competitorProducts: competitorAnalysis.data,
          gaps,
          llmAnalysis: llmEnhancedAnalysis,
        },
        insights: [...gaps.insights, ...(llmEnhancedAnalysis.insights || [])],
        recommendations: [
          ...gaps.recommendations,
          ...(llmEnhancedAnalysis.recommendations || []),
        ],
        marketReport: llmEnhancedAnalysis.marketReport || null,
        dataSource: ourProductAnalysis?.data?.dataSource || "unknown",
        dataSourceDetails,
      });
    } catch (error) {
      logger.error("Error in product gap analysis:", error);
      return `Error analyzing product gaps: ${
        error instanceof Error ? error.message : String(error)
      }`;
    }
  }

  /**
   * Get product analysis from product catalog (without POS data)
   * This serves as a backup when POS data is unavailable
   */
  private async getProductCatalogAnalysis(locationId: number) {
    try {
      // Query the products table to get our product catalog
      const products = await Product.query()
        .where("location_id", locationId)
        .select([
          "product_name",
          "category as master_category",
          "subcategory",
          "latest_price as avg_price",
          "brand_name",
        ]);

      // Transform product data to match format expected by identifyProductGaps
      const productData = products.map((product: any) => ({
        product_name: product.product_name,
        master_category: product.master_category || "Unknown",
        avg_price: product.avg_price || 0,
        units_sold: null, // We don't have sales data
        revenue: null, // We don't have revenue data
        profit: null, // We don't have profit data
      }));

      // Generate simplified insights for catalog-only data
      const insights: string[] = [];
      if (productData.length > 0) {
        insights.push(
          `[PRODUCT CATALOG DATA] Product catalog contains ${
            productData.length
          } products across ${
            new Set(productData.map((p: any) => p.master_category)).size
          } categories`
        );
        insights.push(
          `Note: This analysis is based on product catalog data only, not actual sales performance data.`
        );
      } else {
        insights.push("No products found in product catalog");
      }

      return {
        type: "product_catalog",
        data: {
          productData,
          dataSource: "product_catalog",
        },
        insights,
        recommendations: [],
      };
    } catch (error) {
      logger.error("Error retrieving product catalog data:", error);
      throw error;
    }
  }

  private async identifyProductGaps(
    ourAnalysis: any,
    competitorAnalysis: any
  ): Promise<{
    insights: string[];
    recommendations: string[];
    categories: {
      missing: string[];
      overlapping: string[];
      unique: string[];
    };
    priceGaps: Array<{
      productName: string;
      ourPrice: number;
      competitorAvgPrice: number;
      priceDifference: number;
      percentageDifference: number;
    }>;
    popularCompetitorProducts: Array<{
      name: string;
      category: string;
      popularity: number;
      competitorName?: string;
    }>;
  }> {
    const insights: string[] = [];
    const recommendations: string[] = [];

    // Default return value for categories
    const categories: {
      missing: string[];
      overlapping: string[];
      unique: string[];
    } = {
      missing: [],
      overlapping: [],
      unique: [],
    };

    // Default return values for pricing and popular products
    const priceGaps: Array<{
      productName: string;
      ourPrice: number;
      competitorAvgPrice: number;
      priceDifference: number;
      percentageDifference: number;
    }> = [];

    const popularCompetitorProducts: Array<{
      name: string;
      category: string;
      popularity: number;
      competitorName?: string;
    }> = [];

    try {
      // Get our product data directly from the ourAnalysis object
      const ourProductData = ourAnalysis?.data?.productData || [];

      // Get competitor product data from the competitorAnalysis object
      const competitorProductData = competitorAnalysis?.data?.productData || [];

      // If we still don't have any data, return minimal analysis
      if (ourProductData.length === 0 && competitorProductData.length === 0) {
        insights.push("Insufficient data available for analysis");
        recommendations.push(
          "Consider setting up data collection for both your products and competitors"
        );
        return {
          insights,
          recommendations,
          categories,
          priceGaps,
          popularCompetitorProducts,
        };
      }

      // If we only have competitor data, provide limited analysis
      if (ourProductData.length === 0 && competitorProductData.length > 0) {
        insights.push(
          `No product data available for your store, but found ${competitorProductData.length} competitor products`
        );
        recommendations.push("Set up product tracking to enable full analysis");

        // At least analyze competitor products by category
        const competitorCategories: string[] = Array.from(
          new Set(
            competitorProductData.map((p: any) => p.master_category as string)
          )
        );

        categories.missing = competitorCategories;

        // Find popular competitor products
        popularCompetitorProducts.push(
          ...this.findPopularCompetitorProducts(competitorProductData)
        );

        if (popularCompetitorProducts.length > 0) {
          insights.push(
            `Identified ${popularCompetitorProducts.length} popular competitor products that could be added to your inventory`
          );
          recommendations.push(
            "Consider adding these popular competitor products to your inventory"
          );
        }

        return {
          insights,
          recommendations,
          categories,
          priceGaps,
          popularCompetitorProducts,
        };
      }

      // Standard analysis when we have both our data and competitor data
      if (ourProductData.length > 0 && competitorProductData.length > 0) {
        // Identify categories where competitors have products but we don't
        const ourCategories: Set<string> = new Set(
          ourProductData.map((p: any) => p.master_category as string)
        );
        const competitorCategories: Set<string> = new Set(
          competitorProductData.map((p: any) => p.master_category as string)
        );

        categories.missing = [...competitorCategories].filter(
          (cat: string) => !ourCategories.has(cat)
        );

        categories.unique = [...ourCategories].filter(
          (cat: string) => !competitorCategories.has(cat)
        );

        categories.overlapping = [...ourCategories].filter((cat: string) =>
          competitorCategories.has(cat)
        );

        if (categories.missing.length > 0) {
          insights.push(
            `We are missing products in these categories: ${categories.missing.join(
              ", "
            )}`
          );
          recommendations.push(
            `Consider expanding product offerings in: ${categories.missing.join(
              ", "
            )}`
          );
        }

        if (categories.unique.length > 0) {
          insights.push(
            `We offer unique products in these categories that competitors don't have: ${categories.unique.join(
              ", "
            )}`
          );
          recommendations.push(
            `Consider promoting our unique offerings in: ${categories.unique.join(
              ", "
            )}`
          );
        }

        // Identify price gaps
        const calculatedPriceGaps = this.analyzePriceGaps(
          ourProductData,
          competitorProductData
        );
        priceGaps.push(...calculatedPriceGaps);

        if (priceGaps.length > 0) {
          insights.push(
            `Found ${priceGaps.length} products with significant price differences compared to competitors`
          );
          recommendations.push(
            ...priceGaps.map(
              (gap) =>
                `Review pricing strategy for ${gap.productName} (our price: $${gap.ourPrice}, competitor avg: $${gap.competitorAvgPrice})`
            )
          );
        }

        // Identify popular competitor products we don't carry
        const popularProducts = this.findPopularCompetitorProducts(
          competitorProductData
        );
        popularCompetitorProducts.push(...popularProducts);

        if (popularCompetitorProducts.length > 0) {
          insights.push(
            `Competitors have ${popularCompetitorProducts.length} popular products we don't carry`
          );
          recommendations.push(
            ...popularCompetitorProducts.map(
              (product) =>
                `Consider adding ${product.name} to our product lineup`
            )
          );
        }
      }

      // Generate high-level market insights
      const totalOurProducts = ourProductData.length;
      const totalCompetitorProducts = competitorProductData.length;
      const productRatio = totalOurProducts / totalCompetitorProducts;

      if (productRatio < 0.5) {
        insights.push(
          `Your product offering (${totalOurProducts}) is significantly smaller than your competitors' (${totalCompetitorProducts})`
        );
        recommendations.push(
          `Consider expanding your product catalog to increase competitiveness`
        );
      } else if (productRatio > 2) {
        insights.push(
          `Your product offering (${totalOurProducts}) is significantly larger than your competitors' (${totalCompetitorProducts})`
        );
        recommendations.push(
          `Analyze which of your products perform best and consider optimizing your catalog`
        );
      }

      return {
        insights,
        recommendations,
        categories,
        priceGaps,
        popularCompetitorProducts,
      };
    } catch (error) {
      logger.error("Error identifying product gaps:", error);
      return {
        insights: ["Error analyzing product gaps"],
        recommendations: ["Please try again with a different date range"],
        categories,
        priceGaps,
        popularCompetitorProducts,
      };
    }
  }

  private analyzePriceGaps(
    ourProducts: any[],
    competitorProducts: any[]
  ): Array<{
    productName: string;
    ourPrice: number;
    competitorAvgPrice: number;
    priceDifference: number;
    percentageDifference: number;
  }> {
    const gaps = [];
    const PRICE_DIFFERENCE_THRESHOLD = 0.2; // 20% difference

    for (const ourProduct of ourProducts) {
      // Skip products with no price data
      if (ourProduct.avg_price === null || ourProduct.avg_price === undefined) {
        continue;
      }

      const competitorMatches = competitorProducts.filter(
        (p: any) => p.master_category === ourProduct.master_category
      );

      if (competitorMatches.length > 0) {
        // Filter out competitors with no price data
        const matchesWithPrices = competitorMatches.filter(
          (p: any) => p.avg_price !== null && p.avg_price !== undefined
        );

        if (matchesWithPrices.length === 0) {
          continue;
        }

        const avgCompetitorPrice =
          matchesWithPrices.reduce(
            (sum: number, p: any) => sum + p.avg_price,
            0
          ) / matchesWithPrices.length;

        const priceDifference = ourProduct.avg_price - avgCompetitorPrice;
        const percentageDifference = priceDifference / avgCompetitorPrice;

        if (Math.abs(percentageDifference) > PRICE_DIFFERENCE_THRESHOLD) {
          gaps.push({
            productName: ourProduct.product_name,
            ourPrice: ourProduct.avg_price,
            competitorAvgPrice: avgCompetitorPrice,
            priceDifference,
            percentageDifference,
          });
        }
      }
    }

    return gaps;
  }

  private findPopularCompetitorProducts(competitorProducts: any[]): Array<{
    name: string;
    category: string;
    popularity: number;
    competitorName?: string;
  }> {
    // Sort products by popularity (could be based on sales, reviews, etc.)
    return competitorProducts
      .sort((a: any, b: any) => (b.popularity || 0) - (a.popularity || 0))
      .slice(0, 5) // Get top 5 popular products
      .map((p: any) => ({
        name: p.product_name,
        category: p.master_category,
        popularity: p.popularity || 0,
        competitorName: p.competitor_name,
      }));
  }

  private async enhanceAnalysisWithLLM(
    ourProductAnalysis: any,
    competitorAnalysis: any,
    gapAnalysis: any
  ): Promise<{
    insights: string[];
    recommendations: string[];
    marketReport?: string;
  }> {
    try {
      // Prepare data for the LLM prompt
      const ourProductData = ourProductAnalysis?.data?.productData || [];
      const competitorProductData = competitorAnalysis?.data?.productData || [];

      // Determine the data source for clear communication to the LLM
      const dataSource = ourProductAnalysis?.data?.dataSource || "unknown";
      const isProductCatalog = dataSource === "product_catalog";
      const dataSourceInfo = isProductCatalog
        ? "NOTE: This analysis is based on PRODUCT CATALOG DATA only, not actual sales performance data. The analysis reflects product offerings rather than sales performance."
        : "This analysis is based on actual sales performance data.";

      // Prepare prompt with structured data for the LLM
      const prompt = `
You are a retail market analysis expert. Analyze the following product data and provide strategic insights and recommendations.

DATA SOURCE INFORMATION: ${dataSourceInfo}
${
  isProductCatalog
    ? "When generating your insights and recommendations, acknowledge this limitation and focus on product offering gaps rather than sales performance metrics."
    : ""
}

OUR PRODUCT DATA (${ourProductData.length} products):
${JSON.stringify(ourProductData.slice(0, 10), null, 2)}
${
  ourProductData.length > 10
    ? `... and ${ourProductData.length - 10} more products`
    : ""
}

COMPETITOR PRODUCT DATA (${competitorProductData.length} products):
${JSON.stringify(competitorProductData.slice(0, 10), null, 2)}
${
  competitorProductData.length > 10
    ? `... and ${competitorProductData.length - 10} more products`
    : ""
}

INITIAL GAP ANALYSIS:
Categories missing from our inventory: ${
        gapAnalysis.categories.missing.join(", ") || "None"
      }
Categories unique to our inventory: ${
        gapAnalysis.categories.unique.join(", ") || "None"
      }
Number of products with significant price differences: ${
        gapAnalysis.priceGaps.length
      }
Popular competitor products we don't carry: ${
        gapAnalysis.popularCompetitorProducts
          .map(
            (p: {
              name: string;
              category: string;
              popularity: number;
              competitorName?: string;
            }) => p.name
          )
          .join(", ") || "None"
      }

Based on this data, please provide:
1. A list of 3-5 key market insights that weren't already identified in the initial gap analysis
2. A list of 3-5 strategic recommendations that weren't already identified
3. A brief market position summary report (max 200 words)

Format your response as a JSON object with the following structure:
{
  "insights": ["insight 1", "insight 2", ...],
  "recommendations": ["recommendation 1", "recommendation 2", ...],
  "marketReport": "your concise market position summary here"
}
`;

      // Use OpenAI directly instead of ChatAIService
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });

      // Make the request
      const response = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content:
              "You are a retail market analysis expert specializing in data-driven insights.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.2, // Lower temperature for more deterministic outputs
      });

      const llmResponse = response.choices[0]?.message?.content || "";

      // Parse the LLM response
      try {
        // The response may be direct JSON or text with JSON
        const jsonMatch =
          llmResponse.match(/```json\s*([\s\S]*?)\s*```/) ||
          llmResponse.match(/{[\s\S]*}/);

        const jsonString = jsonMatch
          ? jsonMatch[0].replace(/```json|```/g, "")
          : llmResponse;
        const parsedResponse = JSON.parse(jsonString);

        return {
          insights: parsedResponse.insights || [],
          recommendations: parsedResponse.recommendations || [],
          marketReport: parsedResponse.marketReport || "",
        };
      } catch (parseError) {
        logger.error("Error parsing LLM response:", parseError);
        // If parsing failed, try to extract insights and recommendations directly
        const insights = llmResponse.match(
          /insights?:?\s*(.*?)(?=recommendations?:|$)/is
        );
        const recommendations = llmResponse.match(
          /recommendations?:?\s*(.*?)(?=market|$)/is
        );

        return {
          insights: insights
            ? [insights[1].trim()]
            : ["Unable to generate AI-enhanced insights"],
          recommendations: recommendations
            ? [recommendations[1].trim()]
            : ["Unable to generate AI-enhanced recommendations"],
        };
      }
    } catch (error) {
      logger.error("Error enhancing analysis with LLM:", error);
      return {
        insights: ["Unable to generate AI-enhanced insights"],
        recommendations: ["Unable to generate AI-enhanced recommendations"],
      };
    }
  }
}
