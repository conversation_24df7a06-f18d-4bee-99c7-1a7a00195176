/* eslint-disable indent */
import { Tool } from "langchain/tools";
import { logger } from "../../config/logger";
import { PosData } from "../../pos/PosData";
import { DataAnalysisService } from "../../analysis/DataAnalysisService";
import { OpenAI } from "openai";

/**
 * Tool for analyzing profit margins at various levels (product, category, overall)
 */
export class MarginAnalysisTool extends Tool {
  name = "analyze_margins";
  description =
    "Analyze profit margins for products, categories, or the entire business. Input should be a JSON string with locationId, startDate, endDate, and optionally level (product, category, or overall) and target (specific product or category name).";

  async _call(input: string): Promise<string> {
    try {
      const {
        locationId,
        startDate,
        endDate,
        level = "overall",
        target = null,
      } = JSON.parse(input);

      const result = await this.performMarginAnalysis(
        locationId,
        new Date(startDate),
        new Date(endDate),
        level,
        target
      );

      return JSON.stringify(result);
    } catch (error) {
      logger.error("Error in margin analysis:", error);
      return `Error analyzing margins: ${
        error instanceof Error ? error.message : String(error)
      }`;
    }
  }

  /**
   * Performs detailed margin analysis for different levels of the business
   */
  private async performMarginAnalysis(
    locationId: number,
    startDate: Date,
    endDate: Date,
    level: "product" | "category" | "overall",
    target?: string | null
  ) {
    try {
      // Base query to get financial data
      let query = PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate);

      // Apply filters based on level and target
      if (level === "product" && target) {
        query = query.where("product_name", target);
      } else if (level === "category" && target) {
        query = query.where("master_category", target);
      }

      // Execute the query
      const data = await query;

      // Calculate aggregate metrics
      const totalGrossSales = data.reduce(
        (sum: number, record: PosData) => sum + (record.gross_sales || 0),
        0
      );
      const totalNetSales = data.reduce(
        (sum: number, record: PosData) => sum + (record.net_sales || 0),
        0
      );
      const totalCost = data.reduce(
        (sum: number, record: PosData) => sum + (record.inventory_cost || 0),
        0
      );
      const totalProfit = data.reduce(
        (sum: number, record: PosData) => sum + (record.inventory_profit || 0),
        0
      );

      // Calculate margins
      const grossMargin =
        totalGrossSales > 0 ? (totalProfit / totalGrossSales) * 100 : 0;
      const netMargin =
        totalNetSales > 0 ? (totalProfit / totalNetSales) * 100 : 0;

      // Calculate discount rate
      const totalDiscounts = data.reduce(
        (sum: number, record: PosData) => sum + (record.discounted_amount || 0),
        0
      );
      const discountRate =
        totalGrossSales > 0 ? (totalDiscounts / totalGrossSales) * 100 : 0;

      // Group and analyze by time periods (monthly)
      const monthlyData = this.groupDataByMonth(data);

      // For product or category level, calculate more detailed metrics
      let detailedAnalysis = {};

      if (level === "product" || level === "category") {
        const groupField =
          level === "product" ? "product_name" : "master_category";
        detailedAnalysis = await this.calculateDetailedMetrics(
          data,
          groupField
        );
      }

      // Generate insights
      const insights = this.generateInsights({
        level,
        target,
        grossMargin,
        netMargin,
        discountRate,
        totalGrossSales,
        totalNetSales,
        totalProfit,
        monthlyData,
      });

      // Generate recommendations
      const recommendations = this.generateRecommendations({
        grossMargin,
        netMargin,
        discountRate,
        monthlyData,
      });

      return {
        type: "margin_analysis",
        data: {
          level,
          target,
          summary: {
            totalGrossSales,
            totalNetSales,
            totalCost,
            totalProfit,
            grossMargin,
            netMargin,
            discountRate,
          },
          monthlyTrends: monthlyData,
          detailedAnalysis,
        },
        insights,
        recommendations,
      };
    } catch (error) {
      logger.error("Error performing margin analysis:", error);
      throw error;
    }
  }

  /**
   * Groups financial data by month for trend analysis
   */
  private groupDataByMonth(data: PosData[]) {
    const monthlyData: Record<string, any> = {};

    for (const record of data) {
      const date = new Date(record.order_date);
      const monthKey = `${date.getFullYear()}-${String(
        date.getMonth() + 1
      ).padStart(2, "0")}`;

      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          grossSales: 0,
          netSales: 0,
          cost: 0,
          profit: 0,
          discounts: 0,
        };
      }

      monthlyData[monthKey].grossSales += record.gross_sales || 0;
      monthlyData[monthKey].netSales += record.net_sales || 0;
      monthlyData[monthKey].cost += record.inventory_cost || 0;
      monthlyData[monthKey].profit += record.inventory_profit || 0;
      monthlyData[monthKey].discounts += record.discounted_amount || 0;
    }

    // Calculate margins for each month
    Object.keys(monthlyData).forEach((month) => {
      const m = monthlyData[month];
      m.grossMargin = m.grossSales > 0 ? (m.profit / m.grossSales) * 100 : 0;
      m.netMargin = m.netSales > 0 ? (m.profit / m.netSales) * 100 : 0;
      m.discountRate =
        m.grossSales > 0 ? (m.discounts / m.grossSales) * 100 : 0;
    });

    return monthlyData;
  }

  /**
   * Calculates detailed metrics for product or category level analysis
   */
  private async calculateDetailedMetrics(data: PosData[], groupField: string) {
    const groupedData: Record<string, any> = {};

    for (const record of data) {
      const key = record[groupField as keyof PosData] as string;
      if (!key) continue;

      if (!groupedData[key]) {
        groupedData[key] = {
          grossSales: 0,
          netSales: 0,
          cost: 0,
          profit: 0,
          discounts: 0,
          count: 0,
        };
      }

      groupedData[key].grossSales += record.gross_sales || 0;
      groupedData[key].netSales += record.net_sales || 0;
      groupedData[key].cost += record.inventory_cost || 0;
      groupedData[key].profit += record.inventory_profit || 0;
      groupedData[key].discounts += record.discounted_amount || 0;
      groupedData[key].count += 1;
    }

    // Calculate margins for each group
    Object.keys(groupedData).forEach((key) => {
      const g = groupedData[key];
      g.grossMargin = g.grossSales > 0 ? (g.profit / g.grossSales) * 100 : 0;
      g.netMargin = g.netSales > 0 ? (g.profit / g.netSales) * 100 : 0;
      g.discountRate =
        g.grossSales > 0 ? (g.discounts / g.grossSales) * 100 : 0;
      g.averagePrice = g.count > 0 ? g.grossSales / g.count : 0;
    });

    return groupedData;
  }

  /**
   * Generates insights based on margin analysis
   */
  private generateInsights(data: any): string[] {
    const insights: string[] = [];

    // Overall margin insights
    insights.push(
      `Overall gross profit margin: ${data.grossMargin.toFixed(2)}%`
    );

    if (data.grossMargin < 30) {
      insights.push("Gross profit margin is below industry average (30-35%)");
    } else if (data.grossMargin > 50) {
      insights.push(
        "Gross profit margin is above industry average, which could indicate premium positioning"
      );
    }

    // Discount rate insights
    if (data.discountRate > 15) {
      insights.push(
        `High discount rate (${data.discountRate.toFixed(
          2
        )}%) may be eroding margins`
      );
    }

    // Trend analysis from monthly data
    const months = Object.keys(data.monthlyData).sort();
    if (months.length >= 2) {
      const firstMonth = data.monthlyData[months[0]];
      const lastMonth = data.monthlyData[months[months.length - 1]];

      const marginChange = lastMonth.grossMargin - firstMonth.grossMargin;
      if (Math.abs(marginChange) > 5) {
        const direction = marginChange > 0 ? "increased" : "decreased";
        insights.push(
          `Profit margin has ${direction} by ${Math.abs(marginChange).toFixed(
            2
          )} percentage points over the analysis period`
        );
      }
    }

    return insights;
  }

  /**
   * Generates recommendations based on margin analysis
   */
  private generateRecommendations(data: any): string[] {
    const recommendations: string[] = [];

    // Margin-based recommendations
    if (data.grossMargin < 30) {
      recommendations.push(
        "Consider reviewing pricing strategy to improve gross margins"
      );
      recommendations.push("Evaluate inventory cost management to reduce COGS");
    }

    // Discount-based recommendations
    if (data.discountRate > 15) {
      recommendations.push(
        "Analyze discount strategy to determine impact on profitability"
      );
      recommendations.push(
        "Consider more targeted promotions instead of across-the-board discounts"
      );
    }

    // Trend-based recommendations
    const months = Object.keys(data.monthlyData).sort();
    if (months.length >= 2) {
      const firstMonth = data.monthlyData[months[0]];
      const lastMonth = data.monthlyData[months[months.length - 1]];

      const marginChange = lastMonth.grossMargin - firstMonth.grossMargin;
      if (marginChange < -5) {
        recommendations.push(
          "Investigate causes of declining profit margins and develop an action plan"
        );
      }
    }

    return recommendations;
  }
}

/**
 * Tool for forecasting revenue based on historical data
 */
export class RevenueForecasterTool extends Tool {
  name = "forecast_revenue";
  description =
    "Generate revenue forecasts based on historical sales data. Input should be a JSON string with locationId, historicalStartDate, historicalEndDate, forecastPeriod (days), and optionally categoryFilter or productFilter.";

  async _call(input: string): Promise<string> {
    try {
      const {
        locationId,
        historicalStartDate,
        historicalEndDate,
        forecastPeriod = 30,
        categoryFilter = null,
        productFilter = null,
      } = JSON.parse(input);

      const result = await this.generateRevenueForecast(
        locationId,
        new Date(historicalStartDate),
        new Date(historicalEndDate),
        forecastPeriod,
        categoryFilter,
        productFilter
      );

      return JSON.stringify(result);
    } catch (error) {
      logger.error("Error in revenue forecasting:", error);
      return `Error forecasting revenue: ${
        error instanceof Error ? error.message : String(error)
      }`;
    }
  }

  /**
   * Generates revenue forecasts based on historical data and time series analysis
   */
  private async generateRevenueForecast(
    locationId: number,
    historicalStartDate: Date,
    historicalEndDate: Date,
    forecastPeriod: number,
    categoryFilter?: string | null,
    productFilter?: string | null
  ) {
    try {
      // Query to get historical financial data with appropriate filters
      let query = PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", historicalStartDate)
        .where("order_date", "<=", historicalEndDate)
        .orderBy("order_date", "asc");

      if (categoryFilter) {
        query = query.where("master_category", categoryFilter);
      }

      if (productFilter) {
        query = query.where("product_name", productFilter);
      }

      // Get daily aggregated revenue data for time series analysis
      const rawData = await query
        .select([
          PosData.raw("DATE(order_date) as date"),
          PosData.raw("SUM(gross_sales) as daily_revenue"),
          PosData.raw("SUM(net_sales) as daily_net_revenue"),
          PosData.raw("SUM(inventory_profit) as daily_profit"),
        ])
        .groupBy("date");

      // Convert to time series format for analysis
      const timeSeriesData = rawData.map((record: any) => ({
        date: record.date,
        value: record.daily_revenue,
      }));

      // Leverage existing time series analysis from DataAnalysisService
      const timeSeriesAnalysis = await DataAnalysisService.analyzeTimeSeries(
        timeSeriesData
      );

      // Generate forecasted values for the requested period
      const forecastData = this.generateForecastedData(
        timeSeriesData,
        timeSeriesAnalysis,
        forecastPeriod
      );

      // Calculate forecast metrics
      const historicalTotal = timeSeriesData.reduce(
        (sum: number, day: { value: number }) => sum + day.value,
        0
      );
      const forecastTotal = forecastData.reduce(
        (sum: number, day: { value: number }) => sum + day.value,
        0
      );
      const forecastAverage = forecastTotal / forecastData.length;
      const growthRate =
        historicalTotal > 0
          ? (forecastTotal /
              forecastPeriod /
              (historicalTotal / timeSeriesData.length) -
              1) *
            100
          : 0;

      // Apply category or product level forecasting if specified
      let detailedForecast = {};
      if (categoryFilter || productFilter) {
        detailedForecast = await this.generateDetailedForecast(
          locationId,
          historicalStartDate,
          historicalEndDate,
          forecastPeriod,
          categoryFilter,
          productFilter
        );
      }

      // Generate insights
      const insights = this.generateForecastInsights({
        timeSeriesAnalysis,
        forecastData,
        historicalTotal,
        forecastTotal,
        growthRate,
        categoryFilter,
        productFilter,
        forecastPeriod,
      });

      // Generate recommendations
      const recommendations = this.generateForecastRecommendations({
        timeSeriesAnalysis,
        growthRate,
        categoryFilter,
        productFilter,
      });

      return {
        type: "revenue_forecast",
        data: {
          historicalData: timeSeriesData,
          forecastData,
          summary: {
            historicalTotal,
            forecastTotal,
            forecastAverage,
            growthRate,
            trend: timeSeriesAnalysis.trend,
            seasonality: timeSeriesAnalysis.seasonality,
            confidence: timeSeriesAnalysis.forecast.confidence,
          },
          detailedForecast,
          filters: {
            categoryFilter,
            productFilter,
          },
        },
        insights,
        recommendations,
      };
    } catch (error) {
      logger.error("Error generating revenue forecast:", error);
      throw error;
    }
  }

  /**
   * Generates detailed category or product level forecasts
   */
  private async generateDetailedForecast(
    locationId: number,
    historicalStartDate: Date,
    historicalEndDate: Date,
    forecastPeriod: number,
    categoryFilter?: string | null,
    productFilter?: string | null
  ) {
    // Group level determines whether we're forecasting by category or product
    const groupLevel = productFilter ? "product" : "category";
    const groupField =
      groupLevel === "product" ? "product_name" : "master_category";

    // Query to get historical data grouped by the appropriate field
    let query = PosData.query()
      .where("location_id", locationId)
      .where("order_date", ">=", historicalStartDate)
      .where("order_date", "<=", historicalEndDate);

    if (categoryFilter) {
      query = query.where("master_category", categoryFilter);
    }

    if (productFilter) {
      query = query.where("product_name", productFilter);
    }

    // Get revenue data grouped by the appropriate field
    const groupedData = await query
      .select([
        groupField,
        PosData.raw("SUM(gross_sales) as total_revenue"),
        PosData.raw("AVG(gross_sales) as avg_revenue"),
        PosData.raw("COUNT(*) as transaction_count"),
      ])
      .groupBy(groupField);

    // Process each group to generate a simple forecast
    const detailedForecasts: Record<string, any> = {};

    for (const group of groupedData) {
      const name = group[groupField as keyof typeof group] as string;
      const dailyAverage =
        group.total_revenue /
        ((historicalEndDate.getTime() - historicalStartDate.getTime()) /
          (1000 * 60 * 60 * 24));

      // Simple linear forecast (could be enhanced with more sophisticated modeling)
      detailedForecasts[name] = {
        historicalTotal: group.total_revenue,
        historicalAverage: dailyAverage,
        forecastTotal: dailyAverage * forecastPeriod,
        transactionCount: group.transaction_count,
      };
    }

    return detailedForecasts;
  }

  /**
   * Generates forecasted data points based on time series analysis
   */
  private generateForecastedData(
    historicalData: Array<{ date: string; value: number }>,
    timeSeriesAnalysis: any,
    forecastPeriod: number
  ) {
    const forecastData: Array<{ date: string; value: number }> = [];

    // Get the last date in the historical data
    const lastDate = new Date(historicalData[historicalData.length - 1].date);

    // Basic slope and intercept from the regression analysis
    const slope =
      timeSeriesAnalysis.trend.direction === "up"
        ? timeSeriesAnalysis.trend.magnitude
        : timeSeriesAnalysis.trend.direction === "down"
        ? -timeSeriesAnalysis.trend.magnitude
        : 0;

    // Last value from historical data as baseline
    const lastValue = historicalData[historicalData.length - 1].value;

    // Generate forecast for each day in the forecast period
    for (let i = 1; i <= forecastPeriod; i++) {
      const forecastDate = new Date(lastDate);
      forecastDate.setDate(forecastDate.getDate() + i);

      // Calculate forecasted value with trend and seasonality
      let forecastedValue = lastValue + slope * i;

      // Apply seasonality if detected
      if (
        timeSeriesAnalysis.seasonality.detected &&
        timeSeriesAnalysis.seasonality.period
      ) {
        const seasonalPeriod = timeSeriesAnalysis.seasonality.period;
        const seasonalIndex = (historicalData.length + i) % seasonalPeriod;
        const seasonalReference = Math.max(0, seasonalIndex - 1);

        // Apply a seasonality factor if we have historical data for this season
        if (historicalData[seasonalReference]) {
          const seasonalFactor = timeSeriesAnalysis.seasonality.strength || 0.5;
          const seasonalAdjustment =
            (historicalData[seasonalReference].value - lastValue) *
            seasonalFactor;

          forecastedValue += seasonalAdjustment;
        }
      }

      // Ensure forecast values don't go negative
      forecastedValue = Math.max(0, forecastedValue);

      forecastData.push({
        date: forecastDate.toISOString().split("T")[0],
        value: forecastedValue,
      });
    }

    return forecastData;
  }

  /**
   * Generates insights based on forecast analysis
   */
  private generateForecastInsights(data: any): string[] {
    const insights: string[] = [];

    // Generate insights about the trend
    if (data.timeSeriesAnalysis.trend.direction === "up") {
      insights.push(
        `Revenue is trending upward with a confidence level of ${(
          data.timeSeriesAnalysis.trend.confidence * 100
        ).toFixed(1)}%`
      );
    } else if (data.timeSeriesAnalysis.trend.direction === "down") {
      insights.push(
        `Revenue is trending downward with a confidence level of ${(
          data.timeSeriesAnalysis.trend.confidence * 100
        ).toFixed(1)}%`
      );
    } else {
      insights.push(`Revenue is relatively stable with minimal trend`);
    }

    // Generate insights about seasonality
    if (data.timeSeriesAnalysis.seasonality.detected) {
      insights.push(
        `${
          data.timeSeriesAnalysis.seasonality.period
        }-day seasonal pattern detected with ${
          (data.timeSeriesAnalysis.seasonality.strength || 0.5) * 100
        }% strength`
      );
    }

    // Generate insights about the forecast
    if (data.categoryFilter) {
      insights.push(
        `For category "${
          data.categoryFilter
        }", projected revenue for the next ${
          data.forecastPeriod
        } days: $${data.forecastTotal.toFixed(2)}`
      );
    } else if (data.productFilter) {
      insights.push(
        `For product "${data.productFilter}", projected revenue for the next ${
          data.forecastPeriod
        } days: $${data.forecastTotal.toFixed(2)}`
      );
    } else {
      insights.push(
        `Projected total revenue for the next ${
          data.forecastPeriod
        } days: $${data.forecastTotal.toFixed(2)}`
      );
    }

    // Growth rate insights
    if (Math.abs(data.growthRate) > 5) {
      const direction = data.growthRate > 0 ? "increase" : "decrease";
      insights.push(
        `Forecast indicates a ${Math.abs(data.growthRate).toFixed(
          1
        )}% ${direction} in daily revenue compared to historical average`
      );
    }

    return insights;
  }

  /**
   * Generates recommendations based on forecast analysis
   */
  private generateForecastRecommendations(data: any): string[] {
    const recommendations: string[] = [];

    // Base recommendations on trend direction
    if (data.timeSeriesAnalysis.trend.direction === "down") {
      recommendations.push(
        "Consider marketing initiatives to counteract the downward revenue trend"
      );
      recommendations.push("Review pricing strategies to optimize revenue");
    } else if (data.timeSeriesAnalysis.trend.direction === "up") {
      recommendations.push(
        "Ensure inventory levels can meet projected increased demand"
      );
      recommendations.push(
        "Consider capitalizing on growth with targeted marketing"
      );
    }

    // Recommendations based on seasonality
    if (data.timeSeriesAnalysis.seasonality.detected) {
      recommendations.push(
        `Plan staffing and inventory based on the ${data.timeSeriesAnalysis.seasonality.period}-day seasonal pattern`
      );
      recommendations.push(
        "Consider seasonal promotions aligned with identified patterns"
      );
    }

    // Specific recommendations for category/product forecasts
    if (data.categoryFilter || data.productFilter) {
      if (data.growthRate < -10) {
        recommendations.push(
          `Develop strategies to address projected decline in ${
            data.categoryFilter || data.productFilter
          } revenue`
        );
      } else if (data.growthRate > 10) {
        recommendations.push(
          `Ensure sufficient stock of ${
            data.categoryFilter || data.productFilter
          } to meet projected increased demand`
        );
      }
    }

    return recommendations;
  }
}

/**
 * Tool for analyzing and optimizing operational costs
 */
export class CostOptimizationTool extends Tool {
  name = "optimize_costs";
  description =
    "Analyze operational costs and identify optimization opportunities. Input should be a JSON string with locationId, startDate, endDate, and optionally costType (inventory, discount, operations).";

  async _call(input: string): Promise<string> {
    try {
      const {
        locationId,
        startDate,
        endDate,
        costType = "all",
      } = JSON.parse(input);

      const result = await this.analyzeAndOptimizeCosts(
        locationId,
        new Date(startDate),
        new Date(endDate),
        costType
      );

      return JSON.stringify(result);
    } catch (error) {
      logger.error("Error in cost optimization analysis:", error);
      return `Error analyzing costs: ${
        error instanceof Error ? error.message : String(error)
      }`;
    }
  }

  /**
   * Analyzes operational costs and identifies optimization opportunities
   */
  private async analyzeAndOptimizeCosts(
    locationId: number,
    startDate: Date,
    endDate: Date,
    costType: "inventory" | "discount" | "operations" | "all"
  ) {
    try {
      // Base query to get cost data
      const query = PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate);

      // Execute the query
      const data = await query;

      // Analyze different cost types based on the request
      const costAnalysis: any = {
        summary: {},
        details: {},
      };

      // Calculate overall metrics
      const totalRevenue = data.reduce(
        (sum: number, record: PosData) => sum + (record.gross_sales || 0),
        0
      );
      const totalCost = data.reduce(
        (sum: number, record: PosData) => sum + (record.inventory_cost || 0),
        0
      );
      const totalDiscounts = data.reduce(
        (sum: number, record: PosData) => sum + (record.discounted_amount || 0),
        0
      );
      const totalLoyaltyDiscounts = data.reduce(
        (sum: number, record: PosData) =>
          sum + (record.loyalty_as_discount || 0),
        0
      );

      costAnalysis.summary = {
        totalRevenue,
        totalCost,
        totalDiscounts,
        totalLoyaltyDiscounts,
        costToRevenueRatio:
          totalRevenue > 0 ? (totalCost / totalRevenue) * 100 : 0,
        discountRate:
          totalRevenue > 0 ? (totalDiscounts / totalRevenue) * 100 : 0,
      };

      // Perform detailed analysis based on the requested cost type
      if (costType === "all" || costType === "inventory") {
        costAnalysis.details.inventory = await this.analyzeInventoryCosts(data);
      }

      if (costType === "all" || costType === "discount") {
        costAnalysis.details.discounts = await this.analyzeDiscounts(data);
      }

      if (costType === "all" || costType === "operations") {
        costAnalysis.details.operations = await this.analyzeOperationalCosts(
          data
        );
      }

      // Identify optimization opportunities
      const optimizationOpportunities = this.identifyOptimizationOpportunities(
        costAnalysis,
        costType
      );

      // Generate insights
      const insights = this.generateCostInsights(costAnalysis, costType);

      // Generate recommendations
      const recommendations = this.generateCostRecommendations(
        costAnalysis,
        optimizationOpportunities
      );

      return {
        type: "cost_optimization",
        data: {
          summary: costAnalysis.summary,
          details: costAnalysis.details,
          optimizationOpportunities,
        },
        insights,
        recommendations,
      };
    } catch (error) {
      logger.error("Error analyzing and optimizing costs:", error);
      throw error;
    }
  }

  /**
   * Analyzes inventory costs by category and identifies high-cost items
   */
  private async analyzeInventoryCosts(data: PosData[]) {
    // Group data by category
    const categoryCosts: Record<string, any> = {};

    for (const record of data) {
      const category = record.master_category || "Uncategorized";

      if (!categoryCosts[category]) {
        categoryCosts[category] = {
          totalCost: 0,
          totalRevenue: 0,
          itemCount: 0,
          costToRevenueRatio: 0,
        };
      }

      categoryCosts[category].totalCost += record.inventory_cost || 0;
      categoryCosts[category].totalRevenue += record.gross_sales || 0;
      categoryCosts[category].itemCount += 1;
    }

    // Calculate cost-to-revenue ratio for each category
    for (const category in categoryCosts) {
      const c = categoryCosts[category];
      c.costToRevenueRatio =
        c.totalRevenue > 0 ? (c.totalCost / c.totalRevenue) * 100 : 0;
      c.averageCostPerItem = c.itemCount > 0 ? c.totalCost / c.itemCount : 0;
    }

    // Identify high-cost products
    const productCosts = this.getProductCosts(data);

    // Sort products by cost-to-revenue ratio (highest first)
    const highCostProducts = Object.entries(productCosts)
      .filter(([_, product]) => product.totalRevenue > 0) // Filter out products with no revenue
      .sort((a, b) => b[1].costToRevenueRatio - a[1].costToRevenueRatio)
      .slice(0, 10) // Get top 10 highest cost-to-revenue ratio products
      .map(([name, metrics]) => ({
        name,
        ...metrics,
      }));

    return {
      categoryCosts,
      highCostProducts,
      totalInventoryCost: Object.values(categoryCosts).reduce(
        (sum: number, cat: any) => sum + cat.totalCost,
        0
      ),
    };
  }

  /**
   * Analyzes discount patterns and their impact on revenue
   */
  private async analyzeDiscounts(data: PosData[]) {
    // Calculate aggregate discount metrics
    const totalDiscounts = data.reduce(
      (sum, record) => sum + (record.discounted_amount || 0),
      0
    );
    const totalLoyaltyDiscounts = data.reduce(
      (sum, record) => sum + (record.loyalty_as_discount || 0),
      0
    );
    const totalRevenue = data.reduce(
      (sum, record) => sum + (record.gross_sales || 0),
      0
    );

    // Track discounts by day of week
    const discountsByDay: Record<string, any> = {
      0: { total: 0, count: 0 }, // Sunday
      1: { total: 0, count: 0 },
      2: { total: 0, count: 0 },
      3: { total: 0, count: 0 },
      4: { total: 0, count: 0 },
      5: { total: 0, count: 0 },
      6: { total: 0, count: 0 }, // Saturday
    };

    // Track discounts by product category
    const discountsByCategory: Record<string, any> = {};

    for (const record of data) {
      // Day of week analysis
      const date = new Date(record.order_date);
      const dayOfWeek = date.getDay().toString();

      discountsByDay[dayOfWeek].total += record.discounted_amount || 0;
      discountsByDay[dayOfWeek].count += 1;

      // Category analysis
      const category = record.master_category || "Uncategorized";

      if (!discountsByCategory[category]) {
        discountsByCategory[category] = {
          totalDiscounts: 0,
          totalRevenue: 0,
          discountRate: 0,
        };
      }

      discountsByCategory[category].totalDiscounts +=
        record.discounted_amount || 0;
      discountsByCategory[category].totalRevenue += record.gross_sales || 0;
    }

    // Calculate average discounts by day
    for (const day in discountsByDay) {
      discountsByDay[day].average =
        discountsByDay[day].count > 0
          ? discountsByDay[day].total / discountsByDay[day].count
          : 0;
    }

    // Calculate discount rates by category
    for (const category in discountsByCategory) {
      discountsByCategory[category].discountRate =
        discountsByCategory[category].totalRevenue > 0
          ? (discountsByCategory[category].totalDiscounts /
              discountsByCategory[category].totalRevenue) *
            100
          : 0;
    }

    // Get high discount products
    const productDiscounts = this.getProductDiscounts(data);

    // Sort products by discount rate (highest first)
    const highDiscountProducts = Object.entries(productDiscounts)
      .filter(
        ([_, product]) => product.totalRevenue > 0 && product.totalRevenue > 100
      ) // Filter out products with low revenue
      .sort((a, b) => b[1].discountRate - a[1].discountRate)
      .slice(0, 10) // Get top 10 highest discount rate products
      .map(([name, metrics]) => ({
        name,
        ...metrics,
      }));

    return {
      totalDiscounts,
      totalLoyaltyDiscounts,
      overallDiscountRate:
        totalRevenue > 0 ? (totalDiscounts / totalRevenue) * 100 : 0,
      loyaltyDiscountRate:
        totalRevenue > 0 ? (totalLoyaltyDiscounts / totalRevenue) * 100 : 0,
      discountsByDay,
      discountsByCategory,
      highDiscountProducts,
    };
  }

  /**
   * Analyzes operational costs based on available data
   * Note: Limited by what's available in PosData, could be expanded with additional data sources
   */
  private async analyzeOperationalCosts(data: PosData[]) {
    // Group sales by day to identify patterns in operational efficiency
    const salesByDay: Record<string, any> = {};

    for (const record of data) {
      const dateKey = new Date(record.order_date).toISOString().split("T")[0];

      if (!salesByDay[dateKey]) {
        salesByDay[dateKey] = {
          totalRevenue: 0,
          totalCost: 0,
          transactionCount: 0,
          budtenders: new Set(),
        };
      }

      salesByDay[dateKey].totalRevenue += record.gross_sales || 0;
      salesByDay[dateKey].totalCost += record.inventory_cost || 0;
      salesByDay[dateKey].transactionCount += 1;

      if (record.budtender_name) {
        salesByDay[dateKey].budtenders.add(record.budtender_name);
      }
    }

    // Calculate operational metrics by day
    const operationalMetrics = Object.entries(salesByDay).map(
      ([date, metrics]: [string, any]) => {
        const staffCount = metrics.budtenders.size;
        const revenuePerStaff =
          staffCount > 0 ? metrics.totalRevenue / staffCount : 0;
        const transactionsPerStaff =
          staffCount > 0 ? metrics.transactionCount / staffCount : 0;
        const averageTransactionValue =
          metrics.transactionCount > 0
            ? metrics.totalRevenue / metrics.transactionCount
            : 0;

        return {
          date,
          staffCount,
          transactionCount: metrics.transactionCount,
          totalRevenue: metrics.totalRevenue,
          revenuePerStaff,
          transactionsPerStaff,
          averageTransactionValue,
        };
      }
    );

    // Calculate aggregate metrics
    const totalDays = operationalMetrics.length;
    const totalRevenue = operationalMetrics.reduce(
      (sum, day) => sum + day.totalRevenue,
      0
    );
    const totalTransactions = operationalMetrics.reduce(
      (sum, day) => sum + day.transactionCount,
      0
    );
    const avgStaffPerDay =
      operationalMetrics.reduce((sum, day) => sum + day.staffCount, 0) /
      totalDays;
    const avgRevenuePerStaff =
      operationalMetrics.reduce((sum, day) => sum + day.revenuePerStaff, 0) /
      totalDays;
    const avgTransactionsPerStaff =
      operationalMetrics.reduce(
        (sum, day) => sum + day.transactionsPerStaff,
        0
      ) / totalDays;

    // Identify days with low operational efficiency
    const sortedByEfficiency = [...operationalMetrics]
      .filter((day) => day.staffCount > 0)
      .sort((a, b) => a.revenuePerStaff - b.revenuePerStaff);

    const lowEfficiencyDays = sortedByEfficiency.slice(
      0,
      Math.min(5, Math.floor(sortedByEfficiency.length / 2))
    );

    return {
      dailyMetrics: operationalMetrics,
      aggregateMetrics: {
        totalRevenue,
        totalTransactions,
        avgStaffPerDay,
        avgRevenuePerStaff,
        avgTransactionsPerStaff,
        avgTransactionValue:
          totalTransactions > 0 ? totalRevenue / totalTransactions : 0,
      },
      lowEfficiencyDays,
    };
  }

  /**
   * Calculates cost metrics at the product level
   */
  private getProductCosts(data: PosData[]): Record<string, any> {
    const productCosts: Record<string, any> = {};

    for (const record of data) {
      if (!record.product_name) continue;

      if (!productCosts[record.product_name]) {
        productCosts[record.product_name] = {
          totalCost: 0,
          totalRevenue: 0,
          itemCount: 0,
          costToRevenueRatio: 0,
          category: record.master_category,
        };
      }

      productCosts[record.product_name].totalCost += record.inventory_cost || 0;
      productCosts[record.product_name].totalRevenue += record.gross_sales || 0;
      productCosts[record.product_name].itemCount += 1;
    }

    // Calculate ratios
    for (const product in productCosts) {
      const p = productCosts[product];
      p.costToRevenueRatio =
        p.totalRevenue > 0 ? (p.totalCost / p.totalRevenue) * 100 : 0;
      p.averageCostPerItem = p.itemCount > 0 ? p.totalCost / p.itemCount : 0;
    }

    return productCosts;
  }

  /**
   * Calculates discount metrics at the product level
   */
  private getProductDiscounts(data: PosData[]): Record<string, any> {
    const productDiscounts: Record<string, any> = {};

    for (const record of data) {
      if (!record.product_name) continue;

      if (!productDiscounts[record.product_name]) {
        productDiscounts[record.product_name] = {
          totalDiscounts: 0,
          totalRevenue: 0,
          transactionCount: 0,
          discountRate: 0,
          category: record.master_category,
        };
      }

      productDiscounts[record.product_name].totalDiscounts +=
        record.discounted_amount || 0;
      productDiscounts[record.product_name].totalRevenue +=
        record.gross_sales || 0;
      productDiscounts[record.product_name].transactionCount += 1;
    }

    // Calculate rates
    for (const product in productDiscounts) {
      const p = productDiscounts[product];
      p.discountRate =
        p.totalRevenue > 0 ? (p.totalDiscounts / p.totalRevenue) * 100 : 0;
      p.avgDiscountPerTransaction =
        p.transactionCount > 0 ? p.totalDiscounts / p.transactionCount : 0;
    }

    return productDiscounts;
  }

  /**
   * Identifies cost optimization opportunities based on analysis
   */
  private identifyOptimizationOpportunities(
    costAnalysis: any,
    costType: string
  ): any[] {
    const opportunities: any[] = [];

    // Inventory cost opportunities
    if (costType === "all" || costType === "inventory") {
      // High cost-to-revenue ratio categories
      const highCostCategories = Object.entries(
        costAnalysis.details.inventory.categoryCosts
      )
        .filter(
          ([_, category]: [string, any]) => category.costToRevenueRatio > 60
        )
        .sort(
          (a: [string, any], b: [string, any]) =>
            b[1].totalCost - a[1].totalCost
        )
        .slice(0, 3);

      if (highCostCategories.length > 0) {
        opportunities.push({
          type: "inventory",
          subtype: "high_cost_categories",
          impact: "high",
          details: highCostCategories.map(([name, metrics]: [string, any]) => ({
            name,
            costToRevenueRatio: metrics.costToRevenueRatio,
            totalCost: metrics.totalCost,
            potentialSavings: metrics.totalCost * 0.15, // Estimate 15% potential savings
          })),
        });
      }

      // High cost products
      if (costAnalysis.details.inventory.highCostProducts.length > 0) {
        opportunities.push({
          type: "inventory",
          subtype: "high_cost_products",
          impact: "medium",
          details: costAnalysis.details.inventory.highCostProducts
            .filter((product: any) => product.costToRevenueRatio > 75)
            .map((product: any) => ({
              name: product.name,
              costToRevenueRatio: product.costToRevenueRatio,
              totalCost: product.totalCost,
              potentialSavings: product.totalCost * 0.2, // Estimate 20% potential savings
            })),
        });
      }
    }

    // Discount opportunities
    if (costType === "all" || costType === "discount") {
      // High overall discount rate
      if (costAnalysis.details.discounts.overallDiscountRate > 20) {
        opportunities.push({
          type: "discount",
          subtype: "high_overall_discount",
          impact: "high",
          details: {
            currentRate: costAnalysis.details.discounts.overallDiscountRate,
            totalDiscounts: costAnalysis.details.discounts.totalDiscounts,
            potentialSavings:
              costAnalysis.details.discounts.totalDiscounts * 0.25, // Estimate 25% potential reduction
          },
        });
      }

      // Categories with excessive discounts
      const highDiscountCategories = Object.entries(
        costAnalysis.details.discounts.discountsByCategory
      )
        .filter(
          ([_, category]: [string, any]) =>
            category.discountRate > 25 && category.totalRevenue > 1000
        )
        .sort(
          (a: [string, any], b: [string, any]) =>
            b[1].discountRate - a[1].discountRate
        )
        .slice(0, 3);

      if (highDiscountCategories.length > 0) {
        opportunities.push({
          type: "discount",
          subtype: "high_discount_categories",
          impact: "medium",
          details: highDiscountCategories.map(
            ([name, metrics]: [string, any]) => ({
              name,
              discountRate: metrics.discountRate,
              totalDiscounts: metrics.totalDiscounts,
              potentialSavings: metrics.totalDiscounts * 0.3, // Estimate 30% potential reduction
            })
          ),
        });
      }

      // Days with excessive discounts
      const highDiscountDays = Object.entries(
        costAnalysis.details.discounts.discountsByDay
      )
        .filter(([_, day]: [string, any]) => day.average > 0)
        .sort(
          (a: [string, any], b: [string, any]) => b[1].average - a[1].average
        )
        .slice(0, 2);

      if (highDiscountDays.length > 0) {
        opportunities.push({
          type: "discount",
          subtype: "high_discount_days",
          impact: "medium",
          details: highDiscountDays.map(([day, metrics]: [string, any]) => ({
            day: [
              "Sunday",
              "Monday",
              "Tuesday",
              "Wednesday",
              "Thursday",
              "Friday",
              "Saturday",
            ][parseInt(day)],
            averageDiscount: metrics.average,
            totalDiscounts: metrics.total,
            potentialSavings: metrics.total * 0.2, // Estimate 20% potential reduction
          })),
        });
      }
    }

    // Operational cost opportunities
    if (costType === "all" || costType === "operations") {
      // Low staff efficiency days
      if (costAnalysis.details.operations.lowEfficiencyDays.length > 0) {
        const avgEfficiency =
          costAnalysis.details.operations.aggregateMetrics.avgRevenuePerStaff;
        const lowEfficiencyImpact =
          costAnalysis.details.operations.lowEfficiencyDays.reduce(
            (sum: number, day: any) =>
              sum + (avgEfficiency - day.revenuePerStaff) * day.staffCount,
            0
          );

        opportunities.push({
          type: "operations",
          subtype: "staff_efficiency",
          impact: "medium",
          details: {
            lowEfficiencyDays:
              costAnalysis.details.operations.lowEfficiencyDays.map(
                (day: any) => ({
                  date: day.date,
                  staffCount: day.staffCount,
                  revenuePerStaff: day.revenuePerStaff,
                  efficiencyGap: avgEfficiency - day.revenuePerStaff,
                })
              ),
            avgRevenuePerStaff: avgEfficiency,
            potentialSavings: lowEfficiencyImpact * 0.5, // Estimate 50% of the gap could be recovered
          },
        });
      }
    }

    return opportunities;
  }

  /**
   * Generates insights based on cost analysis
   */
  private generateCostInsights(costAnalysis: any, costType: string): string[] {
    const insights: string[] = [];

    // Overall cost insights
    insights.push(
      `Overall cost-to-revenue ratio: ${costAnalysis.summary.costToRevenueRatio.toFixed(
        2
      )}%`
    );

    if (costAnalysis.summary.discountRate > 15) {
      insights.push(
        `High overall discount rate (${costAnalysis.summary.discountRate.toFixed(
          2
        )}%) may be reducing profitability`
      );
    }

    // Add type-specific insights
    if (costType === "all" || costType === "inventory") {
      const inventory = costAnalysis.details.inventory;

      // Find highest cost category
      const highestCostCategory: [string, any] = Object.entries(
        inventory.categoryCosts
      ).sort(
        (a: [string, any], b: [string, any]) => b[1].totalCost - a[1].totalCost
      )[0];

      if (highestCostCategory) {
        insights.push(
          `${
            highestCostCategory[0]
          } represents your highest inventory cost category (${highestCostCategory[1].totalCost.toFixed(
            2
          )})`
        );
      }

      // High cost products insight
      if (
        inventory.highCostProducts.length > 0 &&
        inventory.highCostProducts[0].costToRevenueRatio > 70
      ) {
        insights.push(
          `${
            inventory.highCostProducts[0].name
          } has a high cost-to-revenue ratio of ${inventory.highCostProducts[0].costToRevenueRatio.toFixed(
            2
          )}%`
        );
      }
    }

    if (costType === "all" || costType === "discount") {
      const discounts = costAnalysis.details.discounts;

      // Loyalty discount insight
      if (discounts.loyaltyDiscountRate > 0) {
        insights.push(
          `Loyalty discounts represent ${discounts.loyaltyDiscountRate.toFixed(
            2
          )}% of your revenue`
        );
      }

      // Day of week insight
      const discountsByDay = Object.entries(discounts.discountsByDay)
        .map(([day, metrics]: [string, any]) => ({
          day: parseInt(day),
          metrics,
        }))
        .sort((a, b) => b.metrics.average - a.metrics.average);

      if (discountsByDay.length > 0 && discountsByDay[0].metrics.average > 0) {
        const dayNames = [
          "Sunday",
          "Monday",
          "Tuesday",
          "Wednesday",
          "Thursday",
          "Friday",
          "Saturday",
        ];
        insights.push(
          `${
            dayNames[discountsByDay[0].day]
          } has the highest average discount per transaction ($${discountsByDay[0].metrics.average.toFixed(
            2
          )})`
        );
      }
    }

    if (costType === "all" || costType === "operations") {
      const operations = costAnalysis.details.operations;

      // Staff efficiency insight
      insights.push(
        `Average revenue per staff member: $${operations.aggregateMetrics.avgRevenuePerStaff.toFixed(
          2
        )}`
      );

      // Transaction value insight
      insights.push(
        `Average transaction value: $${operations.aggregateMetrics.avgTransactionValue.toFixed(
          2
        )}`
      );
    }

    return insights;
  }

  /**
   * Generates recommendations based on cost analysis and identified opportunities
   */
  private generateCostRecommendations(
    costAnalysis: any,
    opportunities: any[]
  ): string[] {
    const recommendations: string[] = [];

    // Generate recommendations based on identified opportunities
    for (const opportunity of opportunities) {
      switch (opportunity.type) {
        case "inventory":
          if (opportunity.subtype === "high_cost_categories") {
            recommendations.push(
              `Review pricing or suppliers for ${opportunity.details[0].name} to reduce cost-to-revenue ratio`
            );
          } else if (opportunity.subtype === "high_cost_products") {
            const productNames = opportunity.details
              .slice(0, 3)
              .map((product: any) => product.name)
              .join(", ");
            recommendations.push(
              `Evaluate alternative suppliers or consider discontinuing high-cost products: ${productNames}`
            );
          }
          break;

        case "discount":
          if (opportunity.subtype === "high_overall_discount") {
            recommendations.push(
              `Revise overall discount strategy to reduce discount rate from ${opportunity.details.currentRate.toFixed(
                2
              )}% to industry standard (15-18%)`
            );
          } else if (opportunity.subtype === "high_discount_categories") {
            const categoryNames = opportunity.details
              .slice(0, 2)
              .map((category: any) => category.name)
              .join(", ");
            recommendations.push(
              `Implement more targeted promotions for high-discount categories: ${categoryNames}`
            );
          } else if (opportunity.subtype === "high_discount_days") {
            recommendations.push(
              `Revise promotion schedule to optimize discounts on ${opportunity.details[0].day}s`
            );
          }
          break;

        case "operations":
          if (opportunity.subtype === "staff_efficiency") {
            recommendations.push(
              "Optimize staffing levels based on predicted sales volume to improve revenue per staff member"
            );
            recommendations.push(
              "Consider staff training to improve transaction efficiency and average transaction value"
            );
          }
          break;
      }
    }

    // Add general recommendations if we don't have many specific ones
    if (recommendations.length < 3) {
      if (costAnalysis.summary.costToRevenueRatio > 50) {
        recommendations.push(
          "Conduct a comprehensive review of your inventory purchasing to identify cost reduction opportunities"
        );
      }

      if (costAnalysis.summary.discountRate > 20) {
        recommendations.push(
          "Implement a data-driven discount strategy that targets specific customer segments instead of broad discounts"
        );
      }
    }

    return recommendations;
  }
}

/**
 * Tool for generating financial KPI dashboards and metrics
 */
export class FinancialDashboardTool extends Tool {
  name = "generate_financial_dashboard";
  description =
    "Generate a comprehensive financial KPI dashboard with key metrics. Input should be a JSON string with locationId, startDate, endDate, and optionally dashboardType (overview, profitability, growth, efficiency).";

  async _call(input: string): Promise<string> {
    try {
      const {
        locationId,
        startDate,
        endDate,
        dashboardType = "overview",
      } = JSON.parse(input);

      const result = await this.generateFinancialDashboard(
        locationId,
        new Date(startDate),
        new Date(endDate),
        dashboardType
      );

      return JSON.stringify(result);
    } catch (error) {
      logger.error("Error generating financial dashboard:", error);
      return `Error generating financial dashboard: ${
        error instanceof Error ? error.message : String(error)
      }`;
    }
  }

  /**
   * Generates a financial KPI dashboard with key metrics
   */
  private async generateFinancialDashboard(
    locationId: number,
    startDate: Date,
    endDate: Date,
    dashboardType: "overview" | "profitability" | "growth" | "efficiency"
  ) {
    try {
      // Query to get all financial data for the period
      const data = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .orderBy("order_date", "asc");

      // Calculate date ranges for period comparisons
      const periodLengthMs = endDate.getTime() - startDate.getTime();
      const previousPeriodEndDate = new Date(startDate);
      previousPeriodEndDate.setTime(previousPeriodEndDate.getTime() - 1); // One day before current period

      const previousPeriodStartDate = new Date(previousPeriodEndDate);
      previousPeriodStartDate.setTime(
        previousPeriodStartDate.getTime() - periodLengthMs
      );

      // Query to get data for the previous period
      const previousPeriodData = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", previousPeriodStartDate)
        .where("order_date", "<=", previousPeriodEndDate)
        .orderBy("order_date", "asc");

      // Process data based on dashboard type
      let dashboardData;
      let insights: string[] = [];
      let recommendations: string[] = [];

      switch (dashboardType) {
        case "overview":
          dashboardData = this.generateOverviewDashboard(
            data,
            previousPeriodData
          );
          break;
        case "profitability":
          dashboardData = this.generateProfitabilityDashboard(
            data,
            previousPeriodData
          );
          break;
        case "growth":
          dashboardData = this.generateGrowthDashboard(
            data,
            previousPeriodData
          );
          break;
        case "efficiency":
          dashboardData = this.generateEfficiencyDashboard(
            data,
            previousPeriodData
          );
          break;
        default:
          dashboardData = this.generateOverviewDashboard(
            data,
            previousPeriodData
          );
      }

      // Generate insights and recommendations for the dashboard
      const analysisResults = await this.analyzeDashboard(
        dashboardData,
        dashboardType
      );
      insights = analysisResults.insights;
      recommendations = analysisResults.recommendations;

      return {
        type: "financial_dashboard",
        dashboardType,
        data: dashboardData,
        period: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
          previousStart: previousPeriodStartDate.toISOString(),
          previousEnd: previousPeriodEndDate.toISOString(),
        },
        insights,
        recommendations,
      };
    } catch (error) {
      logger.error("Error generating financial dashboard:", error);
      throw error;
    }
  }

  /**
   * Generates an overview dashboard with high-level financial metrics
   */
  private generateOverviewDashboard(
    currentData: PosData[],
    previousData: PosData[]
  ) {
    // Calculate current period metrics
    const totalRevenue = currentData.reduce(
      (sum, record) => sum + (record.gross_sales || 0),
      0
    );
    const totalNetRevenue = currentData.reduce(
      (sum, record) => sum + (record.net_sales || 0),
      0
    );
    const totalCost = currentData.reduce(
      (sum, record) => sum + (record.inventory_cost || 0),
      0
    );
    const totalProfit = currentData.reduce(
      (sum, record) => sum + (record.inventory_profit || 0),
      0
    );
    const totalTransactions = currentData.length;

    // Calculate previous period metrics
    const prevTotalRevenue = previousData.reduce(
      (sum, record) => sum + (record.gross_sales || 0),
      0
    );
    const prevTotalNetRevenue = previousData.reduce(
      (sum, record) => sum + (record.net_sales || 0),
      0
    );
    const prevTotalCost = previousData.reduce(
      (sum, record) => sum + (record.inventory_cost || 0),
      0
    );
    const prevTotalProfit = previousData.reduce(
      (sum, record) => sum + (record.inventory_profit || 0),
      0
    );
    const prevTotalTransactions = previousData.length;

    // Calculate percentage changes
    const revenueChange = this.calculatePercentageChange(
      totalRevenue,
      prevTotalRevenue
    );
    const netRevenueChange = this.calculatePercentageChange(
      totalNetRevenue,
      prevTotalNetRevenue
    );
    const costChange = this.calculatePercentageChange(totalCost, prevTotalCost);
    const profitChange = this.calculatePercentageChange(
      totalProfit,
      prevTotalProfit
    );
    const transactionChange = this.calculatePercentageChange(
      totalTransactions,
      prevTotalTransactions
    );

    // Calculate key metrics
    const grossMargin =
      totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;
    const netMargin =
      totalNetRevenue > 0 ? (totalProfit / totalNetRevenue) * 100 : 0;
    const costToRevenueRatio =
      totalRevenue > 0 ? (totalCost / totalRevenue) * 100 : 0;
    const averageTransactionValue =
      totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

    // Previous period metrics
    const prevGrossMargin =
      prevTotalRevenue > 0 ? (prevTotalProfit / prevTotalRevenue) * 100 : 0;
    const prevNetMargin =
      prevTotalNetRevenue > 0
        ? (prevTotalProfit / prevTotalNetRevenue) * 100
        : 0;
    const prevCostToRevenueRatio =
      prevTotalRevenue > 0 ? (prevTotalCost / prevTotalRevenue) * 100 : 0;
    const prevAverageTransactionValue =
      prevTotalTransactions > 0 ? prevTotalRevenue / prevTotalTransactions : 0;

    // Calculate percentage changes for derived metrics
    const grossMarginChange = this.calculatePercentageChange(
      grossMargin,
      prevGrossMargin
    );
    const netMarginChange = this.calculatePercentageChange(
      netMargin,
      prevNetMargin
    );
    const costToRevenueRatioChange = this.calculatePercentageChange(
      costToRevenueRatio,
      prevCostToRevenueRatio
    );
    const avgTransactionValueChange = this.calculatePercentageChange(
      averageTransactionValue,
      prevAverageTransactionValue
    );

    // Get top performing categories
    const categoryPerformance = this.getCategoryPerformance(currentData);
    const topCategories = Object.entries(categoryPerformance)
      .sort((a, b) => b[1].revenue - a[1].revenue)
      .slice(0, 5)
      .map(([name, metrics]) => ({
        name,
        revenue: metrics.revenue,
        profit: metrics.profit,
        margin: metrics.margin,
        transactions: metrics.transactions,
      }));

    return {
      summary: {
        totalRevenue,
        totalNetRevenue,
        totalCost,
        totalProfit,
        totalTransactions,
      },
      keyMetrics: {
        grossMargin,
        netMargin,
        costToRevenueRatio,
        averageTransactionValue,
      },
      changes: {
        revenueChange,
        netRevenueChange,
        costChange,
        profitChange,
        transactionChange,
        grossMarginChange,
        netMarginChange,
        costToRevenueRatioChange,
        avgTransactionValueChange,
      },
      topCategories,
    };
  }

  /**
   * Generates a profitability-focused dashboard
   */
  private generateProfitabilityDashboard(
    currentData: PosData[],
    previousData: PosData[]
  ) {
    // Calculate overall profitability metrics
    const totalRevenue = currentData.reduce(
      (sum, record) => sum + (record.gross_sales || 0),
      0
    );
    const totalNetRevenue = currentData.reduce(
      (sum, record) => sum + (record.net_sales || 0),
      0
    );
    const totalCost = currentData.reduce(
      (sum, record) => sum + (record.inventory_cost || 0),
      0
    );
    const totalProfit = currentData.reduce(
      (sum, record) => sum + (record.inventory_profit || 0),
      0
    );
    const totalDiscounts = currentData.reduce(
      (sum, record) => sum + (record.discounted_amount || 0),
      0
    );

    // Calculate key profitability ratios
    const grossMargin =
      totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;
    const netMargin =
      totalNetRevenue > 0 ? (totalProfit / totalNetRevenue) * 100 : 0;
    const discountRate =
      totalRevenue > 0 ? (totalDiscounts / totalRevenue) * 100 : 0;

    // Previous period metrics for comparison
    const prevTotalRevenue = previousData.reduce(
      (sum, record) => sum + (record.gross_sales || 0),
      0
    );
    const prevTotalNetRevenue = previousData.reduce(
      (sum, record) => sum + (record.net_sales || 0),
      0
    );
    const prevTotalCost = previousData.reduce(
      (sum, record) => sum + (record.inventory_cost || 0),
      0
    );
    const prevTotalProfit = previousData.reduce(
      (sum, record) => sum + (record.inventory_profit || 0),
      0
    );
    const prevTotalDiscounts = previousData.reduce(
      (sum, record) => sum + (record.discounted_amount || 0),
      0
    );

    // Previous period ratios
    const prevGrossMargin =
      prevTotalRevenue > 0 ? (prevTotalProfit / prevTotalRevenue) * 100 : 0;
    const prevNetMargin =
      prevTotalNetRevenue > 0
        ? (prevTotalProfit / prevTotalNetRevenue) * 100
        : 0;
    const prevDiscountRate =
      prevTotalRevenue > 0 ? (prevTotalDiscounts / prevTotalRevenue) * 100 : 0;

    // Calculate percentage changes
    const revenueChange = this.calculatePercentageChange(
      totalRevenue,
      prevTotalRevenue
    );
    const profitChange = this.calculatePercentageChange(
      totalProfit,
      prevTotalProfit
    );
    const grossMarginChange = this.calculatePercentageChange(
      grossMargin,
      prevGrossMargin
    );
    const netMarginChange = this.calculatePercentageChange(
      netMargin,
      prevNetMargin
    );
    const discountRateChange = this.calculatePercentageChange(
      discountRate,
      prevDiscountRate
    );

    // Calculate profitability by category
    const categoryProfitability = this.getCategoryPerformance(currentData);

    // Sort by margin (higher to lower)
    const categoriesByMargin = Object.entries(categoryProfitability)
      .filter(([_, metrics]) => metrics.revenue > 0) // Filter out zero revenue categories
      .sort((a, b) => b[1].margin - a[1].margin)
      .slice(0, 5) // Top 5 by margin
      .map(([name, metrics]) => ({
        name,
        revenue: metrics.revenue,
        profit: metrics.profit,
        margin: metrics.margin,
        transactions: metrics.transactions,
      }));

    // Sort by total profit (higher to lower)
    const categoriesByProfit = Object.entries(categoryProfitability)
      .sort((a, b) => b[1].profit - a[1].profit)
      .slice(0, 5) // Top 5 by profit
      .map(([name, metrics]) => ({
        name,
        revenue: metrics.revenue,
        profit: metrics.profit,
        margin: metrics.margin,
        transactions: metrics.transactions,
      }));

    // Find low margin categories
    const lowMarginCategories = Object.entries(categoryProfitability)
      .filter(([_, metrics]) => metrics.revenue > totalRevenue * 0.05) // Consider only categories with significant revenue
      .sort((a, b) => a[1].margin - b[1].margin)
      .slice(0, 3) // Bottom 3 by margin
      .map(([name, metrics]) => ({
        name,
        revenue: metrics.revenue,
        profit: metrics.profit,
        margin: metrics.margin,
        transactions: metrics.transactions,
      }));

    return {
      summary: {
        totalRevenue,
        totalNetRevenue,
        totalCost,
        totalProfit,
        totalDiscounts,
        grossMargin,
        netMargin,
        discountRate,
      },
      changes: {
        revenueChange,
        profitChange,
        grossMarginChange,
        netMarginChange,
        discountRateChange,
      },
      profitabilityByCategory: {
        topByMargin: categoriesByMargin,
        topByProfit: categoriesByProfit,
        lowMargin: lowMarginCategories,
      },
    };
  }

  /**
   * Generates a growth-focused dashboard
   */
  private generateGrowthDashboard(
    currentData: PosData[],
    previousData: PosData[]
  ) {
    // Calculate growth metrics
    const totalRevenue = currentData.reduce(
      (sum, record) => sum + (record.gross_sales || 0),
      0
    );
    const totalProfit = currentData.reduce(
      (sum, record) => sum + (record.inventory_profit || 0),
      0
    );
    const totalTransactions = currentData.length;
    const uniqueCustomers = new Set(
      currentData.map((record) => record.customer_name)
    ).size;

    // Previous period metrics
    const prevTotalRevenue = previousData.reduce(
      (sum, record) => sum + (record.gross_sales || 0),
      0
    );
    const prevTotalProfit = previousData.reduce(
      (sum, record) => sum + (record.inventory_profit || 0),
      0
    );
    const prevTotalTransactions = previousData.length;
    const prevUniqueCustomers = new Set(
      previousData.map((record) => record.customer_name)
    ).size;

    // Calculate growth rates
    const revenueGrowth = this.calculatePercentageChange(
      totalRevenue,
      prevTotalRevenue
    );
    const profitGrowth = this.calculatePercentageChange(
      totalProfit,
      prevTotalProfit
    );
    const transactionGrowth = this.calculatePercentageChange(
      totalTransactions,
      prevTotalTransactions
    );
    const customerGrowth = this.calculatePercentageChange(
      uniqueCustomers,
      prevUniqueCustomers
    );

    // Calculate average transaction value
    const avgTransactionValue =
      totalTransactions > 0 ? totalRevenue / totalTransactions : 0;
    const prevAvgTransactionValue =
      prevTotalTransactions > 0 ? prevTotalRevenue / prevTotalTransactions : 0;
    const avgTransactionGrowth = this.calculatePercentageChange(
      avgTransactionValue,
      prevAvgTransactionValue
    );

    // Average revenue per customer
    const revenuePerCustomer =
      uniqueCustomers > 0 ? totalRevenue / uniqueCustomers : 0;
    const prevRevenuePerCustomer =
      prevUniqueCustomers > 0 ? prevTotalRevenue / prevUniqueCustomers : 0;
    const revenuePerCustomerGrowth = this.calculatePercentageChange(
      revenuePerCustomer,
      prevRevenuePerCustomer
    );

    // Calculate growth by category
    const currentCategoryData = this.getCategoryPerformance(currentData);
    const previousCategoryData = this.getCategoryPerformance(previousData);

    // Calculate growth for each category
    const categoryGrowth: Record<string, any> = {};

    for (const [category, metrics] of Object.entries(currentCategoryData)) {
      const prevMetrics = previousCategoryData[category] || {
        revenue: 0,
        profit: 0,
        transactions: 0,
      };

      categoryGrowth[category] = {
        currentRevenue: metrics.revenue,
        previousRevenue: prevMetrics.revenue,
        revenueGrowth: this.calculatePercentageChange(
          metrics.revenue,
          prevMetrics.revenue
        ),
        currentProfit: metrics.profit,
        previousProfit: prevMetrics.profit,
        profitGrowth: this.calculatePercentageChange(
          metrics.profit,
          prevMetrics.profit
        ),
        currentTransactions: metrics.transactions,
        previousTransactions: prevMetrics.transactions,
        transactionGrowth: this.calculatePercentageChange(
          metrics.transactions,
          prevMetrics.transactions
        ),
      };
    }

    // Find fastest growing categories by revenue
    const fastestGrowing = Object.entries(categoryGrowth)
      .filter(
        ([_, metrics]) =>
          metrics.previousRevenue > 0 &&
          metrics.currentRevenue > totalRevenue * 0.01
      ) // Filter for meaningful growth
      .sort((a, b) => b[1].revenueGrowth - a[1].revenueGrowth)
      .slice(0, 5)
      .map(([name, metrics]) => ({
        name,
        revenueGrowth: metrics.revenueGrowth,
        currentRevenue: metrics.currentRevenue,
        previousRevenue: metrics.previousRevenue,
      }));

    // Find categories with declining revenue
    const declining = Object.entries(categoryGrowth)
      .filter(
        ([_, metrics]) =>
          metrics.previousRevenue > prevTotalRevenue * 0.05 && // Only significant categories
          metrics.revenueGrowth < 0
      )
      .sort((a, b) => a[1].revenueGrowth - b[1].revenueGrowth)
      .slice(0, 5)
      .map(([name, metrics]) => ({
        name,
        revenueGrowth: metrics.revenueGrowth,
        currentRevenue: metrics.currentRevenue,
        previousRevenue: metrics.previousRevenue,
      }));

    // New categories (those not present in previous period)
    const newCategories = Object.entries(currentCategoryData)
      .filter(([category, _]) => !previousCategoryData[category])
      .map(([name, metrics]) => ({
        name,
        revenue: metrics.revenue,
        profit: metrics.profit,
        transactions: metrics.transactions,
      }));

    return {
      summary: {
        totalRevenue,
        totalProfit,
        totalTransactions,
        uniqueCustomers,
        revenueGrowth,
        profitGrowth,
        transactionGrowth,
        customerGrowth,
      },
      customerMetrics: {
        avgTransactionValue,
        avgTransactionGrowth,
        revenuePerCustomer,
        revenuePerCustomerGrowth,
      },
      categoryGrowth: {
        fastestGrowing,
        declining,
        newCategories,
      },
    };
  }

  /**
   * Generates an efficiency-focused dashboard
   */
  private generateEfficiencyDashboard(
    currentData: PosData[],
    previousData: PosData[]
  ) {
    // Calculate efficiency metrics
    const totalRevenue = currentData.reduce(
      (sum, record) => sum + (record.gross_sales || 0),
      0
    );
    const totalCost = currentData.reduce(
      (sum, record) => sum + (record.inventory_cost || 0),
      0
    );
    const totalProfit = currentData.reduce(
      (sum, record) => sum + (record.inventory_profit || 0),
      0
    );
    const totalTransactions = currentData.length;

    // Calculate efficiency ratios
    const profitPerTransaction =
      totalTransactions > 0 ? totalProfit / totalTransactions : 0;
    const costPerTransaction =
      totalTransactions > 0 ? totalCost / totalTransactions : 0;
    const revenuePerTransaction =
      totalTransactions > 0 ? totalRevenue / totalTransactions : 0;

    // Previous period metrics
    const prevTotalRevenue = previousData.reduce(
      (sum, record) => sum + (record.gross_sales || 0),
      0
    );
    const prevTotalCost = previousData.reduce(
      (sum, record) => sum + (record.inventory_cost || 0),
      0
    );
    const prevTotalProfit = previousData.reduce(
      (sum, record) => sum + (record.inventory_profit || 0),
      0
    );
    const prevTotalTransactions = previousData.length;

    // Previous period ratios
    const prevProfitPerTransaction =
      prevTotalTransactions > 0 ? prevTotalProfit / prevTotalTransactions : 0;
    const prevCostPerTransaction =
      prevTotalTransactions > 0 ? prevTotalCost / prevTotalTransactions : 0;
    const prevRevenuePerTransaction =
      prevTotalTransactions > 0 ? prevTotalRevenue / prevTotalTransactions : 0;

    // Calculate percentage changes
    const profitPerTransactionChange = this.calculatePercentageChange(
      profitPerTransaction,
      prevProfitPerTransaction
    );
    const costPerTransactionChange = this.calculatePercentageChange(
      costPerTransaction,
      prevCostPerTransaction
    );
    const revenuePerTransactionChange = this.calculatePercentageChange(
      revenuePerTransaction,
      prevRevenuePerTransaction
    );

    // Group by budtender to calculate efficiency
    const budtenderEfficiency = this.getBudtenderEfficiency(currentData);
    const prevBudtenderEfficiency = this.getBudtenderEfficiency(previousData);

    // Calculate efficiency changes for each budtender
    const budtenderComparison: Record<string, any> = {};

    for (const [budtender, metrics] of Object.entries(budtenderEfficiency)) {
      const prevMetrics = prevBudtenderEfficiency[budtender] || {
        transactions: 0,
        revenue: 0,
        profit: 0,
        revenuePerTransaction: 0,
      };

      budtenderComparison[budtender] = {
        currentTransactions: metrics.transactions,
        previousTransactions: prevMetrics.transactions,
        currentRevenue: metrics.revenue,
        previousRevenue: prevMetrics.revenue,
        currentProfit: metrics.profit,
        previousProfit: prevMetrics.profit,
        currentRevenuePerTransaction: metrics.revenuePerTransaction,
        previousRevenuePerTransaction: prevMetrics.revenuePerTransaction,
        revenuePerTransactionChange: this.calculatePercentageChange(
          metrics.revenuePerTransaction,
          prevMetrics.revenuePerTransaction
        ),
      };
    }

    // Sort budtenders by current revenue per transaction
    const budtendersByEfficiency = Object.entries(budtenderComparison)
      .filter(([_, metrics]) => metrics.currentTransactions > 10) // Only consider budtenders with sufficient transactions
      .sort(
        (a, b) =>
          b[1].currentRevenuePerTransaction - a[1].currentRevenuePerTransaction
      )
      .map(([name, metrics]) => ({
        name,
        transactions: metrics.currentTransactions,
        revenuePerTransaction: metrics.currentRevenuePerTransaction,
        revenuePerTransactionChange: metrics.revenuePerTransactionChange,
      }));

    // Calculate efficiency by day of week
    const dayOfWeekEfficiency = this.getDayOfWeekEfficiency(currentData);
    const prevDayOfWeekEfficiency = this.getDayOfWeekEfficiency(previousData);

    // Calculate efficiency changes for each day of week
    const dayOfWeekComparison: Record<string, any> = {};

    for (const [day, metrics] of Object.entries(dayOfWeekEfficiency)) {
      const prevMetrics = prevDayOfWeekEfficiency[day] || {
        transactions: 0,
        revenue: 0,
        revenuePerTransaction: 0,
      };

      dayOfWeekComparison[day] = {
        currentTransactions: metrics.transactions,
        previousTransactions: prevMetrics.transactions,
        currentRevenue: metrics.revenue,
        previousRevenue: prevMetrics.revenue,
        currentRevenuePerTransaction: metrics.revenuePerTransaction,
        previousRevenuePerTransaction: prevMetrics.revenuePerTransaction,
        revenuePerTransactionChange: this.calculatePercentageChange(
          metrics.revenuePerTransaction,
          prevMetrics.revenuePerTransaction
        ),
      };
    }

    return {
      summary: {
        totalRevenue,
        totalCost,
        totalProfit,
        totalTransactions,
        profitPerTransaction,
        costPerTransaction,
        revenuePerTransaction,
      },
      changes: {
        profitPerTransactionChange,
        costPerTransactionChange,
        revenuePerTransactionChange,
      },
      staffEfficiency: {
        budtendersByEfficiency,
      },
      timeEfficiency: {
        dayOfWeekComparison,
      },
    };
  }

  /**
   * Uses LLM to generate insights and recommendations based on the dashboard data
   */
  private async analyzeDashboard(
    dashboardData: any,
    dashboardType: string
  ): Promise<{ insights: string[]; recommendations: string[] }> {
    try {
      // Prepare a prompt for the LLM based on the dashboard type
      let prompt = `You are a financial analyst for a cannabis dispensary. Analyze this financial dashboard data and provide insights and recommendations.\n\n`;

      // Add dashboard-specific context
      switch (dashboardType) {
        case "overview":
          prompt += `This is an OVERVIEW dashboard showing high-level financial metrics.\n`;
          break;
        case "profitability":
          prompt += `This is a PROFITABILITY dashboard focusing on margins and profit metrics.\n`;
          break;
        case "growth":
          prompt += `This is a GROWTH dashboard focusing on revenue growth and customer acquisition.\n`;
          break;
        case "efficiency":
          prompt += `This is an EFFICIENCY dashboard focusing on operational efficiency metrics.\n`;
          break;
      }

      // Add the dashboard data
      prompt += `DASHBOARD DATA: ${JSON.stringify(dashboardData, null, 2)}\n\n`;

      // Specify what we want
      prompt += `Please provide:
1. 3-5 key insights based on the data (focus on significant trends, important metrics, and noteworthy performance indicators)
2. 3-5 actionable recommendations based on the insights

Return your response as a JSON object with two properties: "insights" (array of strings) and "recommendations" (array of strings).`;

      // Use OpenAI API to generate insights and recommendations
      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });

      const response = await openai.chat.completions.create({
        model: "gpt-4.1-mini",
        messages: [
          {
            role: "system",
            content:
              "You are a financial analyst specializing in cannabis retail operations.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.3,
      });

      // Parse the response
      const content = response.choices[0]?.message?.content || "";

      try {
        // Extract JSON from the response (handling potential markdown formatting)
        const jsonMatch =
          content.match(/```json\s*([\s\S]*?)\s*```/) ||
          content.match(/\{[\s\S]*\}/);
        const jsonContent = jsonMatch
          ? jsonMatch[0].replace(/```json|```/g, "")
          : content;

        const parsedResponse = JSON.parse(jsonContent);
        return {
          insights: Array.isArray(parsedResponse.insights)
            ? parsedResponse.insights
            : [],
          recommendations: Array.isArray(parsedResponse.recommendations)
            ? parsedResponse.recommendations
            : [],
        };
      } catch (parseError) {
        // Fallback to simple text parsing if JSON extraction fails
        logger.warn("Failed to parse LLM response as JSON:", parseError);

        const insights: string[] = [];
        const recommendations: string[] = [];

        // Try to extract insights and recommendations from text
        const insightMatch = content.match(
          /insights?:?\s*(.*?)(?=recommendations?:|$)/is
        );
        if (insightMatch && insightMatch[1]) {
          // Split by number patterns (e.g., "1.", "2.")
          const insightLines = insightMatch[1].split(/\s*\d+\.\s+/);
          insights.push(
            ...insightLines.filter((line) => line.trim().length > 0)
          );
        }

        const recsMatch = content.match(/recommendations?:?\s*(.*?)(?=$)/is);
        if (recsMatch && recsMatch[1]) {
          // Split by number patterns (e.g., "1.", "2.")
          const recLines = recsMatch[1].split(/\s*\d+\.\s+/);
          recommendations.push(
            ...recLines.filter((line) => line.trim().length > 0)
          );
        }

        return {
          insights:
            insights.length > 0
              ? insights
              : ["Unable to generate insights due to processing error"],
          recommendations:
            recommendations.length > 0
              ? recommendations
              : ["Unable to generate recommendations due to processing error"],
        };
      }
    } catch (error) {
      logger.error("Error analyzing dashboard with LLM:", error);
      return {
        insights: ["Unable to generate AI-powered insights at this time"],
        recommendations: ["Consider reviewing the dashboard data manually"],
      };
    }
  }

  /**
   * Calculates percentage change between current and previous values
   */
  private calculatePercentageChange(current: number, previous: number): number {
    if (previous === 0) {
      return current > 0 ? 100 : 0; // If previous is zero, return 100% increase if current is positive
    }

    return ((current - previous) / previous) * 100;
  }

  /**
   * Gets performance metrics grouped by category
   */
  private getCategoryPerformance(data: PosData[]): Record<string, any> {
    const categoryData: Record<string, any> = {};

    for (const record of data) {
      const category = record.master_category || "Uncategorized";

      if (!categoryData[category]) {
        categoryData[category] = {
          revenue: 0,
          profit: 0,
          transactions: 0,
          margin: 0,
        };
      }

      categoryData[category].revenue += record.gross_sales || 0;
      categoryData[category].profit += record.inventory_profit || 0;
      categoryData[category].transactions += 1;
    }

    // Calculate margins
    for (const category in categoryData) {
      const c = categoryData[category];
      c.margin = c.revenue > 0 ? (c.profit / c.revenue) * 100 : 0;
    }

    return categoryData;
  }

  /**
   * Gets efficiency metrics grouped by budtender
   */
  private getBudtenderEfficiency(data: PosData[]): Record<string, any> {
    const budtenderData: Record<string, any> = {};

    for (const record of data) {
      const budtender = record.budtender_name || "Unknown";

      if (!budtenderData[budtender]) {
        budtenderData[budtender] = {
          revenue: 0,
          profit: 0,
          transactions: 0,
          revenuePerTransaction: 0,
        };
      }

      budtenderData[budtender].revenue += record.gross_sales || 0;
      budtenderData[budtender].profit += record.inventory_profit || 0;
      budtenderData[budtender].transactions += 1;
    }

    // Calculate efficiency metrics
    for (const budtender in budtenderData) {
      const b = budtenderData[budtender];
      b.revenuePerTransaction =
        b.transactions > 0 ? b.revenue / b.transactions : 0;
      b.profitPerTransaction =
        b.transactions > 0 ? b.profit / b.transactions : 0;
    }

    return budtenderData;
  }

  /**
   * Gets efficiency metrics grouped by day of week
   */
  private getDayOfWeekEfficiency(data: PosData[]): Record<string, any> {
    const dayData: Record<string, any> = {
      Sunday: { revenue: 0, transactions: 0, revenuePerTransaction: 0 },
      Monday: { revenue: 0, transactions: 0, revenuePerTransaction: 0 },
      Tuesday: { revenue: 0, transactions: 0, revenuePerTransaction: 0 },
      Wednesday: { revenue: 0, transactions: 0, revenuePerTransaction: 0 },
      Thursday: { revenue: 0, transactions: 0, revenuePerTransaction: 0 },
      Friday: { revenue: 0, transactions: 0, revenuePerTransaction: 0 },
      Saturday: { revenue: 0, transactions: 0, revenuePerTransaction: 0 },
    };

    const dayNames = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];

    for (const record of data) {
      const date = new Date(record.order_date);
      const dayName = dayNames[date.getDay()];

      dayData[dayName].revenue += record.gross_sales || 0;
      dayData[dayName].transactions += 1;
    }

    // Calculate efficiency metrics
    for (const day in dayData) {
      const d = dayData[day];
      d.revenuePerTransaction =
        d.transactions > 0 ? d.revenue / d.transactions : 0;
    }

    return dayData;
  }
}
