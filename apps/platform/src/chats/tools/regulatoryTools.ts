import { Tool as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@langchain/core/tools";

/**
 * Tool for providing state-specific cannabis regulatory information
 */
export class StateRegulationsTool extends LangChainTool {
  name = "state_regulations";
  description =
    "Get specific cannabis regulations and compliance information for a particular state";

  protected async _call(input: string): Promise<string> {
    try {
      const params = JSON.parse(input);
      const { state } = params;

      if (!state) {
        return JSON.stringify({
          error: "State parameter is required",
        });
      }

      // Map of state regulations - this would ideally come from a database
      const stateRegulations: Record<string, any> = {
        CA: {
          marketingRules:
            "No advertising within 1,000 feet of schools/playgrounds. No cartoon characters or youth-appealing imagery. All advertisements must include license number and state warning.",
          packagingRequirements:
            "Child-resistant, resealable, opaque for edibles. THC symbol required. Warning labels must be specific size.",
          purchaseLimits: "28.5g flower, 8g concentrate per day for adult-use.",
          potencyLimits:
            "10mg THC per serving, 100mg THC per package for edibles.",
          deliveryRules:
            "Permitted with license, vehicles must be unmarked, GPS tracked.",
        },
        CO: {
          marketingRules:
            "No advertising with medical claims or appealing to minors. No outdoor advertising except for dispensary location.",
          packagingRequirements:
            "Child-resistant, opaque for edibles. Universal THC symbol required.",
          purchaseLimits: "28g flower per day for adult-use.",
          potencyLimits:
            "10mg THC per serving for edibles, no limit on flower potency.",
          deliveryRules:
            "Permitted with additional endorsement, strict ID verification.",
        },
        IL: {
          marketingRules:
            "No marketing within 1,000 feet of schools, playgrounds, rec centers. No promotion of overconsumption.",
          packagingRequirements:
            "Child-resistant, warning labels, THC content displayed prominently.",
          purchaseLimits:
            "30g flower, 5g concentrate, 500mg THC in products for residents.",
          potencyLimits:
            "No specific limits on potency, but all products must be tested.",
          deliveryRules: "Delivery prohibited for recreational dispensaries.",
        },
        MI: {
          marketingRules:
            "Cannot target minors or use cartoons/characters. Cannot advertise free product or giveaways.",
          packagingRequirements:
            "Child-resistant, opaque containers, THC warning symbol, tracking information.",
          purchaseLimits: "2.5oz flower, 15g concentrate per day.",
          potencyLimits:
            "No specific potency limits but all products must be tested and labeled.",
          deliveryRules: "Permitted with proper licensing and manifests.",
        },
        NY: {
          marketingRules:
            "No advertising near schools or public transit. No misleading health claims. Must include health warnings.",
          packagingRequirements:
            "Child-resistant, tamper-evident, minimal branding. Warning labels required.",
          purchaseLimits: "3oz flower, 24g concentrate for adult-use.",
          potencyLimits:
            "Maximum THC: 10mg per serving, 100mg per package for edibles.",
          deliveryRules:
            "Permitted with proper licensing, no more than $20,000 in product per vehicle.",
        },
        NV: {
          marketingRules:
            "No advertising within 1,000 feet of schools or appealing to minors. Strict health warning requirements.",
          packagingRequirements:
            "Child-resistant, opaque for edibles. THC symbol and warnings required.",
          purchaseLimits: "1oz flower, 3.5g concentrate per day.",
          potencyLimits: "10mg THC per serving, 100mg per package for edibles.",
          deliveryRules:
            "Permitted for medical and retail, strict verification requirements.",
        },
        MD: {
          marketingRules:
            "No advertising on public transit or within 500 feet of schools. Age verification required on websites.",
          packagingRequirements:
            "Child-resistant, opaque containers. THC warning labels.",
          purchaseLimits: "1.5oz flower, 12g concentrate for adult-use.",
          potencyLimits: "No specific potency limits but testing required.",
          deliveryRules:
            "Permitted for medical patients only, adult-use delivery pending regulations.",
        },
        NJ: {
          marketingRules:
            "No advertising within 200 feet of schools. Restricted use of illustrations that appeal to minors.",
          packagingRequirements:
            "Child-resistant, tamper-evident packaging. THC warnings and symbols required.",
          purchaseLimits: "1oz flower per transaction.",
          potencyLimits: "No potency caps but thorough testing requirements.",
          deliveryRules:
            "Delivery services authorized but require separate license.",
        },
        OK: {
          marketingRules:
            "Limited restrictions beyond standard advertising regulations. Medical focus.",
          packagingRequirements:
            "Child-resistant, warning labels, batch testing information.",
          purchaseLimits:
            "3oz flower, 1oz concentrate, 72oz edibles for medical patients.",
          potencyLimits: "No specific potency limits.",
          deliveryRules:
            "Not specifically permitted under current regulations.",
        },
        MO: {
          marketingRules:
            "No advertising within 1,000 feet of schools/parks. No appealing to minors or misleading claims.",
          packagingRequirements:
            "Child-resistant, opaque packaging with required warnings.",
          purchaseLimits:
            "3oz flower per month for medical, 3oz possession limit for adult-use.",
          potencyLimits: "No specific potency limits but testing required.",
          deliveryRules:
            "Permitted for medical and recreational with proper licensing.",
        },
        MN: {
          marketingRules:
            "New adult-use regulations in development. Restrictions on health claims and youth appeal expected.",
          packagingRequirements:
            "Child-resistant, opaque packaging with warnings expected for recreational.",
          purchaseLimits:
            "Adult-use possession limit of 2oz flower, limits on purchases being determined.",
          potencyLimits:
            "Limits may be implemented as part of new regulations.",
          deliveryRules: "Being determined under new adult-use framework.",
        },
      };

      // Return the regulations for the requested state
      if (stateRegulations[state]) {
        return JSON.stringify({
          state,
          regulations: stateRegulations[state],
          disclaimer:
            "Regulations may change. Always verify with official state resources.",
        });
      } else {
        return JSON.stringify({
          state,
          error:
            "State regulations not found. Available states: " +
            Object.keys(stateRegulations).join(", "),
        });
      }
    } catch (error) {
      return JSON.stringify({
        error:
          "Error processing regulation request: " +
          (error instanceof Error ? error.message : String(error)),
      });
    }
  }
}
