import { tool } from "@langchain/core/tools";
import { z } from "zod";
import { SupabaseService } from "../../supabase/SupabaseService";
import { CompetitorService } from "../../competitors/CompetitorService";
import { logger } from "../../config/logger";

// Initialize services
const supabaseService = new SupabaseService({
  url: process.env.SUPABASE_URL || "",
  key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
  bucket: process.env.SUPABASE_BUCKET || "location-data",
  timeout: process.env.SUPABASE_TIMEOUT
    ? parseInt(process.env.SUPABASE_TIMEOUT)
    : 30000, // 30 seconds default
});

const competitorService = new CompetitorService();

export const RetailerSearchTool = tool(
  async ({ query, limit }: { query: string; limit?: number }) => {
    try {
      logger.info("🔥 RetailerSearchTool called with structured input", {
        query,
        limit,
        inputTypes: { query: typeof query, limit: typeof limit },
      });

      if (!query) {
        logger.error("Invalid or missing query", { query });
        return JSON.stringify({
          error: "Query is required",
          details: "query must be provided",
        });
      }

      const finalLimit = limit || 10;
      const retailers = await supabaseService.searchRetailers(
        query,
        finalLimit
      );

      logger.info("Retrieved retailers successfully", {
        query,
        limit: finalLimit,
        count: retailers.length,
      });

      return JSON.stringify({ retailers, count: retailers.length });
    } catch (error) {
      logger.error("RetailerSearchTool error", { error });
      return JSON.stringify({
        error: "Failed to search retailers",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "retailer_search",
    description:
      "Search for cannabis retailers by name, address, or other keywords. REQUIRES: query (string) - search keywords, limit (number, optional) - max results to return (default: 10)",
    schema: z.object({
      query: z.string().describe("Search keywords for finding retailers"),
      limit: z
        .number()
        .optional()
        .describe("Maximum number of results to return (default: 10)"),
    }),
  }
);

export const NearbyRetailersTool = tool(
  async ({
    latitude,
    longitude,
    radius,
    limit,
  }: {
    latitude: number;
    longitude: number;
    radius?: number;
    limit?: number;
  }) => {
    try {
      logger.info("🔥 NearbyRetailersTool called with structured input", {
        latitude,
        longitude,
        radius,
        limit,
        inputTypes: {
          latitude: typeof latitude,
          longitude: typeof longitude,
          radius: typeof radius,
          limit: typeof limit,
        },
      });

      if (!latitude || !longitude || isNaN(latitude) || isNaN(longitude)) {
        logger.error("Invalid or missing coordinates", { latitude, longitude });
        return JSON.stringify({
          error: "Valid latitude and longitude are required",
          details: "Both latitude and longitude must be valid numbers",
        });
      }

      const finalRadius = radius || 15;
      const finalLimit = limit || 10;

      const retailers = await supabaseService.searchNearbyRetailers(
        Number(latitude),
        Number(longitude),
        finalRadius,
        finalLimit
      );

      logger.info("Retrieved nearby retailers successfully", {
        latitude,
        longitude,
        radius: finalRadius,
        limit: finalLimit,
        count: Array.isArray(retailers) ? retailers.length : 0,
      });

      return JSON.stringify(retailers);
    } catch (error) {
      logger.error("NearbyRetailersTool error", { error });
      return JSON.stringify({
        error: "Failed to find nearby retailers",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "nearby_retailers",
    description:
      "Find cannabis retailers near a specific location by latitude and longitude. REQUIRES: latitude (number), longitude (number), radius (number, optional) - search radius in miles (default: 15), limit (number, optional) - max results (default: 10)",
    schema: z.object({
      latitude: z
        .number()
        .describe("Latitude coordinate for the search center"),
      longitude: z
        .number()
        .describe("Longitude coordinate for the search center"),
      radius: z
        .number()
        .optional()
        .describe("Search radius in miles (default: 15)"),
      limit: z
        .number()
        .optional()
        .describe("Maximum number of results to return (default: 10)"),
    }),
  }
);

export const RetailerProductsTool = tool(
  async ({ retailerId, limit }: { retailerId: string; limit?: number }) => {
    try {
      logger.info("🔥 RetailerProductsTool called with structured input", {
        retailerId,
        limit,
        inputTypes: { retailerId: typeof retailerId, limit: typeof limit },
      });

      if (!retailerId) {
        logger.error("Invalid or missing retailerId", { retailerId });
        return JSON.stringify({
          error: "Retailer ID is required",
          details: "retailerId must be provided",
        });
      }

      const finalLimit = limit || 100;
      const result = await supabaseService.getRetailerProducts(
        retailerId,
        finalLimit
      );

      logger.info("Retrieved retailer products successfully", {
        retailerId,
        limit: finalLimit,
        resultCount: Array.isArray(result.products)
          ? result.products.length
          : 0,
      });

      return JSON.stringify(result);
    } catch (error) {
      logger.error("RetailerProductsTool error", { error });
      return JSON.stringify({
        error: "Failed to get retailer products",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "retailer_products",
    description:
      "Get products for a specific retailer by their retailer_id. REQUIRES: retailerId (string) - the ID of the retailer to get products for, limit (number, optional) - max number of products to return (default: 100)",
    schema: z.object({
      retailerId: z
        .string()
        .describe("The ID of the retailer to get products for"),
      limit: z
        .number()
        .optional()
        .describe("Maximum number of products to return (default: 100)"),
    }),
  }
);

export const RetailerDetailsTool = tool(
  async ({ retailerId }: { retailerId: string }) => {
    try {
      logger.info("🔥 RetailerDetailsTool called with structured input", {
        retailerId,
        inputType: typeof retailerId,
      });

      if (!retailerId) {
        logger.error("Invalid or missing retailerId", { retailerId });
        return JSON.stringify({
          error: "Retailer ID is required",
          details: "retailerId must be provided",
        });
      }

      const retailer = await supabaseService.getRetailerById(retailerId);

      logger.info("Retrieved retailer details successfully", {
        retailerId,
        hasData: !!retailer,
      });

      return JSON.stringify(retailer);
    } catch (error) {
      logger.error("RetailerDetailsTool error", { error });
      return JSON.stringify({
        error: "Failed to get retailer details",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "retailer_details",
    description:
      "Get detailed information about a specific retailer by their retailer_id. REQUIRES: retailerId (string) - the ID of the retailer to get details for",
    schema: z.object({
      retailerId: z
        .string()
        .describe("The ID of the retailer to get details for"),
    }),
  }
);

export const MarketAnalysisTool = tool(
  async ({
    competitors,
    userRetailerId,
  }: {
    competitors: Array<{ place_id: string; name: string }>;
    userRetailerId?: string;
  }) => {
    try {
      logger.info("🔥 MarketAnalysisTool called with structured input", {
        competitors,
        userRetailerId,
        inputTypes: {
          competitors: typeof competitors,
          userRetailerId: typeof userRetailerId,
        },
        competitorsCount: Array.isArray(competitors) ? competitors.length : 0,
      });

      if (!competitors || !Array.isArray(competitors)) {
        logger.error("Invalid or missing competitors array", { competitors });
        return JSON.stringify({
          error: "Competitors array is required",
          details: "competitors must be an array of competitor objects",
        });
      }

      // Use the comprehensive performMarketAnalysis method
      // Convert the input format to CompetitorResult format
      const competitorResults = competitors.map((comp) => ({
        place_id: comp.place_id,
        name: comp.name,
        address: (comp as any).address || "",
        location: {
          lat: 0,
          lng: 0,
        },
        distance: (comp as any).distance || 0,
      }));

      const analysis = await competitorService.performMarketAnalysis(
        competitorResults,
        userRetailerId
      );

      logger.info("Market analysis completed successfully", {
        competitorsCount: competitors.length,
        hasAnalysis: !!analysis,
        categoriesFound: analysis.categoriesFound,
        totalProductsAnalyzed: analysis.totalProductsAnalyzed,
      });

      return JSON.stringify(analysis);
    } catch (error) {
      logger.error("MarketAnalysisTool error", { error });
      return JSON.stringify({
        error: "Failed to perform market analysis",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "market_analysis",
    description:
      "Perform comprehensive market analysis across competitors with weight-aware pricing, product counts, competitor breakdowns, gap analysis, and strategic recommendations across all categories. REQUIRES: competitors (array) - array of competitor objects. OPTIONAL: userRetailerId (string) - user's retailer ID for comparison.",
    schema: z.object({
      competitors: z
        .array(
          z.object({
            name: z.string().describe("Competitor name"),
            place_id: z.string().describe("Competitor place ID"),
            address: z.string().optional().describe("Competitor address"),
            distance: z.number().optional().describe("Distance in miles"),
          })
        )
        .describe("Array of competitor objects to analyze"),
      userRetailerId: z
        .string()
        .optional()
        .describe("The user's retailer ID for comparison (optional)"),
    }),
  }
);

export const CompetitorsTool = tool(
  async ({ locationId }: { locationId: number }) => {
    try {
      logger.info("🔥 CompetitorsTool called with structured input", {
        locationId,
        inputType: typeof locationId,
      });

      if (!locationId || isNaN(Number(locationId))) {
        logger.error("Invalid or missing locationId", { locationId });
        return JSON.stringify({
          error: "Invalid locationId provided",
          details: "locationId must be a valid number",
        });
      }

      logger.info("Getting competitors for location", { locationId });

      const competitorService = new CompetitorService();
      const competitors = await competitorService.getCompetitors(
        Number(locationId)
      );

      logger.info("Retrieved competitors successfully", {
        locationId,
        count: competitors.length,
      });

      return JSON.stringify({
        count: competitors.length,
        locationId,
        competitors: competitors.map((c) => ({
          name: c.name,
          retailer_id: c.place_id,
          place_id: c.place_id,
          address: c.address,
          distance: c.distance,
        })),
      });
    } catch (error) {
      logger.error("CompetitorsTool error", { error });
      return JSON.stringify({
        error: "Failed to get competitors",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "get_competitors",
    description:
      "Get all competitors for a specific location. REQUIRES: locationId (number) - the ID of the location to get competitors for.",
    schema: z.object({
      locationId: z
        .number()
        .describe("The ID of the location to get competitors for"),
    }),
  }
);

export const BrandSearchTool = tool(
  async ({ brandName, limit }: { brandName: string; limit?: number }) => {
    try {
      logger.info("🔥 BrandSearchTool called with structured input", {
        brandName,
        limit,
        inputTypes: { brandName: typeof brandName, limit: typeof limit },
      });

      if (!brandName) {
        logger.error("Invalid or missing brandName", { brandName });
        return JSON.stringify({
          error: "Brand name is required",
          details: "brandName must be provided",
        });
      }

      const finalLimit = limit || 10;

      // Use supabaseClient
      const { data, error } = await supabaseService.supabaseClient
        .from("brands")
        .select("*")
        .ilike("brand_name", `%${brandName}%`)
        .limit(finalLimit);

      if (error) throw error;

      logger.info("Brand search completed successfully", {
        brandName,
        limit: finalLimit,
        count: (data || []).length,
      });

      return JSON.stringify({ brands: data || [], count: (data || []).length });
    } catch (error) {
      logger.error("BrandSearchTool error", { error });
      return JSON.stringify({
        error: "Failed to search brands",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "brand_search",
    description:
      "Search for cannabis brands by name or other criteria. REQUIRES: brandName (string) - name of the brand to search for, limit (number, optional) - max results to return (default: 10)",
    schema: z.object({
      brandName: z.string().describe("Name of the brand to search for"),
      limit: z
        .number()
        .optional()
        .describe("Maximum number of results to return (default: 10)"),
    }),
  }
);

export const ProductSearchTool = tool(
  async ({
    query,
    category,
    brandName,
    limit,
  }: {
    query: string;
    category?: string;
    brandName?: string;
    limit?: number;
  }) => {
    try {
      logger.info("🔥 ProductSearchTool called with structured input", {
        query,
        category,
        brandName,
        limit,
        inputTypes: {
          query: typeof query,
          category: typeof category,
          brandName: typeof brandName,
          limit: typeof limit,
        },
      });

      if (!query) {
        logger.error("Invalid or missing query", { query });
        return JSON.stringify({
          error: "Query is required",
          details: "query must be provided for product search",
        });
      }

      const finalLimit = limit || 20;

      // Create a query builder with supabaseClient
      let queryBuilder = supabaseService.supabaseClient
        .from("products")
        .select("*")
        .or(`product_name.ilike.%${query}%, raw_product_name.ilike.%${query}%`);

      // Add filters if provided
      if (category) {
        queryBuilder = queryBuilder.eq("category", category);
      }

      if (brandName) {
        queryBuilder = queryBuilder.eq("brand_name", brandName);
      }

      // Execute the query with limit
      const { data, error } = await queryBuilder.limit(finalLimit);

      if (error) throw error;

      logger.info("Product search completed successfully", {
        query,
        category,
        brandName,
        limit: finalLimit,
        count: (data || []).length,
      });

      return JSON.stringify({
        products: data || [],
        count: (data || []).length,
      });
    } catch (error) {
      logger.error("ProductSearchTool error", { error });
      return JSON.stringify({
        error: "Failed to search products",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "product_search",
    description:
      "Search for cannabis products by name, category, or brand. REQUIRES: query (string) - search keywords, category (string, optional) - product category filter, brandName (string, optional) - brand name filter, limit (number, optional) - max results (default: 20)",
    schema: z.object({
      query: z.string().describe("Search keywords for finding products"),
      category: z.string().optional().describe("Product category to filter by"),
      brandName: z.string().optional().describe("Brand name to filter by"),
      limit: z
        .number()
        .optional()
        .describe("Maximum number of results to return (default: 20)"),
    }),
  }
);

export const ProductAnalysisTool = tool(
  async ({
    category,
    brandName,
  }: {
    category?: string;
    brandName?: string;
  }) => {
    try {
      logger.info("🔥 ProductAnalysisTool called with structured input", {
        category,
        brandName,
        inputTypes: {
          category: typeof category,
          brandName: typeof brandName,
        },
      });

      // Build query for average prices by category with supabaseClient
      let queryBuilder = supabaseService.supabaseClient
        .from("products")
        .select("category, latest_price");

      if (category) {
        queryBuilder = queryBuilder.eq("category", category);
      }

      if (brandName) {
        queryBuilder = queryBuilder.eq("brand_name", brandName);
      }

      const { data, error } = await queryBuilder.limit(500);

      if (error) throw error;

      // Group by category and calculate average prices
      const productsByCategory: Record<string, any[]> = {};
      data?.forEach((product) => {
        if (!product.category) return;

        if (!productsByCategory[product.category]) {
          productsByCategory[product.category] = [];
        }

        productsByCategory[product.category].push(product);
      });

      // Calculate statistics
      const analysis = Object.entries(productsByCategory).map(
        ([category, products]) => {
          const prices = products
            .map((p) => p.latest_price)
            .filter((price) => price !== null && price !== undefined);

          const avgPrice =
            prices.length > 0
              ? prices.reduce((sum, price) => sum + price, 0) / prices.length
              : 0;

          return {
            category,
            product_count: products.length,
            average_price: avgPrice.toFixed(2),
            price_range: {
              min: Math.min(...prices).toFixed(2),
              max: Math.max(...prices).toFixed(2),
            },
          };
        }
      );

      logger.info("Product analysis completed successfully", {
        category,
        brandName,
        totalProducts: data?.length || 0,
        categoriesAnalyzed: Object.keys(productsByCategory).length,
      });

      return JSON.stringify({
        analysis,
        total_products: data?.length || 0,
        categories_count: Object.keys(productsByCategory).length,
      });
    } catch (error) {
      logger.error("ProductAnalysisTool error", { error });
      return JSON.stringify({
        error: "Failed to analyze products",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "product_analysis",
    description:
      "Analyze product pricing, categories, and availability in the market. OPTIONAL: category (string) - specific category to analyze, brandName (string) - specific brand to analyze",
    schema: z.object({
      category: z
        .string()
        .optional()
        .describe("Product category to analyze (optional)"),
      brandName: z
        .string()
        .optional()
        .describe("Brand name to analyze (optional)"),
    }),
  }
);

export const RegionalMarketAnalysisByLocationTool = tool(
  async ({
    city,
    state,
    userRetailerId,
    maxRetailers,
  }: {
    city?: string;
    state?: string;
    userRetailerId?: string;
    maxRetailers?: number;
  }) => {
    try {
      logger.info(
        "🔥 RegionalMarketAnalysisByLocationTool called with structured input",
        {
          city,
          state,
          userRetailerId,
          maxRetailers,
          inputTypes: {
            city: typeof city,
            state: typeof state,
            userRetailerId: typeof userRetailerId,
            maxRetailers: typeof maxRetailers,
          },
        }
      );

      if (!city && !state) {
        logger.error("Invalid or missing city or state", { city, state });
        return JSON.stringify({
          error: "City or state is required",
          details: "Either city or state must be provided",
        });
      }

      const finalMaxRetailers = maxRetailers || 50;

      const analysis =
        await competitorService.getRegionalMarketAnalysisByLocation(
          city,
          state,
          userRetailerId,
          finalMaxRetailers
        );

      logger.info("Regional market analysis completed successfully", {
        city,
        state,
        userRetailerId,
        maxRetailers: finalMaxRetailers,
        hasAnalysis: !!analysis,
        categoriesFound: analysis.categoriesFound,
        totalProductsAnalyzed: analysis.totalProductsAnalyzed,
      });

      return JSON.stringify(analysis);
    } catch (error) {
      logger.error("RegionalMarketAnalysisByLocationTool error", { error });
      return JSON.stringify({
        error: "Failed to perform regional market analysis",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "RegionalMarketAnalysisByLocationTool",
    description:
      "Analyze regional market data across a city, state, or broader geographic area. Provides insights into regional pricing trends, competitor density, market opportunities, and geographic market positioning.",
    schema: z.object({
      city: z
        .string()
        .optional()
        .describe(
          "City name for regional analysis (optional if state is provided)"
        ),
      state: z
        .string()
        .optional()
        .describe(
          "State name for regional analysis (optional if city is provided)"
        ),
      userRetailerId: z
        .string()
        .optional()
        .describe(
          "Optional retailer ID of the user's business for comparison in regional context"
        ),
      maxRetailers: z
        .number()
        .min(1)
        .max(100)
        .optional()
        .describe(
          "Maximum number of retailers to include in analysis (1-100, default 50)"
        ),
    }),
  }
);

export const RegionalMarketAnalysisByRadiusTool = tool(
  async ({
    latitude,
    longitude,
    radiusMiles,
    userRetailerId,
    maxRetailers,
  }: {
    latitude: number;
    longitude: number;
    radiusMiles: number;
    userRetailerId?: string;
    maxRetailers?: number;
  }) => {
    try {
      logger.info(
        "🔥 RegionalMarketAnalysisByRadiusTool called with structured input",
        {
          latitude,
          longitude,
          radiusMiles,
          userRetailerId,
          maxRetailers,
          inputTypes: {
            latitude: typeof latitude,
            longitude: typeof longitude,
            radiusMiles: typeof radiusMiles,
            userRetailerId: typeof userRetailerId,
            maxRetailers: typeof maxRetailers,
          },
        }
      );

      if (!latitude || !longitude || isNaN(latitude) || isNaN(longitude)) {
        logger.error("Invalid or missing coordinates", { latitude, longitude });
        return JSON.stringify({
          error: "Valid latitude and longitude are required",
          details: "Both latitude and longitude must be valid numbers",
        });
      }

      const finalRadiusMiles = radiusMiles || 30;
      const finalMaxRetailers = maxRetailers || 50;

      const analysis =
        await competitorService.getRegionalMarketAnalysisByRadius(
          Number(latitude),
          Number(longitude),
          finalRadiusMiles,
          userRetailerId,
          finalMaxRetailers
        );

      logger.info("Regional market analysis completed successfully", {
        latitude,
        longitude,
        radiusMiles: finalRadiusMiles,
        userRetailerId,
        maxRetailers: finalMaxRetailers,
        hasAnalysis: !!analysis,
        categoriesFound: analysis.categoriesFound,
        totalProductsAnalyzed: analysis.totalProductsAnalyzed,
      });

      return JSON.stringify(analysis);
    } catch (error) {
      logger.error("RegionalMarketAnalysisByRadiusTool error", { error });
      return JSON.stringify({
        error: "Failed to perform regional market analysis",
        details: error instanceof Error ? error.message : String(error),
      });
    }
  },
  {
    name: "RegionalMarketAnalysisByRadiusTool",
    description:
      "Analyze market data within a specified radius from geographic coordinates. Provides distance-based insights, competitor proximity analysis, geographic density patterns, and location-specific market opportunities.",
    schema: z.object({
      latitude: z
        .number()
        .describe("Center latitude for radius-based analysis")
        .min(-90)
        .max(90),
      longitude: z
        .number()
        .describe("Center longitude for radius-based analysis")
        .min(-180)
        .max(180),
      radiusMiles: z
        .number()
        .describe("Radius in miles for analysis (1-500, default 30)")
        .min(1)
        .max(500),
      userRetailerId: z
        .string()
        .optional()
        .describe(
          "Optional retailer ID of the user's business for distance-based comparison"
        ),
      maxRetailers: z
        .number()
        .describe(
          "Maximum number of retailers to include in analysis (1-100, default 50)"
        )
        .min(1)
        .max(100),
    }),
  }
);
