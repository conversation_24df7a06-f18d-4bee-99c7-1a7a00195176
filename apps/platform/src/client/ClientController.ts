/**
 * @swagger
 * tags:
 *   name: Client
 *   description: Client-side interaction endpoints
 */

import Router from "@koa/router";
import EventPostJob from "./EventPostJob";
import { JSONSchemaType, validate } from "../core/validate";
import {
  ClientIdentifyParams,
  ClientIdentityKeys,
  ClientPostEventsRequest,
} from "./Client";
import { LocationState } from "../auth/AuthMiddleware";
import { locationMiddleware } from "../locations/LocationController";
import { DeviceParams } from "../users/User";
import UserPatchJob from "../users/UserPatchJob";
import UserDeviceJob from "../users/UserDeviceJob";
import User<PERSON>liasJob from "../users/UserAliasJob";

const router = new Router<LocationState>();
router.use(locationMiddleware);

/**
 * @swagger
 * /client/alias:
 *   post:
 *     summary: Create User Alias
 *     description: Creates an alias for a user to track across different identifiers
 *     tags: [Client]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               previous_id:
 *                 type: string
 *               user_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Alias created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *       400:
 *         description: Invalid input
 */
const aliasParams: JSONSchemaType<ClientIdentityKeys> = {
  $id: "aliasParams",
  type: "object",
  required: ["external_id", "anonymous_id"],
  properties: {
    anonymous_id: {
      type: "string",
    },
    external_id: {
      type: "string",
    },
    auth_id: {
      type: "string",
      nullable: true,
    },
  },
};
router.post("/alias", async (ctx) => {
  const alias = validate(aliasParams, ctx.request.body);
  await UserAliasJob.from({
    location_id: ctx.state.location.id,
    ...alias,
  }).queue();
  ctx.status = 204;
  ctx.body = "";
});

/**
 * @swagger
 * /client/identify:
 *   post:
 *     summary: Identify User
 *     description: Identifies a user with their traits and properties
 *     tags: [Client]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_id:
 *                 type: string
 *               traits:
 *                 type: object
 *     responses:
 *       200:
 *         description: User identified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *       400:
 *         description: Invalid input
 */
const identifyParams: JSONSchemaType<ClientIdentifyParams> = {
  $id: "identifyParams",
  type: "object",
  required: [],
  properties: {
    anonymous_id: {
      type: "string",
      nullable: true,
    },
    external_id: {
      type: "string",
      nullable: true,
    },
    email: {
      type: "string",
      nullable: true,
    },
    phone: {
      type: "string",
      nullable: true,
    },
    timezone: {
      type: "string",
      nullable: true,
    },
    locale: {
      type: "string",
      nullable: true,
    },
    data: {
      type: "object",
      nullable: true,
      additionalProperties: true,
    },
  },
  anyOf: [
    {
      required: ["anonymous_id"],
    },
    {
      required: ["external_id"],
    },
  ],
  additionalProperties: false,
} as any;
router.post("/identify", async (ctx) => {
  const user = validate(identifyParams, ctx.request.body);
  await UserPatchJob.from({
    location_id: ctx.state.location.id,
    user,
  }).queue();

  ctx.status = 204;
  ctx.body = "";
});

/**
 * @swagger
 * /client/devices:
 *   post:
 *     summary: Register Device
 *     description: Registers a new device for a user
 *     tags: [Client]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_id:
 *                 type: string
 *               device_id:
 *                 type: string
 *               platform:
 *                 type: string
 *     responses:
 *       200:
 *         description: Device registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *       400:
 *         description: Invalid input
 */
const deviceParams: JSONSchemaType<DeviceParams> = {
  $id: "deviceParams",
  type: "object",
  required: ["device_id", "os", "model", "app_build", "app_version"],
  properties: {
    anonymous_id: {
      type: "string",
      nullable: true,
    },
    external_id: {
      type: "string",
      nullable: true,
    },
    device_id: {
      type: "string",
    },
    token: {
      type: "string",
      nullable: true,
    },
    os: {
      type: "string",
    },
    os_version: {
      type: "string",
    },
    model: {
      type: "string",
    },
    app_build: {
      type: "string",
    },
    app_version: {
      type: "string",
    },
  },
  anyOf: [
    {
      required: ["anonymous_id"],
    },
    {
      required: ["external_id"],
    },
  ],
} as any;
router.post("/devices", async (ctx) => {
  const device = validate(deviceParams, ctx.request.body);
  await UserDeviceJob.from({
    location_id: ctx.state.location.id,
    ...device,
  }).queue();

  ctx.status = 204;
  ctx.body = "";
});

/**
 * @swagger
 * /client/events:
 *   post:
 *     summary: Track Event
 *     description: Tracks a user event with optional properties
 *     tags: [Client]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_id:
 *                 type: string
 *               event:
 *                 type: string
 *               properties:
 *                 type: object
 *     responses:
 *       200:
 *         description: Event tracked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *       400:
 *         description: Invalid input
 */
const postEventsRequest: JSONSchemaType<ClientPostEventsRequest> = {
  $id: "postEvents",
  type: "array",
  items: {
    type: "object",
    required: ["name"],
    properties: {
      name: {
        type: "string",
      },
      anonymous_id: {
        type: "string",
        nullable: true,
      },
      external_id: {
        type: "string",
        nullable: true,
      },
      data: {
        type: "object",
        nullable: true,
        additionalProperties: true,
      },
      user: {
        type: "object",
        nullable: true,
        properties: {
          email: {
            type: "string",
            nullable: true,
          },
          phone: {
            type: "string",
            nullable: true,
          },
          timezone: {
            type: "string",
            nullable: true,
          },
          locale: {
            type: "string",
            nullable: true,
          },
          data: {
            type: "object",
            nullable: true,
            additionalProperties: true,
          },
        },
      },
    },
    anyOf: [
      {
        required: ["anonymous_id"],
      },
      {
        required: ["external_id"],
      },
    ],
  },
  minItems: 1,
} as any;
router.post("/events", async (ctx) => {
  const events = validate(postEventsRequest, ctx.request.body);

  for (const event of events) {
    await EventPostJob.from({
      location_id: ctx.state.location.id,
      event,
    }).queue();
  }

  ctx.status = 204;
  ctx.body = "";
});

export default router;
