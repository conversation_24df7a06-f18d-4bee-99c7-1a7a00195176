import Router from "@koa/router";
import App from "../app";
import Event<PERSON>ostJob from "./EventPostJob";
import { JSONSchemaType, validate } from "../core/validate";
import { SegmentPostEventsRequest } from "./Client";
import { LocationState } from "../auth/AuthMiddleware";
import { locationMiddleware } from "../locations/LocationController";
import UserPatchJob from "../users/UserPatchJob";
import { Job } from "../queue";
import { parseLocale } from "../utilities";
import UserAliasJob from "../users/UserAliasJob";

const router = new Router<LocationState>();
router.use(locationMiddleware);

const segmentEventsRequest: JSONSchemaType<SegmentPostEventsRequest> = {
  $id: "segmentPostEvents",
  type: "array",
  items: {
    type: "object",
    required: ["type"],
    properties: {
      type: { type: "string" },
      event: {
        type: "string",
        nullable: true,
      },
      anonymousId: {
        type: "string",
        nullable: true,
      },
      userId: {
        type: "string",
        nullable: true,
      },
      previousId: {
        type: "string",
        nullable: true,
      },
      properties: {
        type: "object",
        nullable: true,
        additionalProperties: true,
      },
      traits: {
        type: "object",
        nullable: true,
        additionalProperties: true,
      },
      context: {
        type: "object",
        nullable: true,
        additionalProperties: true,
      },
      timestamp: { type: "string" },
    },
    anyOf: [
      {
        required: ["anonymousId"],
      },
      {
        required: ["userId"],
      },
    ],
  },
  minItems: 1,
  maxItems: 1000,
} as any;

/**
 * @swagger
 * components:
 *   schemas:
 *     SegmentEvent:
 *       type: object
 *       required:
 *         - type
 *       properties:
 *         type:
 *           type: string
 *           enum: [identify, alias, track]
 *           description: The type of Segment event
 *         event:
 *           type: string
 *           nullable: true
 *           description: Event name (required for track events)
 *         anonymousId:
 *           type: string
 *           nullable: true
 *           description: Anonymous ID of the user
 *         userId:
 *           type: string
 *           nullable: true
 *           description: User ID of the user
 *         previousId:
 *           type: string
 *           nullable: true
 *           description: Previous ID for alias events
 *         properties:
 *           type: object
 *           nullable: true
 *           additionalProperties: true
 *           description: Event properties (for track events)
 *         traits:
 *           type: object
 *           nullable: true
 *           additionalProperties: true
 *           description: User traits (for identify events)
 *         context:
 *           type: object
 *           nullable: true
 *           additionalProperties: true
 *           description: Event context
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: Event timestamp
 *       oneOf:
 *         - required: [anonymousId]
 *         - required: [userId]
 *     SegmentEventBatch:
 *       type: array
 *       items:
 *         $ref: '#/components/schemas/SegmentEvent'
 *       minItems: 1
 *       maxItems: 1000
 *       description: Batch of Segment events to process
 */

/**
 * @swagger
 * tags:
 *   name: Segment
 *   description: Segment event processing endpoints
 */

/**
 * @swagger
 * /client/segment:
 *   post:
 *     summary: Process Segment Events
 *     description: Accepts a batch of Segment events (identify, alias, track) for processing. Events are queued for asynchronous processing.
 *     tags: [Segment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SegmentEventBatch'
 *           example:
 *             - type: "identify"
 *               userId: "user123"
 *               traits:
 *                 email: "<EMAIL>"
 *                 phone: "+1234567890"
 *               context:
 *                 timezone: "America/New_York"
 *                 locale: "en-US"
 *               timestamp: "2024-03-20T10:00:00Z"
 *             - type: "track"
 *               userId: "user123"
 *               event: "Product Viewed"
 *               properties:
 *                 productId: "prod_123"
 *                 price: 99.99
 *               context:
 *                 page: "/products/123"
 *               timestamp: "2024-03-20T10:01:00Z"
 *             - type: "alias"
 *               userId: "user123"
 *               previousId: "anon_456"
 *               timestamp: "2024-03-20T10:02:00Z"
 *     responses:
 *       204:
 *         description: Events successfully queued for processing
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       401:
 *         description: Unauthorized - Location not found or invalid
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.post("/segment", async (ctx) => {
  const events = validate(segmentEventsRequest, ctx.request.body);

  let chunks: Job[] = [];

  for (const event of events) {
    const identity = {
      anonymous_id: event.anonymousId,
      external_id: event.userId,
    };
    if (event.type === "alias") {
      chunks.push(
        UserAliasJob.from({
          location_id: ctx.state.location.id,
          previous_id: event.previousId,
          ...identity,
        })
      );
    } else if (event.type === "identify") {
      chunks.push(
        UserPatchJob.from({
          location_id: ctx.state.location.id,
          user: {
            ...identity,
            email: event.traits?.email,
            phone: event.traits?.phone,
            timezone: event.context.timezone,
            locale: event.context.locale && parseLocale(event.context.locale),
            data: event.traits,
          },
        })
      );
    } else if (event.type === "track") {
      chunks.push(
        EventPostJob.from({
          location_id: ctx.state.location.id,
          event: {
            ...identity,
            name: event.event,
            data: { ...event.properties, ...event.context },
            created_at: new Date(event.timestamp),
          },
        })
      );
    }

    // Based on queue max batch size, process in largest chunks
    // possible
    if (chunks.length > App.main.queue.batchSize) {
      await App.main.queue.enqueueBatch(chunks);
      chunks = [];
    }
  }

  // Insert any remaining items
  if (chunks.length > 0) {
    await App.main.queue.enqueueBatch(chunks);
  }

  ctx.status = 204;
  ctx.body = "";
});

export default router;
