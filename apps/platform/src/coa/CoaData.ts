import Model from "../core/Model";

export class CoaData extends Model {
  declare id: number;
  location_id!: number;

  // Required fields
  product_name!: string;
  product_type!: string;
  batch_number!: string;
  sample_id!: string;
  test_date!: Date;
  lab_name!: string;
  product_id!: string;

  // Optional fields
  production_date?: Date;
  lab_license?: string;
  coa_url?: string;

  // Cannabinoid profile
  thc_percent?: number;
  thca_percent?: number;
  cbd_percent?: number;
  cbda_percent?: number;
  cbg_percent?: number;
  cbga_percent?: number;
  cbn_percent?: number;
  cbc_percent?: number;
  total_thc?: number;
  total_cbd?: number;
  total_cannabinoids?: number;

  // Terpene profile
  terpene_myrcene?: number;
  terpene_limonene?: number;
  terpene_caryophyllene?: number;
  terpene_linalool?: number;
  terpene_pinene?: number;
  terpene_humulene?: number;
  terpene_terpinolene?: number;
  total_terpenes?: number;

  // Residual solvents
  solvent_butane?: number;
  solvent_propane?: number;
  solvent_ethanol?: number;
  solvents_pass?: boolean;

  // Heavy metals
  metal_lead?: number;
  metal_mercury?: number;
  metal_arsenic?: number;
  metal_cadmium?: number;
  metals_pass?: boolean;

  // Microbials
  microbial_ecoli?: boolean;
  microbial_salmonella?: boolean;
  microbial_aspergillus?: boolean;
  yeast_mold_count?: number;
  microbials_pass?: boolean;

  // Other metrics
  moisture_content?: number;
  water_activity?: number;
  overall_pass?: boolean;

  // Metadata
  data?: Record<string, unknown>;
  declare created_at: Date;
  declare updated_at: Date;

  static tableName = "coa_data";

  static jsonAttributes = ["data"];

  flatten() {
    return {
      ...this,
      test_date: this.test_date?.toISOString(),
      production_date: this.production_date?.toISOString(),
      created_at: this.created_at?.toISOString(),
      updated_at: this.updated_at?.toISOString(),
    };
  }
}
