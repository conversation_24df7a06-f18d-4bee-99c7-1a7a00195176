import Router from "@koa/router";
import { locationRoleMiddleware } from "../locations/LocationService";
import {
  importFromFile,
  importFromNormalizedData,
} from "./CoaDataImportService";
import parse from "../storage/FileStream";
import { CoaData } from "./CoaData";
import { NormalizedData } from "../core/DataNormalizationService";
import { Knex } from "knex";
import { extractQueryParams } from "../utilities";
import { SearchSchema } from "../core/searchParams";
import { logger } from "../config/logger";
import { RequestError } from "../core/errors";

/**
 * @swagger
 * components:
 *   schemas:
 *     CoaData:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         location_id:
 *           type: integer
 *         product_id:
 *           type: integer
 *         test_date:
 *           type: string
 *           format: date
 *         data:
 *           type: object
 *           additionalProperties: true
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     CoaImportResponse:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *         status:
 *           type: string
 *         errors:
 *           type: array
 *           items:
 *             type: string
 *         imported:
 *           type: integer
 *           nullable: true
 *         skipped:
 *           type: integer
 *           nullable: true
 *     CoaListResponse:
 *       type: object
 *       properties:
 *         data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/CoaData'
 *         pagination:
 *           type: object
 *           properties:
 *             total:
 *               type: integer
 *             page:
 *               type: integer
 *             limit:
 *               type: integer
 *             pages:
 *               type: integer
 *     CoaProductListResponse:
 *       type: object
 *       properties:
 *         data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/CoaData'
 *         count:
 *           type: integer
 *     CoaErrorResponse:
 *       type: object
 *       properties:
 *         error:
 *           type: string
 */

/**
 * @swagger
 * tags:
 *   name: COA
 *   description: Certificate of Analysis (COA) data import and retrieval endpoints
 */

const router = new Router({
  prefix: "/coa",
});

/**
 * @swagger
 * /coa/import:
 *   post:
 *     summary: Import COA data (JSON or file upload)
 *     tags: [COA]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               coa_data:
 *                 type: array
 *                 items:
 *                   type: object
 *                   additionalProperties: true
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: COA data import started
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CoaImportResponse'
 *       400:
 *         description: Import failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CoaErrorResponse'
 */
/**
 * Import COA data
 * POST /coa/import
 */
router.post("/import", locationRoleMiddleware("editor"), async (ctx) => {
  // Check if this is a direct JSON data import
  if (ctx.request.body?.coa_data) {
    try {
      // Create normalized data directly
      const normalizedData: NormalizedData = {
        type: "coa",
        data: Array.isArray(ctx.request.body.coa_data)
          ? ctx.request.body.coa_data
          : [ctx.request.body.coa_data],
        errors: [],
      };

      // Import the normalized data
      const result = await importFromNormalizedData(
        ctx.state.location.id,
        normalizedData
      );

      ctx.status = 200;
      ctx.body = {
        message: "COA data imported successfully",
        status: "processing",
        ...result,
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        error:
          error instanceof Error ? error.message : "Failed to import COA data",
      };
    }
    return;
  }

  // Handle file upload
  try {
    const stream = await parse(ctx);

    // Import file data
    const result = await importFromFile(ctx.state.location.id, stream);

    ctx.status = 200;
    ctx.body = {
      message: "File format validated. COA import started in background.",
      status: "processing",
      ...result,
    };
  } catch (error) {
    ctx.status = 400;
    ctx.body = {
      error:
        error instanceof Error ? error.message : "Failed to import COA data",
    };
  }
});

/**
 * @swagger
 * /coa/product/{product_id}:
 *   get:
 *     summary: Get COAs for a specific product
 *     tags: [COA]
 *     parameters:
 *       - in: path
 *         name: product_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Product ID
 *     responses:
 *       200:
 *         description: List of COAs for the product
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CoaProductListResponse'
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CoaErrorResponse'
 *       500:
 *         description: Failed to get COA data for product
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CoaErrorResponse'
 */
/**
 * Get COAs for a specific product by product_id
 * GET /coa/product/:product_id
 */
router.get(
  "/product/:product_id",
  locationRoleMiddleware("support"),
  async (ctx) => {
    try {
      const productId = ctx.params.product_id;
      if (!productId) {
        throw new RequestError("Product ID is required");
      }

      // Get all COA data for this product, sorted by test date (newest first)
      const coaData = await CoaData.all(function (this: Knex.QueryBuilder) {
        return this.where({
          location_id: ctx.state.location.id,
          product_id: productId,
        }).orderBy("test_date", "desc");
      });

      ctx.body = {
        data: coaData,
        count: coaData.length,
      };
    } catch (error) {
      ctx.status = error instanceof RequestError ? 400 : 500;
      ctx.body = {
        error:
          error instanceof Error
            ? error.message
            : "Failed to get COA data for product",
      };
    }
  }
);

/**
 * @swagger
 * /coa/{id}:
 *   get:
 *     summary: Get a COA by ID
 *     tags: [COA]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: COA ID
 *     responses:
 *       200:
 *         description: COA details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CoaData'
 *       400:
 *         description: Invalid COA ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CoaErrorResponse'
 *       404:
 *         description: COA not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CoaErrorResponse'
 *       500:
 *         description: Failed to get COA
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CoaErrorResponse'
 */
/**
 * Get COA by ID
 * GET /coa/:id
 */
router.get("/:id", locationRoleMiddleware("support"), async (ctx) => {
  try {
    const id = parseInt(ctx.params.id);
    if (isNaN(id)) {
      throw new RequestError("Invalid COA ID");
    }

    const coa = await CoaData.first(function (this: Knex.QueryBuilder) {
      return this.where({ id, location_id: ctx.state.location.id });
    });

    if (!coa) {
      ctx.status = 404;
      ctx.body = { error: "COA not found" };
      return;
    }

    ctx.body = coa;
  } catch (error) {
    ctx.status = error instanceof RequestError ? 400 : 500;
    ctx.body = {
      error: error instanceof Error ? error.message : "Failed to get COA",
    };
  }
});

/**
 * @swagger
 * /coa:
 *   get:
 *     summary: List COAs (paginated)
 *     tags: [COA]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Sort field
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort direction
 *     responses:
 *       200:
 *         description: Paginated list of COAs
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CoaListResponse'
 *       500:
 *         description: Failed to list COA data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CoaErrorResponse'
 */
/**
 * List COAs
 * GET /coa
 */
router.get("/", locationRoleMiddleware("support"), async (ctx) => {
  try {
    // Use the predefined schema without type arguments
    const searchSchema = SearchSchema("coaSearchSchema", {
      sort: "test_date",
      direction: "desc",
      limit: 20,
    });

    // Use standard javascript without type assertion
    const params = extractQueryParams(ctx.query, searchSchema);

    // Destructure the parameters with defaults
    const {
      page = 1,
      limit = 20,
      sort = "test_date",
      direction = "desc",
    } = params;

    // Get total count for pagination
    const countResult = await CoaData.query()
      .count("id as total")
      .where("location_id", ctx.state.location.id)
      .first();
    const total = parseInt((countResult?.total as string) || "0");

    // Ensure these are numbers
    const pageNum = Number(page);
    const limitNum = Number(limit);

    // Get paginated results
    const results = await CoaData.all(function (this: Knex.QueryBuilder) {
      return this.where("location_id", ctx.state.location.id)
        .orderBy(sort, direction)
        .limit(limitNum)
        .offset((pageNum - 1) * limitNum);
    });

    ctx.body = {
      data: results,
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum),
      },
    };
  } catch (error) {
    ctx.status = 500;
    ctx.body = {
      error: error instanceof Error ? error.message : "Failed to list COA data",
    };
  }
});

export default router;
