import { FileStream } from "../storage/FileStream";
import { RequestError } from "../core/errors";
import { CoaData } from "./CoaData";
import {
  DataNormalizationService,
  NormalizedData,
} from "../core/DataNormalizationService";
import { logger } from "../config/logger";
import { Product } from "../products/Product";
import App from "../app";
import { Chunker } from "../utilities";

/**
 * Helper function to find a product by batch number
 * @param batch_number The batch number to search for
 * @param location_id The location ID to scope the search
 * @returns The product's meta_sku if found, null otherwise
 */
async function findProductIdByBatchNumber(
  batch_number: string,
  location_id: number
): Promise<string | null> {
  try {
    const product = await Product.first((qb) => {
      return qb.where({
        location_id,
        batch_number,
      });
    });

    return product ? product.meta_sku : null;
  } catch (error) {
    logger.error(`Error finding product by batch number: ${error}`);
    return null;
  }
}

/**
 * Validates and normalizes COA data from an uploaded file stream
 * @param stream The uploaded file stream
 * @returns The normalized data ready for import
 */
export const validateCoaFileData = async (
  stream: FileStream
): Promise<NormalizedData> => {
  if (!stream) {
    throw new RequestError("No file stream provided");
  }

  // Normalize the data from the stream
  const normalizedData = await DataNormalizationService.normalizeData(stream);

  // Verify this is COA data
  if (normalizedData.type !== "coa") {
    throw new RequestError(
      "The uploaded file is not recognized as Certificate of Analysis (COA) data. Please use the appropriate import endpoint."
    );
  }

  // Report any errors found during normalization
  if (normalizedData.errors.length > 0) {
    const errorMessages = normalizedData.errors.map(
      (err) => `Row ${err.row}: ${err.error}`
    );
    throw new RequestError(
      `Data validation errors:\n${errorMessages.join("\n")}`
    );
  }

  return normalizedData;
};

/**
 * Imports COA data from a normalized data structure
 * @param location_id The location to import data for
 * @param normalizedData Previously normalized COA data
 * @param onProgress Optional callback for progress updates
 * @returns Result of the import operation
 */
export const importFromNormalizedData = async (
  location_id: number,
  normalizedData: NormalizedData,
  onProgress?: (count: number) => void
): Promise<{ processed: number; errors: any[] }> => {
  if (!location_id) {
    throw new RequestError("Missing required parameter: location_id");
  }

  if (normalizedData.type !== "coa") {
    throw new RequestError("Invalid data type. Expected COA data.");
  }

  try {
    // Process the data rows
    let rowCount = 0;
    const errors: any[] = [];
    const chunker = new Chunker<Partial<CoaData>>(async (items) => {
      for (const item of items) {
        try {
          await CoaData.insert(item);
        } catch (error) {
          logger.error(`Error inserting COA data: ${error}`);
          errors.push({
            row: rowCount,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }
    }, App.main.queue.batchSize);

    for (const row of normalizedData.data) {
      rowCount++;
      try {
        const coaData = row as Partial<CoaData>;

        // Add location_id to the record
        coaData.location_id = location_id;

        // If product_id is not provided but batch_number is, try to find the product
        if (!coaData.product_id && coaData.batch_number) {
          const productId = await findProductIdByBatchNumber(
            coaData.batch_number,
            location_id
          );

          if (productId) {
            coaData.product_id = productId;
            logger.info(
              `Found product ID ${productId} for batch ${coaData.batch_number}`
            );
          } else {
            // If no product found, use batch number as product_id to avoid database constraints
            coaData.product_id = coaData.batch_number;
            logger.warn(
              `No product found for batch ${coaData.batch_number}, using batch number as product_id`
            );
          }
        }

        // Queue for insertion
        await chunker.add(coaData);

        // Update the corresponding product with the COA URL if batch_number matches
        if (coaData.batch_number && coaData.coa_url) {
          try {
            // Find products with matching batch number
            const products = await Product.all((qb) => {
              return qb
                .where("location_id", location_id)
                .where("batch_number", coaData.batch_number);
            });

            // Update each matching product with the COA URL
            for (const product of products) {
              await Product.update((qb) => qb.where("id", product.id), {
                coa_url: coaData.coa_url,
              });
              logger.info(`Updated product ${product.meta_sku} with COA URL`);
            }
          } catch (linkError) {
            logger.error(`Error linking COA to products: ${linkError}`);
          }
        }

        if (onProgress) {
          onProgress(rowCount);
        }
      } catch (error) {
        logger.error(`Error processing COA row ${rowCount}:`, error);
        errors.push({
          row: rowCount,
          error: error instanceof Error ? error.message : String(error),
        });
        continue; // Skip failed rows instead of stopping
      }
    }

    // Make sure to flush any remaining items
    await chunker.flush();

    return {
      processed: rowCount - errors.length,
      errors,
    };
  } catch (error) {
    logger.error("Error in COA data import:", error);
    throw error;
  }
};

/**
 * Imports COA data from a file upload
 * @param location_id The location to import data for
 * @param stream The file upload stream to process
 * @param onProgress Optional callback for progress updates
 * @returns Result of the import operation
 */
export const importFromFile = async (
  location_id: number,
  stream: FileStream,
  onProgress?: (count: number) => void
): Promise<{ processed: number; errors: any[] }> => {
  if (!location_id) {
    throw new RequestError("Missing required parameter: location_id");
  }

  if (!stream) {
    throw new RequestError("No file stream provided");
  }

  try {
    // First validate and normalize the data
    const processedData = await validateCoaFileData(stream);
    logger.info(
      `Normalization complete. Found ${processedData.data.length} valid COA records`
    );

    // Process the normalized data
    return await importFromNormalizedData(
      location_id,
      processedData,
      onProgress
    );
  } catch (error) {
    logger.error("Error in COA data import:", error);
    throw error;
  }
};
