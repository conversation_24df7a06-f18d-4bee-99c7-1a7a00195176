/* eslint-disable indent */
import { logger } from "../config/logger";
import { RequestError } from "../core/errors";
import App from "../app";
import { CompetitorDataVectorService } from "./CompetitorDataVectorService";
import { SupabaseService } from "../supabase/SupabaseService";
import { CompetitorService, competitorService } from "./CompetitorService";

export interface Product {
  id: string;
  meta_sku: string;
  meta_upc: string;
  product_name: string;
  latest_price: number | null;
  category: string;
  retailer_id?: string;
  retailer_name?: string;
  updated_at: string;
  location_id: number;
}

// Types for competitor analysis
export interface CompetitorPrice {
  id?: number;
  location_id: number;
  competitor_place_id: string;
  product_sku: string;
  product_name: string;
  price: number;
  product_category?: string;
  brand_name?: string;
  on_special?: boolean;
  regular_price?: number;
  source: string;
  observed_at: Date;
  created_at?: Date;
  updated_at?: Date;
}

export interface PriceComparison {
  product_id: string;
  product_name: string;
  our_price: number;
  competitor_prices: {
    competitor_name: string;
    price: number;
    price_difference: number;
    price_difference_percentage: number;
    last_updated: string;
  }[];
  average_market_price: number;
  min_market_price: number;
  max_market_price: number;
  pricing_position: "below_average" | "average" | "premium";
}

export interface MarketAnalysis {
  category: string;
  avg_prices: {
    our_avg_price: number;
    market_avg_price: number;
    difference_percentage: number;
  };
  price_trends: {
    period: string;
    our_avg_price: number;
    market_avg_price: number;
  }[];
  competitors: {
    name: string;
    market_share_estimate: number;
    price_position: string;
    key_products: string[];
  }[];
  insights: string[];
  recommendations: string[];
}

export interface Competitor {
  id?: number;
  location_id: number;
  competitor_place_id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  distance_km: number;
  product_count?: number;
  created_at?: Date;
  updated_at?: Date;
}

export interface PriceComparisonResult {
  productId: string;
  productName: string;
  yourPrice: number;
  competitorPrices: {
    competitorId: number;
    competitorName: string;
    price: number;
    priceDifference: number;
    percentageDifference: number;
  }[];
  averagePrice: number;
  minimumPrice: number;
  maximumPrice: number;
  pricePosition:
    | "lowest"
    | "below_average"
    | "average"
    | "above_average"
    | "highest";
}

export interface MarketAnalysisResult {
  category: string;
  yourAvgPrice: number;
  marketAvgPrice: number;
  priceGap: number;
  priceGapPercentage: number;
  competitorsWithCategory: number;
  totalCompetitors: number;
  marketLeader: {
    id: number;
    name: string;
    averagePrice: number;
  };
  pricingRecommendation?: {
    recommendedPrice: number;
    potentialRevenueDifference: number;
  };
  insights: string[];
  recommendations: string[];
}

const COMPETITOR_INDEX = "competitor-analysis";

export class CompetitorAnalysisService {
  private static getSupabaseService(): SupabaseService {
    return new SupabaseService({
      url: process.env.SUPABASE_URL || "",
      key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
      bucket: process.env.SUPABASE_BUCKET || "location-data",
    });
  }

  private static getCompetitorService(): CompetitorService {
    return competitorService;
  }

  private competitors: any[] = [];
  private competitorService: any;

  constructor() {
    this.competitorService = new CompetitorService();
  }

  private extractCompetitorName(query: string): string | null {
    const competitorNameMatch = query.match(
      /([A-Za-z\s]+)(?:'s|s)?\s+(?:products|inventory|stock|selection)/i
    );
    return competitorNameMatch ? competitorNameMatch[1].trim() : null;
  }

  /**
   * Initialize the service
   */
  static async initialize() {
    try {
      // Make sure the vector service is initialized
      await CompetitorDataVectorService.initialize();
      logger.info("CompetitorAnalysisService initialized");
    } catch (error) {
      logger.error(`Error initializing competitor analysis service: ${error}`);
    }
  }

  /**
   * Compare our product pricing with competitors
   * Uses products table which already contains scraped Weedmaps data
   */
  static async compareProductPricing(
    locationId: number,
    productId: string
  ): Promise<PriceComparisonResult> {
    try {
      // Get product details from your store
      const product = await App.main
        .db("products")
        .where({ id: productId, location_id: locationId })
        .first();

      if (!product) {
        throw new Error(`Product not found: ${productId}`);
      }

      // Get competitors for this location
      const competitors = await App.main
        .db("location_competitors")
        .where({ location_id: locationId })
        .select("*");

      if (!competitors.length) {
        throw new Error(`No competitors found for location: ${locationId}`);
      }

      // Get supabase service
      const supabaseService = this.getSupabaseService();

      // Set up result object
      const result: PriceComparisonResult = {
        productId,
        productName: product.name,
        yourPrice: product.price,
        competitorPrices: [],
        averagePrice: product.price,
        minimumPrice: product.price,
        maximumPrice: product.price,
        pricePosition: "average",
      };

      // For each competitor, search for similar products
      for (const competitor of competitors) {
        try {
          // Get competitor products from Supabase
          const response = await supabaseService.getRetailerProducts(
            competitor.competitor_place_id
          );

          const products = response.products;

          if (!products || products.length === 0) {
            continue;
          }

          // Find similar products by name
          const similarProducts = products.filter((p) => {
            const productNameLower = product.name.toLowerCase();
            const competitorProductNameLower = p.product_name.toLowerCase();
            return (
              competitorProductNameLower.includes(productNameLower) ||
              productNameLower.includes(competitorProductNameLower)
            );
          });

          if (similarProducts.length > 0) {
            // Get the average price of similar products
            const avgPrice =
              similarProducts.reduce(
                (sum, p) => sum + (p.latest_price || 0),
                0
              ) / similarProducts.length;

            // Add to comparison
            result.competitorPrices.push({
              competitorId: competitor.id,
              competitorName: competitor.name,
              price: avgPrice,
              priceDifference: product.price - avgPrice,
              percentageDifference:
                ((product.price - avgPrice) / avgPrice) * 100,
            });
          }
        } catch (error) {
          logger.error(
            `Error getting products for competitor ${competitor.id}:`,
            error
          );
          // Continue with other competitors even if one fails
        }
      }

      // Calculate overall market metrics
      if (result.competitorPrices.length > 0) {
        const allPrices = [
          product.price,
          ...result.competitorPrices.map((c) => c.price),
        ];
        result.averagePrice =
          allPrices.reduce((sum, price) => sum + price, 0) / allPrices.length;
        result.minimumPrice = Math.min(...allPrices);
        result.maximumPrice = Math.max(...allPrices);

        // Determine price position
        if (product.price === result.minimumPrice) {
          result.pricePosition = "lowest";
        } else if (product.price === result.maximumPrice) {
          result.pricePosition = "highest";
        } else if (product.price < result.averagePrice) {
          result.pricePosition = "below_average";
        } else if (product.price > result.averagePrice) {
          result.pricePosition = "above_average";
        } else {
          result.pricePosition = "average";
        }
      }

      return result;
    } catch (error) {
      logger.error("Error comparing product pricing:", error);
      throw error;
    }
  }

  /**
   * Get market analysis for a category
   * Uses products table which already contains scraped Weedmaps data
   */
  static async getMarketAnalysis(
    locationId: number,
    category: string
  ): Promise<MarketAnalysisResult> {
    try {
      // Get products in this category from your store
      const yourProducts = await App.main
        .db("products")
        .where({ location_id: locationId, category })
        .select("*");

      if (!yourProducts.length) {
        throw new Error(
          `No products found in category ${category} for location ${locationId}`
        );
      }

      // Calculate your average price
      const yourAvgPrice =
        yourProducts.reduce((sum, p) => sum + (p.price || 0), 0) /
        yourProducts.length;

      // Get competitors for this location
      const competitors = await App.main
        .db("location_competitors")
        .where({ location_id: locationId })
        .select("*");

      if (!competitors.length) {
        throw new Error(`No competitors found for location: ${locationId}`);
      }

      // Get supabase service
      const supabaseService = this.getSupabaseService();

      // Track market data
      const competitorCategoryData: {
        competitorId: number;
        competitorName: string;
        averagePrice: number;
        productCount: number;
      }[] = [];

      let totalMarketProducts = 0;
      let totalMarketPrice = 0;

      // For each competitor, get their products in this category
      for (const competitor of competitors) {
        try {
          // Get competitor products from Supabase
          const response = await supabaseService.getRetailerProducts(
            competitor.competitor_place_id
          );

          const products = response.products;

          if (!products || products.length === 0) {
            continue;
          }

          // Filter by category
          const categoryProducts = products.filter(
            (p) =>
              p.category && p.category.toLowerCase() === category.toLowerCase()
          );

          if (categoryProducts.length > 0) {
            const avgPrice =
              categoryProducts.reduce(
                (sum: number, p: any) => sum + (p.latest_price || 0),
                0
              ) / categoryProducts.length;

            // Add to comparison
            competitorCategoryData.push({
              competitorId: competitor.id,
              competitorName: competitor.name,
              averagePrice: avgPrice,
              productCount: categoryProducts.length,
            });

            totalMarketProducts += categoryProducts.length;
            totalMarketPrice += categoryProducts.reduce(
              (sum: number, p: any) => sum + (p.latest_price || 0),
              0
            );
          }
        } catch (error) {
          logger.error(
            `Error getting products for competitor ${competitor.id}:`,
            error
          );
          // Continue with other competitors even if one fails
        }
      }

      // Calculate market averages
      const marketAvgPrice =
        competitorCategoryData.length > 0
          ? totalMarketPrice / totalMarketProducts
          : yourAvgPrice;

      // Find market leader (competitor with most products in this category)
      const marketLeader =
        competitorCategoryData.length > 0
          ? competitorCategoryData.reduce((prev, current) =>
              prev.productCount > current.productCount ? prev : current
            )
          : {
              competitorId: 0,
              competitorName: "N/A",
              averagePrice: 0,
              productCount: 0,
            };

      // Create analysis result
      const result: MarketAnalysisResult = {
        category,
        yourAvgPrice,
        marketAvgPrice,
        priceGap: yourAvgPrice - marketAvgPrice,
        priceGapPercentage:
          ((yourAvgPrice - marketAvgPrice) / marketAvgPrice) * 100,
        competitorsWithCategory: competitorCategoryData.length,
        totalCompetitors: competitors.length,
        marketLeader: {
          id: marketLeader.competitorId,
          name: marketLeader.competitorName,
          averagePrice: marketLeader.averagePrice,
        },
        insights: [],
        recommendations: [],
      };

      // Add pricing recommendation if applicable
      if (result.priceGapPercentage > 10) {
        // If your prices are more than 10% higher, suggest a adjustment
        const recommendedPrice = marketAvgPrice * 1.05; // 5% above market average
        const potentialRevenueDifference =
          (recommendedPrice - yourAvgPrice) * yourProducts.length;

        result.pricingRecommendation = {
          recommendedPrice,
          potentialRevenueDifference,
        };
      }

      return result;
    } catch (error) {
      logger.error("Error getting market analysis:", error);
      throw error;
    }
  }

  /**
   * Sync competitor data from external sources
   * This would integrate with web search in a production environment
   */
  static async syncCompetitorData(locationId: number) {
    try {
      // Get existing competitors for this location
      const competitors = await App.main
        .db("location_competitors")
        .where({ location_id: locationId })
        .select("*");

      if (!competitors.length) {
        return {
          success: true,
          message: "No competitors found to sync",
          updated: 0,
          total: 0,
        };
      }

      // Get supabase service
      const supabaseService = this.getSupabaseService();

      let updatedCount = 0;

      // For each competitor, update product count and latest data
      for (const competitor of competitors) {
        try {
          // Get competitor data from Supabase
          const retailer = await supabaseService.getRetailerById(
            competitor.competitor_place_id
          );

          if (!retailer) {
            continue;
          }

          // Update competitor with latest data
          const response = await supabaseService.getRetailerProducts(
            competitor.competitor_place_id
          );
          const productCount = response.products ? response.products.length : 0;

          // Update in database
          await App.main
            .db("location_competitors")
            .where({ id: competitor.id })
            .update({
              name:
                retailer.name || retailer.dispensary_name || competitor.name,
              address:
                retailer.address ||
                retailer.physical_address ||
                competitor.address,
              product_count: productCount,
              updated_at: new Date(),
            });

          updatedCount++;
        } catch (error) {
          logger.error(`Error syncing competitor ${competitor.id}:`, error);
          // Continue with other competitors even if one fails
        }
      }

      return {
        success: true,
        message: `Successfully synced ${updatedCount} competitors`,
        updated: updatedCount,
        total: competitors.length,
      };
    } catch (error) {
      logger.error("Error syncing competitor data:", error);
      throw error;
    }
  }

  /**
   * Helper method to store analysis results in the vector database
   * Uses CompetitorDataVectorService for storage
   */
  private static async storeAnalysisResult(
    analysisType: string,
    data: any,
    locationId: number,
    identifier: string
  ) {
    try {
      // Convert the analysis to a text representation for vector search
      let text = `${analysisType} for ${identifier}: `;

      if (analysisType === "price_comparison") {
        const comparison = data as PriceComparison;
        text += `Product: ${comparison.product_name}, Our price: $${comparison.our_price}, `;
        text += `Market average: $${comparison.average_market_price}, `;
        text += `Position: ${comparison.pricing_position}`;
      } else if (analysisType === "market_analysis") {
        const analysis = data as MarketAnalysis;
        text += `Category: ${analysis.category}, `;
        text += `Our average: $${analysis.avg_prices.our_avg_price}, `;
        text += `Market average: $${analysis.avg_prices.market_avg_price}, `;
        text += `Insights: ${analysis.insights.join(". ")}`;
      }

      // Use the CompetitorDataVectorService to store this data
      const record = {
        id: Date.now(), // Using timestamp as ID
        location_id: locationId,
        competitor_place_id: "internal_analysis",
        name: analysisType,
        address: identifier, // Using identifier (product ID or category) as the address field
        latitude: 0,
        longitude: 0,
        distance_km: 0,
        analysisData: data, // Store the full analysis data
      };

      await CompetitorDataVectorService.upsertCompetitorData([record]);
    } catch (error) {
      logger.error(`Error storing ${analysisType} result in vector DB:`, error);
      // Non-critical operation, so we just log and continue
    }
  }

  /**
   * Format product price comparison for agent consumption
   */
  static formatPriceComparisonForAgent(
    locationId: number,
    productId: string
  ): Promise<string> {
    return this.compareProductPricing(locationId, productId)
      .then((comparison) => {
        const competitorPrices = comparison.competitorPrices
          .map((c) => {
            return `- ${c.competitorName}: $${c.price.toFixed(2)} (${
              c.priceDifference > 0 ? "+" : ""
            }${c.priceDifference.toFixed(2)}, ${
              c.percentageDifference > 0 ? "+" : ""
            }${c.percentageDifference.toFixed(1)}%)`;
          })
          .join("\n");

        return `
Product: ${comparison.productName}
Your price: $${comparison.yourPrice.toFixed(2)}

Competitor prices:
${competitorPrices}

Market analysis:
- Average market price: $${comparison.averagePrice.toFixed(2)}
- Minimum market price: $${comparison.minimumPrice.toFixed(2)}
- Maximum market price: $${comparison.maximumPrice.toFixed(2)}
- Your pricing position: ${comparison.pricePosition.replace("_", " ")}
        `;
      })
      .catch((error) => {
        logger.error("Error formatting price comparison for agent:", error);
        return "Unable to retrieve price comparison data at this time.";
      });
  }

  /**
   * Format market analysis for agent consumption
   */
  static formatMarketAnalysisForAgent(
    locationId: number,
    category: string
  ): Promise<string> {
    return this.getMarketAnalysis(locationId, category)
      .then((analysis) => {
        const insights = analysis.insights
          .map((insight) => `- ${insight}`)
          .join("\n");

        const recommendations = analysis.recommendations
          .map((rec) => `- ${rec}`)
          .join("\n");

        return `
Market Analysis for ${category}:

Price comparison:
- Your average price: $${analysis.yourAvgPrice.toFixed(2)}
- Market average price: $${analysis.marketAvgPrice.toFixed(2)}
- Difference: ${analysis.priceGap > 0 ? "+" : ""}${analysis.priceGap.toFixed(
          1
        )}%

Competitors:
- ${analysis.marketLeader.name}: ${analysis.marketLeader.averagePrice.toFixed(
          2
        )}

Insights:
${insights}

Recommendations:
${recommendations}
        `;
      })
      .catch((error) => {
        logger.error("Error formatting market analysis for agent:", error);
        return "Unable to retrieve market analysis data at this time.";
      });
  }

  static async getCompetitorAnalysis(
    locationId: number,
    category: string
  ): Promise<string> {
    return this.getMarketAnalysis(locationId, category)
      .then((analysis) => {
        // Generate insights from the analysis data
        const insights = [
          `Your average price in ${category} is ${
            analysis.priceGapPercentage > 0 ? "above" : "below"
          } the market average by ${Math.abs(
            analysis.priceGapPercentage
          ).toFixed(1)}%`,
          `You have ${analysis.totalCompetitors} competitors with ${analysis.competitorsWithCategory} offering products in this category`,
          `Market leader is ${
            analysis.marketLeader.name
          } with average price of $${analysis.marketLeader.averagePrice.toFixed(
            2
          )}`,
        ];

        // Generate recommendations
        const recommendations = [];
        if (analysis.priceGapPercentage > 10) {
          recommendations.push(
            `Consider adjusting prices to be closer to market average (${analysis.marketAvgPrice.toFixed(
              2
            )})`
          );
        } else if (analysis.priceGapPercentage < -10) {
          recommendations.push(
            "Consider opportunity to increase margins by raising prices"
          );
        }

        if (analysis.pricingRecommendation) {
          recommendations.push(
            `Recommended price point: $${analysis.pricingRecommendation.recommendedPrice.toFixed(
              2
            )} could yield $${Math.abs(
              analysis.pricingRecommendation.potentialRevenueDifference
            ).toFixed(2)} in revenue ${
              analysis.pricingRecommendation.potentialRevenueDifference > 0
                ? "increase"
                : "adjustments"
            }`
          );
        }

        // Use generated insights and recommendations
        analysis.insights = insights;
        analysis.recommendations = recommendations;

        const insightsText = (analysis.insights || [])
          .map((insight) => `- ${insight}`)
          .join("\n");
        const recommendationsText = (analysis.recommendations || [])
          .map((rec) => `- ${rec}`)
          .join("\n");

        return `
## Market Analysis for ${category}

Price comparison:
- Your average price: $${analysis.yourAvgPrice.toFixed(2)}
- Market average price: $${analysis.marketAvgPrice.toFixed(2)}
- Difference: ${analysis.priceGap > 0 ? "+" : ""}${analysis.priceGap.toFixed(
          2
        )} (${
          analysis.priceGapPercentage > 0 ? "+" : ""
        }${analysis.priceGapPercentage.toFixed(1)}%)

Competitors:
- Market leader: ${
          analysis.marketLeader.name
        } ($${analysis.marketLeader.averagePrice.toFixed(2)})
- Competitors with ${category} products: ${
          analysis.competitorsWithCategory
        } of ${analysis.totalCompetitors}

Insights:
${insightsText}

Recommendations:
${recommendationsText}
        `;
      })
      .catch((error) => {
        logger.error("Error generating competitor analysis:", error);
        return `Failed to generate market analysis for ${category}. Error: ${error.message}`;
      });
  }

  /**
   * Get specific competitor information for a given query directly from Supabase
   * This is useful for targeted questions about competitors that don't need vector search
   */
  async getSpecificCompetitorInfo(
    locationId: number,
    query: string
  ): Promise<any> {
    try {
      logger.info({
        message: "Starting getSpecificCompetitorInfo",
        locationId,
        query,
      });

      // Get competitors for this location
      this.competitors = await App.main
        .db("location_competitors")
        .where({ location_id: locationId })
        .select("*");

      logger.info({
        message: "Retrieved competitors from database",
        locationId,
        competitorCount: this.competitors.length,
        competitors: this.competitors.map((c) => ({
          id: c.place_id,
          name: c.name,
        })),
      });

      if (!this.competitors.length) {
        logger.warn({
          message: "No competitors found for location",
          locationId,
          query,
        });
        return { error: `No competitors found for location: ${locationId}` };
      }

      // Normalize query for better matching
      const normalizedQuery = query.toLowerCase().trim();

      // Extract competitor name from query if present
      const competitorName = this.extractCompetitorName(normalizedQuery);

      logger.info({
        message: "Extracted competitor name",
        originalQuery: query,
        extractedName: competitorName,
      });

      if (competitorName) {
        // Find the specific competitor
        const specificCompetitor = this.competitors.find(
          (c) => c.name.toLowerCase() === competitorName.toLowerCase()
        );

        logger.info({
          message: "Found specific competitor",
          competitorName,
          found: Boolean(specificCompetitor),
          competitorDetails: specificCompetitor
            ? {
                id: specificCompetitor.place_id,
                name: specificCompetitor.name,
                address: specificCompetitor.address,
              }
            : null,
        });

        if (specificCompetitor) {
          logger.info({
            message: "Attempting to get products for competitor",
            competitorName,
            placeId: specificCompetitor.place_id,
          });

          // Get products for this competitor
          const products = await this.competitorService.getRetailerProducts(
            specificCompetitor.place_id
          );

          logger.info({
            message: "Retrieved products for specific competitor",
            competitorName,
            placeId: specificCompetitor.place_id,
            productCount: products.total_count,
            hasProducts: products.products.length > 0,
            sampleProducts: products.products.slice(0, 3).map((p: Product) => ({
              id: p.id,
              name: p.product_name,
              price: p.latest_price,
            })),
          });

          if (products.products.length === 0) {
            logger.warn({
              message: "No products found for specific competitor",
              competitorName,
              placeId: specificCompetitor.place_id,
            });
          }

          return {
            competitor: specificCompetitor,
            products: products.products,
            total_products: products.total_count,
          };
        }
      }

      // If no specific competitor found or no competitor name in query,
      // return general competitor information
      const generalInfo = {
        total_competitors: this.competitors.length,
        competitors: this.competitors.map((c) => ({
          name: c.name,
          address: c.address,
          distance: c.distance,
        })),
      };

      logger.info({
        message: "Returning general competitor information",
        totalCompetitors: generalInfo.total_competitors,
        competitorNames: generalInfo.competitors.map((c) => c.name),
      });

      return generalInfo;
    } catch (error) {
      logger.error({
        message: "Error in getSpecificCompetitorInfo",
        locationId,
        query,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  static async analyzeCompetitorProducts(
    locationId: number,
    startDate: Date,
    endDate: Date
  ) {
    try {
      logger.info({
        message: "Starting analyzeCompetitorProducts",
        locationId,
        startDate,
        endDate,
      });

      // Initialize the Supabase service
      const supabaseService = this.getSupabaseService();

      // Get competitors for this location
      const competitors = await App.main
        .db("location_competitors")
        .where("location_id", locationId)
        .select("*");

      logger.info({
        message: "Retrieved competitors for analysis",
        locationId,
        competitorCount: competitors.length,
      });

      // Get competitor product data from Supabase
      const competitorProductData: any[] = [];

      for (const competitor of competitors) {
        try {
          // Get competitor products from Supabase
          const result = await supabaseService.getRetailerProducts(
            competitor.competitor_place_id,
            500 // Limit to 500 products per competitor
          );

          if (result.products && result.products.length > 0) {
            // Enhance competitor products with additional analytics fields to match our schema
            const enhancedProducts = result.products.map((product: any) => ({
              product_name: product.product_name,
              master_category: product.category || "Unknown",
              avg_price: product.latest_price || 0,
              units_sold: 0, // We don't have this data for competitors
              revenue: 0, // We don't have this data for competitors
              profit: 0, // We don't have this data for competitors
              popularity: product.popularity || 0, // Some products might have this field
              competitor_id: competitor.competitor_place_id,
              competitor_name: competitor.name,
            }));

            competitorProductData.push(...enhancedProducts);

            logger.info({
              message: "Retrieved competitor products from Supabase",
              competitor: competitor.name,
              productCount: enhancedProducts.length,
            });
          }
        } catch (error) {
          logger.error({
            message: "Error getting competitor products",
            competitor: competitor.name,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }

      logger.info({
        message: "Completed competitor product analysis",
        locationId,
        competitorProductCount: competitorProductData.length,
      });

      // Process competitor data to calculate popularity based on frequency
      // Map categories to frequency to use as a proxy for popularity
      const categoryPopularity: Record<string, number> = {};
      competitorProductData.forEach((product) => {
        const category = product.master_category;
        categoryPopularity[category] = (categoryPopularity[category] || 0) + 1;
      });

      // Add popularity score to competitor products based on category frequency
      competitorProductData.forEach((product) => {
        if (!product.popularity) {
          product.popularity = categoryPopularity[product.master_category] || 1;
        }
      });

      return {
        type: "competitor_products",
        data: {
          productData: competitorProductData,
          totalProducts: competitorProductData.length,
          dataSource: "supabase",
        },
      };
    } catch (error) {
      logger.error("Error analyzing competitor products:", error);
      throw error;
    }
  }
}
