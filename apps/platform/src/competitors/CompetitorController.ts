import Router from "@koa/router";
import { Context } from "koa";
import { CompetitorAnalysisService } from "./CompetitorAnalysisService";
import { CompetitorDataVectorService } from "./CompetitorDataVectorService";
import { logger } from "../config/logger";
import { locationRoleMiddleware } from "../locations/LocationService";
import { requireAgentCapability } from "../agents/AgentCapabilityDecorator";
import App from "../app";
import { LocationState } from "../auth/AuthMiddleware";
import { competitorService } from "./CompetitorService";

const router = new Router<LocationState>({
  prefix: "/competitors",
});

/**
 * @swagger
 * tags:
 *   name: Competitor
 *   description: Competitor management endpoints
 */

/**
 * @swagger
 * /competitors:
 *   get:
 *     summary: List Competitors
 *     description: Retrieves a paginated list of competitors for the current location
 *     tags: [Competitor]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort direction
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Competitor'
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 */
router.get("/", locationRoleMiddleware("support"), async (ctx: Context) => {
  const locationId = ctx.state.location.id;

  try {
    // Get competitors from the service instead of direct DB call
    const competitors = await competitorService.getCompetitors(locationId);

    // Get product counts for each competitor from Supabase via CompetitorService
    const competitorsWithProductCounts = await Promise.all(
      competitors.map(async (competitor) => {
        try {
          if (
            !competitor.place_id ||
            competitor.place_id.startsWith("custom-")
          ) {
            // Skip custom competitors or ones without place_id
            return {
              ...competitor,
              productCount: 0,
              isFromOurDatabase: false,
            };
          }

          // Use the new method to get just the product count
          const productCount = await competitorService.getRetailerProductCount(
            competitor.place_id
          );

          return {
            ...competitor,
            productCount,
            isFromOurDatabase: true,
          };
        } catch (err) {
          logger.error(
            `Error getting product count for competitor ${competitor.place_id}:`,
            err
          );
          return {
            ...competitor,
            productCount: 0,
            isFromOurDatabase:
              competitor.place_id && !competitor.place_id.startsWith("custom-"),
          };
        }
      })
    );

    ctx.status = 200;
    ctx.body = { competitors: competitorsWithProductCounts };
  } catch (error) {
    logger.error("Error fetching competitors:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to fetch competitors",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /competitors:
 *   post:
 *     summary: Create Competitor
 *     description: Creates a new competitor for the current location
 *     tags: [Competitor]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CompetitorCreateParams'
 *     responses:
 *       200:
 *         description: Competitor created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Competitor'
 *       400:
 *         description: Invalid input
 */
router.post("/", locationRoleMiddleware("admin"), async (ctx: Context) => {
  const locationId = ctx.state.location.id;
  const competitors = ctx.request.body.competitors || [ctx.request.body];

  logger.info("Adding competitors:", {
    locationId,
    requestBody: ctx.request.body,
    competitors,
    competitorsLength: competitors.length,
  });

  try {
    // Validate input
    if (!Array.isArray(competitors) || competitors.length === 0) {
      logger.warn("Invalid competitors array:", { competitors });
      ctx.status = 400;
      ctx.body = {
        error: "Missing required fields",
      };
      return;
    }

    const results = [];
    for (const competitor of competitors) {
      logger.info("Processing competitor:", competitor);

      if (!competitor.name || !competitor.address) {
        logger.warn("Skipping invalid competitor:", competitor);
        continue; // Skip invalid competitors
      }

      // Save using the service
      const result = await competitorService.saveCompetitor(
        locationId,
        competitor
      );
      logger.info("Competitor saved successfully:", result);
      results.push(result);
    }

    logger.info("All competitors processed:", { resultsCount: results.length });
    ctx.status = 201;
    ctx.body = results.length === 1 ? results[0] : results;
  } catch (error) {
    logger.error("Error adding competitor:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to add competitor",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /competitors/{competitorId}:
 *   get:
 *     summary: Get Competitor by ID
 *     description: Retrieves a specific competitor by its identifier
 *     tags: [Competitor]
 *     parameters:
 *       - in: path
 *         name: competitorId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Competitor identifier
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Competitor'
 *       404:
 *         description: Competitor not found
 */
router.get(
  "/:competitorId",
  locationRoleMiddleware("support"),
  async (ctx: Context) => {
    const locationId = ctx.state.location.id;
    const placeId = ctx.params.competitorId;

    try {
      // Get competitor from the service
      const competitor = await competitorService.getRetailerById(placeId);

      if (!competitor) {
        ctx.status = 404;
        ctx.body = { error: "Competitor not found" };
        return;
      }

      ctx.status = 200;
      ctx.body = competitor;
    } catch (error) {
      logger.error("Error getting competitor:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to get competitor",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /competitors/{competitorId}:
 *   patch:
 *     summary: Update Competitor
 *     description: Updates an existing competitor
 *     tags: [Competitor]
 *     parameters:
 *       - in: path
 *         name: competitorId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Competitor identifier
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CompetitorUpdateParams'
 *     responses:
 *       200:
 *         description: Competitor updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Competitor'
 *       404:
 *         description: Competitor not found
 */
router.patch(
  "/:competitorId",
  locationRoleMiddleware("admin"),
  async (ctx: Context) => {
    const locationId = ctx.state.location.id;
    const placeId = ctx.params.competitorId;
    const updates = ctx.request.body;

    try {
      // Update competitor using the service
      const updatedCompetitor = await competitorService.saveCompetitor(
        locationId,
        { ...updates, place_id: placeId }
      );

      if (!updatedCompetitor) {
        ctx.status = 404;
        ctx.body = { error: "Competitor not found" };
        return;
      }

      ctx.status = 200;
      ctx.body = updatedCompetitor;
    } catch (error) {
      logger.error("Error updating competitor:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to update competitor",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /competitors/{competitorId}:
 *   delete:
 *     summary: Delete Competitor
 *     description: Deletes a competitor
 *     tags: [Competitor]
 *     parameters:
 *       - in: path
 *         name: competitorId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Competitor identifier
 *     responses:
 *       204:
 *         description: Competitor deleted successfully
 *       404:
 *         description: Competitor not found
 */
router.delete(
  "/:competitorId",
  locationRoleMiddleware("admin"),
  async (ctx: Context) => {
    const locationId = ctx.state.location.id;
    const placeId = ctx.params.competitorId;

    try {
      // Remove using the service
      await competitorService.removeCompetitor(locationId, placeId);

      ctx.status = 204;
      ctx.body = { success: true };
    } catch (error) {
      logger.error("Error deleting competitor:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to delete competitor",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /competitors/price-comparison/{productId}:
 *   get:
 *     summary: Get Product Price Comparison
 *     description: Retrieves price comparison for a product across competitors.
 *     tags: [Competitor]
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: string
 *         description: Product ID
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       500:
 *         description: Failed to get price comparison
 */
router.get(
  "/price-comparison/:productId",
  locationRoleMiddleware("support"),
  async (ctx: Context) => {
    const locationId = ctx.state.location.id;
    const productId = ctx.params.productId;

    try {
      const comparison = await CompetitorAnalysisService.compareProductPricing(
        locationId,
        productId
      );

      ctx.status = 200;
      ctx.body = comparison;
    } catch (error) {
      logger.error("Error getting price comparison:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to get price comparison",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /competitors/nearby-retailers:
 *   get:
 *     summary: Search Nearby Retailers
 *     description: Searches for nearby retailers to add as competitors.
 *     tags: [Competitor]
 *     parameters:
 *       - in: query
 *         name: latitude
 *         required: true
 *         schema:
 *           type: string
 *         description: Latitude
 *       - in: query
 *         name: longitude
 *         required: true
 *         schema:
 *           type: string
 *         description: Longitude
 *       - in: query
 *         name: radius
 *         required: false
 *         schema:
 *           type: string
 *         description: Search radius in miles
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: string
 *         description: Page number
 *       - in: query
 *         name: pageSize
 *         required: false
 *         schema:
 *           type: string
 *         description: Page size
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       400:
 *         description: Invalid or missing parameters
 *       500:
 *         description: Failed to search nearby retailers
 */
router.get(
  "/nearby-retailers",
  locationRoleMiddleware("support"),
  async (ctx: Context) => {
    const {
      latitude,
      longitude,
      radius = "30",
      page = "1",
      pageSize = "10",
    } = ctx.query;
    const locationId = ctx.state.location.id;

    if (!latitude || !longitude) {
      ctx.status = 400;
      ctx.body = {
        error: "Latitude and longitude parameters are required",
      };
      return;
    }

    try {
      const lat = parseFloat(latitude as string);
      const lng = parseFloat(longitude as string);
      const radiusMiles = parseFloat(radius as string);
      const currentPage = parseInt(page as string, 10);
      const limit = parseInt(pageSize as string, 10);

      if (
        isNaN(lat) ||
        isNaN(lng) ||
        isNaN(radiusMiles) ||
        isNaN(currentPage) ||
        isNaN(limit)
      ) {
        ctx.status = 400;
        ctx.body = {
          error: "Invalid parameters provided",
        };
        return;
      }

      // Get nearby retailers using the service
      const result = await competitorService.searchNearbyRetailers(
        lat,
        lng,
        radiusMiles,
        limit * 2,
        currentPage,
        limit
      );

      ctx.status = 200;
      ctx.body = result;
    } catch (error) {
      logger.error("Error searching nearby retailers:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to search nearby retailers",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

// Search for retailers by name/query
router.get(
  "/search-retailers",
  locationRoleMiddleware("support"),
  async (ctx: Context) => {
    const { query, limit = "10" } = ctx.query;
    const locationId = ctx.state.location.id;

    if (!query || typeof query !== "string") {
      ctx.status = 400;
      ctx.body = {
        error: "Query parameter is required",
      };
      return;
    }

    try {
      const limitNum = parseInt(limit as string);
      const retailers = await competitorService.searchRetailers(
        query,
        limitNum
      );

      ctx.status = 200;
      ctx.body = { retailers };
    } catch (error) {
      logger.error("Error searching retailers:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to search retailers",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

// Sync competitor data from external sources
router.post("/sync", locationRoleMiddleware("admin"), async (ctx: Context) => {
  const locationId = ctx.state.location.id;

  try {
    const result = await CompetitorAnalysisService.syncCompetitorData(
      locationId
    );

    ctx.status = 200;
    ctx.body = result;
  } catch (error) {
    logger.error("Error syncing competitor data:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to sync competitor data",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

// Trigger live scraping for competitors
router.post(
  "/trigger-scrape",
  locationRoleMiddleware("admin"),
  async (ctx: Context) => {
    const locationId = ctx.state.location.id;
    const location = ctx.state.location;
    const { retailer_ids } = ctx.request.body;

    if (
      !retailer_ids ||
      !Array.isArray(retailer_ids) ||
      retailer_ids.length === 0
    ) {
      ctx.status = 400;
      ctx.body = {
        error: "At least one retailer ID is required",
      };
      return;
    }

    try {
      const scrapeUrl =
        "https://cannabis-marketing-chatbot-224bde0578da.herokuapp.com/api/v1/scrape-multiple-retailers?source=cannmenus";
      logger.info(
        `Triggering rescrape for ${retailer_ids.length} competitors in state ${location.state}`
      );

      let productCount = 0;
      let estimatedScrapeTimeMinutes = 5; // Default to 5 mins

      // Make request to scrape service
      const scrapeResponse = await fetch(scrapeUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          retailer_ids,
          states: [location.state],
        }),
      });

      if (!scrapeResponse.ok) {
        throw new Error(
          `Scrape service returned ${
            scrapeResponse.status
          }: ${await scrapeResponse.text()}`
        );
      }

      const scrapeResult = await scrapeResponse.json();

      logger.info(`Scrape initiated response: ${JSON.stringify(scrapeResult)}`);

      if (scrapeResult.count) {
        productCount = scrapeResult.count;
        // Estimate 10 seconds per product with a minimum of 5 minutes
        estimatedScrapeTimeMinutes = Math.max(
          5,
          Math.round((productCount * 10) / 60)
        );
      }

      ctx.status = 200;
      ctx.body = {
        success: true,
        message: "Scrape process initiated",
        status: "processing",
        productCount,
        estimatedScrapeTime: estimatedScrapeTimeMinutes,
        scrapedRetailerIds: retailer_ids,
      };
    } catch (error) {
      logger.error("Error triggering scrape for competitors:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to trigger scrape",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

// Search competitor data using vector search
router.post(
  "/search",
  locationRoleMiddleware("support"),
  async (ctx: Context) => {
    const locationId = ctx.state.location.id;
    const { query, filters = {}, limit = 10 } = ctx.request.body;

    if (!query) {
      ctx.status = 400;
      ctx.body = {
        error: "Search query is required",
      };
      return;
    }

    try {
      const results =
        await CompetitorDataVectorService.queryCompetitorDataSupabase(
          query,
          locationId,
          limit
        );

      ctx.status = 200;
      ctx.body = {
        results,
        query,
        filters,
        limit,
      };
    } catch (error) {
      logger.error("Error searching competitor data:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Error searching competitor data",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

// Add re-vectorization endpoint
router.post(
  "/re-vectorize",
  locationRoleMiddleware("admin"),
  async (ctx: Context) => {
    const locationId = ctx.state.location.id;
    const { clean_start = true } = ctx.request.body;

    try {
      // First ensure the index exists
      await CompetitorDataVectorService.ensureIndexExists();

      // If clean_start is true, delete all existing vectors for this location first
      if (clean_start) {
        logger.info(
          `Deleting existing vectors for location ${locationId} before re-vectorization`
        );
        await CompetitorDataVectorService.deleteCompetitorDataSupabase(
          locationId
        );
        logger.info(
          `Successfully deleted existing vectors for location ${locationId}`
        );
      }

      // Get all competitors for this location
      const competitors = await App.main
        .db("location_competitors")
        .where("location_id", locationId)
        .select("*");

      if (competitors.length === 0) {
        ctx.status = 200;
        ctx.body = {
          message: "No competitors found to vectorize",
          count: 0,
          clean_start,
        };
        return;
      }

      // Vectorize all competitors
      const result =
        await CompetitorDataVectorService.upsertCompetitorDataSupabase(
          competitors
        );

      logger.info(
        `Re-vectorization completed for ${result.successCount} competitors in location ${locationId}`
      );

      ctx.status = 200;
      ctx.body = {
        message: "Competitor data re-vectorization completed",
        status: "completed",
        success_count: result.successCount,
        error_count: result.errorCount,
        clean_start,
        total_competitors: competitors.length,
      };
    } catch (error) {
      logger.error(
        `Error re-vectorizing competitors for location ${locationId}:`,
        error
      );
      ctx.status = 500;
      ctx.body = {
        error: "Failed to re-vectorize competitor data",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /competitors/{competitorId}/analysis:
 *   get:
 *     summary: Get Competitor Analysis
 *     description: Retrieves analysis data for a specific competitor
 *     tags: [Competitor]
 *     parameters:
 *       - in: path
 *         name: competitorId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Competitor identifier
 *     responses:
 *       200:
 *         description: Analysis data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 analysis:
 *                   type: object
 *                   additionalProperties: true
 *       404:
 *         description: Competitor not found
 */
router.get("/:competitorId/analysis", async (ctx: Context) => {
  const locationId = ctx.state.location.id;
  const placeId = ctx.params.competitorId;

  try {
    // Get analysis data from the service
    const analysisService = new CompetitorAnalysisService();
    const analysis = await analysisService.getSpecificCompetitorInfo(
      locationId,
      placeId
    );

    if (!analysis) {
      ctx.status = 404;
      ctx.body = { error: "Competitor not found" };
      return;
    }

    ctx.status = 200;
    ctx.body = { analysis };
  } catch (error) {
    logger.error("Error getting competitor analysis:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to get competitor analysis",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

export default router;
