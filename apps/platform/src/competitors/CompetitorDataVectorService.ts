/* eslint-disable indent */
import { VectorService, VectorData } from "../core/VectorService";
import { logger } from "../config/logger";
import App from "../app";
import { SupabaseService } from "../supabase/SupabaseService";

const COMPETITOR_DATA_INDEX = "competitor-data";

interface CompetitorRecord {
  id: number;
  location_id: number;
  competitor_place_id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  distance_km: number;
  productCount?: number;
  specialCount?: number;
  [key: string]: any;
}

interface CompetitorData {
  id: number;
  location_id: number;
  competitor_place_id: string;
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  distance_km: number;
  product_count?: number;
  products?: any[];
  created_at?: Date;
  updated_at?: Date;
}

interface VectorResult {
  success: boolean;
  successCount: number;
  errorCount: number;
  errors: any[];
}

export class CompetitorDataVectorService {
  private static vectorService: VectorService | null = null;

  /**
   * Get the vector service instance
   */
  private static getVectorService(): VectorService {
    if (!this.vectorService) {
      this.vectorService = VectorService.getInstance();
    }
    return this.vectorService;
  }

  static async initialize() {
    await this.getVectorService().initialize();
  }

  /**
   * Converts a location ID to a namespace string
   */
  private static getNamespace(locationId: number): string {
    return `location-${locationId}`;
  }

  static async upsertCompetitorData(competitorData: CompetitorRecord[]) {
    try {
      if (!competitorData.length) {
        logger.warn(
          "No competitor data provided to upsert, skipping vectorization"
        );
        return { successCount: 0, errorCount: 0, failedIds: [] };
      }

      // Group records by location ID for namespace-based processing
      const recordsByLocation = competitorData.reduce((groups, record) => {
        const namespace = this.getNamespace(record.location_id);
        if (!groups[namespace]) {
          groups[namespace] = [];
        }
        groups[namespace].push(record);
        return groups;
      }, {} as Record<string, CompetitorRecord[]>);

      const totalResults = {
        successCount: 0,
        errorCount: 0,
        failedIds: [] as string[],
      };

      // Process each location group with its own namespace
      for (const [namespace, records] of Object.entries(recordsByLocation)) {
        // Convert competitor data to vector format with enhanced text representation
        const vectorData: VectorData[] = records.map((record) => ({
          id: `competitor_${record.id}`,
          text: this.generateEnhancedText(record),
          metadata: {
            location_id: record.location_id,
            source_type: "competitor",
            source_id: record.id.toString(),
            competitor_place_id: record.competitor_place_id,
            name: record.name,
            address: record.address,
            latitude: record.latitude,
            longitude: record.longitude,
            distance_km: record.distance_km,
            created_at: Date.now(),
            updated_at: Date.now(),
          },
        }));

        // Use centralized vector service to upsert data with namespace
        const result = await this.getVectorService().upsertVectors(
          COMPETITOR_DATA_INDEX,
          vectorData,
          undefined, // Use default batch size
          namespace
        );

        // Aggregate results
        totalResults.successCount += result.successCount;
        totalResults.errorCount += result.errorCount;
        totalResults.failedIds = totalResults.failedIds.concat(
          result.failedIds
        );

        logger.info(
          `Competitor data vectorization for namespace ${namespace} complete: ${result.successCount} succeeded, ${result.errorCount} failed`
        );
      }

      return totalResults;
    } catch (error) {
      logger.error("Error upserting competitor data vectors:", error);
      throw error;
    }
  }

  static async queryCompetitorData(
    query: string,
    locationId: number,
    topK: number = 10
  ) {
    try {
      console.log("queryCompetitorData", query);
      if (!query) {
        throw new Error(
          "Empty query provided to competitor data vector search"
        );
      }

      if (!locationId && typeof locationId !== "number") {
        logger.warn(
          "No location_id provided for competitor data query, this may cause errors"
        );
        // Return empty results when no location ID is specified
        return [];
      }

      // Get namespace for this location
      const namespace = this.getNamespace(locationId);

      // Add pre-processing for query to improve search results
      const enhancedQuery = this.enhanceSearchQuery(query);

      try {
        return await this.getVectorService().queryVectors(
          COMPETITOR_DATA_INDEX,
          enhancedQuery,
          {}, // No additional filter needed since we're using namespaces
          topK,
          namespace // Pass namespace for location-specific search
        );
      } catch (error) {
        // Enhanced error logging
        logger.error({
          message: "Error in competitor vector query operation",
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          query: enhancedQuery,
          namespace,
          index: COMPETITOR_DATA_INDEX,
        });

        // Handle 404 errors specifically
        if (error instanceof Error && error.message.includes("404")) {
          logger.warn(
            `Competitor index "${COMPETITOR_DATA_INDEX}" not found, attempting to create it`
          );
          await this.ensureIndexExists();

          // Return empty results instead of throwing
          return [];
        }

        throw error;
      }
    } catch (error) {
      logger.error("Error querying competitor data vectors:", error);
      throw error;
    }
  }

  /**
   * Creates an enhanced text representation of competitor data for better semantic matching
   */
  private static generateEnhancedText(record: CompetitorRecord): string {
    // Base text with core information
    let text = `Competitor: ${record.name}, located at ${record.address}. `;

    // Add distance if available
    if (record.distance_km) {
      text += `Located ${record.distance_km.toFixed(2)} km away. `;
    }

    // Add price information if available
    if (record.productCount) {
      text += `Carries approximately ${record.productCount} products. `;
    }

    // Add promotional information if available from competitor_prices
    if (record.specialCount && record.specialCount > 0) {
      text += `Currently has ${record.specialCount} items on special/promotion. `;
    }

    return text;
  }

  /**
   * Enhances search queries for better competitor data retrieval
   */
  private static enhanceSearchQuery(query: string): string {
    // Enhance basic queries with relevant terms
    let enhancedQuery = query;

    // Add terms for common competitor-related queries
    if (
      query.toLowerCase().includes("competitor") ||
      query.toLowerCase().includes("competition")
    ) {
      enhancedQuery += " nearby dispensary cannabis store";
    }

    // Add terms for pricing-related queries
    if (
      query.toLowerCase().includes("price") ||
      query.toLowerCase().includes("pricing") ||
      query.toLowerCase().includes("cheaper")
    ) {
      enhancedQuery += " pricing strategy cost comparison";
    }

    return enhancedQuery;
  }

  static async deleteCompetitorData(locationId: number) {
    try {
      if (!locationId) {
        logger.warn(
          "Attempted to delete competitor data without specifying location_id"
        );
        return false;
      }

      // Delete all vectors in this location's namespace
      const namespace = this.getNamespace(locationId);
      return await this.getVectorService().deleteNamespace(
        COMPETITOR_DATA_INDEX,
        namespace
      );
    } catch (error) {
      logger.error("Error deleting competitor data vectors:", error);
      throw error;
    }
  }

  /**
   * Helper method to get enriched competitor data including product info
   */
  static async getEnrichedCompetitorData(locationId: number) {
    try {
      // Get basic competitor info
      const competitors = await App.main
        .db("location_competitors")
        .where({ location_id: locationId });

      if (!competitors.length) {
        return [];
      }

      // For each competitor, get associated product data
      const enrichedCompetitors = await Promise.all(
        competitors.map(async (competitor: any) => {
          // Get count of products
          const productCount = await App.main
            .db("competitor_prices")
            .where({
              location_id: locationId,
              competitor_place_id: competitor.competitor_place_id,
            })
            .countDistinct("product_sku as count")
            .first();

          // Get count of specials
          const specialCount = await App.main
            .db("competitor_prices")
            .where({
              location_id: locationId,
              competitor_place_id: competitor.competitor_place_id,
              on_special: true,
            })
            .count("* as count")
            .first();

          return {
            ...competitor,
            productCount: productCount ? productCount.count : 0,
            specialCount: specialCount ? specialCount.count : 0,
          };
        })
      );

      return enrichedCompetitors;
    } catch (error) {
      logger.error("Error getting enriched competitor data:", error);
      throw error;
    }
  }

  /**
   * Validates that the Pinecone index exists and creates it if needed
   */
  static async ensureIndexExists() {
    try {
      const vectorService = this.getVectorService();
      await vectorService.initialize();

      // Attempt to create the index if it doesn't exist
      await vectorService.createIndex(COMPETITOR_DATA_INDEX, {
        dimension: 3072, // Updated dimension for text-embedding-3-large model (was 1536)
        metric: "cosine",
        serverless: {
          cloud: "aws", // Specify AWS instead of GCP
          region: "us-east-1", // Specify the us-east-1 region as required for free tier
        },
      });

      logger.info(
        `Competitor index '${COMPETITOR_DATA_INDEX}' verification complete`
      );
      return true;
    } catch (error) {
      logger.error(`Failed to verify competitor index: ${error}`);
      throw error;
    }
  }

  /**
   * Empties the entire competitor index, removing all vectors across all namespaces
   * Use with caution as this will delete ALL competitor data vectors
   */
  static async emptyIndex() {
    try {
      const vectorService = this.getVectorService();
      logger.warn(
        "Emptying entire competitor index - this will delete ALL competitor vectors for ALL locations"
      );
      return await vectorService.emptyIndex(COMPETITOR_DATA_INDEX);
    } catch (error) {
      logger.error("Error emptying competitor index:", error);
      throw error;
    }
  }

  /**
   * Gets vector statistics for a specific location namespace
   */
  static async getLocationStats(locationId: number) {
    try {
      const vectorService = this.getVectorService();
      const namespace = this.getNamespace(locationId);
      const stats = await vectorService.getIndexStats(COMPETITOR_DATA_INDEX);

      // Check if the namespace exists in the stats
      if (stats.namespaces && stats.namespaces[namespace]) {
        return {
          recordCount: stats.namespaces[namespace].recordCount || 0,
          vectorCount: stats.namespaces[namespace].recordCount || 0, // Backward compatibility
          namespace,
        };
      }

      return {
        recordCount: 0,
        vectorCount: 0, // Backward compatibility
        namespace,
      };
    } catch (error) {
      logger.error(`Error getting stats for location ${locationId}:`, error);
      return {
        recordCount: 0,
        vectorCount: 0, // Backward compatibility
        namespace: this.getNamespace(locationId),
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  private static getSupabaseService(): SupabaseService {
    return new SupabaseService({
      url: process.env.SUPABASE_URL || "",
      key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
      bucket: process.env.SUPABASE_BUCKET || "location-data",
    });
  }

  // Get enriched competitor data with product counts from Supabase
  static async getEnrichedCompetitorDataSupabase(
    locationId: number
  ): Promise<CompetitorData[]> {
    try {
      // Get all competitors for this location
      const competitors = await App.main
        .db("location_competitors")
        .where("location_id", locationId)
        .select("*");

      // Get supabase service
      const supabaseService = this.getSupabaseService();

      // Enrich with product counts from Supabase
      const enrichedCompetitors = await Promise.all(
        competitors.map(async (competitor) => {
          try {
            // Try to get product count from Supabase
            const products = await supabaseService.getRetailerProducts(
              competitor.competitor_place_id,
              5 // Limit to 5 products for preview
            );

            return {
              ...competitor,
              product_count: products ? products.total_count : 0,
              products: products || [],
            };
          } catch (error) {
            logger.error(
              `Error enriching competitor ${competitor.id} data:`,
              error
            );
            return {
              ...competitor,
              product_count: 0,
              products: [],
            };
          }
        })
      );

      return enrichedCompetitors;
    } catch (error) {
      logger.error("Error getting enriched competitor data:", error);
      throw error;
    }
  }

  // Remove competitor data from vector database
  static async deleteCompetitorDataSupabase(
    locationId: number
  ): Promise<boolean> {
    try {
      // Implement vector deletion if needed
      // This would remove vectors from your vector database
      logger.info(
        `Would delete competitor vector data for location ${locationId}`
      );
      return true;
    } catch (error) {
      logger.error("Error deleting competitor vectors:", error);
      throw error;
    }
  }

  // Insert or update competitor data in vector database
  static async upsertCompetitorDataSupabase(
    competitors: CompetitorData[]
  ): Promise<VectorResult> {
    try {
      // Get supabase service
      const supabaseService = this.getSupabaseService();

      const results: VectorResult = {
        success: true,
        successCount: 0,
        errorCount: 0,
        errors: [],
      };

      // Process each competitor
      for (const competitor of competitors) {
        try {
          // Get retailer details from Supabase
          const retailer = await supabaseService.getRetailerById(
            competitor.competitor_place_id
          );

          if (!retailer) {
            results.errorCount++;
            results.errors.push({
              competitorId: competitor.id,
              error: "Retailer not found in Supabase",
            });
            continue;
          }

          // Get products from Supabase
          const products = await supabaseService.getRetailerProducts(
            competitor.competitor_place_id
          );

          // Update competitor in the database with product count
          await App.main
            .db("location_competitors")
            .where("id", competitor.id)
            .update({
              name:
                retailer.name || retailer.dispensary_name || competitor.name,
              address:
                retailer.address ||
                retailer.physical_address ||
                competitor.address,
              product_count: products ? products.total_count : 0,
              updated_at: new Date(),
            });

          // Here you would implement vector storage
          // ...

          results.successCount++;
        } catch (error) {
          logger.error(
            `Error processing competitor ${competitor.id} for vectorization:`,
            error
          );
          results.errorCount++;
          results.errors.push({
            competitorId: competitor.id,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }

      results.success = results.errorCount === 0;
      return results;
    } catch (error) {
      logger.error("Error upserting competitor data to vectors:", error);
      throw error;
    }
  }

  // Query competitor data using vector search
  static async queryCompetitorDataSupabase(
    query: string,
    locationId: number,
    limit: number = 10
  ): Promise<any[]> {
    try {
      // Get supabase service
      const supabaseService = this.getSupabaseService();

      // Get all competitors for this location
      const competitors = await App.main
        .db("location_competitors")
        .where("location_id", locationId)
        .select("*");

      if (!competitors || competitors.length === 0) {
        logger.warn(`No competitors found for location ${locationId}`);
        return [];
      }

      // For each competitor, search for products matching the query
      const results = [];
      const processedQuery = query.toLowerCase().trim();

      // Check for specific types of queries
      const isPriceQuery = /price|cost|expensive|cheap/i.test(processedQuery);
      const isCategoryQuery = /category|flower|vape|edible|concentrate/i.test(
        processedQuery
      );
      const isBrandQuery = /brand|manufacturer|producer/i.test(processedQuery);

      // Extract potentially mentioned categories
      const categoryMatches = processedQuery.match(
        /(flower|vape|edible|concentrate|topical|tincture|pre-?roll)/i
      );
      const searchedCategory = categoryMatches
        ? categoryMatches[1].toLowerCase()
        : null;

      // Process each competitor in parallel for better performance
      const competitorPromises = competitors.map(async (competitor) => {
        try {
          // Get products from Supabase
          const response = await supabaseService.getRetailerProducts(
            competitor.competitor_place_id
          );

          if (
            !response ||
            !response.products ||
            response.products.length === 0
          ) {
            return null;
          }

          // Filter and process products based on query
          let matchingProducts = response.products;
          let matchReason = "general search";

          // Apply filters based on query type
          if (searchedCategory) {
            // Category filter
            matchingProducts = matchingProducts.filter((product: any) => {
              const productCategory = (product.category || "").toLowerCase();
              return productCategory.includes(searchedCategory);
            });
            matchReason = `category: ${searchedCategory}`;
          } else if (isPriceQuery) {
            // Sort by price for price queries
            matchingProducts = matchingProducts
              .filter((product: any) => product.latest_price)
              .sort((a: any, b: any) => {
                if (processedQuery.includes("expensive")) {
                  return (b.latest_price || 0) - (a.latest_price || 0);
                }
                return (a.latest_price || 0) - (b.latest_price || 0);
              });
            matchReason = processedQuery.includes("expensive")
              ? "highest priced products"
              : "lowest priced products";
          } else {
            // General keyword search
            matchingProducts = matchingProducts.filter((product: any) => {
              const searchFields = [
                product.product_name,
                product.brand_name,
                product.category,
                product.subcategory,
                product.product_description,
              ].filter(Boolean);

              const searchText = searchFields.join(" ").toLowerCase();
              return searchText.includes(processedQuery);
            });
          }

          // Return null if no matching products
          if (matchingProducts.length === 0) {
            return null;
          }

          // Format the result
          return {
            competitor: {
              id: competitor.id,
              name: competitor.name,
              address: competitor.address,
              place_id: competitor.competitor_place_id,
              distance_km: competitor.distance_km,
            },
            matching_products: matchingProducts.slice(0, 5), // Limit to 5 products per competitor
            total_matching_products: matchingProducts.length,
            match_reason: matchReason,
            total_products: response.total_count || 0,
          };
        } catch (error) {
          logger.error(
            `Error searching products for competitor ${competitor.id}:`,
            error
          );
          return null;
        }
      });

      // Wait for all competitors to be processed
      const competitorResults = await Promise.all(competitorPromises);

      // Filter out null results and limit to requested limit
      const validResults = competitorResults.filter(
        (result) => result !== null
      );

      // Add in competitor data summary
      const summary = {
        source_type: "competitor_summary",
        content: `Found ${validResults.length} competitors with matching products out of ${competitors.length} total competitors for location ${locationId}.`,
        total_competitors: competitors.length,
        competitors_with_matches: validResults.length,
        query_type: isPriceQuery
          ? "price"
          : isCategoryQuery
          ? "category"
          : isBrandQuery
          ? "brand"
          : "general",
        location_id: locationId,
      };

      // Return results with summary
      return [summary, ...validResults.slice(0, limit)];
    } catch (error) {
      logger.error("Error querying competitor data:", error);
      throw error;
    }
  }
}
