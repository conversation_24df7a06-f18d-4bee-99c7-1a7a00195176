import { logger } from "../config/logger";
import App from "../app";

/**
 * Service for managing product compliance and test results
 * This is a stub implementation that will be expanded in the future
 */
export class ProductComplianceService {
  /**
   * Check if a product is compliant based on test results
   */
  static async isProductCompliant(
    locationId: number,
    productId: string
  ): Promise<{
    compliant: boolean;
    status: string;
    lastVerified: Date | null;
    issues: string[];
  }> {
    try {
      // Check if we have a compliance status record for this product
      const status = await App.main
        .db("product_compliance_status")
        .where({
          location_id: locationId,
          product_id: productId,
        })
        .orderBy("last_verified_at", "desc")
        .first();

      if (status) {
        return {
          compliant: status.compliance_status === "compliant",
          status: status.compliance_status,
          lastVerified: new Date(status.last_verified_at),
          issues: status.compliance_issues || [],
        };
      }

      // If no status exists yet, return a default "pending" status
      return {
        compliant: false,
        status: "pending",
        lastVerified: null,
        issues: ["Product compliance status not yet determined"],
      };
    } catch (error) {
      logger.error("Error checking product compliance:", error);

      // In case of error, return a conservative "unknown" compliance status
      return {
        compliant: false,
        status: "unknown",
        lastVerified: null,
        issues: ["Error checking compliance status"],
      };
    }
  }

  /**
   * Get lab test results for a product
   */
  static async getProductTestResults(
    locationId: number,
    productId: string
  ): Promise<any[]> {
    try {
      // This table may not exist yet since it's part of the "coming soon" functionality
      // We'll add a try/catch to gracefully handle this
      try {
        const results = await App.main
          .db("product_test_results")
          .where({
            location_id: locationId,
            product_id: productId,
          })
          .orderBy("test_date", "desc");

        return results;
      } catch (dbError) {
        // If the table doesn't exist yet, return an empty array
        logger.warn("product_test_results table may not exist yet:", dbError);
        return [];
      }
    } catch (error) {
      logger.error("Error getting product test results:", error);
      return [];
    }
  }

  /**
   * Set the compliance status for a product
   * This would typically be called after analyzing test results
   */
  static async setProductComplianceStatus(
    locationId: number,
    productId: string,
    status: string,
    issues: string[] = [],
    verifiedBy: string = "system"
  ): Promise<boolean> {
    try {
      // This is a stub implementation - would actually update the database in production
      logger.info(
        `Would set compliance status for product ${productId} to ${status}`
      );

      return true;
    } catch (error) {
      logger.error("Error setting product compliance status:", error);
      return false;
    }
  }

  /**
   * Check if the compliance feature is available/enabled
   */
  static async isComplianceFeatureAvailable(
    locationId: number
  ): Promise<boolean> {
    // This is a stub that will always return false until the feature is fully implemented
    return false;
  }
}
