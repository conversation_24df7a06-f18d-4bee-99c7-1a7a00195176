import Router from "@koa/router";
import { koaSwagger } from "koa2-swagger-ui";
import { getSwaggerSpec } from "./swagger";

/**
 * @swagger
 * tags:
 *   name: API Documentation
 *   description: API documentation and Swagger UI endpoints
 */

const router = new Router();

/**
 * @swagger
 * /swagger.json:
 *   get:
 *     summary: Get the OpenAPI/Swagger specification
 *     tags: [API Documentation]
 *     description: Returns the complete OpenAPI specification in JSON format
 *     responses:
 *       200:
 *         description: OpenAPI specification
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               description: The complete OpenAPI specification
 */
router.get("/swagger.json", async (ctx) => {
  ctx.set("Content-Type", "application/json");
  ctx.body = getSwaggerSpec();
});

/**
 * @swagger
 * /api-docs:
 *   get:
 *     summary: Get the Swagger UI interface
 *     tags: [API Documentation]
 *     description: Returns the Swagger UI interface for exploring the API
 *     responses:
 *       200:
 *         description: Swagger UI HTML page
 *         content:
 *           text/html:
 *             schema:
 *               type: string
 *               description: The Swagger UI interface HTML
 */
router.get(
  "/api-docs",
  koaSwagger({
    routePrefix: false,
    swaggerOptions: {
      url: "/swagger.json",
    },
    title: "BakedBot API Documentation",
  })
);

export default router;
