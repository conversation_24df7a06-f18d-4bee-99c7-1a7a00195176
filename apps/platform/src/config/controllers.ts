import Router from "@koa/router";
import send from "koa-send";
import LocationController, {
  LocationSubrouter,
  locationMiddleware,
} from "../locations/LocationController";
import ClientController from "../client/ClientController";
import SegmentController from "../client/SegmentController";
import CampaignController from "../campaigns/CampaignController";
import ListController from "../lists/ListController";
import SubscriptionController, {
  publicRouter as PublicSubscriptionController,
} from "../subscriptions/SubscriptionController";
import JourneyController from "../journey/JourneyController";
import ImageController from "../storage/ImageController";
import AuthController from "../auth/AuthController";
import {
  adminRouter as AdminProviderController,
  publicRouter as PublicProviderController,
} from "../providers/ProviderController";
import LinkController from "../render/LinkController";
import TemplateController from "../render/TemplateController";
import UserController from "../users/UserController";
import ProfileController from "../profile/ProfileController";
import TagController from "../tags/TagController";
import { authMiddleware, scopeMiddleware } from "../auth/AuthMiddleware";
import LocationAdminController from "../locations/LocationAdminController";
import LocationApiKeyController from "../locations/LocationApiKeyController";
import LocationLocaleController from "../locations/LocationLocaleController";
import AdminController from "../auth/AdminController";
import OrganizationController from "../organizations/OrganizationController";
import App from "../app";
import { organizationMiddleware } from "../organizations/OrganizationMiddleware";
import ResourceController from "../render/ResourceController";
import PosDataController from "../pos/PosDataController";
import ProductController from "../products/ProductController";
import DashboardController from "../dashboard/DashboardController";
import MiscController from "../misc/MiscController";
import { setupChatRoutes } from "../chats/ChatController";
import DocumentController from "../documents/DocumentController";
import PosController from "../pos/PosController";
import PosDataAnalyticsController from "../pos/PosDataAnalyticsController";
import RetailerDataController from "../retailers/RetailerDataController";
import ReviewDataController from "../reviews/ReviewDataController";
import AgentController from "../agents/AgentController";
import systemRoutes from "../routes/system";
import InsightController from "../insights/InsightController";
import CoaDataController from "../coa/CoaDataController";
import { setupCustomPromptRoutes } from "../users/CustomPromptController";
import PublicProductController from "../products/PublicProductController";
import SharedAssetsController from "../shared/SharedAssetsController";
import CompetitorController from "../competitors/CompetitorController";
import { setupSwagger, getSwaggerSpec } from "./swagger";
import SwaggerController from "./SwaggerController";
import RetailerController from "../retailers/RetailerController";
import OrderController from "../orders/OrderController";
import PublicOrderController from "../orders/PublicOrderController";
import EventController from "../events/EventController";
import MenuSettingsController from "../content/MenuSettingsController";
import CustomerController from "../customers/CustomerController";

export const register = (parent: Router, ...routers: Router[]) => {
  for (const router of routers) {
    parent.use(router.routes(), router.allowedMethods());
  }
  return parent;
};

export type SubRouter = Router & { global?: boolean };

export default (app: App) => {
  const routers: Record<string, SubRouter> = {
    admin: adminRouter(app),
    client: clientRouter(app),
    public: publicRouter(app),
    swagger: SwaggerController as SubRouter,
  };

  // Create a root router for swagger.json
  const rootRouter = new Router() as SubRouter;
  rootRouter.get("/swagger.json", async (ctx) => {
    ctx.body = getSwaggerSpec();
  });
  rootRouter.global = true;
  routers.swagger = rootRouter;

  if (app.env.config.monoDocker) {
    const ui = new Router();
    ui.get("/(.*)", async (ctx, next) => {
      try {
        await send(ctx, "./public/index.html");
      } catch {
        return next();
      }
    });
    routers.ui = ui;
    routers.ui.global = true;
  }

  routers.swagger.global = true;
  return routers;
};

/**
 * Admin Router
 * All endpoints for use with admin UI control plane
 * @returns Router
 */
export const adminRouter = (app: App) => {
  const admin = new Router({ prefix: "/admin" });
  admin.use(organizationMiddleware);
  admin.use(authMiddleware);

  admin.use(scopeMiddleware(["admin", "secret"]));
  return register(
    admin,
    LocationController,
    locationRouter("/locations/:location"),
    ProfileController,
    AdminController,
    OrganizationController
  );
};

/**
 * Location Router
 * A subrouter to the admin router which wraps location specific endpoints
 * inside of a location scope
 * @returns Router
 */
export const locationRouter = (prefix?: string) => {
  const router = new Router({ prefix });
  router.use(locationMiddleware);
  return register(
    router,
    LocationSubrouter,
    CampaignController,
    setupChatRoutes(App.main),
    setupCustomPromptRoutes(App.main),
    ListController,
    AgentController,
    SubscriptionController,
    JourneyController,
    ImageController,
    TemplateController,
    AdminProviderController,
    LocationAdminController,
    LocationApiKeyController,
    LocationLocaleController,
    UserController,
    TagController,
    ResourceController,
    PosDataController,
    ProductController,
    DashboardController,
    DocumentController,
    PosController,
    PosDataAnalyticsController,
    RetailerDataController,
    ReviewDataController,
    InsightController,
    CompetitorController,
    CoaDataController,
    OrderController,
    EventController,
    MenuSettingsController
  );
};

/**
 * Client Router
 * All endpoints that can be accessed using client level authentication.
 * For use by third parties.
 * @returns Router
 */
export const clientRouter = (app: App) => {
  const router = new Router({ prefix: "/client" });
  router.use(authMiddleware);
  register(router, ClientController);
  register(router, SegmentController);

  router.use(scopeMiddleware("secret"));
  register(router, locationRouter());

  return router;
};

/**
 * Public Router
 * All endpoints that need to be accessed with absolutely no auth
 * at all. Primarily contains auth endpoints and unsubscribe endpoints.
 * @returns Router
 */
export const publicRouter = (app: App) => {
  const router = new Router();
  router.use(organizationMiddleware);

  router.get("/health", async (ctx) => {
    ctx.body = {
      status: "ok",
      environment: process.env.NODE_ENV,
      time: new Date(),
    };
  });

  // Debug route for swagger spec
  router.get("/debug-swagger", async (ctx) => {
    ctx.body = getSwaggerSpec();
  });

  return register(
    router,
    setupSwagger(app),
    AuthController,
    PublicSubscriptionController,
    PublicProviderController,
    LinkController,
    MiscController,
    systemRoutes,
    InsightController,
    PublicProductController,
    SharedAssetsController,
    RetailerController,
    OrderController,
    PublicOrderController,
    CustomerController
  );
};
