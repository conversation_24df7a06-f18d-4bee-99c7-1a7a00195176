import knex, { Knex as Database } from "knex";
import path from "path";
import { remove<PERSON><PERSON>, sleep } from "../utilities";
import { logger } from "./logger";

export { Database };

export interface DatabaseConfig {
  client: string;
  host?: string;
  port?: number;
  user?: string;
  password?: string;
  database?: string;
  filename?: string;
  migrationPaths: string[];
}

export type Query = (
  builder: Database.QueryBuilder<any>
) => Database.QueryBuilder<any>;

const MIGRATION_RETRIES = 3;

knex.QueryBuilder.extend(
  "when",
  function (condition: boolean, fnif: Query, fnelse?: Query) {
    return condition ? fnif(this) : fnelse ? fnelse(this) : this;
  }
);

const connect = (config: DatabaseConfig, withDB = true) => {
  // Handle SQLite configuration
  if (config.client === "sqlite3") {
    return knex({
      client: config.client,
      connection: {
        filename: config.filename || "./database.sqlite",
      },
      useNullAsDefault: true,
      asyncStackTraces: true,
    });
  }

  // Handle MySQL configuration
  let connection: any = {
    host: config.host,
    port: config.port,
    user: config.user,
    password: config.password,
  };

  if (withDB) {
    connection.database = config.database;
  }

  return knex({
    client: config.client,
    connection: {
      ...connection,
      typeCast(field: any, next: any) {
        if (field.type === "TINY" && field.length === 1) {
          return field.string() === "1";
        }
        return next();
      },
    },
    pool: {
      afterCreate: (conn: any, done: any) => {
        // Disable ONLY_FULL_GROUP_BY to allow flexible GROUP BY queries
        conn.query(
          'SET SESSION sql_mode=(SELECT REPLACE(@@sql_mode,"ONLY_FULL_GROUP_BY",""));',
          (err: any) => {
            if (err) {
              logger.error("Error setting SQL mode:", err);
            } else {
              logger.info(
                "ONLY_FULL_GROUP_BY SQL mode disabled for this connection"
              );
            }
            done(err, conn);
          }
        );
      },
    },
    asyncStackTraces: true,
  });
};

const migrate = async (
  config: DatabaseConfig,
  db: Database,
  retries = MIGRATION_RETRIES
): Promise<void> => {
  try {
    return await db.migrate.latest({
      directory: [
        path.resolve(__dirname, "../../db/migrations"),
        ...config.migrationPaths,
      ],
      tableName: "migrations",
      loadExtensions: [".js", ".ts"],
    });
  } catch (error: any) {
    if (error?.name === "MigrationLocked" && retries > 0) {
      --retries;
      await sleep((MIGRATION_RETRIES - retries) * 1000);
      return await migrate(config, db, retries);
    }
    throw error;
  }
};

const createDatabase = async (config: DatabaseConfig, db: Database) => {
  try {
    await db.raw(`CREATE DATABASE ${config.database}`);
  } catch (error: any) {
    if (error.errno !== 1007) throw error;
  }
};

export default async (config: DatabaseConfig) => {
  // Attempt to connect & migrate
  try {
    const db = connect(config);
    await migrate(config, db);
    return db;
  } catch (error: any) {
    // Check if error is related to DB not existing
    if (error?.errno === 1049) {
      // Connect without database and create it
      let db = connect(config, false);
      await createDatabase(config, db);

      // Reconnect using new database
      db = connect(config);
      await migrate(config, db);
      return db;
    } else {
      logger.error(error, "database error");
      throw error;
    }
  }
};
