import Queue from "../queue";
import EmailJob from "../providers/email/EmailJob";
import EventPostJob from "../client/EventPostJob";
import TextJob from "../providers/text/TextJob";
import UserDeleteJob from "../users/UserDeleteJob";
import UserPatchJob from "../users/UserPatchJob";
import WebhookJob from "../providers/webhook/WebhookJob";
import { QueueConfig } from "../queue/Queue";
import JourneyDelayJob from "../journey/JourneyDelayJob";
import JourneyProcessJob from "../journey/JourneyProcessJob";
import ListPopulateJob from "../lists/ListPopulateJob";
import ListStatsJob from "../lists/ListStatsJob";
import ProcessListsJob from "../lists/ProcessListsJob";
import ProcessCampaignsJob from "../campaigns/ProcessCampaignsJob";
import CampaignEnqueueSendJob from "../campaigns/CampaignEnqueueSendsJob";
import CampaignStateJob from "../campaigns/CampaignStateJob";
import CampaignGenerateListJob from "../campaigns/CampaignGenerateListJob";
import CampaignInteractJob from "../campaigns/CampaignInteractJob";
import CampaignTriggerSendJob from "../campaigns/CampaignTriggerSendJob";
import PushJob from "../providers/push/PushJob";
import UserAliasJob from "../users/UserAliasJob";
import UserSchemaSyncJob from "../schema/UserSchemaSyncJob";
import UserDeviceJob from "../users/UserDeviceJob";
import JourneyStatsJob from "../journey/JourneyStatsJob";
import UpdateJourneysJob from "../journey/UpdateJourneysJob";
import ScheduledEntranceJob from "../journey/ScheduledEntranceJob";
import ScheduledEntranceOrchestratorJob from "../journey/ScheduledEntranceOrchestratorJob";
import ProductPatchJob from "../products/ProductPatchJob";
import ProductDeleteJob from "../products/ProductDeleteJob";
import DocumentAnalysisJob from "../documents/DocumentAnalysisJob";
import PosDataVectorJob from "../pos/PosDataVectorJob";
import UserDataVectorJob from "../users/UserDataVectorJob";
import ProductDataVectorJob from "../products/ProductDataVectorJob";
import ReviewDataVectorJob from "../reviews/ReviewDataVectorJob";
import RetailerDataVectorJob from "../retailers/RetailerDataVectorJob";
import AIEnhancementJob from "../products/AIEnhancementJob";
import DocumentVectorJob from "../documents/DocumentVectorJob";
export const jobs = [
  AIEnhancementJob,
  CampaignGenerateListJob,
  CampaignEnqueueSendJob,
  CampaignInteractJob,
  ProductDataVectorJob,
  CampaignStateJob,
  CampaignTriggerSendJob,
  DocumentAnalysisJob,
  EmailJob,
  EventPostJob,
  JourneyDelayJob,
  JourneyProcessJob,
  JourneyStatsJob,
  ListPopulateJob,
  ListStatsJob,
  ProcessListsJob,
  ProcessCampaignsJob,
  ProductPatchJob,
  ProductDeleteJob,
  PushJob,
  ScheduledEntranceJob,
  ScheduledEntranceOrchestratorJob,
  TextJob,
  UpdateJourneysJob,
  UserAliasJob,
  UserDeleteJob,
  UserDeviceJob,
  UserPatchJob,
  UserSchemaSyncJob,
  WebhookJob,
  PosDataVectorJob,
  UserDataVectorJob,
  ReviewDataVectorJob,
  RetailerDataVectorJob,
  DocumentVectorJob,
];

export const loadJobs = (queue: Queue) => {
  for (const job of jobs) {
    queue.register(job);
  }
};

export default (config: QueueConfig) => {
  return new Queue(config);
};
