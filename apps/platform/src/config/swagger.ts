import { koaSwagger } from "koa2-swagger-ui";
import swagger<PERSON>SD<PERSON> from "swagger-jsdoc";
import Router from "@koa/router";
import App from "../app";
import path from "path";
import fs from "fs";

// Swagger definition
const swaggerDefinition = {
  openapi: "3.0.0",
  info: {
    title: "BakedBot API",
    version: "1.0.0",
    description: "API documentation for BakedBot platform",
  },
  servers: [
    {
      url: "/api",
      description: "API Server",
    },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        description: "JWT token for authenticated users (admin/staff access)",
      },
      ApiKeyAuth: {
        type: "apiKey",
        in: "header",
        name: "Authorization",
        description:
          "Location API key in the format 'Bearer YOUR_API_KEY'. Required for all public endpoints.",
      },
      UserAuth: {
        type: "apiKey",
        in: "header",
        name: "x-user-token",
        description:
          "Optional user authentication token for enhanced features like chat history persistence. Use alongside Api<PERSON>eyAuth for full functionality.",
      },
    },
    schemas: {
      DualAuthExample: {
        type: "object",
        description:
          "Example showing how to use both API key and user authentication together",
        properties: {
          headers: {
            type: "object",
            properties: {
              Authorization: {
                type: "string",
                example: "Bearer your_location_api_key_here",
                description: "Required: Location API key",
              },
              "x-user-token": {
                type: "string",
                example: "user_jwt_token_here",
                description:
                  "Optional: User authentication token for enhanced features",
              },
            },
          },
        },
      },
    },
  },
  security: [
    {
      bearerAuth: [],
    },
  ],
};

// Options for the swagger docs
const options = {
  swaggerDefinition,
  apis: [
    path.join(__dirname, "../**/*Controller.ts"),
    path.join(__dirname, "../**/*Controller_new.ts"),
    path.join(__dirname, "../routes/**/*.ts"),
  ],
};

// Initialize swagger-jsdoc for development
const swaggerSpec = swaggerJSDoc(options);

// Setup Swagger UI routes
export const setupSwagger = (app: App) => {
  const router = new Router();
  const isProd = process.env.NODE_ENV === "production";

  // Serve swagger spec as JSON
  router.get("/swagger.json", async (ctx) => {
    if (isProd) {
      // In production, serve the static file
      const staticSpecPath = path.join(__dirname, "../../public/swagger.json");
      try {
        const staticSpec = JSON.parse(fs.readFileSync(staticSpecPath, "utf8"));
        ctx.body = staticSpec;
      } catch (error) {
        console.error("Error reading swagger.json:", error);
        ctx.body = swaggerSpec; // Fallback to dynamic spec
      }
    } else {
      // In development, use dynamic spec
      ctx.body = swaggerSpec;
    }
  });

  // Setup the Swagger UI route
  router.get(
    "/api-docs",
    koaSwagger({
      routePrefix: false,
      swaggerOptions: {
        url: "/api/swagger.json",
      },
      title: "BakedBot API Documentation",
    })
  );

  return router;
};

// Expose the swagger spec
export const getSwaggerSpec = () => swaggerSpec;
