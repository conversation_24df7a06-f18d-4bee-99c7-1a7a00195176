import { logger } from "../config/logger";

/**
 * Configuration for field cleaning and default value handling
 */
export interface FieldCleaningConfig {
  /** Field name */
  field: string;
  /** Default value to use when field is empty/null */
  defaultValue: any;
  /** Whether this field is required (will use default if empty) */
  required?: boolean;
  /** Custom validation function */
  validator?: (value: any) => boolean;
  /** Custom transformation function */
  transformer?: (value: any) => any;
  /** Whether to allow empty strings (default: false) */
  allowEmpty?: boolean;
}

/**
 * Predefined cleaning configurations for common fields
 */
export const DEFAULT_FIELD_CONFIGS: FieldCleaningConfig[] = [
  // User fields
  {
    field: "first_name",
    defaultValue: "Unknown",
    required: true,
    transformer: (value: any) => typeof value === "string" ? value.trim() : String(value || "").trim(),
  },
  {
    field: "last_name", 
    defaultValue: "User",
    required: true,
    transformer: (value: any) => typeof value === "string" ? value.trim() : String(value || "").trim(),
  },
  {
    field: "full_name",
    defaultValue: "Unknown User",
    required: true,
    transformer: (value: any) => typeof value === "string" ? value.trim() : String(value || "").trim(),
  },
  {
    field: "name",
    defaultValue: "Unknown",
    required: true,
    transformer: (value: any) => typeof value === "string" ? value.trim() : String(value || "").trim(),
  },
  {
    field: "email",
    defaultValue: "", // Empty string for email as null might cause issues
    required: false,
    validator: (value: any) => {
      if (!value) return true; // Allow empty
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value);
    },
    transformer: (value: any) => typeof value === "string" ? value.trim().toLowerCase() : "",
  },
  {
    field: "phone",
    defaultValue: "",
    required: false,
    transformer: (value: any) => {
      if (!value) return "";
      const phone = String(value).trim();
      // Add + prefix if missing and looks like a phone number
      if (phone && !phone.includes("+") && /^\d+$/.test(phone)) {
        return `+${phone}`;
      }
      return phone;
    },
  },
  {
    field: "address",
    defaultValue: "Unknown Address",
    required: false,
    transformer: (value: any) => typeof value === "string" ? value.trim() : String(value || "").trim(),
  },
  {
    field: "city",
    defaultValue: "Unknown City",
    required: false,
    transformer: (value: any) => typeof value === "string" ? value.trim() : String(value || "").trim(),
  },
  {
    field: "state",
    defaultValue: "Unknown State",
    required: false,
    transformer: (value: any) => typeof value === "string" ? value.trim().toUpperCase() : String(value || "").trim(),
  },
  {
    field: "zip_code",
    defaultValue: "00000",
    required: false,
    transformer: (value: any) => String(value || "").trim(),
  },
  {
    field: "country",
    defaultValue: "US",
    required: false,
    transformer: (value: any) => typeof value === "string" ? value.trim().toUpperCase() : "US",
  },
  // Product fields
  {
    field: "product_name",
    defaultValue: "Unknown Product",
    required: true,
    transformer: (value: any) => typeof value === "string" ? value.trim() : String(value || "").trim(),
  },
  {
    field: "brand",
    defaultValue: "Unknown Brand",
    required: false,
    transformer: (value: any) => typeof value === "string" ? value.trim() : String(value || "").trim(),
  },
  {
    field: "category",
    defaultValue: "Uncategorized",
    required: false,
    transformer: (value: any) => typeof value === "string" ? value.trim() : String(value || "").trim(),
  },
  {
    field: "description",
    defaultValue: "",
    required: false,
    allowEmpty: true,
    transformer: (value: any) => typeof value === "string" ? value.trim() : String(value || "").trim(),
  },
  // Retailer fields
  {
    field: "retailer_name",
    defaultValue: "Unknown Retailer",
    required: true,
    transformer: (value: any) => typeof value === "string" ? value.trim() : String(value || "").trim(),
  },
  // Numeric fields
  {
    field: "price",
    defaultValue: 0,
    required: false,
    validator: (value: any) => !isNaN(Number(value)),
    transformer: (value: any) => {
      const num = Number(value);
      return isNaN(num) ? 0 : num;
    },
  },
  {
    field: "quantity",
    defaultValue: 0,
    required: false,
    validator: (value: any) => !isNaN(Number(value)),
    transformer: (value: any) => {
      const num = Number(value);
      return isNaN(num) ? 0 : num;
    },
  },
  // Date fields
  {
    field: "created_at",
    defaultValue: null,
    required: false,
    transformer: (value: any) => {
      if (!value) return null;
      try {
        const date = new Date(value);
        return isNaN(date.getTime()) ? null : date;
      } catch {
        return null;
      }
    },
  },
  {
    field: "updated_at",
    defaultValue: null,
    required: false,
    transformer: (value: any) => {
      if (!value) return null;
      try {
        const date = new Date(value);
        return isNaN(date.getTime()) ? null : date;
      } catch {
        return null;
      }
    },
  },
];

/**
 * Enhanced data cleaning service that handles empty fields with intelligent defaults
 */
export class DataCleaningService {
  private static configs: Map<string, FieldCleaningConfig> = new Map();

  static {
    // Initialize with default configurations
    DEFAULT_FIELD_CONFIGS.forEach(config => {
      this.configs.set(config.field, config);
    });
  }

  /**
   * Add or update a field cleaning configuration
   */
  static addFieldConfig(config: FieldCleaningConfig): void {
    this.configs.set(config.field, config);
  }

  /**
   * Add multiple field configurations
   */
  static addFieldConfigs(configs: FieldCleaningConfig[]): void {
    configs.forEach(config => this.addFieldConfig(config));
  }

  /**
   * Check if a value is considered empty
   */
  static isEmpty(value: any): boolean {
    return (
      value === null ||
      value === undefined ||
      value === "" ||
      value === "NULL" ||
      value === "null" ||
      value === "undefined" ||
      (typeof value === "string" && value.trim() === "")
    );
  }

  /**
   * Clean a single field value using the configured rules
   */
  static cleanField(fieldName: string, value: any): any {
    const config = this.configs.get(fieldName);
    
    // If no config exists, use basic cleaning
    if (!config) {
      return this.basicClean(value, fieldName);
    }

    // Check if value is empty
    const isEmpty = this.isEmpty(value);
    
    // If empty and we have a default, use it
    if (isEmpty) {
      if (config.required || config.defaultValue !== undefined) {
        logger.debug(`Using default value for empty field '${fieldName}': ${config.defaultValue}`);
        return config.defaultValue;
      }
      // If not required and no default, return appropriate empty value
      return config.allowEmpty ? "" : null;
    }

    // Apply transformer if provided
    let cleanedValue = value;
    if (config.transformer) {
      try {
        cleanedValue = config.transformer(value);
      } catch (error) {
        logger.warn(`Error transforming field '${fieldName}':`, error);
        cleanedValue = config.defaultValue;
      }
    }

    // Validate if validator provided
    if (config.validator && !config.validator(cleanedValue)) {
      logger.warn(`Validation failed for field '${fieldName}', using default value`);
      return config.defaultValue;
    }

    return cleanedValue;
  }

  /**
   * Basic cleaning for fields without specific configuration
   */
  private static basicClean(value: any, fieldName: string): any {
    if (this.isEmpty(value)) {
      // Return appropriate default based on field name patterns
      if (fieldName.includes("name") || fieldName.includes("title")) {
        return "Unknown";
      }
      if (fieldName.includes("email")) {
        return "";
      }
      if (fieldName.includes("phone")) {
        return "";
      }
      if (fieldName.includes("price") || fieldName.includes("amount") || fieldName.includes("quantity")) {
        return 0;
      }
      if (fieldName.includes("_at") || fieldName.includes("date")) {
        return null;
      }
      // Default to empty string for most fields
      return "";
    }

    // Basic transformations
    if (typeof value === "string") {
      const trimmed = value.trim();
      
      // Handle boolean strings
      if (trimmed.toLowerCase() === "false") return false;
      if (trimmed.toLowerCase() === "true") return true;
      
      // Handle phone numbers
      if (fieldName === "phone" && !trimmed.includes("+") && /^\d+$/.test(trimmed)) {
        return `+${trimmed}`;
      }
      
      // Handle dates
      if (fieldName.includes("_at") || fieldName.includes("date")) {
        try {
          const date = new Date(trimmed);
          return isNaN(date.getTime()) ? null : date;
        } catch {
          return null;
        }
      }
      
      return trimmed;
    }

    return value;
  }

  /**
   * Clean an entire row/object using configured rules
   */
  static cleanRow(row: Record<string, any>): Record<string, any> {
    const cleaned: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(row)) {
      cleaned[key] = this.cleanField(key, value);
    }
    
    return cleaned;
  }

  /**
   * Clean multiple rows
   */
  static cleanRows(rows: Record<string, any>[]): Record<string, any>[] {
    return rows.map(row => this.cleanRow(row));
  }

  /**
   * Get statistics about cleaning operations
   */
  static getCleaningStats(originalRows: Record<string, any>[], cleanedRows: Record<string, any>[]): {
    totalRows: number;
    fieldsWithDefaults: Record<string, number>;
    emptyFieldsFound: Record<string, number>;
  } {
    const stats = {
      totalRows: originalRows.length,
      fieldsWithDefaults: {} as Record<string, number>,
      emptyFieldsFound: {} as Record<string, number>,
    };

    originalRows.forEach((originalRow, index) => {
      const cleanedRow = cleanedRows[index];
      
      for (const [key, originalValue] of Object.entries(originalRow)) {
        const cleanedValue = cleanedRow[key];
        
        if (this.isEmpty(originalValue)) {
          stats.emptyFieldsFound[key] = (stats.emptyFieldsFound[key] || 0) + 1;
          
          if (cleanedValue !== null && cleanedValue !== undefined && cleanedValue !== "") {
            stats.fieldsWithDefaults[key] = (stats.fieldsWithDefaults[key] || 0) + 1;
          }
        }
      }
    });

    return stats;
  }
}
