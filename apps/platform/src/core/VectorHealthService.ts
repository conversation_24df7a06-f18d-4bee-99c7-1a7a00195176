import { VectorService } from "./VectorService";
import { logger } from "../config/logger";
import { ProductDataVectorService } from "../products/ProductDataVectorService";
import { ReviewDataVectorService } from "../reviews/ReviewDataVectorService";
import { UserDataVectorService } from "../users/UserDataVectorService";
import { PosDataVectorService } from "../pos/PosDataVectorService";
import { CompetitorDataVectorService } from "../competitors/CompetitorDataVectorService";
import { RetailerDataVectorService } from "../retailers/RetailerDataVectorService";

export interface VectorServiceStatus {
  name: string;
  status: "healthy" | "warning" | "error";
  latency: number;
  message?: string;
  indexExists?: boolean;
  recordCount?: number;
}

export interface VectorHealthResponse {
  overallStatus: "healthy" | "warning" | "error";
  services: VectorServiceStatus[];
  lastChecked: Date;
}

export class VectorHealthService {
  private static lastCheck: VectorHealthResponse | null = null;
  private static checkInProgress = false;
  private static vectorService: VectorService | null = null;

  /**
   * Get the vector service instance
   */
  private static getVectorService(): VectorService {
    if (!this.vectorService) {
      this.vectorService = VectorService.getInstance();
    }
    return this.vectorService;
  }

  /**
   * Checks the health of all vector services
   */
  static async checkHealth(): Promise<VectorHealthResponse> {
    // If we already have a check in progress, don't start another one
    if (this.checkInProgress) {
      return (
        this.lastCheck || {
          overallStatus: "warning" as const,
          services: [],
          lastChecked: new Date(),
        }
      );
    }

    this.checkInProgress = true;

    try {
      // Initialize the vector service
      await this.getVectorService().initialize();

      // Get a list of all indices
      const indices = await this.getVectorService().listIndices();

      // Check all vector services
      const services: VectorServiceStatus[] = await Promise.all([
        this.checkServiceHealth(
          "Product Vectors",
          "product-embeddings",
          indices,
          ProductDataVectorService
        ),
        this.checkServiceHealth(
          "Review Vectors",
          "review-embeddings",
          indices,
          ReviewDataVectorService
        ),
        this.checkServiceHealth(
          "User Vectors",
          "user-embeddings",
          indices,
          UserDataVectorService
        ),
        this.checkServiceHealth(
          "POS Data Vectors",
          "pos-data",
          indices,
          PosDataVectorService
        ),
        this.checkServiceHealth(
          "Competitor Vectors",
          "competitor-data",
          indices,
          CompetitorDataVectorService
        ),
        this.checkServiceHealth(
          "Retailer Vectors",
          "retailer-data",
          indices,
          RetailerDataVectorService
        ),
      ]);

      // Determine overall status
      let overallStatus: "healthy" | "warning" | "error" = "healthy";

      if (services.some((s) => s.status === "error")) {
        overallStatus = "error";
      } else if (services.some((s) => s.status === "warning")) {
        overallStatus = "warning";
      }

      const result: VectorHealthResponse = {
        overallStatus,
        services,
        lastChecked: new Date(),
      };

      this.lastCheck = result;
      return result;
    } catch (error) {
      logger.error("Error checking vector service health", error);

      const result: VectorHealthResponse = {
        overallStatus: "error",
        services: [
          {
            name: "Vector Database",
            status: "error",
            latency: 0,
            message: error instanceof Error ? error.message : String(error),
          },
        ],
        lastChecked: new Date(),
      };

      this.lastCheck = result;
      return result;
    } finally {
      this.checkInProgress = false;
    }
  }

  /**
   * Checks the health of a specific vector service
   */
  private static async checkServiceHealth(
    name: string,
    indexName: string,
    indices: string[],
    serviceClass: any
  ): Promise<VectorServiceStatus> {
    const startTime = Date.now();

    try {
      // Check if the index exists
      const indexExists = indices.includes(indexName);

      if (!indexExists) {
        return {
          name,
          status: "warning",
          latency: Date.now() - startTime,
          message: "Index does not exist",
          indexExists: false,
        };
      }

      // Try to ensure the index exists (most services have this method)
      if (typeof serviceClass.ensureIndexExists === "function") {
        await serviceClass.ensureIndexExists();
      }

      // Try a basic query to test latency
      const testQuery = "test";
      const filters = { location_id: 0 };

      try {
        await this.getVectorService().queryVectors(indexName, testQuery, filters, 1);

        return {
          name,
          status: "healthy",
          latency: Date.now() - startTime,
          indexExists: true,
        };
      } catch (error) {
        // If the query fails but the index exists, it's a warning
        return {
          name,
          status: "warning",
          latency: Date.now() - startTime,
          message: error instanceof Error ? error.message : String(error),
          indexExists: true,
        };
      }
    } catch (error) {
      // Any other errors are treated as service issues
      return {
        name,
        status: "error",
        latency: Date.now() - startTime,
        message: error instanceof Error ? error.message : String(error),
        indexExists: indices.includes(indexName),
      };
    }
  }

  /**
   * Get cached health status or check if expired
   */
  static async getHealth(forceRefresh = false): Promise<VectorHealthResponse> {
    // If we have a cached result and it's less than 5 minutes old, use it
    if (
      !forceRefresh &&
      this.lastCheck &&
      Date.now() - this.lastCheck.lastChecked.getTime() < 5 * 60 * 1000
    ) {
      return this.lastCheck;
    }

    // Otherwise check health
    return this.checkHealth();
  }
}
