/* eslint-disable indent */
import { Pinecone } from "@pinecone-database/pinecone";
import { OpenAIEmbeddings } from "@langchain/openai";
import { logger } from "../config/logger";
import crypto from "crypto";

export interface VectorMetadata {
  location_id: number;
  source_type: string;
  source_id: string;
  created_at: number;
  updated_at: number;
  [key: string]: any;
}

export interface VectorData {
  id: string;
  text: string;
  metadata: VectorMetadata;
}

export interface VectorQueryResult {
  id: string;
  score: number;
  metadata: VectorMetadata;
}

export interface ValidationConfig {
  maxTextLength: number;
  maxMetadataSize: number;
  allowedMetadataTypes: Set<string>;
}

export interface ResourceLimits {
  maxVectorsPerRequest: number;
  maxConcurrentOperations: number;
  maxIndexSize: number;
  maxNamespacesPerIndex: number;
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  resetTimeout: number;
  halfOpenTimeout: number;
}

export interface RateLimitConfig {
  maxRequestsPerSecond: number;
  maxConcurrentRequests: number;
  burstLimit: number;
}

export interface CacheConfig {
  enabled: boolean;
  ttl: number;
  maxSize: number;
  invalidationStrategy: "time" | "lru" | "both";
}

export interface MaintenanceConfig {
  cleanupInterval: number;
  maxAge: number;
  compactionSchedule: string; // cron expression
  metricsRetention: number;
}

export interface ObservabilityConfig {
  metricsEnabled: boolean;
  tracingEnabled: boolean;
  logLevel: "debug" | "info" | "warn" | "error";
  samplingRate: number;
}

export interface VectorServiceConfig {
  batchSize?: number;
  maxRetries?: number;
  retryDelay?: number;
  connectionTimeout?: number;
  operationTimeout?: number;
  validation?: ValidationConfig;
  resources?: ResourceLimits;
  circuitBreaker?: CircuitBreakerConfig;
  rateLimit?: RateLimitConfig;
  cache?: CacheConfig;
  maintenance?: MaintenanceConfig;
  observability?: ObservabilityConfig;
}

export interface UpsertResult {
  successCount: number;
  errorCount: number;
  failedIds: string[];
  metrics: OperationMetrics;
}

export interface OperationMetrics {
  startTime: number;
  endTime: number;
  duration: number;
  embeddingTime?: number;
  vectorDbTime?: number;
  cacheHit?: boolean;
  retryCount?: number;
  errorType?: string;
  namespace?: string;
}

export interface IndexConfig {
  dimension: number;
  metric?: "cosine" | "euclidean" | "dotproduct";
  pods?: number;
  replicas?: number;
  serverless?: {
    cloud: "aws" | "gcp" | "azure";
    region: string;
  };
}

export interface Transaction {
  id: string;
  operations: Array<{
    type: "upsert" | "delete";
    data: any;
    timestamp: number;
  }>;
  status: "pending" | "committed" | "rolled_back";
  startTime: number;
  endTime?: number;
}

export interface HealthStatus {
  isHealthy: boolean;
  details: {
    initialized: boolean;
    activeConnections: number;
    lastOperationTime?: number;
    circuitBreakerStatus: "closed" | "open" | "half-open";
    currentLoad: number;
    errors?: string[];
    namespaceCount?: number;
    vectorCount?: number;
  };
}

export interface TelemetryData {
  operationType: "upsert" | "query" | "delete";
  indexName: string;
  namespace?: string;
  duration: number;
  vectorCount: number;
  status: "success" | "error";
  errorType?: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

// Default configurations
const DEFAULT_VALIDATION_CONFIG: Required<ValidationConfig> = {
  maxTextLength: 10000,
  maxMetadataSize: 1024 * 100, // 100KB
  allowedMetadataTypes: new Set(["string", "number", "boolean"]),
};

const DEFAULT_RESOURCE_LIMITS: Required<ResourceLimits> = {
  maxVectorsPerRequest: 1000,
  maxConcurrentOperations: 50,
  maxIndexSize: 1000000,
  maxNamespacesPerIndex: 100,
};

const DEFAULT_CIRCUIT_BREAKER_CONFIG: Required<CircuitBreakerConfig> = {
  failureThreshold: 5,
  resetTimeout: 60000,
  halfOpenTimeout: 30000,
};

const DEFAULT_RATE_LIMIT_CONFIG: Required<RateLimitConfig> = {
  maxRequestsPerSecond: 100,
  maxConcurrentRequests: 20,
  burstLimit: 150,
};

const DEFAULT_CACHE_CONFIG: Required<CacheConfig> = {
  enabled: true,
  ttl: 3600000, // 1 hour
  maxSize: 1000,
  invalidationStrategy: "both",
};

const DEFAULT_MAINTENANCE_CONFIG: Required<MaintenanceConfig> = {
  cleanupInterval: 3600000, // 1 hour
  maxAge: 2592000000, // 30 days
  compactionSchedule: "0 0 * * *", // Daily at midnight
  metricsRetention: 604800000, // 7 days
};

const DEFAULT_OBSERVABILITY_CONFIG: Required<ObservabilityConfig> = {
  metricsEnabled: true,
  tracingEnabled: true,
  logLevel: "info",
  samplingRate: 0.1,
};

export class VectorService {
  private static instance: VectorService;
  private client!: Pinecone;
  private embeddings: OpenAIEmbeddings;
  private initialized = false;
  private config: Required<VectorServiceConfig>;
  private activeConnections: Set<string> = new Set();
  private metrics: Map<string, OperationMetrics[]> = new Map();
  private transactions: Map<string, Transaction> = new Map();
  private queryCache: Map<string, { result: any; timestamp: number }> =
    new Map();

  private circuitState: Map<
    string,
    {
      failures: number;
      lastFailure: number;
      status: "closed" | "open" | "half-open";
    }
  > = new Map();

  private rateLimiter: {
    lastRequestTime: number;
    requestCount: number;
    concurrentRequests: number;
    tokenBucket: number;
  } = {
    lastRequestTime: 0,
    requestCount: 0,
    concurrentRequests: 0,
    tokenBucket: DEFAULT_RATE_LIMIT_CONFIG.burstLimit,
  };

  private maintenanceInterval?: NodeJS.Timeout;
  private telemetryEvents: TelemetryData[] = [];

  private static DEFAULT_CONFIG: Required<VectorServiceConfig> = {
    batchSize: 100,
    maxRetries: 3,
    retryDelay: 1000,
    connectionTimeout: 30000,
    operationTimeout: 60000,
    validation: DEFAULT_VALIDATION_CONFIG,
    resources: DEFAULT_RESOURCE_LIMITS,
    circuitBreaker: DEFAULT_CIRCUIT_BREAKER_CONFIG,
    rateLimit: DEFAULT_RATE_LIMIT_CONFIG,
    cache: DEFAULT_CACHE_CONFIG,
    maintenance: DEFAULT_MAINTENANCE_CONFIG,
    observability: DEFAULT_OBSERVABILITY_CONFIG,
  };

  private constructor(config: VectorServiceConfig = {}) {
    this.config = {
      ...VectorService.DEFAULT_CONFIG,
      ...config,
      validation: { ...DEFAULT_VALIDATION_CONFIG, ...config.validation },
      resources: { ...DEFAULT_RESOURCE_LIMITS, ...config.resources },
      circuitBreaker: {
        ...DEFAULT_CIRCUIT_BREAKER_CONFIG,
        ...config.circuitBreaker,
      },
      rateLimit: { ...DEFAULT_RATE_LIMIT_CONFIG, ...config.rateLimit },
      cache: { ...DEFAULT_CACHE_CONFIG, ...config.cache },
      maintenance: { ...DEFAULT_MAINTENANCE_CONFIG, ...config.maintenance },
      observability: {
        ...DEFAULT_OBSERVABILITY_CONFIG,
        ...config.observability,
      },
    };

    // Check if the OpenAI API key is set
    if (!process.env.OPENAI_API_KEY) {
      logger.error("OPENAI_API_KEY environment variable is not set");
      throw new Error("OPENAI_API_KEY environment variable is not set");
    }

    try {
      // Log a masked version of the API key for debugging
      const maskedApiKey = process.env.OPENAI_API_KEY
        ? `${process.env.OPENAI_API_KEY.substring(
            0,
            4
          )}...${process.env.OPENAI_API_KEY.substring(
            process.env.OPENAI_API_KEY.length - 4
          )}`
        : "not set";

      logger.info(
        `Initializing OpenAI embeddings with API key: ${maskedApiKey}`
      );

      this.embeddings = new OpenAIEmbeddings({
        openAIApiKey: process.env.OPENAI_API_KEY,
        modelName: "text-embedding-3-large",
      });

      logger.info(
        "VectorService initialized with text-embedding-3-large model (3072 dimensions)"
      );
    } catch (error) {
      logger.error({
        message: "Failed to initialize OpenAI embeddings",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw error;
    }
  }

  static getInstance(config?: VectorServiceConfig): VectorService {
    if (!VectorService.instance) {
      VectorService.instance = new VectorService(config);
    }
    return VectorService.instance;
  }

  private startOperation(): OperationMetrics {
    const startTime = Date.now();
    return {
      startTime,
      endTime: 0,
      duration: 0,
    };
  }

  private endOperation(metrics: OperationMetrics): OperationMetrics {
    metrics.endTime = Date.now();
    metrics.duration = metrics.endTime - metrics.startTime;
    return metrics;
  }

  private trackMetrics(indexName: string, metrics: OperationMetrics) {
    if (!this.metrics.has(indexName)) {
      this.metrics.set(indexName, []);
    }
    this.metrics.get(indexName)?.push(metrics);

    // Keep only last 1000 metrics per index
    const indexMetrics = this.metrics.get(indexName);
    if (indexMetrics && indexMetrics.length > 1000) {
      this.metrics.set(indexName, indexMetrics.slice(-1000));
    }
  }

  async initialize() {
    if (this.initialized) return;

    if (!process.env.PINECONE_API_KEY) {
      logger.error("PINECONE_API_KEY environment variable is not set");
      throw new Error("PINECONE_API_KEY environment variable is not set");
    }

    try {
      // Log a masked version of the API key for debugging
      const maskedPineconeKey = process.env.PINECONE_API_KEY
        ? `${process.env.PINECONE_API_KEY.substring(
            0,
            4
          )}...${process.env.PINECONE_API_KEY.substring(
            process.env.PINECONE_API_KEY.length - 4
          )}`
        : "not set";

      logger.info(`Initializing Pinecone with API key: ${maskedPineconeKey}`);

      this.client = new Pinecone({
        apiKey: process.env.PINECONE_API_KEY,
      });

      this.initialized = true;
      logger.info("VectorService initialized successfully");

      // Test the connection by listing indices
      try {
        const indices = await this.client.listIndexes();
        logger.info(
          `Successfully connected to Pinecone. Available indices: ${
            indices.indexes?.map((i) => i.name).join(", ") || "none"
          }`
        );
      } catch (listError) {
        logger.warn({
          message: "Connected to Pinecone but failed to list indices",
          error:
            listError instanceof Error ? listError.message : String(listError),
        });
      }
    } catch (error) {
      logger.error({
        message: "Failed to initialize VectorService with Pinecone",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });
      throw new Error(
        "Failed to initialize VectorService: " +
          (error instanceof Error ? error.message : String(error))
      );
    }
  }

  async shutdown(): Promise<void> {
    try {
      // Wait for active operations to complete
      if (this.activeConnections.size > 0) {
        logger.info(
          `Waiting for ${this.activeConnections.size} active connections to complete...`
        );
        await new Promise((resolve) => setTimeout(resolve, 5000)); // Wait up to 5 seconds
      }

      // Force close any remaining connections
      this.activeConnections.clear();
      this.initialized = false;
      logger.info("VectorService shut down successfully");
    } catch (error) {
      logger.error("Error during VectorService shutdown:", error);
      throw error;
    }
  }

  async createIndex(indexName: string, config: IndexConfig): Promise<void> {
    if (!this.initialized) {
      throw new Error("VectorService not initialized");
    }

    try {
      const existingIndexes = await this.client.listIndexes();
      if (existingIndexes.indexes?.find((index) => index.name === indexName)) {
        logger.warn(`Index ${indexName} already exists`);
        return;
      }

      // Add support for both pod-based and serverless configurations
      if (config.serverless) {
        await this.client.createIndex({
          name: indexName,
          dimension: config.dimension,
          metric: config.metric || "cosine",
          spec: {
            serverless: config.serverless,
          },
        });
      } else {
        await this.client.createIndex({
          name: indexName,
          dimension: config.dimension,
          metric: config.metric || "cosine",
          spec: {
            pod: {
              environment: process.env.PINECONE_ENVIRONMENT || "gcp-starter",
              podType: "p1.x1",
              pods: config.pods || 1,
              replicas: config.replicas || 1,
            },
          },
        });
      }

      logger.info(`Created index ${indexName}`);
    } catch (error) {
      logger.error(`Error creating index ${indexName}:`, error);
      throw error;
    }
  }

  async deleteIndex(indexName: string): Promise<void> {
    if (!this.initialized) {
      throw new Error("VectorService not initialized");
    }

    try {
      await this.client.deleteIndex(indexName);
      logger.info(`Deleted index ${indexName}`);
    } catch (error) {
      logger.error(`Error deleting index ${indexName}:`, error);
      throw error;
    }
  }

  async listIndices(): Promise<string[]> {
    if (!this.initialized) {
      throw new Error("VectorService not initialized");
    }

    try {
      const indices = await this.client.listIndexes();
      return indices.indexes?.map((index) => index.name) || [];
    } catch (error) {
      logger.error("Error listing indices:", error);
      throw error;
    }
  }

  getMetrics(indexName: string): OperationMetrics[] {
    return this.metrics.get(indexName) || [];
  }

  /**
   * Get the Pinecone client instance
   * @returns The initialized Pinecone client
   */
  getPineconeClient(): Pinecone {
    if (!this.initialized) {
      throw new Error(
        "VectorService not initialized. Call initialize() first."
      );
    }
    return this.client;
  }

  private async retry<T>(
    operation: () => Promise<T>,
    retries = this.config.maxRetries
  ): Promise<T> {
    try {
      return await operation();
    } catch (error) {
      if (retries > 0) {
        logger.warn(
          `Operation failed, retrying... (${retries} attempts remaining)`
        );
        await new Promise((resolve) =>
          setTimeout(resolve, this.config.retryDelay)
        );
        return this.retry(operation, retries - 1);
      }
      throw error;
    }
  }

  private async withTimeout<T>(
    operation: () => Promise<T>,
    timeout: number
  ): Promise<T> {
    const timeoutPromise = new Promise<never>((resolve, reject) =>
      setTimeout(() => reject(new Error("Operation timed out")), timeout)
    );
    return Promise.race([operation(), timeoutPromise]);
  }

  private getIndex(indexName: string) {
    if (!this.initialized) {
      throw new Error(
        "VectorService not initialized. Call initialize() first."
      );
    }
    return this.client.Index(indexName);
  }

  async upsertVectors(
    indexName: string,
    data: VectorData[],
    batchSize = this.config.batchSize,
    namespace?: string
  ): Promise<UpsertResult> {
    const index = this.getIndex(indexName);
    let successCount = 0;
    let errorCount = 0;
    const failedIds: string[] = [];
    const metrics = this.startOperation();

    try {
      // Process in batches
      for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize);
        const batchMetrics = this.startOperation();

        try {
          await this.withTimeout(async () => {
            await this.retry(async () => {
              try {
                const embeddingStart = Date.now();
                const texts = batch.map((item) => item.text);

                logger.debug(
                  `Creating embeddings for batch of ${
                    texts.length
                  } items, batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(
                    data.length / batchSize
                  )}`
                );

                const embeddings = await this.embeddings.embedDocuments(texts);
                batchMetrics.embeddingTime = Date.now() - embeddingStart;

                logger.debug(
                  `Successfully created ${embeddings.length} embeddings`
                );

                const vectorRecords = batch.map((item, idx) => ({
                  id: item.id,
                  values: embeddings[idx],
                  metadata: item.metadata,
                }));

                const vectorDbStart = Date.now();

                const namespaceStr = namespace
                  ? ` in namespace ${namespace}`
                  : "";
                logger.debug(
                  `Upserting ${vectorRecords.length} vectors to index ${indexName}${namespaceStr}`
                );

                // Using Pinecone v5.x API format
                if (namespace) {
                  // Use namespace API
                  await index.namespace(namespace).upsert(vectorRecords);
                } else {
                  // Default namespace
                  await index.upsert(vectorRecords);
                }

                batchMetrics.vectorDbTime = Date.now() - vectorDbStart;

                logger.debug(
                  `Successfully upserted vectors to index ${indexName}${namespaceStr}`
                );
              } catch (embeddingError) {
                logger.error({
                  message: "Error in embedding or upsert operation",
                  error:
                    embeddingError instanceof Error
                      ? embeddingError.message
                      : String(embeddingError),
                  stack:
                    embeddingError instanceof Error
                      ? embeddingError.stack
                      : undefined,
                  indexName,
                  batchSize: batch.length,
                  batchNumber: Math.floor(i / batchSize) + 1,
                });
                throw embeddingError; // rethrow to trigger retry mechanism
              }
            });
          }, this.config.operationTimeout);

          successCount += batch.length;
          logger.debug(
            `Processed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(
              data.length / batchSize
            )}`
          );
        } catch (error) {
          logger.error({
            message: `Error processing batch starting at index ${i}`,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
            indexName,
            batchNumber: Math.floor(i / batchSize) + 1,
            totalBatches: Math.ceil(data.length / batchSize),
          });
          errorCount += batch.length;
          failedIds.push(...batch.map((item) => item.id));
        } finally {
          this.trackMetrics(indexName, this.endOperation(batchMetrics));
        }
      }
    } finally {
      this.endOperation(metrics);
      this.trackMetrics(indexName, metrics);
    }

    return { successCount, errorCount, failedIds, metrics };
  }

  /**
   * Queries vectors from an index
   */
  async queryVectors(
    indexName: string,
    query: string,
    filters: Record<string, any> = {},
    topK: number = 10,
    namespace?: string
  ): Promise<VectorQueryResult[]> {
    const index = this.getIndex(indexName);
    const metrics = this.startOperation();

    try {
      // Skip if empty query
      if (!query) {
        logger.debug("Empty query provided, returning empty results");
        return [];
      }

      const embeddingStart = Date.now();
      // Generate embeddings for the query
      const embedding = await this.embeddings.embedQuery(query);
      metrics.embeddingTime = Date.now() - embeddingStart;

      const vectorDbStart = Date.now();

      // First try query with namespace and filters (if provided)
      try {
        logger.debug(
          `Querying ${indexName} with ${query}, filters: ${JSON.stringify(
            filters
          )}, namespace: ${namespace || "default"}`
        );

        // Create query parameters
        const queryParams = {
          vector: embedding,
          filter: Object.keys(filters).length > 0 ? filters : undefined,
          topK,
          includeMetadata: true,
        };

        // Use the namespace chaining syntax for Pinecone v5.x
        const results = await this.withTimeout(
          async () =>
            this.retry(async () => {
              try {
                // Pinecone v5.x API: Use namespace method chain
                if (namespace) {
                  return await index.namespace(namespace).query(queryParams);
                } else {
                  // Query without namespace (default namespace)
                  return await index.query(queryParams);
                }
              } catch (error: any) {
                logger.error({
                  message: "Error in vector database query operation",
                  index: indexName,
                  filter: JSON.stringify(filters),
                  errorMessage:
                    error instanceof Error ? error.message : String(error),
                  errorStack: error instanceof Error ? error.stack : undefined,
                  errorName: error.constructor
                    ? error.constructor.name
                    : "Unknown Error",
                });
                throw error;
              }
            }),
          this.config.operationTimeout
        );

        // Format and return results
        metrics.vectorDbTime = Date.now() - vectorDbStart;
        return (results.matches || []).map((match) => ({
          id: match.id,
          score: typeof match.score === "number" ? match.score : 0,
          metadata: match.metadata as VectorMetadata,
        }));
      } catch (error: any) {
        // Log the error
        logger.error({
          message: "Error in vector query operation",
          stack: error instanceof Error ? error.stack : undefined,
          query,
          namespace,
          filters,
          index: indexName,
          error: error instanceof Error ? error.message : String(error),
        });

        throw new Error(
          `Failed to query vectors from index '${indexName}': ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    } catch (error: any) {
      logger.error({
        message: "Error querying vectors",
        index: indexName,
        filter: JSON.stringify(filters),
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        errorName: error.constructor ? error.constructor.name : "Unknown Error",
      });
      throw error;
    } finally {
      this.endOperation(metrics);
      this.trackMetrics(indexName, metrics);
    }
  }

  async deleteVectors(
    indexName: string,
    filter: Record<string, any>
  ): Promise<boolean> {
    const index = this.getIndex(indexName);
    const metrics = this.startOperation();

    try {
      const vectorDbStart = Date.now();
      await this.withTimeout(
        async () => this.retry(async () => index.deleteMany({ filter })),
        this.config.operationTimeout
      );
      metrics.vectorDbTime = Date.now() - vectorDbStart;
      return true;
    } catch (error) {
      logger.error("Error deleting vectors:", error);
      throw new Error(
        "Failed to delete vectors: " +
          (error instanceof Error ? error.message : String(error))
      );
    } finally {
      this.endOperation(metrics);
      this.trackMetrics(indexName, metrics);
    }
  }

  private validateVectorData(data: VectorData): void {
    const { validation } = this.config;

    if (data.text.length > validation.maxTextLength) {
      throw new Error(
        `Text exceeds maximum length of ${validation.maxTextLength}`
      );
    }

    const metadataSize = JSON.stringify(data.metadata).length;
    if (metadataSize > validation.maxMetadataSize) {
      throw new Error(
        `Metadata exceeds maximum size of ${validation.maxMetadataSize}`
      );
    }

    // Validate metadata types
    Object.entries(data.metadata).forEach(([key, value]) => {
      const type = typeof value;
      if (!validation.allowedMetadataTypes.has(type)) {
        throw new Error(`Invalid metadata type for key ${key}: ${type}`);
      }
    });
  }

  private async checkResourceLimits(
    indexName: string,
    vectorCount: number
  ): Promise<void> {
    const { resources } = this.config;

    if (vectorCount > resources.maxVectorsPerRequest) {
      throw new Error(
        `Request exceeds maximum vectors per request: ${resources.maxVectorsPerRequest}`
      );
    }

    if (this.activeConnections.size >= resources.maxConcurrentOperations) {
      throw new Error(
        `Maximum concurrent operations reached: ${resources.maxConcurrentOperations}`
      );
    }
  }

  private async enforceRateLimit(): Promise<void> {
    const { rateLimit } = this.config;
    const now = Date.now();
    const timeSinceLastRequest = now - this.rateLimiter.lastRequestTime;

    // Replenish token bucket
    if (timeSinceLastRequest > 0) {
      const newTokens = Math.floor(
        timeSinceLastRequest * (rateLimit.maxRequestsPerSecond / 1000)
      );
      this.rateLimiter.tokenBucket = Math.min(
        rateLimit.burstLimit,
        this.rateLimiter.tokenBucket + newTokens
      );
      this.rateLimiter.lastRequestTime = now;
    }

    if (this.rateLimiter.tokenBucket < 1) {
      throw new Error("Rate limit exceeded");
    }

    this.rateLimiter.tokenBucket--;
  }

  private async checkCircuitBreaker(operation: string): Promise<void> {
    const { circuitBreaker } = this.config;
    const state = this.circuitState.get(operation) || {
      failures: 0,
      lastFailure: 0,
      status: "closed" as const,
    };

    switch (state.status) {
      case "open":
        if (this.now() - state.lastFailure > circuitBreaker.resetTimeout) {
          state.status = "half-open";
        } else {
          throw new Error(`Circuit breaker is open for ${operation}`);
        }
        break;

      case "half-open":
        if (this.now() - state.lastFailure > circuitBreaker.halfOpenTimeout) {
          state.status = "closed";
          state.failures = 0;
        }
        break;
    }

    this.circuitState.set(operation, state);
  }

  private getCacheKey(
    indexName: string,
    operation: string,
    params: any
  ): string {
    return `${indexName}:${operation}:${JSON.stringify(params)}`;
  }

  private async withCache<T>(
    indexName: string,
    operation: string,
    params: any,
    fetchFn: () => Promise<T>
  ): Promise<T> {
    const { cache } = this.config;
    if (!cache.enabled) {
      return fetchFn();
    }

    const cacheKey = this.getCacheKey(indexName, operation, params);
    const cached = this.queryCache.get(cacheKey);

    if (cached && this.now() - cached.timestamp < cache.ttl) {
      return cached.result as T;
    }

    const result = await fetchFn();
    this.queryCache.set(cacheKey, { result, timestamp: this.now() });

    // Implement cache size management
    if (this.queryCache.size > cache.maxSize) {
      const oldestKey = Array.from(this.queryCache.entries()).sort(
        ([, a], [, b]) => a.timestamp - b.timestamp
      )[0][0];
      this.queryCache.delete(oldestKey);
    }

    return result;
  }

  private async trackTelemetry(data: TelemetryData): Promise<void> {
    this.telemetryEvents.push(data);

    // Ship telemetry if we've accumulated enough events
    if (this.telemetryEvents.length >= 100) {
      await this.shipTelemetry();
    }
  }

  private async shipTelemetry(): Promise<void> {
    if (this.telemetryEvents.length === 0) return;

    try {
      const events = [...this.telemetryEvents];
      this.telemetryEvents = [];

      // TODO: Implement actual telemetry shipping logic
      logger.debug(`Shipping ${events.length} telemetry events`);
    } catch (error) {
      logger.error("Failed to ship telemetry:", error);
    }
  }

  private async withObservability<T>(
    operation: () => Promise<T>,
    context: {
      operationType: string;
      indexName: string;
      metadata?: Record<string, any>;
    }
  ): Promise<T> {
    const { observability } = this.config;
    const startTime = this.now();
    const traceId = crypto.randomUUID();

    try {
      if (observability.tracingEnabled) {
        logger.debug("Operation started", { traceId, ...context });
      }

      const result = await operation();

      if (observability.metricsEnabled) {
        await this.trackTelemetry({
          operationType: context.operationType as any,
          indexName: context.indexName,
          duration: this.now() - startTime,
          vectorCount: 0, // Set appropriate value
          status: "success",
          timestamp: this.now(),
          metadata: context.metadata,
        });
      }

      return result;
    } catch (error) {
      if (observability.metricsEnabled) {
        await this.trackTelemetry({
          operationType: context.operationType as any,
          indexName: context.indexName,
          duration: this.now() - startTime,
          vectorCount: 0,
          status: "error",
          errorType: error instanceof Error ? error.name : "Unknown",
          timestamp: this.now(),
          metadata: context.metadata,
        });
      }
      throw error;
    }
  }

  private now(): number {
    return Date.now();
  }

  /**
   * Deletes all vectors in a specific namespace
   */
  async deleteNamespace(
    indexName: string,
    namespace: string
  ): Promise<boolean> {
    const index = this.getIndex(indexName);
    const metrics = this.startOperation();

    try {
      const vectorDbStart = Date.now();

      logger.debug(
        `Deleting all vectors in namespace ${namespace} from index ${indexName}`
      );

      // Delete all vectors in the specified namespace using Pinecone v5.x API
      await this.withTimeout(
        async () =>
          this.retry(async () => {
            try {
              // Pinecone v5.x API for deleteAll with namespace
              await index.namespace(namespace).deleteAll();
            } catch (error) {
              // Check if the error is a 404 (namespace not found)
              // This is actually OK since it means the namespace doesn't exist (already deleted or never existed)
              const errorStr = String(error);
              if (errorStr.includes("HTTP status 404")) {
                logger.info(
                  `Namespace ${namespace} not found in index ${indexName}, considering it already deleted`
                );
                return; // This is not actually an error, so we can continue
              }

              // For other errors, rethrow
              logger.error(`Error deleting namespace: ${error}`);
              throw error;
            }
          }),
        this.config.operationTimeout
      );

      metrics.vectorDbTime = Date.now() - vectorDbStart;
      logger.info(
        `Successfully deleted all vectors in namespace ${namespace} from index ${indexName}`
      );

      return true;
    } catch (error) {
      logger.error(
        `Error deleting namespace ${namespace} from index ${indexName}:`,
        error
      );
      throw new Error(
        `Failed to delete namespace: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      this.endOperation(metrics);
      this.trackMetrics(indexName, metrics);
    }
  }

  /**
   * Gets statistics about an index, including vector counts per namespace
   */
  async getIndexStats(indexName: string) {
    const index = this.getIndex(indexName);
    const metrics = this.startOperation();

    try {
      const vectorDbStart = Date.now();

      logger.debug(`Fetching statistics for index ${indexName}`);

      const stats = await this.withTimeout(
        async () =>
          this.retry(async () => {
            return await index.describeIndexStats();
          }),
        this.config.operationTimeout
      );

      metrics.vectorDbTime = Date.now() - vectorDbStart;

      return {
        dimension: stats.dimension,
        indexFullness: stats.indexFullness,
        namespaces: stats.namespaces || {},
        totalVectorCount: stats.totalRecordCount,
      };
    } catch (error) {
      logger.error(`Error getting index stats for ${indexName}:`, error);
      throw new Error(
        `Failed to get index stats: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      this.endOperation(metrics);
      this.trackMetrics(indexName, metrics);
    }
  }

  /**
   * Empties an index by deleting all vectors from it
   * This will delete all vectors across all namespaces
   */
  async emptyIndex(indexName: string): Promise<boolean> {
    const index = this.getIndex(indexName);
    const metrics = this.startOperation();

    try {
      const vectorDbStart = Date.now();

      logger.info(
        `Emptying index ${indexName} (deleting all vectors in all namespaces)`
      );

      // First get stats to find all namespaces
      const stats = await this.getIndexStats(indexName);
      const namespaces = Object.keys(stats.namespaces || {});

      if (namespaces.length === 0) {
        logger.info(`Index ${indexName} is already empty, no namespaces found`);
        return true;
      }

      // Delete all vectors in each namespace
      logger.info(`Deleting vectors from ${namespaces.length} namespaces`);

      for (const namespace of namespaces) {
        logger.debug(`Deleting all vectors in namespace ${namespace}`);
        await this.withTimeout(
          async () =>
            this.retry(async () => {
              try {
                await index.namespace(namespace).deleteAll();
              } catch (error) {
                // Check if the error is a 404 (namespace not found)
                // This is OK since it means the namespace doesn't exist (already deleted or never existed)
                const errorStr = String(error);
                if (errorStr.includes("HTTP status 404")) {
                  logger.info(
                    `Namespace ${namespace} not found in index ${indexName}, considering it already deleted`
                  );
                  return; // This is not actually an error, so we can continue
                }

                // For other errors, rethrow
                logger.error(`Error deleting namespace ${namespace}: ${error}`);
                throw error;
              }
            }),
          this.config.operationTimeout
        );
      }

      // Also delete any vectors in the default namespace (empty string)
      logger.debug(`Deleting all vectors in default namespace`);
      await this.withTimeout(
        async () =>
          this.retry(async () => {
            try {
              await index.namespace("").deleteAll();
            } catch (error) {
              // Check if the error is a 404 (namespace not found)
              // This is OK since it means the namespace doesn't exist (already deleted or never existed)
              const errorStr = String(error);
              if (errorStr.includes("HTTP status 404")) {
                logger.info(
                  `Default namespace not found in index ${indexName}, considering it already deleted`
                );
                return; // This is not actually an error, so we can continue
              }

              // For other errors, rethrow
              logger.error(`Error deleting default namespace: ${error}`);
              throw error;
            }
          }),
        this.config.operationTimeout
      );

      metrics.vectorDbTime = Date.now() - vectorDbStart;
      logger.info(
        `Successfully emptied index ${indexName}, deleted vectors from ${
          namespaces.length + 1
        } namespaces`
      );

      return true;
    } catch (error) {
      logger.error(`Error emptying index ${indexName}:`, error);
      throw new Error(
        `Failed to empty index: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      this.endOperation(metrics);
      this.trackMetrics(indexName, metrics);
    }
  }
}
