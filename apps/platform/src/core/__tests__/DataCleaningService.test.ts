import { DataCleaningService, FieldCleaningConfig, DEFAULT_FIELD_CONFIGS } from "../DataCleaningService";

describe("DataCleaningService", () => {
  beforeEach(() => {
    // Reset to default configurations before each test
    (DataCleaningService as any).configs.clear();
    DEFAULT_FIELD_CONFIGS.forEach(config => {
      (DataCleaningService as any).configs.set(config.field, config);
    });
  });

  describe("isEmpty", () => {
    it("should identify empty values correctly", () => {
      expect(DataCleaningService.isEmpty(null)).toBe(true);
      expect(DataCleaningService.isEmpty(undefined)).toBe(true);
      expect(DataCleaningService.isEmpty("")).toBe(true);
      expect(DataCleaningService.isEmpty("   ")).toBe(true);
      expect(DataCleaningService.isEmpty("NULL")).toBe(true);
      expect(DataCleaningService.isEmpty("null")).toBe(true);
      expect(DataCleaningService.isEmpty("undefined")).toBe(true);
      
      expect(DataCleaningService.isEmpty("test")).toBe(false);
      expect(DataCleaningService.isEmpty(0)).toBe(false);
      expect(DataCleaningService.isEmpty(false)).toBe(false);
    });
  });

  describe("cleanField", () => {
    it("should use default values for empty fields", () => {
      expect(DataCleaningService.cleanField("first_name", "")).toBe("Unknown");
      expect(DataCleaningService.cleanField("last_name", null)).toBe("User");
      expect(DataCleaningService.cleanField("email", undefined)).toBe("");
      expect(DataCleaningService.cleanField("phone", "NULL")).toBe("");
    });

    it("should transform valid values", () => {
      expect(DataCleaningService.cleanField("first_name", "  John  ")).toBe("John");
      expect(DataCleaningService.cleanField("email", "  <EMAIL>  ")).toBe("<EMAIL>");
      expect(DataCleaningService.cleanField("phone", "1234567890")).toBe("+1234567890");
      expect(DataCleaningService.cleanField("country", "us")).toBe("US");
    });

    it("should validate email addresses", () => {
      expect(DataCleaningService.cleanField("email", "invalid-email")).toBe("");
      expect(DataCleaningService.cleanField("email", "<EMAIL>")).toBe("<EMAIL>");
    });

    it("should handle numeric fields", () => {
      expect(DataCleaningService.cleanField("price", "")).toBe(0);
      expect(DataCleaningService.cleanField("price", "invalid")).toBe(0);
      expect(DataCleaningService.cleanField("price", "19.99")).toBe(19.99);
      expect(DataCleaningService.cleanField("quantity", "5")).toBe(5);
    });

    it("should handle date fields", () => {
      expect(DataCleaningService.cleanField("created_at", "")).toBe(null);
      expect(DataCleaningService.cleanField("created_at", "invalid-date")).toBe(null);

      const validDate = "2023-01-01T00:00:00Z";
      const result = DataCleaningService.cleanField("created_at", validDate);
      expect(result).toBeInstanceOf(Date);
      expect(result.getUTCFullYear()).toBe(2023);
    });

    it("should use basic cleaning for unknown fields", () => {
      expect(DataCleaningService.cleanField("unknown_field", "")).toBe("");
      expect(DataCleaningService.cleanField("unknown_field", "  test  ")).toBe("test");
      expect(DataCleaningService.cleanField("unknown_name", "")).toBe("Unknown");
      expect(DataCleaningService.cleanField("unknown_email", "")).toBe("");
      expect(DataCleaningService.cleanField("unknown_price", "")).toBe(0);
    });
  });

  describe("cleanRow", () => {
    it("should clean all fields in a row", () => {
      const dirtyRow = {
        first_name: "",
        last_name: null,
        email: "  <EMAIL>  ",
        phone: "1234567890",
        price: "invalid",
        created_at: "2023-01-01",
        unknown_field: "  test  ",
      };

      const cleanedRow = DataCleaningService.cleanRow(dirtyRow);

      expect(cleanedRow.first_name).toBe("Unknown");
      expect(cleanedRow.last_name).toBe("User");
      expect(cleanedRow.email).toBe("<EMAIL>");
      expect(cleanedRow.phone).toBe("+1234567890");
      expect(cleanedRow.price).toBe(0);
      expect(cleanedRow.created_at).toBeInstanceOf(Date);
      expect(cleanedRow.unknown_field).toBe("test");
    });
  });

  describe("addFieldConfig", () => {
    it("should allow adding custom field configurations", () => {
      const customConfig: FieldCleaningConfig = {
        field: "custom_field",
        defaultValue: "custom_default",
        required: true,
        transformer: (value) => `custom_${value}`,
      };

      DataCleaningService.addFieldConfig(customConfig);

      expect(DataCleaningService.cleanField("custom_field", "")).toBe("custom_default");
      expect(DataCleaningService.cleanField("custom_field", "test")).toBe("custom_test");
    });
  });

  describe("getCleaningStats", () => {
    it("should provide statistics about cleaning operations", () => {
      const originalRows = [
        { first_name: "", email: "<EMAIL>", phone: "" },
        { first_name: "John", email: "", phone: "1234567890" },
      ];

      const cleanedRows = DataCleaningService.cleanRows(originalRows);
      const stats = DataCleaningService.getCleaningStats(originalRows, cleanedRows);

      expect(stats.totalRows).toBe(2);
      expect(stats.emptyFieldsFound.first_name).toBe(1);
      expect(stats.emptyFieldsFound.email).toBe(1);
      expect(stats.emptyFieldsFound.phone).toBe(1);
      expect(stats.fieldsWithDefaults.first_name).toBe(1);
    });
  });

  describe("real-world scenarios", () => {
    it("should handle typical user import data", () => {
      const userImportData = [
        {
          external_id: "user1",
          first_name: "",
          last_name: "Doe",
          email: "  <EMAIL>  ",
          phone: "NULL",
          address: "",
          city: "New York",
          state: "ny",
          zip_code: "",
          created_at: "2023-01-01T10:00:00Z",
        },
        {
          external_id: "user2",
          first_name: "Jane",
          last_name: "",
          email: "invalid-email",
          phone: "9876543210",
          address: "123 Main St",
          city: "",
          state: "CA",
          zip_code: "90210",
          created_at: "invalid-date",
        },
      ];

      const cleanedData = DataCleaningService.cleanRows(userImportData);

      // First user
      expect(cleanedData[0].first_name).toBe("Unknown");
      expect(cleanedData[0].last_name).toBe("Doe");
      expect(cleanedData[0].email).toBe("<EMAIL>");
      expect(cleanedData[0].phone).toBe("");
      expect(cleanedData[0].address).toBe("Unknown Address");
      expect(cleanedData[0].city).toBe("New York");
      expect(cleanedData[0].state).toBe("NY");
      expect(cleanedData[0].zip_code).toBe("00000");
      expect(cleanedData[0].created_at).toBeInstanceOf(Date);

      // Second user
      expect(cleanedData[1].first_name).toBe("Jane");
      expect(cleanedData[1].last_name).toBe("User");
      expect(cleanedData[1].email).toBe(""); // Invalid email becomes empty
      expect(cleanedData[1].phone).toBe("+9876543210");
      expect(cleanedData[1].address).toBe("123 Main St");
      expect(cleanedData[1].city).toBe("Unknown City");
      expect(cleanedData[1].state).toBe("CA");
      expect(cleanedData[1].zip_code).toBe("90210");
      expect(cleanedData[1].created_at).toBe(null); // Invalid date becomes null
    });

    it("should handle product import data", () => {
      const productData = [
        {
          product_name: "",
          brand: "  Acme Corp  ",
          category: null,
          price: "invalid",
          quantity: "10",
          description: "",
        },
      ];

      const cleanedData = DataCleaningService.cleanRows(productData);

      expect(cleanedData[0].product_name).toBe("Unknown Product");
      expect(cleanedData[0].brand).toBe("Acme Corp");
      expect(cleanedData[0].category).toBe("Uncategorized");
      expect(cleanedData[0].price).toBe(0);
      expect(cleanedData[0].quantity).toBe(10);
      expect(cleanedData[0].description).toBe("");
    });
  });
});
