import { z } from "zod";

const dataSources = z.object({
  name: z.string().nonempty(),
  id: z.string().nonempty(),
  available: z.boolean(),
  connected: z.boolean(),
  apiKey: z.string().optional(),
  siteId: z.string().optional(),
});

const fileObj = z.object({
  lastModified: z.number().optional(),
  name: z.string().optional(),
  webkitRelativePath: z.string().optional(),
});

const location = z.object({
  lat: z.number().optional(),
  lng: z.number().optional(),
});

const competitors = z.object({
  place_id: z.string().optional(),
  name: z.string().optional(),
  address: z.string().optional(),
  location: location.optional(),
  distance: z.number().optional(),
});

const socialPlatforms = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  icon: z.string().optional(),
  connectedAccount: z.string().optional(),
  available: z.boolean().optional(),
  accessToken: z.string().optional(),
});

const settings = z.object({
  senderEmail: z.string(),
  senderName: z.string(),
  contactEmail: z.string(),
  contactPhone: z.string(),
});

export const completeOnboardingRequestSchema = z.object({
  businessName: z.string(),
  address: z.string(),
  city: z.string(),
  state: z.string(),
  zip: z.string(),
  phone: z.string(),
  website: z.string().optional(),
  locale: z.string(),
  longitude: z.number().optional(),
  latitude: z.number().optional(),
  timezone: z.string().nonempty(),
  description: z.string().optional(),
  dataSources: dataSources.array(),
  customerData: fileObj.array(),
  competitors: competitors.array(),
  documents: fileObj.array(),
  socialPlatforms: socialPlatforms.array(),
  communicationSettings: settings,
});

export const searchNearbyRequestSchema = z.object({
  longitude: z.number(),
  latitude: z.number(),
});

export const searchPlacesRequestSchema = z.object({
  input: z.string(),
});

export const geocodeRequestSchema = z.object({
  placeId: z.string().nonempty(),
});

export const userInfoRequestSchema = z.object({
  accessToken: z.string().nonempty(),
});

export const linkedInTokenRequestSchema = z.object({
  code: z.string().nonempty(),
});

export const subscriptionUpgradeRequestSchema = z.object({
  subscriptionId: z.string().nonempty(),
  newBasePlanPriceId: z.string().nonempty(),
  newAddOnPriceId: z.string().nonempty(),
  additionalLocations: z.number().int().positive(),
  organizationId: z.number().int().positive(),
});

export const searchEventsRequestSchema = z.object({
  query: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  limit: z.number().int().min(1).max(100).optional(),
  page: z.number().int().min(1).optional(),
});

export const createEventRequestSchema = z.object({
  event_name: z.string().min(1, "Event name is required"),
  category: z.array(z.string()).optional(),
  start_time: z
    .string()
    .min(1, "Start time is required")
    .refine(
      (val) => {
        const eventDate = new Date(val);
        const now = new Date();
        return eventDate > now;
      },
      {
        message: "Event start time must be in the future",
      }
    ),
  timezone: z.string().optional(),
  host: z.string().optional(),
  starting_price: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postal_code: z.string().optional(),
  image: z.string().url().optional().or(z.literal("")),
  url: z.string().url().optional().or(z.literal("")),
});

export const updateEventRequestSchema = z.object({
  event_name: z.string().min(1).optional(),
  category: z.array(z.string()).optional(),
  start_time: z
    .string()
    .refine(
      (val) => {
        if (!val) return true; // Allow empty/undefined for updates
        const eventDate = new Date(val);
        const now = new Date();
        return eventDate > now;
      },
      {
        message: "Event start time must be in the future",
      }
    )
    .optional(),
  timezone: z.string().optional(),
  host: z.string().optional(),
  starting_price: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  postal_code: z.string().optional(),
  image: z.string().url().optional().or(z.literal("")),
  url: z.string().url().optional().or(z.literal("")),
});

export const getEventsRequestSchema = z.object({
  page: z.coerce.number().min(1).default(1).optional(),
  limit: z.coerce.number().min(1).max(100).default(20).optional(),
  search: z.string().optional(),
  future_only: z.coerce.boolean().default(true).optional(),
});
