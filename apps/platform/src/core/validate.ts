import Ajv, { ErrorObject, JSONSchemaType } from "ajv";
import addFormats from "ajv-formats";
import addErrors from "ajv-errors";
import { capitalizeFirstLetter } from "../utilities";
import { RequestError } from "./errors";

type IsValidSchema = [boolean, Error | undefined];

const validator = new Ajv({ allErrors: true });
addFormats(validator);
addErrors(validator);

export { JSONSchemaType, IsValidSchema, validator };

export function validate<T>(schema: JSONSchemaType<T>, data: any): T {
  const validate = schema.$id
    ? validator.getSchema<T>(schema.$id) || validator.compile(schema)
    : validator.compile(schema);
  if (validate(data)) {
    return data;
  }
  throw new RequestError(parseError(validate.errors), 422);
}

export const isValid = (schema: any, data: any): IsValidSchema => {
  const validate = validator.compile(schema);
  const isValid = validate(data);
  if (isValid) return [isValid, undefined];

  const error = parseError(validate.errors);
  return [isValid, new RequestError(error, 422)];
};

export const parseError = (errors: ErrorObject[] | null | undefined = []) => {
  if (!errors || errors.length === 0) {
    return "There was an unknown error validating your request.";
  }

  const error = errors[0];
  const path = error.instancePath || "(root)";
  const keyword = error.keyword;
  const params = JSON.stringify(error.params);
  const message = error.message || "No specific message provided.";

  let detailedMessage = `Validation Error: Path: \`${path}\`, Keyword: \`${keyword}\`, Details: ${message}`;

  if (params && params !== "{}") {
    detailedMessage += `, Params: ${params}`;
  }

  return capitalizeFirstLetter(detailedMessage);
};
