/**
 * @swagger
 * components:
 *   schemas:
 *     DashboardResponse:
 *       type: object
 *       properties:
 *         hasData:
 *           type: boolean
 *         timeframe:
 *           type: string
 *         kpi:
 *           type: object
 *           properties:
 *             revenue:
 *               type: number
 *               nullable: true
 *             customers:
 *               type: integer
 *             averageOrder:
 *               type: number
 *               nullable: true
 *             customerValue:
 *               type: number
 *               nullable: true
 *             products:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                   sales:
 *                     type: integer
 *                   revenue:
 *                     type: number
 *                   averagePrice:
 *                     type: number
 *         charts:
 *           type: object
 *           properties:
 *             revenue:
 *               type: array
 *               items:
 *                 type: object
 *             sales:
 *               type: array
 *               items:
 *                 type: object
 *         insights:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               impact:
 *                 type: string
 *               type:
 *                 type: string
 *               actions:
 *                 type: array
 *                 items:
 *                   type: string
 *         customerMetrics:
 *           type: object
 *           properties:
 *             total:
 *               type: integer
 *             purchasedInTimeframe:
 *               type: integer
 *             new:
 *               type: integer
 *             returning:
 *               type: integer
 *         analytics:
 *           type: object
 *           properties:
 *             ageDistribution:
 *               type: object
 *               nullable: true
 *               properties:
 *                 under_30:
 *                   type: integer
 *                 age_30_45:
 *                   type: integer
 *                 age_46_60:
 *                   type: integer
 *                 over_60:
 *                   type: integer
 *                 total_with_age:
 *                   type: integer
 *             budtenderPerformance:
 *               type: array
 *               nullable: true
 *               items:
 *                 type: object
 *                 properties:
 *                   budtender_name:
 *                     type: string
 *                   total_sales:
 *                     type: integer
 *                   revenue:
 *                     type: number
 *                   average_sale:
 *                     type: number
 *                   total_profit:
 *                     type: number
 *                   unique_customers:
 *                     type: integer
 *                   profit_margin:
 *                     type: number
 *             customerTypeAnalysis:
 *               type: array
 *               nullable: true
 *               items:
 *                 type: object
 *                 properties:
 *                   customer_type:
 *                     type: string
 *                   total_orders:
 *                     type: integer
 *                   revenue:
 *                     type: number
 *                   average_order:
 *                     type: number
 *                   unique_customers:
 *                     type: integer
 *                   profit_margin:
 *                     type: number
 *             profitabilityAnalysis:
 *               type: object
 *               nullable: true
 *               properties:
 *                 total_profit:
 *                   type: number
 *                 total_cost:
 *                   type: number
 *                 total_discounts:
 *                   type: number
 *                 total_loyalty_discounts:
 *                   type: number
 *                 profit_margin:
 *                   type: number
 *                 total_revenue:
 *                   type: number
 *     InsightGenerateRequest:
 *       type: object
 *       properties:
 *         model:
 *           type: string
 *     InsightGenerateResponse:
 *       type: array
 *       items:
 *         type: object
 *         properties:
 *           id:
 *             type: string
 *           title:
 *             type: string
 *           description:
 *             type: string
 *           impact:
 *             type: string
 *           type:
 *             type: string
 *           actions:
 *             type: array
 *             items:
 *               type: string
 *     AutomationPlanRequest:
 *       type: object
 *       properties:
 *         model:
 *           type: string
 *         imageQuality:
 *           type: string
 *           enum: [SD, HD]
 *           default: HD
 *     AutomationPlanResponse:
 *       type: object
 *       properties:
 *         plan:
 *           type: object
 *         items:
 *           type: object
 *     RegenerateStepRequest:
 *       type: object
 *       properties:
 *         stepIndex:
 *           type: integer
 *           nullable: true
 *         itemKey:
 *           type: string
 *           nullable: true
 *         model:
 *           type: string
 *           nullable: true
 *         prompt:
 *           type: string
 *           nullable: true
 *         imageQuality:
 *           type: string
 *           enum: [SD, HD]
 *           default: HD
 */

/**
 * @swagger
 * tags:
 *   name: Dashboard
 *   description: Dashboard and insights management endpoints
 */

/* eslint-disable indent */
import Router from "@koa/router";
import { locationRoleMiddleware } from "../locations/LocationService";
import { PosData } from "../pos/PosData";
import Campaign from "../campaigns/Campaign";
import {
  generateInsights,
  calculateAIScore,
  getInsights,
  generateAutomationPlanForInsight,
  updateInsightStatus,
  Insight,
  getExistingResources,
  handleNaturalLanguageRequest,
  regenerateStep,
  findDuplicateInsights,
} from "../insights/InsightService";
import { AutomationPromptService } from "../insights/PromptService";
import { subscriptionForChannel } from "../subscriptions/SubscriptionService";
import { getProviderByGroup } from "../providers/ProviderRepository";
import { User } from "../users/User";
import { Product } from "../products/Product";

// Update the Insight type to include execution results
declare module "../insights/InsightService" {
  interface Insight {
    execution_results?: string;
    executed_at?: Date;
  }
}

const router = new Router({
  prefix: "/dashboard",
});

/**
 * @swagger
 * /dashboard:
 *   get:
 *     summary: Get dashboard data
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d, 1y, all]
 *           default: 30d
 *         description: Time period for dashboard data
 *     responses:
 *       200:
 *         description: Dashboard data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DashboardResponse'
 *       500:
 *         description: Failed to fetch dashboard data
 */
router.get("/", locationRoleMiddleware("support"), async (ctx) => {
  console.log("Starting dashboard data fetch...");
  const location_id = ctx.state.location.id;
  const { timeframe = "30d" } = ctx.query; // Support different timeframes

  // Calculate date range based on timeframe
  const endDate = new Date();
  const startDate = new Date();
  switch (timeframe) {
    case "7d":
      startDate.setDate(endDate.getDate() - 7);
      break;
    case "30d":
      startDate.setDate(endDate.getDate() - 30);
      break;
    case "90d":
      startDate.setDate(endDate.getDate() - 90);
      break;
    case "1y":
      startDate.setFullYear(endDate.getFullYear() - 1);
      break;
    case "all":
      startDate.setDate(0);
      break;
    default:
      startDate.setDate(endDate.getDate() - 30);
      break;
  }

  try {
    // Add an initial check for any data
    const hasPosData = await PosData.query()
      .where("location_id", location_id)
      .first();

    const [
      hasUserData,
      hasProductData,
      totalUsers,
      totalProducts,
      productQuality,
    ] = await Promise.all([
      User.query().where("location_id", location_id).first(),
      Product.query().where("location_id", location_id).first(),
      User.query()
        .where("location_id", location_id)
        .count("* as count")
        .first()
        .then((r: any) => Number(r?.count) || 0),
      Product.query()
        .where("location_id", location_id)
        .count("* as count")
        .first()
        .then((r: any) => Number(r?.count) || 0),
      calculateProductQuality(location_id),
    ]);

    if (!hasPosData) {
      ctx.body = {
        hasPosData: false,
        hasUserData: !!hasUserData,
        hasProductData: !!hasProductData,
        stats: {
          totalUsers,
          totalProducts,
          productQuality,
        },
        message: "No POS data available for this location.",
        timeframe,
      };
      return;
    }

    // KPI Query
    const kpiQuery = PosData.query()
      .where("location_id", location_id)
      .where("order_date", ">=", startDate)
      .where("order_date", "<=", endDate)
      .select([
        PosData.raw("SUM(net_sales) as total_revenue"),
        PosData.raw("COUNT(DISTINCT customer_name) as total_customers"),
        PosData.raw("AVG(net_sales) as average_order"),
        PosData.raw(
          "SUM(net_sales) / COUNT(DISTINCT customer_name) as customer_value"
        ),
      ])
      .first();

    console.log("KPI SQL:", kpiQuery.toString());
    console.log("KPI Debug:", {
      location_id,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    });

    // Revenue Query
    const revenueQuery = PosData.query()
      .where("location_id", location_id)
      .where("order_date", ">=", startDate)
      .where("order_date", "<=", endDate)
      .select([
        PosData.raw("DATE_FORMAT(order_date, '%Y-%m-%d') as order_date"),
        PosData.raw("SUM(net_sales) as revenue"),
      ])
      .groupByRaw("DATE_FORMAT(order_date, '%Y-%m-%d')")
      .orderByRaw("DATE_FORMAT(order_date, '%Y-%m-%d')");

    console.log("Revenue SQL:", revenueQuery.toString());

    // Now execute all queries
    const [
      kpiData,
      revenueOverTime,
      salesOverTime,
      topProducts,
      activeCampaigns,
      customerInsights,
      productInsights,
      salesInsights,
      communicationStats,
      aiScore,
      campaignMetrics,
      ageDistribution,
      budtenderPerformance,
      profitabilityAnalysis,
      customerTypeAnalysis,
    ] = await Promise.all([
      kpiQuery.then((result) => {
        console.log("KPI Result:", result);
        return result;
      }),
      revenueQuery.then((result) => {
        console.log("Revenue Result:", result);
        return result;
      }),
      // Sales Over Time
      PosData.query()
        .where("location_id", location_id)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          PosData.raw("DATE_FORMAT(order_date, '%Y-%m-%d') as order_date"),
          PosData.raw("COUNT(*) as total_orders"),
          PosData.raw("COUNT(DISTINCT customer_name) as unique_customers"),
        ])
        .groupByRaw("DATE_FORMAT(order_date, '%Y-%m-%d')")
        .orderBy("order_date"),

      // Top Products
      PosData.query()
        .where("location_id", location_id)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .groupBy("product_name")
        .select([
          "product_name",
          PosData.raw("COUNT(*) as total_sales"),
          PosData.raw("SUM(net_sales) as revenue"),
          PosData.raw("AVG(net_sales) as average_price"),
        ])
        .orderBy("total_sales", "desc")
        .limit(5),

      // Active Campaigns
      Campaign.query()
        .where("location_id", location_id)
        .whereIn("state", ["scheduled", "running", "pending"])
        .select("*")
        .orderBy("created_at", "desc")
        .limit(5),

      // Customer Insights
      PosData.query()
        .where("location_id", location_id)
        .select([
          // Get total customers (all time) using subquery
          PosData.raw(`
            (
              SELECT COUNT(DISTINCT customer_name) 
              FROM pos_data 
              WHERE location_id = ${location_id}
            ) as total_customers
          `),
          // Get customers who purchased in timeframe
          PosData.raw(
            "COUNT(DISTINCT customer_name) as purchased_in_timeframe"
          ),
          // Get new customers in timeframe
          PosData.raw(`
            COUNT(DISTINCT CASE 
              WHEN customer_type = 'new' THEN customer_name 
              END) as new_customers
          `),
          // Get returning customers in timeframe
          PosData.raw(`
            COUNT(DISTINCT CASE 
              WHEN customer_type = 'returning' THEN customer_name 
              END) as returning_customers
          `),
        ])
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .first(),

      // Product Category Insights
      PosData.query()
        .where("location_id", location_id)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .groupBy("master_category")
        .select([
          "master_category",
          PosData.raw("COUNT(*) as total_sales"),
          PosData.raw("SUM(net_sales) as revenue"),
          PosData.raw("AVG(net_sales) as average_price"),
        ])
        .orderBy("revenue", "desc"),

      // Sales Performance by Time
      PosData.query()
        .where("location_id", location_id)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          PosData.raw(`
            SUM(CASE 
              WHEN HOUR(order_date) BETWEEN 9 AND 12 THEN net_sales 
              ELSE 0 
            END) as morning_sales
          `),
          PosData.raw(`
            SUM(CASE 
              WHEN HOUR(order_date) BETWEEN 13 AND 17 THEN net_sales 
              ELSE 0 
            END) as afternoon_sales
          `),
          PosData.raw(`
            SUM(CASE 
              WHEN HOUR(order_date) BETWEEN 18 AND 23 THEN net_sales 
              ELSE 0 
            END) as evening_sales
          `),
        ])
        .first(),

      // Communication stats
      Campaign.query()
        .where("location_id", location_id)
        .where("created_at", ">=", startDate)
        .where("created_at", "<=", endDate)
        .select([
          Campaign.raw(`
            SUM(CASE 
              WHEN channel = 'email' THEN CAST(JSON_EXTRACT(delivery, '$.sent') AS UNSIGNED) 
              ELSE 0 
            END) as emails_sent`),
          Campaign.raw(`
            SUM(CASE 
              WHEN channel = 'text' THEN CAST(JSON_EXTRACT(delivery, '$.sent') AS UNSIGNED) 
              ELSE 0 
            END) as sms_sent`),
        ])
        .first(),

      // AI Score
      calculateAIScore(location_id, timeframe.toString()),

      // Campaign engagement metrics
      Campaign.query()
        .where("location_id", location_id)
        .where("created_at", ">=", startDate)
        .where("created_at", "<=", endDate)
        .whereNotNull("delivery")
        .select([
          Campaign.raw(`
            AVG(CAST(JSON_EXTRACT(delivery, '$.opens') AS DECIMAL) / 
                CAST(JSON_EXTRACT(delivery, '$.sent') AS DECIMAL) * 100) as avg_open_rate
          `),
          Campaign.raw(`
            AVG(CAST(JSON_EXTRACT(delivery, '$.clicks') AS DECIMAL) / 
                CAST(JSON_EXTRACT(delivery, '$.sent') AS DECIMAL) * 100) as avg_click_rate
          `),
          Campaign.raw(`
            AVG(CAST(JSON_EXTRACT(delivery, '$.sent') AS DECIMAL) / 
                CAST(JSON_EXTRACT(delivery, '$.total') AS DECIMAL) * 100) as avg_delivery_rate
          `),
          Campaign.raw(`COUNT(*) as total_campaigns`),
        ])
        .first(),

      // Age Distribution
      PosData.query()
        .where("location_id", location_id)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .where("birth_date", "IS NOT", null)
        .select([
          PosData.raw(`
            COUNT(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) < 30 THEN 1 END) as under_30,
            COUNT(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 30 AND 45 THEN 1 END) as age_30_45,
            COUNT(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 46 AND 60 THEN 1 END) as age_46_60,
            COUNT(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 60 THEN 1 END) as over_60,
            COUNT(*) as total_with_age
          `),
        ])
        .first(),

      // Budtender Performance
      PosData.query()
        .where("location_id", location_id)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .groupBy("budtender_name")
        .select([
          "budtender_name",
          PosData.raw("COUNT(*) as total_sales"),
          PosData.raw("SUM(net_sales) as revenue"),
          PosData.raw("AVG(net_sales) as average_sale"),
          PosData.raw("SUM(inventory_profit) as total_profit"),
          PosData.raw("COUNT(DISTINCT customer_name) as unique_customers"),
          PosData.raw(
            "SUM(inventory_profit) / SUM(net_sales) * 100 as profit_margin"
          ),
        ])
        .orderBy("revenue", "desc"),

      // Profitability Analysis
      PosData.query()
        .where("location_id", location_id)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          PosData.raw("SUM(inventory_profit) as total_profit"),
          PosData.raw("SUM(inventory_cost) as total_cost"),
          PosData.raw("SUM(discounted_amount) as total_discounts"),
          PosData.raw("SUM(loyalty_as_discount) as total_loyalty_discounts"),
          PosData.raw(
            "AVG(inventory_profit / net_sales) * 100 as profit_margin"
          ),
          PosData.raw("SUM(net_sales) as total_revenue"),
        ])
        .first(),

      // Customer Type Analysis
      PosData.query()
        .where("location_id", location_id)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .groupBy("customer_type")
        .select([
          "customer_type",
          PosData.raw("COUNT(*) as total_orders"),
          PosData.raw("SUM(net_sales) as revenue"),
          PosData.raw("AVG(net_sales) as average_order"),
          PosData.raw("COUNT(DISTINCT customer_name) as unique_customers"),
          PosData.raw(
            "SUM(inventory_profit) / SUM(net_sales) * 100 as profit_margin"
          ),
        ]),
    ]);

    // Calculate insights based on the data
    const insights = [];

    // Revenue trend insight
    const revenueChange = calculateRevenueChange(revenueOverTime);
    if (Math.abs(revenueChange) > 10) {
      insights.push({
        id: "revenue_trend",
        title:
          revenueChange > 0
            ? `Revenue increased by ${revenueChange.toFixed(1)}%`
            : `Revenue decreased by ${Math.abs(revenueChange).toFixed(1)}%`,
        description:
          "Significant change in revenue compared to previous period",
        impact: revenueChange > 0 ? "positive" : "negative",
        type: "revenue",
        actions: [
          "Review pricing strategy",
          "Analyze successful products",
          "Investigate customer behavior",
        ],
      });
    }

    // Customer retention insight
    const returnRate =
      (customerInsights.returning_customers /
        customerInsights.total_customers) *
      100;
    if (returnRate < 30) {
      insights.push({
        id: "customer_retention",
        title: "Low Customer Return Rate",
        description: `Only ${returnRate.toFixed(
          1
        )}% of customers are returning. Consider implementing retention strategies.`,
        impact: "high",
        type: "customers",
        actions: [
          "Launch loyalty program",
          "Create targeted email campaign",
          "Review customer feedback",
        ],
      });
    }

    // Product category insight
    const topCategory = productInsights[0];
    if (topCategory && topCategory.revenue / kpiData.total_revenue > 0.4) {
      insights.push({
        id: "category_concentration",
        title: "High Category Concentration",
        description: `${topCategory.master_category} represents over 40% of revenue. Consider diversifying product mix.`,
        impact: "medium",
        type: "products",
        actions: [
          "Expand other categories",
          "Review pricing strategy",
          "Analyze customer preferences",
        ],
      });
    }

    // Campaign engagement insight
    if (campaignMetrics?.avg_open_rate) {
      const openRate = Number(campaignMetrics.avg_open_rate);
      if (openRate < 15) {
        insights.push({
          id: "low_email_engagement",
          title: "Low Email Open Rate",
          description: `Average email open rate is ${openRate.toFixed(
            1
          )}%. Industry average is typically 15-25%.`,
          impact: "high",
          type: "campaigns",
          actions: [
            "Review email subject lines",
            "Optimize send times",
            "Segment audience better",
            "A/B test content",
          ],
        });
      }
    }

    // Delivery rate insight
    if (campaignMetrics?.avg_delivery_rate) {
      const deliveryRate = Number(campaignMetrics.avg_delivery_rate);
      if (deliveryRate < 95) {
        insights.push({
          id: "delivery_issues",
          title: "Email Delivery Issues",
          description: `Average delivery rate is ${deliveryRate.toFixed(
            1
          )}%. This indicates potential list quality issues.`,
          impact: "high",
          type: "campaigns",
          actions: [
            "Clean email list",
            "Remove bounced addresses",
            "Verify email addresses",
            "Review sending infrastructure",
          ],
        });
      }
    }

    ctx.body = {
      hasPosData: true,
      hasUserData: !!hasUserData,
      hasProductData: !!hasProductData,
      stats: {
        totalUsers,
        totalProducts,
        productQuality,
      },
      timeframe,
      kpi: {
        revenue: Number(kpiData?.total_revenue) || null,
        customers: Number(kpiData?.total_customers) || 0,
        averageOrder: Number(kpiData?.average_order) || null,
        customerValue: Number(kpiData?.customer_value) || null,
        products: topProducts.map((product: any) => ({
          name: product.product_name,
          sales: Number(product.total_sales),
          revenue: Number(product.revenue),
          averagePrice: Number(product.average_price),
        })),
      },
      charts: {
        revenue: revenueOverTime,
        sales: salesOverTime,
      },
      topProducts,
      activeCampaigns,
      insights,
      customerMetrics: {
        total: customerInsights.total_customers,
        purchasedInTimeframe: customerInsights.purchased_in_timeframe,
        new: customerInsights.new_customers,
        returning: customerInsights.returning_customers,
      },
      categoryPerformance: productInsights,
      timeDistribution: salesInsights,
      communicationStats: {
        emailsSent: Number(communicationStats.emails_sent) || 0,
        smsSent: Number(communicationStats.sms_sent) || 0,
      },
      aiScore,
      campaignMetrics: {
        openRate: campaignMetrics?.avg_open_rate
          ? Number(campaignMetrics.avg_open_rate)
          : 0,
        clickRate: campaignMetrics?.avg_click_rate
          ? Number(campaignMetrics.avg_click_rate)
          : 0,
        deliveryRate: campaignMetrics?.avg_delivery_rate
          ? Number(campaignMetrics.avg_delivery_rate)
          : 0,
        totalCampaigns: campaignMetrics?.total_campaigns
          ? Number(campaignMetrics.total_campaigns)
          : 0,
      },
      analytics: {
        ageDistribution:
          ageDistribution?.total_with_age > 0
            ? {
                under_30: Number(ageDistribution.under_30) || 0,
                age_30_45: Number(ageDistribution.age_30_45) || 0,
                age_46_60: Number(ageDistribution.age_46_60) || 0,
                over_60: Number(ageDistribution.over_60) || 0,
                total_with_age: Number(ageDistribution.total_with_age) || 0,
              }
            : null,

        budtenderPerformance:
          Array.isArray(budtenderPerformance) && budtenderPerformance.length > 0
            ? budtenderPerformance.map((b) => ({
                budtender_name: b.budtender_name,
                total_sales: Number(b.total_sales) || 0,
                revenue: Number(b.revenue) || 0,
                average_sale: Number(b.average_sale) || 0,
                total_profit: Number(b.total_profit) || 0,
                unique_customers: Number(b.unique_customers) || 0,
                profit_margin: Number(b.profit_margin) || 0,
              }))
            : null,

        customerTypeAnalysis:
          Array.isArray(customerTypeAnalysis) && customerTypeAnalysis.length > 0
            ? customerTypeAnalysis.map((ct) => ({
                customer_type: ct.customer_type,
                total_orders: Number(ct.total_orders) || 0,
                revenue: Number(ct.revenue) || 0,
                average_order: Number(ct.average_order) || 0,
                unique_customers: Number(ct.unique_customers) || 0,
                profit_margin: Number(ct.profit_margin) || 0,
              }))
            : null,

        profitabilityAnalysis: profitabilityAnalysis
          ? {
              total_profit: Number(profitabilityAnalysis.total_profit) || 0,
              total_cost: Number(profitabilityAnalysis.total_cost) || 0,
              total_discounts:
                Number(profitabilityAnalysis.total_discounts) || 0,
              total_loyalty_discounts:
                Number(profitabilityAnalysis.total_loyalty_discounts) || 0,
              profit_margin: Number(profitabilityAnalysis.profit_margin) || 0,
              total_revenue: Number(profitabilityAnalysis.total_revenue) || 0,
            }
          : null,
      },
    };
  } catch (error) {
    console.error("Error fetching dashboard data:", error);
    ctx.status = 500;
    ctx.body = {
      error: "An error occurred while fetching dashboard data",
      hasPosData: false,
    };
  }
});

/**
 * @swagger
 * /dashboard/insights/generate:
 *   post:
 *     summary: Generate insights
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeframe
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d, 1y, all]
 *           default: 30d
 *         description: Time period for insights
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/InsightGenerateRequest'
 *     responses:
 *       200:
 *         description: Insights generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/InsightGenerateResponse'
 *       500:
 *         description: Failed to generate insights
 */
router.post(
  "/insights/generate",
  locationRoleMiddleware("support"),
  async (ctx) => {
    const location_id = ctx.state.location.id;
    const { timeframe = "30d" } = ctx.query;
    const { model } = ctx.request.body;

    try {
      const insights = await generateInsights(
        location_id,
        timeframe.toString(),
        model
      );

      if (!insights || insights.length === 0) {
        ctx.body = {
          message: "No insights generated",
          insights: [],
        };
        return;
      }

      ctx.body = insights;
    } catch (error: any) {
      console.error("Error generating insights:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to generate insights",
        details:
          process.env.NODE_ENV === "development" ? error.message : undefined,
        insights: [],
      };
    }
  }
);

/**
 * @swagger
 * /dashboard/insights:
 *   get:
 *     summary: Get all insights
 *     tags: [Dashboard]
 *     responses:
 *       200:
 *         description: List of insights retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/InsightGenerateResponse'
 */
router.get("/insights", async (ctx) => {
  const location_id = ctx.state.location.id;
  const insights = await getInsights(location_id);
  ctx.body = insights;
});

/**
 * @swagger
 * /locations/{locationId}/dashboard/insights/duplicates:
 *   get:
 *     summary: Find duplicate insights for debugging
 *     description: Returns insights that have duplicate titles for investigation
 *     tags: [Dashboard]
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Location ID
 *     responses:
 *       200:
 *         description: Duplicate insights found
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   title:
 *                     type: string
 *                   count:
 *                     type: integer
 *                   insights:
 *                     type: array
 *                     items:
 *                       type: object
 */
router.get("/insights/duplicates", async (ctx) => {
  const location_id = ctx.state.location.id;
  const duplicates = await findDuplicateInsights(location_id);
  ctx.body = duplicates;
});

/**
 * @swagger
 * /dashboard/insights/{id}/automation-plan:
 *   post:
 *     summary: Generate automation plan for an insight
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Insight ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AutomationPlanRequest'
 *     responses:
 *       200:
 *         description: Automation plan generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AutomationPlanResponse'
 *       500:
 *         description: Failed to generate automation plan
 */
router.post(
  "/insights/:id/automation-plan",
  locationRoleMiddleware("support"),
  async (ctx) => {
    const insightId = parseInt(ctx.params.id);
    const { model, imageQuality = "HD" } = ctx.request.body;

    try {
      console.log(`Generating automation plan for insight ${insightId}`);

      const plan = await generateAutomationPlanForInsight(
        insightId,
        model,
        imageQuality
      );

      console.log(
        `Successfully generated automation plan for insight ${insightId}`
      );
      ctx.body = plan;
    } catch (error: any) {
      console.error(
        `Error generating automation plan for insight ${insightId}:`,
        error
      );

      // Provide more specific error messages based on the error type
      let errorMessage = "Failed to generate automation plan";
      let statusCode = 500;

      if (error.message?.includes("Insight not found")) {
        errorMessage = "Insight not found";
        statusCode = 404;
      } else if (error.message?.includes("Database connection not available")) {
        errorMessage =
          "Service temporarily unavailable - database connection issue";
        statusCode = 503;
      } else if (error.message?.includes("OpenAI API key")) {
        errorMessage = "Service configuration error";
        statusCode = 503;
      } else if (error.message?.includes("Failed to initialize")) {
        errorMessage = "Service initialization error";
        statusCode = 503;
      }

      ctx.status = statusCode;
      ctx.body = {
        error: errorMessage,
        details:
          process.env.NODE_ENV === "development" ? error.message : undefined,
        stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
      };
    }
  }
);

/**
 * @swagger
 * /dashboard/insights/{id}/regenerate-step:
 *   post:
 *     summary: Regenerate a step in the automation plan
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Insight ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RegenerateStepRequest'
 *     responses:
 *       200:
 *         description: Step regenerated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AutomationPlanResponse'
 *       400:
 *         description: Invalid request parameters
 *       404:
 *         description: Insight not found
 *       500:
 *         description: Failed to regenerate step
 */
router.post(
  "/insights/:id/regenerate-step",
  locationRoleMiddleware("support"),
  async (ctx) => {
    const insightId = parseInt(ctx.params.id);
    const {
      stepIndex,
      itemKey,
      model,
      prompt,
      imageQuality = "HD",
    } = ctx.request.body;

    console.log("Regenerate step request:", {
      insightId,
      stepIndex,
      itemKey,
      hasModel: !!model,
      hasPrompt: !!prompt,
      imageQuality,
    });

    try {
      // Check if we have itemKey directly or need to fetch it from stepIndex
      let actualItemKey = itemKey;

      // Only use stepIndex if it's not null and not undefined
      if (!actualItemKey && stepIndex !== null && stepIndex !== undefined) {
        // We need to get the itemKey from the stepIndex
        const insight = await Insight.query().where("id", insightId).first();
        if (!insight) {
          console.error(`Insight not found: ${insightId}`);
          ctx.throw(404, "Insight not found");
          return;
        }

        // Safely parse the plan, handling if it's already an object
        let plan;
        if (!insight.plan) {
          plan = {};
        } else if (typeof insight.plan === "string") {
          try {
            plan = JSON.parse(insight.plan);
          } catch (parseError: any) {
            console.error("Error parsing plan:", parseError);
            ctx.throw(400, `Failed to parse plan: ${parseError.message}`);
            return;
          }
        } else {
          // If it's already an object, use it directly
          plan = insight.plan;
        }

        const itemKeys = Object.keys(plan.items || {});
        console.log(`Available item keys for insight ${insightId}:`, itemKeys);

        if (itemKeys.length > stepIndex) {
          actualItemKey = itemKeys[stepIndex];
          console.log(
            `Resolved stepIndex ${stepIndex} to itemKey "${actualItemKey}"`
          );
        } else {
          console.error(
            `Step index ${stepIndex} is out of bounds. Available indices: 0-${
              itemKeys.length - 1
            }`
          );
          ctx.throw(
            400,
            `Step index ${stepIndex} is out of bounds. Available indices: 0-${
              itemKeys.length - 1
            }`
          );
          return;
        }
      }

      if (!actualItemKey) {
        console.error("Missing required parameters", { itemKey, stepIndex });
        ctx.throw(
          400,
          "A valid itemKey or stepIndex must be provided. Please ensure you are specifying either a string itemKey or a numeric stepIndex (0-based)."
        );
        return;
      }

      // Regenerate the step
      console.log(`Regenerating step with itemKey "${actualItemKey}"`);
      const regeneratedStep = await regenerateStep(
        insightId,
        actualItemKey,
        model,
        prompt,
        imageQuality
      );

      // The plan update is now handled in CrewAIService.regenerateItem
      console.log(
        `Successfully regenerated step with itemKey "${actualItemKey}"`
      );
      ctx.body = regeneratedStep;
    } catch (error: any) {
      console.error("Error regenerating step:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to regenerate step",
        details: error.message,
      };
    }
  }
);

router.put("/insights/:id/status", async (ctx: any) => {
  const location_id = ctx.state.location.id;
  const insight_id = parseInt(ctx.params.id);
  const { status } = ctx.request.body;

  try {
    await updateInsightStatus(insight_id, status);
    ctx.body = { success: true };
  } catch (error: any) {
    console.error("Error updating insight status:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to update insight status",
      details:
        process.env.NODE_ENV === "development" ? error.message : undefined,
    };
  }
});

router.post(
  "/insights/:id/feedback",
  locationRoleMiddleware("support"),
  async (ctx) => {
    const insightId = parseInt(ctx.params.id);
    const feedback = ctx.request.body;

    // Set a timeout for this request - increased to 120 seconds for complex operations
    const timeoutPromise = new Promise<never>((_resolve, reject) => {
      setTimeout(() => reject(new Error("Request timeout")), 120000); // 120 second timeout
    });

    try {
      console.log(
        `[FEEDBACK] Starting feedback processing for insight ${insightId} at ${new Date().toISOString()}`
      );

      await Promise.race([
        (async () => {
          console.log(
            `[FEEDBACK] Processing feedback for insight ${insightId}`
          );

          const insight = await Insight.query().where("id", insightId).first();

          if (!insight) {
            ctx.throw(404, "Insight not found");
            return;
          }

          // Get the original plan and resources used for generation with timeout
          console.log(
            `[FEEDBACK] Fetching existing resources and subscriptions for location ${insight.location_id}...`
          );
          const startTime = Date.now();
          const [existingResources, emailSubscription, smsSubscription] =
            await Promise.all([
              getExistingResources(insight.location_id),
              subscriptionForChannel("email", insight.location_id),
              subscriptionForChannel("text", insight.location_id),
            ]);
          console.log(
            `[FEEDBACK] Resources fetched in ${Date.now() - startTime}ms`
          );

          const [emailProvider, smsProvider] = await Promise.all([
            emailSubscription
              ? getProviderByGroup("email", insight.location_id)
              : null,
            smsSubscription
              ? getProviderByGroup("text", insight.location_id)
              : null,
          ]);

          // Create automation prompt service instance
          const automationPromptService = new AutomationPromptService(
            "automation"
          );

          // Add feedback to training data with full context
          await automationPromptService.addTrainingExample({
            input: {
              insight,
              plan: insight.plan,
              resources: existingResources,
              subscriptions: {
                email: emailSubscription,
                text: smsSubscription,
              },
              providers: {
                email: emailProvider,
                text: smsProvider,
              },
            },
            output: insight.plan,
            success: feedback.success,
            error: feedback.error,
          });

          // Process training examples asynchronously to avoid blocking
          const trainingPromises = [];

          // Add main feedback to training data with full context
          trainingPromises.push(
            automationPromptService
              .addTrainingExample({
                input: {
                  insight,
                  plan: insight.plan,
                  resources: existingResources,
                  subscriptions: {
                    email: emailSubscription,
                    text: smsSubscription,
                  },
                  providers: {
                    email: emailProvider,
                    text: smsProvider,
                  },
                },
                output: insight.plan,
                success: feedback.success,
                error: feedback.error,
              })
              .catch((error) => {
                console.warn("Failed to add main training example:", error);
              })
          );

          // If there were step-specific errors, add those as separate examples
          if (feedback.steps) {
            for (const stepResult of feedback.steps) {
              if (!stepResult.success) {
                trainingPromises.push(
                  automationPromptService
                    .addTrainingExample({
                      input: {
                        insight,
                        step: stepResult.step,
                        resources: existingResources,
                        subscriptions: {
                          email: emailSubscription,
                          text: smsSubscription,
                        },
                        providers: {
                          email: emailProvider,
                          text: smsProvider,
                        },
                      },
                      output: stepResult.step,
                      success: false,
                      error: stepResult.error,
                    })
                    .catch((error) => {
                      console.warn(
                        "Failed to add step training example:",
                        error
                      );
                    })
                );
              }
            }
          }

          // Process training examples in background - don't wait for completion
          Promise.allSettled(trainingPromises).then(() => {
            console.log(
              `Completed processing training examples for insight ${insightId}`
            );
          });

          console.log(`[FEEDBACK] Updating insight status...`);
          // Update insight status based on feedback
          await updateInsightStatus(
            insightId,
            feedback.success ? "completed" : "viewed"
          );

          // If successful, store the execution results for future reference
          if (feedback.success) {
            await Insight.query()
              .where("id", insightId)
              .update({
                execution_results: JSON.stringify(feedback.steps),
                executed_at: new Date(),
              });
          }

          console.log(
            `[FEEDBACK] Successfully processed feedback for insight ${insightId} at ${new Date().toISOString()}`
          );
          ctx.body = { success: true };
        })(),
        timeoutPromise,
      ]);
    } catch (error: any) {
      console.error(
        `[FEEDBACK] Error processing feedback for insight ${insightId}:`,
        {
          error: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString(),
        }
      );

      if (error.message === "Request timeout") {
        ctx.status = 504;
        ctx.body = {
          error: "Gateway Timeout",
          message:
            "The feedback processing took too long. The operation may still be running in the background. Please try again in a few moments.",
          code: 504,
        };
      } else {
        ctx.status = 500;
        ctx.body = {
          error: "Failed to process feedback",
          message:
            "An error occurred while processing the feedback. Please try again.",
          details:
            process.env.NODE_ENV === "development" ? error.message : undefined,
        };
      }
    }
  }
);

router.post(
  "/insights/request",
  locationRoleMiddleware("support"),
  async (ctx) => {
    const location_id = ctx.state.location.id;
    const { request, model } = ctx.request.body;

    try {
      const response = await handleNaturalLanguageRequest(
        location_id,
        request,
        model
      );
      ctx.body = response;
    } catch (error: any) {
      console.error("Error handling natural language request:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to process request",
        details:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      };
    }
  }
);

router.post(
  "/insights/execute-step",
  locationRoleMiddleware("support"),
  async (ctx) => {
    const location_id = ctx.state.location.id;
    const { step } = ctx.request.body;

    try {
      // Execute the step based on its type
      let result;
      switch (step.type) {
        case "list":
          result = await executeListStep(location_id, step.config.list);
          break;
        case "campaign":
          result = await executeCampaignStep(location_id, step.config.campaign);
          break;
        case "template":
          result = await executeTemplateStep(location_id, step.config.template);
          break;
        default:
          throw new Error(`Unsupported step type: ${step.type}`);
      }

      ctx.body = result;
    } catch (error: any) {
      console.error("Error executing step:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to execute step",
        details:
          process.env.NODE_ENV === "development" ? error.message : undefined,
      };
    }
  }
);

// Helper functions to execute different types of steps
async function executeListStep(locationId: number, config: any) {
  // Implement list creation logic
  // This should integrate with your existing list creation service
  return { success: true, message: "List created successfully" };
}

async function executeCampaignStep(locationId: number, config: any) {
  // Implement campaign creation logic
  // This should integrate with your existing campaign creation service
  return { success: true, message: "Campaign created successfully" };
}

async function executeTemplateStep(locationId: number, config: any) {
  // Implement template creation logic
  // This should integrate with your existing template creation service
  return { success: true, message: "Template created successfully" };
}

// Helper function to calculate revenue change
function calculateRevenueChange(revenueData: any[]) {
  if (revenueData.length < 2) return 0;

  const midPoint = Math.floor(revenueData.length / 2);
  const previousPeriod = revenueData
    .slice(0, midPoint)
    .reduce((sum, item) => sum + Number(item.revenue), 0);
  const currentPeriod = revenueData
    .slice(midPoint)
    .reduce((sum, item) => sum + Number(item.revenue), 0);

  return ((currentPeriod - previousPeriod) / previousPeriod) * 100;
}

async function calculateProductQuality(locationId: number) {
  const products = await Product.query()
    .where("location_id", locationId)
    .select(
      "product_description",
      "images_urls",
      "percentage_thc",
      "percentage_cbd",
      "category"
    );

  if (products.length === 0) {
    return {
      score: 0,
      breakdown: {
        description: 0,
        images: 0,
        thc_cbd: 0,
        category: 0,
      },
    };
  }

  let totalScore = 0;
  const breakdown = {
    description: 0,
    images: 0,
    thc_cbd: 0,
    category: 0,
  };

  products.forEach((p: any) => {
    let productScore = 0;
    if (p.product_description && p.product_description.length > 20) {
      productScore += 0.4;
      breakdown.description++;
    }
    if (p.images_urls && p.images_urls.length > 0) {
      productScore += 0.2;
      breakdown.images++;
    }
    if (p.percentage_thc > 0 || p.percentage_cbd > 0) {
      productScore += 0.2;
      breakdown.thc_cbd++;
    }
    if (p.category && p.category !== "Uncategorized") {
      productScore += 0.2;
      breakdown.category++;
    }
    totalScore += productScore;
  });

  const numProducts = products.length;
  return {
    score: (totalScore / numProducts) * 100,
    breakdown: {
      description: (breakdown.description / numProducts) * 100,
      images: (breakdown.images / numProducts) * 100,
      thc_cbd: (breakdown.thc_cbd / numProducts) * 100,
      category: (breakdown.category / numProducts) * 100,
    },
  };
}

export default router;
