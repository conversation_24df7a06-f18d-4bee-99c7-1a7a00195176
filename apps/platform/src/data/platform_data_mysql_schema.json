{"bakedBot": [{"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "author", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "json", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "helpful_votes", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "product_id", "is_nullable": "NO", "column_default": null}, {"data_type": "float", "column_name": "rating", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "retailer_id", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "review_date", "is_nullable": "YES", "column_default": null}, {"data_type": "text", "column_name": "text", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "title", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "tinyint", "column_name": "verified_purchase", "is_nullable": "YES", "column_default": null}], "table_name": "reviews"}, {"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "agent_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "chat_id", "is_nullable": "NO", "column_default": null}, {"data_type": "text", "column_name": "content", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "json", "column_name": "metadata", "is_nullable": "YES", "column_default": null}, {"data_type": "enum", "column_name": "role", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "timestamp", "is_nullable": "NO", "column_default": null}], "table_name": "messages"}, {"columns": [{"data_type": "int", "column_name": "batch", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "migration_time", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": null}], "table_name": "migrations"}, {"columns": [{"data_type": "int", "column_name": "index", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "is_locked", "is_nullable": "YES", "column_default": null}], "table_name": "migrations_lock"}, {"columns": [{"data_type": "timestamp", "column_name": "artificial_trial_end", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "auth", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "domain", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "notification_provider_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "tracking_deeplink_mirror_url", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "username", "is_nullable": "YES", "column_default": null}], "table_name": "organizations"}, {"columns": [{"data_type": "decimal", "column_name": "amount_paid_in_cash", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "amount_paid_in_debit", "is_nullable": "YES", "column_default": null}, {"data_type": "date", "column_name": "birth_date", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "budtender_name", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "customer_name", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "customer_type", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "discounted_amount", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "gross_sales", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "decimal", "column_name": "inventory_cost", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "inventory_profit", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "invoice_total", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "location_name", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "loyalty_as_discount", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "loyalty_as_payment", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "master_category", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "net_sales", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "order_date", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "product_name", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "profit_margin", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "returned_amount", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "tax_amount", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "decimal", "column_name": "wholesale_cost", "is_nullable": "YES", "column_default": null}], "table_name": "pos_data"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "event_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "event_type", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "processed_at", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "processed_stripe_events"}, {"columns": [{"data_type": "json", "column_name": "compliance_issues", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "compliance_status", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "last_verified_at", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "product_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "verified_by", "is_nullable": "YES", "column_default": null}], "table_name": "product_compliance_status"}, {"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "batch_id", "is_nullable": "NO", "column_default": null}, {"data_type": "json", "column_name": "cannabinoid_profile", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "cbd_content", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "certificate_url", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "contaminants_data", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "tinyint", "column_name": "heavy_metals_pass", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "lab_name", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "tinyint", "column_name": "microbiological_pass", "is_nullable": "NO", "column_default": null}, {"data_type": "text", "column_name": "notes", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "pesticide_pass", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "product_id", "is_nullable": "NO", "column_default": null}, {"data_type": "tinyint", "column_name": "residual_solvents_pass", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "status", "is_nullable": "YES", "column_default": "pending"}, {"data_type": "json", "column_name": "terpene_profile", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "test_date", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "test_id", "is_nullable": "NO", "column_default": null}, {"data_type": "decimal", "column_name": "thc_content", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "product_test_results"}, {"columns": [{"data_type": "json", "column_name": "ai_enhanced_fields", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "batch_number", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "brand_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "brand_name", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "cann_sku_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "category", "is_nullable": "YES", "column_default": null}, {"data_type": "float", "column_name": "cbd", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "coa_url", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "cultivar", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "display_weight", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "effects", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "enhancement_error", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "enhancement_status", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "estimated_cbd_percentage", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "estimated_thc_percentage", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "external_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "grower_name", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "harvest_date", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "image_url", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "images_urls", "is_nullable": "YES", "column_default": null}, {"data_type": "float", "column_name": "latest_price", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "tinyint", "column_name": "medical", "is_nullable": "YES", "column_default": "0"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "menu_provider", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "meta_sku", "is_nullable": "NO", "column_default": null}, {"data_type": "float", "column_name": "mg_cbd", "is_nullable": "YES", "column_default": null}, {"data_type": "float", "column_name": "mg_thc", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "mood", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "msrp", "is_nullable": "YES", "column_default": null}, {"data_type": "float", "column_name": "percentage_cbd", "is_nullable": "YES", "column_default": null}, {"data_type": "float", "column_name": "percentage_thc", "is_nullable": "YES", "column_default": null}, {"data_type": "text", "column_name": "product_description", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "product_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "product_name", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "product_tags", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "profit_margin", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "quantity_per_package", "is_nullable": "YES", "column_default": null}, {"data_type": "float", "column_name": "rating", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "raw_product_category", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "raw_product_name", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "raw_subcategory", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "raw_weight_string", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "recreational", "is_nullable": "YES", "column_default": "0"}, {"data_type": "decimal", "column_name": "retail_price", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "retailer_id", "is_nullable": "NO", "column_default": null}, {"data_type": "text", "column_name": "review_summary", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "reviews_count", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "slug", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "source", "is_nullable": "NO", "column_default": "weedmaps"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "subcategory", "is_nullable": "YES", "column_default": null}, {"data_type": "float", "column_name": "thc", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "url", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "variants", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "wholesale_price", "is_nullable": "YES", "column_default": null}], "table_name": "products"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "json", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "group", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "tinyint", "column_name": "is_default", "is_nullable": "YES", "column_default": "0"}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "rate_interval", "is_nullable": "YES", "column_default": "second"}, {"data_type": "int", "column_name": "rate_limit", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "type", "is_nullable": "YES", "column_default": ""}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "providers"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "type", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "json", "column_name": "value", "is_nullable": "YES", "column_default": null}], "table_name": "resources"}, {"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "address", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "avatar", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "city", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "country", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "json", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "text", "column_name": "description", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "email", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "hours", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "tinyint", "column_name": "is_active", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "is_open", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "latitude", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "license_type", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "longitude", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "online_ordering", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "phone", "is_nullable": "YES", "column_default": null}, {"data_type": "float", "column_name": "rating", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "retailer_id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "reviews_count", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "serves_medical_users", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "serves_recreational_users", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "services", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "slug", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "state", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "website_url", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "zip_code", "is_nullable": "YES", "column_default": null}], "table_name": "retailers"}, {"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "address", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "city", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "country", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "json", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "deleted_at", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "description", "is_nullable": "YES", "column_default": ""}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "facebook", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "instagram", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "latitude", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "link_wrap_email", "is_nullable": "YES", "column_default": "0"}, {"data_type": "tinyint", "column_name": "link_wrap_push", "is_nullable": "YES", "column_default": "0"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "locale", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "longitude", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": ""}, {"data_type": "int", "column_name": "organization_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "phone", "is_nullable": "NO", "column_default": ""}, {"data_type": "text", "column_name": "pinecone_assistant_error", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "pinecone_assistant_id", "is_nullable": "YES", "column_default": null}, {"data_type": "text", "column_name": "pinecone_assistant_instructions", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "pinecone_assistant_region", "is_nullable": "YES", "column_default": "us"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "pinecone_assistant_status", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "<PERSON><PERSON>e_assistant_updated_at", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "retailer_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "sender_email", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "sender_name", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "social_media_accounts", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "state", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "text_help_message", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "text_opt_out_message", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "timezone", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "twitter", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "website", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "zip", "is_nullable": "YES", "column_default": null}], "table_name": "locations"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "tinyint", "column_name": "result", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "rule_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "user_id", "is_nullable": "NO", "column_default": null}], "table_name": "rule_evaluations"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "group", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "operator", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "parent_uuid", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "path", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "root_uuid", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "type", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "uuid", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "value", "is_nullable": "YES", "column_default": null}], "table_name": "rules"}, {"columns": [{"data_type": "timestamp", "column_name": "canceled_at", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "current_period_end", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "current_period_start", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "customer_id", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "organization_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "price_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "product_id", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "quantity", "is_nullable": "YES", "column_default": "1"}, {"data_type": "enum", "column_name": "status", "is_nullable": "NO", "column_default": "inactive"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "subscription_id", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "trial_end", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "trial_start", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "stripe_subscriptions"}, {"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "bcc", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "cc", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "channel", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "from_email", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "from_name", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "from_phone", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": ""}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "reply_to", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "subscriptions"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": ""}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "tags"}, {"columns": [{"data_type": "int", "column_name": "campaign_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "json", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "locale", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "type", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "templates"}, {"columns": [{"data_type": "int", "column_name": "admin_id", "is_nullable": "NO", "column_default": null}, {"data_type": "text", "column_name": "content", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "tinyint", "column_name": "is_active", "is_nullable": "NO", "column_default": "1"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "user_custom_prompts"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "json", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": ""}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "user_id", "is_nullable": "NO", "column_default": null}], "table_name": "user_events"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "timestamp", "column_name": "deleted_at", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "event_id", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "list_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "user_id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "version", "is_nullable": "YES", "column_default": "0"}], "table_name": "user_list"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "tinyint", "column_name": "state", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "subscription_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "user_id", "is_nullable": "NO", "column_default": null}], "table_name": "user_subscription"}, {"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "anonymous_id", "is_nullable": "YES", "column_default": null}, {"data_type": "date", "column_name": "birth_date", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "json", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "devices", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "email", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "external_id", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "locale", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "phone", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "timezone", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "users"}, {"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "code", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "timestamp", "column_name": "deleted_at", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "organization_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "tinyint", "column_name": "used", "is_nullable": "YES", "column_default": "0"}, {"data_type": "timestamp", "column_name": "used_at", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "used_by", "is_nullable": "YES", "column_default": null}], "table_name": "invite_codes"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "timestamp", "column_name": "deleted_at", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "email", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "first_name", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "text", "column_name": "image_url", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "last_name", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "organization_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "role", "is_nullable": "NO", "column_default": "member"}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "admins"}, {"columns": [{"data_type": "json", "column_name": "capabilities", "is_nullable": "NO", "column_default": null}, {"data_type": "text", "column_name": "description", "is_nullable": "NO", "column_default": null}, {"data_type": "tinyint", "column_name": "disabled", "is_nullable": "YES", "column_default": "0"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "icon", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "json", "column_name": "metadata", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "role", "is_nullable": "NO", "column_default": null}], "table_name": "agents"}, {"columns": [{"data_type": "int", "column_name": "campaign_id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "clicks", "is_nullable": "YES", "column_default": "0"}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "opened_at", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "reference_id", "is_nullable": "NO", "column_default": "0"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "reference_type", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "send_at", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "state", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "user_id", "is_nullable": "NO", "column_default": null}], "table_name": "campaign_sends"}, {"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "channel", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "timestamp", "column_name": "deleted_at", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "delivery", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "exclusion_list_ids", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "json", "column_name": "list_ids", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": ""}, {"data_type": "int", "column_name": "provider_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "send_at", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "send_in_user_timezone", "is_nullable": "YES", "column_default": "0"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "state", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "subscription_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "type", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "campaigns"}, {"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "chat_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "content_type", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "original_name", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "path", "is_nullable": "NO", "column_default": null}, {"data_type": "bigint", "column_name": "size", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "uploaded_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "uploaded_by", "is_nullable": "YES", "column_default": null}], "table_name": "chat_attachments"}, {"columns": [{"data_type": "json", "column_name": "agent_ids", "is_nullable": "NO", "column_default": null}, {"data_type": "json", "column_name": "agents", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "chat_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "json", "column_name": "metadata", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "NO", "column_default": null}, {"data_type": "enum", "column_name": "status", "is_nullable": "YES", "column_default": "active"}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "chats"}, {"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "batch_number", "is_nullable": "NO", "column_default": null}, {"data_type": "decimal", "column_name": "cbc_percent", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "cbd_percent", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "cbda_percent", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "cbg_percent", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "cbga_percent", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "cbn_percent", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "coa_url", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "json", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "lab_license", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "lab_name", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "decimal", "column_name": "metal_arsenic", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "metal_cadmium", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "metal_lead", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "metal_mercury", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "metals_pass", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "microbial_aspergillus", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "microbial_ecoli", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "microbial_salmonella", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "microbials_pass", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "moisture_content", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "overall_pass", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "product_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "product_name", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "product_type", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "production_date", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "sample_id", "is_nullable": "NO", "column_default": null}, {"data_type": "decimal", "column_name": "solvent_butane", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "solvent_ethanol", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "solvent_propane", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "solvents_pass", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "terpene_caryophyllene", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "terpene_humulene", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "terpene_limonene", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "terpene_linalool", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "terpene_myrcene", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "terpene_pinene", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "terpene_terpinolene", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "test_date", "is_nullable": "NO", "column_default": null}, {"data_type": "decimal", "column_name": "thc_percent", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "thca_percent", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "total_cannabinoids", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "total_cbd", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "total_terpenes", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "total_thc", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "decimal", "column_name": "water_activity", "is_nullable": "YES", "column_default": null}, {"data_type": "decimal", "column_name": "yeast_mold_count", "is_nullable": "YES", "column_default": null}], "table_name": "coa_data"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "text", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "deleted_at", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "json", "column_name": "metadata", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "pinecone_file_id", "is_nullable": "YES", "column_default": null}, {"data_type": "text", "column_name": "pinecone_upload_error", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "pinecone_upload_error_time", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "size", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "status", "is_nullable": "NO", "column_default": "pending"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "storage_path", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "type", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "documents"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "entity", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "entity_id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "tag_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "entity_tags"}, {"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "alt", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "extension", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "file_size", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": ""}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "original_name", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "uuid", "is_nullable": "NO", "column_default": null}], "table_name": "images"}, {"columns": [{"data_type": "timestamp", "column_name": "acted_at", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "actions", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "enum", "column_name": "delivery_channel", "is_nullable": "YES", "column_default": "email"}, {"data_type": "text", "column_name": "description", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "executed_at", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "execution_results", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "enum", "column_name": "impact", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "json", "column_name": "plan", "is_nullable": "YES", "column_default": null}, {"data_type": "enum", "column_name": "status", "is_nullable": "YES", "column_default": "new"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "title", "is_nullable": "NO", "column_default": null}, {"data_type": "enum", "column_name": "type", "is_nullable": "NO", "column_default": "general"}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "insights"}, {"columns": [{"data_type": "int", "column_name": "admin_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "timestamp", "column_name": "expires_at", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "ip", "is_nullable": "YES", "column_default": null}, {"data_type": "tinyint", "column_name": "revoked", "is_nullable": "YES", "column_default": "0"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "token", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "user_agent", "is_nullable": "YES", "column_default": null}], "table_name": "access_tokens"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "timestamp", "column_name": "expiration", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "key", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "owner", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "job_locks"}, {"columns": [{"data_type": "int", "column_name": "child_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "json", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "path", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "priority", "is_nullable": "NO", "column_default": "0"}, {"data_type": "int", "column_name": "step_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "journey_step_child"}, {"columns": [{"data_type": "int", "column_name": "child_id", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "json", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "data_key", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "external_id", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "journey_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "next_scheduled_at", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "stats", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "stats_at", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "type", "is_nullable": "YES", "column_default": ""}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "float", "column_name": "x", "is_nullable": "NO", "column_default": "0.00"}, {"data_type": "float", "column_name": "y", "is_nullable": "NO", "column_default": "0.00"}], "table_name": "journey_steps"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "json", "column_name": "data", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "delay_until", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "ended_at", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "entrance_id", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "journey_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "ref", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "step_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "type", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "user_id", "is_nullable": "YES", "column_default": null}], "table_name": "journey_user_step"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "timestamp", "column_name": "deleted_at", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "description", "is_nullable": "YES", "column_default": ""}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": ""}, {"data_type": "tinyint", "column_name": "published", "is_nullable": "YES", "column_default": null}, {"data_type": "json", "column_name": "stats", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "stats_at", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "journeys"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "timestamp", "column_name": "deleted_at", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "tinyint", "column_name": "is_visible", "is_nullable": "YES", "column_default": "1"}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": ""}, {"data_type": "json", "column_name": "rule", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "rule_id", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "state", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "type", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "users_count", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "version", "is_nullable": "YES", "column_default": "0"}], "table_name": "lists"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "key", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "label", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "locales"}, {"columns": [{"data_type": "int", "column_name": "admin_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "timestamp", "column_name": "deleted_at", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "role", "is_nullable": "NO", "column_default": "support"}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "location_admins"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "timestamp", "column_name": "deleted_at", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "description", "is_nullable": "YES", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "role", "is_nullable": "NO", "column_default": "support"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "scope", "is_nullable": "YES", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "value", "is_nullable": "NO", "column_default": null}], "table_name": "location_api_keys"}, {"columns": [{"data_type": "<PERSON><PERSON><PERSON>", "column_name": "address", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "competitor_place_id", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "created_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "decimal", "column_name": "distance_km", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "decimal", "column_name": "latitude", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "decimal", "column_name": "longitude", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "NO", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "location_competitors"}, {"columns": [{"data_type": "timestamp", "column_name": "created_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}, {"data_type": "int", "column_name": "id", "is_nullable": "NO", "column_default": null}, {"data_type": "int", "column_name": "location_id", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "name", "is_nullable": "YES", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "path", "is_nullable": "NO", "column_default": null}, {"data_type": "<PERSON><PERSON><PERSON>", "column_name": "type", "is_nullable": "NO", "column_default": null}, {"data_type": "timestamp", "column_name": "updated_at", "is_nullable": "YES", "column_default": "CURRENT_TIMESTAMP"}], "table_name": "location_rule_paths"}]}