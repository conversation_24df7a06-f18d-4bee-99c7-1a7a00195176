[{"name": "brands", "description": "brands table", "columns": [{"name": "id", "type": "bigint"}, {"name": "brand_id", "type": "text"}, {"name": "brand_name", "type": "text"}, {"name": "brand_name_aliases", "type": "jsonb"}, {"name": "preferred_brand_name", "type": "text"}, {"name": "preferred_image_url", "type": "text"}, {"name": "preferred_website", "type": "text"}, {"name": "aproperhigh_brand_website", "type": "text"}, {"name": "aproperhigh_slug", "type": "text"}, {"name": "aproperhigh_logo_url", "type": "text"}, {"name": "aproperhigh_brand_logo_cann_s3_url", "type": "text"}, {"name": "aproperhigh_twitter_profile_url", "type": "text"}, {"name": "aproperhigh_instagram_profile_url", "type": "text"}, {"name": "aproperhigh_facebook_profile_url", "type": "text"}, {"name": "aproperhigh_linkedin_profile_url", "type": "text"}, {"name": "aproperhigh_description", "type": "text"}, {"name": "aproperhigh_regions", "type": "jsonb"}, {"name": "hubspot_id", "type": "text"}, {"name": "leafly_brand_logo_cann_s3_url", "type": "text"}, {"name": "leafly_brand_website", "type": "text"}, {"name": "leafly_slug", "type": "text"}, {"name": "weedmaps_brand_logo_cann_s3_url", "type": "text"}, {"name": "weedmaps_slug", "type": "text"}, {"name": "wikileaf_brand_logo_cann_s3_url", "type": "text"}, {"name": "wikileaf_brand_website", "type": "text"}, {"name": "wikileaf_instagram_profile_url", "type": "text"}, {"name": "wikileaf_slug", "type": "text"}, {"name": "wikileaf_twitter_profile_url", "type": "text"}, {"name": "dutchie_brand_id", "type": "text"}, {"name": "dutchie_brand_logo_cann_s3_url", "type": "text"}, {"name": "regions", "type": "jsonb"}, {"name": "description", "type": "text"}, {"name": "logo_url", "type": "text"}, {"name": "website_url", "type": "text"}, {"name": "instagram_url", "type": "text"}, {"name": "twitter_url", "type": "text"}, {"name": "facebook_url", "type": "text"}, {"name": "linkedin_url", "type": "text"}, {"name": "social_media", "type": "jsonb"}, {"name": "data", "type": "jsonb"}, {"name": "source", "type": "text"}, {"name": "created_at", "type": "timestamp with time zone"}, {"name": "updated_at", "type": "timestamp with time zone"}]}, {"name": "events", "description": "events table", "columns": [{"name": "id", "type": "bigint"}, {"name": "event_id", "type": "text"}, {"name": "job_id", "type": "text"}, {"name": "user_id", "type": "text"}, {"name": "event_name", "type": "text"}, {"name": "category", "type": "jsonb"}, {"name": "start_time", "type": "text"}, {"name": "timezone", "type": "text"}, {"name": "host", "type": "text"}, {"name": "starting_price", "type": "text"}, {"name": "address", "type": "text"}, {"name": "city", "type": "text"}, {"name": "state", "type": "text"}, {"name": "postal_code", "type": "text"}, {"name": "image", "type": "text"}, {"name": "url", "type": "text"}, {"name": "source", "type": "text"}, {"name": "data", "type": "jsonb"}, {"name": "created_at", "type": "timestamp with time zone"}, {"name": "updated_at", "type": "timestamp with time zone"}, {"name": "location_id", "type": "bigint"}]}, {"name": "product_history", "description": "product_history table", "columns": [{"name": "id", "type": "bigint"}, {"name": "product_id", "type": "text"}, {"name": "retailer_id", "type": "text"}, {"name": "price", "type": "numeric"}, {"name": "previous_price", "type": "numeric"}, {"name": "stock_quantity", "type": "integer"}, {"name": "previous_quantity", "type": "integer"}, {"name": "source", "type": "text"}, {"name": "recorded_at", "type": "timestamp with time zone"}, {"name": "event_type", "type": "text"}, {"name": "metadata", "type": "jsonb"}]}, {"name": "products", "description": "products table", "columns": [{"name": "id", "type": "bigint"}, {"name": "product_id", "type": "text"}, {"name": "meta_sku", "type": "text"}, {"name": "retailer_id", "type": "text"}, {"name": "location_id", "type": "integer"}, {"name": "cann_sku_id", "type": "text"}, {"name": "external_id", "type": "text"}, {"name": "brand_name", "type": "text"}, {"name": "brand_id", "type": "integer"}, {"name": "url", "type": "text"}, {"name": "image_url", "type": "text"}, {"name": "images_urls", "type": "text"}, {"name": "raw_product_name", "type": "text"}, {"name": "product_name", "type": "text"}, {"name": "raw_weight_string", "type": "text"}, {"name": "display_weight", "type": "text"}, {"name": "raw_product_category", "type": "text"}, {"name": "category", "type": "text"}, {"name": "raw_subcategory", "type": "text"}, {"name": "subcategory", "type": "text"}, {"name": "product_tags", "type": "jsonb"}, {"name": "percentage_thc", "type": "numeric"}, {"name": "percentage_cbd", "type": "numeric"}, {"name": "mg_thc", "type": "numeric"}, {"name": "mg_cbd", "type": "numeric"}, {"name": "quantity_per_package", "type": "integer"}, {"name": "medical", "type": "boolean"}, {"name": "recreational", "type": "boolean"}, {"name": "latest_price", "type": "numeric"}, {"name": "menu_provider", "type": "text"}, {"name": "review_summary", "type": "text"}, {"name": "rating", "type": "numeric"}, {"name": "reviews_count", "type": "integer"}, {"name": "product_description", "type": "text"}, {"name": "thc", "type": "numeric"}, {"name": "cbd", "type": "numeric"}, {"name": "variants", "type": "jsonb"}, {"name": "enhancement_status", "type": "text"}, {"name": "mood", "type": "jsonb"}, {"name": "estimated_cbd_percentage", "type": "text"}, {"name": "estimated_thc_percentage", "type": "text"}, {"name": "effects", "type": "jsonb"}, {"name": "enhancement_error", "type": "text"}, {"name": "wholesale_price", "type": "numeric"}, {"name": "retail_price", "type": "numeric"}, {"name": "msrp", "type": "numeric"}, {"name": "profit_margin", "type": "numeric"}, {"name": "grower_name", "type": "text"}, {"name": "cultivar", "type": "text"}, {"name": "batch_number", "type": "text"}, {"name": "harvest_date", "type": "timestamp with time zone"}, {"name": "coa_url", "type": "text"}, {"name": "data", "type": "jsonb"}, {"name": "source", "type": "text"}, {"name": "slug", "type": "text"}, {"name": "created_at", "type": "timestamp with time zone"}, {"name": "updated_at", "type": "timestamp with time zone"}]}, {"name": "retailers", "description": "retailers table", "columns": [{"name": "id", "type": "bigint"}, {"name": "retailer_id", "type": "text"}, {"name": "name", "type": "text"}, {"name": "slug", "type": "text"}, {"name": "address", "type": "text"}, {"name": "city", "type": "text"}, {"name": "state", "type": "text"}, {"name": "zip_code", "type": "text"}, {"name": "country", "type": "text"}, {"name": "phone", "type": "text"}, {"name": "email", "type": "text"}, {"name": "website_url", "type": "text"}, {"name": "latitude", "type": "text"}, {"name": "longitude", "type": "text"}, {"name": "rating", "type": "numeric"}, {"name": "reviews_count", "type": "integer"}, {"name": "description", "type": "text"}, {"name": "hours", "type": "text"}, {"name": "is_open", "type": "boolean"}, {"name": "license_type", "type": "text"}, {"name": "serves_medical_users", "type": "boolean"}, {"name": "serves_recreational_users", "type": "boolean"}, {"name": "services", "type": "jsonb"}, {"name": "online_ordering", "type": "text"}, {"name": "avatar", "type": "text"}, {"name": "is_active", "type": "boolean"}, {"name": "data", "type": "jsonb"}, {"name": "source", "type": "text"}, {"name": "created_at", "type": "timestamp with time zone"}, {"name": "updated_at", "type": "timestamp with time zone"}, {"name": "dispensary_name", "type": "text"}, {"name": "cann_dispensary_slug", "type": "text"}, {"name": "physical_address", "type": "text"}, {"name": "contact_phone", "type": "text"}, {"name": "contact_email", "type": "text"}, {"name": "ratings", "type": "numeric"}, {"name": "payment_methods", "type": "jsonb"}, {"name": "online_services_offered", "type": "jsonb"}, {"name": "brand_ids", "type": "jsonb"}]}, {"name": "scraper_jobs", "description": "scraper_jobs table", "columns": [{"name": "id", "type": "bigint"}, {"name": "job_id", "type": "text"}, {"name": "user_id", "type": "text"}, {"name": "job_type", "type": "text"}, {"name": "status", "type": "text"}, {"name": "retailer_id", "type": "text"}, {"name": "source", "type": "text"}, {"name": "product_count", "type": "integer"}, {"name": "error_message", "type": "text"}, {"name": "parent_job_id", "type": "text"}, {"name": "progress", "type": "jsonb"}, {"name": "metadata", "type": "jsonb"}, {"name": "store_in_db", "type": "boolean"}, {"name": "urls", "type": "ARRAY"}, {"name": "url_type", "type": "text"}, {"name": "created_at", "type": "timestamp with time zone"}, {"name": "updated_at", "type": "timestamp with time zone"}, {"name": "completed_at", "type": "timestamp with time zone"}, {"name": "retailer_count", "type": "integer"}, {"name": "event_count", "type": "integer"}]}, {"name": "spatial_ref_sys", "description": "spatial_ref_sys table", "columns": [{"name": "srid", "type": "integer"}, {"name": "auth_name", "type": "character varying"}, {"name": "auth_srid", "type": "integer"}, {"name": "srtext", "type": "character varying"}, {"name": "proj4text", "type": "character varying"}]}]