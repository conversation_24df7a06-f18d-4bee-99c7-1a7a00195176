import Model, { ModelParams } from "../core/Model";
import { Database } from "../config/database";

export type DocumentStatus = "pending" | "processing" | "completed" | "failed";

export interface DocumentData {
  content?: string;
  content_summary?: string;
  analysis?: {
    agent_contributions: Array<{
      agent_id: string;
      relevance_score: number;
      key_insights: string[];
    }>;
    content_summary?: string;
    completion_percentage?: number;
  };
  error?: string;
  error_time?: string;
  reprocessed_at?: string;
  previous_status?: DocumentStatus;
  vectorization?: {
    status: "completed" | "partial" | "failed";
    chunks_total: number;
    chunks_indexed: number;
    chunks_failed: number;
    completed_at: string;
  };
  vectorization_error?: string;
  vectorization_error_time?: string;
}

export interface DocumentParams {
  location_id: number;
  name: string;
  type: string;
  size: number;
  status?: DocumentStatus;
  data?: DocumentData;
  metadata?: Record<string, any>;
  storage_path?: string;
}

export class Document extends Model {
  declare id: number;
  location_id!: number;
  name!: string;
  type!: string;
  size!: number;
  status!: DocumentStatus;
  data!: DocumentData;
  metadata!: Record<string, any>;
  storage_path?: string;
  pinecone_file_id?: string;
  pinecone_upload_error?: string;
  pinecone_upload_error_time?: string;
  declare created_at: Date;
  declare updated_at: Date;

  static tableName = "documents";
  static jsonAttributes = ["data", "metadata"];

  static async create(params: DocumentParams) {
    return await this.insertAndFetch(params);
  }
}

export type UpdateDocumentParams = Partial<Omit<DocumentParams, "location_id">>;
