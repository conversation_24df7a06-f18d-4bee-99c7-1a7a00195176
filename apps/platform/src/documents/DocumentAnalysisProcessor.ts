interface AgentContribution {
  agent_id: string;
  relevance_score: number;
  key_insights: string[];
}

// Map of agent IDs to their keywords and context
const AGENT_CONTEXT = {
  smokey: {
    keywords: [
      "product",
      "strain",
      "thc",
      "cbd",
      "effects",
      "customer",
      "recommendation",
    ],
    context: "Product information and customer experience",
  },
  craig: {
    keywords: [
      "marketing",
      "campaign",
      "promotion",
      "social media",
      "email",
      "advertising",
    ],
    context: "Marketing and promotional content",
  },
  pops: {
    keywords: [
      "sales",
      "performance",
      "inventory",
      "staff",
      "operations",
      "metrics",
    ],
    context: "Business operations and performance",
  },
  ezal: {
    keywords: [
      "competitor",
      "market",
      "price",
      "trend",
      "analysis",
      "industry",
    ],
    context: "Market and competitor analysis",
  },
  "money-mike": {
    keywords: [
      "financial",
      "profit",
      "revenue",
      "cost",
      "margin",
      "budget",
      "forecast",
    ],
    context: "Financial data and analysis",
  },
  "mrs-parker": {
    keywords: [
      "customer",
      "loyalty",
      "vip",
      "retention",
      "preference",
      "behavior",
    ],
    context: "Customer relations and loyalty",
  },
  deebo: {
    keywords: [
      "compliance",
      "license",
      "regulation",
      "security",
      "quality",
      "testing",
      "lab",
    ],
    context: "Compliance and quality assurance",
  },
};

interface AnalysisResult {
  text: string;
  summary: string;
  insights: string[];
}

export function processAnalysisResults(
  analysis: AnalysisResult
): AgentContribution[] {
  const contributions: AgentContribution[] = [];

  for (const [agentId, context] of Object.entries(AGENT_CONTEXT)) {
    const keywordMatches = context.keywords.filter((keyword) =>
      analysis.text.toLowerCase().includes(keyword.toLowerCase())
    ).length;

    const relevanceScore = Math.min(
      100,
      Math.round((keywordMatches / context.keywords.length) * 100)
    );

    const insights =
      analysis.insights?.filter((insight: string) =>
        context.keywords.some((keyword) =>
          insight.toLowerCase().includes(keyword.toLowerCase())
        )
      ) || [];

    if (relevanceScore > 0 || insights.length > 0) {
      contributions.push({
        agent_id: agentId,
        relevance_score: relevanceScore,
        key_insights: insights.slice(0, 5),
      });
    }
  }

  return contributions;
}
