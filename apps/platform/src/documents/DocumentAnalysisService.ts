import { FileStream } from "../storage/FileStream";
import { Job } from "../queue";
import { Knex } from "knex";
import { Document } from "./Document";
import { OpenAIService } from "../services/openai";
import { extractContent } from "./DocumentContentExtractor";
import { processAnalysisResults } from "./DocumentAnalysisProcessor";
import App from "../app";

// External module imports - using require() instead of import
import PDFParser from "pdf2json";
const xlsx = require("xlsx");
const mammoth = require("mammoth");

interface UploadedFile {
  buffer: Buffer;
  mimetype: string;
  filename: string;
}

interface DocumentFile {
  file: UploadedFile;
  path: string;
}

interface DocumentAnalysisJob extends Job {
  document_id: number;
  location_id: number;
  file: FileStream;
  status: "pending" | "processing" | "completed" | "failed";
  result?: AnalysisResult;
  error?: string;
}

interface AgentContribution {
  agent_id: string;
  relevance_score: number;
  key_insights: string[];
}

interface AnalysisResult {
  status: "pending" | "processing" | "completed" | "failed";
  agent_contributions: Array<{
    agent_id: string;
    relevance_score: number;
    key_insights: string[];
  }>;
  content_summary?: string;
  completion_percentage?: number;
}

export class DocumentAnalysisService {
  static async analyze(params: {
    document_id: number;
    location_id: number;
    file: FileStream;
  }): Promise<void> {
    try {
      // Update document status to processing
      await Document.update(
        (qb: Knex.QueryBuilder) => qb.where({ id: params.document_id }),
        { status: "processing" }
      );

      // Extract text content from document
      const content = await extractContent(params.file);

      // Analyze content with OpenAI
      const openai = new OpenAIService();
      const analysis = await openai.analyze(content);

      // Process AI analysis results
      const agent_contributions = processAnalysisResults(analysis);

      // Calculate completion percentage
      const completion_percentage =
        this.calculateCompletionPercentage(agent_contributions);

      // Update document with results
      await Document.update(
        (qb: Knex.QueryBuilder) => qb.where({ id: params.document_id }),
        {
          status: "completed",
          data: {
            analysis: {
              agent_contributions,
              content_summary: analysis.summary,
              completion_percentage,
            },
          },
        }
      );
    } catch (error) {
      console.error("Document analysis failed:", error);
      await Document.update(
        (qb: Knex.QueryBuilder) => qb.where({ id: params.document_id }),
        {
          status: "failed",
          data: {
            error: error instanceof Error ? error.message : "Unknown error",
          },
        }
      );
    }
  }

  static async getAnalysis(document_id: number): Promise<AnalysisResult> {
    const document = await Document.first((qb: Knex.QueryBuilder) =>
      qb.where({ id: document_id })
    );

    if (!document) {
      throw new Error("Document not found");
    }

    return {
      status: document.status,
      agent_contributions: document.data?.analysis?.agent_contributions || [],
      content_summary: document.data?.analysis?.content_summary || "",
      completion_percentage:
        document.data?.analysis?.completion_percentage || 0,
    };
  }

  private static calculateCompletionPercentage(
    contributions: Array<{ relevance_score: number }>
  ): number {
    if (!contributions.length) return 0;
    const totalScore = contributions.reduce(
      (sum, c) => sum + c.relevance_score,
      0
    );
    const maxPossibleScore = contributions.length * 100;
    return Math.round((totalScore / maxPossibleScore) * 100);
  }
}
