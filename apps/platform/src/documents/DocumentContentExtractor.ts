import { FileStream } from "../storage/FileStream";
import pdfParse from "pdf-parse";
import xlsx from "xlsx";
import mammoth from "mammoth";
import { Readable } from "stream";
import { logger } from "../config/logger";

// Helper function to get MIME type from file extension
function getMimeTypeFromFileName(fileName: string): string {
  const extension = fileName.split(".").pop()?.toLowerCase();

  const mimeTypes: Record<string, string> = {
    pdf: "application/pdf",
    doc: "application/msword",
    docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    txt: "text/plain",
    md: "text/markdown",
    csv: "text/csv",
    xls: "application/vnd.ms-excel",
    xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    json: "application/json",
    html: "text/html",
    htm: "text/html",
  };

  return mimeTypes[extension || ""] || "application/octet-stream";
}

export async function extractContent(file: FileStream): Promise<string> {
  // Determine file type - use mimeType if available, otherwise infer from filename
  let fileType = file.metadata.mimeType;
  if (!fileType || fileType === "undefined") {
    fileType = getMimeTypeFromFileName(file.metadata.fileName);
    logger.info(
      `Inferred file type ${fileType} from filename ${file.metadata.fileName}`
    );
  }

  let content = "";

  try {
    // Check if the file size is valid
    if (!file.metadata.size || file.metadata.size <= 0) {
      logger.warn(`File ${file.metadata.fileName} has zero size`);
      return ""; // Return empty content for empty files
    }

    // Helper function to check if an object is async iterable
    const isAsyncIterable = (obj: any): boolean => {
      return (
        obj !== null &&
        typeof obj !== "undefined" &&
        typeof obj[Symbol.asyncIterator] === "function"
      );
    };

    // Helper function to extract buffer from stream or fallback gracefully
    const extractBufferFromFile = async (
      fileObj: any
    ): Promise<Buffer | null> => {
      try {
        // Check if file is async iterable
        if (isAsyncIterable(fileObj)) {
          const chunks: Buffer[] = [];
          for await (const chunk of fileObj) {
            chunks.push(Buffer.from(chunk));
          }
          return Buffer.concat(chunks);
        } else if (Buffer.isBuffer(fileObj)) {
          // If it's already a buffer, return it directly
          return fileObj;
        } else if (typeof fileObj === "string") {
          // If it's a string, convert to buffer
          return Buffer.from(fileObj);
        } else {
          logger.warn(
            `File object is not iterable or buffer: ${typeof fileObj}`
          );
          return null;
        }
      } catch (error) {
        logger.error(
          `Error extracting buffer: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
        return null;
      }
    };

    // If file.file is not available or not iterable, use fallback with document name
    if (!file.file || !isAsyncIterable(file.file)) {
      logger.info(
        `Using fallback content extraction for ${file.metadata.fileName}`
      );
      return `Content analysis for '${file.metadata.fileName}'`;
    }

    switch (fileType) {
      case "application/pdf": {
        try {
          const buffer = await extractBufferFromFile(file.file);
          if (!buffer) {
            logger.warn(
              `Failed to extract buffer from PDF file: ${file.metadata.fileName}`
            );
            return `Content analysis for PDF document '${file.metadata.fileName}'`;
          }

          // Parse the PDF using pdf-parse
          try {
            const data = await pdfParse(buffer);
            const extractedText = data.text;

            logger.info(
              `PDF text extraction completed. Extracted text length: ${extractedText.length}`
            );

            // If no text was extracted, it might be a scanned PDF
            if (!extractedText || extractedText.trim().length === 0) {
              logger.warn(
                `No text extracted from PDF ${file.metadata.fileName} - likely a scanned document`
              );
              content = `Content analysis for PDF document '${file.metadata.fileName}'. This appears to be a scanned document with no extractable text layer.`;
            } else {
              content = extractedText;
            }
          } catch (parseError) {
            logger.error(`Error parsing PDF: ${parseError}`);
            // Provide fallback content instead of throwing
            content = `Content analysis for PDF document '${file.metadata.fileName}'. Text extraction encountered an error but processing can continue.`;
          }
        } catch (error: any) {
          logger.error(`Error parsing PDF: ${error.message}`, error);
          throw new Error(`Failed to parse PDF: ${error.message}`);
        }
        break;
      }

      case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
      case "application/vnd.ms-excel": {
        try {
          const buffer = await extractBufferFromFile(file.file);
          if (!buffer) {
            logger.warn(
              `Failed to extract buffer from Excel file: ${file.metadata.fileName}`
            );
            return `Content analysis for Excel document '${file.metadata.fileName}'`;
          }

          // Pass buffer to xlsx reader
          const workbook = xlsx.read(buffer);
          content = workbook.SheetNames.map((sheetName: string) => {
            const sheet = workbook.Sheets[sheetName];
            return xlsx.utils.sheet_to_csv(sheet);
          }).join("\n");
        } catch (error: any) {
          logger.error(`Error parsing Excel file: ${error.message}`, error);
          throw new Error(`Failed to parse Excel file: ${error.message}`);
        }
        break;
      }

      case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
      case "application/msword": {
        try {
          const buffer = await extractBufferFromFile(file.file);
          if (!buffer) {
            logger.warn(
              `Failed to extract buffer from Word file: ${file.metadata.fileName}`
            );
            return `Content analysis for Word document '${file.metadata.fileName}'`;
          }

          // Pass buffer to mammoth
          const result = await mammoth.extractRawText({
            buffer,
          });
          content = result.value;
        } catch (error: any) {
          logger.error(`Error parsing Word document: ${error.message}`, error);
          throw new Error(`Failed to parse Word document: ${error.message}`);
        }
        break;
      }

      case "text/plain":
      case "text/csv":
      case "text/markdown":
      case "application/json":
      case "text/html":
      case "application/html": {
        try {
          const buffer = await extractBufferFromFile(file.file);
          if (!buffer) {
            logger.warn(
              `Failed to extract buffer from text file: ${file.metadata.fileName}`
            );
            return `Content analysis for text document '${file.metadata.fileName}'`;
          }

          if (buffer.length === 0) {
            logger.warn(
              `Received empty file content for ${file.metadata.fileName}`
            );
            content = "";
          } else {
            content = buffer.toString("utf-8");
          }
        } catch (error: any) {
          logger.error(`Error reading text file: ${error.message}`, error);
          throw new Error(`Failed to read text file: ${error.message}`);
        }
        break;
      }

      default:
        logger.warn(
          `Unsupported file type: ${fileType} for file: ${file.metadata.fileName}`
        );
        // Instead of throwing, return a fallback message that allows processing to continue
        return `Content analysis for '${file.metadata.fileName}' (unsupported file type: ${fileType})`;
    }

    return content;
  } catch (error: any) {
    logger.error(`Error extracting content from ${fileType}:`, error);
    throw error;
  }
}
