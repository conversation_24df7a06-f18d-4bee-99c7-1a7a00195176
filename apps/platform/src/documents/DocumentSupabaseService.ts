import { Document } from "./Document";
import { FileStream } from "../storage/FileStream";
import { extractContent } from "./DocumentContentExtractor";
import { SupabaseService } from "../supabase/SupabaseService";
import { logger } from "../config/logger";
import Storage from "../storage/Storage";
import App from "../app";

/**
 * Service to handle documents for Supabase integration
 */
export class DocumentSupabaseService {
  private supabaseService: SupabaseService;

  constructor() {
    this.supabaseService = new SupabaseService({
      url:
        process.env.SUPABASE_URL || "https://nixatetkmouteapspmth.supabase.co",
      key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
      bucket: process.env.SUPABASE_BUCKET || "location-data",
    });
  }

  /**
   * Process a document and upload its content to Supabase for search
   */
  async processDocument(
    document: Document,
    locationId: number | string
  ): Promise<Document> {
    logger.info(`Processing document ${document.id} for Supabase integration`, {
      documentId: document.id,
      locationId,
    });

    try {
      // Get the file from storage
      const storage = new Storage({
        driver: App.main.env.storage.driver as any,
        ...(App.main.env.storage as any),
      });

      const filePath = document.storage_path
        ? document.storage_path.split("/").pop() || ""
        : "";

      // Extract content from the document based on its type
      const contentResult = await this.extractDocumentContent(document);

      // Update the document with extracted content
      document.data.content = contentResult.content || "";
      document.status = "completed";
      await Document.update((qb) => qb.where({ id: document.id }), {
        data: document.data,
        status: document.status,
      });

      // Prepare document metadata for Supabase
      const documentData = {
        id: document.id,
        name: document.name,
        size: document.size,
        type: document.type,
        url: document.storage_path ? Storage.url(document.storage_path) : "",
        content: document.data.content,
        documentId: document.id,
        status: document.status,
        created_at: document.created_at.toISOString(),
        updated_at: document.updated_at.toISOString(),
      };

      // Upload to Supabase
      await this.supabaseService.uploadLocationData(locationId, {
        documents: [documentData],
      });

      return document;
    } catch (error: any) {
      logger.error(
        `Error processing document ${document.id} for Supabase: ${error.message}`,
        { documentId: document.id, locationId, error: error.stack }
      );

      // Update document status to error
      document.status = "failed";
      document.data.error = error.message;
      await Document.update((qb) => qb.where({ id: document.id }), {
        status: document.status,
        data: document.data,
      });

      throw error;
    }
  }

  /**
   * Process multiple documents in bulk
   */
  async processDocumentsBulk(
    documents: Document[],
    locationId: number | string
  ): Promise<{ processed: number; failed: number }> {
    logger.info(
      `Processing ${documents.length} documents in bulk for Supabase integration`,
      { locationId, count: documents.length }
    );

    const results = {
      processed: 0,
      failed: 0,
    };

    // Prepare document metadata for all documents
    const documentsData = await Promise.all(
      documents.map(async (document) => {
        try {
          // Extract content if not already present
          if (!document.data.content) {
            const contentResult = await this.extractDocumentContent(document);
            document.data.content = contentResult.content || "";
            document.status = "completed";
            await Document.update((qb) => qb.where({ id: document.id }), {
              data: document.data,
              status: document.status,
            });
          }

          results.processed++;

          return {
            id: document.id,
            name: document.name,
            size: document.size,
            type: document.type,
            url: document.storage_path
              ? Storage.url(document.storage_path)
              : "",
            content: document.data.content,
            documentId: document.id,
            status: document.status,
            created_at: document.created_at.toISOString(),
            updated_at: document.updated_at.toISOString(),
          };
        } catch (error: any) {
          logger.error(
            `Error processing document ${document.id} in bulk: ${error.message}`,
            { documentId: document.id, locationId, error: error.stack }
          );

          // Update document status to error
          document.status = "failed";
          document.data.error = error.message;
          await Document.update((qb) => qb.where({ id: document.id }), {
            status: document.status,
            data: document.data,
          });

          results.failed++;
          return null;
        }
      })
    );

    // Filter out failed documents
    const validDocuments = documentsData.filter((doc) => doc !== null);

    if (validDocuments.length > 0) {
      // Upload to Supabase
      await this.supabaseService.uploadLocationData(locationId, {
        documents: validDocuments,
      });
    }

    return results;
  }

  /**
   * Extract content from a document based on its type
   */
  private async extractDocumentContent(
    document: Document
  ): Promise<{ content: string | null; error?: string }> {
    try {
      // Since we don't have direct access to the file,
      // we'll return content if it exists or a placeholder
      if (document.data && document.data.content) {
        return { content: document.data.content };
      }

      return { content: `[Content for document ${document.id} not available]` };
    } catch (error: any) {
      logger.error(
        `Error extracting content from document ${document.id}: ${error.message}`,
        { documentId: document.id, error: error.stack }
      );
      return { content: null, error: error.message };
    }
  }
}

// Export singleton instance
export const documentSupabaseService = new DocumentSupabaseService();
