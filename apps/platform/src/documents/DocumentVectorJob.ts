import { Job } from "../queue";
import { Document } from "./Document";
import { DocumentVectorService } from "./DocumentVectorService";
import { logger } from "../config/logger";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";

interface DocumentVectorParams {
  document_id: number;
  location_id: number;
  retry_count?: number;
}

export default class DocumentVectorJob extends Job {
  static $name = "document-vector-job";

  document_id!: number;
  location_id!: number;
  retry_count?: number;

  static from(params: DocumentVectorParams) {
    logger.info(`Creating document vectorization job`, {
      document_id: params.document_id,
      location_id: params.location_id,
      retry_count: params.retry_count || 0,
    });

    return new this({
      document_id: params.document_id,
      location_id: params.location_id,
      retry_count: params.retry_count || 0,
    });
  }

  static async handler({
    document_id,
    location_id,
    retry_count = 0,
  }: DocumentVectorParams) {
    logger.info(
      `Starting document vectorization for document ${document_id} in location ${location_id}`
    );

    const MAX_RETRIES = 3;
    const jobType = "document_vectorization";

    try {
      // Initialize the vector service
      await DocumentVectorService.initialize();

      // Start tracking this job
      OnboardingJobTracker.startJob(location_id, jobType);

      // Get the document
      const document = await Document.first((qb) =>
        qb.where({ id: document_id, location_id })
      );

      if (!document) {
        throw new Error(`Document ${document_id} not found`);
      }

      // Process the document
      await DocumentVectorService.processDocument(document_id, location_id);

      // Mark job as complete
      OnboardingJobTracker.completeJob(location_id, jobType);

      logger.info(
        `Document vectorization completed successfully for document ${document_id}`
      );

      return true;
    } catch (error) {
      logger.error(
        `Document vectorization failed for document ${document_id}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
        error
      );

      // If we haven't exceeded max retries, try again
      if (retry_count < MAX_RETRIES) {
        logger.info(
          `Retrying document vectorization for document ${document_id} (attempt ${
            retry_count + 1
          } of ${MAX_RETRIES})`
        );

        // Queue a retry with exponential backoff
        const backoff = Math.pow(2, retry_count) * 1000; // 1s, 2s, 4s...
        await DocumentVectorJob.from({
          document_id,
          location_id,
          retry_count: retry_count + 1,
        })
          .delay(backoff)
          .queue();
      } else {
        // Max retries exceeded, mark job as failed
        OnboardingJobTracker.failJob(
          location_id,
          jobType,
          error instanceof Error ? error : new Error(String(error))
        );
      }

      return false;
    }
  }
}
