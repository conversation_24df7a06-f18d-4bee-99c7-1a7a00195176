import { logger } from "../config/logger";
import { checkIntegrationStatus } from "../integrations";
import { AgentsConfig, Agent } from "../chats/models/types";
import { Insight, storeInsights, getInsights } from "./InsightService";
import App from "../app";
import * as agentsConfigJson from "../agents/agents.json";

interface EnrichedAgent extends Agent {
  unlocked: boolean;
  missingRequirements: string[];
}

async function getIntegrationStatus(
  app: App,
  locationId: string
): Promise<Record<string, { connected: boolean; status?: string }>> {
  try {
    if (!app.db) {
      throw new Error("Database is not initialized");
    }
    const integrations = await checkIntegrationStatus(app.db, locationId);

    const result: Record<string, { connected: boolean; status?: string }> = {};

    Object.entries(integrations).forEach(([key, value]) => {
      if (value) {
        const integration: { connected: boolean; status?: string } = {
          connected: !!value.connected,
        };

        const valueAny = value as any;
        if (
          Object.prototype.hasOwnProperty.call(valueAny, "status") &&
          valueAny.status
        ) {
          integration.status = String(valueAny.status);
        }

        result[key] = integration;
      } else {
        result[key] = { connected: false };
      }
    });

    return result;
  } catch (error) {
    logger.error("Error checking integration status:", error);
    return {};
  }
}

async function isAgentUnlocked(
  agentId: string,
  locationId: string,
  integrations: Record<string, { connected: boolean; status?: string }>
): Promise<{ unlocked: boolean; missingRequirements: string[] }> {
  const config = agentsConfigJson as unknown as AgentsConfig;
  const agentConfig = config.agents[agentId];

  if (!agentConfig || agentConfig.disabled) {
    return { unlocked: false, missingRequirements: ["Agent is disabled"] };
  }

  const missingRequirements =
    agentConfig.requirements?.required
      ?.filter((req) => !integrations[req]?.connected)
      .map((req) => `Missing ${req} integration`) || [];

  return {
    unlocked: missingRequirements.length === 0,
    missingRequirements,
  };
}

async function getAgentById(
  app: App,
  id: string,
  locationId: string
): Promise<EnrichedAgent | undefined> {
  const config = agentsConfigJson as unknown as AgentsConfig;
  const agentConfig = config.agents[id];
  if (!agentConfig || agentConfig.disabled) return undefined;

  const integrationStatus = await getIntegrationStatus(app, locationId);
  const { unlocked, missingRequirements } = await isAgentUnlocked(
    id,
    locationId,
    integrationStatus
  );

  return {
    id,
    name: agentConfig.name,
    role: agentConfig.role,
    description: agentConfig.description,
    icon: agentConfig.icon || "🤖",
    capabilities: agentConfig.capabilities,
    disabled: false,
    metadata: {},
    unlocked,
    missingRequirements,
  };
}

export class AgentBasedInsightService {
  private app: App;

  constructor(app: App) {
    this.app = app;
  }

  /**
   * Generate insights using available agents and their specific capabilities
   */
  async generateInsightsWithAgents(
    locationId: number,
    timeframe: string = "30d",
    model?: string
  ): Promise<Insight[]> {
    try {
      logger.info({
        message: "Starting agent-based insight generation",
        locationId,
        timeframe,
      });

      // Get all available agents for this location
      const availableAgents = await this.getAvailableAgents(locationId);

      if (availableAgents.length === 0) {
        logger.warn({
          message: "No available agents found for insight generation",
          locationId,
        });
        return [];
      }

      logger.info({
        message: "Found available agents",
        locationId,
        agentCount: availableAgents.length,
        agents: availableAgents.map((a) => ({
          id: a.id,
          name: a.name,
          unlocked: a.unlocked,
        })),
      });

      // Generate insights from each available agent
      const allInsights: Partial<Insight>[] = [];

      for (const agent of availableAgents) {
        if (!agent.unlocked) {
          logger.debug({
            message: "Skipping locked agent",
            agentId: agent.id,
            agentName: agent.name,
            missingRequirements: agent.missingRequirements,
          });
          continue;
        }

        try {
          logger.info({
            message: "Generating insights with agent",
            agentId: agent.id,
            agentName: agent.name,
            agentRole: agent.role,
          });

          const agentInsights = await this.generateInsightsForAgent(
            agent,
            locationId,
            timeframe,
            model
          );

          if (agentInsights.length > 0) {
            logger.info({
              message: "Agent generated insights",
              agentId: agent.id,
              agentName: agent.name,
              insightCount: agentInsights.length,
            });

            allInsights.push(...agentInsights);
          } else {
            logger.debug({
              message: "Agent generated no insights",
              agentId: agent.id,
              agentName: agent.name,
            });
          }
        } catch (error) {
          logger.error({
            message: "Error generating insights for agent",
            agentId: agent.id,
            agentName: agent.name,
            error,
          });
          // Continue with other agents even if one fails
        }
      }

      if (allInsights.length === 0) {
        logger.warn({
          message: "No insights generated by any agent",
          locationId,
          availableAgentCount: availableAgents.length,
        });
        return [];
      }

      // Store insights with agent attribution
      const insertResult = await storeInsights(locationId, allInsights);

      // Fetch the actual insights that were stored
      const storedInsights = await getInsights(locationId);

      logger.info({
        message: "Successfully stored agent-generated insights",
        locationId,
        totalInsights: storedInsights.length,
        agentDistribution: this.getAgentDistribution(allInsights),
      });

      return storedInsights;
    } catch (error) {
      logger.error({
        message: "Error in agent-based insight generation",
        locationId,
        error,
      });
      throw error;
    }
  }

  /**
   * Get all available agents for a location
   */
  private async getAvailableAgents(
    locationId: number
  ): Promise<EnrichedAgent[]> {
    const config = agentsConfigJson as unknown as AgentsConfig;
    const allAgents = Object.keys(config.agents);

    const enrichedAgents: EnrichedAgent[] = [];

    for (const agentId of allAgents) {
      const agent = await getAgentById(
        this.app,
        agentId,
        locationId.toString()
      );
      if (agent) {
        enrichedAgents.push(agent);
      }
    }

    return enrichedAgents;
  }

  /**
   * Generate insights for a specific agent using their domain expertise
   */
  private async generateInsightsForAgent(
    agent: EnrichedAgent,
    locationId: number,
    timeframe: string,
    model?: string
  ): Promise<Partial<Insight>[]> {
    const config = agentsConfigJson as unknown as AgentsConfig;
    const agentConfig = config.agents[agent.id];

    if (!agentConfig) {
      logger.warn({
        message: "Agent config not found",
        agentId: agent.id,
      });
      return [];
    }

    // Create insight prompts based on agent's domain and capabilities
    const insights = await this.generateDomainSpecificInsights(
      agent,
      agentConfig,
      locationId,
      timeframe,
      model
    );

    // Add agent attribution to all insights
    return insights.map((insight) => ({
      ...insight,
      agent_id: agent.id,
      agent_name: agent.name,
    }));
  }

  /**
   * Generate domain-specific insights based on agent capabilities and data access
   */
  private async generateDomainSpecificInsights(
    agent: EnrichedAgent,
    agentConfig: any,
    locationId: number,
    timeframe: string,
    model?: string
  ): Promise<Partial<Insight>[]> {
    const insights: Partial<Insight>[] = [];

    // Map agent capabilities to insight generation strategies
    switch (agent.name) {
      case "SMOKEY":
        insights.push(
          ...(await this.generateSmokeyInsights(locationId, timeframe))
        );
        break;
      case "CRAIG":
        insights.push(
          ...(await this.generateCraigInsights(locationId, timeframe))
        );
        break;
      case "POPS":
        insights.push(
          ...(await this.generatePopsInsights(locationId, timeframe))
        );
        break;
      case "EZAL":
        insights.push(
          ...(await this.generateEzalInsights(locationId, timeframe))
        );
        break;
      case "MONEY MIKE":
        insights.push(
          ...(await this.generateMoneyMikeInsights(locationId, timeframe))
        );
        break;
      case "MRS. PARKER":
        insights.push(
          ...(await this.generateMrsParkerInsights(locationId, timeframe))
        );
        break;
      case "DEEBO":
        insights.push(
          ...(await this.generateDeeboInsights(locationId, timeframe))
        );
        break;
      default:
        logger.debug({
          message: "No specific insight generation strategy for agent",
          agentName: agent.name,
        });
    }

    return insights;
  }

  // Agent-specific insight generation methods
  private async generateSmokeyInsights(
    locationId: number,
    timeframe: string
  ): Promise<Partial<Insight>[]> {
    const insights: Partial<Insight>[] = [];

    try {
      // Import database models
      const { PosData } = await import("../pos/PosData");
      const { subDays } = await import("date-fns");

      const endDate = new Date();
      const days = parseInt(timeframe.replace("d", ""), 10) || 30;
      const startDate = subDays(endDate, days);

      // Analyze product performance for SMOKEY's insights
      const productPerformance = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .groupBy("product_name", "master_category")
        .select([
          "product_name",
          "master_category",
          PosData.raw("COUNT(*) as sales_count"),
          PosData.raw("SUM(net_sales) as total_revenue"),
          PosData.raw("AVG(net_sales) as avg_price"),
          PosData.raw("SUM(inventory_profit) as total_profit"),
        ])
        .orderBy("sales_count", "desc");

      if (productPerformance.length > 0) {
        const topProduct = productPerformance[0];
        const bottomProducts = productPerformance.slice(-5);

        // Top performing product insight
        insights.push({
          title: `${topProduct.product_name} is your top performer`,
          description: `Your best-selling product generated ${
            topProduct.sales_count
          } sales and $${Number(topProduct.total_revenue).toFixed(
            2
          )} in revenue over the last ${days} days. Consider promoting similar products or expanding this line.`,
          impact: "high" as const,
          type: "automation" as const,
          delivery_channel: "email" as const,
          actions: JSON.stringify([
            "Create upsell campaign for complementary products",
            "Increase inventory for high-demand items",
            "Develop customer education content about this product category",
          ]),
        });

        // Underperforming products insight
        if (bottomProducts.length > 0) {
          const underperformers = bottomProducts.filter(
            (p: any) => Number(p.sales_count) < 3
          );
          if (underperformers.length > 0) {
            insights.push({
              title: "Low-performing products need attention",
              description: `${underperformers.length} products have fewer than 3 sales in ${days} days. Consider promotional strategies, price adjustments, or staff training on these items.`,
              impact: "medium" as const,
              type: "campaign" as const,
              delivery_channel: "email" as const,
              actions: JSON.stringify([
                "Create promotional campaigns for slow movers",
                "Train staff on product benefits and selling points",
                "Review pricing strategy for underperformers",
              ]),
            });
          }
        }
      }

      // Category performance analysis
      const categoryPerformance = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .groupBy("master_category")
        .select([
          "master_category",
          PosData.raw("COUNT(*) as category_sales"),
          PosData.raw("SUM(net_sales) as category_revenue"),
        ])
        .orderBy("category_revenue", "desc");

      if (categoryPerformance.length > 1) {
        const topCategory = categoryPerformance[0];
        const categoryShare =
          (Number(topCategory.category_revenue) /
            categoryPerformance.reduce(
              (sum: number, cat: any) => sum + Number(cat.category_revenue),
              0
            )) *
          100;

        if (categoryShare > 50) {
          insights.push({
            title: `${topCategory.master_category} dominates your sales`,
            description: `${
              topCategory.master_category
            } accounts for ${categoryShare.toFixed(
              1
            )}% of your revenue. Consider diversifying your product mix or capitalizing on this strength with expanded offerings.`,
            impact: "medium" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Expand successful category with new products",
              "Create cross-selling opportunities with other categories",
              "Develop category-specific marketing campaigns",
            ]),
          });
        }
      }
    } catch (error) {
      logger.error({
        message: "Error generating SMOKEY insights",
        error,
        locationId,
        timeframe,
      });
    }

    return insights;
  }

  private async generateCraigInsights(
    locationId: number,
    timeframe: string
  ): Promise<Partial<Insight>[]> {
    const insights: Partial<Insight>[] = [];

    try {
      // Import database models
      const { default: Campaign } = await import("../campaigns/Campaign");
      const { subDays } = await import("date-fns");

      const endDate = new Date();
      const days = parseInt(timeframe.replace("d", ""), 10) || 30;
      const startDate = subDays(endDate, days);

      // Analyze campaign performance
      const campaigns: any[] = (await Campaign.query()
        .where("location_id", locationId)
        .where("created_at", ">=", startDate)
        .where("created_at", "<=", endDate)
        .select("*")) as any;

      if (campaigns.length === 0) {
        insights.push({
          title: "No recent marketing campaigns detected",
          description: `You haven't run any campaigns in the last ${days} days. Regular customer communication can increase retention and drive sales.`,
          impact: "high" as const,
          type: "campaign" as const,
          delivery_channel: "email" as const,
          actions: JSON.stringify([
            "Create a welcome series for new customers",
            "Set up weekly product highlight campaigns",
            "Develop a customer retention email sequence",
          ]),
        });
      } else {
        // Analyze campaign performance metrics
        let totalSent = 0;
        let totalOpens = 0;
        let totalClicks = 0;
        let emailCampaigns = 0;
        let smsCampaigns = 0;

        campaigns.forEach((campaign: any) => {
          if (campaign.delivery && typeof campaign.delivery === "object") {
            totalSent += campaign.delivery.sent || 0;
            totalOpens += campaign.delivery.opens || 0;
            totalClicks += campaign.delivery.clicks || 0;

            if (campaign.channel === "email") emailCampaigns++;
            if (campaign.channel === "text") smsCampaigns++;
          }
        });

        const openRate = totalSent > 0 ? (totalOpens / totalSent) * 100 : 0;
        const clickRate = totalOpens > 0 ? (totalClicks / totalOpens) * 100 : 0;

        if (openRate < 20) {
          insights.push({
            title: "Email open rates need improvement",
            description: `Your average email open rate is ${openRate.toFixed(
              1
            )}%, below the cannabis industry average of 25%. Consider improving subject lines and send timing.`,
            impact: "medium" as const,
            type: "campaign" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "A/B test different subject line styles",
              "Optimize send times based on customer behavior",
              "Segment audiences for more targeted messaging",
            ]),
          });
        }

        if (clickRate < 3 && totalOpens > 0) {
          insights.push({
            title: "Email engagement could be stronger",
            description: `Your click-through rate is ${clickRate.toFixed(
              1
            )}%. Improve email content relevance and calls-to-action to drive more engagement.`,
            impact: "medium" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Redesign email templates with stronger CTAs",
              "Create more personalized content based on purchase history",
              "Add product recommendations to increase relevance",
            ]),
          });
        }

        // Channel mix analysis
        if (emailCampaigns > 0 && smsCampaigns === 0) {
          insights.push({
            title: "Consider adding SMS to your marketing mix",
            description: `You're only using email campaigns. SMS typically has higher open rates (90%+) and can complement your email strategy effectively.`,
            impact: "medium" as const,
            type: "campaign" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Set up SMS campaigns for urgent promotions",
              "Create opt-in workflows for SMS subscribers",
              "Test SMS for flash sales and limited-time offers",
            ]),
          });
        }
      }
    } catch (error) {
      logger.error({
        message: "Error generating CRAIG insights",
        error,
        locationId,
        timeframe,
      });
    }

    return insights;
  }

  private async generatePopsInsights(
    locationId: number,
    timeframe: string
  ): Promise<Partial<Insight>[]> {
    const insights: Partial<Insight>[] = [];

    try {
      // Import database models
      const { PosData } = await import("../pos/PosData");
      const { subDays } = await import("date-fns");

      const endDate = new Date();
      const days = parseInt(timeframe.replace("d", ""), 10) || 30;
      const startDate = subDays(endDate, days);
      const previousStartDate = subDays(startDate, days);

      // Current period metrics
      const currentMetrics = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          PosData.raw("COUNT(*) as total_transactions"),
          PosData.raw("SUM(net_sales) as total_revenue"),
          PosData.raw("AVG(net_sales) as avg_order_value"),
          PosData.raw("COUNT(DISTINCT customer_name) as unique_customers"),
          PosData.raw("COUNT(DISTINCT DATE(order_date)) as active_days"),
        ])
        .first();

      // Previous period for comparison
      const previousMetrics = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", previousStartDate)
        .where("order_date", "<", startDate)
        .select([
          PosData.raw("COUNT(*) as total_transactions"),
          PosData.raw("SUM(net_sales) as total_revenue"),
          PosData.raw("AVG(net_sales) as avg_order_value"),
          PosData.raw("COUNT(DISTINCT customer_name) as unique_customers"),
        ])
        .first();

      if (currentMetrics && previousMetrics) {
        const revenueChange =
          ((Number(currentMetrics.total_revenue) -
            Number(previousMetrics.total_revenue)) /
            Number(previousMetrics.total_revenue)) *
          100;
        const transactionChange =
          ((Number(currentMetrics.total_transactions) -
            Number(previousMetrics.total_transactions)) /
            Number(previousMetrics.total_transactions)) *
          100;
        const customerChange =
          ((Number(currentMetrics.unique_customers) -
            Number(previousMetrics.unique_customers)) /
            Number(previousMetrics.unique_customers)) *
          100;

        // Revenue trend analysis
        if (revenueChange < -10) {
          insights.push({
            title: "Revenue decline needs immediate attention",
            description: `Revenue dropped ${Math.abs(revenueChange).toFixed(
              1
            )}% compared to the previous ${days} days. Current revenue: $${Number(
              currentMetrics.total_revenue
            ).toFixed(2)}`,
            impact: "high" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Analyze top-performing products from previous periods",
              "Launch customer win-back campaigns",
              "Review and optimize pricing strategy",
            ]),
          });
        } else if (revenueChange > 15) {
          insights.push({
            title: "Strong revenue growth detected",
            description: `Revenue increased ${revenueChange.toFixed(
              1
            )}% compared to the previous period. Current revenue: $${Number(
              currentMetrics.total_revenue
            ).toFixed(2)}`,
            impact: "high" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Identify and replicate successful strategies",
              "Increase inventory for high-performing products",
              "Scale successful marketing campaigns",
            ]),
          });
        }

        // Customer acquisition analysis
        if (customerChange < -5) {
          insights.push({
            title: "Customer acquisition declining",
            description: `Unique customers decreased ${Math.abs(
              customerChange
            ).toFixed(1)}% from ${Number(
              previousMetrics.unique_customers
            )} to ${Number(currentMetrics.unique_customers)}`,
            impact: "high" as const,
            type: "campaign" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Launch new customer acquisition campaigns",
              "Implement referral programs",
              "Increase local marketing efforts",
            ]),
          });
        }

        // Daily transaction analysis
        const avgTransactionsPerDay =
          Number(currentMetrics.total_transactions) /
          Number(currentMetrics.active_days);
        if (avgTransactionsPerDay < 10) {
          insights.push({
            title: "Low daily transaction volume",
            description: `Averaging ${avgTransactionsPerDay.toFixed(
              1
            )} transactions per day. Consider strategies to increase foot traffic and purchase frequency.`,
            impact: "medium" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Implement happy hour or daily specials",
              "Create loyalty programs to encourage repeat visits",
              "Improve in-store experience and product displays",
            ]),
          });
        }
      }

      // Peak hours analysis
      const hourlyData = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          PosData.raw("HOUR(order_date) as hour"),
          PosData.raw("COUNT(*) as transaction_count"),
          PosData.raw("SUM(net_sales) as hourly_revenue"),
        ])
        .groupBy("hour")
        .orderBy("transaction_count", "desc");

      if (hourlyData.length > 0) {
        const peakHour = hourlyData[0];
        const slowHours = hourlyData.filter(
          (h: any) =>
            Number(h.transaction_count) <
            Number(peakHour.transaction_count) * 0.3
        );

        if (slowHours.length > 0) {
          insights.push({
            title: "Optimize slow business hours",
            description: `Peak hour (${peakHour.hour}:00) has ${peakHour.transaction_count} transactions, while ${slowHours.length} hours have very low activity. Consider targeted promotions for slow periods.`,
            impact: "medium" as const,
            type: "campaign" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Create time-specific promotions for slow hours",
              "Adjust staffing based on traffic patterns",
              "Launch happy hour or off-peak discounts",
            ]),
          });
        }
      }
    } catch (error) {
      logger.error({
        message: "Error generating POPS insights",
        error,
        locationId,
        timeframe,
      });
    }

    return insights;
  }

  private async generateEzalInsights(
    locationId: number,
    timeframe: string
  ): Promise<Partial<Insight>[]> {
    const insights: Partial<Insight>[] = [];

    try {
      // Import database models
      const { PosData } = await import("../pos/PosData");
      const { subDays } = await import("date-fns");

      const endDate = new Date();
      const days = parseInt(timeframe.replace("d", ""), 10) || 30;
      const startDate = subDays(endDate, days);

      // Pricing analysis across categories
      const pricingAnalysis = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .groupBy("master_category")
        .select([
          "master_category",
          PosData.raw("AVG(net_sales) as avg_price"),
          PosData.raw("MIN(net_sales) as min_price"),
          PosData.raw("MAX(net_sales) as max_price"),
          PosData.raw("COUNT(*) as sales_count"),
          PosData.raw("STDDEV(net_sales) as price_variance"),
        ])
        .orderBy("sales_count", "desc");

      if (pricingAnalysis.length > 0) {
        // High variance pricing insight
        const highVarianceCategories = pricingAnalysis.filter(
          (cat: any) => Number(cat.price_variance) > Number(cat.avg_price) * 0.5
        );
        if (highVarianceCategories.length > 0) {
          const category = highVarianceCategories[0];
          insights.push({
            title: `Price inconsistency in ${category.master_category}`,
            description: `${
              category.master_category
            } shows high price variance ($${Number(category.min_price).toFixed(
              2
            )} - $${Number(category.max_price).toFixed(
              2
            )}). Consider standardizing pricing or creating clear product tiers.`,
            impact: "medium" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Review and standardize pricing within categories",
              "Create clear product tiers (budget, premium, luxury)",
              "Analyze competitor pricing for benchmarking",
            ]),
          });
        }

        // Premium vs budget analysis
        const avgOrderValue =
          pricingAnalysis.reduce(
            (sum: number, cat: any) => sum + Number(cat.avg_price),
            0
          ) / pricingAnalysis.length;
        const premiumCategories = pricingAnalysis.filter(
          (cat: any) => Number(cat.avg_price) > avgOrderValue * 1.5
        );
        const budgetCategories = pricingAnalysis.filter(
          (cat: any) => Number(cat.avg_price) < avgOrderValue * 0.7
        );

        if (premiumCategories.length > 0 && budgetCategories.length > 0) {
          insights.push({
            title: "Balanced pricing strategy opportunity",
            description: `You have both premium (${premiumCategories
              .map((c: any) => c.master_category)
              .join(", ")}) and budget (${budgetCategories
              .map((c: any) => c.master_category)
              .join(
                ", "
              )}) options. Consider creating bundles to increase average order value.`,
            impact: "medium" as const,
            type: "campaign" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Create premium + budget product bundles",
              "Develop upselling strategies for budget customers",
              "Market premium products with value propositions",
            ]),
          });
        }
      }

      // Market positioning analysis based on customer behavior
      const customerSegmentation = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          "customer_name",
          PosData.raw("COUNT(*) as visit_frequency"),
          PosData.raw("AVG(net_sales) as avg_spend"),
          PosData.raw("SUM(net_sales) as total_spend"),
        ])
        .groupBy("customer_name")
        .orderBy("total_spend", "desc")
        .limit(100);

      if (customerSegmentation.length > 10) {
        const topCustomers = customerSegmentation.slice(0, 5);
        const topCustomerSpend = topCustomers.reduce(
          (sum: number, c: any) => sum + Number(c.total_spend),
          0
        );
        const totalSpend = customerSegmentation.reduce(
          (sum: number, c: any) => sum + Number(c.total_spend),
          0
        );
        const topCustomerPercentage = (topCustomerSpend / totalSpend) * 100;

        if (topCustomerPercentage > 40) {
          insights.push({
            title: "High customer concentration risk",
            description: `Your top 5 customers account for ${topCustomerPercentage.toFixed(
              1
            )}% of revenue. Consider expanding your customer base to reduce dependency.`,
            impact: "high" as const,
            type: "campaign" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Launch new customer acquisition campaigns",
              "Implement referral programs with existing customers",
              "Expand marketing reach to new demographics",
            ]),
          });
        }

        // High-value customer insights
        const highValueCustomers = customerSegmentation.filter(
          (c: any) => Number(c.avg_spend) > 100
        );
        if (highValueCustomers.length > 0) {
          insights.push({
            title: "VIP customer program opportunity",
            description: `${highValueCustomers.length} customers spend over $100 per visit on average. Consider creating a VIP program to retain these valuable customers.`,
            impact: "high" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Create VIP customer program with exclusive benefits",
              "Offer early access to new products for high spenders",
              "Implement personalized communication for top customers",
            ]),
          });
        }
      }
    } catch (error) {
      logger.error({
        message: "Error generating EZAL insights",
        error,
        locationId,
        timeframe,
      });
    }

    return insights;
  }

  private async generateMoneyMikeInsights(
    locationId: number,
    timeframe: string
  ): Promise<Partial<Insight>[]> {
    const insights: Partial<Insight>[] = [];

    try {
      // Import database models
      const { PosData } = await import("../pos/PosData");
      const { subDays } = await import("date-fns");

      const endDate = new Date();
      const days = parseInt(timeframe.replace("d", ""), 10) || 30;
      const startDate = subDays(endDate, days);

      // Profit margin analysis by product
      const profitAnalysis = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .groupBy("product_name", "master_category")
        .select([
          "product_name",
          "master_category",
          PosData.raw("COUNT(*) as sales_count"),
          PosData.raw("SUM(net_sales) as total_revenue"),
          PosData.raw("SUM(inventory_profit) as total_profit"),
          PosData.raw("SUM(inventory_cost) as total_cost"),
          PosData.raw("AVG(inventory_profit / net_sales) * 100 as avg_margin"),
        ])
        .having("sales_count", ">=", 3) // Only products with meaningful sales
        .orderBy("total_profit", "desc");

      if (profitAnalysis.length > 0) {
        // Low margin products
        const lowMarginProducts = profitAnalysis.filter(
          (p: any) => Number(p.avg_margin) < 20
        );
        if (lowMarginProducts.length > 0) {
          const totalLowMarginRevenue = lowMarginProducts.reduce(
            (sum: number, p: any) => sum + Number(p.total_revenue),
            0
          );
          insights.push({
            title: `${lowMarginProducts.length} products have low profit margins`,
            description: `Products generating $${totalLowMarginRevenue.toFixed(
              2
            )} in revenue have margins below 20%. Consider repricing or finding better suppliers.`,
            impact: "high" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Review supplier costs for low-margin products",
              "Adjust pricing on products with margins below 20%",
              "Focus marketing on higher-margin alternatives",
            ]),
          });
        }

        // High profit winners
        const highMarginProducts = profitAnalysis.filter(
          (p: any) => Number(p.avg_margin) > 50 && Number(p.sales_count) >= 5
        );
        if (highMarginProducts.length > 0) {
          insights.push({
            title: "High-margin profit drivers identified",
            description: `${highMarginProducts.length} products have excellent margins (>50%) and good sales volume. These are your profit drivers to promote.`,
            impact: "high" as const,
            type: "campaign" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Increase marketing spend on high-margin products",
              "Train staff to recommend profitable items",
              "Create prominent displays for profit drivers",
            ]),
          });
        }

        // Category profitability
        const categoryProfits: Record<
          string,
          { totalProfit: number; totalRevenue: number; count: number }
        > = {};
        profitAnalysis.forEach((product: any) => {
          const category = product.master_category;
          if (!categoryProfits[category]) {
            categoryProfits[category] = {
              totalProfit: 0,
              totalRevenue: 0,
              count: 0,
            };
          }
          categoryProfits[category].totalProfit += Number(product.total_profit);
          categoryProfits[category].totalRevenue += Number(
            product.total_revenue
          );
          categoryProfits[category].count += Number(product.sales_count);
        });

        const sortedCategories = Object.entries(categoryProfits)
          .map(([category, data]: [string, any]) => ({
            category,
            margin: (data.totalProfit / data.totalRevenue) * 100,
            profit: data.totalProfit,
            revenue: data.totalRevenue,
          }))
          .sort((a, b) => b.margin - a.margin);

        if (sortedCategories.length > 1) {
          const bestCategory = sortedCategories[0];
          const worstCategory = sortedCategories[sortedCategories.length - 1];

          insights.push({
            title: `${bestCategory.category} is your most profitable category`,
            description: `${
              bestCategory.category
            } has a ${bestCategory.margin.toFixed(1)}% margin vs ${
              worstCategory.category
            } at ${worstCategory.margin.toFixed(
              1
            )}%. Focus resources on high-margin categories.`,
            impact: "medium" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              `Expand ${bestCategory.category} product selection`,
              "Shift marketing budget toward profitable categories",
              `Analyze why ${worstCategory.category} has low margins`,
            ]),
          });
        }
      }

      // Cash flow analysis
      const paymentMethods = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          PosData.raw("SUM(amount_paid_in_cash) as total_cash"),
          PosData.raw("SUM(amount_paid_in_debit) as total_debit"),
          PosData.raw("SUM(invoice_total) as total_sales"),
          PosData.raw("COUNT(*) as total_transactions"),
        ])
        .first();

      if (paymentMethods) {
        const cashPercentage =
          (Number(paymentMethods.total_cash) /
            Number(paymentMethods.total_sales)) *
          100;
        const debitPercentage =
          (Number(paymentMethods.total_debit) /
            Number(paymentMethods.total_sales)) *
          100;

        if (cashPercentage > 70) {
          insights.push({
            title: "High cash dependency noted",
            description: `${cashPercentage.toFixed(
              1
            )}% of payments are cash. Consider promoting digital payments to improve transaction efficiency and tracking.`,
            impact: "medium" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Implement digital payment incentives",
              "Train staff on promoting card payments",
              "Consider loyalty points for non-cash payments",
            ]),
          });
        }
      }
    } catch (error) {
      logger.error({
        message: "Error generating MONEY MIKE insights",
        error,
        locationId,
        timeframe,
      });
    }

    return insights;
  }

  private async generateMrsParkerInsights(
    locationId: number,
    timeframe: string
  ): Promise<Partial<Insight>[]> {
    const insights: Partial<Insight>[] = [];

    try {
      // Import database models
      const { PosData } = await import("../pos/PosData");
      const { subDays } = await import("date-fns");

      const endDate = new Date();
      const days = parseInt(timeframe.replace("d", ""), 10) || 30;
      const startDate = subDays(endDate, days);
      const previousStartDate = subDays(startDate, days);

      // Customer loyalty analysis
      const customerAnalysis = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .groupBy("customer_name")
        .select([
          "customer_name",
          "customer_type",
          PosData.raw("COUNT(*) as visit_count"),
          PosData.raw("SUM(net_sales) as total_spent"),
          PosData.raw("AVG(net_sales) as avg_order"),
          PosData.raw("SUM(loyalty_as_discount) as loyalty_savings"),
          PosData.raw("MAX(order_date) as last_visit"),
          PosData.raw("MIN(order_date) as first_visit"),
        ])
        .orderBy("total_spent", "desc");

      if (customerAnalysis.length > 0) {
        // Identify VIP customers (top 10% by spending)
        const vipThreshold = Math.ceil(customerAnalysis.length * 0.1);
        const vipCustomers = customerAnalysis.slice(0, vipThreshold);
        const vipRevenue = vipCustomers.reduce(
          (sum: number, c: any) => sum + Number(c.total_spent),
          0
        );
        const totalRevenue = customerAnalysis.reduce(
          (sum: number, c: any) => sum + Number(c.total_spent),
          0
        );
        const vipRevenuePercentage = (vipRevenue / totalRevenue) * 100;

        insights.push({
          title: `Top ${vipThreshold} customers drive ${vipRevenuePercentage.toFixed(
            1
          )}% of revenue`,
          description: `Your VIP customers are crucial to your success. These high-value relationships need special attention and personalized service.`,
          impact: "high" as const,
          type: "campaign" as const,
          delivery_channel: "email" as const,
          actions: JSON.stringify([
            "Create personalized VIP communication campaigns",
            "Offer exclusive products or early access to VIP customers",
            "Implement personal shopping services for top spenders",
          ]),
        });

        // Customer retention analysis
        const returningCustomers = customerAnalysis.filter(
          (c: any) => c.customer_type === "returning"
        ).length;
        const newCustomers = customerAnalysis.filter(
          (c: any) => c.customer_type === "new"
        ).length;
        const retentionRate =
          (returningCustomers / (returningCustomers + newCustomers)) * 100;

        if (retentionRate < 60) {
          insights.push({
            title: "Customer retention needs improvement",
            description: `Only ${retentionRate.toFixed(
              1
            )}% of customers are returning. Implement strategies to improve customer loyalty and repeat visits.`,
            impact: "high" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Create a comprehensive loyalty program",
              "Send follow-up emails after first purchase",
              "Implement customer feedback and satisfaction surveys",
            ]),
          });
        }

        // Loyalty program effectiveness
        const loyaltyUsers = customerAnalysis.filter(
          (c: any) => Number(c.loyalty_savings) > 0
        );
        const loyaltyUsage =
          (loyaltyUsers.length / customerAnalysis.length) * 100;
        const avgLoyaltySavings =
          loyaltyUsers.reduce(
            (sum: number, c: any) => sum + Number(c.loyalty_savings),
            0
          ) / loyaltyUsers.length;

        if (loyaltyUsage < 30) {
          insights.push({
            title: "Low loyalty program participation",
            description: `Only ${loyaltyUsage.toFixed(
              1
            )}% of customers use loyalty rewards. Increase awareness and make the program more attractive.`,
            impact: "medium" as const,
            type: "campaign" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Launch loyalty program awareness campaign",
              "Train staff to promote loyalty signups",
              "Simplify loyalty program redemption process",
            ]),
          });
        } else if (avgLoyaltySavings > 0) {
          insights.push({
            title: "Loyalty program driving customer value",
            description: `${loyaltyUsage.toFixed(
              1
            )}% of customers use loyalty rewards, saving an average of $${avgLoyaltySavings.toFixed(
              2
            )}. Consider expanding program benefits.`,
            impact: "medium" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Add tier-based loyalty benefits for higher spenders",
              "Create special loyalty member events",
              "Implement referral bonuses for loyalty members",
            ]),
          });
        }

        // At-risk customer identification
        const now = new Date();
        const atRiskCustomers = customerAnalysis.filter((c: any) => {
          const daysSinceLastVisit =
            (now.getTime() - new Date(c.last_visit).getTime()) /
            (1000 * 60 * 60 * 24);
          return daysSinceLastVisit > 30 && Number(c.total_spent) > 100; // High-value customers who haven't visited in 30+ days
        });

        if (atRiskCustomers.length > 0) {
          insights.push({
            title: `${atRiskCustomers.length} high-value customers at risk`,
            description: `Valuable customers haven't visited in 30+ days. Implement win-back campaigns to re-engage these customers before losing them permanently.`,
            impact: "high" as const,
            type: "campaign" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Create personalized win-back email campaigns",
              "Offer special discounts to lapsed high-value customers",
              "Send personalized offers based on past purchase history",
            ]),
          });
        }

        // Frequency analysis
        const frequentCustomers = customerAnalysis.filter(
          (c: any) => Number(c.visit_count) >= 5
        );
        const occasionalCustomers = customerAnalysis.filter(
          (c: any) => Number(c.visit_count) >= 2 && Number(c.visit_count) < 5
        );

        if (occasionalCustomers.length > frequentCustomers.length) {
          insights.push({
            title: "Opportunity to convert occasional to frequent customers",
            description: `${occasionalCustomers.length} customers visit occasionally (2-4 times) vs ${frequentCustomers.length} frequent visitors. Focus on increasing visit frequency.`,
            impact: "medium" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Create frequency-based reward campaigns",
              "Send regular product updates to occasional customers",
              "Implement subscription or auto-delivery programs",
            ]),
          });
        }
      }
    } catch (error) {
      logger.error({
        message: "Error generating MRS. PARKER insights",
        error,
        locationId,
        timeframe,
      });
    }

    return insights;
  }

  private async generateDeeboInsights(
    locationId: number,
    timeframe: string
  ): Promise<Partial<Insight>[]> {
    const insights: Partial<Insight>[] = [];

    try {
      // Import database models
      const { PosData } = await import("../pos/PosData");
      const { subDays } = await import("date-fns");

      const endDate = new Date();
      const days = parseInt(timeframe.replace("d", ""), 10) || 30;
      const startDate = subDays(endDate, days);

      // Transaction compliance analysis
      const complianceAnalysis = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          PosData.raw("COUNT(*) as total_transactions"),
          PosData.raw(
            "SUM(CASE WHEN net_sales < 0 THEN 1 ELSE 0 END) as negative_transactions"
          ),
          PosData.raw(
            "SUM(CASE WHEN tax_amount = 0 THEN 1 ELSE 0 END) as no_tax_transactions"
          ),
          PosData.raw("AVG(tax_amount / net_sales) * 100 as avg_tax_rate"),
          PosData.raw("COUNT(DISTINCT customer_name) as unique_customers"),
          PosData.raw("MAX(net_sales) as highest_single_transaction"),
        ])
        .first();

      if (complianceAnalysis) {
        // Check for compliance red flags
        const negativeTransactionRate =
          (Number(complianceAnalysis.negative_transactions) /
            Number(complianceAnalysis.total_transactions)) *
          100;
        if (negativeTransactionRate > 5) {
          insights.push({
            title: "High rate of negative transactions detected",
            description: `${negativeTransactionRate.toFixed(
              1
            )}% of transactions are negative (returns/voids). Review return procedures and staff training on proper transaction handling.`,
            impact: "high" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Review return and void procedures with staff",
              "Implement additional approval steps for negative transactions",
              "Analyze patterns in returns to identify systemic issues",
            ]),
          });
        }

        // Tax compliance check
        const noTaxRate =
          (Number(complianceAnalysis.no_tax_transactions) /
            Number(complianceAnalysis.total_transactions)) *
          100;
        if (noTaxRate > 1) {
          insights.push({
            title: "Tax compliance issue detected",
            description: `${noTaxRate.toFixed(
              1
            )}% of transactions have no tax applied. Ensure all sales are properly taxed according to local regulations.`,
            impact: "high" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Review POS system tax configuration",
              "Train staff on proper tax application procedures",
              "Audit recent transactions for tax compliance",
            ]),
          });
        }

        // Average tax rate validation
        const avgTaxRate = Number(complianceAnalysis.avg_tax_rate);
        if (avgTaxRate < 5 || avgTaxRate > 30) {
          // Typical cannabis tax rates
          insights.push({
            title: "Unusual tax rate detected",
            description: `Average tax rate is ${avgTaxRate.toFixed(
              1
            )}%, which may be outside normal ranges. Verify tax settings and compliance with local rates.`,
            impact: "medium" as const,
            type: "automation" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Verify current local tax rates and regulations",
              "Update POS system tax configuration if needed",
              "Consult with compliance specialist if rates seem incorrect",
            ]),
          });
        }

        // Large transaction monitoring
        const highestTransaction = Number(
          complianceAnalysis.highest_single_transaction
        );
        if (highestTransaction > 1000) {
          insights.push({
            title: "Large transaction requires compliance review",
            description: `Highest single transaction was $${highestTransaction.toFixed(
              2
            )}. Large transactions may require additional documentation or reporting.`,
            impact: "medium" as const,
            type: "general" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Review large transaction documentation requirements",
              "Ensure proper customer verification for high-value sales",
              "Check if transaction reporting thresholds were met",
            ]),
          });
        }
      }

      // Customer verification patterns
      const customerPatterns = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .groupBy("customer_name")
        .select([
          "customer_name",
          PosData.raw("COUNT(*) as transaction_count"),
          PosData.raw("SUM(net_sales) as total_spent"),
          PosData.raw("COUNT(DISTINCT DATE(order_date)) as visit_days"),
        ])
        .having("transaction_count", ">", 10) // Customers with many transactions
        .orderBy("transaction_count", "desc");

      if (customerPatterns.length > 0) {
        const suspiciousPatterns = customerPatterns.filter(
          (c: any) => Number(c.transaction_count) > Number(c.visit_days) * 3 // More than 3 transactions per day on average
        );

        if (suspiciousPatterns.length > 0) {
          insights.push({
            title: "Unusual customer transaction patterns detected",
            description: `${suspiciousPatterns.length} customers show high transaction frequency that may require compliance review. Monitor for potential structuring or unusual activity.`,
            impact: "high" as const,
            type: "general" as const,
            delivery_channel: "email" as const,
            actions: JSON.stringify([
              "Review high-frequency customer transactions manually",
              "Verify customer information and purchase patterns",
              "Document any unusual activity for compliance records",
            ]),
          });
        }
      }

      // Daily limits and purchase tracking
      const dailyLimits = await PosData.query()
        .where("location_id", locationId)
        .where("order_date", ">=", startDate)
        .where("order_date", "<=", endDate)
        .select([
          PosData.raw("DATE(order_date) as transaction_date"),
          PosData.raw("customer_name"),
          PosData.raw("COUNT(*) as daily_transactions"),
          PosData.raw("SUM(net_sales) as daily_total"),
        ])
        .groupBy("transaction_date", "customer_name")
        .having("daily_transactions", ">", 3) // Multiple transactions per day per customer
        .orderBy("daily_transactions", "desc");

      if (dailyLimits.length > 0) {
        insights.push({
          title: "Multiple daily transactions detected",
          description: `${dailyLimits.length} instances of customers making multiple transactions per day. Ensure compliance with daily purchase limits and proper documentation.`,
          impact: "medium" as const,
          type: "general" as const,
          delivery_channel: "email" as const,
          actions: JSON.stringify([
            "Review daily purchase limit compliance procedures",
            "Train staff on recognizing and preventing limit circumvention",
            "Implement better daily purchase tracking in POS system",
          ]),
        });
      }
    } catch (error) {
      logger.error({
        message: "Error generating DEEBO insights",
        error,
        locationId,
        timeframe,
      });
    }

    return insights;
  }

  /**
   * Helper method to get agent distribution for logging
   */
  private getAgentDistribution(
    insights: Partial<Insight>[]
  ): Record<string, number> {
    const distribution: Record<string, number> = {};

    insights.forEach((insight: any) => {
      if (insight.agent_name) {
        distribution[insight.agent_name] =
          (distribution[insight.agent_name] || 0) + 1;
      }
    });

    return distribution;
  }
}

// Lazy instantiation to avoid circular dependency issues
let _agentBasedInsightService: AgentBasedInsightService | null = null;

export function getAgentBasedInsightService(): AgentBasedInsightService {
  if (!_agentBasedInsightService) {
    if (!App.main) {
      throw new Error(
        "App.main is not initialized. Make sure the app is properly started before using AgentBasedInsightService."
      );
    }
    _agentBasedInsightService = new AgentBasedInsightService(App.main);
  }
  return _agentBasedInsightService;
}

// For backward compatibility, export a getter
export const agentBasedInsightService = {
  get instance() {
    return getAgentBasedInsightService();
  },
};
