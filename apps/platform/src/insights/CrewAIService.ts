/* eslint-disable indent */
import { z } from "zod";
import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";
import {
  StructuredOutputParser,
  JsonOutputFunctionsParser,
} from "langchain/output_parsers";
import {
  Insight,
  getExistingResources,
  isInsightActionable,
  createJourneysFromPlan,
} from "./InsightService";
import { subscriptionForChannel } from "../subscriptions/SubscriptionService";
import { getProviderByGroup } from "../providers/ProviderRepository";
import Location from "../locations/Location";
import { RequestError } from "../core/errors";
import { PosData } from "../pos/PosData";
import { subDays, startOfDay, endOfDay } from "date-fns";
import {
  AutomationPlanSchema,
  InsightsArraySchema,
  ListConfigSchema,
  CampaignConfigSchema,
  TemplateConfigSchema,
  JourneyConfigSchema,
  RuleSchema,
  ListStepSchema,
  CampaignStepSchema,
  TemplateStepSchema,
} from "./schemas";
import { ImageGenerationService } from "./ImageGenerationService";
import { imageDownloadService } from "../storage/ImageDownloadService";
import { Readable } from "stream";
import Image from "../storage/Image";
import App from "../app";
import Storage from "../storage/Storage";
import { uuid } from "../utilities";

import promptsJson from "../prompts/prompts.json";
import { AutomationPromptService } from "./PromptService";
import { zodToJsonSchema } from "zod-to-json-schema";
import { ChatPromptTemplate } from "@langchain/core/prompts";

// Import agent modules
import {
  ContextAgent,
  ValidationAgent,
  EvaluatorAgent,
  VotingAgent,
  ContentGenerationAgent,
  GeneralAgent,
  SimpleParser,
} from "./agents";

// Import ChatAIServiceWithTools for leveraging existing agent capabilities
import { ChatAIServiceWithTools } from "../chats/ChatAIServiceWithTools";
import { OpenAI } from "openai";
import { AgentAvailabilityService } from "../agents/AgentAvailabilityService";
import { Knex } from "knex";

/**
 * Type definitions extracted from Zod schemas to ensure consistency
 * between validation schemas and type checking.
 */
// Basic configuration types
interface ChannelConfig {
  subscription_id: number;
  provider_id: number;
}

interface ChannelConfigs {
  email: ChannelConfig | null;
  text: ChannelConfig | null;
}

// Core types derived from schemas
type ListRule = z.infer<typeof RuleSchema>;
type ListConfig = z.infer<typeof ListConfigSchema>;
type CampaignConfig = z.infer<typeof CampaignConfigSchema>;
type TemplateConfig = z.infer<typeof TemplateConfigSchema>;
type TemplateData = TemplateConfig["data"];

interface AutomationItem {
  type: "list" | "campaign" | "template" | "journey";
  config: {
    list?: ListConfig;
    campaign?: CampaignConfig;
    template?: TemplateConfig;
    journey?: z.infer<typeof JourneyConfigSchema>["config"]["journey"];
  };
}

class ValidationError extends Error {
  constructor(message: string, public stepType?: string, public details?: any) {
    super(message);
    this.name = "ValidationError";
  }
}

// Defined prompt templates
const InsightPrompts = {
  base_prompt:
    "You are an AI marketing expert analyzing POS data to identify insights for marketing campaigns.",
  examples: "Example insights: [...]",
};

/**
 * Main CrewAI Service that orchestrates the agents
 */
export class CrewAIService {
  private contextAgent: ContextAgent;
  private validationAgent: ValidationAgent;
  private evaluatorAgent: EvaluatorAgent;
  private votingAgent: VotingAgent;
  private contentAgent: ContentGenerationAgent;
  private generalAgent: GeneralAgent;
  private chatAIService?: ChatAIServiceWithTools;
  private initialized: boolean = false;
  private imageGenerationService: ImageGenerationService | null = null;
  private automationPromptService: AutomationPromptService | null = null;

  /**
   * Get the image generation service instance
   */
  private getImageGenerationService(): ImageGenerationService {
    if (!this.imageGenerationService) {
      this.imageGenerationService = new ImageGenerationService();
    }
    return this.imageGenerationService;
  }

  /**
   * Get the automation prompt service instance
   */
  private getAutomationPromptService(): AutomationPromptService {
    if (!this.automationPromptService) {
      this.automationPromptService = new AutomationPromptService("automation");
    }
    return this.automationPromptService;
  }

  constructor() {
    this.contextAgent = new ContextAgent();
    this.validationAgent = new ValidationAgent();
    this.evaluatorAgent = new EvaluatorAgent();
    this.votingAgent = new VotingAgent();
    this.contentAgent = new ContentGenerationAgent();
    this.generalAgent = new GeneralAgent();
    // Don't initialize ChatAIServiceWithTools in constructor
    // It will be initialized lazily when needed
  }

  private async initialize() {
    if (this.initialized) return;

    try {
      // Initialize ChatAIServiceWithTools with OpenAI client
      const openaiApiKey = process.env.OPENAI_API_KEY;
      if (!openaiApiKey) {
        console.error("OPENAI_API_KEY is not set");
        throw new Error("OpenAI API key is required");
      }

      const openai = new OpenAI({
        apiKey: openaiApiKey,
      });

      // Get database connection safely with retry logic
      let db;
      let retries = 3;
      while (retries > 0) {
        try {
          if (!App.main) {
            throw new Error("App.main is not initialized yet");
          }
          if (!App.main.db) {
            throw new Error("App.main.db is not initialized yet");
          }
          db = App.main.db;
          break;
        } catch (error) {
          retries--;
          if (retries === 0) {
            console.error(
              "App.main or App.main.db is not initialized after retries"
            );
            throw new Error("Database connection not available after retries");
          }
          console.warn(
            `Database not ready, retrying... (${retries} attempts left)`
          );
          // Wait a bit before retrying
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      this.chatAIService = new ChatAIServiceWithTools(
        openai,
        db as Knex<any, any[]>,
        {
          defaultModel: "gpt-4.1-mini",
          defaultTemperature: 0.7,
          contextWindow: 15,
        }
      );

      this.initialized = true;
      console.log("CrewAIService successfully initialized");
    } catch (error) {
      console.error("Failed to initialize CrewAIService:", error);
      throw error;
    }
  }

  private async ensureChatAIService(): Promise<ChatAIServiceWithTools> {
    if (!this.chatAIService) {
      await this.initialize();
      if (!this.chatAIService) {
        throw new Error("Failed to initialize ChatAIServiceWithTools");
      }
    }
    return this.chatAIService;
  }

  async generateInsights(
    locationId: number,
    timeframe: string = "30d",
    model?: string
  ): Promise<Insight[]> {
    try {
      // Ensure ChatAIService is initialized and get instance
      const chatAIService = await this.ensureChatAIService();

      // Gather context
      const context = await this.contextAgent.gatherContext(
        locationId,
        timeframe
      );

      // Get available agent capabilities for this location
      const availableAgents = await chatAIService.getAvailableAgents(
        locationId
      );
      const availableCapabilities = new Set<string>();

      // Collect all capabilities from available agents
      availableAgents.forEach((agent) => {
        agent.capabilities.forEach((capability) => {
          availableCapabilities.add(capability);
        });
      });

      // Convert set to array
      const capabilities = Array.from(availableCapabilities);

      console.log(
        `Available capabilities for location ${locationId}:`,
        capabilities
      );

      // Define LLM
      const llm = new ChatOpenAI({
        modelName: model || "gpt-4.1-mini",
        temperature: 0.7,
        openAIApiKey: process.env.OPENAI_API_KEY,
      });

      // Create an object wrapper around the array to fix JSON schema issue
      const wrapperSchema = z.object({
        insights: InsightsArraySchema,
      });

      // Convert to JSON schema
      const jsonSchema = zodToJsonSchema(wrapperSchema);

      // Create a modified function definition using the wrapped schema
      const functionDef = {
        name: "generate_insights",
        description: "Generate marketing insights from the provided data",
        parameters: jsonSchema,
      };

      // Interactive insights generation process
      // Using progressively refined insights through a series of questions
      const questions = [
        "What are the top-selling products in the last month?",
        "Which customer segments have shown the highest engagement?",
        "Are there any notable sales trends or patterns?",
        "What marketing opportunities can be identified from the data?",
        "What are potential automation opportunities based on customer behavior?",
      ];

      // Start with basic context
      const enhancedContext = {
        location: context.location,
        metrics: context.metrics,
        existingResources: context.existingResources,
        capabilities,
        additionalData: {} as Record<string, string>,
      };

      // Gather additional insights by asking questions
      for (const question of questions) {
        try {
          // Skip questions that require capabilities we don't have
          if (
            question.toLowerCase().includes("product") &&
            !capabilities.some((c) => c.toLowerCase().includes("product"))
          ) {
            console.log(
              `Skipping question about products due to missing capability`
            );
            continue;
          }

          if (
            question.toLowerCase().includes("customer") &&
            !capabilities.some((c) => c.toLowerCase().includes("customer"))
          ) {
            console.log(
              `Skipping question about customers due to missing capability`
            );
            continue;
          }

          // Use ChatAIServiceWithTools to analyze the question and get relevant context
          const analysis = await chatAIService.analyzeIntent(question);

          // Simulate a message object
          const message = {
            content: question,
            metadata: {
              requiredContextTypes: analysis.entities.requiredContextTypes,
              intent: analysis.intent,
              confidence: analysis.confidence,
            },
            role: "user" as const,
            id: parseInt(uuid().replace(/\D/g, "").substring(0, 8)), // Convert to number like we did for chat id
            chat_id: uuid(), // Add required chat_id
            timestamp: new Date(), // Add required timestamp
          };

          // Find the best agent to answer this question
          const bestAgent = await chatAIService.selectAgent(
            analysis,
            locationId,
            availableAgents
          );

          // Create a simulated chat object to pass to generateResponse
          const simulatedChat = {
            id: parseInt(uuid().replace(/\D/g, "").substring(0, 8)), // Convert to number for compatibility
            location_id: locationId,
            created_at: new Date(),
            updated_at: new Date(),
            metadata: {
              context_timeframe: timeframe,
              task: "generate insights",
            },
            chat_id: uuid(),
            agent_ids: [bestAgent.id],
            status: "active" as const,
            name: "Insight Generation Session",
          };

          // Generate a response from the agent
          const response = await chatAIService.generateResponse(
            simulatedChat,
            message,
            bestAgent
          );

          // Store the response in our enhanced context
          enhancedContext.additionalData[question] = response;

          console.log(`Added context for question: "${question}"`);
        } catch (error) {
          console.warn(
            `Error gathering context for question "${question}":`,
            error
          );
          // Continue with other questions even if one fails
        }
      }

      // Format the prompt with enhanced context
      const prompt = `
        You are an AI marketing expert analyzing POS data to identify insights for marketing campaigns.

        CONTEXT:
        Location: ${enhancedContext.location.name}
        Timeframe: ${timeframe} (${
        enhancedContext.metrics.timeframe.start
      } to ${enhancedContext.metrics.timeframe.end})
        
        POS Data:
        ${JSON.stringify(enhancedContext.metrics.current, null, 2)}
        
        Existing resources:
        Lists: ${enhancedContext.existingResources.lists.length}
        Campaigns: ${enhancedContext.existingResources.campaigns.length}
        
        Available Capabilities:
        ${capabilities.join(", ")}
        
        Additional Analysis:
        ${Object.entries(enhancedContext.additionalData)
          .map(([q, a]) => `Q: ${q}\nA: ${a}`)
          .join("\n\n")}
        
        Return a JSON object with an 'insights' array containing actionable marketing insights based on the data.
        Each insight should include:
        - title: A clear, descriptive title
        - description: Detailed explanation of the insight
        - impact: "high", "medium", or "low"
        - type: "automation", "campaign", or "general"
        - delivery_channel: "email" or "text"
        - actions: An array of action steps to take
      `;

      console.log("Sending prompt to LLM:", prompt.substring(0, 500) + "...");

      // Create function calling parser
      const parser = new JsonOutputFunctionsParser();

      try {
        // Make the LLM call with function definition
        const result = await llm
          .bind({
            functions: [functionDef],
            function_call: { name: "generate_insights" },
          })
          .pipe(parser)
          .invoke(prompt);

        // Extract insights from the wrapper object
        const insightsResults = (result as any).insights || [];

        // Ensure each insight has the required fields
        const formattedInsights = insightsResults.map((insight: any) => ({
          ...insight,
          location_id: locationId,
          status: insight.status || "new",
          delivery_channel: insight.delivery_channel || "email",
          // Convert actions array to string if needed, for backward compatibility
          actions: Array.isArray(insight.actions)
            ? JSON.stringify(insight.actions)
            : insight.actions,
          agent_id: 2,
          agent_name: "Craig",
        }));

        // Evaluate and improve the insights
        const evaluatedInsights = await this.evaluatorAgent.evaluateInsights(
          formattedInsights,
          context,
          model || "gpt-4.1-mini"
        );

        // Filter insights based on actionability
        const actionableInsights = [];
        for (const insight of evaluatedInsights) {
          try {
            const isActionable = await isInsightActionable(insight);
            if (isActionable) {
              actionableInsights.push(insight);
            }
          } catch (error) {
            console.warn("Error checking if insight is actionable:", error);
            // Default to considering it actionable if check fails
            actionableInsights.push(insight);
          }
        }

        return actionableInsights;
      } catch (error) {
        console.error("Error generating insights:", error);
        throw new Error("Failed to generate insights: " + error);
      }
    } catch (error) {
      console.error("Error in generateInsights:", error);
      throw error;
    }
  }

  async generateAutomationPlan(
    insight: Insight,
    model?: string,
    imageQuality: string = "HD"
  ) {
    try {
      // Ensure ChatAIService is initialized
      await this.ensureChatAIService();

      if (!insight) {
        throw new RequestError("Invalid insight: no insight provided", 422);
      }

      // Validate insight has minimum required data
      if (!insight.title && !insight.description) {
        throw new RequestError("Invalid insight: missing title and description", 422);
      }

      if (!insight.location_id) {
        throw new RequestError("Invalid insight: missing location_id", 422);
      }

      // Add more detailed logging for debugging
      console.log("Generating automation plan for insight:", {
        id: insight.id,
        type: insight.type,
        delivery_channel: insight.delivery_channel,
      });

      // Accept multiple valid insight types: automation, campaign, blast
      const validTypes = ["automation", "campaign", "blast"];
      let typeIsValid = validTypes.includes(String(insight.type).toLowerCase());

      // Handle case where insight.type might be stored as JSON or have different casing
      if (typeof insight.type === "string" && !typeIsValid) {
        try {
          const lowerType = insight.type.toLowerCase();
          if (validTypes.some((type) => lowerType.includes(type))) {
            typeIsValid = true;
            // Normalize the type to help downstream processing
            if (lowerType.includes("campaign") || lowerType.includes("blast")) {
              insight.type = "campaign" as any;
            } else {
              insight.type = "automation" as any;
            }
          }
        } catch (e) {
          // Keep original type check if parsing fails
        }
      }

      // Set default delivery channel if missing
      if (!insight.delivery_channel) {
        console.warn(
          `Insight ${insight.id} missing delivery_channel, defaulting to email`
        );
        insight.delivery_channel = "email" as any;
      }

      if (!typeIsValid) {
        console.warn(
          `Insight ${insight.id} has type "${insight.type}" which is not a valid automation type`
        );
        throw new RequestError(
          `Invalid insight: type must be one of: ${validTypes.join(", ")}`,
          422
        );
      }

      const context = await this.contextAgent.gatherContext(
        insight.location_id,
        "30d",
        insight
      );

      // Validate context has minimum required data
      if (!context.location) {
        throw new RequestError("Failed to load location data for automation plan", 500);
      }

      if (!context.channelConfig || (!context.channelConfig.email && !context.channelConfig.text)) {
        console.warn("No email or SMS channel configuration found for location", insight.location_id);
        // Don't throw error, but log warning as this might affect plan generation
      }

      // Use a custom structured output parser without implementing the StructuredOutputParser interface
      const customParser: SimpleParser = {
        parse: async (text: string) => {
          try {
            const cleaned = text
              .replace(/```json\s*|\s*```/g, "")
              .replace(/^`|`$/g, "")
              .trim();

            return JSON.parse(cleaned);
          } catch (error) {
            throw new Error(`Failed to parse output: ${error}`);
          }
        },
        getFormatInstructions: () => {
          return "Return a valid JSON object matching the required format.";
        },
      };

      // Determine if we should create a simple campaign plan or full automation journey
      const insightType = String(insight.type).toLowerCase();
      const isSimpleCampaign =
        insightType === "campaign" || insightType === "blast";

      // Use the GeneralAgent's prompt methods
      const basePrompt = this.generalAgent.automationPrompts.base;
      const constraintsPrompt =
        this.generalAgent.automationPrompts.constraints.join("\n");
      const examplePrompt = {}; // Simplified example

      let promptTemplate;

      if (isSimpleCampaign) {
        // Simplified prompt for campaign-only plans
        promptTemplate = `
          ${basePrompt}
          
          DESIGN OBJECTIVE:
          ${insight.title}
          ${insight.description}
          
          TARGET CHANNEL: ${insight.delivery_channel}
          
          CONSTRAINTS:
          - Create a simple campaign plan (not a complex journey)
          - Focus on creating a single campaign with a good template
          - Set up appropriate lists based on the objective
          ${constraintsPrompt}
          
          AVAILABLE RESOURCES:
          ${JSON.stringify(context.existingResources, null, 2)}
          
          CHANNEL CONFIGURATION:
          ${JSON.stringify(context.channelConfig, null, 2)}
          
          CREATE A CAMPAIGN PLAN WITH THIS FORMAT:
          {
            "name": "Campaign Plan Name",
            "description": "Overall plan description",
            "items": {
              "listKey": {
                "type": "list",
                "config": {
                  "list": {
                    "name": "List Name",
                    "type": "dynamic",
                    "description": "List description",
                    "rule": {
                      "uuid": "auto-generated-uuid",
                      "type": "string",
                      "group": "user",
                      "path": "id",
                      "operator": "is set",
                      "value": "",
                      "children": []
                    },
                    "is_visible": true,
                    "tags": ["tag1", "tag2"],
                    "user_ids": []
                  }
                }
              },
              "campaignKey": {
                "type": "campaign",
                "config": {
                  "campaign": {
                    "name": "Campaign Name",
                    "type": "blast",
                    "channel": "${insight.delivery_channel}",
                    "subscription_id": 1,
                    "provider_id": 1,
                    "list_refs": ["listKey"],
                    "send_in_user_timezone": true,
                    "send_at": "",
                    "state": "scheduled",
                    "tags": ["tag1", "tag2"]
                  }
                }
              },
              "templateKey": {
                "type": "template",
                "config": {
                  "template": {
                    "type": "${insight.delivery_channel}",
                    "name": "Template Name",
                    "locale": "en",
                    "campaign_ref": "campaignKey",
                    "data": {
                      "editor": "code",
                      "name": "Template Name",
                      "subject": "Email Subject Line",
                      "html": "<html><body><p>HTML content for email template</p></body></html>",
                      "text": "Plain text version of the content",
                      "from": {
                        "name": "Sender Name",
                        "address": "<EMAIL>"
                      },
                      "reply_to": "<EMAIL>",
                      "imagePrompt": "Detailed description of the image to generate for the email"
                    }
                  }
                }
              }
            }
          }
          
          IMPORTANT NOTES:
          - For blast campaigns, the send_at field is an empty string "" by default or a properly formatted date
          - If specifying a date, it MUST be in this exact format: "YYYY-MM-DDThh:mm:ss+hh:mm" with timezone offset
          - DO NOT use "Z" at the end of the date string! Use a proper offset like "+07:00"
          - The state field MUST be set to "scheduled" for blast campaigns
          - The send_in_user_timezone field MUST be set to true for most cases
          - For email templates, always include a detailed imagePrompt to generate a beautiful header image
          - The from field requires both email and address properties with the same value
          
          Create a basic campaign plan that addresses the design objective.
          Return ONLY valid JSON that matches the required format.
        `;
      } else {
        // Full automation journey prompt
        promptTemplate = `
          ${basePrompt}
          
          DESIGN OBJECTIVE:
          ${insight.title}
          ${insight.description}
          
          TARGET CHANNEL: ${insight.delivery_channel}
          
          CONSTRAINTS:
          ${constraintsPrompt}
          
          AVAILABLE RESOURCES:
          ${JSON.stringify(context.existingResources, null, 2)}
          
          CHANNEL CONFIGURATION:
          ${JSON.stringify(context.channelConfig, null, 2)}
          
          CREATE A COMPREHENSIVE AUTOMATION PLAN WITH THIS FORMAT:
          {
            "name": "Automation Plan Name",
            "description": "Overall automation plan description",
            "items": {
              "listKey": {
                "type": "list",
                "config": {
                  "list": {
                    "name": "List Name",
                    "type": "dynamic",
                    "description": "List description",
                    "rule": {
                      "uuid": "auto-generated-uuid",
                      "type": "string",
                      "group": "user",
                      "path": "id",
                      "operator": "is set",
                      "value": "",
                      "children": []
                    },
                    "is_visible": true,
                    "tags": ["tag1", "tag2"],
                    "user_ids": []
                  }
                }
              },
              "campaignKey": {
                "type": "campaign",
                "config": {
                  "campaign": {
                    "name": "Campaign Name",
                    "type": "blast",
                    "channel": "${insight.delivery_channel}",
                    "subscription_id": 1,
                    "provider_id": 1,
                    "list_refs": ["listKey"],
                    "send_in_user_timezone": true,
                    "send_at": "",
                    "state": "scheduled",
                    "tags": ["tag1", "tag2"]
                  }
                }
              },
              "templateKey": {
                "type": "template",
                "config": {
                  "template": {
                    "type": "${insight.delivery_channel}",
                    "name": "Template Name",
                    "locale": "en",
                    "campaign_ref": "campaignKey",
                    "data": {
                      "editor": "code",
                      "name": "Template Name",
                      "subject": "Email Subject Line",
                      "html": "<html><body><p>HTML content for email template</p></body></html>",
                      "text": "Plain text version of the content",
                      "from": {
                        "name": "Sender Name",
                        "address": "<EMAIL>"
                      },
                      "reply_to": "<EMAIL>",
                      "imagePrompt": "Detailed description of the image to generate for the email"
                    }
                  }
                }
              },
              "journeyKey": {
                "type": "journey",
                "config": {
                  "journey": {
                    "name": "Journey Name",
                    "description": "Journey description",
                    "published": false,
                    "tags": ["tag1", "tag2"],
                    "steps": {
                      "entrance_step": {
                        "type": "entrance",
                        "name": "Journey Start",
                        "x": 0,
                        "y": 0,
                        "data": {
                          "trigger": "schedule",
                          "multiple": true,
                          "schedule": "DTSTART:20250401T000000Z",
                          "list_id": "listKey"
                        },
                        "children": []
                      }
                    }
                  }
                }
              }
            }
          }
          
          IMPORTANT NOTES:
          - For campaigns, send_at can be empty string "" by default or a properly formatted date
          - If specifying a date, it MUST be in this exact format: "YYYY-MM-DDThh:mm:ss+hh:mm" with timezone offset 
          - DO NOT use "Z" at the end of the date string! Use a proper offset like "+07:00"
          - All campaigns must have state field set to "scheduled" or "draft"
          - For email templates, always include a detailed imagePrompt to generate a beautiful header image
          - The from field requires both email and address properties with the same value
          - For journeys, ensure all steps have proper x/y coordinates for visualization
          
          Create a complete automation plan that addresses the design objective.
          Return ONLY valid JSON that matches the required format.
        `;
      }

      try {
        // Generate plan with voting (multiple candidates)
        const plan = await this.votingAgent.generateWithVoting(
          insight,
          context,
          model || "gpt-4.1-mini",
          customParser,
          promptTemplate,
          isSimpleCampaign ? 2 : 3 // Fewer candidates for simple campaigns
        );

        // Set sender information in templates based on location data
        this.validationAgent.setSenderInformation(plan, context.location);

        // If we're generating an email template, generate image if needed
        if (
          insight.delivery_channel === "email" &&
          plan.items &&
          Object.values(plan.items).some(
            (item: any) => item.type === "template"
          )
        ) {
          // Process any templates to generate images if needed
          for (const [key, item] of Object.entries<any>(plan.items)) {
            if (
              item.type === "template" &&
              item.config?.template?.data?.imagePrompt
            ) {
              const imagePrompt = item.config.template.data.imagePrompt;
              console.log(
                `Generating image for template "${key}" with prompt: ${imagePrompt}`
              );

              try {
                const imageUrl = await this.generateImage(
                  insight.location_id,
                  imagePrompt,
                  item.config.template.data.subject,
                  insight.id
                );

                item.config.template.data.imageUrl = imageUrl;
                console.log(`Generated image: ${imageUrl}`);

                // Insert the image into the HTML if not already present
                if (
                  item.config.template.data.html &&
                  !item.config.template.data.html.includes(imageUrl)
                ) {
                  const imgHtml = `<img src="${imageUrl}" alt="${
                    item.config.template.data.subject || "Email header"
                  }" style="width:100%;max-width:600px;height:auto;display:block;margin:0 auto;" />`;

                  // For modern HTML email structure, insert in header div
                  if (
                    item.config.template.data.html.includes(
                      '<div class="header">'
                    )
                  ) {
                    item.config.template.data.html =
                      item.config.template.data.html.replace(
                        '<div class="header">',
                        `<div class="header">${imgHtml}`
                      );
                  } else if (
                    item.config.template.data.html.includes("<body>")
                  ) {
                    item.config.template.data.html =
                      item.config.template.data.html.replace(
                        "<body>",
                        "<body>" + imgHtml
                      );
                  } else if (item.config.template.data.html.includes("<p>")) {
                    item.config.template.data.html =
                      item.config.template.data.html.replace(
                        "<p>",
                        imgHtml + "<p>"
                      );
                  } else {
                    item.config.template.data.html =
                      imgHtml + item.config.template.data.html;
                  }
                }
              } catch (error) {
                console.error("Failed to generate image:", error);
                // Continue without image if generation fails
              }
            }
          }
        }

        return plan;
      } catch (error) {
        console.error("Error generating automation plan:", error);

        // Log additional context for debugging
        console.error("Automation plan generation context:", {
          insightId: insight.id,
          insightType: insight.type,
          deliveryChannel: insight.delivery_channel,
          model: model || "gpt-4.1-mini",
          isSimpleCampaign,
          hasEmailConfig: !!context.channelConfig?.email,
          hasTextConfig: !!context.channelConfig?.text,
        });

        // Provide more specific error message
        let errorMessage = "Failed to generate automation plan";
        if (error instanceof Error) {
          if (error.message.includes("No valid automation plan candidates")) {
            errorMessage = "Failed to generate valid automation plan candidates. This may be due to validation issues or LLM response format problems.";
          } else if (error.message.includes("OpenAI")) {
            errorMessage = "OpenAI API error during automation plan generation";
          } else if (error.message.includes("validation")) {
            errorMessage = "Automation plan validation failed";
          }
        }

        throw new Error(`${errorMessage}: ${error}`);
      }
    } catch (error) {
      console.error("Error in generateAutomationPlan:", error);
      throw error;
    }
  }

  async regenerateItem(
    insight: Insight,
    itemKey: string,
    model?: string,
    prompt?: string,
    imageQuality: string = "HD"
  ) {
    try {
      // Ensure ChatAIService is initialized
      await this.ensureChatAIService();

      // Get context for the regeneration
      const context = await this.contextAgent.gatherContext(
        insight.location_id,
        "30d",
        insight
      );

      // Get existing plan from insight
      const plan = insight.plan ? JSON.parse(insight.plan) : null;
      if (!plan || !plan.items || !plan.items[itemKey]) {
        throw new Error(`Item ${itemKey} not found in the plan`);
      }

      // Regenerate the specific item
      const item = plan.items[itemKey];

      // Use the appropriate method based on the item type
      if (item.type === "template" && item.config?.template) {
        // For templates, generate variants and use the first one
        const variants = await this.contentAgent.generateTemplateVariants(
          insight.location_id,
          insight.delivery_channel,
          { ...context, originalItem: item, customPrompt: prompt },
          model || "gpt-4.1-mini",
          1 // Just get one variant
        );

        if (variants && variants.length > 0) {
          // Update template data while keeping other config
          item.config.template.data = {
            ...item.config.template.data,
            ...variants[0],
          };
        }
      }

      // Update the plan with the regenerated item
      plan.items[itemKey] = item;

      // If it's a template with image prompt, generate a new image
      if (
        item.type === "template" &&
        item.config?.template?.data?.imagePrompt &&
        insight.delivery_channel === "email"
      ) {
        try {
          const imageUrl = await this.generateImage(
            insight.location_id,
            item.config.template.data.imagePrompt,
            item.config.template.data.subject,
            insight.id
          );
          item.config.template.data.imageUrl = imageUrl;

          // Insert the image into the HTML if not already present
          if (
            item.config.template.data.html &&
            !item.config.template.data.html.includes(imageUrl)
          ) {
            const imgHtml = `<img src="${imageUrl}" alt="${
              item.config.template.data.subject || "Email header"
            }" style="width:100%;max-width:600px;height:auto;display:block;margin:0 auto;" />`;

            // For modern HTML email structure, insert in header div
            if (
              item.config.template.data.html.includes('<div class="header">')
            ) {
              item.config.template.data.html =
                item.config.template.data.html.replace(
                  '<div class="header">',
                  `<div class="header">${imgHtml}`
                );
            } else if (item.config.template.data.html.includes("<body>")) {
              item.config.template.data.html =
                item.config.template.data.html.replace(
                  "<body>",
                  "<body>" + imgHtml
                );
            } else if (item.config.template.data.html.includes("<p>")) {
              item.config.template.data.html =
                item.config.template.data.html.replace("<p>", imgHtml + "<p>");
            } else {
              item.config.template.data.html =
                imgHtml + item.config.template.data.html;
            }
          }
        } catch (error) {
          console.error("Failed to generate image during regeneration:", error);
        }
      }
    } catch (error) {
      console.error("Error regenerating item:", error);
      throw new Error(`Failed to regenerate item: ${error}`);
    }
  }

  async handleRequest(locationId: number, request: string, model?: string) {
    try {
      // Ensure ChatAIService is initialized
      await this.ensureChatAIService();

      // Gather context for the request
      const context = await this.contextAgent.gatherContext(locationId, "30d");

      // Use the general agent to handle the natural language request
      return await this.generalAgent.handleRequest(
        request,
        context,
        model || "gpt-4.1-mini"
      );
    } catch (error) {
      console.error("Error handling natural language request:", error);
      throw new Error(`Failed to process request: ${error}`);
    }
  }

  private async generateImage(
    locationId: number,
    prompt: string,
    title: string,
    insightId?: number
  ): Promise<string> {
    try {
      console.log(`Generating image with prompt: ${prompt}`);
      // Call imageGenerationService with proper parameters
      const result = await this.getImageGenerationService().generateImage(prompt, {
        quality: "hd",
        size: "1024x1024",
        style: "vivid",
        high_fidelity: true,
        enhance_for_marketing: true, // Explicitly request marketing enhancement
      });

      if (!result) {
        console.error("Failed to generate image - no URL returned");
        // Return a sensible default image URL instead of throwing
        return "https://placehold.co/600x400/23504A/FFFFFF?text=Default+Image";
      }

      // Download and store the generated image
      console.log(`Downloading image from: ${result}`);
      // Use ImageDownloadService properly
      try {
        const image = await imageDownloadService.downloadAndStoreImage(
          locationId,
          result,
          title
        );

        // Ensure we're returning a valid URL
        if (!image || !image.url) {
          console.error("Downloaded image has no valid URL");
          return "https://placehold.co/600x400/23504A/FFFFFF?text=Default+Image";
        }

        return image.url;
      } catch (error) {
        console.error("Failed to download image:", error);
        // Fallback to a default URL if download fails
        return "https://placehold.co/600x400/23504A/FFFFFF?text=Default+Image";
      }
    } catch (error) {
      console.error("Failed to generate and store image:", error);
      // Always return a default image URL instead of throwing
      return "https://placehold.co/600x400/23504A/FFFFFF?text=Default+Image";
    }
  }
}

// Create and export a singleton instance of the CrewAIService
// This will be lazily initialized when needed
export const crewAIService = new CrewAIService();
