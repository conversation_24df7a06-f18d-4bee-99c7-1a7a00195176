import OpenAI from "openai";
import axios from "axios";

interface ImageGenerationOptions {
  size?: "1024x1024" | "1024x1792" | "1792x1024";
  quality?: "standard" | "hd";
  style?: "vivid" | "natural";
  high_fidelity?: boolean;
  enhance_for_marketing?: boolean;
  provider?: "openai" | "ideogram";
}

class ImageGenerationService {
  private openai: OpenAI;
  private ideogramApiKey: string | undefined;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
    this.ideogramApiKey = process.env.IDEOGRAM_API_KEY;
  }

  async generateImage(
    prompt: string,
    options: ImageGenerationOptions = {}
  ): Promise<string> {
    try {
      console.log("=== Starting Image Generation ===");
      console.log("Prompt:", prompt);
      console.log("Options:", options);

      // Determine which provider to use - default to OpenAI if not specified
      const provider = options.provider || "openai";
      console.log(`Using provider: ${provider}`);

      if (provider === "ideogram" && this.ideogramApiKey) {
        return this.generateImageWithIdeogram(prompt, options);
      } else {
        return this.generateImageWithOpenAI(prompt, options);
      }
    } catch (error) {
      console.error(`Error generating image:`, error);
      return "https://placehold.co/600x400/23504A/FFFFFF?text=Default+Image";
    }
  }

  private async generateImageWithOpenAI(
    prompt: string,
    options: ImageGenerationOptions = {}
  ): Promise<string> {
    // Only enhance the prompt if specifically requested
    let finalPrompt = options.enhance_for_marketing
      ? this.enhanceMarketingPrompt(prompt)
      : prompt;

    // Add quality cues to match ChatGPT web behavior
    // But only if not already a marketing prompt (which already has quality hints)
    if (
      !options.enhance_for_marketing &&
      !finalPrompt.toLowerCase().includes("high quality") &&
      !finalPrompt.toLowerCase().includes("high-quality")
    ) {
      finalPrompt = `High quality ${finalPrompt.trim()}. Detailed, sharp focus, professional quality.`;
    }

    console.log(
      options.enhance_for_marketing
        ? "Enhanced marketing prompt:"
        : "Using refined prompt:",
      finalPrompt
    );

    // Check if we should use GPT-4o or DALL-E 3
    const useGpt4o = options.high_fidelity !== false;

    if (useGpt4o) {
      console.log("Using GPT-4o for image generation");

      // For GPT-4o, do NOT specify a model parameter (leave it blank as per API error message)
      const response = await this.openai.images.generate({
        prompt: finalPrompt,
        n: 1,
        quality: "hd", // Always use HD quality to match web interface
      });

      if (!response.data?.[0]?.url) {
        console.error("No image URL in GPT-4o response");
        return "https://placehold.co/600x400/23504A/FFFFFF?text=Default+Image";
      }

      return response.data[0].url;
    } else {
      // For DALL-E 3, use the model parameter as before
      console.log("Using DALL-E 3 for image generation");

      const response = await this.openai.images.generate({
        model: "dall-e-3",
        prompt: finalPrompt,
        n: 1,
        size: options.size || "1024x1024",
        quality: "hd", // Always use HD quality to match web interface
        style: options.style || "vivid",
      });

      if (!response.data?.[0]?.url) {
        console.error("No image URL in DALL-E 3 response");
        return "https://placehold.co/600x400/23504A/FFFFFF?text=Default+Image";
      }

      return response.data[0].url;
    }
  }

  private async generateImageWithIdeogram(
    prompt: string,
    options: ImageGenerationOptions = {}
  ): Promise<string> {
    if (!this.ideogramApiKey) {
      console.error("Ideogram API key not configured");
      throw new Error("Ideogram API key not configured");
    }

    try {
      console.log("Using Ideogram for image generation");

      // Enhance the prompt if requested
      const finalPrompt = options.enhance_for_marketing
        ? this.enhanceMarketingPrompt(prompt)
        : prompt;

      const response = await axios({
        method: "post",
        url: "https://api.ideogram.ai/generate",
        headers: {
          "Api-Key": this.ideogramApiKey,
          "Content-Type": "application/json",
        },
        data: {
          image_request: {
            prompt: finalPrompt,
            model: "V_2",
            style_type: options.style === "vivid" ? "REALISTIC" : "DESIGN",
            aspect_ratio: this.mapSizeToAspectRatio(options.size),
            magic_prompt_option: "AUTO",
          },
        },
      });

      if (
        response.data &&
        response.data.data &&
        response.data.data.length > 0
      ) {
        // Return the URL of the first generated image
        return response.data.data[0].url;
      } else {
        console.error("No image URL in Ideogram response", response.data);
        return "https://placehold.co/600x400/23504A/FFFFFF?text=Default+Image";
      }
    } catch (error: any) {
      console.error("Error with Ideogram API:", error);
      // Log the error response data if available
      if (error.response && error.response.data) {
        console.error("Ideogram API error response:", error.response.data);
      }
      throw error;
    }
  }

  private mapSizeToAspectRatio(size?: string): string {
    switch (size) {
      case "1024x1792":
        return "ASPECT_9_16"; // Portrait
      case "1792x1024":
        return "ASPECT_16_9"; // Landscape
      default:
        return "ASPECT_1_1"; // Square (1024x1024)
    }
  }

  /**
   * Enhance a basic prompt to create better marketing images
   */
  private enhanceMarketingPrompt(basePrompt: string): string {
    // If already detailed, don't modify
    if (basePrompt.length > 300) return basePrompt;

    // Check if already enhanced
    if (
      basePrompt.includes("marketing email") ||
      basePrompt.includes("professional quality") ||
      basePrompt.includes("header image")
    ) {
      return basePrompt;
    }

    return `Create a beautiful, professional marketing email header image with the following characteristics:
- High-quality, visually striking design suitable for email marketing campaigns
- Professional and modern aesthetic with balanced composition
- Clean layout with appropriate negative space
- Bold, appealing visuals that immediately capture attention
- Compatible with both desktop and mobile viewing
- Perfect for an email hero image that encourages recipients to continue reading

The image should effectively communicate:
${basePrompt}

Create something that would appear in a premium marketing campaign from a top brand.
Use professional marketing photography style with perfect lighting, balanced composition,
and modern design aesthetics. Ensure vibrant yet professional colors that render well
across email clients.`;
  }

  /**
   * Generate multiple image variations and return all URLs
   */
  async generateVariations(
    prompt: string,
    count: number = 3,
    options: ImageGenerationOptions = {}
  ): Promise<string[]> {
    console.log("=== Generating Image Variations ===");
    console.log("Prompt:", prompt);
    console.log("Count:", count);
    console.log("Options:", options);

    const promises = Array(count)
      .fill(null)
      .map(() => this.generateImage(prompt, options));

    try {
      const results = await Promise.all(promises);
      console.log(`Successfully generated ${results.length} image variations`);
      return results;
    } catch (error) {
      console.error("Error generating image variations:", error);
      throw error;
    }
  }

  /**
   * Generate channel-specific marketing images
   */
  async generateMarketingImage(
    channel: string,
    prompt: string,
    options: ImageGenerationOptions = {}
  ): Promise<string> {
    console.log("=== Generating Marketing Image ===");
    console.log("Channel:", channel);
    console.log("Original prompt:", prompt);

    const channelRequirements = this.getChannelImageRequirements(channel);
    const enhancedPrompt = `
      Create a marketing image for ${channel} that:
      ${channelRequirements}

      Specific requirements:
      ${prompt}

      The image should be visually striking, modern, and optimized for ${channel} viewing.
      Ensure high contrast, clear focal points, and appropriate white space.
      The style should be professional and aligned with current marketing trends.
    `;

    console.log("Enhanced prompt:", enhancedPrompt);
    console.log(
      "Channel-specific options:",
      this.getChannelImageOptions(channel)
    );

    return await this.generateImage(enhancedPrompt.trim(), {
      ...options,
      ...this.getChannelImageOptions(channel),
      enhance_for_marketing: true, // Always use marketing enhancement for marketing images
    });
  }

  private getChannelImageRequirements(channel: string): string {
    switch (channel) {
      case "email":
        return `
          - Works well at both desktop and mobile sizes
          - Has clear text overlay areas
          - Uses colors that display well across email clients
          - Maintains quality when compressed
        `;
      case "text":
        return `
          - Simple and impactful design
          - Works well at small sizes
          - High contrast for mobile viewing
          - Clear focal point
        `;
      case "social":
        return `
          - Eye-catching and scroll-stopping
          - Works well in feed and expanded views
          - Follows platform-specific design trends
          - Leaves space for text overlays
        `;
      default:
        return "";
    }
  }

  private getChannelImageOptions(
    channel: string
  ): Partial<ImageGenerationOptions> {
    switch (channel) {
      case "email":
        return {
          size: "1024x1024",
          quality: "standard",
          style: "natural",
        };
      case "text":
        return {
          size: "1024x1024",
          quality: "standard",
          style: "vivid",
        };
      case "social":
        return {
          size: "1792x1024",
          quality: "hd",
          style: "vivid",
        };
      default:
        return {};
    }
  }
}

// Export the class for manual instantiation after env is loaded
export { ImageGenerationService };
