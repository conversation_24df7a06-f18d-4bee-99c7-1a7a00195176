/**
 * @swagger
 * components:
 *   schemas:
 *     MarketInsightsResponse:
 *       type: object
 *       properties:
 *         market_size:
 *           type: number
 *           description: Estimated market size in dollars
 *         growth_rate:
 *           type: number
 *           description: Annual growth rate as a percentage
 *         competition_level:
 *           type: string
 *           enum: [low, medium, high]
 *           description: Level of competition in the market
 *         demographics:
 *           type: object
 *           properties:
 *             age_distribution:
 *               type: object
 *               additionalProperties: true
 *             income_levels:
 *               type: object
 *               additionalProperties: true
 *         regulations:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *               description:
 *                 type: string
 *               impact:
 *                 type: string
 *         trends:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               impact:
 *                 type: string
 *         recommendations:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               category:
 *                 type: string
 *               suggestion:
 *                 type: string
 *               priority:
 *                 type: string
 *                 enum: [low, medium, high]
 */

/**
 * @swagger
 * tags:
 *   name: Insights
 *   description: Market and business insights endpoints
 */

import Router from "@koa/router";
import { InsightService } from "../services/InsightService";
import { logger } from "../config/logger";
import { cacheGet, cacheSet } from "../config/redis";
import { insightPatternService } from "./InsightPatternService";
import { isInsightActionable } from "./InsightService";
import { locationRoleMiddleware } from "../locations/LocationService";
import App from "../app";

const router = new Router({
  prefix: "/insights",
});

const insightService = new InsightService();

// Cache TTL in seconds (24 hours)
const CACHE_TTL = 86400;

/**
 * @swagger
 * /insights/market:
 *   get:
 *     summary: Get Market Insights
 *     description: Retrieve market insights for a given city and optional location parameters
 *     tags: [Insights]
 *     parameters:
 *       - in: query
 *         name: locationName
 *         schema:
 *           type: string
 *         description: Name of the location (optional)
 *       - in: query
 *         name: city
 *         required: true
 *         schema:
 *           type: string
 *         description: City name
 *       - in: query
 *         name: state
 *         schema:
 *           type: string
 *         description: State abbreviation (optional)
 *       - in: query
 *         name: zip
 *         schema:
 *           type: string
 *         description: Zip code (optional)
 *       - in: query
 *         name: latitude
 *         schema:
 *           type: number
 *         description: Latitude (optional)
 *       - in: query
 *         name: longitude
 *         schema:
 *           type: number
 *         description: Longitude (optional)
 *     responses:
 *       200:
 *         description: Market insights retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/MarketInsightsResponse'
 *             example:
 *               market_size: 150000000
 *               growth_rate: 12.5
 *               competition_level: "medium"
 *               demographics:
 *                 age_distribution:
 *                   "18-24": 15
 *                   "25-34": 35
 *                   "35-44": 25
 *                   "45-54": 15
 *                   "55+": 10
 *                 income_levels:
 *                   "low": 20
 *                   "medium": 45
 *                   "high": 35
 *               regulations:
 *                 - type: "licensing"
 *                   description: "Required state licensing"
 *                   impact: "high"
 *               trends:
 *                 - name: "Online ordering"
 *                   description: "Growing preference for online orders"
 *                   impact: "positive"
 *               recommendations:
 *                 - category: "Marketing"
 *                   suggestion: "Focus on digital presence"
 *                   priority: "high"
 *       400:
 *         description: Bad Request - Missing city parameter
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *             example:
 *               error: "Missing required parameter: city"
 *       500:
 *         description: Failed to retrieve market insights
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 details:
 *                   type: string
 *             example:
 *               error: "Failed to retrieve market insights"
 *               details: "Error connecting to data provider"
 */
router.get("/market", async (ctx) => {
  try {
    const { locationName, city, state, zip, latitude, longitude } =
      ctx.request.query;

    if (!city) {
      ctx.status = 400;
      ctx.body = {
        error: "Missing required parameter: city",
      };
      return;
    }

    // Generate cache key from query parameters
    const cacheKey = `insights:market:${city}:${state || ""}:${zip || ""}:${
      latitude || ""
    }:${longitude || ""}`;

    // Try to get from cache first
    const cachedInsights = await cacheGet(App.main.redis, cacheKey);
    if (cachedInsights) {
      logger.debug(`Cache hit for market insights: ${cacheKey}`);
      ctx.body = cachedInsights;
      return;
    }

    logger.debug(`Cache miss for market insights: ${cacheKey}`);
    const insights = await insightService.getMarketInsights(
      (locationName as string) || "your dispensary",
      city as string,
      state as string | undefined,
      zip as string | undefined,
      latitude ? parseFloat(latitude as string) : undefined,
      longitude ? parseFloat(longitude as string) : undefined
    );

    // Cache the results
    await cacheSet(App.main.redis, cacheKey, insights, CACHE_TTL);
    logger.debug(`Cached market insights: ${cacheKey}`);

    ctx.status = 200;
    ctx.body = insights;
  } catch (error) {
    logger.error("Error fetching market insights:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to retrieve market insights",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /insights/patterns:
 *   get:
 *     summary: Get all insight patterns
 *     description: Retrieve all insight patterns for admin management
 *     tags: [Insights]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of insight patterns
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get("/patterns", locationRoleMiddleware("admin"), async (ctx) => {
  try {
    const patterns = await insightPatternService.getAllPatterns();

    // Convert patterns to include parsed examples
    const formattedPatterns = patterns.map((pattern) => ({
      ...pattern,
      examples: pattern.getExamplesArray(),
    }));

    ctx.body = {
      success: true,
      data: formattedPatterns,
    };
  } catch (error) {
    logger.error("Error fetching insight patterns:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to fetch insight patterns",
    };
  }
});

/**
 * @swagger
 * /insights/patterns:
 *   post:
 *     summary: Create a new insight pattern
 *     description: Create a new pattern for detecting actionable/non-actionable insights
 *     tags: [Insights]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - pattern
 *               - description
 *               - type
 *             properties:
 *               pattern:
 *                 type: string
 *                 description: Regex pattern to match
 *               description:
 *                 type: string
 *                 description: Human-readable description
 *               type:
 *                 type: string
 *                 enum: [non_actionable, actionable]
 *               is_active:
 *                 type: boolean
 *                 default: true
 *               priority:
 *                 type: integer
 *                 default: 0
 *               examples:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Pattern created successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post("/patterns", locationRoleMiddleware("admin"), async (ctx) => {
  try {
    const { pattern, description, type, is_active, priority, examples } = ctx
      .request.body as any;

    // Validate required fields
    if (!pattern || !description || !type) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Pattern, description, and type are required",
      };
      return;
    }

    // Validate type
    if (!["non_actionable", "actionable"].includes(type)) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Type must be either 'non_actionable' or 'actionable'",
      };
      return;
    }

    // Test the regex pattern
    const testResult = await insightPatternService.testPattern(pattern, "test");
    if (testResult.error) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: `Invalid regex pattern: ${testResult.error}`,
      };
      return;
    }

    const newPattern = await insightPatternService.createPattern({
      pattern,
      description,
      type,
      is_active: is_active ?? true,
      priority: priority ?? 0,
      examples: examples || [],
    });

    ctx.body = {
      success: true,
      data: {
        ...newPattern,
        examples: newPattern.getExamplesArray(),
      },
    };
  } catch (error) {
    logger.error("Error creating insight pattern:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to create insight pattern",
    };
  }
});

/**
 * @swagger
 * /insights/patterns/{id}:
 *   put:
 *     summary: Update an insight pattern
 *     description: Update an existing insight pattern
 *     tags: [Insights]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               pattern:
 *                 type: string
 *               description:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [non_actionable, actionable]
 *               is_active:
 *                 type: boolean
 *               priority:
 *                 type: integer
 *               examples:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: Pattern updated successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.put("/patterns/:id", locationRoleMiddleware("admin"), async (ctx) => {
  try {
    const id = parseInt(ctx.params.id);
    const { pattern, description, type, is_active, priority, examples } = ctx
      .request.body as any;

    if (isNaN(id)) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Invalid pattern ID",
      };
      return;
    }

    // Validate type if provided
    if (type && !["non_actionable", "actionable"].includes(type)) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Type must be either 'non_actionable' or 'actionable'",
      };
      return;
    }

    // Test the regex pattern if provided
    if (pattern) {
      const testResult = await insightPatternService.testPattern(
        pattern,
        "test"
      );
      if (testResult.error) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          error: `Invalid regex pattern: ${testResult.error}`,
        };
        return;
      }
    }

    const updatedPattern = await insightPatternService.updatePattern(id, {
      pattern,
      description,
      type,
      is_active,
      priority,
      examples,
    });

    ctx.body = {
      success: true,
      data: {
        ...updatedPattern,
        examples: updatedPattern.getExamplesArray(),
      },
    };
  } catch (error) {
    logger.error("Error updating insight pattern:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to update insight pattern",
    };
  }
});

/**
 * @swagger
 * /insights/patterns/{id}:
 *   delete:
 *     summary: Delete an insight pattern
 *     description: Delete an existing insight pattern
 *     tags: [Insights]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *     responses:
 *       200:
 *         description: Pattern deleted successfully
 *       400:
 *         description: Invalid pattern ID
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.delete("/patterns/:id", locationRoleMiddleware("admin"), async (ctx) => {
  try {
    const id = parseInt(ctx.params.id);

    if (isNaN(id)) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Invalid pattern ID",
      };
      return;
    }

    await insightPatternService.deletePattern(id);

    ctx.body = {
      success: true,
      message: "Pattern deleted successfully",
    };
  } catch (error) {
    logger.error("Error deleting insight pattern:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to delete insight pattern",
    };
  }
});

/**
 * @swagger
 * /insights/patterns/test:
 *   post:
 *     summary: Test a pattern against sample text
 *     description: Test if a regex pattern matches given text
 *     tags: [Insights]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - pattern
 *               - testText
 *             properties:
 *               pattern:
 *                 type: string
 *                 description: Regex pattern to test
 *               testText:
 *                 type: string
 *                 description: Text to test against
 *     responses:
 *       200:
 *         description: Test result
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post("/patterns/test", locationRoleMiddleware("admin"), async (ctx) => {
  try {
    const { pattern, testText } = ctx.request.body as any;

    if (!pattern || !testText) {
      ctx.status = 400;
      ctx.body = {
        success: false,
        error: "Pattern and testText are required",
      };
      return;
    }

    const result = await insightPatternService.testPattern(pattern, testText);

    ctx.body = {
      success: true,
      data: result,
    };
  } catch (error) {
    logger.error("Error testing pattern:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: "Failed to test pattern",
    };
  }
});

/**
 * Check if an insight is actionable based on patterns and insight data
 */
router.post(
  "/check-actionability",
  locationRoleMiddleware("support"),
  async (ctx) => {
    try {
      const { title, description, type, actions } = ctx.request.body as any;

      logger.info("Checking insight actionability:", {
        title,
        type,
        hasActions: !!actions,
        actionsType: typeof actions,
      });

      if (!title) {
        ctx.status = 400;
        ctx.body = {
          success: false,
          error: "Title is required",
        };
        return;
      }

      const title_text = title || "";
      const description_text = description || "";

      // Check database patterns for non-actionable insights
      const isNonActionable = await insightPatternService.isNonActionable(
        title_text,
        description_text
      );
      if (isNonActionable) {
        // If it matches a non-actionable pattern, it's not actionable
        ctx.body = {
          success: true,
          data: { isActionable: false },
        };
        return;
      }

      // Check database patterns for explicitly actionable insights
      const isExplicitlyActionable = await insightPatternService.isActionable(
        title_text,
        description_text
      );
      if (isExplicitlyActionable) {
        // If it matches an actionable pattern, it's actionable
        ctx.body = {
          success: true,
          data: { isActionable: true },
        };
        return;
      }

      // If no patterns match, use the original logic: type + actions
      const hasValidType = type === "automation" || type === "campaign";

      // Parse actions - handle both JSON array and comma-separated string formats
      let actionsArray = [];
      if (actions) {
        if (Array.isArray(actions)) {
          actionsArray = actions;
        } else if (typeof actions === "string") {
          try {
            // Try parsing as JSON first
            actionsArray = JSON.parse(actions);
          } catch {
            // If JSON parsing fails, split by comma (legacy format)
            actionsArray = actions
              .split(",")
              .map((a) => a.trim())
              .filter((a) => a.length > 0);
          }
        }
      }

      const hasActions = actionsArray.length > 0;
      const isActionable = hasValidType && hasActions;

      logger.info("Actionability check result:", {
        title,
        hasValidType,
        hasActions,
        actionsArrayLength: actionsArray.length,
        isActionable,
      });

      ctx.body = {
        success: true,
        data: {
          isActionable,
        },
      };
    } catch (error) {
      logger.error("Error checking insight actionability:", error);
      ctx.status = 500;
      ctx.body = {
        success: false,
        error: "Failed to check insight actionability",
      };
    }
  }
);

export default router;
