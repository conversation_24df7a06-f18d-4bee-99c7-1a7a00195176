import { z } from "zod";
import { StructuredOutputParser } from "langchain/output_parsers";
import { subDays, startOfDay, endOfDay } from "date-fns";
import Model from "../core/Model";
import Campaign from "../campaigns/Campaign";
import { PosData } from "../pos/PosData";
import { OpenAI, ChatOpenAI } from "@langchain/openai";
import { Tag } from "../tags/Tag";
import List from "../lists/List";
import Template from "../render/Template";
import Journey from "../journey/Journey";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";
import { subscriptionForChannel } from "../subscriptions/SubscriptionService";
import {
  getProvider,
  getProviderByGroup,
} from "../providers/ProviderRepository";
import { ChannelType } from "../config/channels";
import { uuid } from "../utilities";
import Location from "../locations/Location";
import api from "../api";
import { journeyStepTypes } from "../journey/JourneyStep";
import { crewAIService } from "./CrewAIService";
import { InsightSchema, AutomationPlanSchema } from "./schemas";
import { insightPatternService } from "./InsightPatternService";
import { createJourney } from "../journey/JourneyRepository";

/**
 * Insight model representing actionable insights for a given location.
 */
export class Insight extends Model {
  location_id!: number;
  title!: string;
  description!: string;
  impact!: "high" | "medium" | "low";
  type!: "automation" | "campaign" | "general" | null;
  status!: "new" | "viewed" | "completed" | "dismissed";
  actions!: string;
  plan?: string;
  acted_at?: Date;
  delivery_channel!: "email" | "text";
  static tableName = "insights";
}

/**
 * Determine if an insight is actionable by checking if it is labeled as automation/campaign
 * and has enough info to create a plan. You can expand this logic to fit your needs (e.g.,
 * checking specific fields, verifying data structure, etc.).
 */
export async function isInsightActionable(
  insightData: z.infer<typeof InsightSchema>
): Promise<boolean> {
  // Check if it's explicitly marked as not actionable
  if (insightData.not_actionable === true) {
    return false;
  }

  const title = insightData.title || "";
  const description = insightData.description || "";

  // Check database patterns for non-actionable insights
  const isNonActionable = await insightPatternService.isNonActionable(title, description);
  if (isNonActionable) {
    return false;
  }

  // Check database patterns for explicitly actionable insights
  const isExplicitlyActionable = await insightPatternService.isActionable(title, description);
  if (isExplicitlyActionable) {
    return true;
  }

  // Must be type "automation" or "campaign" and have at least one action
  const hasValidType =
    insightData.type === "automation" || insightData.type === "campaign";
  const hasActions = insightData.actions && insightData.actions.length > 0;

  return hasValidType && hasActions;
}

/**
 * Generate actionable insights using the LLM; if an insight is not actionable,
 * mark it accordingly or place it in a separate collection as desired.
 */
export async function generateInsights(
  locationId: number,
  timeframe: string = "30d",
  model?: string
): Promise<Insight[]> {
  // Get insights from the enhanced CrewAIService
  const insights = await crewAIService.generateInsights(
    locationId,
    timeframe,
    model
  );

  // Use the existing storeInsights method to save them
  if (insights && insights.length > 0) {
    try {
      await storeInsights(locationId, insights);
      console.log(
        `Successfully stored ${insights.length} insights for location ${locationId} using storeInsights`
      );
    } catch (error) {
      console.error("Error storing insights in database:", error);
    }
  }

  return insights;
}

/**
 * Check if an insight with similar title already exists for the location
 */
async function isDuplicateInsight(
  locationId: number,
  title: string,
  description: string
): Promise<boolean> {
  // Check for exact title match first
  const exactMatch = await Insight.query()
    .where("location_id", locationId)
    .where("title", title)
    .whereIn("status", ["new", "viewed"])
    .first();

  if (exactMatch) {
    return true;
  }

  // Check for similar titles (simple similarity check)
  const existingInsights = await Insight.query()
    .where("location_id", locationId)
    .whereIn("status", ["new", "viewed"])
    .select("title", "description");

  // Simple similarity check - if titles are very similar or descriptions contain similar key phrases
  const titleWords = title.toLowerCase().split(/\s+/);
  const descWords = description.toLowerCase().split(/\s+/);

  for (const existing of existingInsights) {
    const existingTitleWords = existing.title.toLowerCase().split(/\s+/);
    const existingDescWords = existing.description.toLowerCase().split(/\s+/);

    // Check if titles share significant words (more than 60% overlap)
    const titleOverlap = titleWords.filter(
      (word) => word.length > 3 && existingTitleWords.includes(word)
    ).length;
    const titleSimilarity =
      titleOverlap / Math.max(titleWords.length, existingTitleWords.length);

    // Check if descriptions share key phrases
    const descOverlap = descWords.filter(
      (word) => word.length > 4 && existingDescWords.includes(word)
    ).length;
    const descSimilarity =
      descOverlap / Math.max(descWords.length, existingDescWords.length);

    // Consider it a duplicate if title similarity > 60% or description similarity > 40%
    if (titleSimilarity > 0.6 || descSimilarity > 0.4) {
      return true;
    }
  }

  return false;
}

/**
 * Store a set of insights directly without LLM generation.
 * Now includes duplicate detection to prevent similar insights.
 */
export async function storeInsights(
  locationId: number,
  insights: Partial<Insight>[]
) {
  const uniqueInsights: Partial<Insight>[] = [];

  // Filter out duplicates
  for (const insight of insights) {
    const title = insight.title || "";
    const description = insight.description || "";

    if (title && !(await isDuplicateInsight(locationId, title, description))) {
      uniqueInsights.push(insight);
    } else {
      console.log(`Skipping duplicate insight: "${title}"`);
    }
  }

  if (uniqueInsights.length === 0) {
    console.log("No new unique insights to store");
    return [];
  }

  const formattedInsights = uniqueInsights.map((insight) => ({
    location_id: locationId,
    status: "new" as const,
    title: insight.title || "",
    description: insight.description || "",
    impact: insight.impact || ("medium" as const),
    type: insight.type || ("automation" as const),
    actions: Array.isArray(insight.actions)
      ? JSON.stringify(insight.actions)
      : JSON.stringify([]),
  }));

  console.log(
    `Storing ${formattedInsights.length} unique insights out of ${insights.length} total`
  );
  return await Insight.query().insert(formattedInsights);
}

/**
 * Fetch all insights for a given location.
 */
export async function getInsights(locationId: number) {
  return await Insight.query()
    .where("location_id", locationId)
    .whereIn("status", ["new", "viewed"])
    .orderBy("created_at", "desc");
}

/**
 * Find duplicate insights for a given location (for debugging/investigation)
 */
export async function findDuplicateInsights(locationId: number) {
  const duplicates = await Insight.query()
    .select("title")
    .count("* as count")
    .where("location_id", locationId)
    .whereIn("status", ["new", "viewed"])
    .groupBy("title")
    .having("count", ">", 1)
    .orderBy("count", "desc");

  const detailedDuplicates = [];

  for (const duplicate of duplicates) {
    const insights = await Insight.query()
      .where("location_id", locationId)
      .where("title", duplicate.title)
      .whereIn("status", ["new", "viewed"])
      .orderBy("created_at", "desc");

    detailedDuplicates.push({
      title: duplicate.title,
      count: duplicate.count,
      insights: insights.map((i: Insight) => ({
        id: i.id,
        description: i.description.substring(0, 100) + "...",
        created_at: i.created_at,
        status: i.status,
      })),
    });
  }

  return detailedDuplicates;
}

/**
 * Update the status of a given insight.
 */
export async function updateInsightStatus(
  insightId: number,
  status: Insight["status"]
) {
  return await Insight.query()
    .update({
      status,
      acted_at: status === "completed" ? new Date() : undefined,
      updated_at: new Date(),
    })
    .where("id", insightId);
}

/**
 * Check if there are any old insights that haven't been acted upon for over a week.
 * Returns a reminder automation if such insights exist.
 */
export function checkInsightEngagement(insights: Insight[]) {
  const oldUnactedInsights = insights.filter(
    (insight) =>
      ["new", "viewed"].includes(insight.status) &&
      new Date(insight.created_at).getTime() <
        Date.now() - 7 * 24 * 60 * 60 * 1000
  );

  if (oldUnactedInsights.length > 0) {
    return {
      id: "insight_engagement",
      title: "Unactioned Insights Pending",
      description: `You have ${oldUnactedInsights.length} insights older than 1 week that haven't been acted upon.`,
      impact: "medium" as const,
      type: "automation" as const,
      actions: ["Review pending insights", "Set up automated insight reviews"],
    };
  }
  return null;
}

/**
 * Calculate an AI-driven score for the business performance based on various metrics.
 */
export async function calculateAIScore(
  locationId: number,
  timeframe: string
): Promise<{
  score: number;
  factors: Array<{
    name: string;
    impact: "positive" | "negative";
    weight: number;
    description: string;
  }>;
}> {
  const factors: Array<{
    name: string;
    impact: "positive" | "negative";
    weight: number;
    description: string;
  }> = [];

  let totalScore = 70; // Base score
  const endDate = new Date();
  const days = parseInt(timeframe.replace("d", ""), 10) || 30;
  const startDate = subDays(endDate, days);
  const previousStartDate = subDays(startDate, days);

  const [currentMetrics, previousMetrics, campaignMetrics] = await Promise.all([
    PosData.query()
      .where("location_id", locationId)
      .whereBetween("order_date", [startDate, endDate])
      .select([
        PosData.raw("AVG(net_sales) as avg_order_value"),
        PosData.raw(
          "COUNT(DISTINCT customer_name) / COUNT(*) as customer_retention"
        ),
        PosData.raw(
          "COUNT(*) / COUNT(DISTINCT DATE(order_date)) as daily_orders"
        ),
        PosData.raw("SUM(loyalty_as_discount > 0) / COUNT(*) as loyalty_usage"),
      ])
      .first(),
    PosData.query()
      .where("location_id", locationId)
      .whereBetween("order_date", [previousStartDate, startDate])
      .select([
        PosData.raw("AVG(net_sales) as avg_order_value"),
        PosData.raw(
          "COUNT(DISTINCT customer_name) / COUNT(*) as customer_retention"
        ),
        PosData.raw(
          "COUNT(*) / COUNT(DISTINCT DATE(order_date)) as daily_orders"
        ),
      ])
      .first(),
    Campaign.query()
      .where("location_id", locationId)
      .where("created_at", ">=", startDate)
      .select([
        Campaign.raw(
          "AVG(CAST(JSON_EXTRACT(delivery, '$.opens') AS UNSIGNED) / CAST(JSON_EXTRACT(delivery, '$.sent') AS UNSIGNED)) as avg_open_rate"
        ),
        Campaign.raw(
          "AVG(CAST(JSON_EXTRACT(delivery, '$.clicks') AS UNSIGNED) / CAST(JSON_EXTRACT(delivery, '$.opens') AS UNSIGNED)) as avg_click_rate"
        ),
        Campaign.raw("COUNT(*) as campaign_count"),
      ])
      .first(),
  ]);

  if (!currentMetrics || !previousMetrics || !campaignMetrics) {
    console.warn("Not enough data to calculate AI score.");
    return { score: 70, factors: [] };
  }

  // Score calculation logic
  if (currentMetrics.avg_order_value > 50) {
    totalScore += 10;
    factors.push({
      name: "Average Order Value",
      impact: "positive",
      weight: 10,
      description: "Higher than industry average ($50)",
    });
  }

  if (currentMetrics.customer_retention > previousMetrics.customer_retention) {
    totalScore += 15;
    factors.push({
      name: "Customer Retention Trend",
      impact: "positive",
      weight: 15,
      description: "Improving retention rate compared to previous period",
    });
  }

  if (campaignMetrics.avg_open_rate > 0.2) {
    totalScore += 10;
    factors.push({
      name: "Email Engagement",
      impact: "positive",
      weight: 10,
      description: "Above average email open rates",
    });
  }

  if (campaignMetrics.avg_click_rate > 0.1) {
    totalScore += 10;
    factors.push({
      name: "Click Performance",
      impact: "positive",
      weight: 10,
      description: "Strong click-through rates on campaigns",
    });
  }

  if (campaignMetrics.campaign_count < 2) {
    totalScore -= 10;
    factors.push({
      name: "Marketing Activity",
      impact: "negative",
      weight: 10,
      description: "Low number of active campaigns",
    });
  }

  if (currentMetrics.daily_orders > previousMetrics.daily_orders) {
    totalScore += 10;
    factors.push({
      name: "Order Volume Trend",
      impact: "positive",
      weight: 10,
      description: "Increasing daily order volume",
    });
  }

  if (currentMetrics.loyalty_usage > 0.15) {
    totalScore += 10;
    factors.push({
      name: "Loyalty Adoption",
      impact: "positive",
      weight: 10,
      description: "Strong loyalty program usage",
    });
  }

  return {
    score: Math.min(Math.max(totalScore, 0), 100),
    factors,
  };
}

/**
 * Generate an automation plan for a specific insight by its ID.
 */
export async function generateAutomationPlanForInsight(
  insightId: number,
  model?: string,
  imageQuality: string = "HD"
) {
  const insight = await Insight.query().where("id", insightId).first();
  if (!insight) {
    throw new Error("Insight not found");
  }

  const plan = await crewAIService.generateAutomationPlan(
    insight,
    model,
    imageQuality
  );

  await Insight.query()
    .where("id", insightId)
    .update({
      plan: JSON.stringify(plan),
      updated_at: new Date(),
    });

  return plan;
}

/**
 * Generate list segmentation suggestions for targeted marketing or analysis.
 */
export async function getExistingResources(locationId: number) {
  try {
    // Add timeout and limit queries to essential fields only
    const queryTimeout = 5000; // 5 second timeout for each query

    const [tags, lists, campaigns, templates, journeys] = await Promise.all([
      Promise.race([
        Tag.query().where("location_id", locationId).select("name").limit(100),
        new Promise((_resolve, reject) =>
          setTimeout(() => reject(new Error("Tag query timeout")), queryTimeout)
        ),
      ]),
      Promise.race([
        // Fix: lists table doesn't have a tags column - remove it from select
        List.query()
          .where("location_id", locationId)
          .select("id", "name")
          .limit(100),
        new Promise((_resolve, reject) =>
          setTimeout(
            () => reject(new Error("List query timeout")),
            queryTimeout
          )
        ),
      ]),
      Promise.race([
        // Fix: campaigns table doesn't have a tags column - remove it from select
        Campaign.query()
          .where("location_id", locationId)
          .select("id", "name")
          .limit(100),
        new Promise((_resolve, reject) =>
          setTimeout(
            () => reject(new Error("Campaign query timeout")),
            queryTimeout
          )
        ),
      ]),
      Promise.race([
        Template.query()
          .where("location_id", locationId)
          .select("id", "type", "campaign_id")
          .limit(100),
        new Promise((_resolve, reject) =>
          setTimeout(
            () => reject(new Error("Template query timeout")),
            queryTimeout
          )
        ),
      ]),
      Promise.race([
        // Fix: journeys table doesn't have a tags column - remove it from select
        Journey.query()
          .where("location_id", locationId)
          .select("id", "name")
          .limit(100),
        new Promise((_resolve, reject) =>
          setTimeout(
            () => reject(new Error("Journey query timeout")),
            queryTimeout
          )
        ),
      ]),
    ]);

    return {
      tags: (tags as any[]).map((t: any) => t.name),
      lists: (lists as any[]).map((l: any) => ({
        id: l.id,
        name: l.name,
        tags: [],
      })), // Tags are stored separately in entity_tags table
      campaigns: (campaigns as any[]).map((c: any) => ({
        id: c.id,
        name: c.name,
        tags: [], // Tags are stored separately in entity_tags table
      })),
      templates: (templates as any[]).map((t: any) => ({
        id: t.id,
        type: t.type,
        campaign_id: t.campaign_id,
      })),
      journeys: (journeys as any[]).map((j: any) => ({
        id: j.id,
        name: j.name,
        tags: [], // Tags are stored separately in entity_tags table
      })),
    };
  } catch (error) {
    console.warn("Error fetching existing resources:", error);
    // Return empty resources if queries fail
    return {
      tags: [],
      lists: [],
      campaigns: [],
      templates: [],
      journeys: [],
    };
  }
}

export async function generateListSuggestions(
  locationId: number
): Promise<any[]> {
  const [customerData, transactionMetrics, existingResources] =
    await Promise.all([
      PosData.query()
        .where("location_id", locationId)
        .select([
          "customer_name",
          "customer_type",
          PosData.raw("SUM(net_sales) as total_spent"),
          PosData.raw("COUNT(DISTINCT DATE(order_date)) as visit_count"),
          PosData.raw("MAX(order_date) as last_visit"),
        ])
        .groupBy(["customer_name", "customer_type"])
        .limit(100),

      PosData.query()
        .where("location_id", locationId)
        .select([
          PosData.raw("AVG(net_sales) as avg_order_value"),
          PosData.raw("COUNT(DISTINCT customer_name) as total_customers"),
          PosData.raw("MAX(net_sales) as highest_order"),
          PosData.raw("MIN(net_sales) as lowest_order"),
        ])
        .first(),

      getExistingResources(locationId),
    ]);

  const prompt = `
      As a customer segmentation expert, analyze this customer data and suggest 3-5 new customer segments (lists) that don't already exist.
    Make sure you don't create new lists that have the same name or identical rules as the existing lists in the system.
  
    Existing resources in the system:
    ${JSON.stringify(existingResources, null, 2)}

    Customer data for analysis:
    ${JSON.stringify({ customerData, transactionMetrics }, null, 2)}
   
    Return an array of items, where each item can be either:

    {
      "type": "list",
      "config": {
        "list": {
          "name": "List Name",
          "description": "Description text",
          "type": "dynamic",
          "is_visible": true,
          "tags": ["tag1", "tag2"],
          "rule": {
            "uuid": "UUID-STRING",
            "root_uuid": null,
            "parent_uuid": null,
            "type": "wrapper",
            "group": "parent",
            "path": "$",
            "operator": "and",
            "children": [
              {
                "uuid": "CHILD-UUID-STRING",
                "root_uuid": "UUID-STRING",
                "parent_uuid": "UUID-STRING",
                "type": "string|number|date|boolean",
                "group": "user",
                "path": "$.field_name",
                "operator": "=|>|<|>=|<=|contains",
                "value": "actual-value"
              }
            ]
          }
        }
      }
    }

    Return ONLY valid JSON array with items for creating 3-5 segments.`;

  const model = new ChatOpenAI({
    modelName: "gpt-4.1-mini",
    temperature: 0.7,
    openAIApiKey: process.env.OPENAI_API_KEY,
  });

  try {
    const response = await model.call([
      new SystemMessage(
        "You are a customer segmentation expert. Return only valid JSON."
      ),
      new HumanMessage(prompt),
    ]);

    return JSON.parse(
      typeof response.content === "string"
        ? response.content
        : JSON.stringify(response.content)
    );
  } catch (error) {
    console.error("Error generating list suggestions:", error);
    throw error;
  }
}

/**
 * Handle natural language requests for marketing automation
 */
export async function handleNaturalLanguageRequest(
  locationId: number,
  request: string,
  model?: string
): Promise<any> {
  return await crewAIService.handleRequest(locationId, request, model);
}

/**
 * Regenerate a specific step in an automation plan
 */
export async function regenerateStep(
  insightId: number,
  itemKey: string,
  model?: string,
  prompt?: string,
  imageQuality?: string
): Promise<any> {
  const insight = await Insight.query().where("id", insightId).first();
  if (!insight) {
    throw new Error("Insight not found");
  }

  return await crewAIService.regenerateItem(
    insight,
    itemKey,
    model,
    prompt,
    imageQuality
  );
}

/**
 * Create journeys from a generated automation plan
 */
export async function createJourneysFromPlan(plan: any, locationId: number) {
  if (!plan || !plan.items) {
    console.log("No plan or items to process for journeys");
    return null;
  }

  const journeyItems = Object.entries(plan.items)
    .filter(([_, item]: [string, any]) => item.type === "journey")
    .map(([key, item]: [string, any]) => ({ key, item }));

  console.log(`Found ${journeyItems.length} journeys to create from plan`);

  if (journeyItems.length === 0) {
    return null;
  }

  const createdJourneys: any[] = [];

  for (const { key, item } of journeyItems) {
    try {
      const journeyConfig = item.config?.journey;
      if (!journeyConfig) {
        console.error(`Journey ${key} has no config`);
        continue;
      }

      console.log(`Creating journey: ${journeyConfig.name}`);

      // Create the journey in the database
      const journey = await createJourney(locationId, {
        name: journeyConfig.name,
        description: journeyConfig.description || "",
        published: journeyConfig.published || false,
        tags: journeyConfig.tags || [],
      });

      if (journey) {
        console.log(`Successfully created journey ${journey.id}`);

        // TODO: Implement steps creation when journey API is available
        // This would create steps from journeyConfig.steps

        createdJourneys.push(journey);
      }
    } catch (error) {
      console.error(`Error creating journey ${key}:`, error);
    }
  }

  return createdJourneys.length > 0 ? createdJourneys : null;
}
