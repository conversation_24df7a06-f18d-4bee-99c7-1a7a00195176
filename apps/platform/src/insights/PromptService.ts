import fs, { promises as fsPromises } from "fs";
import path from "path";
import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";

interface TrainingExample {
  input: any;
  output: any;
  success: boolean;
  error?: string;
}

interface ErrorPattern {
  pattern: string;
  fix: string;
  count: number;
}

interface PromptConfig {
  base_prompt: string;
  constraints: string[];
  training_examples: TrainingExample[];
  error_patterns: ErrorPattern[];
}

interface PromptIndex {
  version: string;
  files: {
    base_prompt: string;
    constraints: string;
    training_examples: string;
    error_patterns: string;
  };
}

export class AutomationPromptService {
  private basePrompt = `You are a marketing automation expert creating step-by-step automation plans. 
Your task is to create practical, efficient automation plans that follow our system's constraints.

IMPORTANT - How the system works:
1. The frontend receives your JSON plan and processes each item in sequential order
2. Each item depends on previous items being completed first
3. Items are stored in a dictionary with unique keys, not an array
4. Lists can be referenced in two ways (never both):
   - For new lists created in your plan: Use list_refs: ["listKey"] where list<PERSON>ey is the key of the list in the items dictionary
   - For existing lists: Use list_ids: [number]
5. Templates must reference campaigns using ONE of these approaches (never both):
   - For new campaigns: Use campaign_ref: "campaignKey" where campaignKey is the key of the campaign in the items dictionary
   - For existing campaigns: Use campaign_id: number

Items must be created in this order:
1. Lists (if needed)
2. Campaigns
3. Templates (referencing campaigns from above)

Return a valid JSON object containing:
- name: string
- description: string
- items: dictionary of named items`;

  private stepConstraints = `Each item must follow these rules:
1. List items:
   - Must have config.list object
   - Must include name, type, and description
   - Dynamic lists must have a valid rule

2. Campaign items:
   - Must have config.campaign object
   - Must specify type, name, channel, subscription_id, provider_id
   - Must include either list_refs (array of keys) or list_ids (array of numbers), never both
   - Can optionally include exclusion_list_refs (array of keys) or exclusion_list_ids (array of numbers), never both

3. Template items:
   - Must have config.template object
   - Must match campaign channel type (email/text)
   - Must reference campaign using ONE of:
     a) campaign_ref: "campaignKey" for new campaigns
     b) campaign_id: number for existing campaigns
   - Never use both campaign_ref and campaign_id
   - Email templates require from.name, from.address, reply_to
   - Text templates require text content`;

  private validationRules = `Validation requirements:
1. No undefined or null values (use empty string "" instead)
2. Use ISO date strings (e.g. "2025-01-02T00:00:00Z")
3. All required fields must be present
4. Campaign references must be correct:
   - New campaigns: Use campaign_ref: "campaignKey"
   - Existing campaigns: Use numeric ID only
5. Template items must reference campaign items that come BEFORE them in the dictionary`;

  // Updated variables for modular prompts
  private promptPath: string;
  private indexPath: string;
  private modulePath: string;
  private config: PromptConfig = {
    base_prompt: "",
    constraints: [],
    training_examples: [],
    error_patterns: [],
  };

  private model: ChatOpenAI;
  private isModular: boolean = false;

  constructor(promptName: string) {
    // Handle both development and production paths
    const isDevelopment = process.env.NODE_ENV === "development";
    const basePath = isDevelopment
      ? path.join(__dirname)
      : path.join(__dirname, "..");

    // Legacy prompt file path
    this.promptPath = path.join(basePath, "prompts", `${promptName}.json`);

    // New modular paths
    this.modulePath = path.join(basePath, "prompts", promptName);
    this.indexPath = path.join(this.modulePath, "index.json");

    // Check if modular structure exists
    this.isModular = fs.existsSync(this.indexPath);

    // Load config - this now handles both modular and legacy formats
    this.loadConfig();

    // Initialize LLM
    this.model = new ChatOpenAI({
      modelName: "gpt-4.1-mini",
      temperature: 0.3,
      openAIApiKey: process.env.OPENAI_API_KEY,
    });
  }

  // Updated to handle both legacy and modular formats
  private loadConfig(): void {
    try {
      if (this.isModular) {
        // Load from modular structure
        this.config = this.loadModularConfig();
        console.log("Successfully loaded modular prompt configuration");
      } else {
        // Legacy loading
        if (!fs.existsSync(this.promptPath)) {
          this.initializeDefaultConfig();
        } else {
          const content = fs.readFileSync(this.promptPath, "utf-8");
          this.config = JSON.parse(content);
        }
        console.log("Successfully loaded legacy prompt configuration");
      }
    } catch (error) {
      console.error("Error loading prompt config:", error);
      this.initializeDefaultConfig();
    }
  }

  // New method to load modular configuration
  private loadModularConfig(): PromptConfig {
    // Read the index file
    const indexContent = fs.readFileSync(this.indexPath, "utf-8");
    const index: PromptIndex = JSON.parse(indexContent);

    // Read each component file
    const basePromptPath = path.join(this.modulePath, index.files.base_prompt);
    const constraintsPath = path.join(this.modulePath, index.files.constraints);
    const trainingExamplesPath = path.join(
      this.modulePath,
      index.files.training_examples
    );
    const errorPatternsPath = path.join(
      this.modulePath,
      index.files.error_patterns
    );

    // Load content from each file
    const basePromptContent = JSON.parse(
      fs.readFileSync(basePromptPath, "utf-8")
    );
    const constraintsContent = JSON.parse(
      fs.readFileSync(constraintsPath, "utf-8")
    );
    const trainingExamplesContent = JSON.parse(
      fs.readFileSync(trainingExamplesPath, "utf-8")
    );
    const errorPatternsContent = JSON.parse(
      fs.readFileSync(errorPatternsPath, "utf-8")
    );

    // Construct the config
    return {
      base_prompt: basePromptContent.base_prompt,
      constraints: constraintsContent.constraints,
      training_examples: trainingExamplesContent.training_examples,
      error_patterns: errorPatternsContent.error_patterns || [],
    };
  }

  // Initialize with default config
  private initializeDefaultConfig(): void {
    this.config = {
      base_prompt: this.basePrompt,
      constraints: [this.stepConstraints, this.validationRules],
      training_examples: [
        // Default example removed for brevity - see original code
      ],
      error_patterns: [],
    };

    // Write default to file
    this.saveConfig();
  }

  // Updated to save to appropriate structure
  private saveConfig(): void {
    try {
      if (this.isModular) {
        this.saveModularConfig();
      } else {
        // Create directory if it doesn't exist
        const promptDir = path.dirname(this.promptPath);
        if (!fs.existsSync(promptDir)) {
          fs.mkdirSync(promptDir, { recursive: true });
        }

        // Write to legacy file
        fs.writeFileSync(
          this.promptPath,
          JSON.stringify(this.config, null, 2),
          "utf-8"
        );
      }
    } catch (error) {
      console.error("Error saving prompt config:", error);
    }
  }

  // Async version of saveConfig for better performance
  private async saveConfigAsync(): Promise<void> {
    try {
      if (this.isModular) {
        await this.saveModularConfigAsync();
      } else {
        // Create directory if it doesn't exist
        const promptDir = path.dirname(this.promptPath);
        try {
          await fsPromises.access(promptDir);
        } catch {
          await fsPromises.mkdir(promptDir, { recursive: true });
        }

        // Write to legacy file
        await fsPromises.writeFile(
          this.promptPath,
          JSON.stringify(this.config, null, 2),
          "utf-8"
        );
      }
    } catch (error) {
      console.error("Error saving prompt config:", error);
    }
  }

  // New method to save modular configuration
  private saveModularConfig(): void {
    // Write each component to its file
    const indexContent = JSON.parse(fs.readFileSync(this.indexPath, "utf-8"));
    const index: PromptIndex = indexContent;

    // Save each component
    const basePromptPath = path.join(this.modulePath, index.files.base_prompt);
    const constraintsPath = path.join(this.modulePath, index.files.constraints);
    const trainingExamplesPath = path.join(
      this.modulePath,
      index.files.training_examples
    );
    const errorPatternsPath = path.join(
      this.modulePath,
      index.files.error_patterns
    );

    fs.writeFileSync(
      basePromptPath,
      JSON.stringify({ base_prompt: this.config.base_prompt }, null, 2),
      "utf-8"
    );

    fs.writeFileSync(
      constraintsPath,
      JSON.stringify({ constraints: this.config.constraints }, null, 2),
      "utf-8"
    );

    fs.writeFileSync(
      trainingExamplesPath,
      JSON.stringify(
        { training_examples: this.config.training_examples },
        null,
        2
      ),
      "utf-8"
    );

    fs.writeFileSync(
      errorPatternsPath,
      JSON.stringify({ error_patterns: this.config.error_patterns }, null, 2),
      "utf-8"
    );
  }

  // Async version of saveModularConfig for better performance
  private async saveModularConfigAsync(): Promise<void> {
    try {
      // Read the index file
      const indexContent = await fsPromises.readFile(this.indexPath, "utf-8");
      const index: PromptIndex = JSON.parse(indexContent);

      // Save each component
      const basePromptPath = path.join(
        this.modulePath,
        index.files.base_prompt
      );
      const constraintsPath = path.join(
        this.modulePath,
        index.files.constraints
      );
      const trainingExamplesPath = path.join(
        this.modulePath,
        index.files.training_examples
      );
      const errorPatternsPath = path.join(
        this.modulePath,
        index.files.error_patterns
      );

      // Write all files in parallel
      await Promise.all([
        fsPromises.writeFile(
          basePromptPath,
          JSON.stringify({ base_prompt: this.config.base_prompt }, null, 2),
          "utf-8"
        ),
        fsPromises.writeFile(
          constraintsPath,
          JSON.stringify({ constraints: this.config.constraints }, null, 2),
          "utf-8"
        ),
        fsPromises.writeFile(
          trainingExamplesPath,
          JSON.stringify(
            { training_examples: this.config.training_examples },
            null,
            2
          ),
          "utf-8"
        ),
        fsPromises.writeFile(
          errorPatternsPath,
          JSON.stringify(
            { error_patterns: this.config.error_patterns },
            null,
            2
          ),
          "utf-8"
        ),
      ]);
    } catch (error) {
      console.error("Error saving modular config:", error);
    }
  }

  public getPrompt(input: any): string {
    const relevantExamples = this.getRelevantExamples(input);
    const errorPatterns = this.getCommonErrorPatterns();

    return `
${this.config.base_prompt}

CONSTRAINTS:
${this.config.constraints.map((c) => `- ${c}`).join("\n")}

${
  relevantExamples.length > 0
    ? `
SUCCESSFUL EXAMPLES:
${relevantExamples
  .map(
    (ex) => `
Input: ${JSON.stringify(ex.input)}
Output: ${JSON.stringify(ex.output)}
`
  )
  .join("\n")}`
    : ""
}

${
  errorPatterns.length > 0
    ? `
COMMON ERRORS TO AVOID:
${errorPatterns.map((ep) => `- ${ep.pattern}: ${ep.fix}`).join("\n")}`
    : ""
}

Current Input:
${JSON.stringify(input)}

Generate a response following the above constraints and examples.`;
  }

  private getRelevantExamples(input: any): TrainingExample[] {
    // Get successful examples that are most similar to current input
    // For now, just return the most recent successful examples
    // TODO: Implement semantic similarity search for better example selection
    return this.config.training_examples.filter((ex) => ex.success).slice(-2); // Get last 2 successful examples
  }

  private getCommonErrorPatterns(): ErrorPattern[] {
    // Return error patterns sorted by frequency
    return this.config.error_patterns
      .sort((a, b) => b.count - a.count)
      .slice(0, 3); // Top 3 most common errors
  }

  public async addTrainingExample(example: TrainingExample) {
    try {
      // Add journey-specific validation for training examples
      if (example.input?.type === "journey") {
        try {
          await this.validateJourney(example.output);
        } catch (error) {
          console.warn("Invalid journey in training example:", error);
          example.success = false;
          example.error =
            error instanceof Error ? error.message : String(error);
        }
      }

      this.config.training_examples.push(example);

      // Process error patterns asynchronously if needed
      if (!example.success && example.error) {
        // Use Promise.allSettled to handle potential failures gracefully
        const [patternResult, fixResult] = await Promise.allSettled([
          this.extractErrorPattern(example.error, example.output),
          this.generateErrorFix(example.error, example.output, example.input),
        ]);

        if (
          patternResult.status === "fulfilled" &&
          fixResult.status === "fulfilled"
        ) {
          const pattern = patternResult.value;
          const fix = fixResult.value;

          const existingPattern = this.config.error_patterns.find(
            (ep) => ep.pattern === pattern
          );

          if (existingPattern) {
            existingPattern.count++;
            // Update fix if it's more detailed
            if (fix.length > existingPattern.fix.length) {
              existingPattern.fix = fix;
            }
          } else {
            this.config.error_patterns.push({
              pattern,
              fix,
              count: 1,
            });
          }
        } else {
          console.warn("Failed to process error patterns:", {
            patternError:
              patternResult.status === "rejected" ? patternResult.reason : null,
            fixError: fixResult.status === "rejected" ? fixResult.reason : null,
          });
        }
      }

      // Save config asynchronously
      await this.saveConfigAsync();
    } catch (error) {
      console.error("Error adding training example:", error);
      // Don't throw - this is a background operation
    }
  }

  private async extractErrorPattern(
    error: string,
    failedOutput: any
  ): Promise<string> {
    const prompt = `
Analyze this error and output to extract a general error pattern.
The pattern should be reusable for similar errors.

Error: ${error}
Failed Output: ${JSON.stringify(failedOutput, null, 2)}

Return just the pattern, no explanation needed.
Example patterns:
- "Invalid JSON structure"
- "Missing required field"
- "Invalid data type for field"
`;

    try {
      // Add timeout to the API call
      const timeoutPromise = new Promise<never>((_resolve, reject) => {
        setTimeout(() => reject(new Error("OpenAI API timeout")), 10000); // 10 second timeout
      });

      const response = await Promise.race([
        this.model.call([
          new SystemMessage(
            "You are an error analysis expert. Be concise and precise."
          ),
          new HumanMessage(prompt),
        ]),
        timeoutPromise,
      ]);

      return typeof response.content === "string"
        ? response.content.trim()
        : "Unknown error pattern";
    } catch (error) {
      console.warn("Failed to extract error pattern:", error);
      return "Unknown error pattern";
    }
  }

  private async generateErrorFix(
    error: string,
    failedOutput: any,
    input: any
  ): Promise<string> {
    const prompt = `
Analyze this error and generate a clear fix instruction.
The fix should help prevent similar errors in the future.

Error: ${error}
Failed Output: ${JSON.stringify(failedOutput, null, 2)}
Input Context: ${JSON.stringify(input, null, 2)}

Return a concise, actionable fix instruction.
Example fixes:
- "Ensure all JSON objects have matching braces and quotes"
- "Include all required fields in the response"
- "Use correct data types for all fields"
`;

    try {
      // Add timeout to the API call
      const timeoutPromise = new Promise<never>((_resolve, reject) => {
        setTimeout(() => reject(new Error("OpenAI API timeout")), 10000); // 10 second timeout
      });

      const response = await Promise.race([
        this.model.call([
          new SystemMessage(
            "You are a debugging expert. Be clear and actionable."
          ),
          new HumanMessage(prompt),
        ]),
        timeoutPromise,
      ]);

      return typeof response.content === "string"
        ? response.content.trim()
        : "Ensure the output follows the correct format and constraints";
    } catch (error) {
      console.warn("Failed to generate error fix:", error);
      return "Ensure the output follows the correct format and constraints";
    }
  }

  private validateJourneyStep(
    step: any,
    stepKey: string,
    steps: Record<string, any>
  ): void {
    // Validate basic step structure
    if (!step.type || !step.data || !Array.isArray(step.children)) {
      throw new Error(`Invalid step structure for ${stepKey}`);
    }

    // Validate coordinates
    if (step.type === "entrance" && (step.x !== 0 || step.y !== 0)) {
      throw new Error("Entrance step must be at coordinates (0, 0)");
    }

    // Validate step-specific requirements
    switch (step.type) {
      case "entrance": {
        if (
          !step.data.trigger ||
          !["schedule", "event", "api"].includes(step.data.trigger)
        ) {
          throw new Error("Entrance step must have valid trigger type");
        }
        if (step.data.trigger === "schedule" && !step.data.schedule) {
          throw new Error("Schedule trigger requires schedule configuration");
        }
        if (step.data.trigger === "event" && !step.data.event_name) {
          throw new Error("Event trigger requires event_name");
        }
        break;
      }

      case "gate": {
        if (
          !step.data.rule ||
          !step.data.rule.path ||
          !step.data.rule.operator
        ) {
          throw new Error("Gate step must have valid rule configuration");
        }
        const hasYesNoPath =
          step.children.some((c: { path: string }) => c.path === "yes") &&
          step.children.some((c: { path: string }) => c.path === "no");
        if (!hasYesNoPath) {
          throw new Error("Gate step must have both yes and no paths");
        }
        break;
      }

      case "experiment": {
        const ratios = step.children.map(
          (c: { data?: { ratio?: number } }) => c.data?.ratio || 0
        );
        const totalRatio = ratios.reduce(
          (sum: number, r: number) => sum + r,
          0
        );
        if (totalRatio !== 100) {
          throw new Error("Experiment step ratios must sum to 100");
        }
        break;
      }

      case "action": {
        if (!step.data.campaign_id && !step.data.template_id) {
          throw new Error(
            "Action step must have either campaign_id or template_id"
          );
        }
        break;
      }

      case "delay": {
        const hasValidDelay =
          (step.data.duration && step.data.unit) ||
          step.data.until_time ||
          step.data.until_date;
        if (!hasValidDelay) {
          throw new Error(
            "Delay step must specify duration+unit or until_time/date"
          );
        }
        break;
      }

      case "exit": {
        if (!step.data.entrance_uuid) {
          throw new Error("Exit step must reference entrance_uuid");
        }
        // Verify entrance exists
        if (!steps[step.data.entrance_uuid]) {
          throw new Error(
            `Exit references non-existent entrance: ${step.data.entrance_uuid}`
          );
        }
        break;
      }
    }

    // Validate children references
    step.children.forEach((child: any) => {
      if (!child.external_id || !steps[child.external_id]) {
        throw new Error(
          `Invalid child reference in step ${stepKey}: ${child.external_id}`
        );
      }
    });
  }

  private validateJourneyLayout(steps: Record<string, any>): void {
    // Track used coordinates to ensure uniqueness
    const usedCoords = new Set<string>();

    // Track levels for vertical spacing
    const levelYCoords = new Map<number, number>();

    Object.entries(steps).forEach(([key, step]) => {
      const coordKey = `${step.x},${step.y}`;
      if (usedCoords.has(coordKey)) {
        throw new Error(`Duplicate coordinates at ${coordKey}`);
      }
      usedCoords.add(coordKey);

      // Check vertical spacing
      const level = Math.round(step.y / 200); // Assuming ~200px spacing
      if (levelYCoords.has(level) && levelYCoords.get(level) !== step.y) {
        throw new Error(`Inconsistent vertical spacing at level ${level}`);
      }
      levelYCoords.set(level, step.y);

      // Check horizontal spacing for parallel paths
      const sameLevel = Object.values(steps).filter(
        (s: { x: number; y: number }) =>
          Math.abs(s.y - step.y) < 50 && s !== step
      );
      sameLevel.forEach((other) => {
        const spacing = Math.abs(other.x - step.x);
        if (spacing > 0 && spacing < 200) {
          throw new Error(
            `Insufficient horizontal spacing between parallel steps: ${spacing}px`
          );
        }
      });
    });
  }

  public async validateJourney(journey: any): Promise<void> {
    if (!journey.steps || typeof journey.steps !== "object") {
      throw new Error("Journey must have steps object");
    }

    // Validate each step
    Object.entries(journey.steps).forEach(([key, step]) => {
      this.validateJourneyStep(step, key, journey.steps);
    });

    // Validate overall layout
    this.validateJourneyLayout(journey.steps);

    // Validate path connectivity
    const visited = new Set<string>();
    const entrance = Object.entries(journey.steps).find(
      ([_, s]: any) => s.type === "entrance"
    );
    if (!entrance) {
      throw new Error("Journey must have an entrance step");
    }

    // DFS to verify all steps are connected
    const traverse = (stepKey: string) => {
      if (visited.has(stepKey)) return;
      visited.add(stepKey);
      const step = journey.steps[stepKey];
      step.children.forEach((child: any) => traverse(child.external_id));
    };
    traverse(entrance[0]);

    // Check if all steps are reachable
    const unreachable = Object.keys(journey.steps).filter(
      (k) => !visited.has(k)
    );
    if (unreachable.length > 0) {
      throw new Error(`Unreachable steps found: ${unreachable.join(", ")}`);
    }
  }

  // New method to get the current configuration
  public getConfig(): PromptConfig {
    return this.config;
  }
}
