import { Chat<PERSON>penAI } from "@langchain/openai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";

/**
 * Content Generation Agent: Responsible for generating content for templates and other
 * materials based on marketing insights.
 */
export class ContentGenerationAgent {
  // This is a simplified implementation
  async generateTemplateVariants(
    locationId: number,
    channel: string,
    context: any,
    model: string,
    numVariants: number = 3
  ): Promise<any[]> {
    const llm = new ChatOpenAI({
      modelName: model || "gpt-4.1-mini",
      temperature: 0.7,
      openAIApiKey: process.env.OPENAI_API_KEY,
    });

    const prompt = `
      Generate ${numVariants} compelling ${channel} template variants based on the following context:
      
      ${JSON.stringify(context, null, 2)}
      
      ${
        channel === "email"
          ? `
      FOR EMAIL TEMPLATES:
      - Include detailed imagePrompt field with a descriptive prompt for generating a beautiful hero image
      - The imagePrompt should be highly detailed, specific, and visually descriptive (30-50 words)
      - Describe a professional, engaging image that will draw attention in an email header
      - The image should match the email's theme, tone, and message
      - Avoid generic descriptions; be specific about colors, style, composition, and mood
      
      CREATE BEAUTIFUL HTML EMAIL TEMPLATES:
      - Use modern, responsive HTML email design with inline CSS styles
      - Include a header, content sections, and footer
      - Use attractive color schemes that match the brand and message
      - Incorporate well-structured divs with proper padding and margins
      - Use web-safe fonts and font stacks for better compatibility
      - Add visual elements like buttons, dividers, and spacing
      - Ensure the template is mobile-responsive with media queries
      - Include call-to-action buttons with hover effects
      - Use a proper HTML structure with <!DOCTYPE html> and <head> sections
      `
          : ""
      }
      
      Return a JSON array with ${numVariants} template variants in the following format:
      [
        {
          "name": "Template Name",
          "subject": "Email Subject Line", 
          "preheader": "Preheader text for email",
          "html": "HTML content with proper styling and structure",
          "text": "Plain text version of the content",
          ${
            channel === "email"
              ? '"imagePrompt": "Detailed description for a beautiful marketing image that matches the content and theme",'
              : ""
          }
          "from": {
            "name": "Sender Name",
            "email": "<EMAIL>"
          },
          "reply_to": "<EMAIL>"
        }
      ]
    `;

    const response = await llm.call([
      new SystemMessage(
        "You are a marketing content specialist with expertise in both copywriting and visual design. Generate compelling marketing templates with vivid, detailed image descriptions for email headers and beautiful HTML email designs that follow modern email development best practices."
      ),
      new HumanMessage(prompt),
    ]);

    const content =
      typeof response.content === "string"
        ? response.content
        : JSON.stringify(response.content);

    // Clean and parse the response
    const cleaned = content
      .replace(/```json\s*|\s*```/g, "")
      .replace(/^`|`$/g, "")
      .trim();

    try {
      const templates = JSON.parse(cleaned);

      // Enhance HTML for email templates if needed
      if (channel === "email") {
        return templates.map((template: any) => {
          if (template.html && !template.html.includes("<!DOCTYPE html>")) {
            // Add proper HTML structure if it's missing
            template.html = this.enhanceEmailHTML(
              template.html,
              template.subject || "Marketing Email"
            );
          }
          return template;
        });
      }

      return templates;
    } catch (error) {
      console.error("Failed to parse generated template variants:", error);
      return [];
    }
  }

  /**
   * Enhances basic HTML to create proper email template structure
   * @param html The basic HTML content
   * @param subject The email subject for title
   * @returns Enhanced, structured HTML for email
   */
  private enhanceEmailHTML(html: string, subject: string): string {
    // Check if HTML already has proper structure
    if (html.includes("<!DOCTYPE html>") && html.includes("<head>")) {
      return html;
    }

    const content = html.replace(/<html>|<body>|<\/html>|<\/body>/g, "").trim();

    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${subject}</title>
    <style type="text/css">
        /* Base styles */
        body, html {
            margin: 0;
            padding: 0;
            font-family: Arial, Helvetica, sans-serif;
            line-height: 1.6;
            color: #333333;
        }
        * {
            box-sizing: border-box;
        }
        img {
            max-width: 100%;
            border: 0;
        }
        /* Container styles */
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
        }
        .content-block {
            padding: 20px;
        }
        /* Header styles */
        .header {
            text-align: center;
            padding: 0;
        }
        .header img {
            width: 100%;
            max-width: 600px;
            height: auto;
            display: block;
        }
        /* Button styles */
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            margin: 15px 0;
        }
        .button:hover {
            background-color: #45a049;
        }
        /* Footer styles */
        .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #666666;
            background-color: #f5f5f5;
        }
        /* Mobile responsiveness */
        @media screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
            }
            .content-block {
                padding: 15px !important;
            }
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f5f5f5;">
    <div class="email-container">
        <div class="header">
            <!-- Image will be inserted here by the image generation process -->
        </div>
        <div class="content-block">
            ${content}
        </div>
        <div class="footer">
            <p>© 2023 Company Name. All rights reserved.</p>
            <p>You're receiving this email because you signed up for updates.</p>
            <p><a href="#unsubscribe">Unsubscribe</a> | <a href="#preferences">Update Preferences</a></p>
        </div>
    </div>
</body>
</html>`;
  }

  /**
   * Enhances a basic prompt to create better marketing images
   * @param prompt The basic image prompt
   * @returns Enhanced, detailed prompt
   */
  private enhanceImagePrompt(prompt: string): string {
    // Skip enhancement if prompt is already detailed
    if (prompt.length > 150) return prompt;

    return `Create a beautiful, professional marketing email header image with the following characteristics:
- High-quality, visually striking design suitable for email marketing
- Professional and modern aesthetic with balanced composition
- Clean layout with appropriate negative space
- No text overlay (will be added separately)
- Suitable as a hero image at the top of an email
- Image should grab attention and convey the message visually

The image should communicate: ${prompt}

Create something that would appear in a premium marketing campaign from a top brand.`.trim();
  }

  /**
   * Process template variants after they're generated to enhance them with images
   * @param locationId ID of the location
   * @param templates Array of template data objects
   * @returns Enhanced template array with processed images
   */
  async processTemplateVariants(
    locationId: number,
    templates: any[]
  ): Promise<any[]> {
    const processedTemplates = [];

    for (const template of templates) {
      // Make a deep copy to avoid modifying the original
      const processedTemplate = JSON.parse(JSON.stringify(template));

      // Generate and insert image if this is an email template with an imagePrompt
      if (processedTemplate.imagePrompt && !processedTemplate.imageUrl) {
        try {
          // Import here to avoid circular dependencies
          const {
            imageGenerationService,
          } = require("../ImageGenerationService");

          // Enhance the prompt for better results
          const enhancedPrompt = this.enhanceImagePrompt(
            processedTemplate.imagePrompt
          );

          const imageUrl = await imageGenerationService.generateImage(
            enhancedPrompt,
            { quality: "hd", size: "1024x1024", style: "vivid" }
          );

          processedTemplate.imageUrl = imageUrl;

          // Insert the image into the HTML content if it's not already there
          if (
            processedTemplate.html &&
            !processedTemplate.html.includes(imageUrl)
          ) {
            const imgHtml = `<img src="${imageUrl}" alt="${
              processedTemplate.subject || "Email header"
            }" style="width:100%;max-width:600px;height:auto;display:block;margin:0 auto;" />`;

            // For modern HTML email structure, insert in header div
            if (processedTemplate.html.includes('<div class="header">')) {
              processedTemplate.html = processedTemplate.html.replace(
                '<div class="header">',
                `<div class="header">${imgHtml}`
              );
            } else if (processedTemplate.html.includes("<body>")) {
              processedTemplate.html = processedTemplate.html.replace(
                "<body>",
                "<body>" + imgHtml
              );
            } else if (processedTemplate.html.includes("<p>")) {
              processedTemplate.html = processedTemplate.html.replace(
                "<p>",
                imgHtml + "<p>"
              );
            } else {
              processedTemplate.html = imgHtml + processedTemplate.html;
            }
          }
        } catch (error) {
          console.error(
            "Failed to generate or insert image for template:",
            error
          );
          // Continue without image
        }
      }

      processedTemplates.push(processedTemplate);
    }

    return processedTemplates;
  }
}
