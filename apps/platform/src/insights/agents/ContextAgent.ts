import { subDays, startOfDay, endOfDay } from "date-fns";
import { Insight, getExistingResources } from "../InsightService";
import { subscriptionForChannel } from "../../subscriptions/SubscriptionService";
import { getProviderByGroup } from "../../providers/ProviderRepository";
import Location from "../../locations/Location";
import { RequestError } from "../../core/errors";
import { PosData } from "../../pos/PosData";
import { ChannelConfigs } from "./types";

/**
 * Context Agent: Responsible for gathering and formatting all necessary data
 * before making LLM calls.
 */
export class ContextAgent {
  private getTimeframeDates(timeframe: string = "30d"): {
    start: Date;
    end: Date;
  } {
    const days = parseInt(timeframe.replace("d", ""), 10) || 30;
    const end = endOfDay(new Date());
    const start = startOfDay(subDays(end, days));
    return { start, end };
  }

  async gatherContext(
    locationId: number,
    timeframe?: string,
    insight?: Insight,
    additionalContext?: any
  ) {
    try {
      // Get location
      const location = await Location.query().where("id", locationId).first();

      if (!location) {
        throw new Error(`Location not found: ${locationId}`);
      }

      // Get existing resources
      const existingResources = await getExistingResources(locationId);
      if (!existingResources) {
        throw new Error(`Failed to fetch resources for location ${locationId}`);
      }

      // Get POS metrics with timeframe
      const { start, end } = this.getTimeframeDates(timeframe);
      const aggregatedMetrics = await PosData.query()
        .where("location_id", locationId)
        .whereBetween("order_date", [start, end])
        .select([
          PosData.raw("customer_name"),
          PosData.raw("COUNT(*) as total_orders"),
          PosData.raw("SUM(net_sales) as total_sales"),
          PosData.raw("AVG(net_sales) as average_order_value"),
        ])
        .groupBy("customer_name");

      // Get previous period metrics for comparison
      const prevStart = startOfDay(
        subDays(start, end.getTime() - start.getTime())
      );
      const previousMetrics = await PosData.query()
        .where("location_id", locationId)
        .whereBetween("order_date", [prevStart, start])
        .select([
          PosData.raw("AVG(net_sales) as avg_order_value"),
          PosData.raw("COUNT(DISTINCT customer_name) as total_customers"),
          PosData.raw(
            "COUNT(*) / COUNT(DISTINCT DATE(order_date)) as daily_orders"
          ),
        ])
        .first();

      if (!aggregatedMetrics || aggregatedMetrics.length === 0) {
        console.warn(
          "No aggregated metrics found for the given period. Proceeding with limited context."
        );
      }

      // If we have an insight, get channel-specific configs
      let channelConfig: ChannelConfigs = {
        email: null,
        text: null,
      };

      if (insight) {
        if (!insight.delivery_channel) {
          throw new RequestError(
            "Insight is missing required delivery_channel",
            422
          );
        }

        const deliveryChannel = insight.delivery_channel;
        const [emailSubscription, smsSubscription] = await Promise.all([
          subscriptionForChannel("email", locationId),
          subscriptionForChannel("text", locationId),
        ]);

        const [emailProvider, smsProvider] = await Promise.all([
          emailSubscription ? getProviderByGroup("email", locationId) : null,
          smsSubscription ? getProviderByGroup("text", locationId) : null,
        ]);

        if (
          deliveryChannel === "email" &&
          (!emailSubscription || !emailProvider)
        ) {
          throw new RequestError(
            "Email delivery channel requested but no email configuration available",
            422
          );
        }

        if (deliveryChannel === "text" && (!smsSubscription || !smsProvider)) {
          throw new RequestError(
            "Text delivery channel requested but no SMS configuration available",
            422
          );
        }

        channelConfig = {
          email:
            emailSubscription && emailProvider
              ? {
                  subscription_id: emailSubscription.id,
                  provider_id: emailProvider.id,
                }
              : null,
          text:
            smsSubscription && smsProvider
              ? {
                  subscription_id: smsSubscription.id,
                  provider_id: smsProvider.id,
                }
              : null,
        };
      }

      return {
        location,
        existingResources,
        channelConfig,
        metrics: {
          timeframe: {
            start: start.toISOString(),
            end: end.toISOString(),
            days: Math.round(
              (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)
            ),
          },
          current: aggregatedMetrics || [],
          previous: previousMetrics || null,
        },
      };
    } catch (error) {
      console.error("Error in ContextAgent:", error);
      throw error;
    }
  }
}
