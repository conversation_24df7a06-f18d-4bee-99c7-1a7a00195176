/* eslint-disable indent */
import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";

/**
 * Evaluator Agent: Responsible for evaluating and improving generated insights
 */
export class EvaluatorAgent {
  async evaluateInsights(
    insights: any[],
    context: any,
    model: string
  ): Promise<any[]> {
    const llm = new ChatOpenAI({
      modelName: model || "gpt-4.1-mini",
      temperature: 0.3,
      openAIApiKey: process.env.OPENAI_API_KEY,
    });

    const prompt = `
      You are an expert evaluator of marketing insights and automation plans.
      Review these insights for quality, actionability, and business value.
      
      Evaluation criteria:
      1. Actionability - Can this be implemented with our automation tools?
      2. Business Impact - Is the potential impact clearly defined and significant?
      3. Data Support - Is the insight well-supported by the provided metrics?
      4. Clarity - Is the insight clear and specific enough to act on?

      For each insight that doesn't meet these criteria, provide specific improvements.
      Return the improved insights array with any necessary corrections.

      Context:
      ${JSON.stringify(context)}

      Insights to evaluate:
      ${JSON.stringify(insights)}

      Return ONLY the improved insights array in valid JSON format.
      DO NOT include any markdown formatting, backticks, or explanations.
      Just return the raw JSON array.
    `;

    const response = await llm.call([
      new SystemMessage(
        "You are a helpful AI assistant. Return only valid JSON without any formatting or backticks."
      ),
      new HumanMessage(prompt),
    ]);

    try {
      // Clean the response by removing markdown code blocks and other formatting
      const cleanedResponse =
        typeof response.content === "string"
          ? response.content
              .replace(/```json\s*|\s*```/g, "") // Remove code blocks
              .replace(/^`|`$/g, "") // Remove single backticks
              .trim()
          : JSON.stringify(response.content);

      // Try parsing the cleaned response
      try {
        return JSON.parse(cleanedResponse);
      } catch (parseError) {
        // If basic cleaning fails, try more aggressive cleaning
        const aggressiveCleaned = cleanedResponse
          .replace(/\/\/.*/g, "") // Remove comments
          .replace(/,(\s*[}\]])/g, "$1") // Fix trailing commas
          .replace(/(['"])?([a-zA-Z0-9_]+)(['"])?\s*:/g, '"$2": ') // Fix quotes
          .trim();

        try {
          return JSON.parse(aggressiveCleaned);
        } catch (finalError) {
          console.error("All JSON parsing attempts failed");
          console.error("Original content:", response.content);
          console.error("Basic cleaned content:", cleanedResponse);
          console.error("Aggressively cleaned content:", aggressiveCleaned);

          // If all parsing attempts fail, return the original insights
          console.warn("Falling back to original insights");
          return insights;
        }
      }
    } catch (error) {
      console.error("Error in evaluateInsights:", error);
      // Return original insights if evaluation fails
      return insights;
    }
  }
}
