import { Chat<PERSON><PERSON>A<PERSON> } from "@langchain/openai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";
import { ValidationAgent } from "./ValidationAgent";
import { ContentGenerationAgent } from "./ContentGenerationAgent";
import { VotingAgent } from "./VotingAgent";

/**
 * General Agent: A versatile agent that can handle various requests
 * that don't fit into the specialized categories of other agents.
 */
export class GeneralAgent {
  private validationAgent: ValidationAgent;
  private contentAgent: ContentGenerationAgent;
  private votingAgent: VotingAgent;
  public automationPrompts: any;

  constructor() {
    this.validationAgent = new ValidationAgent();
    this.contentAgent = new ContentGenerationAgent();
    this.votingAgent = new VotingAgent();
    this.automationPrompts = {};
    this.loadPrompts();
  }

  private loadPrompts() {
    // Simplified version for demonstration
    this.automationPrompts = {
      base: "You are an AI assistant specializing in marketing automation.",
      constraints: [
        "Keep all content professional and brand-appropriate.",
        "Consider user privacy and data protection in all designs.",
      ],
      examples: [],
    };
  }

  async handleRequest(
    request: string,
    context: any,
    model: string = "gpt-4.1-mini"
  ): Promise<any> {
    const llm = new ChatOpenAI({
      modelName: model,
      temperature: 0.5,
      openAIApiKey: process.env.OPENAI_API_KEY,
    });

    const prompt = `
      ${this.automationPrompts.base}
      
      Request: ${request}
      
      Context:
      ${JSON.stringify(context, null, 2)}
      
      Constraints:
      ${this.automationPrompts.constraints.join("\n")}
      
      Provide a helpful response addressing the request.
      Return your response in JSON format if appropriate.
    `;

    const response = await llm.call([
      new SystemMessage(
        "You are a helpful AI assistant specializing in marketing automation."
      ),
      new HumanMessage(prompt),
    ]);

    // Process the response based on content type
    const content =
      typeof response.content === "string"
        ? response.content
        : JSON.stringify(response.content);

    // Check if the response is JSON
    try {
      if (content.trim().startsWith("{") || content.trim().startsWith("[")) {
        return JSON.parse(content);
      }
    } catch (error) {
      // Not valid JSON, return as is
    }

    return content;
  }
}
