/* eslint-disable brace-style */
import { z } from "zod";
import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";
import {
  AutomationItem,
  ListRule,
  ListConfig,
  CampaignConfig,
  ValidationError,
} from "./types";
import Location from "../../locations/Location";

/**
 * Validation Agent: Responsible for validating and potentially fixing LLM outputs
 */
export class ValidationAgent {
  private cleanRawResponse(response: string): string {
    // Remove markdown code blocks
    let cleaned = response
      .replace(/```json\n/g, "")
      .replace(/```\n/g, "")
      .replace(/```/g, "");

    // Remove any leading/trailing whitespace
    cleaned = cleaned.trim();

    // Log the cleaning process
    console.log("Raw response:", response);
    console.log("Cleaned response:", cleaned);

    return cleaned;
  }

  private attemptJsonRepair(rawJson: string): string {
    try {
      // First try basic cleanup
      const cleaned = this.cleanRawResponse(rawJson);

      // Try parsing to validate
      JSON.parse(cleaned);
      return cleaned;
    } catch (error) {
      // If basic cleanup fails, try more aggressive repairs
      console.warn("Basic JSON cleanup failed, attempting repairs");

      const repaired = rawJson
        // Remove any markdown formatting
        .replace(/```[\s\S]*?```/g, "")
        // Remove comments
        .replace(/\/\/.*/g, "")
        // Fix trailing commas
        .replace(/,(\s*[}\]])/g, "$1")
        // Ensure proper quotes
        .replace(/(['"])?([a-zA-Z0-9_]+)(['"])?\s*:/g, '"$2": ')
        .trim();

      console.log("Repaired JSON:", repaired);
      return repaired;
    }
  }

  private validateListStep(list: ListConfig, stepType: string): void {
    if (!list.name || typeof list.name !== "string") {
      throw new ValidationError("List must have a valid name", stepType);
    }
    if (list.rule) {
      this.validateListRule(list.rule);
    }
  }

  /**
   * Validates campaign step campaign
   */
  private validateCampaignStep(
    campaign: any,
    stepType: string,
    items?: Record<string, AutomationItem>
  ): void {
    if (!campaign.name) {
      throw new ValidationError("Campaign must have a valid name", stepType);
    }

    if (!campaign.type) {
      throw new ValidationError(
        "Campaign must specify a type (blast or trigger)",
        stepType
      );
    }

    if (!["blast", "trigger"].includes(campaign.type)) {
      throw new ValidationError(
        `Invalid campaign type: ${campaign.type}. Must be 'blast' or 'trigger'`,
        stepType
      );
    }

    if (!campaign.channel) {
      throw new ValidationError("Campaign must specify a channel", stepType);
    }

    if (!["email", "text", "sms", "push", "webhook"].includes(campaign.channel)) {
      throw new ValidationError(
        `Invalid campaign channel: ${campaign.channel}. Must be 'email', 'text', 'sms', 'push', or 'webhook'`,
        stepType
      );
    }

    if (
      campaign.subscription_id === undefined ||
      campaign.provider_id === undefined
    ) {
      throw new ValidationError(
        "Campaign must specify subscription_id and provider_id",
        stepType
      );
    }

    // Validate send_at is either null or a valid ISO date string with offset
    if (campaign.send_at !== null && typeof campaign.send_at === "string") {
      // MySQL format requirement: YYYY-MM-DD HH:MM:SS or YYYY-MM-DDThh:mm:ss±hh:mm
      // This regex validates ISO 8601 format with timezone offset
      const validDateRegex =
        /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:.\d+)?(?:Z|[+-]\d{2}:\d{2})$/;

      if (!validDateRegex.test(campaign.send_at) && campaign.send_at !== "") {
        // Try to fix the date format if it's invalid
        try {
          const date = new Date(campaign.send_at);
          if (!isNaN(date.getTime())) {
            // Convert to proper format with timezone offset
            // Use local timezone offset to create the string
            const tzOffset = -date.getTimezoneOffset();
            const tzHours = Math.floor(Math.abs(tzOffset) / 60)
              .toString()
              .padStart(2, "0");
            const tzMinutes = (Math.abs(tzOffset) % 60)
              .toString()
              .padStart(2, "0");
            const tzSign = tzOffset >= 0 ? "+" : "-";

            // Format: YYYY-MM-DDThh:mm:ss±hh:mm
            // This ensures we have timezone offset in the proper format
            campaign.send_at =
              date.getFullYear() +
              "-" +
              String(date.getMonth() + 1).padStart(2, "0") +
              "-" +
              String(date.getDate()).padStart(2, "0") +
              "T" +
              String(date.getHours()).padStart(2, "0") +
              ":" +
              String(date.getMinutes()).padStart(2, "0") +
              ":" +
              String(date.getSeconds()).padStart(2, "0") +
              tzSign +
              tzHours +
              ":" +
              tzMinutes;

            console.log(`Fixed date format: ${campaign.send_at}`);
          } else {
            throw new ValidationError(
              "Invalid send_at format. Must be null, empty string, or ISO date string with timezone offset (YYYY-MM-DDThh:mm:ss±hh:mm)",
              stepType
            );
          }
        } catch (error) {
          throw new ValidationError(
            "Invalid send_at format. Must be null, empty string, or ISO date string with timezone offset (YYYY-MM-DDThh:mm:ss±hh:mm)",
            stepType
          );
        }
      }
    }

    // Validate that at least one list is specified
    const hasListRefs = !!(campaign.list_refs && campaign.list_refs.length > 0);
    const hasListIds = !!(campaign.list_ids && campaign.list_ids.length > 0);

    if (!hasListRefs && !hasListIds) {
      throw new ValidationError(
        "Campaign must specify lists using either list_refs or list_ids",
        stepType
      );
    }

    // If items are provided, validate that list_refs reference valid lists
    if (items && hasListRefs) {
      for (const listRef of campaign.list_refs || []) {
        const referencedItem = items[listRef];

        if (!referencedItem) {
          throw new ValidationError(
            `Referenced list "${listRef}" does not exist in items dictionary`,
            stepType
          );
        }

        if (referencedItem.type !== "list") {
          throw new ValidationError(
            `Referenced item "${listRef}" must be a list`,
            stepType
          );
        }
      }
    }

    // Validate exclusion lists if present
    if (
      items &&
      campaign.exclusion_list_refs &&
      campaign.exclusion_list_refs.length > 0
    ) {
      for (const listRef of campaign.exclusion_list_refs) {
        const referencedItem = items[listRef];

        if (!referencedItem) {
          throw new ValidationError(
            `Referenced exclusion list "${listRef}" does not exist in items dictionary`,
            stepType
          );
        }

        if (referencedItem.type !== "list") {
          throw new ValidationError(
            `Referenced exclusion item "${listRef}" must be a list`,
            stepType
          );
        }
      }
    }

    // Validate send_in_user_timezone is a boolean if present
    if (
      campaign.send_in_user_timezone !== undefined &&
      typeof campaign.send_in_user_timezone !== "boolean"
    ) {
      throw new ValidationError(
        "send_in_user_timezone must be a boolean",
        stepType
      );
    }

    // Validate tags are an array if present
    if (campaign.tags !== undefined && !Array.isArray(campaign.tags)) {
      throw new ValidationError("tags must be an array", stepType);
    }

    // Ensure state is set to "draft" or "scheduled" for blast campaigns
    if (
      campaign.type === "blast" &&
      (!campaign.state || campaign.state === "")
    ) {
      campaign.state = "scheduled";
    }
  }

  private validateTemplateStep(
    item: AutomationItem,
    itemKey: string,
    items: Record<string, AutomationItem>
  ): void {
    console.log("=== Validating Template Step ===");
    console.log("Template item:", JSON.stringify(item, null, 2));
    console.log("Available items:", JSON.stringify(items, null, 2));

    if (!item.config?.template) {
      throw new ValidationError("Missing template configuration", "template");
    }

    const template = item.config.template;
    const templateData = template.data;

    // Validate required template fields
    if (!template.type) {
      throw new ValidationError(
        "Template must specify a type (email or text)",
        "template"
      );
    }

    if (!["email", "text"].includes(template.type)) {
      throw new ValidationError(
        `Invalid template type: ${template.type}. Must be 'email' or 'text'`,
        "template"
      );
    }

    if (!template.name) {
      throw new ValidationError("Template must have a name", "template");
    }

    if (!template.locale) {
      throw new ValidationError("Template must specify a locale", "template");
    }

    // Validate campaign reference format and existence
    const hasCampaignRef = template.campaign_ref !== undefined;
    const hasCampaignId = template.campaign_id !== undefined;

    if (!hasCampaignRef && !hasCampaignId) {
      throw new ValidationError(
        "Template must have either campaign_ref or campaign_id",
        "template"
      );
    }

    if (hasCampaignRef && hasCampaignId) {
      throw new ValidationError(
        "Template must have either campaign_ref OR campaign_id, but not both",
        "template"
      );
    }

    if (hasCampaignRef) {
      console.log(`Checking campaign reference: ${template.campaign_ref}`);
      // Check if referenced campaign exists in the items dictionary
      const referencedItem = items[template.campaign_ref!];

      if (!referencedItem) {
        throw new ValidationError(
          `Referenced campaign "${template.campaign_ref}" does not exist in items dictionary`,
          "template"
        );
      }

      // Check if referenced item is a campaign
      if (referencedItem.type !== "campaign") {
        throw new ValidationError(
          `Referenced item "${template.campaign_ref}" must be a campaign`,
          "template"
        );
      }

      // Validate channel types match
      const campaignChannel = referencedItem.config?.campaign?.channel;
      console.log("Validating channel types:", {
        template: template.type,
        campaign: campaignChannel,
      });

      if (template.type !== campaignChannel) {
        throw new ValidationError(
          `Template type (${template.type}) must match campaign channel (${campaignChannel})`,
          "template"
        );
      }
    }

    // Validate required template data fields
    console.log("Validating template data fields...");
    if (!templateData) {
      throw new ValidationError("Template data is required", "template");
    }

    const requiredFields = [
      "editor",
      "name",
      "subject",
      "html",
      "text",
      "from",
    ] as const;

    for (const field of requiredFields) {
      console.log(`Checking required field: ${field}`);
      if (!templateData[field]) {
        throw new ValidationError(
          `Template data missing required field: ${field}`,
          "template"
        );
      }
    }

    // Validate and clean from field
    if (!templateData.from) {
      templateData.from = { name: "", address: "" };
    }

    // Set defaults for empty fields
    if (!templateData.from.name || templateData.from.name.trim() === "") {
      templateData.from.name = "Unknown Sender";
    }
    if (!templateData.from.address || templateData.from.address.trim() === "") {
      templateData.from.address = "<EMAIL>";
    }

    // email-specific validations
    if (template.type === "email") {
      if (!templateData.reply_to) {
        throw new ValidationError(
          "Email templates must have a reply_to field",
          "template"
        );
      }

      // Validate HTML content for email templates
      if (templateData.html && typeof templateData.html !== "string") {
        throw new ValidationError("HTML content must be a string", "template");
      }
    }

    console.log("Template validation completed successfully");
  }

  private validateJourneyStep(
    item: AutomationItem,
    itemKey: string,
    items: Record<string, AutomationItem>
  ): void {
    console.log("=== Validating Journey Step ===");

    if (!item.config?.journey) {
      throw new ValidationError("Missing journey configuration", "journey");
    }

    const journey = item.config.journey;

    // Validate basic journey properties
    if (!journey.name) {
      throw new ValidationError("Journey must have a name", "journey");
    }

    if (
      journey.published !== undefined &&
      typeof journey.published !== "boolean"
    ) {
      throw new ValidationError(
        "journey.published must be a boolean",
        "journey"
      );
    }

    if (journey.tags !== undefined && !Array.isArray(journey.tags)) {
      throw new ValidationError("journey.tags must be an array", "journey");
    }

    // Validate steps
    if (!journey.steps || typeof journey.steps !== "object") {
      throw new ValidationError("Journey must have steps object", "journey");
    }

    // Get all campaign keys for reference validation
    const campaignKeys = Object.entries(items)
      .filter(([_, item]) => item.type === "campaign")
      .map(([key]) => key);

    // Check for entrance steps
    let hasEntranceStep = false;

    for (const [stepKey, step] of Object.entries(journey.steps)) {
      // Validate step basics
      if (!step.type) {
        throw new ValidationError(
          `Step ${stepKey} missing required type`,
          "journey"
        );
      }

      const validStepTypes = [
        "entrance",
        "exit",
        "delay",
        "action",
        "gate",
        "experiment",
        "link",
        "update",
        "balancer",
        "event",
      ];

      if (!validStepTypes.includes(step.type)) {
        throw new ValidationError(
          `Invalid step type "${
            step.type
          }" for step ${stepKey}. Must be one of: ${validStepTypes.join(", ")}`,
          "journey"
        );
      }

      if (!step.name) {
        throw new ValidationError(
          `Step ${stepKey} must have a name`,
          "journey"
        );
      }

      if (step.x === undefined || typeof step.x !== "number") {
        throw new ValidationError(
          `Step ${stepKey} must have a valid x coordinate`,
          "journey"
        );
      }

      if (step.y === undefined || typeof step.y !== "number") {
        throw new ValidationError(
          `Step ${stepKey} must have a valid y coordinate`,
          "journey"
        );
      }

      if (!step.data || typeof step.data !== "object") {
        throw new ValidationError(
          `Step ${stepKey} must have a data object`,
          "journey"
        );
      }

      if (!Array.isArray(step.children)) {
        throw new ValidationError(
          `Step ${stepKey} must have a children array`,
          "journey"
        );
      }

      // Type-specific validations
      if (step.type === "entrance") {
        hasEntranceStep = true;

        // Entrance steps must be at coordinates (0,0)
        if (step.x !== 0 || step.y !== 0) {
          throw new ValidationError(
            `Entrance step ${stepKey} must be at coordinates (0,0), found (${step.x},${step.y})`,
            "journey"
          );
        }

        // Validate entrance step data
        if (!step.data.trigger) {
          throw new ValidationError(
            `Entrance step ${stepKey} must specify a trigger`,
            "journey"
          );
        }

        if (!["schedule", "event", "api"].includes(step.data.trigger)) {
          throw new ValidationError(
            `Invalid trigger "${step.data.trigger}" for entrance step ${stepKey}`,
            "journey"
          );
        }

        if (step.data.trigger === "schedule" && !step.data.schedule) {
          throw new ValidationError(
            `Entrance step ${stepKey} with schedule trigger must specify a schedule`,
            "journey"
          );
        }

        if (step.data.trigger === "event" && !step.data.event_name) {
          throw new ValidationError(
            `Entrance step ${stepKey} with event trigger must specify an event_name`,
            "journey"
          );
        }
      } else if (step.type === "action") {
        // Validate action step campaign references
        const hasCampaignId = step.data.campaign_id !== undefined;
        const hasCampaignRef = step.data.campaign_ref !== undefined;

        if (!hasCampaignId && !hasCampaignRef) {
          throw new ValidationError(
            `Action step ${stepKey} must specify either campaign_id or campaign_ref`,
            "journey"
          );
        }

        if (hasCampaignId && hasCampaignRef) {
          throw new ValidationError(
            `Action step ${stepKey} must specify either campaign_id OR campaign_ref, not both`,
            "journey"
          );
        }

        // If using campaign_ref, validate it references a valid campaign
        if (hasCampaignRef && !campaignKeys.includes(step.data.campaign_ref!)) {
          throw new ValidationError(
            `Action step ${stepKey} references non-existent campaign "${step.data.campaign_ref}"`,
            "journey"
          );
        }
      } else if (step.type === "delay") {
        // Validate delay step has proper timing
        const hasDuration = step.data.duration !== undefined;
        const hasUntilTime = step.data.until_time !== undefined;
        const hasUntilDate = step.data.until_date !== undefined;

        if (!hasDuration && !hasUntilTime && !hasUntilDate) {
          throw new ValidationError(
            `Delay step ${stepKey} must specify either duration, until_time, or until_date`,
            "journey"
          );
        }

        if (hasDuration && !step.data.unit) {
          throw new ValidationError(
            `Delay step ${stepKey} with duration must specify a time unit`,
            "journey"
          );
        }

        if (
          step.data.unit &&
          !["minutes", "hours", "days"].includes(step.data.unit)
        ) {
          throw new ValidationError(
            `Invalid time unit "${step.data.unit}" for delay step ${stepKey}`,
            "journey"
          );
        }
      } else if (step.type === "exit") {
        // Validate exit step points to an entrance step
        if (!step.data.entrance_id) {
          throw new ValidationError(
            `Exit step ${stepKey} must specify an entrance_id`,
            "journey"
          );
        }

        // Check that the referenced entrance exists
        if (!journey.steps[step.data.entrance_id]) {
          throw new ValidationError(
            `Exit step ${stepKey} references non-existent entrance "${step.data.entrance_id}"`,
            "journey"
          );
        }

        if (journey.steps[step.data.entrance_id].type !== "entrance") {
          throw new ValidationError(
            `Exit step ${stepKey} must reference an entrance step, but "${step.data.entrance_id}" is not an entrance`,
            "journey"
          );
        }
      } else if (step.type === "experiment") {
        // Validate experiment variants
        if (!Array.isArray(step.children) || step.children.length < 2) {
          throw new ValidationError(
            `Experiment step ${stepKey} must have at least 2 children (variants)`,
            "journey"
          );
        }

        // Check for proper ratio data
        let totalRatio = 0;
        for (const child of step.children) {
          if (
            !child.data ||
            child.data.ratio === undefined ||
            typeof child.data.ratio !== "number"
          ) {
            throw new ValidationError(
              `Experiment child in step ${stepKey} must specify a numeric ratio`,
              "journey"
            );
          }
          totalRatio += child.data.ratio;
        }

        // Optionally enforce ratio sum (commented out since it might be too strict)
        // if (Math.abs(totalRatio - 100) > 0.001) {
        //   throw new ValidationError(
        //     `Experiment ratios in step ${stepKey} must add up to 100, found ${totalRatio}`,
        //     "journey"
        //   );
        // }
      }

      // Validate that all children reference valid steps
      for (const child of step.children) {
        if (!child.external_id) {
          throw new ValidationError(
            `Child in step ${stepKey} missing required external_id`,
            "journey"
          );
        }

        if (!journey.steps[child.external_id]) {
          throw new ValidationError(
            `Step ${stepKey} references non-existent step "${child.external_id}" as child`,
            "journey"
          );
        }
      }
    }

    if (!hasEntranceStep) {
      throw new ValidationError(
        "Journey must have at least one entrance step",
        "journey"
      );
    }

    console.log("Journey validation completed successfully");
  }

  public validateStep(
    item: AutomationItem,
    itemKey: string,
    items: Record<string, AutomationItem>
  ): void {
    console.log(`=== Validating Step: ${itemKey} ===`);
    console.log("Item:", JSON.stringify(item, null, 2));

    try {
      switch (item.type) {
        case "list":
          console.log("Validating list configuration...");
          if (!item.config.list) {
            throw new ValidationError("Missing list configuration", "list");
          }
          this.validateListStep(item.config.list, "list");
          console.log("List validation successful");
          break;

        case "campaign":
          console.log("Validating campaign configuration...");
          if (!item.config.campaign) {
            throw new ValidationError(
              "Missing campaign configuration",
              "campaign"
            );
          }
          this.validateCampaignStep(item.config.campaign, "campaign", items);
          console.log("Campaign validation successful");
          break;

        case "template":
          console.log("Validating template configuration...");
          if (!item.config.template) {
            throw new ValidationError(
              "Missing template configuration",
              "template"
            );
          }
          this.validateTemplateStep(item, itemKey, items);
          console.log("Template validation successful");
          break;

        case "journey":
          console.log("Validating journey configuration...");
          this.validateJourneyStep(item, itemKey, items);
          console.log("Journey validation successful");
          break;

        default:
          throw new ValidationError(`Unknown step type: ${item.type}`);
      }
    } catch (error) {
      console.error(`Validation failed for item ${itemKey}:`, error);
      if (error instanceof ValidationError) {
        throw error;
      }
      throw new ValidationError(
        `Validation failed for item ${itemKey}`,
        item.type,
        error
      );
    }
  }

  private validateListRule(rule: ListRule): void {
    if (!rule.type || !rule.group || !rule.path) {
      throw new Error("List rule missing required fields (type, group, path)");
    }

    if (
      !["wrapper", "string", "number", "boolean", "date", "array"].includes(
        rule.type
      )
    ) {
      throw new Error(`Invalid rule type: ${rule.type}`);
    }

    if (!["pos", "user", "event", "parent"].includes(rule.group)) {
      throw new Error(`Invalid rule group: ${rule.group}`);
    }

    // Recursively validate child rules
    rule.children?.forEach((child: ListRule) => this.validateListRule(child));
  }

  private async requestJsonFix(
    rawJson: string,
    schema: z.ZodType<any>
  ): Promise<string> {
    const model = new ChatOpenAI({
      modelName: "gpt-4.1-mini",
      temperature: 0.3,
      openAIApiKey: process.env.OPENAI_API_KEY,
    });

    // Detect if this is likely a campaign plan from the content
    const isCampaignPlan =
      rawJson.includes("campaign_plan") ||
      rawJson.includes("target_list_id") ||
      rawJson.includes("template_id");

    let prompt = "";

    if (isCampaignPlan) {
      prompt = `
        You are a JSON repair expert with deep knowledge of marketing automation systems. Fix the following JSON to create a valid campaign plan structure.
        
        The JSON should follow this campaign_plan format:
        {
          "campaign_plan": {
            "name": "Campaign Name",
            "description": "Campaign description...",
            "channel": "email", // or "text"
            "subscription_id": 1, 
            "provider_id": 1,
            "target_list_id": 1, // if using existing list
            "template_id": 1, // if using existing template
            "tags": ["tag1", "tag2"],
            "subject": "Email Subject Line",
            "html": "<p>HTML content for email template</p>",
            "text": "Plain text version of the content",
            "imagePrompt": "Detailed description for a beautiful marketing image that matches the email content", // Required for emails
            "send_at": "2023-01-01T00:00:00Z", // ISO date or null
            "send_in_user_timezone": false
          }
        }
        
        For email campaigns, it's essential to include a detailed imagePrompt that will be used to generate a beautiful header image for the email template.
        
        Here is the invalid JSON to fix:
        ${rawJson}
        
        Return ONLY the fixed JSON without explanations, comments, or markdown formatting.
      `;
    } else {
      prompt = `
        You are a JSON repair expert with deep knowledge of marketing automation systems. Fix the following JSON to EXACTLY match these requirements:

        1. CRITICAL: The root structure MUST be:
        {
          "name": "string", // Required plan name
          "description": "string", // Required plan description
          "items": {
            "itemKey1": { automation item object },
            "itemKey2": { automation item object }
          }
        }

        2. Each automation item MUST follow this structure based on its type:

        For "list" items:
        {
          "type": "list",
          "config": {
            "list": {
              "name": "string", // Required
              "type": "dynamic|static", // Required
              "description": "string", // Required
              "rule": { 
                "uuid": "string",
                "type": "string|number|boolean|date|array", 
                "group": "pos|user|event|parent",
                "path": "string",
                "operator": "string", // e.g., "=", ">", "contains", etc.
                "value": any,
                "children": [] // Array of nested rules
              },
              "is_visible": boolean,
              "tags": string[],
              "user_ids": number[]
            }
          }
        }

        For "campaign" items:
        {
          "type": "campaign",
          "config": {
            "campaign": {
              "type": "blast|trigger", // Required
              "name": "string", // Required
              "channel": "email|text", // Required
              "subscription_id": number, // Required
              "provider_id": number, // Required
              // MUST specify lists using ONE of these approaches:
              "list_refs": ["list1", "list2"], // References to list items
              "list_ids": [123, 456],          // Legacy: Direct list IDs
              // Optional exclusion lists:
              "exclusion_list_refs": ["excludeList1"], // Reference-based
              "exclusion_list_ids": [789],            // Legacy: Direct IDs
              "send_in_user_timezone": boolean,
              "send_at": string, // ISO date string or null
              "tags": string[]
            }
          }
        }

        For "template" items:
        {
          "type": "template",
          "config": {
            "template": {
              "type": "email|text", // Required and MUST match the referenced campaign channel
              "name": "string", // Required
              "locale": "string", // Required
              // CRITICAL: MUST have exactly ONE of these, not both:
              "campaign_ref": "campaignKey", // Key of a campaign in the items dictionary
              "campaign_id": number,         // Existing campaign ID
              "data": {
                "editor": "code", // Required
                "name": "string", // Required
                "subject": "string", // Required
                "html": "string", // Required - HTML content
                "text": "string", // Required - Plain text content
                "from": { // Required
                  "name": "string", // Required
                  "address": "string" // Required
                },
                "reply_to": "string", // Required for email templates
                "preheader": "string" // Optional
              }
            }
          }
        }
        
        For "journey" items:
        {
          "type": "journey",
          "config": {
            "journey": {
              "name": "string", // Required
              "description": "string", // Optional
              "published": boolean, // Optional, default: false
              "tags": string[], // Optional
              "steps": { // Required
                "stepId1": {
                  "type": "entrance|exit|delay|action|gate|experiment|link|update|balancer|event", // Required
                  "name": "string", // Required
                  "x": number, // Required - CRITICAL: entrance steps MUST be at x:0, y:0
                  "y": number, // Required
                  "data": { // Required, content varies by step type
                    // Entrance steps require trigger information
                    "trigger": "schedule|event|api", // For entrance steps
                    // Action steps need campaign reference
                    "campaign_ref": "string", // For action steps, refers to plan item
                    "campaign_id": number     // For action steps, legacy external ID
                  },
                  "children": [ // Required
                    { "external_id": "stepId2" }
                  ]
                }
                // other steps...
              }
            }
          }
        }

        3. CRITICAL RULES TO OBEY:
        - The TOP LEVEL must have name, description, and items properties
        - Config fields MUST be nested under "list", "campaign", "template", or "journey" key matching the step type
        - Template items MUST use either campaign_ref OR campaign_id, not both
        - Campaign items MUST specify lists using either list_refs or list_ids
        - All references (campaign_ref, list_ref, list_refs) MUST point to keys that exist in the items dictionary
        - Journey entrance steps MUST be at x:0, y:0
        - If fixing a journey, preserve all the step IDs and connections between steps
        - Never invent new item keys that don't exist in the original JSON
        - Make minimal changes to fix the structure without altering the core content/intent

        Here is the invalid JSON to fix:
        ${rawJson}

        Return ONLY the fixed JSON without explanations, comments, or markdown formatting.
      `;
    }

    console.log("Sending repair prompt to LLM...");
    console.log(
      "Using specialized prompt for " +
        (isCampaignPlan ? "campaign plan" : "automation plan")
    );

    const response = await model.call([
      new SystemMessage(
        "You are a JSON repair expert. You will fix the provided JSON without explanations. Return ONLY the valid JSON."
      ),
      new HumanMessage(prompt),
    ]);

    const content =
      typeof response.content === "string"
        ? response.content
        : JSON.stringify(response.content);

    // Clean up the response to ensure it's just JSON
    return content
      .replace(/```json\s*|\s*```/g, "") // Remove code blocks
      .replace(/^`|`$/g, "") // Remove single backticks
      .trim(); // Remove extra whitespace
  }

  private postProcessPlan(plan: any): any {
    if (!plan || !plan.items || typeof plan.items !== "object") return plan;

    // Process each item in the dictionary
    const processedItems: Record<string, AutomationItem> = {};
    const itemEntries = Object.entries<AutomationItem>(plan.items);

    // First pass: collect all campaign items for reference
    const campaignItems = new Map<string, AutomationItem>();
    itemEntries.forEach(([key, item]) => {
      if (item.type === "campaign") {
        campaignItems.set(key, item);
      }
    });

    // Second pass: process and validate each item
    itemEntries.forEach(([key, item]: [string, AutomationItem]) => {
      if (!item || typeof item !== "object") {
        processedItems[key] = item;
        return;
      }

      // Ensure config exists
      if (!item.config) item.config = {};

      // Fix missing nested structure
      if (item.type && !item.config[item.type]) {
        const validFields: Record<string, Set<string>> = {
          list: new Set([
            "name",
            "description",
            "type",
            "rule",
            "is_visible",
            "tags",
            "user_ids",
          ]),
          campaign: new Set([
            "name",
            "type",
            "channel",
            "subscription_id",
            "provider_id",
            "list_ids",
            "exclusion_list_ids",
            "send_in_user_timezone",
            "send_at",
            "tags",
          ]),
          template: new Set([
            "type",
            "name",
            "locale",
            "campaign_id",
            "campaign_ref",
            "data",
          ]),
          journey: new Set([
            "name",
            "description",
            "published",
            "tags",
            "steps",
          ]),
        };

        // Move valid fields to nested object
        const nestedConfig: Record<string, any> = {};
        for (const [fieldKey, value] of Object.entries(item.config)) {
          if (validFields[item.type]?.has(fieldKey)) {
            nestedConfig[fieldKey] = value;
          }
        }

        item.config = {
          [item.type]: nestedConfig,
        };
      }

      // Special handling for template items
      if (item.type === "template" && item.config.template) {
        const template = item.config.template;

        // Fix campaign references
        if (template.campaign_ref) {
          // If it's a number, convert to campaign_id
          if (!isNaN(Number(template.campaign_ref))) {
            template.campaign_id = Number(template.campaign_ref);
            delete template.campaign_ref;
          } else {
            // Verify the referenced campaign exists in our items
            const referencedCampaign = campaignItems.get(template.campaign_ref);
            if (!referencedCampaign) {
              // If we can't find it, remove the invalid reference
              delete template.campaign_ref;
            }
          }
        }

        // Ensure we don't have both campaign_ref and campaign_id
        if (template.campaign_ref && template.campaign_id) {
          // Prefer campaign_ref if it references a valid campaign
          if (campaignItems.has(template.campaign_ref)) {
            delete template.campaign_id;
          } else {
            delete template.campaign_ref;
          }
        }

        // Ensure template data structure
        if (template.data) {
          const defaultData = {
            editor: "code",
            name: template.name || "",
            subject: "",
            html: "",
            text: "",
            from: {
              name: "",
              address: "",
            },
            reply_to: "",
            imagePrompt:
              "Beautiful marketing image with professional, modern design",
            imageUrl: null,
          };
          template.data = { ...defaultData, ...template.data };
        }
      }

      // Special handling for journey items
      if (item.type === "journey" && item.config.journey) {
        const journey = item.config.journey;

        // Ensure journey has required fields
        if (!journey.name) journey.name = "New Journey";
        if (!journey.description) journey.description = "";
        if (journey.published === undefined) journey.published = false;
        if (!journey.tags) journey.tags = [];

        // Ensure steps object exists
        if (!journey.steps || typeof journey.steps !== "object") {
          // Generate basic steps structure if missing
          journey.steps = {};

          // Create at least an entrance step if none exists
          const entranceId = `entrance_${Math.random()
            .toString(36)
            .substring(2, 8)}`;
          journey.steps[entranceId as keyof typeof journey.steps] = {
            type: "entrance",
            name: "Entry Point",
            x: 0,
            y: 0,
            data: {
              trigger: "event",
              event_name: "user_event",
              multiple_entries: true,
              simultaneous_entries: false,
            },
            children: [],
          };
        }

        // Process action steps to fix campaign references
        if (journey.steps && Object.keys(journey.steps).length > 0) {
          for (const stepKey in journey.steps) {
            const step = journey.steps[stepKey];
            if (step.type === "action" && step.data) {
              // Convert string campaign_id to campaign_ref (for backward compatibility)
              if (typeof step.data.campaign_id === "string") {
                // Only if it's not a numeric string
                if (isNaN(Number(step.data.campaign_id))) {
                  step.data.campaign_ref = step.data.campaign_id;
                  delete step.data.campaign_id;
                } else {
                  // If it's a numeric string, convert to number
                  step.data.campaign_id = Number(step.data.campaign_id);
                }
              }

              // Ensure we don't have both campaign_ref and campaign_id
              if (step.data.campaign_ref && step.data.campaign_id) {
                // Prefer campaign_ref if it references a valid campaign
                if (campaignItems.has(step.data.campaign_ref)) {
                  delete step.data.campaign_id;
                } else {
                  delete step.data.campaign_ref;
                }
              }
            }
          }
        }
      }

      processedItems[key] = item;
    });

    plan.items = processedItems;
    return plan;
  }

  private fixCommonStructureIssues(parsed: any): any {
    if (!parsed || typeof parsed !== "object") return parsed;

    console.log("Running structure normalization...");

    // Always start with a properly structured object
    let normalized: any = {
      name: "Generated Marketing Plan",
      description: "AI-generated marketing automation plan",
      items: {},
    };

    // CASE 1: Input already has the right structure
    if (
      parsed.name &&
      parsed.description &&
      parsed.items &&
      typeof parsed.items === "object"
    ) {
      normalized = { ...parsed };
    }
    // CASE 2: Input is a campaign_plan structure (common for simple campaign types)
    else if (parsed.campaign_plan && typeof parsed.campaign_plan === "object") {
      console.log(
        "Detected campaign_plan structure, converting to proper format"
      );

      // Use campaign details in the plan name/description if available
      if (parsed.campaign_plan.name) {
        normalized.name = parsed.campaign_plan.name + " Plan";
      }
      if (parsed.campaign_plan.description) {
        normalized.description = parsed.campaign_plan.description;
      }

      // Create the list, campaign, and template items
      const listId =
        parsed.campaign_plan.list_id || parsed.campaign_plan.target_list_id;
      const templateId = parsed.campaign_plan.template_id;

      // Generate random IDs for the components
      const listKey = `list_${Math.random().toString(36).substring(2, 7)}`;
      const campaignKey = `campaign_${Math.random()
        .toString(36)
        .substring(2, 7)}`;
      const templateKey = `template_${Math.random()
        .toString(36)
        .substring(2, 7)}`;

      // Create a list item if a list ID was provided
      if (listId) {
        normalized.items[listKey] = {
          type: "list",
          config: {
            list: {
              name: "Target List",
              type: "dynamic",
              description: "Target audience for the campaign",
              is_visible: true,
              tags: parsed.campaign_plan.tags || [],
              user_ids: [],
              rule: {
                uuid:
                  "auto-generated-" +
                  Math.random().toString(36).substring(2, 15),
                type: "string",
                group: "user",
                path: "id",
                operator: "is set",
                value: "",
                children: [],
              },
            },
          },
        };
      }

      // Create a campaign item using the campaign_plan details
      normalized.items[campaignKey] = {
        type: "campaign",
        config: {
          campaign: {
            name: parsed.campaign_plan.name || "Campaign",
            type: "blast",
            channel:
              parsed.campaign_plan.channel ||
              parsed.campaign_plan.target_channel ||
              "email",
            subscription_id: parsed.campaign_plan.subscription_id || 1,
            provider_id: parsed.campaign_plan.provider_id || 1,
            list_refs: listId ? [listKey] : [],
            list_ids:
              listId && !normalized.items[listKey] ? [listId] : undefined,
            send_in_user_timezone:
              parsed.campaign_plan.send_in_user_timezone || false,
            send_at: this.determineScheduledTime(parsed.campaign_plan),
            tags: parsed.campaign_plan.tags || [],
          },
        },
      };

      // Create a template item if a template ID was provided
      if (templateId) {
        normalized.items[templateKey] = {
          type: normalized.items[campaignKey].config.campaign.channel,
          config: {
            template: {
              type: normalized.items[campaignKey].config.campaign.channel,
              name: `${parsed.campaign_plan.name || "Campaign"} Template`,
              locale: "en",
              campaign_ref: campaignKey,
              data: {
                editor: "code",
                name: `${parsed.campaign_plan.name || "Campaign"} Template`,
                subject:
                  parsed.campaign_plan.subject ||
                  `${parsed.campaign_plan.name || "Important"} - Please Read`,
                html:
                  parsed.campaign_plan.html ||
                  "<html><body><p>Campaign Content</p></body></html>",
                text: parsed.campaign_plan.text || "Campaign Content",
                from: {
                  name: "Sender",
                  address: "<EMAIL>",
                },
                reply_to: "<EMAIL>",
                imagePrompt:
                  parsed.campaign_plan.imagePrompt ||
                  `Beautiful marketing image for ${
                    parsed.campaign_plan.name || "campaign"
                  } with professional, modern design`,
                imageUrl: parsed.campaign_plan.imageUrl,
              },
            },
          },
        };

        // Insert image into template HTML if needed
        const templateItem = normalized.items[templateKey];
        if (templateItem.config.template.type === "email") {
          this.insertImageIntoTemplate(templateItem.config.template);
        }
      }
    }
    // CASE 3: Structure like { journey: {...}, campaign: {...} } with multiple items
    else if (!parsed.items) {
      // Extract existing name/description if present
      if (parsed.name) normalized.name = parsed.name;
      if (parsed.description) normalized.description = parsed.description;

      // Process potential top-level objects that should be items
      for (const [key, value] of Object.entries(parsed)) {
        if (!value || typeof value !== "object") continue;

        // Check if it's directly a valid automation item with 'type' property
        if (
          (value as any).type &&
          typeof (value as any).type === "string" &&
          ["journey", "template", "campaign", "list"].includes(
            (value as any).type
          )
        ) {
          normalized.items[key] = value;

          // Try to extract better plan name/description if available
          if (
            (value as any).config?.[(value as any).type]?.name &&
            normalized.name === "Generated Marketing Plan"
          ) {
            normalized.name = `${
              (value as any).config[(value as any).type].name
            } Plan`;

            if ((value as any).config?.[(value as any).type]?.description) {
              normalized.description = (value as any).config[
                (value as any).type
              ].description;
            }
          }
          continue;
        }

        // Check for journey_plan or automation_plan structure
        if (
          key === "journey_plan" ||
          key === "automation_plan" ||
          key === "plan" ||
          key === "campaign"
        ) {
          if ((value as any).name) normalized.name = (value as any).name;
          if ((value as any).description) {
            normalized.description = (value as any).description;
          }

          // Special case for journey structure with steps array
          if (Array.isArray((value as any).steps)) {
            const journeySteps: any = {};

            // Convert steps array to step objects
            (value as any).steps.forEach((step: any, index: number) => {
              const stepId = `step${index + 1}`;

              // Determine step type
              let stepType = "action"; // Default
              if (index === 0) stepType = "entrance";

              // Create a valid step object
              journeySteps[stepId] = {
                type: stepType,
                name: step.description || `Step ${index + 1}`,
                x: index, // Position steps sequentially
                y: 0,
                data: {},
                children:
                  index < (value as any).steps.length - 1
                    ? [{ external_id: `step${index + 2}` }]
                    : [],
              };

              // Copy useful data properties
              if (step.template_id) {
                journeySteps[stepId].data.template_id = String(
                  step.template_id
                );
              }
              if (step.delay || step.wait_time) {
                journeySteps[stepId].data.delay = step.delay || step.wait_time;
              }
              if (step.action) {
                journeySteps[stepId].data.action_type = step.action;
              }
            });

            // Create journey item
            normalized.items.journey = {
              type: "journey",
              config: {
                journey: {
                  name: (value as any).name || normalized.name,
                  description:
                    (value as any).description || normalized.description,
                  published: (value as any).published || false,
                  tags: (value as any).tags || [],
                  steps: journeySteps,
                },
              },
            };
          }

          // Handle channel configuration if present
          if ((value as any).channel_configuration) {
            // Future enhancement: Could create appropriate subscription items from channel config
          }

          continue;
        }
      }
    }

    // Ensure all template_id values across the structure are strings, not numbers
    this.convertNumericIds(normalized);

    // Apply additional post-processing
    return this.postProcessPlan(normalized);
  }

  // Helper method to recursively convert numeric IDs to strings
  private convertNumericIds(obj: any): void {
    if (!obj || typeof obj !== "object") return;

    // Handle arrays
    if (Array.isArray(obj)) {
      obj.forEach((item) => this.convertNumericIds(item));
      return;
    }

    // Process each property
    for (const [key, value] of Object.entries(obj)) {
      // Convert template_id, campaign_id, list_id numeric values to strings
      if (
        (key === "template_id" || key === "campaign_id" || key === "list_id") &&
        typeof value === "number"
      ) {
        obj[key] = String(value);
      }
      // Also check nested objects
      else if (value && typeof value === "object") {
        this.convertNumericIds(value);
      }
    }
  }

  async validateAndRepair<T>(
    rawJson: string,
    schema: z.ZodType<T>,
    context: any
  ): Promise<T> {
    console.log("=== Starting Validation and Repair ===");
    console.log("Raw JSON input:", "[Input omitted for brevity]");

    try {
      // First attempt: Clean and parse
      const cleaned = this.cleanRawResponse(rawJson);
      console.log("Cleaned JSON:", cleaned);

      let parsed;
      try {
        parsed = JSON.parse(cleaned);
        console.log("Initially parsed JSON:", parsed);
      } catch (error) {
        console.warn("Initial JSON parse failed:", error);
        // Attempt more aggressive JSON repair immediately
        const repairedJson = this.attemptJsonRepair(rawJson);
        parsed = JSON.parse(repairedJson);
        console.log("After repair parse:", parsed);
      }

      // Fix common structure issues
      const fixedStructure = this.fixCommonStructureIssues(parsed);
      console.log("After fixing structure:", fixedStructure);

      // Apply comprehensive validation to each item
      if (fixedStructure.items) {
        console.log("Validating automation plan items...");

        // First validate each item individually
        Object.entries(fixedStructure.items).forEach(([itemKey, item]) => {
          console.log(
            `Validating item "${itemKey}":`,
            "[Item details omitted for brevity]"
          );
          this.validateStep(
            item as AutomationItem,
            itemKey,
            fixedStructure.items
          );
          console.log(`Item "${itemKey}" validated successfully`);
        });

        // Then validate cross-references between items
        this.validateCrossReferences(fixedStructure);
      }

      // Validate with Zod schema
      console.log("Validating against Zod schema...");
      try {
        const validated = await schema.parse(fixedStructure);
        console.log("Schema validation successful");
        return validated;
      } catch (schemaError) {
        // If Zod validation fails, log the exact issues
        console.error("Schema validation error:", schemaError);

        // Log more detailed error information
        if (schemaError && (schemaError as any).errors) {
          console.error("Detailed validation errors:");
          (schemaError as any).errors.forEach((err: any, index: number) => {
            console.error(`  Error ${index + 1}:`, {
              path: err.path?.join('.') || 'root',
              message: err.message,
              code: err.code,
              received: err.received,
              expected: err.expected,
            });
          });
        }

        console.log("Attempting to fix schema violations...");

        // Try to automatically fix common schema issues
        const fixedForSchema = this.fixSchemaViolations(
          fixedStructure,
          schemaError
        );

        console.log("Attempting validation after fixes...");
        const validated = await schema.parse(fixedForSchema);
        console.log("Schema validation successful after fixes");
        return validated;
      }
    } catch (error) {
      const err = error as Error;
      console.warn("First parse attempt failed:", err.message);
      console.warn("Attempting repairs on:", rawJson);

      try {
        // Second attempt: Basic JSON repair
        console.log("Attempting basic JSON repair...");
        const repairedJson = this.attemptJsonRepair(rawJson);
        console.log("After basic repair:", repairedJson);

        // Try to parse again
        let parsed;
        try {
          parsed = JSON.parse(repairedJson);
        } catch (parseError) {
          console.warn("Parse failed after basic repair:", parseError);
          throw new Error(
            "JSON repair failed: Unable to parse after basic repairs"
          );
        }

        // Apply fixes
        const fixed = this.fixCommonStructureIssues(parsed);
        const validated = await schema.parse(fixed);
        console.log("Basic repair successful");
        return validated;
      } catch (error) {
        const err = error as Error;
        console.warn("Basic repair failed:", err.message);
        console.warn("Attempting LLM fix...");

        try {
          // Third attempt: LLM-based fix
          console.log("Starting LLM-based repair...");
          const fixedJson = await this.requestJsonFix(rawJson, schema);
          console.log("After LLM fix:", fixedJson);
          const parsed = await schema.parse(JSON.parse(fixedJson));
          console.log("LLM repair successful");
          return parsed;
        } catch (error) {
          const err = error as Error;
          console.error("All repair attempts failed:", err.message);
          console.error("Original JSON:", rawJson);
          throw new ValidationError(
            "Failed to validate and repair JSON: " + err.message
          );
        }
      }
    }
  }

  /**
   * Validates cross-references between different items in the automation plan
   */
  private validateCrossReferences(plan: any): void {
    if (!plan || !plan.items || typeof plan.items !== "object") {
      return;
    }

    console.log("Validating cross-references...");

    // Get all item keys by type for reference validation
    const listKeys = new Set(
      Object.entries(plan.items)
        .filter(([_, item]: [string, any]) => item?.type === "list")
        .map(([key]) => key)
    );

    const campaignKeys = new Set(
      Object.entries(plan.items)
        .filter(([_, item]: [string, any]) => item?.type === "campaign")
        .map(([key]) => key)
    );

    // Determine if this is a simple campaign plan or complex automation
    const hasJourney = Object.values(plan.items).some(
      (item: any) => item?.type === "journey"
    );

    // Log plan structure type
    if (hasJourney) {
      console.log("Validating a complex automation plan with journey");
    } else {
      console.log("Validating a simple campaign plan");
    }

    // 1. Validate template campaign references
    for (const [key, item] of Object.entries<any>(plan.items)) {
      if (item?.type === "template" && item.config?.template?.campaign_ref) {
        const campaignRef = item.config.template.campaign_ref;

        if (!campaignKeys.has(campaignRef)) {
          throw new ValidationError(
            `Template "${key}" references non-existent campaign "${campaignRef}"`,
            "template"
          );
        }

        // Check that channel types match
        const templateType = item.config.template.type;
        const campaignItem = plan.items[campaignRef];

        if (!campaignItem?.config?.campaign?.channel) {
          throw new ValidationError(
            `Referenced campaign "${campaignRef}" has invalid or missing channel configuration`,
            "template"
          );
        }

        const campaignChannel = campaignItem.config.campaign.channel;

        if (templateType !== campaignChannel) {
          throw new ValidationError(
            `Template "${key}" type (${templateType}) doesn't match referenced campaign "${campaignRef}" channel (${campaignChannel})`,
            "template"
          );
        }
      }
    }

    // 2. Validate campaign list references
    for (const [key, item] of Object.entries<any>(plan.items)) {
      if (item?.type === "campaign" && item.config?.campaign?.list_refs) {
        for (const listRef of item.config.campaign.list_refs) {
          if (!listKeys.has(listRef)) {
            throw new ValidationError(
              `Campaign "${key}" references non-existent list "${listRef}"`,
              "campaign"
            );
          }
        }
      }

      // Also check exclusion lists
      if (
        item?.type === "campaign" &&
        item.config?.campaign?.exclusion_list_refs
      ) {
        for (const listRef of item.config.campaign.exclusion_list_refs) {
          if (!listKeys.has(listRef)) {
            throw new ValidationError(
              `Campaign "${key}" references non-existent exclusion list "${listRef}"`,
              "campaign"
            );
          }
        }
      }
    }

    // 3. Validate journey action step campaign references (only for automation plans)
    if (hasJourney) {
      for (const [key, item] of Object.entries<any>(plan.items)) {
        if (item?.type === "journey" && item.config?.journey?.steps) {
          const steps = item.config.journey.steps;

          for (const [stepKey, step] of Object.entries<any>(steps)) {
            if (step?.type === "action" && step.data?.campaign_ref) {
              const campaignRef = step.data.campaign_ref;

              if (!campaignKeys.has(campaignRef)) {
                throw new ValidationError(
                  `Journey "${key}" action step "${stepKey}" references non-existent campaign "${campaignRef}"`,
                  "journey"
                );
              }
            }
          }
        }
      }
    }

    console.log("Cross-reference validation successful");
  }

  /**
   * Attempts to fix common schema validation issues
   */
  private fixSchemaViolations(plan: any, error: any): any {
    console.log("Attempting to fix schema violations...");

    // Deep clone the plan to avoid mutation issues
    const fixedPlan = JSON.parse(JSON.stringify(plan));

    // Ensure basic structure exists
    if (!fixedPlan.name) {
      fixedPlan.name = "Generated Automation Plan";
    }

    if (!fixedPlan.description) {
      fixedPlan.description = "Automatically generated marketing automation plan";
    }

    if (!fixedPlan.items) {
      fixedPlan.items = {};
    }

    // If items is empty, create minimal structure
    if (Object.keys(fixedPlan.items).length === 0) {
      console.log("Creating minimal plan structure due to empty items");
      this.createMinimalPlanStructure(fixedPlan);
      return fixedPlan;
    }

    // Extract error paths from Zod error
    const errorPaths: string[] = [];
    if (error && error.errors) {
      error.errors.forEach((err: any) => {
        if (err.path) {
          errorPaths.push(err.path.join("."));
        }
      });
    }

    console.log("Error paths:", errorPaths);

    // Determine if this is a simple campaign plan or complex automation
    const hasJourney = Object.values(fixedPlan.items).some(
      (item: any) => item?.type === "journey"
    );

    // Log plan structure type
    if (hasJourney) {
      console.log("Fixing a complex automation plan with journey");
    } else {
      console.log("Fixing a simple campaign plan");
    }

    // Fix each item based on its type
    for (const [key, item] of Object.entries(fixedPlan.items)) {
      if (!item || typeof item !== "object") continue;

      try {
        // Safely access type property
        const itemType = (item as any)?.type;
        if (!itemType) continue;

        switch (itemType) {
          case "list":
            this.fixListItem(key, item, errorPaths);
            break;

          case "campaign":
            this.fixCampaignItem(key, item, errorPaths);
            break;

          case "template":
            this.fixTemplateItem(key, item, errorPaths, null);
            break;

          case "journey":
            this.fixJourneyItem(key, item, errorPaths);
            break;
        }
      } catch (error) {
        console.warn(`Could not fix item "${key}":`, error);
      }
    }

    // For simple campaign plans, ensure there's at least one list and one campaign
    if (!hasJourney && Object.keys(fixedPlan.items).length > 0) {
      let hasList = false;
      let hasCampaign = false;
      let hasTemplate = false;

      for (const item of Object.values(fixedPlan.items) as any[]) {
        if (item?.type === "list") hasList = true;
        if (item?.type === "campaign") hasCampaign = true;
        if (item?.type === "template") hasTemplate = true;
      }

      // If missing critical components, add default ones
      if (!hasList) {
        const listKey = `default_list_${Math.random()
          .toString(36)
          .substring(2, 7)}`;
        fixedPlan.items[listKey] = {
          type: "list",
          config: {
            list: {
              name: "Default List",
              type: "dynamic",
              description: "Generated default list",
              is_visible: true,
              tags: [],
              user_ids: [],
              rule: {
                uuid:
                  "auto-generated-" +
                  Math.random().toString(36).substring(2, 15),
                type: "string",
                group: "user",
                path: "id",
                operator: "is set",
                value: "",
                children: [],
              },
            },
          },
        };
      }

      if (!hasCampaign) {
        const campaignKey = `default_campaign_${Math.random()
          .toString(36)
          .substring(2, 7)}`;

        // Find a list to reference
        const listKey =
          Object.entries(fixedPlan.items).find(
            ([_, item]) => (item as any)?.type === "list"
          )?.[0] || "";

        fixedPlan.items[campaignKey] = {
          type: "campaign",
          config: {
            campaign: {
              name: "Default Campaign",
              type: "blast",
              channel: "email",
              subscription_id: 1,
              provider_id: 1,
              list_refs: listKey ? [listKey] : [],
              send_in_user_timezone: true,
              send_at: "",
              state: "scheduled",
              tags: [],
            },
          },
        };
      }

      if (!hasTemplate) {
        const templateKey = `default_template_${Math.random()
          .toString(36)
          .substring(2, 7)}`;

        // Find a campaign to reference
        const campaignKey =
          Object.entries(fixedPlan.items).find(
            ([_, item]) => (item as any)?.type === "campaign"
          )?.[0] || "";

        fixedPlan.items[templateKey] = {
          type: "template",
          config: {
            template: {
              type: "email",
              name: "Default Template",
              locale: "en",
              campaign_ref: campaignKey || undefined,
              data: {
                editor: "code",
                name: "Default Template",
                subject: "Default Subject",
                html: "<html><body style='font-family: Arial, sans-serif; margin: 0; padding: 20px;'><h1 style='color: #333;'>Welcome</h1><p>Thank you for being a valued customer. We appreciate your business.</p><p><a href='https://example.com' style='background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Learn More</a></p></body></html>",
                text: "Welcome! Thank you for being a valued customer. We appreciate your business. Learn more at: https://example.com",
                from: {
                  name: "Marketing Team",
                  address: "<EMAIL>",
                },
                reply_to: "<EMAIL>",
                imagePrompt:
                  "A professional marketing image with modern design, clean layout, and brand colors that would be appropriate for a welcome email header.",
              },
            },
          },
        };
      }
    }

    return fixedPlan;
  }

  /**
   * Create minimal plan structure when items are empty or invalid
   */
  private createMinimalPlanStructure(plan: any): void {
    const listKey = `minimal_list_${Math.random().toString(36).substring(2, 7)}`;
    const campaignKey = `minimal_campaign_${Math.random().toString(36).substring(2, 7)}`;
    const templateKey = `minimal_template_${Math.random().toString(36).substring(2, 7)}`;

    // Create minimal list
    plan.items[listKey] = {
      type: "list",
      config: {
        list: {
          name: "Target List",
          type: "dynamic",
          description: "Generated target list",
          is_visible: true,
          tags: [],
          user_ids: [],
          rule: {
            uuid: `rule-${Math.random().toString(36).substring(2, 15)}`,
            type: "string",
            group: "user",
            path: "id",
            operator: "is set",
            value: "",
            children: [],
          },
        },
      },
    };

    // Create minimal campaign
    plan.items[campaignKey] = {
      type: "campaign",
      config: {
        campaign: {
          name: "Marketing Campaign",
          type: "blast",
          channel: "email",
          subscription_id: 1,
          provider_id: 1,
          list_refs: [listKey],
          send_in_user_timezone: true,
          send_at: "",
          state: "scheduled",
          tags: [],
        },
      },
    };

    // Create minimal template
    plan.items[templateKey] = {
      type: "template",
      config: {
        template: {
          type: "email",
          name: "Marketing Template",
          locale: "en",
          campaign_ref: campaignKey,
          data: {
            editor: "code",
            name: "Marketing Template",
            subject: "Important Update",
            html: "<html><body style='font-family: Arial, sans-serif; margin: 0; padding: 20px;'><h1 style='color: #333;'>Important Update</h1><p>Thank you for being a valued customer.</p></body></html>",
            text: "Important Update: Thank you for being a valued customer.",
            from: {
              name: "Marketing Team",
              address: "<EMAIL>",
            },
            reply_to: "<EMAIL>",
            imagePrompt: "A professional marketing image with clean design",
          },
        },
      },
    };
  }

  /**
   * Fix common list item issues
   */
  private fixListItem(key: string, item: any, errorPaths: string[]): void {
    if (!item.config) item.config = {};
    if (!item.config.list) item.config.list = {};

    const list = item.config.list;

    // Ensure required fields have defaults
    if (!list.name) list.name = `List ${key}`;
    if (!list.type) list.type = "dynamic";
    if (!list.description) list.description = "";
    if (list.is_visible === undefined) list.is_visible = true;
    if (!list.tags) list.tags = [];
    if (!list.user_ids) list.user_ids = [];

    // Default rule if missing
    if (!list.rule) {
      list.rule = {
        uuid: "auto-generated-" + Math.random().toString(36).substring(2, 15),
        type: "string",
        group: "user",
        path: "id",
        operator: "is set",
        value: "",
        children: [],
      };
    }
  }

  /**
   * Fix common campaign item issues
   */
  private fixCampaignItem(key: string, item: any, errorPaths: string[]): void {
    if (!item.config) item.config = {};
    if (!item.config.campaign) item.config.campaign = {};

    const campaign = item.config.campaign;

    // Ensure required fields have defaults
    if (!campaign.name) campaign.name = `Campaign ${key}`;
    if (!campaign.type) campaign.type = "blast";
    if (!campaign.channel) campaign.channel = "email";
    if (!campaign.subscription_id) campaign.subscription_id = 1;
    if (!campaign.provider_id) campaign.provider_id = 1;
    if (!campaign.tags) campaign.tags = [];

    // Fix lists if needed
    if (!campaign.list_refs && !campaign.list_ids) {
      campaign.list_refs = [];
    }

    // Set default send_in_user_timezone to true according to training examples
    if (campaign.send_in_user_timezone === undefined) {
      campaign.send_in_user_timezone = true;
    }

    // Set default state for campaign
    if (!campaign.state) {
      campaign.state = "scheduled";
    }

    // For send_at field, use empty string instead of null
    if (campaign.send_at === undefined || campaign.send_at === null) {
      campaign.send_at = "";
    }

    // For blast campaign types, ensure a send_at time is set to a future date if not already set
    if (campaign.type === "blast" && campaign.send_at === "") {
      // Set default to 2 days in the future at 10:00 AM
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 2);
      futureDate.setHours(10, 0, 0, 0);

      // Check if campaign name suggests urgency
      const isUrgent = (campaign.name || "").toLowerCase().includes("urgent");
      if (isUrgent) {
        // Set to 1 day in the future for urgent campaigns
        futureDate.setDate(futureDate.getDate() - 1);
      }

      try {
        // Get timezone offset
        const tzOffset = -futureDate.getTimezoneOffset();
        const tzHours = Math.floor(Math.abs(tzOffset) / 60)
          .toString()
          .padStart(2, "0");
        const tzMinutes = (Math.abs(tzOffset) % 60).toString().padStart(2, "0");
        const tzSign = tzOffset >= 0 ? "+" : "-";

        // Use proper date format
        campaign.send_at =
          futureDate.getFullYear() +
          "-" +
          String(futureDate.getMonth() + 1).padStart(2, "0") +
          "-" +
          String(futureDate.getDate()).padStart(2, "0") +
          "T" +
          String(futureDate.getHours()).padStart(2, "0") +
          ":" +
          String(futureDate.getMinutes()).padStart(2, "0") +
          ":" +
          String(futureDate.getSeconds()).padStart(2, "0") +
          tzSign +
          tzHours +
          ":" +
          tzMinutes;
      } catch (e) {
        console.warn("Error fixing date format:", e);
      }
    }
  }

  /**
   * Fix common template item issues
   * @param key Item key
   * @param item The item to fix
   * @param errorPaths Array of error paths
   * @param location Optional location object containing sender information
   */
  private fixTemplateItem(
    key: string,
    item: any,
    errorPaths: string[],
    location?: any
  ): void {
    if (!item.config) item.config = {};
    if (!item.config.template) item.config.template = {};
    const template = item.config.template;
    // Ensure required fields have defaults
    if (!template.name) template.name = `Template ${key}`;
    if (!template.type) template.type = "email";
    if (!template.locale) template.locale = "en";
    // Fix campaign reference issues
    const hasCampaignRef = template.campaign_ref !== undefined;
    const hasCampaignId = template.campaign_id !== undefined;
    if (hasCampaignRef && hasCampaignId) {
      // Prefer campaign_ref over campaign_id if both exist
      delete template.campaign_id;
    }
    if (!hasCampaignRef && !hasCampaignId) {
      // Default to a numeric campaign_id if neither exists
      template.campaign_id = 1;
    }
    // Ensure template data exists and has required fields
    if (!template.data) template.data = {};
    const data = template.data;
    if (!data.editor) data.editor = "code";
    if (!data.name) data.name = template.name;
    if (!data.subject) data.subject = template.name;
    if (!data.html) {
      data.html =
        "<html><body style='font-family: Arial, sans-serif; margin: 0; padding: 20px;'><h1 style='color: #333;'>Welcome</h1><p>Thank you for being a valued customer. We appreciate your business.</p></body></html>";
    }
    if (!data.text) {
      data.text =
        "Welcome! Thank you for being a valued customer. We appreciate your business.";
    }
    if (!data.from) data.from = {};

    // Use location sender information if available, otherwise use defaults
    if (location) {
      data.from.name =
        location.sender_name || location.name || "Marketing Team";
      data.from.address = location.sender_email || "<EMAIL>";
      data.reply_to = location.sender_email || "<EMAIL>";
    } else {
      data.from.name = "Marketing Team";
      data.from.address = "<EMAIL>";
      data.reply_to = "<EMAIL>";
    }

    // For email templates, ensure there's an image prompt for beautiful header images
    if (template.type === "email" && !data.imagePrompt) {
      data.imagePrompt = `Beautiful marketing image for ${
        data.subject || template.name
      } that conveys the message professionally with modern design and appealing visuals.`;
    }
    // Insert image into HTML template if needed
    if (template.type === "email") {
      this.insertImageIntoTemplate(template);
    }
  }

  /**
   * Fix common journey item issues
   */
  private fixJourneyItem(key: string, item: any, errorPaths: string[]): void {
    if (!item.config) item.config = {};
    if (!item.config.journey) item.config.journey = {};

    const journey = item.config.journey;

    // Ensure required fields have defaults
    if (!journey.name) journey.name = `Journey ${key}`;
    if (!journey.description) journey.description = "";
    if (journey.published === undefined) journey.published = false;
    if (!journey.tags) journey.tags = [];

    // Ensure steps object exists
    if (!journey.steps || typeof journey.steps !== "object") {
      journey.steps = {};

      // Create at least an entrance step if none exists
      const entranceId = `entrance_${Math.random()
        .toString(36)
        .substring(2, 8)}`;
      journey.steps[entranceId] = {
        type: "entrance",
        name: "Entry Point",
        x: 0,
        y: 0,
        data: {
          trigger: "event",
          event_name: "user_event",
          multiple_entries: true,
          simultaneous_entries: false,
        },
        children: [],
      };
    } else {
      // Fix existing steps
      let hasEntrance = false;

      for (const [stepKey, step] of Object.entries<any>(journey.steps)) {
        // Ensure all steps have required fields
        if (!step.name) step.name = `Step ${stepKey}`;
        if (!step.data) step.data = {};
        if (!Array.isArray(step.children)) step.children = [];

        // Fix step coordinates if needed
        if (step.x === undefined) step.x = 0;
        if (step.y === undefined) step.y = 0;

        // Fix entrance step positions
        if (step.type === "entrance") {
          hasEntrance = true;
          step.x = 0;
          step.y = 0;
        }
      }

      // Create entrance step if missing
      if (!hasEntrance) {
        const entranceId = `entrance_${Math.random()
          .toString(36)
          .substring(2, 8)}`;
        journey.steps[entranceId] = {
          type: "entrance",
          name: "Entry Point",
          x: 0,
          y: 0,
          data: {
            trigger: "event",
            event_name: "user_event",
            multiple_entries: true,
            simultaneous_entries: false,
          },
          children: [],
        };
      }
    }
  }

  /**
   * Helper method to insert an image into template HTML if needed
   * @param template The template object to process
   */
  private insertImageIntoTemplate(template: any): void {
    if (!template || !template.data) return;
    const data = template.data;

    // Only process if we have an image URL and HTML content
    if (data.imageUrl && data.html) {
      // Skip if image is already in the HTML
      if (
        data.html.includes(`src="${data.imageUrl}"`) ||
        data.html.includes(`src='${data.imageUrl}'`)
      ) {
        return;
      }

      // Create responsive image HTML
      const imgHtml = `<img src="${data.imageUrl}" alt="${
        data.subject || "Email header"
      }" style="width:100%;max-width:600px;height:auto;display:block;margin:0 auto 20px auto;" />`;

      // Insert the image at the beginning of content
      if (data.html.includes("<body>")) {
        data.html = data.html.replace("<body>", "<body>" + imgHtml);
      } else if (data.html.includes("<p>")) {
        data.html = data.html.replace("<p>", imgHtml + "<p>");
      } else {
        data.html = imgHtml + data.html;
      }
    }
  }

  /**
   * Determines an appropriate scheduled time for a campaign
   * If send_at is already provided, use that; otherwise,
   * generate a reasonable future date based on campaign context
   * @param campaignPlan The campaign plan object
   * @returns An ISO date string or null
   */
  private determineScheduledTime(campaignPlan: any): string | null {
    // If a send_at time is explicitly provided, use it
    if (campaignPlan && campaignPlan.send_at) {
      // Convert send_at to ISO date string with timezone offset
      try {
        const sendAt = new Date(campaignPlan.send_at);
        if (!isNaN(sendAt.getTime())) {
          // Generate timezone offset string
          const tzOffset = -sendAt.getTimezoneOffset();
          const tzHours = Math.floor(Math.abs(tzOffset) / 60)
            .toString()
            .padStart(2, "0");
          const tzMinutes = (Math.abs(tzOffset) % 60)
            .toString()
            .padStart(2, "0");
          const tzSign = tzOffset >= 0 ? "+" : "-";

          // Format the date with timezone offset
          return (
            sendAt.getFullYear() +
            "-" +
            String(sendAt.getMonth() + 1).padStart(2, "0") +
            "-" +
            String(sendAt.getDate()).padStart(2, "0") +
            "T" +
            String(sendAt.getHours()).padStart(2, "0") +
            ":" +
            String(sendAt.getMinutes()).padStart(2, "0") +
            ":" +
            String(sendAt.getSeconds()).padStart(2, "0") +
            tzSign +
            tzHours +
            ":" +
            tzMinutes
          );
        }
      } catch (e) {
        console.warn("Invalid send_at date provided:", campaignPlan.send_at);
      }
    }

    // For blast campaigns specifically, we should generate a reasonable future date
    if (campaignPlan && (campaignPlan.type === "blast" || !campaignPlan.type)) {
      // Set a default time 2 days in the future at 10:00 AM
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 2);
      futureDate.setHours(10, 0, 0, 0);

      // Check if campaign has "urgent" in the name or description
      const isUrgent = (
        (campaignPlan.name || "") + (campaignPlan.description || "")
      )
        .toLowerCase()
        .includes("urgent");

      // For urgent campaigns, set date to tomorrow
      if (isUrgent) {
        futureDate.setDate(new Date().getDate() + 1);
      }

      // Generate timezone offset string
      const tzOffset = -futureDate.getTimezoneOffset();
      const tzHours = Math.floor(Math.abs(tzOffset) / 60)
        .toString()
        .padStart(2, "0");
      const tzMinutes = (Math.abs(tzOffset) % 60).toString().padStart(2, "0");
      const tzSign = tzOffset >= 0 ? "+" : "-";

      // Format the date with timezone offset
      return (
        futureDate.getFullYear() +
        "-" +
        String(futureDate.getMonth() + 1).padStart(2, "0") +
        "-" +
        String(futureDate.getDate()).padStart(2, "0") +
        "T" +
        String(futureDate.getHours()).padStart(2, "0") +
        ":" +
        String(futureDate.getMinutes()).padStart(2, "0") +
        ":" +
        String(futureDate.getSeconds()).padStart(2, "0") +
        tzSign +
        tzHours +
        ":" +
        tzMinutes
      );
    }

    return null;
  }

  /**
   * Sets the sender information in all templates of a plan based on location data
   * @param plan The automation plan
   * @param location The location object containing sender information
   */
  public setSenderInformation(plan: any, location: any): void {
    if (!plan || !plan.items) return;

    console.log("Setting sender information from location:", location);

    for (const [key, item] of Object.entries(plan.items)) {
      try {
        // Only process template items
        if ((item as any)?.type === "template") {
          console.log(`Updating sender information for template "${key}"`);

          const template = (item as any).config?.template;
          if (!template || !template.data) {
            console.warn(`Template ${key} has no data to update`);
            continue;
          }

          const data = template.data;

          // Initialize from object if it doesn't exist
          if (!data.from) data.from = {};

          // Set sender name from location data
          data.from.name =
            location.sender_name || location.name || "Marketing Team";

          // Set sender email from location data
          data.from.address = location.sender_email || "<EMAIL>";

          // Set reply-to address
          data.reply_to = location.sender_email || "<EMAIL>";

          console.log(
            `Updated sender information for template "${key}": ${data.from.name} <${data.from.address}>`
          );
        }
      } catch (error) {
        console.warn(
          `Could not update sender information for item "${key}":`,
          error
        );
      }
    }
  }
}
