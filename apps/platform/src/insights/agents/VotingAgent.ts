import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";
import { StructuredOutputParser } from "langchain/output_parsers";
import { Insight } from "../InsightService";
import { ValidationAgent } from "./ValidationAgent";
import { AutomationPlanSchema } from "../schemas";

// Define a simpler parser interface that doesn't require all StructuredOutputParser methods
export interface SimpleParser {
  parse: (text: string) => Promise<any>;
  getFormatInstructions: () => string;
}

/**
 * Voting Agent: Responsible for generating and selecting the best automation plan
 */
export class VotingAgent {
  private validationAgent: ValidationAgent;

  constructor() {
    this.validationAgent = new ValidationAgent();
  }

  private async generatePlan(
    insight: Insight,
    context: any,
    model: string,
    parser: SimpleParser,
    prompt: string
  ) {
    console.log("=== Starting Plan Generation ===");
    console.log("Insight:", "[Insight details omitted for brevity]");
    console.log("Model:", model);

    const llm = new ChatOpenAI({
      modelName: model || "gpt-4.1-mini",
      temperature: 0.7,
      openAIApiKey: process.env.OPENAI_API_KEY,
    });

    console.log("Sending prompt to LLM...");
    console.log("Prompt:", "[Prompt details omitted for brevity]");

    const response = await llm.call([
      new SystemMessage(
        "You are a helpful AI assistant. Return only valid JSON."
      ),
      new HumanMessage(prompt),
    ]);

    console.log("Raw LLM Response:", "[Response content omitted for brevity]");

    return typeof response.content === "string"
      ? response.content
      : JSON.stringify(response.content);
  }

  private async selectBestPlan(
    candidates: any[],
    context: any,
    model: string
  ): Promise<any> {
    console.log("=== Starting Plan Selection ===");
    console.log("Number of candidates:", candidates.length);

    const llm = new ChatOpenAI({
      modelName: model || "gpt-4.1-mini",
      temperature: 0.3,
      openAIApiKey: process.env.OPENAI_API_KEY,
    });

    const prompt = `
      You are an expert in marketing automation and campaign strategy.
      Review these automation plan candidates and select the best one based on:
      1. Effectiveness - Will it achieve the insight's goals?
      2. Efficiency - Does it use resources optimally?
      3. Feasibility - Can it be implemented with our tools?
      4. Impact - Will it drive meaningful results?

      CRITICAL: Your response must be ONLY the JSON of the best plan.
      - No explanations
      - No backticks
      - No markdown
      - Just the raw JSON object

      Context:
      ${JSON.stringify(context)}

      Plan Candidates:
      ${JSON.stringify(candidates)}

      Return ONLY the raw JSON of the best plan.
    `;

    try {
      const response = await llm.call([
        new SystemMessage(
          "You are a helpful AI assistant. Return only valid JSON with no formatting, commentary, or backticks."
        ),
        new HumanMessage(prompt),
      ]);

      const content =
        typeof response.content === "string"
          ? response.content
          : JSON.stringify(response.content);

      // First try basic cleaning
      const cleaned = content
        .replace(/```json\s*|\s*```/g, "") // Remove code blocks
        .replace(/^`|`$/g, "") // Remove single backticks
        .trim();

      try {
        const parsed = JSON.parse(cleaned);

        // Validate the structure
        if (!parsed || !parsed.items || typeof parsed.items !== "object") {
          console.warn("Selected plan missing required structure:", parsed);
          throw new Error("Selected plan missing required structure");
        }

        // Validate item references
        Object.entries(parsed.items).forEach(
          ([itemKey, item]: [string, any]) => {
            if (item.type === "template" && item.config?.template) {
              const template = item.config.template;

              // Check for both campaign_ref and campaign_id
              if (template.campaign_ref && template.campaign_id) {
                delete template.campaign_id;
              }

              // Validate campaign_ref format
              if (template.campaign_ref) {
                const referencedCampaign = parsed.items[template.campaign_ref];
                if (
                  !referencedCampaign ||
                  referencedCampaign.type !== "campaign"
                ) {
                  delete template.campaign_ref;
                }
              }
            }
          }
        );

        return parsed;
      } catch (parseError) {
        // If basic cleaning fails, try more aggressive cleaning
        const aggressive = cleaned
          .replace(/\/\/.*/g, "") // Remove comments
          .replace(/,(\s*[}\]])/g, "$1") // Fix trailing commas
          .replace(/(['"])?([a-zA-Z0-9_]+)(['"])?\s*:/g, '"$2": ') // Fix quotes
          .trim();

        try {
          const parsed = JSON.parse(aggressive);
          if (!parsed || !parsed.items || typeof parsed.items !== "object") {
            throw new Error("Invalid plan structure after aggressive cleaning");
          }
          return parsed;
        } catch (finalError) {
          console.error("All JSON parsing attempts failed");
          console.error("Original content:", content);
          console.error("Basic cleaned content:", cleaned);
          console.error("Aggressively cleaned content:", aggressive);
          console.error(
            "Final error:",
            finalError instanceof Error
              ? finalError.message
              : String(finalError)
          );

          // Fall back to first valid candidate
          const validCandidate = candidates.find(
            (c) => c && c.items && typeof c.items === "object"
          );

          if (!validCandidate) {
            throw new Error("No valid candidates available as fallback");
          }

          return validCandidate;
        }
      }
    } catch (error) {
      console.error("Error in plan selection:", error);
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to select best plan: ${errorMessage}`);
    }
  }

  async generateWithVoting(
    insight: Insight,
    context: any,
    model: string,
    parser: SimpleParser,
    prompt: string,
    numCandidates: number = 3
  ): Promise<any> {
    console.log("=== Starting Plan Generation with Voting ===");
    console.log("Number of candidates to generate:", numCandidates);
    console.log("Using model:", model);

    // Generate multiple plan candidates in parallel
    console.log("Generating plan candidates...");
    const candidatePromises = Array(numCandidates)
      .fill(null)
      .map(() => this.generatePlan(insight, context, model, parser, prompt));

    const candidates = await Promise.all(candidatePromises);
    console.log("Generated raw candidates:", candidates);

    // Parse and validate all candidates
    console.log("Validating candidates...");
    const validCandidates = await Promise.all(
      candidates.map(async (candidate, index) => {
        try {
          console.log(`Validating candidate ${index + 1}...`);
          console.log(`Raw candidate ${index + 1} content:`, candidate.substring(0, 500) + "...");

          const validated = await this.validationAgent.validateAndRepair(
            candidate,
            AutomationPlanSchema,
            context
          );
          console.log(`Candidate ${index + 1} validated successfully`);
          return validated;
        } catch (error) {
          console.error(`Failed to validate candidate ${index + 1}:`, error);
          console.error(`Candidate ${index + 1} raw content:`, candidate);

          // Try to extract more specific error information
          if (error instanceof Error) {
            console.error(`Validation error details for candidate ${index + 1}:`, {
              message: error.message,
              stack: error.stack,
            });
          }

          return null;
        }
      })
    );

    const filteredCandidates = validCandidates.filter((c: any) => c !== null);
    console.log("Number of valid candidates:", filteredCandidates.length);

    if (filteredCandidates.length === 0) {
      console.error("No valid automation plan candidates generated");
      console.log("Attempting to generate fallback plan...");

      try {
        // Generate a minimal fallback plan
        const fallbackPlan = this.generateFallbackPlan(insight, context);
        console.log("Generated fallback plan:", JSON.stringify(fallbackPlan, null, 2));

        // Validate the fallback plan
        const validatedFallback = await this.validationAgent.validateAndRepair(
          JSON.stringify(fallbackPlan),
          AutomationPlanSchema,
          context
        );

        console.log("Fallback plan validated successfully");
        return validatedFallback;
      } catch (fallbackError) {
        console.error("Failed to generate valid fallback plan:", fallbackError);
        throw new Error("No valid automation plan candidates generated and fallback plan failed");
      }
    }

    // Select the best plan through voting
    console.log("Selecting best plan...");
    const bestPlan = await this.selectBestPlan(
      filteredCandidates,
      context,
      model
    );
    console.log("Selected best plan:", JSON.stringify(bestPlan, null, 2));

    return bestPlan;
  }

  /**
   * Generate a minimal fallback automation plan when all candidates fail validation
   */
  private generateFallbackPlan(insight: Insight, context: any): any {
    console.log("Generating fallback automation plan...");

    const channel = insight.delivery_channel || "email";
    const isEmail = channel === "email";

    // Generate unique keys
    const listKey = `fallback_list_${Math.random().toString(36).substring(2, 7)}`;
    const campaignKey = `fallback_campaign_${Math.random().toString(36).substring(2, 7)}`;
    const templateKey = `fallback_template_${Math.random().toString(36).substring(2, 7)}`;

    // Create a minimal valid automation plan
    const fallbackPlan = {
      name: `Fallback Plan for ${insight.title || 'Marketing Campaign'}`,
      description: `Automatically generated fallback plan for insight: ${insight.description || 'Marketing automation'}`,
      items: {
        [listKey]: {
          type: "list",
          config: {
            list: {
              name: "Target Customers",
              type: "dynamic",
              description: "Customers targeted for this campaign",
              is_visible: true,
              tags: [],
              user_ids: [],
              rule: {
                uuid: `rule-${Math.random().toString(36).substring(2, 15)}`,
                type: "string",
                group: "user",
                path: "id",
                operator: "is set",
                value: "",
                children: [],
              },
            },
          },
        },
        [campaignKey]: {
          type: "campaign",
          config: {
            campaign: {
              name: `${insight.title || 'Marketing'} Campaign`,
              type: "blast",
              channel: channel,
              subscription_id: context.channelConfig?.[channel]?.subscription_id || 1,
              provider_id: context.channelConfig?.[channel]?.provider_id || 1,
              list_refs: [listKey],
              send_in_user_timezone: true,
              send_at: "",
              state: "scheduled",
              tags: [],
            },
          },
        },
        [templateKey]: {
          type: "template",
          config: {
            template: {
              type: channel,
              name: `${insight.title || 'Marketing'} Template`,
              locale: "en",
              campaign_ref: campaignKey,
              data: isEmail ? {
                editor: "code",
                name: `${insight.title || 'Marketing'} Template`,
                subject: insight.title || "Important Update",
                html: `<html><body style='font-family: Arial, sans-serif; margin: 0; padding: 20px;'>
                  <h1 style='color: #333;'>${insight.title || 'Important Update'}</h1>
                  <p>${insight.description || 'We have an important update to share with you.'}</p>
                  <p>Thank you for being a valued customer.</p>
                  <p><a href='#' style='background-color: #4CAF50; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>Learn More</a></p>
                </body></html>`,
                text: `${insight.title || 'Important Update'}\n\n${insight.description || 'We have an important update to share with you.'}\n\nThank you for being a valued customer.\n\nLearn more at: [link]`,
                from: {
                  name: context.location?.name || "Marketing Team",
                  address: context.location?.email || "<EMAIL>",
                },
                reply_to: context.location?.email || "<EMAIL>",
                imagePrompt: "A professional marketing image with clean design and modern layout",
              } : {
                text: `${insight.title || 'Important Update'}: ${insight.description || 'We have an important update to share with you.'} Thank you for being a valued customer.`,
              },
            },
          },
        },
      },
    };

    console.log("Generated fallback plan structure");
    return fallbackPlan;
  }
}
