import { z } from "zod";
import {
  RuleSchema,
  ListConfigSchema,
  CampaignConfigSchema,
  TemplateConfigSchema,
  JourneyConfigSchema,
} from "../schemas";

/**
 * Type definitions extracted from Zod schemas to ensure consistency
 * between validation schemas and type checking.
 */
// Basic configuration types
export interface ChannelConfig {
  subscription_id: number;
  provider_id: number;
}

export interface ChannelConfigs {
  email: ChannelConfig | null;
  text: ChannelConfig | null;
}

// Core types derived from schemas
export type ListRule = z.infer<typeof RuleSchema>;
export type ListConfig = z.infer<typeof ListConfigSchema>;
export type CampaignConfig = z.infer<typeof CampaignConfigSchema>;
export type TemplateConfig = z.infer<typeof TemplateConfigSchema>;
export type TemplateData = TemplateConfig["data"];

export interface AutomationItem {
  type: "list" | "campaign" | "template" | "journey";
  config: {
    list?: ListConfig;
    campaign?: CampaignConfig;
    template?: TemplateConfig;
    journey?: z.infer<typeof JourneyConfigSchema>["config"]["journey"];
  };
}

export class ValidationError extends Error {
  constructor(message: string, public stepType?: string, public details?: any) {
    super(message);
    this.name = "ValidationError";
  }
}
