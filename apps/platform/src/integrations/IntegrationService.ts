import { K<PERSON> } from "knex";
import { logger } from "../config/logger";
import * as agentsConfig from "../agents/agents.json";

export interface IntegrationStatus {
  connected: boolean;
  lastChecked: string;
  error?: string;
  recordCount?: number;
}

export interface LocationIntegrations {
  pos?: IntegrationStatus;
  product_data?: IntegrationStatus;
  customer_data?: IntegrationStatus;
  social_media?: IntegrationStatus;
  business_docs?: IntegrationStatus;
  competitor_data?: IntegrationStatus;
  market_data?: IntegrationStatus;
  government_db?: IntegrationStatus;
  gov_db?: IntegrationStatus;
  lab_data?: IntegrationStatus;
  [key: string]: IntegrationStatus | undefined;
}

export interface AgentAvailabilityResult {
  id: string;
  name: string;
  role: string;
  description: string;
  icon: string;
  capabilities: string[];
  disabled: boolean;
  unlocked: boolean;
  missingRequirements: string[];
  metadata: {
    integrationStatus: LocationIntegrations;
    recordCounts: Record<string, number>;
  };
}

export async function checkIntegrationStatus(
  db: Knex,
  locationId: string
): Promise<LocationIntegrations> {
  const now = new Date().toISOString();

  const checkTable = async (
    table: string
  ): Promise<{ connected: boolean; recordCount: number }> => {
    try {
      const result = await db(table)
        .where({ location_id: locationId })
        .count("* as count")
        .first();

      const recordCount = result ? parseInt(result.count as string) : 0;
      return { connected: recordCount > 0, recordCount };
    } catch (error) {
      logger.warn(`Error checking table ${table}:`, error);
      return { connected: false, recordCount: 0 };
    }
  };

  // Check for basic data integrations with record counts
  const posCheck = await checkTable("pos_data");
  const productCheck = await checkTable("products");
  const customerCheck = await checkTable("users");
  const competitorCheck = await checkTable("location_competitors");
  const marketDataCheck = await checkTable("market_trends");
  const govDbCheck = await checkTable("compliance_records");
  const labDataCheck = await checkTable("lab_results");
  const businessDocsCheck = await checkTable("documents");

  return {
    pos: {
      connected: posCheck.connected,
      lastChecked: now,
      recordCount: posCheck.recordCount,
    },
    product_data: {
      connected: productCheck.connected,
      lastChecked: now,
      recordCount: productCheck.recordCount,
    },
    customer_data: {
      connected: customerCheck.connected,
      lastChecked: now,
      recordCount: customerCheck.recordCount,
    },
    competitor_data: {
      connected: competitorCheck.connected,
      lastChecked: now,
      recordCount: competitorCheck.recordCount,
    },
    market_data: {
      connected: marketDataCheck.connected,
      lastChecked: now,
      recordCount: marketDataCheck.recordCount,
    },
    government_db: {
      connected: govDbCheck.connected,
      lastChecked: now,
      recordCount: govDbCheck.recordCount,
    },
    gov_db: {
      connected: govDbCheck.connected,
      lastChecked: now,
      recordCount: govDbCheck.recordCount,
    }, // Alias
    lab_data: {
      connected: labDataCheck.connected,
      lastChecked: now,
      recordCount: labDataCheck.recordCount,
    },
    business_docs: {
      connected: businessDocsCheck.connected,
      lastChecked: now,
      recordCount: businessDocsCheck.recordCount,
    },
  };
}

export async function getAvailableAgents(
  db: Knex,
  locationId: string
): Promise<AgentAvailabilityResult[]> {
  try {
    const integrationStatus = await checkIntegrationStatus(db, locationId);
    const agents = (agentsConfig as any).agents;
    const results: AgentAvailabilityResult[] = [];

    for (const [agentId, agentConfig] of Object.entries(agents)) {
      const agent = agentConfig as any;

      // Skip disabled agents
      if (agent.disabled) {
        continue;
      }

      // Check if agent requirements are met
      const requiredIntegrations = agent.requirements?.required || [];
      const missingRequirements: string[] = [];

      for (const requirement of requiredIntegrations) {
        const integration = integrationStatus[requirement];
        if (!integration?.connected) {
          missingRequirements.push(`Missing ${requirement} integration`);
        }
      }

      // Extract record counts for metadata
      const recordCounts: Record<string, number> = {};
      Object.entries(integrationStatus).forEach(([key, value]) => {
        if (value?.recordCount !== undefined) {
          recordCounts[key] = value.recordCount;
        }
      });

      results.push({
        id: agentId,
        name: agent.name,
        role: agent.role,
        description: agent.description,
        icon: agent.icon || "🤖",
        capabilities: agent.capabilities || [],
        disabled: false,
        unlocked: missingRequirements.length === 0,
        missingRequirements,
        metadata: {
          integrationStatus,
          recordCounts,
        },
      });
    }

    logger.info({
      message: "Agent availability checked",
      locationId,
      totalAgents: results.length,
      availableAgents: results.filter((a) => a.unlocked).length,
      unavailableAgents: results.filter((a) => !a.unlocked).length,
    });

    return results;
  } catch (error) {
    logger.error("Error checking agent availability:", error);
    return [];
  }
}
