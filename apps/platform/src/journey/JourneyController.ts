import Router, { ParamMiddleware } from "@koa/router";
import { locationRoleMiddleware } from "../locations/LocationService";
import { LocationState } from "../auth/AuthMiddleware";
import { searchParamsSchema } from "../core/searchParams";
import { JSONSchemaType, validate } from "../core/validate";
import { extractQueryParams } from "../utilities";
import Journey, {
  JourneyEntranceTriggerParams,
  JourneyParams,
} from "./Journey";
import {
  createJourney,
  getJourneyStepMap,
  getJourney,
  pagedJourneys,
  setJourneyStepMap,
  updateJourney,
  pagedEntrancesByJourney,
  getEntranceLog,
  pagedUsersByStep,
  archiveJourney,
  deleteJourney,
  exitUserFromJourney,
} from "./JourneyRepository";
import {
  JourneyStep,
  JourneyStepMapParams,
  JourneyUserStep,
  journeyStepTypes,
  toJourneyStepMap,
} from "./JourneyStep";
import { User } from "../users/User";
import { RequestError } from "../core/errors";
import JourneyError from "./JourneyError";
import { getUserFromContext } from "../users/UserRepository";
import { triggerEntrance } from "./JourneyService";
import { OpenAI } from "@langchain/openai";
import { PosData } from "../pos/PosData";

/**
 * @swagger
 * components:
 *   schemas:
 *     Journey:
 *       type: object
 *       required: [id, name, location_id, published]
 *       properties:
 *         id:
 *           type: integer
 *         name:
 *           type: string
 *         description:
 *           type: string
 *           nullable: true
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           nullable: true
 *         published:
 *           type: boolean
 *         location_id:
 *           type: integer
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *         deleted_at:
 *           type: string
 *           format: date-time
 *           nullable: true
 *     JourneyStep:
 *       type: object
 *       required: [type, x, y]
 *       properties:
 *         type:
 *           type: string
 *         name:
 *           type: string
 *           nullable: true
 *         data:
 *           type: object
 *           nullable: true
 *           additionalProperties: true
 *         data_key:
 *           type: string
 *           nullable: true
 *         x:
 *           type: number
 *         y:
 *           type: number
 *         children:
 *           type: array
 *           nullable: true
 *           items:
 *             type: object
 *             required: [external_id]
 *             properties:
 *               external_id:
 *                 type: string
 *               path:
 *                 type: string
 *                 nullable: true
 *               data:
 *                 type: object
 *                 nullable: true
 *                 additionalProperties: true
 *     JourneySuggestion:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *         description:
 *           type: string
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *         published:
 *           type: boolean
 *         steps:
 *           type: object
 *           additionalProperties:
 *             $ref: '#/components/schemas/JourneyStep'
 *     JourneyUserStep:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         user_id:
 *           type: integer
 *         journey_id:
 *           type: integer
 *         entrance_id:
 *           type: integer
 *           nullable: true
 *         ended_at:
 *           type: string
 *           format: date-time
 *           nullable: true
 *     JourneyEntrance:
 *       type: object
 *       properties:
 *         journey:
 *           $ref: '#/components/schemas/Journey'
 *         user:
 *           $ref: '#/components/schemas/User'
 *         userSteps:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/JourneyUserStep'
 *     JourneyTriggerRequest:
 *       type: object
 *       required: [entrance_id, user]
 *       properties:
 *         entrance_id:
 *           type: integer
 *         user:
 *           type: object
 *           required: [external_id]
 *           properties:
 *             external_id:
 *               type: string
 *             email:
 *               type: string
 *               nullable: true
 *             phone:
 *               type: string
 *               nullable: true
 *             device_token:
 *               type: string
 *               nullable: true
 *             timezone:
 *               type: string
 *               nullable: true
 *             locale:
 *               type: string
 *               nullable: true
 *         event:
 *           type: object
 *           nullable: true
 *           additionalProperties: true
 */

/**
 * @swagger
 * tags:
 *   name: Journeys
 *   description: Customer journey automation endpoints
 */

const router = new Router<LocationState & { journey?: Journey }>({
  prefix: "/automations",
});

router.use(locationRoleMiddleware("editor"));

/**
 * @swagger
 * /automations:
 *   get:
 *     summary: Get paged journeys
 *     tags: [Journeys]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of journeys
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 items:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Journey'
 *                 total:
 *                   type: integer
 */
router.get("/", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await pagedJourneys(params, ctx.state.location.id);
});

const journeyParams: JSONSchemaType<JourneyParams> = {
  $id: "journeyParams",
  type: "object",
  required: ["name"],
  properties: {
    name: {
      type: "string",
    },
    description: {
      type: "string",
      nullable: true,
    },
    tags: {
      type: "array",
      items: {
        type: "string",
      },
      nullable: true,
    },
    published: {
      type: "boolean",
    },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /automations:
 *   post:
 *     summary: Create a new journey
 *     tags: [Journeys]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [name]
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               published:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Created journey
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Journey'
 */
router.post("/", async (ctx) => {
  const payload = validate(journeyParams, ctx.request.body);

  // Check if journey with same name exists
  const existingJourney = await Journey.first((b) =>
    b.where({
      location_id: ctx.state.location.id,
      name: payload.name,
      deleted_at: null,
    })
  );

  if (existingJourney) {
    ctx.body = existingJourney;
    return;
  }

  ctx.body = await createJourney(ctx.state.location.id, payload);
});

/**
 * @swagger
 * /automations/entrances/{entranceId}:
 *   get:
 *     summary: Get journey entrance details
 *     tags: [Journeys]
 *     parameters:
 *       - in: path
 *         name: entranceId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Entrance ID
 *     responses:
 *       200:
 *         description: Journey entrance details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/JourneyEntrance'
 *       404:
 *         description: Entrance not found
 */
router.get("/entrances/:entranceId", async (ctx) => {
  const entrance = await JourneyUserStep.first((q) =>
    q
      .join("journeys", "journey_user_step.journey_id", "=", "journeys.id")
      .where("journeys.location_id", ctx.state.location.id)
      .where("journey_user_step.id", parseInt(ctx.params.entranceId, 10))
      .whereNull("journey_user_step.entrance_id")
  );
  if (!entrance) {
    return ctx.throw(404);
  }
  const [user, journey, userSteps] = await Promise.all([
    User.find(entrance.user_id),
    Journey.find(entrance.journey_id),
    getEntranceLog(entrance.id),
  ]);
  ctx.body = { journey, user, userSteps };
});

const checkJourneyId: ParamMiddleware = async (value, ctx, next) => {
  ctx.state.journey = await getJourney(parseInt(value), ctx.state.location.id);
  if (!ctx.state.journey) {
    throw new RequestError(JourneyError.JourneyDoesNotExist);
  }
  return await next();
};

router.param("journeyId", checkJourneyId);

router.get("/:journeyId", async (ctx) => {
  ctx.body = ctx.state.journey;
});

router.patch("/:journeyId", async (ctx) => {
  ctx.body = await updateJourney(
    ctx.state.journey!.id,
    validate(journeyParams, ctx.request.body)
  );
});

router.delete("/:journeyId", async (ctx) => {
  const { id, location_id, deleted_at } = ctx.state.journey!;
  if (deleted_at) {
    await deleteJourney(id, location_id);
  } else {
    await archiveJourney(id, location_id);
  }

  ctx.body = true;
});

const journeyStepsParamsSchema: JSONSchemaType<JourneyStepMapParams> = {
  $id: "journeyStepsParams",
  type: "object",
  required: [],
  additionalProperties: {
    type: "object",
    required: ["type", "x", "y"],
    properties: {
      type: {
        type: "string",
        enum: Object.keys(journeyStepTypes),
      },
      name: {
        type: "string",
        nullable: true,
      },
      data: {
        type: "object", // TODO: Could validate further based on sub types
        nullable: true,
        additionalProperties: true,
      },
      data_key: {
        type: "string",
        nullable: true,
      },
      x: {
        type: "number",
      },
      y: {
        type: "number",
      },
      children: {
        type: "array",
        nullable: true,
        items: {
          type: "object",
          required: ["external_id"],
          properties: {
            external_id: {
              type: "string",
            },
            path: {
              type: "string",
              nullable: true,
            },
            data: {
              type: "object", // TODO: this is also specific to the parent node's type
              nullable: true,
              additionalProperties: true,
            },
          },
        },
      },
    },
    additionalProperties: false,
  },
};

/**
 * @swagger
 * /automations/{journeyId}/steps:
 *   get:
 *     summary: Get journey step map
 *     tags: [Journeys]
 *     parameters:
 *       - in: path
 *         name: journeyId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Journey ID
 *     responses:
 *       200:
 *         description: Journey step map
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 $ref: '#/components/schemas/JourneyStep'
 */
router.get("/:journeyId/steps", async (ctx) => {
  ctx.body = await getJourneyStepMap(ctx.state.journey!.id);
});

/**
 * @swagger
 * /automations/{journeyId}/steps:
 *   put:
 *     summary: Update journey step map
 *     tags: [Journeys]
 *     parameters:
 *       - in: path
 *         name: journeyId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Journey ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             additionalProperties:
 *               $ref: '#/components/schemas/JourneyStep'
 *     responses:
 *       200:
 *         description: Updated journey step map
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               additionalProperties:
 *                 $ref: '#/components/schemas/JourneyStep'
 */
router.put("/:journeyId/steps", async (ctx) => {
  const { steps, children } = await setJourneyStepMap(
    ctx.state.journey!.id,
    validate(journeyStepsParamsSchema, ctx.request.body)
  );
  ctx.body = await toJourneyStepMap(steps, children);
});

/**
 * @swagger
 * /automations/{journeyId}/entrances:
 *   get:
 *     summary: Get paged entrances for a journey
 *     tags: [Journeys]
 *     parameters:
 *       - in: path
 *         name: journeyId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Journey ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of entrances
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 items:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/JourneyEntrance'
 *                 total:
 *                   type: integer
 */
router.get("/:journeyId/entrances", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await pagedEntrancesByJourney(ctx.state.journey!.id, params);
});

/**
 * @swagger
 * /automations/{journeyId}/entrances/{entranceId}/users/{userId}:
 *   delete:
 *     summary: Exit user from journey entrance
 *     tags: [Journeys]
 *     parameters:
 *       - in: path
 *         name: journeyId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Journey ID
 *       - in: path
 *         name: entranceId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Entrance ID
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: User exited successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 exits:
 *                   type: integer
 *       404:
 *         description: User not found
 */
router.delete(
  "/:journeyId/entrances/:entranceId/users/:userId",
  async (ctx) => {
    const user = await getUserFromContext(ctx);
    if (!user) return ctx.throw(404);
    const results = await exitUserFromJourney(
      user.id,
      parseInt(ctx.params.entranceId),
      ctx.state.journey!.id
    );
    ctx.body = { exits: results };
  }
);

/**
 * @swagger
 * /automations/{journeyId}/steps/{stepId}/users:
 *   get:
 *     summary: Get paged users in a journey step
 *     tags: [Journeys]
 *     parameters:
 *       - in: path
 *         name: journeyId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Journey ID
 *       - in: path
 *         name: stepId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Step ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of users in step
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 items:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *                 total:
 *                   type: integer
 *       404:
 *         description: Step not found
 */
router.get("/:journeyId/steps/:stepId/users", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  const step = await JourneyStep.first((q) =>
    q
      .where("journey_id", ctx.state.journey!.id)
      .where("id", parseInt(ctx.params.stepId))
  );
  if (!step) return ctx.throw(404);
  ctx.body = await pagedUsersByStep(step.id, params);
});

/**
 * @swagger
 * /automations/{journeyId}/users/{userId}:
 *   delete:
 *     summary: Exit user from journey
 *     tags: [Journeys]
 *     parameters:
 *       - in: path
 *         name: journeyId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Journey ID
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *     responses:
 *       200:
 *         description: User exited successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 exits:
 *                   type: integer
 *       404:
 *         description: User not found
 */
router.delete("/:journeyId/users/:userId", async (ctx) => {
  const user = await getUserFromContext(ctx);
  if (!user) return ctx.throw(404);
  const results = await JourneyUserStep.update(
    (q) =>
      q
        .where("user_id", user.id)
        .whereNull("entrance_id")
        .whereNull("ended_at")
        .where("journey_id", ctx.state.journey!.id),
    { ended_at: new Date() }
  );
  ctx.body = { exits: results };
});

const journeyTriggerParams: JSONSchemaType<JourneyEntranceTriggerParams> = {
  $id: "journeyEntranceTriggerParams",
  type: "object",
  required: ["entrance_id", "user"],
  properties: {
    entrance_id: {
      type: "number",
      minimum: 1,
    },
    user: {
      type: "object",
      required: ["external_id"],
      properties: {
        external_id: { type: "string" },
        email: { type: "string", nullable: true },
        phone: { type: "string", nullable: true },
        device_token: { type: "string", nullable: true },
        timezone: { type: "string", nullable: true },
        locale: { type: "string", nullable: true },
      },
      additionalProperties: true,
    },
    event: {
      type: "object",
      additionalProperties: true,
      nullable: true,
    },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /automations/{journeyId}/trigger:
 *   post:
 *     summary: Manually trigger a journey entrance
 *     tags: [Journeys]
 *     parameters:
 *       - in: path
 *         name: journeyId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Journey ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/JourneyTriggerRequest'
 *     responses:
 *       200:
 *         description: Journey triggered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 */
router.post("/:journeyId/trigger", async (ctx) => {
  const journey = ctx.state.journey!;
  const payload = validate(journeyTriggerParams, ctx.request.body);

  await triggerEntrance(journey, payload);
  ctx.body = { success: true };
});

/**
 * @swagger
 * /automations/suggestions:
 *   get:
 *     summary: Get AI-generated journey suggestions
 *     tags: [Journeys]
 *     responses:
 *       200:
 *         description: List of journey suggestions
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/JourneySuggestion'
 */
router.get("/suggestions", async (ctx) => {
  const locationId = ctx.state.location.id;
  const model = new OpenAI({
    modelName: "gpt-4.1-mini",
    temperature: 0.7,
    openAIApiKey: process.env.OPENAI_API_KEY,
  });

  const [posData, customerMetrics] = await Promise.all([
    PosData.query().where("location_id", locationId).select("*").limit(100),
    // Get customer metrics
    PosData.query()
      .where("location_id", locationId)
      .select([
        PosData.raw("COUNT(DISTINCT customer_name) as total_customers"),
        PosData.raw(
          "COUNT(DISTINCT CASE WHEN customer_type = 'new' THEN customer_name END) as new_customers"
        ),
        PosData.raw(
          "COUNT(DISTINCT CASE WHEN customer_type = 'returning' THEN customer_name END) as returning_customers"
        ),
        PosData.raw("AVG(net_sales) as average_order_value"),
      ])
      .first(),
  ]);

  const prompt = `
    As a marketing automation expert, analyze this customer data and suggest 3 automated customer journeys.
    ${JSON.stringify({ posData, customerMetrics }, null, 2)}

    Each journey MUST follow this structure:
    1. Start with an "entrance" step (using schedule or event trigger)
    2. Include relevant middle steps (action, delay, update, etc)
    3. End with an "exit" step
    4. All steps must be properly connected via children arrays

    Return a JSON array of journeys. Example format:
    [{
      "name": "Weekly VIP Customer Engagement",
      "description": "Engage high-value customers with weekly personalized offers",
      "tags": ["vip", "engagement"],
      "published": false,
      "steps": {
        "entrance-id": {
          "type": "entrance",
          "name": "Weekly Trigger",
          "data": {
            "trigger": "schedule",
            "list_id": 1,
            "schedule": "DTSTART:20250101T000000Z\\nRRULE:FREQ=WEEKLY"
          },
          "x": 0,
          "y": 0,
          "children": [
            { "external_id": "action-id" }
          ]
        },
        "action-id": {
          "type": "action",
          "name": "Send VIP Offer",
          "data": {
            "campaign_id": 1
          },
          "x": 200,
          "y": 0,
          "children": [
            { "external_id": "exit-id" }
          ]
        },
        "exit-id": {
          "type": "exit",
          "name": "Complete Flow",
          "data": {
            "entranceExternalId": "entrance-id"
          },
          "x": 400,
          "y": 0,
          "children": []
        }
      }
    }]

    Valid step types are: entrance, exit, action, delay, gate, experiment, update, event
    Each step must have: type, name, data (appropriate for type), x, y coordinates, and children array
  `;

  const suggestions = JSON.parse(await model.call(prompt));
  ctx.body = suggestions;
});

const journeyCreateSchema: JSONSchemaType<
  JourneyParams & { steps?: JourneyStepMapParams }
> = {
  $id: "journeyCreateSchema",
  type: "object",
  required: ["name"],
  properties: {
    name: {
      type: "string",
    },
    description: {
      type: "string",
      nullable: true,
    },
    tags: {
      type: "array",
      items: {
        type: "string",
      },
      nullable: true,
    },
    published: {
      type: "boolean",
    },
    steps: {
      type: "object",
      required: [],
      nullable: true,
      additionalProperties: {
        type: "object",
        required: ["type", "x", "y"],
        properties: {
          type: {
            type: "string",
            enum: Object.keys(journeyStepTypes),
          },
          name: {
            type: "string",
            nullable: true,
          },
          data: {
            type: "object",
            nullable: true,
            additionalProperties: true,
          },
          data_key: {
            type: "string",
            nullable: true,
          },
          x: {
            type: "number",
          },
          y: {
            type: "number",
          },
          children: {
            type: "array",
            nullable: true,
            items: {
              type: "object",
              required: ["external_id"],
              properties: {
                external_id: {
                  type: "string",
                },
                path: {
                  type: "string",
                  nullable: true,
                },
                data: {
                  type: "object",
                  nullable: true,
                  additionalProperties: true,
                },
              },
            },
          },
        },
        additionalProperties: false,
      },
    },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /automations/create:
 *   post:
 *     summary: Create a new journey with steps
 *     tags: [Journeys]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [name]
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               published:
 *                 type: boolean
 *               steps:
 *                 type: object
 *                 additionalProperties:
 *                   $ref: '#/components/schemas/JourneyStep'
 *     responses:
 *       200:
 *         description: Created journey
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Journey'
 */
router.post("/create", async (ctx) => {
  const { steps, ...journeyData } = validate(
    journeyCreateSchema,
    ctx.request.body
  );

  const journey = await createJourney(ctx.state.location.id, journeyData);
  if (steps) {
    const { steps: journeySteps } = await setJourneyStepMap(journey.id, steps);
  }

  ctx.body = journey;
});

export default router;
