import Organization from "../../organizations/Organization";
import Location from "../../locations/Location";
import { User } from "../../users/User";
import { UserEvent } from "../../users/UserEvent";
import Journey from "../Journey";
import { JourneyStepMap } from "../JourneyStep";

export const setupLocation = async () => {
  const org = await Organization.insertAndFetch({});
  const location = await Location.insertAndFetch({
    organization_id: org.id,
    name: `Location ${Date.now()}`,
  });
  return { org, location };
};

interface SetupJourneyParams {
  data: Record<string, unknown>;
  events?: Array<{
    name: string;
    data: Record<string, unknown>;
  }>;
  stepMap: JourneyStepMap;
}

export const setupTestJourney = async ({
  data,
  events,
  stepMap,
}: SetupJourneyParams) => {
  const { location } = await setupLocation();

  const { journey, steps } = await Journey.create(
    location.id,
    "Test Journey",
    stepMap
  );

  const user = await User.insertAndFetch({
    location_id: location.id,
    external_id: Date.now().toString(),
    data,
    timezone: "America/Chicago",
  });

  if (events?.length) {
    await UserEvent.insert(
      events.map(({ name, data }) => ({
        location_id: location.id,
        user_id: user.id,
        name,
        data,
      }))
    );
  }

  return { journey, location, steps, user };
};
