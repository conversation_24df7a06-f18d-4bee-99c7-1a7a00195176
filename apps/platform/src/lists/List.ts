import Model from "../core/Model";
import { RuleTree } from "../rules/Rule";

export type ListState = "draft" | "ready" | "loading";
type ListType = "static" | "dynamic";

export default class List extends Model {
  location_id!: number;
  name!: string;
  type!: ListType;
  description?: string;
  state!: ListState;
  rule_id?: number;
  rule?: RuleTree;
  version!: number;
  users_count?: number;
  tags?: string[];
  is_visible!: boolean;
  deleted_at?: Date;
}

export type DynamicList = List & { rule: RuleTree };

export class UserList extends Model {
  user_id!: number;
  list_id!: number;
  event_id!: number;
  version!: number;
  deleted_at?: Date;

  static tableName = "user_list";
}

export type ListUpdateParams = Pick<List, "name" | "tags" | "description"> & {
  rule?: RuleTree;
  published?: boolean;
};
export type ListCreateParams = ListUpdateParams &
  Pick<List, "type" | "is_visible"> & {
    rule?: RuleTree;
    user_ids?: number[];
  };
