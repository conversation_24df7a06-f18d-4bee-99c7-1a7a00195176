import Router from "@koa/router";
import { JSONSchemaType, validate } from "../core/validate";
import { extractQueryParams } from "../utilities";
import List, { ListCreateParams, ListUpdateParams, UserList } from "./List";
import {
  archiveList,
  createList,
  deleteList,
  getList,
  getListUsers,
  importUsersToList,
  pagedLists,
  updateList,
} from "./ListService";
import { searchParamsSchema } from "../core/searchParams";
import { LocationState } from "../auth/AuthMiddleware";
import parse from "../storage/FileStream";
import { locationRoleMiddleware } from "../locations/LocationService";
import { generateListSuggestions } from "../insights/InsightService";

/**
 * @swagger
 * components:
 *   schemas:
 *     Rule:
 *       type: object
 *       required: [uuid, type, group, path, operator]
 *       properties:
 *         id:
 *           type: number
 *           nullable: true
 *         uuid:
 *           type: string
 *         root_uuid:
 *           type: string
 *           nullable: true
 *         parent_uuid:
 *           type: string
 *           nullable: true
 *         type:
 *           type: string
 *           enum: [wrapper, string, number, boolean, date, array]
 *         group:
 *           type: string
 *           enum: [user, event, parent, pos]
 *         path:
 *           type: string
 *         operator:
 *           type: string
 *         value:
 *           oneOf:
 *             - type: string
 *             - type: number
 *             - type: boolean
 *           nullable: true
 *         children:
 *           type: array
 *           nullable: true
 *           items:
 *             $ref: '#/components/schemas/Rule'
 *
 *     List:
 *       type: object
 *       required: [id, name, type, location_id]
 *       properties:
 *         id:
 *           type: number
 *         name:
 *           type: string
 *         type:
 *           type: string
 *           enum: [static, dynamic]
 *         description:
 *           type: string
 *           nullable: true
 *         location_id:
 *           type: number
 *         rule:
 *           $ref: '#/components/schemas/Rule'
 *           nullable: true
 *         tags:
 *           type: array
 *           items:
 *             type: string
 *           nullable: true
 *         is_visible:
 *           type: boolean
 *           nullable: true
 *         published:
 *           type: boolean
 *           nullable: true
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *         deleted_at:
 *           type: string
 *           format: date-time
 *           nullable: true
 *
 *     ListSuggestion:
 *       type: object
 *       required: [name, description, rule]
 *       properties:
 *         name:
 *           type: string
 *         description:
 *           type: string
 *         rule:
 *           $ref: '#/components/schemas/Rule'
 *         confidence:
 *           type: number
 *           format: float
 *           minimum: 0
 *           maximum: 1
 *
 *     User:
 *       type: object
 *       required: [id, email]
 *       properties:
 *         id:
 *           type: number
 *         email:
 *           type: string
 *           format: email
 *         first_name:
 *           type: string
 *           nullable: true
 *         last_name:
 *           type: string
 *           nullable: true
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

/**
 * @swagger
 * tags:
 *   name: Lists
 *   description: List management endpoints
 */

const router = new Router<LocationState & { list?: List }>({
  prefix: "/lists",
});

router.use(locationRoleMiddleware("editor"));

/**
 * @swagger
 * /lists:
 *   get:
 *     summary: Get paged lists
 *     tags: [Lists]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of lists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 items:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/List'
 *                 total:
 *                   type: integer
 */
router.get("/", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await pagedLists(params, ctx.state.location.id);
});

const ruleDefinition = (nullable = false) => ({
  type: "object",
  required: ["uuid", "type", "group", "path", "operator"],
  properties: {
    id: {
      type: "number",
      nullable: true,
    },
    uuid: { type: "string" },
    root_uuid: {
      type: "string",
      nullable: true,
    },
    parent_uuid: {
      type: "string",
      nullable: true,
    },
    type: {
      type: "string",
      enum: ["wrapper", "string", "number", "boolean", "date", "array"],
    },
    group: { type: "string", enum: ["user", "event", "parent", "pos"] },
    path: { type: "string" },
    operator: { type: "string" },
    value: {
      type: ["string", "number", "boolean"],
      nullable: true,
    },
    children: {
      type: "array",
      nullable: true,
      minItems: 0,
      items: {
        $ref: "#/definitions/rule",
      } as any,
    },
  },
  additionalProperties: false,
  nullable,
});

const listParams: JSONSchemaType<ListCreateParams> = {
  $id: "listParams",
  definitions: {
    rule: ruleDefinition() as any,
  },
  oneOf: [
    {
      type: "object",
      required: ["name", "type", "rule"],
      properties: {
        name: {
          type: "string",
        },
        type: {
          type: "string",
          enum: ["dynamic"],
        },
        rule: { $ref: "#/definitions/rule" } as any,
        tags: {
          type: "array",
          items: {
            type: "string",
          },
          nullable: true,
        },
        is_visible: {
          type: "boolean",
          nullable: true,
        },
      },
      additionalProperties: false,
    },
    {
      type: "object",
      required: ["name", "type"],
      properties: {
        name: {
          type: "string",
        },
        type: {
          type: "string",
          enum: ["static"],
        },
        tags: {
          type: "array",
          items: {
            type: "string",
          },
          nullable: true,
        },
        is_visible: {
          type: "boolean",
          nullable: true,
        },
        user_ids: {
          type: "array",
          items: { type: "number" },
          nullable: true,
        },
      },
      additionalProperties: false,
    },
  ] as any,
};

/**
 * @swagger
 * /lists:
 *   post:
 *     summary: Create a new list
 *     tags: [Lists]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - type: object
 *                 required: [name, type, rule]
 *                 properties:
 *                   name:
 *                     type: string
 *                   type:
 *                     type: string
 *                     enum: [dynamic]
 *                   rule:
 *                     $ref: '#/components/schemas/Rule'
 *                   tags:
 *                     type: array
 *                     items:
 *                       type: string
 *                   is_visible:
 *                     type: boolean
 *               - type: object
 *                 required: [name, type]
 *                 properties:
 *                   name:
 *                     type: string
 *                   type:
 *                     type: string
 *                     enum: [static]
 *                   tags:
 *                     type: array
 *                     items:
 *                       type: string
 *                   is_visible:
 *                     type: boolean
 *                   user_ids:
 *                     type: array
 *                     items:
 *                       type: integer
 *     responses:
 *       200:
 *         description: Created list
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/List'
 */
router.post("/", async (ctx) => {
  const payload = validate(listParams, ctx.request.body);
  const list = await createList(ctx.state.location.id, payload);

  if (payload.type === "static" && payload.user_ids?.length) {
    await Promise.all(
      payload.user_ids.map((userId) =>
        UserList.query()
          .insert({
            user_id: userId,
            list_id: list.id,
            version: 1,
          })
          .catch((err) => console.error(`Failed to add user ${userId}:`, err))
      )
    );
  }

  ctx.body = list;
});

router.param("listId", async (value, ctx, next) => {
  ctx.state.list = await getList(parseInt(value, 10), ctx.state.location.id);
  if (!ctx.state.list) {
    ctx.throw(404);
    return;
  }
  return await next();
});

/**
 * @swagger
 * /lists/{listId}:
 *   get:
 *     summary: Get a specific list
 *     tags: [Lists]
 *     parameters:
 *       - in: path
 *         name: listId
 *         required: true
 *         schema:
 *           type: integer
 *         description: List ID
 *     responses:
 *       200:
 *         description: List details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/List'
 *       404:
 *         description: List not found
 */
router.get("/:listId", async (ctx) => {
  ctx.body = ctx.state.list;
});

const listUpdateParams: JSONSchemaType<ListUpdateParams> = {
  $id: "listUpdateParams",
  definitions: {
    rule: ruleDefinition(true) as any,
  },
  type: "object",
  required: ["name"],
  properties: {
    name: {
      type: "string",
    },
    description: {
      type: "string",
      nullable: true,
    },
    rule: { $ref: "#/definitions/rule" } as any,
    tags: {
      type: "array",
      items: {
        type: "string",
      },
      nullable: true,
    },
    published: {
      type: "boolean",
      nullable: true,
    },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /lists/{listId}:
 *   patch:
 *     summary: Update a list
 *     tags: [Lists]
 *     parameters:
 *       - in: path
 *         name: listId
 *         required: true
 *         schema:
 *           type: integer
 *         description: List ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [name]
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               rule:
 *                 $ref: '#/components/schemas/Rule'
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *               published:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Updated list
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/List'
 *       404:
 *         description: List not found
 */
router.patch("/:listId", async (ctx) => {
  const payload = validate(listUpdateParams, ctx.request.body);
  ctx.body = await updateList(ctx.state.list!, payload);
});

/**
 * @swagger
 * /lists/{listId}:
 *   delete:
 *     summary: Delete or archive a list
 *     tags: [Lists]
 *     parameters:
 *       - in: path
 *         name: listId
 *         required: true
 *         schema:
 *           type: integer
 *         description: List ID
 *     responses:
 *       200:
 *         description: List deleted/archived successfully
 *       404:
 *         description: List not found
 */
router.delete("/:listId", async (ctx) => {
  const { id, location_id, deleted_at } = ctx.state.list!;
  if (deleted_at) {
    await deleteList(id, location_id);
  } else {
    await archiveList(id, location_id);
  }
  ctx.body = true;
});

/**
 * @swagger
 * /lists/{listId}/users:
 *   get:
 *     summary: Get users in a list
 *     tags: [Lists]
 *     parameters:
 *       - in: path
 *         name: listId
 *         required: true
 *         schema:
 *           type: integer
 *         description: List ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of users
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 items:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *                 total:
 *                   type: integer
 *       404:
 *         description: List not found
 */
router.get("/:listId/users", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await getListUsers(
    ctx.state.list!.id,
    params,
    ctx.state.location.id
  );
});

/**
 * @swagger
 * /lists/{listId}/users:
 *   post:
 *     summary: Import users to a list
 *     tags: [Lists]
 *     parameters:
 *       - in: path
 *         name: listId
 *         required: true
 *         schema:
 *           type: integer
 *         description: List ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *     responses:
 *       204:
 *         description: Users imported successfully
 *       404:
 *         description: List not found
 */
router.post("/:listId/users", async (ctx) => {
  const stream = await parse(ctx);
  await importUsersToList(ctx.state.list!, stream);
  ctx.status = 204;
});

/**
 * @swagger
 * /lists/suggestions:
 *   post:
 *     summary: Generate list suggestions
 *     tags: [Lists]
 *     responses:
 *       200:
 *         description: List suggestions generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/ListSuggestion'
 *       500:
 *         description: Error generating suggestions
 */
router.post("/suggestions", locationRoleMiddleware("editor"), async (ctx) => {
  const locationId = ctx.state.location.id;
  try {
    const suggestions = await generateListSuggestions(locationId);
    ctx.body = suggestions;
  } catch (error: any) {
    console.error("Error generating list suggestions:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to generate list suggestions",
      details:
        process.env.NODE_ENV === "development" ? error.message : undefined,
    };
  }
});

/**
 * @swagger
 * /lists/{listId}/bulk-add-users:
 *   post:
 *     summary: Bulk add users to a list
 *     tags: [Lists]
 *     parameters:
 *       - in: path
 *         name: listId
 *         required: true
 *         schema:
 *           type: integer
 *         description: List ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [user_ids]
 *             properties:
 *               user_ids:
 *                 type: array
 *                 items:
 *                   type: integer
 *     responses:
 *       204:
 *         description: Users added successfully
 *       400:
 *         description: Invalid request body
 *       404:
 *         description: List not found
 */
router.post("/:listId/bulk-add-users", async (ctx) => {
  const { user_ids } = ctx.request.body;

  if (!Array.isArray(user_ids)) {
    ctx.throw(400, "user_ids must be an array of numbers");
    return;
  }

  await Promise.all(
    user_ids.map((userId) =>
      UserList.query()
        .insert({
          user_id: userId,
          list_id: ctx.state.list!.id,
          version: 1,
        })
        .catch((err) => console.error(`Failed to add user ${userId}:`, err))
    )
  );

  ctx.status = 204;
});

export default router;
