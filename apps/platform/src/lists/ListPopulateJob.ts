import { Job } from "../queue";
import { DynamicList } from "./List";
import { getList, populateList } from "./ListService";
import ListStatsJob from "./ListStatsJob";

interface ListPopulateParams {
  listId: number;
  locationId: number;
}

export default class ListPopulateJob extends Job {
  static $name = "list_populate_job";

  static from(listId: number, locationId: number): ListPopulateJob {
    return new this({ listId, locationId });
  }

  static async handler({ listId, locationId }: ListPopulateParams) {
    const list = (await getList(listId, locationId)) as DynamicList;
    if (!list) return;

    await populateList(list);

    await ListStatsJob.from(listId, locationId, true).queue();
  }
}
