import { UserEvent } from "../users/UserEvent";
import { User } from "../users/User";
import { check } from "../rules/RuleEngine";
import List, {
  DynamicList,
  ListCreateParams,
  ListUpdateParams,
  UserList,
} from "./List";
import Rule, { RuleEvaluation, RuleTree } from "../rules/Rule";
import { PageParams } from "../core/searchParams";
import ListPopulateJob from "./ListPopulateJob";
import { importUsers } from "../users/UserImport";
import { FileStream } from "../storage/FileStream";
import { createTagSubquery, getTags, setTags } from "../tags/TagService";
import { Chunker, visit } from "../utilities";
import { getUserEventsForRules } from "../users/UserRepository";
import {
  RuleResults,
  RuleWithEvaluationResult,
  checkRules,
  decompileRule,
  fetchAndCompileRule,
  mergeInsertRules,
} from "../rules/RuleService";
import { updateCampaignSendEnrollment } from "../campaigns/CampaignService";
import { cacheDecr, cacheIncr } from "../config/redis";
import App from "../app";
import { PosData } from "../pos/PosData";

export const pagedLists = async (params: PageParams, locationId: number) => {
  const result = await List.search({ ...params, fields: ["name"] }, (b) => {
    b = b
      .where("location_id", locationId)
      .whereNull("deleted_at")
      .where("is_visible", true);
    params.tag?.length &&
      b.whereIn("id", createTagSubquery(List, locationId, params.tag));
    return b;
  });
  if (result.results?.length) {
    const tags = await getTags(
      List.tableName,
      result.results.map((l) => l.id)
    );
    for (const list of result.results) {
      list.tags = tags.get(list.id);
    }
  }
  return result;
};

export const allLists = async (locationId: number, listIds?: number[]) => {
  const lists = await List.all((qb) => {
    qb.where("location_id", locationId);
    if (listIds) {
      qb.whereIn("id", listIds);
    }
    return qb;
  });

  if (lists.length) {
    const tags = await getTags(
      List.tableName,
      lists.map((l) => l.id)
    );
    for (const list of lists) {
      list.tags = tags.get(list.id);
    }
  }

  return lists;
};

export const getList = async (id: number, locationId: number) => {
  const list = await List.find(id, (qb) => qb.where("location_id", locationId));
  if (list) {
    list.tags = await getTags(List.tableName, [list.id]).then((m) =>
      m.get(list.id)
    );
    if (list.rule_id) list.rule = await fetchAndCompileRule(list.rule_id);
  }
  return list;
};

export const getListUsers = async (
  id: number,
  params: PageParams,
  locationId: number
) => {
  return await User.search(
    { ...params, fields: ["email", "phone"], mode: "exact" },
    (b) =>
      b
        .rightJoin("user_list", "user_list.user_id", "users.id")
        .where("location_id", locationId)
        .where("list_id", id)
        .select("users.*", "user_list.created_at")
  );
};

export const getUserLists = async (
  id: number,
  params: PageParams,
  locationId: number
) => {
  return await List.search(params, (b) =>
    b
      .rightJoin("user_list", "user_list.list_id", "lists.id")
      .where("location_id", locationId)
      .where("user_id", id)
      .select("lists.*")
  );
};

export const createList = async (
  locationId: number,
  { tags, name, type, rule }: ListCreateParams
): Promise<List> => {
  const list = await List.insertAndFetch({
    name,
    type,
    state: type === "dynamic" ? "draft" : "ready",
    users_count: 0,
    location_id: locationId,
  });

  if (tags?.length) {
    await setTags({
      location_id: locationId,
      entity: List.tableName,
      entity_id: list.id,
      names: tags,
    });
  }

  if (rule && list.type === "dynamic") {
    // Decompile rule into separate flat parts
    const [wrapper, ...rules] = decompileRule(rule, {
      location_id: locationId,
    });

    // Insert top level wrapper to get ID to associate
    list.rule_id = await Rule.insert(wrapper);
    await List.update((qb) => qb.where("id", list.id), {
      rule_id: list.rule_id,
    });

    // Insert rest of rules
    if (rules && rules.length) {
      await Rule.insert(rules);

      // If we have additional rules, populate
      await ListPopulateJob.from(list.id, list.location_id).queue();
    }
  }

  return list;
};

export const updateList = async (
  list: List,
  { tags, rule, published, ...params }: ListUpdateParams
): Promise<List | undefined> => {
  list = await List.updateAndFetch(list.id, {
    ...params,
    state:
      list.state === "draft" ? (published ? "ready" : "draft") : list.state,
  });

  if (tags) {
    await setTags({
      location_id: list.location_id,
      entity: List.tableName,
      entity_id: list.id,
      names: tags,
    });
  }

  if (rule && list.type === "dynamic") {
    const rules = decompileRule(rule, { location_id: list.location_id });
    await mergeInsertRules(rules);
    await ListPopulateJob.from(list.id, list.location_id).queue();
  }

  return await getList(list.id, list.location_id);
};

export const archiveList = async (id: number, locationId: number) => {
  await List.archive(id, (qb) => qb.where("location_id", locationId));
  return getList(id, locationId);
};

export const deleteList = async (id: number, locationId: number) => {
  return await List.deleteById(id, (qb) => qb.where("location_id", locationId));
};

export const countKey = (list: List) => `list:${list.id}:${list.version}:count`;

export const addUserToList = async (
  user: User | number,
  list: List,
  event?: UserEvent
) => {
  const userId = user instanceof User ? user.id : user;
  const resp = await UserList.query()
    .insert({
      user_id: userId,
      list_id: list.id,
      event_id: event?.id ?? undefined,
      version: list.version,
    })
    .onConflict(["user_id", "list_id"])
    .ignore();
  if (resp?.[0]) await cacheIncr(App.main.redis, countKey(list));
  return resp;
};

export const removeUserFromList = async (user: User | number, list: List) => {
  const userId = user instanceof User ? user.id : user;
  const count = await UserList.delete((qb) =>
    qb.where("user_id", userId).where("list_id", list.id)
  );
  if (count) await cacheDecr(App.main.redis, countKey(list));
  return count;
};

export const importUsersToList = async (list: List, stream: FileStream) => {
  await updateListState(list.id, { state: "loading" });

  try {
    await importUsers({
      location_id: list.location_id,
      list_id: list!.id,
      stream,
    });
  } finally {
    await updateListState(list.id, { state: "ready" });
  }

  await updateListState(list.id, { state: "ready" });
};

export const populateList = async (list: List) => {
  const { id, version: oldVersion = 0, rule_id } = list;
  const version = oldVersion + 1;
  await updateListState(id, { state: "loading", version });

  if (!rule_id) return;

  const rule = (await fetchAndCompileRule(rule_id)) as RuleTree;

  // Collect matching user ids, insert in batches of 100
  const userChunker = new Chunker<number>(async (userIds) => {
    await UserList.query()
      .insert(
        userIds.map((user_id) => ({
          list_id: list.id,
          user_id,
          version,
        }))
      )
      .onConflict(["user_id", "list_id"])
      .merge(["version"]);
  }, 100);

  // Collect rule evaluations, insert in batches of 100
  const ruleChunker = new Chunker<Partial<RuleEvaluation>>(async (items) => {
    await RuleEvaluation.query()
      .insert(
        items.map(({ user_id, rule_id, result }) => ({
          user_id,
          rule_id,
          result,
        }))
      )
      .onConflict(["user_id", "rule_id"])
      .merge(["result"]);
  }, 100);

  // Fetch all users and iterate over them
  const scroll = User.scroll((q) => q.where("location_id", list.location_id));

  const eventRules: RuleTree[] = [];
  const userRules: RuleTree[] = [];
  const posRules: RuleTree[] = [];
  visit(
    rule,
    (r) => r.children,
    (r) => {
      if (r.id === rule.id) return;
      if (r.type === "wrapper" && r.group === "event") {
        eventRules.push(r);
      } else if (r.group === "user") {
        userRules.push(r);
      } else if (r.group === "pos") {
        posRules.push(r);
      }
    }
  );

  for await (const users of scroll) {
    // For each user, evaluate parts and batch enqueue
    for (const user of users) {
      const parts: RuleWithEvaluationResult[] = [];
      const events = await getUserEventsForRules([user.id], eventRules);

      // Fetch POS data for user if we have POS rules
      let posData: PosData[] = [];
      if (posRules.length > 0) {
        posData = await PosData.query()
          .where("location_id", list.location_id)
          .where("customer_name", user.external_id)
          .select("*")
          .then((results) => (Array.isArray(results) ? results : []));
      }

      for (const rule of eventRules) {
        const result = check(
          {
            user: user.flatten(),
            events: events.map((e) => e.flatten()),
          },
          rule
        );
        await ruleChunker.add({
          rule_id: rule.id,
          user_id: user.id,
          result,
        });
        parts.push({
          ...rule,
          result,
        });
      }

      // Evaluate POS rules
      for (const rule of posRules) {
        const result = check(
          {
            user: user.flatten(),
            pos: posData.map((p) => p.flatten()),
            events: [],
          },
          rule
        );
        await ruleChunker.add({
          rule_id: rule.id,
          user_id: user.id,
          result,
        });
        parts.push({
          ...rule,
          result,
        });
      }

      const result = checkRules(user, rule, [...parts, ...userRules]);
      if (result) {
        await userChunker.add(user.id);
      }
    }
  }

  // Insert any remaining users
  await ruleChunker.flush();
  await userChunker.flush();

  // Once list is regenerated, drop any users from previous version
  await UserList.delete((qb) =>
    qb.where("version", "<", version).where("list_id", list.id)
  );

  // Update list status to ready
  await updateListState(id, { state: "ready" });
};

export const isUserInList = async (user_id: number, list_id: number) => {
  return await UserList.exists((qb) =>
    qb.where("user_id", user_id).where("list_id", list_id)
  );
};

export const updateUsersLists = async (
  user: User,
  results: RuleResults,
  event?: UserEvent
) => {
  const dirtyLists = new Set<number>();
  if (results.success.length) {
    const successLists = await listsForRule(results.success, user.location_id);
    for (const list of successLists) {
      await addUserToList(user, list, event);
      dirtyLists.add(list.id);
    }
  }

  if (results.failure.length) {
    const failureLists = await listsForRule(results.failure, user.location_id);
    for (const list of failureLists) {
      await removeUserFromList(user, list);
      dirtyLists.add(list.id);
    }
  }

  // If any lists were updated for the user, check associated campaigns
  // to see if send list needs to be updated
  if (dirtyLists.size > 0) {
    await updateCampaignSendEnrollment(user);
  }
};

export const listsForRule = async (
  ruleUuids: string[],
  locationId: number
): Promise<DynamicList[]> => {
  return (await List.all((qb) =>
    qb
      .leftJoin("rules", "rules.id", "lists.rule_id")
      .where("lists.location_id", locationId)
      .where("rules.location_id", locationId)
      .where("lists.type", "dynamic")
      .whereNot("lists.state", "draft")
      .whereNull("deleted_at")
      .whereIn("rules.uuid", ruleUuids)
  )) as DynamicList[];
};

interface CountRange {
  sinceDate?: Date;
  sinceId?: number;
  untilId?: number;
}

export const listUserCount = async (
  listId: number,
  since?: CountRange
): Promise<number> => {
  return await UserList.count((qb) => {
    qb.where("list_id", listId);
    if (since && since.sinceDate) {
      qb.where("created_at", ">=", since.sinceDate);
      if (since.sinceId && since.untilId) {
        qb.where("id", ">", since.sinceId).where("id", "<=", since.untilId);
      }
    }
    return qb;
  });
};

export const updateListState = async (
  id: number,
  params: Partial<Pick<List, "state" | "version" | "users_count">>
) => {
  return await List.updateAndFetch(id, params);
};
