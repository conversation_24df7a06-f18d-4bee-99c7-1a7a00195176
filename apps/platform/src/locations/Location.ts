import Model, { ModelParams } from "../core/Model";

export interface SocialMediaAccount {
  connected: boolean;
  accessToken: string;
  email: string;
}

export interface SocialMediaAccounts {
  instagram?: SocialMediaAccount;
  facebook?: SocialMediaAccount;
  twitter?: SocialMediaAccount;
  linkedin?: SocialMediaAccount;
}

export interface DataSource {
  name: string;
  id: string;
  available: boolean;
  connected?: boolean;
  apiKey?: string;
  siteId?: string;
}

export interface Competitor {
  id: string;
  name: string;
  website?: string;
  address?: string;
  distance?: number;
}

export interface CommunicationSettings {
  senderEmail: string;
  senderName: string;
  contactEmail: string;
  contactPhone: string;
}

export interface SocialPlatform {
  id: string;
  name: string;
  icon: string;
  connectedAccount: string | null;
  available: boolean;
  accessToken: string | null;
}

export default class Location extends Model {
  organization_id!: number;
  name!: string;
  description?: string;
  website?: string;
  facebook?: string;
  twitter?: string;
  instagram?: string;
  linkedin?: string;
  deleted_at?: Date;
  locale!: string;
  timezone!: string;
  text_opt_out_message?: string;
  text_help_message?: string;
  link_wrap_email?: boolean;
  link_wrap_push?: boolean;
  sender_email?: string;
  sender_name?: string;
  address?: string;
  city?: string;
  state?: string;
  zip?: string;
  phone?: string;
  country?: string;
  data?: Record<string, any>;
  longitude?: number;
  latitude?: number;
  retailer_id?: string;
  social_media_accounts?: SocialMediaAccounts;
  dataSources?: DataSource[];
  competitors?: Competitor[];
  documents?: any[];
  socialPlatforms?: SocialPlatform[];
  communicationSettings?: CommunicationSettings;
}

export type LocationParams = Omit<
  Location,
  ModelParams | "deleted_at" | "organization_id"
>;

export const locationRoles = ["support", "editor", "admin"] as const;

export type LocationRole = (typeof locationRoles)[number];
