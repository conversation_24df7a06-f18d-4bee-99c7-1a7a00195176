/**
 * @swagger
 * components:
 *   schemas:
 *     LocationAdmin:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Admin ID
 *         location_id:
 *           type: integer
 *           description: ID of the location
 *         admin_id:
 *           type: integer
 *           description: ID of the admin user
 *         role:
 *           type: string
 *           description: Role assigned to the admin in this location
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *         admin:
 *           type: object
 *           properties:
 *             id:
 *               type: integer
 *               description: Admin user ID
 *             email:
 *               type: string
 *               format: email
 *               description: Admin email address
 *             name:
 *               type: string
 *               description: Admin's full name
 *             role:
 *               type: string
 *               description: Admin's global role
 *     LocationAdminParams:
 *       type: object
 *       required: [role]
 *       properties:
 *         role:
 *           type: string
 *           enum: [admin, manager, staff]
 *           description: Role to assign to the admin
 *     LocationAdminCreateParams:
 *       type: object
 *       required: [role, email]
 *       properties:
 *         role:
 *           type: string
 *           enum: [admin, manager, staff]
 *           description: Role to assign to the admin
 *         email:
 *           type: string
 *           format: email
 *           description: Email address of the admin to add
 *     LocationAdminListResponse:
 *       type: object
 *       properties:
 *         data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/LocationAdmin'
 *         total:
 *           type: integer
 *           description: Total number of admins
 *         page:
 *           type: integer
 *           description: Current page number
 *         per_page:
 *           type: integer
 *           description: Number of items per page
 */

/**
 * @swagger
 * tags:
 *   name: Location Admins
 *   description: Endpoints for managing location administrators
 */

/**
 * @swagger
 * /locations/{locationId}/admins:
 *   get:
 *     summary: List Location Admins
 *     description: Retrieve a paginated list of administrators for a location
 *     tags: [Location Admins]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: per_page
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of admins retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LocationAdminListResponse'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *   post:
 *     summary: Add Location Admin
 *     description: Add a new administrator to a location
 *     tags: [Location Admins]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LocationAdminCreateParams'
 *     responses:
 *       200:
 *         description: Admin added successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LocationAdmin'
 *       400:
 *         description: Invalid request body or cannot add self as admin
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *
 * /locations/{locationId}/admins/{adminId}:
 *   get:
 *     summary: Get Location Admin
 *     description: Retrieve details of a specific location administrator
 *     tags: [Location Admins]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *       - in: path
 *         name: adminId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the admin
 *     responses:
 *       200:
 *         description: Admin details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LocationAdmin'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Admin not found
 *   put:
 *     summary: Update Location Admin
 *     description: Update the role of a location administrator
 *     tags: [Location Admins]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *       - in: path
 *         name: adminId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the admin
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LocationAdminParams'
 *     responses:
 *       200:
 *         description: Admin role updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LocationAdmin'
 *       400:
 *         description: Invalid request body or cannot update self
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Admin not found
 *   delete:
 *     summary: Remove Location Admin
 *     description: Remove an administrator from a location
 *     tags: [Location Admins]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *       - in: path
 *         name: adminId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the admin
 *     responses:
 *       200:
 *         description: Admin removed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: boolean
 *               example: true
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Admin not found
 */

import Router from "@koa/router";
import { extractQueryParams } from "../utilities";
import { searchParamsSchema } from "../core/searchParams";
import { LocationState } from "../auth/AuthMiddleware";
import {
  addAdminToLocation,
  getLocationAdmin,
  pagedLocationAdmins,
  removeAdminFromLocation,
} from "./LocationAdminRepository";
import { JSONSchemaType } from "ajv";
import Admin from "../auth/Admin";
import { validate } from "../core/validate";
import { locationRoleMiddleware } from "./LocationService";
import { LocationAdminParams } from "./LocationAdmin";
import { locationRoles } from "./Location";
import { RequestError } from "../core/errors";
import { createOrUpdateAdmin } from "../auth/AdminRepository";
import { AdminInvitationService } from "../auth/AdminInvitationService";
import { getOrganization } from "../organizations/OrganizationService";
import { logger } from "../config/logger";

const router = new Router<LocationState & { admin?: Admin }>({
  prefix: "/admins",
});

router.use(locationRoleMiddleware("admin"));

router.get("/", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await pagedLocationAdmins(params, ctx.state.location.id);
});

const locationCreateAdminParamsSchema: JSONSchemaType<
  LocationAdminParams & { email: string }
> = {
  $id: "locationCreateAdminParams",
  type: "object",
  required: ["role", "email"],
  additionalProperties: true,
  properties: {
    email: {
      type: "string",
      format: "email",
    },
    role: {
      type: "string",
      enum: locationRoles,
    },
  },
};

router.post("/", async (ctx) => {
  const organizationId = ctx.state.location.organization_id;
  const { role, email } = validate(
    locationCreateAdminParamsSchema,
    ctx.request.body
  );
  const admin = await createOrUpdateAdmin({
    organization_id: organizationId,
    email,
    role: "member",
  });
  if (ctx.state.admin!.id === admin.id) {
    throw new RequestError("You cannot add yourself to a location");
  }
  await addAdminToLocation(ctx.state.location.id, admin.id, role);

  // Send invitation email to the newly added location admin
  try {
    const organization = await getOrganization(organizationId);
    if (organization) {
      // Load the full admin record (ctx.state.admin only has JWT data: id, organization_id, role)
      const { getAdmin } = await import('../auth/AdminRepository');
      const invitingAdmin = await getAdmin(ctx.state.admin!.id, ctx.state.admin!.organization_id);

      if (invitingAdmin) {
        await AdminInvitationService.sendInvitationEmail({
          admin,
          organization,
          invitedBy: invitingAdmin,
          location: ctx.state.location,
        });
        logger.info(`Location invitation email sent to new admin: ${admin.email}`);
      } else {
        logger.warn(`Could not load inviting admin ${ctx.state.admin!.id} for location invitation email`);
      }
    } else {
      logger.warn(`Organization ${organizationId} not found, skipping invitation email`);
    }
  } catch (error) {
    logger.error(`Failed to send location invitation email to ${admin.email}:`, error);
    // Don't fail the admin creation if email sending fails
  }

  ctx.body = await getLocationAdmin(ctx.state.location.id, admin.id);
});

const locationAdminParamsSchema: JSONSchemaType<LocationAdminParams> = {
  $id: "locationAdminParams",
  type: "object",
  required: ["role"],
  additionalProperties: true,
  properties: {
    role: {
      type: "string",
      enum: locationRoles,
    },
  },
};

/**
 * @swagger
 * /locations/{locationId}/admins/{adminId}/resend-invitation:
 *   post:
 *     summary: Resend invitation email to a location admin
 *     tags: [Location Admins]
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Location ID
 *       - in: path
 *         name: adminId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Admin ID to resend invitation to
 *     responses:
 *       200:
 *         description: Invitation email resent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: Cannot resend invitation to yourself
 *       404:
 *         description: Admin not found
 *       500:
 *         description: Failed to send invitation email
 */
router.post("/:adminId/resend-invitation", async (ctx) => {
  const adminId = parseInt(ctx.params.adminId, 10);

  // Get the target admin record
  const targetAdmin = await Admin.find(adminId);
  if (!targetAdmin) {
    ctx.status = 404;
    ctx.body = {
      success: false,
      message: "Admin not found"
    };
    return;
  }

  // Prevent users from resending invitations to themselves
  if (targetAdmin.id === ctx.state.admin!.id) {
    ctx.status = 400;
    ctx.body = {
      success: false,
      message: "You cannot resend an invitation to yourself"
    };
    return;
  }

  try {
    const organization = await getOrganization(ctx.state.location.organization_id);
    if (!organization) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: "Organization not found"
      };
      return;
    }

    // Load the full admin record for the person sending the invitation
    const { getAdmin } = await import('../auth/AdminRepository');
    const invitingAdmin = await getAdmin(ctx.state.admin!.id, ctx.state.admin!.organization_id);

    if (!invitingAdmin) {
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: "Could not load your admin profile"
      };
      return;
    }

    // Resend the invitation email
    const success = await AdminInvitationService.resendInvitationEmail({
      admin: targetAdmin,
      organization,
      invitedBy: invitingAdmin,
      location: ctx.state.location,
    });

    if (success) {
      logger.info(`Location invitation resent to admin ${targetAdmin.email} by ${invitingAdmin.email}`);
      ctx.body = {
        success: true,
        message: `Invitation email resent to ${targetAdmin.email}`
      };
    } else {
      ctx.status = 500;
      ctx.body = {
        success: false,
        message: "Failed to send invitation email. Please check your email provider configuration."
      };
    }
  } catch (error) {
    logger.error(`Failed to resend location invitation to ${targetAdmin.email}:`, error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      message: "An error occurred while sending the invitation email"
    };
  }
});

router.put("/:adminId", async (ctx) => {
  const admin = await Admin.find(ctx.params.adminId);
  if (!admin) throw new RequestError("Invalid admin ID", 404);
  const { role } = validate(locationAdminParamsSchema, ctx.request.body);
  if (ctx.state.admin!.id === admin.id) {
    throw new RequestError("You cannot add yourself to a location");
  }
  await addAdminToLocation(ctx.state.location.id, admin.id, role);
  ctx.body = await getLocationAdmin(ctx.state.location.id, admin.id);
});

router.get("/:adminId", async (ctx) => {
  const locationAdmin = await getLocationAdmin(
    ctx.state.location.id,
    parseInt(ctx.params.adminId, 10)
  );
  if (!locationAdmin) return ctx.throw(404);
  ctx.body = locationAdmin;
});

router.delete("/:adminId", async (ctx) => {
  await removeAdminFromLocation(
    ctx.state.location.id,
    parseInt(ctx.params.adminId, 10)
  );
  ctx.body = true;
});

export default router;
