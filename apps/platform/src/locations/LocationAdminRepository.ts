import { Database } from "config/database";
import { PageParams } from "../core/searchParams";
import { LocationRole } from "./Location";
import { LocationAdmin } from "./LocationAdmin";

const adminSelectFields = [
  "admins.first_name",
  "admins.last_name",
  "admins.email",
];
const locationAdminFields = [
  `${LocationAdmin.tableName}.*`,
  ...adminSelectFields,
];

const baseLocationAdminQuery = (
  builder: Database.QueryBuilder<any>,
  locationId: number
) => {
  return builder
    .select(locationAdminFields)
    .join("admins", "admin_id", "=", "admins.id")
    .where("location_id", locationId)
    .whereNull(`${LocationAdmin.tableName}.deleted_at`);
};

export const pagedLocationAdmins = async (
  params: PageParams,
  locationId: number
) => {
  return await LocationAdmin.search(
    { ...params, fields: adminSelectFields },
    (q) => baseLocationAdminQuery(q, locationId)
  );
};

export const getLocationAdmin = async (locationId: number, adminId: number) => {
  return await LocationAdmin.first((q) =>
    baseLocationAdminQuery(q.where("admin_id", adminId), locationId)
  );
};

export const addAdminToLocation = async (
  locationId: number,
  adminId: number,
  role: LocationRole
) => {
  const admin = await getLocationAdmin(locationId, adminId);
  if (admin) {
    return await LocationAdmin.update(
      (qb) => qb.where("location_id", locationId).where("admin_id", adminId),
      { role }
    );
  }
  return await LocationAdmin.insert({
    admin_id: adminId,
    location_id: locationId,
    role,
  });
};

export const removeAdminFromLocation = async (
  locationId: number,
  adminId: number
) => {
  return await LocationAdmin.update(
    (qb) => qb.where("admin_id", adminId).where("location_id", locationId),
    { deleted_at: new Date() }
  );
};
