/**
 * @swagger
 * components:
 *   schemas:
 *     LocationApiKey:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: API key ID
 *         location_id:
 *           type: integer
 *           description: ID of the location this key belongs to
 *         name:
 *           type: string
 *           description: Name of the API key
 *         description:
 *           type: string
 *           nullable: true
 *           description: Optional description of the API key's purpose
 *         scope:
 *           type: string
 *           enum: [public, secret]
 *           description: Scope of the API key
 *         role:
 *           type: string
 *           description: Role assigned to this API key
 *         key:
 *           type: string
 *           description: The actual API key value
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *         deleted_at:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           description: Deletion timestamp if revoked
 *     LocationApiKeyParams:
 *       type: object
 *       required: [name]
 *       properties:
 *         scope:
 *           type: string
 *           enum: [public, secret]
 *           description: Scope of the API key
 *         name:
 *           type: string
 *           description: Name of the API key
 *         description:
 *           type: string
 *           nullable: true
 *           description: Optional description of the API key's purpose
 *         role:
 *           type: string
 *           description: Role assigned to this API key
 *     LocationApiKeyListResponse:
 *       type: object
 *       properties:
 *         data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/LocationApiKey'
 *         total:
 *           type: integer
 *           description: Total number of API keys
 *         page:
 *           type: integer
 *           description: Current page number
 *         per_page:
 *           type: integer
 *           description: Number of items per page
 */

/**
 * @swagger
 * tags:
 *   name: Location API Keys
 *   description: Endpoints for managing location API keys
 */

/**
 * @swagger
 * /locations/{locationId}/keys:
 *   get:
 *     summary: List Location API Keys
 *     description: Retrieve a paginated list of API keys for a location
 *     tags: [Location API Keys]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: per_page
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of API keys retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LocationApiKeyListResponse'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *   post:
 *     summary: Create Location API Key
 *     description: Create a new API key for a location
 *     tags: [Location API Keys]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LocationApiKeyParams'
 *     responses:
 *       200:
 *         description: API key created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LocationApiKey'
 *       400:
 *         description: Invalid request body
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *
 * /locations/{locationId}/keys/{keyId}:
 *   get:
 *     summary: Get Location API Key
 *     description: Retrieve details of a specific API key
 *     tags: [Location API Keys]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *       - in: path
 *         name: keyId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the API key
 *     responses:
 *       200:
 *         description: API key details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LocationApiKey'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: API key not found
 *   patch:
 *     summary: Update Location API Key
 *     description: Update an existing API key
 *     tags: [Location API Keys]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *       - in: path
 *         name: keyId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the API key
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LocationApiKeyParams'
 *     responses:
 *       200:
 *         description: API key updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LocationApiKey'
 *       400:
 *         description: Invalid request body
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: API key not found
 *   delete:
 *     summary: Revoke Location API Key
 *     description: Revoke (delete) an API key
 *     tags: [Location API Keys]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *       - in: path
 *         name: keyId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the API key
 *     responses:
 *       200:
 *         description: API key revoked successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LocationApiKey'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: API key not found
 */

import Router from "@koa/router";
import { JSONSchemaType } from "ajv";
import { LocationState } from "../auth/AuthMiddleware";
import { searchParamsSchema } from "../core/searchParams";
import { validate } from "../core/validate";
import { extractQueryParams } from "../utilities";
import { locationRoles } from "./Location";
import { LocationApiKey, LocationApiKeyParams } from "./LocationApiKey";
import {
  createLocationApiKey,
  pagedApiKeys,
  locationRoleMiddleware,
  revokeLocationApiKey,
  updateLocationApiKey,
} from "./LocationService";

const router = new Router<LocationState & { apiKey?: LocationApiKey }>({
  prefix: "/keys",
});

router.use(locationRoleMiddleware("admin"));

router.get("/", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await pagedApiKeys(params, ctx.state.location.id);
});

const locationKeyParams: JSONSchemaType<LocationApiKeyParams> = {
  $id: "locationKeyCreate",
  type: "object",
  required: ["name"],
  properties: {
    scope: {
      type: "string",
      enum: ["public", "secret"],
    },
    name: {
      type: "string",
    },
    description: {
      type: "string",
      nullable: true,
    },
    role: {
      type: "string",
      enum: locationRoles,
    },
  },
  additionalProperties: false,
};
router.post("/", async (ctx) => {
  const payload = validate(locationKeyParams, ctx.request.body);
  ctx.body = await createLocationApiKey(ctx.state.location.id, payload);
});

router.param("keyId", async (value, ctx, next) => {
  const apiKey = await LocationApiKey.first((q) =>
    q
      .whereNull("deleted_at")
      .where("location_id", ctx.state.location.id)
      .where("id", parseInt(value, 10))
  );
  if (!apiKey) {
    return ctx.throw(404);
  }
  ctx.state.apiKey = apiKey;
  return next();
});

router.get("/:keyId", async (ctx) => {
  ctx.body = ctx.state.apiKey!;
});

router.patch("/:keyId", async (ctx) => {
  ctx.body = await updateLocationApiKey(
    ctx.state.apiKey!.id,
    validate(locationKeyParams, ctx.request.body)
  );
});

router.delete("/:keyId", async (ctx) => {
  ctx.body = await revokeLocationApiKey(parseInt(ctx.params.keyId, 10));
});

export default router;
