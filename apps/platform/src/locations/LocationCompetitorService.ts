import { NotFoundError } from "../errors";
import { Knex } from "knex";

interface Competitor {
  place_id: string;
  name: string;
  address: string;
  location: {
    lat: number;
    lng: number;
  };
  distance: number;
}

export class LocationCompetitorService {
  private knex: Knex;

  constructor(knex: Knex) {
    this.knex = knex;
  }

  async addCompetitors(locationId: number, competitors: Competitor[]) {
    // Verify location exists
    const location = await this.knex("locations")
      .where("id", locationId)
      .first();

    if (!location) {
      throw new NotFoundError("Location not found");
    }

    // Insert competitors
    const competitorRecords = competitors.map((competitor) => ({
      location_id: locationId,
      competitor_place_id: competitor.place_id,
      name: competitor.name,
      address: competitor.address,
      latitude: competitor.location.lat,
      longitude: competitor.location.lng,
      distance_km: competitor.distance / 1000, // Convert meters to kilometers
    }));

    // Use transaction to ensure all competitors are added or none
    await this.knex.transaction(async (trx) => {
      // Remove existing competitors
      await trx("location_competitors")
        .where("location_id", locationId)
        .delete();

      // Add new competitors
      await trx("location_competitors").insert(competitorRecords);
    });
  }

  async getCompetitors(locationId: number) {
    const competitors = await this.knex("location_competitors")
      .where("location_id", locationId)
      .select();

    return competitors.map((competitor) => ({
      place_id: competitor.competitor_place_id,
      name: competitor.name,
      address: competitor.address,
      location: {
        lat: parseFloat(competitor.latitude),
        lng: parseFloat(competitor.longitude),
      },
      distance: competitor.distance_km * 1000, // Convert kilometers to meters
    }));
  }

  async removeCompetitor(locationId: number, placeId: string) {
    const deleted = await this.knex("location_competitors")
      .where({
        location_id: locationId,
        competitor_place_id: placeId,
      })
      .delete();

    if (!deleted) {
      throw new NotFoundError("Competitor not found");
    }
  }
}
