/**
 * @swagger
 * components:
 *   schemas:
 *     Locale:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Locale ID
 *         location_id:
 *           type: integer
 *           description: ID of the location this locale belongs to
 *         key:
 *           type: string
 *           description: Unique key identifier for the locale
 *         label:
 *           type: string
 *           description: Display label for the locale
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *     LocaleParams:
 *       type: object
 *       required: [key, label]
 *       properties:
 *         key:
 *           type: string
 *           description: Unique key identifier for the locale
 *         label:
 *           type: string
 *           description: Display label for the locale
 *     LocaleListResponse:
 *       type: object
 *       properties:
 *         data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Locale'
 *         total:
 *           type: integer
 *           description: Total number of locales
 *         page:
 *           type: integer
 *           description: Current page number
 *         per_page:
 *           type: integer
 *           description: Number of items per page
 */

/**
 * @swagger
 * tags:
 *   name: Location Locales
 *   description: Endpoints for managing location locales
 */

/**
 * @swagger
 * /locations/{locationId}/locales:
 *   get:
 *     summary: List Location Locales
 *     description: Retrieve a paginated list of locales for a location
 *     tags: [Location Locales]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: per_page
 *         schema:
 *           type: integer
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of locales retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LocaleListResponse'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *   post:
 *     summary: Create Location Locale
 *     description: Create a new locale for a location
 *     tags: [Location Locales]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LocaleParams'
 *           example:
 *             key: "en-US"
 *             label: "English (United States)"
 *     responses:
 *       200:
 *         description: Locale created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Locale'
 *       400:
 *         description: Invalid request body
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *
 * /locations/{locationId}/locales/{keyId}:
 *   delete:
 *     summary: Delete Location Locale
 *     description: Delete a locale from a location
 *     tags: [Location Locales]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: locationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the location
 *       - in: path
 *         name: keyId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the locale to delete
 *     responses:
 *       200:
 *         description: Locale deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: boolean
 *               example: true
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Locale not found
 */

import Router from "@koa/router";
import { LocationState } from "../auth/AuthMiddleware";
import { JSONSchemaType } from "ajv";
import {
  createLocale,
  deleteLocale,
  pagedLocales,
  locationRoleMiddleware,
} from "./LocationService";
import { extractQueryParams } from "../utilities";
import { searchParamsSchema } from "../core/searchParams";
import { LocaleParams } from "./Locale";
import { validate } from "../core/validate";

const router = new Router<LocationState & { locale?: Locale }>({
  prefix: "/locales",
});

router.use(locationRoleMiddleware("admin"));

router.get("/", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await pagedLocales(params, ctx.state.location.id);
});

const localeParams: JSONSchemaType<LocaleParams> = {
  $id: "localeCreate",
  type: "object",
  required: ["key", "label"],
  properties: {
    key: { type: "string" },
    label: { type: "string" },
  },
  additionalProperties: false,
};
router.post("/", async (ctx) => {
  const payload = validate(localeParams, ctx.request.body);
  ctx.body = await createLocale(ctx.state.location.id, payload);
});

router.delete("/:keyId", async (ctx) => {
  ctx.body = await deleteLocale(
    ctx.state.location.id,
    parseInt(ctx.params.keyId)
  );
});

export default router;
