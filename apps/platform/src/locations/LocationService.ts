/* eslint-disable indent */
import { LocationState } from "../auth/AuthMiddleware";
import { Next, ParameterizedContext } from "koa";
import { RequestError } from "../core/errors";
import { PageParams } from "../core/searchParams";
import { createSubscription } from "../subscriptions/SubscriptionService";
import { uuid } from "../utilities";
import Location, {
  LocationParams,
  LocationRole,
  locationRoles,
} from "./Location";
import { LocationAdmin } from "./LocationAdmin";
import { LocationApiKey, LocationApiKeyParams } from "./LocationApiKey";
import Admin from "../auth/Admin";
import { getAdmin } from "../auth/AdminRepository";
import Locale, { LocaleParams } from "./Locale";
import { getOrganization } from "../organizations/OrganizationService";
import { logger } from "../config/logger";

export const adminLocationIds = async (adminId: number) => {
  const records = await LocationAdmin.all((qb) =>
    qb.where("admin_id", adminId)
  );
  return records.map((item) => item.location_id);
};

export const pagedLocations = async (
  params: PageParams,
  adminId: number,
  organizationId: number
) => {
  const admin = await getAdmin(adminId, organizationId);
  const locationIds = await adminLocationIds(adminId);
  return await Location.search({ ...params, fields: ["name"] }, (qb) =>
    qb.where((qb) =>
      qb
        .where("organization_id", admin!.organization_id)
        .orWhereIn("locations.id", locationIds)
    )
  );
};

export const allLocations = async (adminId: number, organizationId: number) => {
  const admin = await getAdmin(adminId, organizationId);
  if (!admin) return [];
  const locationIds = await adminLocationIds(adminId);
  return await Location.all((qb) => {
    qb.whereIn("locations.id", locationIds);
    if (admin.role !== "member") {
      qb.orWhere("organization_id", admin.organization_id);
    }
    return qb;
  });
};

export const getLocation = async (id: number, adminId?: number) => {
  return Location.first((qb) => {
    qb.where("locations.id", id).select("locations.*");
    if (adminId != null) {
      qb.leftJoin(
        "location_admins",
        "location_admins.location_id",
        "locations.id"
      )
        .where("admin_id", adminId)
        .select("role");
    }
    return qb;
  });
};

export const createLocation = async (
  admin: Admin,
  params: LocationParams
): Promise<Location> => {
  const organization = await getOrganization(admin.organization_id);

  const locationId = await Location.insert({
    ...params,
    sender_email: params.sender_email || organization?.sender_email,
    organization_id: admin.organization_id,
  });

  // Add the user creating the location to it
  await LocationAdmin.insert({
    location_id: locationId,
    admin_id: admin.id,
    role: "admin",
  });

  // Create initial locale for location
  const languages = new Intl.DisplayNames([params.locale], {
    type: "language",
  });
  await Locale.insert({
    location_id: locationId,
    key: params.locale,
    label: languages.of(params.locale),
  });

  // Create a single subscription for each type
  await createSubscription(locationId, {
    name: "Default Email",
    channel: "email",
  });
  await createSubscription(locationId, {
    name: "Default SMS",
    channel: "text",
  });
  await createSubscription(locationId, {
    name: "Default Push",
    channel: "push",
  });
  await createSubscription(locationId, {
    name: "Default Webhook",
    channel: "webhook",
  });

  const location = await getLocation(locationId, admin.id);

  return location!;
};

export const updateLocation = async (
  id: number,
  adminId: number,
  params: Partial<LocationParams>
) => {
  const location = await getLocation(id);
  if (!location) return;

  // If sender_email is being cleared, get org default
  if (params.sender_email === "") {
    const organization = await getOrganization(location.organization_id);
    params.sender_email = organization?.sender_email || undefined;
  }

  await Location.update((qb) => qb.where("id", id), params);
  return await getLocation(id, adminId);
};

export const pagedApiKeys = async (params: PageParams, locationId: number) => {
  return await LocationApiKey.search(
    { ...params, fields: ["name", "description"] },
    (qb) => qb.where("location_id", locationId).whereNull("deleted_at")
  );
};

export const getLocationApiKey = async (key: string) => {
  return LocationApiKey.first((qb) =>
    qb.where("value", key).whereNull("deleted_at")
  );
};

export const createLocationApiKey = async (
  locationId: number,
  params: LocationApiKeyParams
) => {
  return await LocationApiKey.insertAndFetch({
    ...params,
    value: generateApiKey(params.scope),
    location_id: locationId,
  });
};

export const updateLocationApiKey = async (
  id: number,
  params: LocationApiKeyParams
) => {
  return await LocationApiKey.updateAndFetch(id, params);
};

export const revokeLocationApiKey = async (id: number) => {
  return await LocationApiKey.archive(id);
};

export const generateApiKey = (scope: "public" | "secret") => {
  const key = uuid().replace("-", "");
  const prefix = scope === "public" ? "pk" : "sk";
  return `${prefix}_${key}`;
};

export const requireLocationRole = (
  ctx: ParameterizedContext<LocationState>,
  minRole: LocationRole
) => {
  if (
    locationRoles.indexOf(minRole) >
    locationRoles.indexOf(ctx.state.locationRole)
  ) {
    throw new RequestError(`Minimum location role ${minRole} is required`, 403);
  }
};

export const locationRoleMiddleware =
  (minRole: LocationRole) =>
  async (ctx: ParameterizedContext<LocationState>, next: Next) => {
    requireLocationRole(ctx, minRole);
    return next();
  };

export const pagedLocales = async (params: PageParams, locationId: number) => {
  return await Locale.search({ ...params, fields: ["name"] }, (qb) =>
    qb.where("location_id", locationId)
  );
};

export const createLocale = async (
  locationId: number,
  params: LocaleParams
) => {
  return await Locale.insertAndFetch({
    ...params,
    location_id: locationId,
  });
};

export const deleteLocale = async (locationId: number, id: number) => {
  return await Locale.deleteById(id, (qb) =>
    qb.where("location_id", locationId)
  );
};
