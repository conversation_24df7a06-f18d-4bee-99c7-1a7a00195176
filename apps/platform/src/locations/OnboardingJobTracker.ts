import { logger } from "../config/logger";

export interface OnboardingJob {
  locationId: number;
  jobType: string;
  status: "pending" | "processing" | "completed" | "failed";
  startTime: Date;
  endTime?: Date;
  error?: string;
}

// In-memory store for onboarding jobs
// In a production environment, this should be stored in a database
const onboardingJobs: Record<number, OnboardingJob[]> = {};

export const OnboardingJobTracker = {
  /**
   * Start a new job for a location
   */
  startJob(locationId: number, jobType: string): OnboardingJob {
    logger.info(`Starting ${jobType} job for location ${locationId}`);

    if (!onboardingJobs[locationId]) {
      onboardingJobs[locationId] = [];
    }

    const job: OnboardingJob = {
      locationId,
      jobType,
      status: "processing",
      startTime: new Date(),
    };

    onboardingJobs[locationId].push(job);
    return job;
  },

  /**
   * Mark a job as completed
   */
  completeJob(locationId: number, jobType: string): void {
    logger.info(`Completing ${jobType} job for location ${locationId}`);

    const jobs = onboardingJobs[locationId];
    if (!jobs) return;

    const jobIndex = jobs.findIndex(
      (j) => j.jobType === jobType && j.status === "processing"
    );
    if (jobIndex !== -1) {
      jobs[jobIndex].status = "completed";
      jobs[jobIndex].endTime = new Date();
    }
  },

  /**
   * Mark a job as failed
   */
  failJob(locationId: number, jobType: string, error: any): void {
    logger.error(
      `Failed ${jobType} job for location ${locationId}: ${
        error?.message || error
      }`
    );

    const jobs = onboardingJobs[locationId];
    if (!jobs) return;

    const jobIndex = jobs.findIndex(
      (j) => j.jobType === jobType && j.status === "processing"
    );
    if (jobIndex !== -1) {
      jobs[jobIndex].status = "failed";
      jobs[jobIndex].endTime = new Date();
      jobs[jobIndex].error = error?.message || String(error);
    }
  },

  /**
   * Get all jobs for a location
   */
  getJobs(locationId: number): OnboardingJob[] {
    return onboardingJobs[locationId] || [];
  },

  /**
   * Check if any jobs are still processing for a location
   */
  isProcessing(locationId: number): boolean {
    const jobs = onboardingJobs[locationId] || [];
    return jobs.some((j) => j.status === "processing");
  },

  /**
   * Get a summary of job status for a location
   */
  getSummary(locationId: number): {
    isProcessing: boolean;
    total: number;
    completed: number;
    failed: number;
    pending: number;
    processing: number;
    jobs: OnboardingJob[];
  } {
    const jobs = onboardingJobs[locationId] || [];

    return {
      isProcessing: jobs.some((j) => j.status === "processing"),
      total: jobs.length,
      completed: jobs.filter((j) => j.status === "completed").length,
      failed: jobs.filter((j) => j.status === "failed").length,
      pending: jobs.filter((j) => j.status === "pending").length,
      processing: jobs.filter((j) => j.status === "processing").length,
      jobs,
    };
  },

  /**
   * Updates an existing job with new status or details
   */
  updateJob(
    locationId: number,
    jobType: string,
    updates: Partial<OnboardingJob>
  ): void {
    const existingJob = onboardingJobs[locationId]?.find(
      (j) => j.jobType === jobType
    );

    if (existingJob) {
      const jobIndex = onboardingJobs[locationId].findIndex(
        (j) => j.jobType === jobType
      );
      onboardingJobs[locationId][jobIndex] = {
        ...existingJob,
        ...updates,
      };
    } else {
      logger.warn(
        `Attempted to update non-existent job ${jobType} for location ${locationId}`
      );
    }
  },
};
