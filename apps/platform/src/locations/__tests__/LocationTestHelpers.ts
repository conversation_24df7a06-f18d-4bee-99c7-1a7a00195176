import Admin from "../../auth/Admin";
import { uuid } from "../../utilities";
import { createLocation } from "../LocationService";

export const createTestLocation = async () => {
  const admin = await Admin.insertAndFetch({
    first_name: uuid(),
    last_name: uuid(),
    email: `${uuid()}@test.com`,
  });
  return await createLocation(admin, {
    name: uuid(),
    timezone: "utc",
    locale: "en",
  });
};
