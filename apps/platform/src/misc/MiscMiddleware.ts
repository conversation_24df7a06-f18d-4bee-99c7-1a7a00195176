import { ZodSchema } from "zod";
import { Context, Next } from "koa";

export const validateBody =
  (schema: ZodSchema) => async (ctx: Context, next: Next) => {
    try {
      // Parse and validate the request body.
      // If invalid, <PERSON><PERSON> will throw an error.
      ctx.request.body = schema.parse(ctx.request.body);
      await next();
    } catch (error: any) {
      ctx.status = 400;
      if (error.name === "ZodError") {
        // Format the errors to show field paths
        ctx.body = {
          error: "Validation error",
          details: error.errors.map((e: any) => ({
            path: e.path.join("."), // Shows which field has the error
            message: e.message,
          })),
        };
      } else {
        // Handle other types of errors
        ctx.body = {
          error: error.message || "An error occurred",
        };
      }
    }
  };
