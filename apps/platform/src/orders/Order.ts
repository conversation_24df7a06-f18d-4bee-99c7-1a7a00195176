import Model from "../core/Model";

import { User } from "../users/User";

export interface OrderItem {
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  product_data: {
    product_name: string;
    weight?: string;
    [key: string]: any;
  };
}

export interface OrderAddress {
  name: string;
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone?: string;
}

export class Order extends Model {
  user_id!: number;
  location_id!: number;
  status!: string;
  total_amount!: number;
  currency!: string;
  shipping_address?: OrderAddress;
  billing_address?: OrderAddress;
  payment_intent_id?: string;
  external_order_id?: string;
  order_hash?: string;

  // Relations
  items!: OrderItem[];
  user?: User;

  static get tableName() {
    return "orders";
  }

  static get jsonSchema() {
    return {
      type: "object",
      required: ["user_id", "location_id", "total_amount"],
      properties: {
        id: { type: "integer" },
        user_id: { type: "integer" },
        location_id: { type: "integer" },
        status: { type: "string", default: "pending" },
        total_amount: { type: "number" },
        currency: { type: "string", default: "USD" },
        shipping_address: {
          type: "object",
          properties: {
            name: { type: "string" },
            line1: { type: "string" },
            line2: { type: "string" },
            city: { type: "string" },
            state: { type: "string" },
            postal_code: { type: "string" },
            country: { type: "string" },
            phone: { type: "string" },
          },
        },
        billing_address: {
          type: "object",
          properties: {
            name: { type: "string" },
            line1: { type: "string" },
            line2: { type: "string" },
            city: { type: "string" },
            state: { type: "string" },
            postal_code: { type: "string" },
            country: { type: "string" },
            phone: { type: "string" },
          },
        },
        payment_intent_id: { type: "string" },
        external_order_id: { type: "string" },
        order_hash: { type: "string" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
      },
    };
  }

  static get relationMappings() {
    return {
      user: {
        relation: "belongsTo",
        modelClass: User,
        join: {
          from: "orders.user_id",
          to: "users.id",
        },
      },
    };
  }
}
