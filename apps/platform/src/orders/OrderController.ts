/**
 * @swagger
 * tags:
 *   name: Order
 *   description: |
 *     Order management endpoints with dual authentication support.
 *
 *     ## Checkout Authentication
 *
 *     The checkout endpoint `/orders/checkout` supports two authentication modes:
 *
 *     ### API Key Only (Anonymous Orders)
 *     - **Header**: `Authorization: Bearer your_location_api_key`
 *     - **User Creation**: User will be created from email/phone in request body
 *     - **Best for**: Guest checkout, one-time orders
 *
 *     ### API Key + User Token (Linked Orders)
 *     - **Headers**:
 *       - `Authorization: Bearer your_location_api_key`
 *       - `Authorization: Bearer user_jwt_token` (in same header, user token will override API key for user identification)
 *     - **User Linking**: Order will be linked to authenticated user account
 *     - **Best for**: Registered users, order history tracking
 *
 *     ## Other Order Endpoints
 *
 *     All other order management endpoints require location role-based authentication (admin/staff access).
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     Address:
 *       type: object
 *       required:
 *         - name
 *         - line1
 *         - city
 *         - state
 *         - postal_code
 *         - country
 *       properties:
 *         name:
 *           type: string
 *           description: Full name for the address
 *         line1:
 *           type: string
 *           description: Street address line 1
 *         line2:
 *           type: string
 *           description: Street address line 2 (optional)
 *         city:
 *           type: string
 *           description: City name
 *         state:
 *           type: string
 *           description: State/Province/Region
 *         postal_code:
 *           type: string
 *           description: ZIP or postal code
 *         country:
 *           type: string
 *           description: Country name
 *         phone:
 *           type: string
 *           description: Contact phone number (optional)
 *     UserInfo:
 *       type: object
 *       required:
 *         - birthdate
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: User's email address
 *         phone:
 *           type: string
 *           description: User's phone number
 *         firstName:
 *           type: string
 *           description: User's first name
 *         lastName:
 *           type: string
 *           description: User's last name
 *         birthdate:
 *           type: string
 *           format: date
 *           description: User's birth date (YYYY-MM-DD format)
 *     OrderItem:
 *       type: object
 *       required:
 *         - product_id
 *         - quantity
 *       properties:
 *         product_id:
 *           type: string
 *           description: Unique identifier for the product
 *         quantity:
 *           type: integer
 *           minimum: 1
 *           description: Number of items to order
 *     Order:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Order unique identifier
 *         user_id:
 *           type: integer
 *           description: ID of the user who placed the order
 *         total_amount:
 *           type: number
 *           format: float
 *           description: Total order amount
 *         status:
 *           type: string
 *           enum: [pending, processing, completed, cancelled]
 *           description: Current status of the order
 *         items:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/OrderItem'
 *         shipping_address:
 *           $ref: '#/components/schemas/Address'
 *         billing_address:
 *           $ref: '#/components/schemas/Address'
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 */

import Router from "@koa/router";
import { Context, Next, ParameterizedContext } from "koa";
import { logger } from "../config/logger";
import { Order, OrderItem } from "./Order";
import { JSONSchemaType, validate } from "../core/validate";
import { verify, LocationState } from "../auth/AuthMiddleware";
import {
  getUserFromClientId,
  getUserFromEmail,
  getUserFromPhone,
} from "../users/UserRepository";
import { RequestError } from "../core/errors";
import App from "../app";
import { OrderNotificationService } from "./OrderNotificationService";
import { OrderHashService } from "./OrderHashService";
import {
  getLocationApiKey,
  locationRoleMiddleware,
} from "../locations/LocationService";
import { LocationApiKey } from "../locations/LocationApiKey";
import Location from "../locations/Location";
import { update } from "lodash";
import * as admin from "firebase-admin";

// Define JWT token payload interface
interface JwtPayload {
  sub: string;
  exp?: number;
  iat?: number;
}

// Firebase token verification function
const verifyFirebaseToken = async (idToken: string) => {
  try {
    // Get the initialized Firebase admin instance
    const firebaseApp = admin.apps.find((app) => app !== null);
    if (!firebaseApp) {
      throw new Error("Firebase admin not initialized");
    }

    const decodedToken = await firebaseApp.auth().verifyIdToken(idToken, true);
    return decodedToken;
  } catch (error) {
    logger.warn({
      message: "Firebase token verification failed",
      error: error instanceof Error ? error.message : "Unknown error",
    });
    throw error;
  }
};

// Define interfaces for request body validation
interface CheckoutItem {
  product_id: string;
  quantity: number;
}

interface CheckoutRequest {
  items: CheckoutItem[];
  user: {
    email?: string;
    phone?: string;
    firstName?: string;
    lastName?: string;
    birth_date: string;
  };
  shipping_address?: {
    name: string;
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
    phone?: string;
  };
  billing_address?: {
    name: string;
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
    phone?: string;
  };
}

// Define a state interface for our router that includes both API key and location
interface OrderState {
  apiKey?: LocationApiKey;
  locationId?: number;
  location?: Location;
}

// JSON schema for request validation
const checkoutSchema: JSONSchemaType<CheckoutRequest> = {
  type: "object",
  required: ["items", "user"],
  properties: {
    items: {
      type: "array",
      items: {
        type: "object",
        required: ["product_id", "quantity"],
        properties: {
          product_id: { type: "string" },
          quantity: { type: "number", minimum: 1 },
        },
      },
      minItems: 1,
    },
    user: {
      type: "object",
      properties: {
        email: { type: "string", nullable: true },
        phone: { type: "string", nullable: true },
        firstName: { type: "string", nullable: true },
        lastName: { type: "string", nullable: true },
        birth_date: { type: "string" },
      },
      required: ["birth_date"],
    },
    shipping_address: {
      type: "object",
      nullable: true,
      properties: {
        name: { type: "string" },
        line1: { type: "string" },
        line2: { type: "string", nullable: true },
        city: { type: "string" },
        state: { type: "string" },
        postal_code: { type: "string" },
        country: { type: "string" },
        phone: { type: "string", nullable: true },
      },
      required: ["name", "line1", "city", "state", "postal_code", "country"],
    },
    billing_address: {
      type: "object",
      nullable: true,
      properties: {
        name: { type: "string" },
        line1: { type: "string" },
        line2: { type: "string", nullable: true },
        city: { type: "string" },
        state: { type: "string" },
        postal_code: { type: "string" },
        country: { type: "string" },
        phone: { type: "string", nullable: true },
      },
      required: ["name", "line1", "city", "state", "postal_code", "country"],
    },
  },
};

// Schema for updating order status
interface UpdateOrderStatusRequest {
  status: "pending" | "processing" | "completed" | "cancelled";
}

const updateOrderStatusSchema: JSONSchemaType<UpdateOrderStatusRequest> = {
  type: "object",
  required: ["status"],
  properties: {
    status: {
      type: "string",
      enum: ["pending", "processing", "completed", "cancelled"],
    },
  },
};

// Create separate routers for API key and location role authenticated endpoints
const apiKeyRouter = new Router<OrderState>({
  prefix: "/orders",
});

const locationRouter = new Router<LocationState>({
  prefix: "/orders",
});

// Middleware to authenticate with API key
const apiKeyMiddleware = async (
  ctx: ParameterizedContext<OrderState>,
  next: Next
) => {
  try {
    // Get API key from Authorization header
    const authHeader = String(ctx.request.headers.authorization || "");
    let token: string | undefined;

    if (authHeader.startsWith("Bearer ")) {
      token = authHeader.substring(7, authHeader.length);
    }

    if (!token) {
      logger.warn({
        message: "OrderController: Missing API key",
        path: ctx.path,
        ip: ctx.ip,
      });
      throw new RequestError("API key is required", 401);
    }

    // Log the API key being used (partially masked for security)
    const maskedToken =
      token.length > 8
        ? `${token.substring(0, 4)}...${token.substring(token.length - 4)}`
        : "***";

    logger.info({
      message: "OrderController: API key authentication attempt",
      masked_token: maskedToken,
      path: ctx.path,
    });

    // Validate API key
    const apiKey = await getLocationApiKey(token);
    if (!apiKey) {
      logger.warn({
        message: "OrderController: Invalid API key",
        masked_token: maskedToken,
        path: ctx.path,
        ip: ctx.ip,
      });
      throw new RequestError("Invalid API key", 401);
    }

    // Store API key and location ID in context state
    ctx.state.apiKey = apiKey;
    ctx.state.locationId = apiKey.location_id;

    // Get full location details
    const location = await Location.find(apiKey.location_id);
    if (!location) {
      logger.warn({
        message: "OrderController: Location not found",
        location_id: apiKey.location_id,
        path: ctx.path,
      });
      throw new RequestError("Location not found", 404);
    }

    logger.info({
      message: "OrderController: Authentication successful",
      location_id: apiKey.location_id,
      path: ctx.path,
    });

    ctx.state.location = location;

    return next();
  } catch (error) {
    if (error instanceof RequestError) {
      ctx.status = error.statusCode || 401;
      ctx.body = { error: error.message };
    } else {
      logger.error({
        message: "OrderController: Unexpected auth error",
        error: error instanceof Error ? error.message : "Unknown error",
        path: ctx.path,
      });
      ctx.status = 500;
      ctx.body = { error: "Authentication error" };
    }
  }
};

/**
 * @swagger
 * /orders/checkout:
 *   post:
 *     summary: Create Order (Checkout)
 *     description: |
 *       Create a new order with the specified items. Requires location API key for access.
 *
 *       **Authentication Options:**
 *       - **API Key Only**: Provide API key in Authorization header. User will be created from email/phone in request.
 *       - **API Key + User Token**: Provide both headers to link order to authenticated user account.
 *
 *       **Headers Required:**
 *       - `Authorization: Bearer your_location_api_key` (Required - for location identification)
 *       - `x-user-token: user_jwt_token` (Optional - for user account linking)
 *     tags: [Order]
 *     security:
 *       - ApiKeyAuth: []
 *       - UserAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - items
 *               - user
 *             properties:
 *               items:
 *                 type: array
 *                 items:
 *                   $ref: '#/components/schemas/OrderItem'
 *                 minItems: 1
 *                 description: Items to order
 *               user:
 *                 $ref: '#/components/schemas/UserInfo'
 *               shipping_address:
 *                 $ref: '#/components/schemas/Address'
 *               billing_address:
 *                 $ref: '#/components/schemas/Address'
 *     responses:
 *       200:
 *         description: Order created successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Order'
 *                 - type: object
 *                   properties:
 *                     items:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/OrderItem'
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: API key is required or invalid
 *       404:
 *         description: Product not found
 *       500:
 *         description: Internal server error
 */
// Apply API key middleware to checkout endpoint
apiKeyRouter.post(
  "/checkout",
  apiKeyMiddleware,
  async (ctx: ParameterizedContext<OrderState>) => {
    try {
      const locationId = ctx.state.locationId;
      if (!locationId) {
        throw new RequestError("Location ID is required", 400);
      }

      const payload = validate<CheckoutRequest>(
        checkoutSchema,
        ctx.request.body
      );

      let auth_id: string | null = null;
      let user;
      const userAuthHeader = ctx.request.headers["x-user-token"] as string;

      // Extract auth_id from user token if provided
      if (userAuthHeader) {
        try {
          const decoded = await verifyFirebaseToken(userAuthHeader);

          if (!decoded.sub) {
            throw new RequestError("Invalid token payload", 401);
          }

          auth_id = decoded.sub;

          logger.info({
            message: "Extracted auth_id from user token",
            location_id: ctx.state.locationId,
            auth_id_length: auth_id.length,
          });

          // Try to find user by auth_id first
          user = await getUserFromClientId(locationId, {
            auth_id,
          });

          if (user) {
            logger.info({
              message: "Found existing user by auth_id",
              user_id: user.id,
              location_id: ctx.state.locationId,
            });
          } else {
            logger.info({
              message: "No user found with auth_id, will search by email/phone",
              location_id: ctx.state.locationId,
            });
          }
        } catch (error) {
          logger.warn({
            message: "Error verifying user auth token, will try email/phone",
            error: error instanceof Error ? error.message : "Unknown error",
            location_id: ctx.state.locationId,
          });
          // Reset auth_id if token verification failed
          auth_id = null;
        }
      }

      // If no user found via auth_id, try to find or create user by email/phone
      if (!user) {
        const { email, phone, birth_date } = payload.user;

        if (!email && !phone) {
          throw new RequestError(
            "Either email or phone is required when not authenticated",
            400
          );
        }

        if (!birth_date) {
          throw new RequestError("Birthdate is required", 400);
        }

        logger.info({
          message: "Searching for existing user by email/phone",
          location_id: ctx.state.locationId,
          email: email ? "provided" : "not provided",
          phone: phone ? "provided" : "not provided",
          has_auth_id: !!auth_id,
        });

        // Try to find existing user by email first, then phone
        if (email) {
          user = await getUserFromEmail(locationId, email);

          if (user) {
            logger.info({
              message: "Found existing user by email",
              user_id: user.id,
              location_id: ctx.state.locationId,
              current_auth_id: user.auth_id ? "has_auth_id" : "no_auth_id",
              will_link_auth_id: !!auth_id,
            });
          }
        }

        // If not found by email, try phone
        if (!user && phone) {
          user = await getUserFromPhone(locationId, phone);

          if (user) {
            logger.info({
              message: "Found existing user by phone",
              user_id: user.id,
              location_id: ctx.state.locationId,
              current_auth_id: user.auth_id ? "has_auth_id" : "no_auth_id",
              will_link_auth_id: !!auth_id,
            });
          }
        }

        // If we found a user by email/phone and we have an auth_id, link them
        if (user && auth_id) {
          // Check if user already has a different auth_id
          if (user.auth_id && user.auth_id !== auth_id) {
            logger.warn({
              message: "User already has different auth_id, will not overwrite",
              user_id: user.id,
              location_id: ctx.state.locationId,
              existing_auth_id_length: user.auth_id.length,
              new_auth_id_length: auth_id.length,
            });
          } else if (!user.auth_id) {
            // User doesn't have auth_id yet, link it
            try {
              await App.main.db("users").where({ id: user.id }).update({
                auth_id,
                updated_at: new Date(),
              });

              // Update the user object to reflect the change
              user.auth_id = auth_id;

              logger.info({
                message: "Successfully linked auth_id to existing user",
                user_id: user.id,
                location_id: ctx.state.locationId,
              });
            } catch (error) {
              logger.error({
                message: "Failed to link auth_id to existing user",
                user_id: user.id,
                location_id: ctx.state.locationId,
                error: error instanceof Error ? error.message : "Unknown error",
              });
              // Continue without linking - don't fail the checkout
            }
          } else {
            logger.info({
              message: "User already has matching auth_id",
              user_id: user.id,
              location_id: ctx.state.locationId,
            });
          }
        }

        // If still no user found, create a new one
        if (!user) {
          logger.info({
            message: "Creating new user",
            location_id: ctx.state.locationId,
            email: payload.user.email ? "provided" : "not provided",
            phone: payload.user.phone ? "provided" : "not provided",
            has_auth_id: !!auth_id,
          });

          const userData: any = {};
          if (payload.user.firstName) {
            userData.first_name = payload.user.firstName;
          }
          if (payload.user.lastName) {
            userData.last_name = payload.user.lastName;
          }
          if (payload.user.birth_date) {
            userData.birth_date = payload.user.birth_date;
          }

          try {
            // Prepare user data for insertion, including auth_id if available
            const userInsertData = {
              location_id: ctx.state.locationId,
              email: payload.user.email || null,
              phone: payload.user.phone || null,
              birth_date: payload.user.birth_date,
              data: userData,
              auth_id: auth_id || null, // Include auth_id if available
              created_at: new Date(),
              updated_at: new Date(),
            };

            logger.info({
              message: "Inserting new user",
              location_id: ctx.state.locationId,
              email: payload.user.email ? "provided" : "not provided",
              phone: payload.user.phone ? "provided" : "not provided",
              auth_id: auth_id ? "will_be_linked" : "no_auth_id",
            });

            let newUser;
            try {
              // For MySQL: Insert and then fetch the created user
              const insertResult = await App.main
                .db("users")
                .insert(userInsertData);

              // MySQL returns the auto-generated ID in insertResult[0]
              const newUserId = insertResult[0];

              // Fetch the complete user object
              newUser = await App.main
                .db("users")
                .where({ id: newUserId })
                .first();

              logger.info({
                message:
                  "Successfully created user with database auto-generation",
                user_id: newUser?.id,
                location_id: ctx.state.locationId,
                has_auth_id: !!newUser?.auth_id,
              });
            } catch (dbError) {
              // Fallback to UUID if auto-generation fails
              logger.warn({
                message: "Database auto-generation failed, using UUID fallback",
                error:
                  dbError instanceof Error ? dbError.message : "Unknown error",
              });

              const { v4: uuidv4 } = require("uuid");
              const userId = uuidv4();

              await App.main
                .db("users")
                .insert({ ...userInsertData, id: userId });

              // Fetch the complete user object
              newUser = await App.main
                .db("users")
                .where({ id: userId })
                .first();

              logger.info({
                message: "Successfully created user with UUID fallback",
                user_id: userId,
                location_id: ctx.state.locationId,
                has_auth_id: !!newUser?.auth_id,
              });
            }

            user = newUser;

            if (!user?.id) {
              throw new Error("User creation succeeded but user ID is missing");
            }

            logger.info({
              message: "User creation completed successfully",
              user_id: user.id,
              location_id: ctx.state.locationId,
              auth_id_linked: !!user.auth_id,
            });
          } catch (error) {
            logger.error({
              message: "Error creating new user",
              error: error instanceof Error ? error.message : "Unknown error",
              location_id: ctx.state.locationId,
            });
            throw new RequestError("Failed to create user", 500);
          }
        }
      }

      if (!user) {
        throw new RequestError(
          "Unable to find or create user. Please provide user information with birthdate.",
          400
        );
      }

      // Ensure user has a valid ID
      if (!user.id) {
        logger.error({
          message: "User found/created but missing ID",
          user_data: user,
          location_id: ctx.state.locationId,
        });
        throw new RequestError("User creation failed - missing user ID", 500);
      }

      // Final validation: ensure auth_id is properly linked if it was provided
      if (auth_id && user.auth_id !== auth_id) {
        logger.warn({
          message: "Final check: auth_id mismatch after user processing",
          user_id: user.id,
          location_id: ctx.state.locationId,
          expected_auth_id_length: auth_id.length,
          actual_auth_id: user.auth_id ? "has_different_auth_id" : "no_auth_id",
        });
      } else if (auth_id && user.auth_id === auth_id) {
        logger.info({
          message: "Final check: auth_id successfully linked to user",
          user_id: user.id,
          location_id: ctx.state.locationId,
        });
      }

      // Get products from the database
      const productIds = payload.items.map((item) => item.product_id);
      const products = await App.main.db
        .from("products")
        .whereIn("product_id", productIds)
        .andWhere("location_id", ctx.state.locationId);

      // Validate all products exist and are from the correct location
      const productMap = new Map(products.map((p) => [p.product_id, p]));
      for (const item of payload.items) {
        if (!productMap.has(item.product_id)) {
          throw new RequestError(`Product ${item.product_id} not found`, 404);
        }
      }

      // Calculate order total and prepare order items
      let totalAmount = 0;
      const orderItems: OrderItem[] = payload.items.map((item) => {
        const product = productMap.get(item.product_id)!;
        const unitPrice = parseFloat(product.price || product.latest_price);
        const totalPrice = unitPrice * item.quantity;
        totalAmount += totalPrice;

        return {
          product_id: item.product_id,
          quantity: item.quantity,
          unit_price: unitPrice,
          total_price: totalPrice,
          product_data: product, // Store current product data
        };
      });

      logger.info({
        message: "Creating order",
        user_id: user.id,
        location_id: locationId,
        total_amount: totalAmount,
        items_count: orderItems.length,
      });

      // Create order in transaction
      const order = await App.main.db.transaction(async (trx) => {
        // Create main order
        const createdAt = new Date();
        const orderData = {
          location_id: locationId,
          user_id: user.id,
          status: "pending",
          total_amount: totalAmount,
          shipping_address: payload.shipping_address,
          billing_address: payload.billing_address || payload.shipping_address,
          created_at: createdAt,
          updated_at: createdAt,
        };

        logger.info({
          message: "Inserting order with data",
          orderData: {
            ...orderData,
            shipping_address: orderData.shipping_address
              ? "provided"
              : "not provided",
            billing_address: orderData.billing_address
              ? "provided"
              : "not provided",
          },
        });

        const orderInsertResult = await trx("orders").insert(orderData);

        // Get the inserted order ID (MySQL compatible)
        const orderId = orderInsertResult[0];

        logger.info({
          message: "Order created successfully",
          order_id: orderId,
          user_id: user.id,
          location_id: locationId,
        });

        // Get the full order object
        const order = await trx("orders").where({ id: orderId }).first();

        // Generate and store order hash for public access
        const orderHash = OrderHashService.generateOrderHash(
          orderId,
          locationId,
          createdAt
        );
        await trx("orders")
          .where({ id: orderId })
          .update({ order_hash: orderHash });

        // Create order items
        await trx("order_items").insert(
          orderItems.map((item) => ({
            order_id: orderId,
            ...item,
            created_at: new Date(),
            updated_at: new Date(),
          }))
        );

        // Update order object with hash
        order.order_hash = orderHash;

        return order;
      });

      // Attach user and items to order for notifications
      order.user = user;
      order.items = orderItems;

      // Send notifications
      const baseUrl = process.env.BASE_URL || `https://${ctx.request.host}`;
      await OrderNotificationService.sendOrderNotifications(
        order,
        locationId,
        baseUrl
      );

      // Return created order
      ctx.body = {
        ...order,
        items: orderItems,
      };
    } catch (error) {
      logger.error("Error in checkout:", error);

      if (error instanceof RequestError) {
        ctx.status = error.statusCode || 400;
        ctx.body = { error: error.message };
      } else {
        ctx.status = 500;
        ctx.body = {
          error: "An error occurred while processing your order",
          details: error instanceof Error ? error.message : "Unknown error",
        };
      }
    }
  }
);

// Apply location role middleware to all other endpoints
locationRouter.use(locationRoleMiddleware("support"));

// Helper function to get the locationId from the context state
const getLocationId = (ctx: ParameterizedContext<LocationState>): number => {
  if (!ctx.state.location) {
    throw new RequestError("Location is required", 400);
  }
  return ctx.state.location.id;
};

/**
 * @swagger
 * /orders:
 *   get:
 *     summary: Get all orders with pagination
 *     description: Retrieves a paginated list of orders for the location
 *     tags: [Order]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, processing, completed, cancelled]
 *         description: Filter orders by status
 *     responses:
 *       200:
 *         description: List of orders
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 orders:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Order'
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 totalPages:
 *                   type: integer
 */
locationRouter.get("/", async (ctx: ParameterizedContext<LocationState>) => {
  try {
    const locationId = ctx.state.location.id;

    logger.info({
      message: "Getting orders",
      locationId,
      query: ctx.query,
      path: ctx.path,
    });

    // Extract cursor pagination parameters
    const cursor = ctx.query.cursor as string;
    const page = (ctx.query.page as string) || "next";
    const limit = Math.min(
      100,
      Math.max(1, parseInt(ctx.query.limit as string) || 25)
    );

    // Handle search parameters
    const searchQuery = (ctx.query.q as string)?.trim();
    const sort = (ctx.query.sort as string) || "created_at";
    const direction = (ctx.query.direction as string) || "desc";

    // Handle filter object (from frontend)
    let filterObj: any = {};
    try {
      if (ctx.query.filter && typeof ctx.query.filter === "string") {
        filterObj = JSON.parse(ctx.query.filter);
      } else if (ctx.query.filter && typeof ctx.query.filter === "object") {
        filterObj = ctx.query.filter;
      }
    } catch (e) {
      logger.warn("Invalid filter parameter:", ctx.query.filter);
    }

    // Extract status filter
    const statusFilter = (ctx.query.status as string)?.trim();
    const effectiveStatusFilter = filterObj.status || statusFilter;

    // Build query with joins
    let query = App.main
      .db("orders")
      .leftJoin("users", "orders.user_id", "users.id")
      .where("orders.location_id", locationId);

    // Apply status filter if provided
    if (effectiveStatusFilter) {
      query = query.where("orders.status", effectiveStatusFilter);
    }

    // Apply other filters from filter object
    if (filterObj) {
      Object.keys(filterObj).forEach((key) => {
        if (
          key !== "status" && // status is handled separately
          filterObj[key] !== null &&
          filterObj[key] !== undefined &&
          filterObj[key] !== ""
        ) {
          query = query.where(`orders.${key}`, filterObj[key]);
        }
      });
    }

    // Apply text search if provided
    if (searchQuery) {
      query = query.where((builder) => {
        builder
          .where("orders.id", "like", `%${searchQuery}%`)
          .orWhere("users.email", "like", `%${searchQuery}%`)
          .orWhere("users.phone", "like", `%${searchQuery}%`)
          .orWhereRaw("JSON_EXTRACT(users.data, '$.first_name') LIKE ?", [
            `%${searchQuery}%`,
          ])
          .orWhereRaw("JSON_EXTRACT(users.data, '$.last_name') LIKE ?", [
            `%${searchQuery}%`,
          ])
          .orWhereRaw(
            "CONCAT(JSON_EXTRACT(users.data, '$.first_name'), ' ', JSON_EXTRACT(users.data, '$.last_name')) LIKE ?",
            [`%${searchQuery}%`]
          );
      });
    }

    // Apply sorting
    const validSortFields = ["id", "created_at", "total_amount", "status"];
    const sortField = validSortFields.includes(sort) ? sort : "created_at";
    const sortDirection = direction.toLowerCase() === "asc" ? "asc" : "desc";
    query = query.orderBy(`orders.${sortField}`, sortDirection);

    // Apply cursor pagination
    if (cursor) {
      const operator = page === "prev" ? "<" : ">";
      query = query.where("orders.id", operator, cursor);
    }

    // Get one extra item to check if there are more results
    const ordersWithUsers = await query
      .limit(limit + 1)
      .select(
        "orders.id",
        "orders.user_id",
        "orders.total_amount",
        "orders.status",
        "orders.created_at",
        "users.id as user_id_ref",
        "users.email as user_email",
        "users.phone as user_phone",
        "users.data as user_data"
      );

    const hasMore = ordersWithUsers.length > limit;
    if (hasMore) {
      ordersWithUsers.pop(); // Remove the extra item we fetched
    }

    // Transform results to include user information
    const orders = ordersWithUsers.map((orderRow) => ({
      id: orderRow.id,
      user_id: orderRow.user_id,
      total_amount: orderRow.total_amount,
      status: orderRow.status,
      created_at: orderRow.created_at,
      user: orderRow.user_id_ref
        ? {
            id: orderRow.user_id_ref,
            email: orderRow.user_email,
            phone: orderRow.user_phone,
            first_name: orderRow.user_data?.first_name || "",
            last_name: orderRow.user_data?.last_name || "",
          }
        : null,
    }));

    logger.info({
      message: "Retrieved orders",
      count: orders.length,
      hasMore,
      locationId,
      searchQuery,
      statusFilter: effectiveStatusFilter,
      sort: sortField,
      direction: sortDirection,
    });

    ctx.body = {
      results: orders,
      nextCursor: hasMore ? orders[orders.length - 1]?.id : undefined,
      prevCursor: cursor,
      limit,
    };
  } catch (error) {
    logger.error({
      message: "Error fetching orders",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.location?.id,
    });

    ctx.status = 500;
    ctx.body = { error: "Failed to fetch orders" };
  }
});

/**
 * @swagger
 * /orders/{id}:
 *   get:
 *     summary: Get order by ID
 *     description: Retrieves detailed information about a specific order
 *     tags: [Order]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order ID
 *     responses:
 *       200:
 *         description: Order details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Order'
 */
locationRouter.get("/:id", async (ctx: ParameterizedContext<LocationState>) => {
  try {
    const locationId = getLocationId(ctx);

    const orderId = parseInt(ctx.params.id);

    // Get order with user information using JOIN
    const orderWithUser = await App.main
      .db("orders")
      .leftJoin("users", "orders.user_id", "users.id")
      .where({
        "orders.id": orderId,
        "orders.location_id": locationId,
      })
      .select(
        "orders.*",
        "users.id as user_id_ref",
        "users.email as user_email",
        "users.phone as user_phone",
        "users.data as user_data"
      )
      .first();

    if (!orderWithUser) {
      ctx.status = 404;
      ctx.body = { error: "Order not found" };
      return;
    }

    // Get order items
    const items = await App.main
      .db("order_items")
      .where("order_id", orderId)
      .select("*");

    // Format the response
    ctx.body = {
      ...orderWithUser,
      items,
      user: orderWithUser.user_id_ref
        ? {
            id: orderWithUser.user_id_ref,
            first_name: orderWithUser.user_data?.first_name,
            last_name: orderWithUser.user_data?.last_name,
            email: orderWithUser.user_email,
            phone: orderWithUser.user_phone,
            data: orderWithUser.user_data,
          }
        : null,
      // Remove the user fields that were added by the JOIN
      user_id_ref: undefined,
      user_email: undefined,
      user_phone: undefined,
      user_data: undefined,
    };
  } catch (error) {
    logger.error("Error getting order:", error);
    ctx.status = 500;
    ctx.body = {
      error: "An error occurred while fetching the order",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
});

/**
 * @swagger
 * /orders/{id}/status:
 *   put:
 *     summary: Update order status
 *     description: Updates the status of a specific order
 *     tags: [Order]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [pending, processing, completed, cancelled]
 *     responses:
 *       200:
 *         description: Order status updated successfully
 */
locationRouter.put(
  "/:id/status",
  async (ctx: ParameterizedContext<LocationState>) => {
    try {
      const locationId = getLocationId(ctx);

      const orderId = parseInt(ctx.params.id);
      const payload = validate<UpdateOrderStatusRequest>(
        updateOrderStatusSchema,
        ctx.request.body
      );

      // Get order with user information using JOIN
      const orderWithUser = await App.main
        .db("orders")
        .leftJoin("users", "orders.user_id", "users.id")
        .where({
          "orders.id": orderId,
          "orders.location_id": locationId,
        })
        .select(
          "orders.*",
          "users.id as user_id_ref",
          "users.email as user_email",
          "users.phone as user_phone",
          "users.data as user_data"
        )
        .first();

      if (!orderWithUser) {
        ctx.status = 404;
        ctx.body = { error: "Order not found" };
        return;
      }

      // Validate status change rules
      const currentStatus = orderWithUser.status;
      const newStatus = payload.status;

      // Prevent changing status FROM completed or cancelled (irreversible)
      if (currentStatus === "completed" || currentStatus === "cancelled") {
        ctx.status = 400;
        ctx.body = {
          error: `Cannot change status from ${currentStatus}. This status is irreversible.`,
        };
        return;
      }

      // Validate status transitions
      const validTransitions: Record<string, string[]> = {
        pending: ["processing", "completed", "cancelled"],
        processing: ["completed", "cancelled"],
        completed: [], // No transitions allowed FROM completed
        cancelled: [], // No transitions allowed FROM cancelled
      };

      if (!validTransitions[currentStatus]?.includes(newStatus)) {
        ctx.status = 400;
        ctx.body = {
          error: `Invalid status transition from ${currentStatus} to ${newStatus}`,
        };
        return;
      }

      // Update order status
      await App.main.db("orders").where("id", orderId).update({
        status: payload.status,
        updated_at: new Date(),
      });

      // Fetch the updated order (MySQL doesn't support RETURNING clause)
      const updatedOrder = await App.main
        .db("orders")
        .where("id", orderId)
        .first();

      // Prepare user data for notifications
      const user = orderWithUser.user_id_ref
        ? {
            id: orderWithUser.user_id_ref,
            first_name: orderWithUser.user_data?.first_name,
            last_name: orderWithUser.user_data?.last_name,
            email: orderWithUser.user_email,
            phone: orderWithUser.user_phone,
            data: orderWithUser.user_data,
          }
        : null;

      // Get order items for notifications
      const items = await App.main
        .db("order_items")
        .where("order_id", orderId)
        .select("*");

      // Send notification about status change
      const baseUrl = process.env.BASE_URL || `https://${ctx.request.host}`;
      await OrderNotificationService.sendOrderNotifications(
        { ...updatedOrder, user, items },
        locationId,
        baseUrl
      );

      ctx.body = updatedOrder;
    } catch (error) {
      logger.error("Error updating order status:", error);
      if (error instanceof RequestError) {
        ctx.status = error.statusCode || 400;
        ctx.body = { error: error.message };
      } else {
        ctx.status = 500;
        ctx.body = {
          error: "An error occurred while updating the order",
          details: error instanceof Error ? error.message : "Unknown error",
        };
      }
    }
  }
);

/**
 * @swagger
 * /orders/{id}/cancel:
 *   post:
 *     summary: Cancel an order
 *     description: Cancels a specific order if it's not already completed
 *     tags: [Order]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Order ID
 *     responses:
 *       200:
 *         description: Order cancelled successfully
 */
locationRouter.post(
  "/:id/cancel",
  async (ctx: ParameterizedContext<LocationState>) => {
    try {
      const locationId = getLocationId(ctx);

      const orderId = parseInt(ctx.params.id);

      // Get order with user information using JOIN
      const orderWithUser = await App.main
        .db("orders")
        .leftJoin("users", "orders.user_id", "users.id")
        .where({
          "orders.id": orderId,
          "orders.location_id": locationId,
        })
        .select(
          "orders.*",
          "users.id as user_id_ref",
          "users.email as user_email",
          "users.phone as user_phone",
          "users.data as user_data"
        )
        .first();

      if (!orderWithUser) {
        ctx.status = 404;
        ctx.body = { error: "Order not found" };
        return;
      }

      if (orderWithUser.status === "completed") {
        ctx.status = 400;
        ctx.body = {
          error:
            "Cannot cancel a completed order. This status is irreversible.",
        };
        return;
      }

      if (orderWithUser.status === "cancelled") {
        ctx.status = 400;
        ctx.body = { error: "Order is already cancelled" };
        return;
      }

      // Update order status to cancelled
      await App.main.db("orders").where("id", orderId).update({
        status: "cancelled",
        updated_at: new Date(),
      });

      // Fetch the updated order (MySQL doesn't support RETURNING clause)
      const updatedOrder = await App.main
        .db("orders")
        .where("id", orderId)
        .first();

      // Prepare user data for notifications
      const user = orderWithUser.user_id_ref
        ? {
            id: orderWithUser.user_id_ref,
            first_name: orderWithUser.user_data?.first_name,
            last_name: orderWithUser.user_data?.last_name,
            email: orderWithUser.user_email,
            phone: orderWithUser.user_phone,
            data: orderWithUser.user_data,
          }
        : null;

      // Get order items for notifications
      const items = await App.main
        .db("order_items")
        .where("order_id", orderId)
        .select("*");

      // Send notification about cancellation
      const baseUrl = process.env.BASE_URL || `https://${ctx.request.host}`;
      await OrderNotificationService.sendOrderNotifications(
        { ...updatedOrder, user, items },
        locationId,
        baseUrl
      );

      ctx.body = updatedOrder;
    } catch (error) {
      logger.error("Error cancelling order:", error);
      ctx.status = 500;
      ctx.body = {
        error: "An error occurred while cancelling the order",
        details: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
);

/**
 * @swagger
 * /orders/user/{userId}:
 *   get:
 *     summary: Get user order history
 *     description: Retrieves order history for a specific user with pagination
 *     tags: [Order]
 *     security:
 *       - ApiKeyAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: User's order history
 */
locationRouter.get(
  "/user/:userId",
  async (ctx: ParameterizedContext<LocationState>) => {
    try {
      const locationId = getLocationId(ctx);

      const userId = parseInt(ctx.params.userId);
      const page = Math.max(1, parseInt(ctx.query.page as string) || 1);
      const limit = Math.min(
        100,
        Math.max(1, parseInt(ctx.query.limit as string) || 20)
      );
      const offset = (page - 1) * limit;

      // Verify user exists and belongs to location
      const user = await App.main
        .db("users")
        .where({
          id: userId,
          location_id: locationId,
        })
        .first();

      if (!user) {
        ctx.status = 404;
        ctx.body = { error: "User not found" };
        return;
      }

      // Build query with JOIN to get user information
      const query = App.main
        .db("orders")
        .leftJoin("users", "orders.user_id", "users.id")
        .where({
          "orders.user_id": userId,
          "orders.location_id": locationId,
        })
        .orderBy("orders.created_at", "desc");

      // Get total count
      const [{ count }] = await query.clone().count();
      const total = parseInt(count as string);

      // Get paginated orders with user information
      const ordersWithUsers = await query
        .limit(limit)
        .offset(offset)
        .select(
          "orders.id",
          "orders.user_id",
          "orders.total_amount",
          "orders.status",
          "orders.created_at",
          "users.id as user_id_ref",
          "users.email as user_email",
          "users.phone as user_phone",
          "users.data as user_data"
        );

      // Get order items for each order (only if needed for this endpoint)
      const ordersWithItems = await Promise.all(
        ordersWithUsers.map(async (row) => {
          const items = await App.main
            .db("order_items")
            .where("order_id", row.id)
            .select("*");

          return {
            id: row.id,
            user_id: row.user_id,
            total_amount: row.total_amount,
            status: row.status,
            created_at: row.created_at,
            user: row.user_id_ref
              ? {
                  id: row.user_id_ref,
                  first_name: row.user_data?.first_name,
                  last_name: row.user_data?.last_name,
                  email: row.user_email,
                  phone: row.user_phone,
                }
              : null,
            items,
          };
        })
      );

      ctx.body = {
        orders: ordersWithItems,
        total,
        page,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      logger.error("Error getting user order history:", error);
      ctx.status = 500;
      ctx.body = {
        error: "An error occurred while fetching the order history",
        details: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }
);

// Export a combined router
const router = new Router<LocationState>();
router.use(apiKeyRouter.routes());
router.use(locationRouter.routes());

export default router;
