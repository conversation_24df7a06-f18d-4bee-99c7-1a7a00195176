import { loadTextChannel } from "../providers/text";
import { loadEmailChannel } from "../providers/email";
import { Order } from "./Order";
import { logger } from "../config/logger";
import { generateQRCode } from "./QRCodeService";
import { Email } from "../providers/email/Email";
import { TextMessage } from "../providers/text/TextMessage";
import { allProviders } from "../providers/ProviderService";
import Provider from "../providers/Provider";

export class OrderNotificationService {
  static async sendOrderNotifications(
    order: Order,
    locationId: number,
    baseUrl: string
  ) {
    try {
      // Get all providers for the location
      const providers = await allProviders(locationId);
      const emailProvider = providers.find((p) => p.group === "email");
      const textProvider = providers.find((p) => p.group === "text");

      if (!emailProvider && !textProvider) {
        logger.error(
          "No notification providers available for location:",
          locationId
        );
        return false;
      }

      // Generate QR code for order
      const orderUrl = `${baseUrl}/locations/${locationId}/orders/${order.id}`;

      // Generate public order viewing URL using hash (for customer emails)
      const publicOrderUrl = `${baseUrl}/api/public/orders/${order.order_hash}`;
      const qrCode = await generateQRCode(orderUrl);

      // Send customer notifications
      if (order.user?.email && emailProvider) {
        await this.sendCustomerEmail(
          order,
          emailProvider.id,
          locationId,
          qrCode,
          publicOrderUrl
        );
      }
      if (order.user?.phone && textProvider) {
        await this.sendCustomerSMS(
          order,
          textProvider.id,
          locationId,
          publicOrderUrl
        );
      }

      // Send retailer notifications
      await this.sendRetailerNotifications(
        order,
        emailProvider?.id,
        textProvider?.id,
        locationId,
        qrCode,
        orderUrl
      );

      // Send admin notifications
      await this.sendAdminNotifications(
        order,
        emailProvider?.id,
        textProvider?.id,
        locationId,
        qrCode,
        orderUrl
      );

      return true;
    } catch (error) {
      logger.error("Error sending order notifications:", error);
      return false;
    }
  }

  private static async sendCustomerEmail(
    order: Order,
    providerId: number,
    locationId: number,
    qrCode: Buffer,
    orderUrl: string
  ) {
    try {
      const channel = await loadEmailChannel(providerId, locationId);
      if (!channel) {
        throw new Error("Email channel not available");
      }

      const email: Email = {
        to: order.user!.email!,
        from: "<EMAIL>",
        subject: `Order Confirmation #${order.id}`,
        text: `Thank you for your order #${order.id}. View your order at: ${orderUrl}`,
        html: this.generateCustomerEmailHtml(order, orderUrl),
        attachments: [
          {
            filename: `order-${order.id}-qr.png`,
            content: qrCode,
            contentType: "image/png",
          },
        ],
      };

      await channel.provider.send(email);
      logger.info(`Customer email sent for order ${order.id}`);
    } catch (error) {
      logger.error(
        `Error sending customer email for order ${order.id}:`,
        error
      );
    }
  }

  private static async sendCustomerSMS(
    order: Order,
    providerId: number,
    locationId: number,
    orderUrl: string
  ) {
    try {
      const channel = await loadTextChannel(providerId, locationId);
      if (!channel) {
        throw new Error("SMS channel not available");
      }

      const message: TextMessage = {
        to: order.user!.phone!,
        text: `Order Confirmation #${order.id}\nThank you for your order! View details at: ${orderUrl}\nWe'll contact you soon with pickup details.`,
      };

      await channel.provider.send(message);
      logger.info(`Customer SMS sent for order ${order.id}`);
    } catch (error) {
      logger.error(`Error sending customer SMS for order ${order.id}:`, error);
    }
  }

  private static async sendRetailerNotifications(
    order: Order,
    emailProviderId: number | undefined,
    textProviderId: number | undefined,
    locationId: number,
    qrCode: Buffer,
    orderUrl: string
  ) {
    // Send to retailer email
    if (emailProviderId) {
      try {
        const channel = await loadEmailChannel(emailProviderId, locationId);
        if (channel && process.env.RETAILER_EMAIL) {
          const email: Email = {
            to: process.env.RETAILER_EMAIL,
            from: "<EMAIL>",
            subject: `New Order #${order.id}`,
            text: `New order received #${order.id}. View order at: ${orderUrl}`,
            html: this.generateRetailerEmailHtml(order, orderUrl),
            attachments: [
              {
                filename: `order-${order.id}-qr.png`,
                content: qrCode,
                contentType: "image/png",
              },
            ],
          };

          await channel.provider.send(email);
          logger.info(`Retailer email sent for order ${order.id}`);
        }
      } catch (error) {
        logger.error(
          `Error sending retailer email for order ${order.id}:`,
          error
        );
      }
    }

    // Send to retailer SMS
    if (textProviderId) {
      try {
        const channel = await loadTextChannel(textProviderId, locationId);
        if (channel && process.env.RETAILER_PHONE) {
          const message: TextMessage = {
            to: process.env.RETAILER_PHONE,
            text: `NEW ORDER #${order.id}\nCustomer: ${order.user?.firstName} ${order.user?.lastName}\nView details at: ${orderUrl}`,
          };

          await channel.provider.send(message);
          logger.info(`Retailer SMS sent for order ${order.id}`);
        }
      } catch (error) {
        logger.error(
          `Error sending retailer SMS for order ${order.id}:`,
          error
        );
      }
    }
  }

  private static async sendAdminNotifications(
    order: Order,
    emailProviderId: number | undefined,
    textProviderId: number | undefined,
    locationId: number,
    qrCode: Buffer,
    orderUrl: string
  ) {
    // Send to admin email
    if (emailProviderId) {
      try {
        const channel = await loadEmailChannel(emailProviderId, locationId);
        if (channel && process.env.ADMIN_EMAIL) {
          const email: Email = {
            to: process.env.ADMIN_EMAIL,
            from: "<EMAIL>",
            subject: `[ADMIN] New Order #${order.id}`,
            text: `New order received #${order.id}. View order at: ${orderUrl}?admin=true`,
            html: this.generateAdminEmailHtml(order, `${orderUrl}?admin=true`),
            attachments: [
              {
                filename: `order-${order.id}-qr.png`,
                content: qrCode,
                contentType: "image/png",
              },
            ],
          };

          await channel.provider.send(email);
          logger.info(`Admin email sent for order ${order.id}`);
        }
      } catch (error) {
        logger.error(`Error sending admin email for order ${order.id}:`, error);
      }
    }

    // Send to admin SMS
    if (textProviderId) {
      try {
        const channel = await loadTextChannel(textProviderId, locationId);
        if (channel && process.env.ADMIN_PHONE) {
          const message: TextMessage = {
            to: process.env.ADMIN_PHONE,
            text: `[ADMIN] NEW ORDER #${order.id}\nCustomer: ${order.user?.firstName} ${order.user?.lastName}\nView details at: ${orderUrl}?admin=true`,
          };

          await channel.provider.send(message);
          logger.info(`Admin SMS sent for order ${order.id}`);
        }
      } catch (error) {
        logger.error(`Error sending admin SMS for order ${order.id}:`, error);
      }
    }
  }

  private static generateCustomerEmailHtml(
    order: Order,
    orderUrl: string
  ): string {
    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <h2>Thank you for your order!</h2>
          <p>Order #${order.id}</p>
          
          <h3>Order Details:</h3>
          ${this.generateOrderItemsHtml(order)}
          
          <p>We will contact you soon with pickup details.</p>
          <p><strong>View your complete order details:</strong> <a href="${orderUrl}" style="color: #6366f1; text-decoration: none;">Click here to view your order</a></p>
          <p style="font-size: 14px; color: #6b7280;">This is a secure link - no login required. You can bookmark this page to check your order status anytime.</p>
          <p>Show this QR code to the cashier when you pick up your order.</p>
          
          <p>Best regards,<br>BakedBot</p>
        </body>
      </html>
    `;
  }

  private static generateRetailerEmailHtml(
    order: Order,
    orderUrl: string
  ): string {
    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <h2>New Order Received</h2>
          
          <p>Order #${order.id}</p>
          <p>Customer: ${order.user?.firstName} ${order.user?.lastName}</p>
          <p>Email: ${order.user?.email || "Not provided"}</p>
          <p>Phone: ${order.user?.phone || "Not provided"}</p>
          
          <h3>Order Details:</h3>
          ${this.generateOrderItemsHtml(order)}
          
          <p>View complete order details: <a href="${orderUrl}">${orderUrl}</a></p>
          <p>Or scan the QR code in the attachment.</p>
        </body>
      </html>
    `;
  }

  private static generateAdminEmailHtml(
    order: Order,
    orderUrl: string
  ): string {
    return `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="background-color: #6610f2; color: white; padding: 10px; margin-bottom: 20px; border-radius: 5px; text-align: center;">
            <h2 style="margin: 0;">ADMIN NOTIFICATION</h2>
          </div>
          
          <h2>New Order Received</h2>
          
          <p>Order #${order.id}</p>
          <p>Customer: ${order.user?.firstName} ${order.user?.lastName}</p>
          <p>Email: ${order.user?.email || "Not provided"}</p>
          <p>Phone: ${order.user?.phone || "Not provided"}</p>
          
          <h3>Order Details:</h3>
          ${this.generateOrderItemsHtml(order)}
          
          <p>View complete order details with admin controls: <a href="${orderUrl}">${orderUrl}</a></p>
          <p>Or scan the QR code in the attachment.</p>
        </body>
      </html>
    `;
  }

  private static generateOrderItemsHtml(order: Order): string {
    let total = 0;
    const itemsHtml = order.items
      .map((item) => {
        // Ensure unit_price is a number (in case it comes from DB as string)
        const unitPrice = parseFloat(item.unit_price as any) || 0;
        const quantity = parseInt(item.quantity as any) || 0;
        const itemTotal = unitPrice * quantity;
        total += itemTotal;
        return `
        <tr>
          <td style="padding: 12px; border: 1px solid #dee2e6;">${
            item.product_data.product_name
          }</td>
          <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${
            item.product_data.weight || "N/A"
          }</td>
          <td style="padding: 12px; border: 1px solid #dee2e6; text-align: right;">$${unitPrice.toFixed(
            2
          )}</td>
          <td style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">${quantity}</td>
          <td style="padding: 12px; border: 1px solid #dee2e6; text-align: right;">$${itemTotal.toFixed(
            2
          )}</td>
        </tr>
      `;
      })
      .join("");

    return `
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <thead>
          <tr style="background-color: #f8f9fa;">
            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: left;">Product</th>
            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">Weight</th>
            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: right;">Price</th>
            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: center;">Quantity</th>
            <th style="padding: 12px; border: 1px solid #dee2e6; text-align: right;">Total</th>
          </tr>
        </thead>
        <tbody>
          ${itemsHtml}
          <tr style="background-color: #f8f9fa; font-weight: bold;">
            <td colspan="4" style="padding: 12px; border: 1px solid #dee2e6; text-align: right;">Total</td>
            <td style="padding: 12px; border: 1px solid #dee2e6; text-align: right;">$${total.toFixed(
              2
            )}</td>
          </tr>
        </tbody>
      </table>
    `;
  }
}
