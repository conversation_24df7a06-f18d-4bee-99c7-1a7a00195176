import Model, { ModelParams } from "../core/Model";

export interface InviteCodeParams {
  code: string;
  name: string;
  organization_id: number;
  used?: boolean;
  used_at?: Date;
  used_by?: string;
}

class InviteCode extends Model {
  static tableName = "invite_codes";
  static softDelete = true;

  code!: string;
  name!: string;
  organization_id!: number;
  used: boolean = false;
  used_at?: Date;
  used_by?: string;
  deleted_at?: Date;

  static jsonSchema = {
    type: "object",
    required: ["code", "name", "organization_id"],
    properties: {
      id: { type: "integer" },
      code: { type: "string", minLength: 1 },
      name: { type: "string", minLength: 1 },
      organization_id: { type: "integer" },
      used: { type: "boolean" },
      used_at: { type: ["string", "null"], format: "date-time" },
      used_by: { type: ["string", "null"] },
      created_at: { type: "string", format: "date-time" },
      updated_at: { type: "string", format: "date-time" },
      deleted_at: { type: ["string", "null"], format: "date-time" },
    },
  };
}

export default InviteCode;
