/**
 * @swagger
 * tags:
 *   name: Organization
 *   description: Organization management endpoints
 */

import Router from "@koa/router";
import { JSONSchemaType, validate } from "../core/validate";
import App from "../app";
import { Context } from "koa";
import { JwtAdmin, LocationState } from "../auth/AuthMiddleware";
import {
  createInviteCode,
  deleteInviteCode,
  deleteOrganization,
  getInviteCodes,
  getOrganization,
  markInviteCodeAsUsed,
  organizationIntegrations,
  organizationRoleMiddleware,
  requireOrganizationRole,
  updateOrganization,
  validateInviteCode,
  verifySender,
} from "./OrganizationService";
import Organization, { OrganizationParams } from "./Organization";
import { jobs } from "../config/queue";
import { Client } from "@sendgrid/client";

const router = new Router<{
  admin: JwtAdmin;
  organization: Organization;
}>({
  prefix: "/organizations",
});

router.use(async (ctx: Context, next: () => void) => {
  ctx.state.organization = await getOrganization(
    ctx.state.admin.organization_id
  );
  return next();
});

/**
 * @swagger
 * /organizations:
 *   get:
 *     summary: List Organizations
 *     description: Get a list of organizations with optional filtering
 *     tags: [Organization]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of organizations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 organizations:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       status:
 *                         type: string
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 */
router.get("/", async (ctx) => {
  const organization = {
    ...ctx.state.organization,
    artificial_trial_end: ctx.state.organization.artificial_trial_end,
  };
  ctx.body = organization;
});

router.use(organizationRoleMiddleware("admin"));

/**
 * @swagger
 * /organizations/invite-codes:
 *   get:
 *     summary: List Invite Codes
 *     description: Get a list of active invite codes for the organization
 *     tags: [Organization]
 *     responses:
 *       200:
 *         description: List of invite codes
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   code:
 *                     type: string
 *                   created_at:
 *                     type: string
 *                     format: date-time
 *                   expires_at:
 *                     type: string
 *                     format: date-time
 */
router.get("/invite-codes", async (ctx) => {
  ctx.body = await getInviteCodes(ctx.state.organization);
});

interface CreateInviteCodeParams {
  name: string;
  prefix?: string;
}

const createInviteCodeSchema: JSONSchemaType<CreateInviteCodeParams> = {
  type: "object",
  required: ["name"],
  properties: {
    name: { type: "string" },
    prefix: { type: "string", nullable: true },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /organizations/invite-codes:
 *   post:
 *     summary: Create Invite Code
 *     description: Generate a new invite code for the organization
 *     tags: [Organization]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               expires_at:
 *                 type: string
 *                 format: date-time
 *     responses:
 *       200:
 *         description: Invite code created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: string
 *                 expires_at:
 *                   type: string
 *                   format: date-time
 */
router.post("/invite-codes", async (ctx) => {
  requireOrganizationRole(ctx.state.admin!, "owner");
  const params = validate(createInviteCodeSchema, ctx.request.body);
  ctx.body = await createInviteCode(ctx.state.organization, params);
});

/**
 * @swagger
 * /organizations/invite-codes/{id}:
 *   delete:
 *     summary: Delete Invite Code
 *     description: Delete an existing invite code
 *     tags: [Organization]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Invite code identifier
 *     responses:
 *       204:
 *         description: Invite code deleted successfully
 *       404:
 *         description: Invite code not found
 */
router.delete("/invite-codes/:id", async (ctx) => {
  requireOrganizationRole(ctx.state.admin!, "owner");
  const codeId = parseInt(ctx.params.id, 10);

  if (isNaN(codeId)) {
    ctx.throw(400, "Invalid code ID");
  }

  const result = await deleteInviteCode(ctx.state.organization, codeId);

  if (!result) {
    ctx.throw(
      404,
      "Invite code not found or does not belong to this organization"
    );
  }

  ctx.body = { success: true };
});

router.get("/performance/queue", async (ctx) => {
  /**
   * @swagger
   * /organizations/performance/queue:
   *   get:
   *     summary: Get Performance Queue
   *     description: Get the current performance queue status
   *     tags: [Organization]
   *     responses:
   *       200:
   *         description: Queue status
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 active:
   *                   type: integer
   *                 waiting:
   *                   type: integer
   *                 completed:
   *                   type: integer
   */
  ctx.body = await App.main.queue.metrics();
});

router.get("/performance/jobs", async (ctx) => {
  /**
   * @swagger
   * /organizations/performance/jobs:
   *   get:
   *     summary: List Performance Jobs
   *     description: Get a list of performance-related jobs
   *     tags: [Organization]
   *     responses:
   *       200:
   *         description: List of jobs
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 type: object
   *                 properties:
   *                   id:
   *                     type: string
   *                   status:
   *                     type: string
   *                   created_at:
   *                     type: string
   *                     format: date-time
   */
  ctx.body = jobs.map((job) => job.$name);
});

router.get("/performance/jobs/:job", async (ctx) => {
  /**
   * @swagger
   * /organizations/performance/jobs/{job}:
   *   get:
   *     summary: Get Job Details
   *     description: Get detailed information about a specific job
   *     tags: [Organization]
   *     parameters:
   *       - in: path
   *         name: job
   *         required: true
   *         schema:
   *           type: string
   *         description: Job identifier
   *     responses:
   *       200:
   *         description: Job details
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 id:
   *                   type: string
   *                 status:
   *                   type: string
   *                 progress:
   *                   type: number
   *                 result:
   *                   type: object
   *       404:
   *         description: Job not found
   */
  ctx.body = await App.main.stats.list(ctx.params.job);
});

router.get("/performance/failed", async (ctx) => {
  /**
   * @swagger
   * /organizations/performance/failed:
   *   get:
   *     summary: List Failed Jobs
   *     description: Get a list of failed performance jobs
   *     tags: [Organization]
   *     responses:
   *       200:
   *         description: List of failed jobs
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 type: object
   *                 properties:
   *                   id:
   *                     type: string
   *                   error:
   *                     type: string
   *                   failed_at:
   *                     type: string
   *                     format: date-time
   */
  ctx.body = await App.main.queue.failed();
});

router.get("/integrations", async (ctx) => {
  /**
   * @swagger
   * /organizations/integrations:
   *   get:
   *     summary: List Integrations
   *     description: Get a list of active integrations for the organization
   *     tags: [Organization]
   *     responses:
   *       200:
   *         description: List of integrations
   *         content:
   *           application/json:
   *             schema:
   *               type: array
   *               items:
   *                 type: object
   *                 properties:
   *                   id:
   *                     type: string
   *                   type:
   *                     type: string
   *                   status:
   *                     type: string
   *                   config:
   *                     type: object
   */
  ctx.body = await organizationIntegrations(ctx.state.organization);
});

// Simplified schema without artificial_trial_end for validation
const organizationUpdateParams = {
  $id: "organizationUpdate",
  type: "object",
  required: ["username"],
  properties: {
    username: { type: "string" },
    domain: {
      type: "string",
      nullable: true,
    },
    tracking_deeplink_mirror_url: {
      type: "string",
      nullable: true,
    },
    sender_email: {
      type: "string",
      nullable: true,
    },
    artificial_trial_end: {
      type: ["string", "null"],
      format: "date-time",
    },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /organizations/{id}:
 *   patch:
 *     summary: Update Organization
 *     description: Update organization details
 *     tags: [Organization]
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Organization identifier
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               settings:
 *                 type: object
 *     responses:
 *       200:
 *         description: Organization updated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 name:
 *                   type: string
 *                 updated_at:
 *                   type: string
 *                   format: date-time
 *       404:
 *         description: Organization not found
 */
router.patch("/:id", async (ctx) => {
  requireOrganizationRole(ctx.state.admin!, "owner");
  // Use any to bypass type checking for the schema validation
  const validated = validate(
    organizationUpdateParams as any,
    ctx.request.body
  ) as {
    username: string;
    domain?: string;
    tracking_deeplink_mirror_url?: string;
    sender_email?: string;
    artificial_trial_end?: string;
  };

  // Convert date string to Date object if present
  const payload: OrganizationParams = {
    username: validated.username,
    domain: validated.domain,
    tracking_deeplink_mirror_url: validated.tracking_deeplink_mirror_url,
    sender_email: validated.sender_email,
    artificial_trial_end: validated.artificial_trial_end
      ? new Date(validated.artificial_trial_end)
      : undefined,
  };

  ctx.body = await updateOrganization(ctx.state.organization, payload);
});

/**
 * @swagger
 * /organizations:
 *   delete:
 *     summary: Delete Organization
 *     description: Delete an organization and all its associated data
 *     tags: [Organization]
 *     responses:
 *       204:
 *         description: Organization deleted successfully
 *       400:
 *         description: Cannot delete organization with active subscriptions
 */
router.delete("/", async (ctx) => {
  requireOrganizationRole(ctx.state.admin!, "owner");
  await deleteOrganization(ctx.state.organization);
  ctx.body = true;
});

interface VerifySenderParams {
  email: string;
}

const verifySenderParams: JSONSchemaType<VerifySenderParams> = {
  type: "object",
  required: ["email"],
  properties: {
    email: { type: "string" },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /organizations/verify-sender:
 *   post:
 *     summary: Verify Sender
 *     description: Verify an email sender for the organization
 *     tags: [Organization]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *     responses:
 *       200:
 *         description: Verification email sent
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                 verification_id:
 *                   type: string
 */
router.post("/verify-sender", async (ctx) => {
  const { email } = validate(verifySenderParams, ctx.request.body);
  try {
    ctx.body = await verifySender(ctx.state.organization, email);
  } catch (error: any) {
    console.error("SendGrid sender verification failed:", error);
    ctx.throw(400, error.message || "Failed to verify sender");
  }
});

export default router;
