/* eslint-disable indent */
import { RequestError } from "../core/errors";
import Admin from "../auth/Admin";
import Provider from "../providers/Provider";
import { encodeHashid, uuid } from "../utilities";
import Organization, {
  OrganizationRole,
  organizationRoles,
} from "./Organization";
import { JwtAdmin } from "../auth/AuthMiddleware";
import { Next, ParameterizedContext } from "koa";
import { Client } from "@sendgrid/client";
import StripeSubscription from "../subscriptions/StripeSubscription";
import InviteCode, { InviteCodeParams } from "./InviteCode";
import Location from "../locations/Location";
export const getOrganization = async (id: number) => {
  return await Organization.find(id);
};

export const getOrganizationByUsername = async (username: string) => {
  return await Organization.first((qb) => qb.where("username", username));
};

export const getOrganizationByDomain = async (domain?: string) => {
  if (!domain) return undefined;
  return await Organization.first((qb) => qb.where("domain", domain));
};

export const getOrganizationByEmail = async (email: string) => {
  const admin = await Admin.first((qb) => qb.where("email", email));
  if (!admin) return undefined;
  return await getOrganization(admin.organization_id);
};

export const getDefaultOrganization = async () => {
  return await Organization.first();
};

export const createOrganization = async (
  email: string
): Promise<Organization> => {
  let org: Organization | undefined;
  try {
    // Set artificial trial end date to 14 days from now
    const trialEndDate = new Date();
    trialEndDate.setDate(trialEndDate.getDate() + 14);

    org = await Organization.insertAndFetch({
      domain: email,
      username: email,
      artificial_trial_end: trialEndDate,
    });

    // const subscriptionObj = await StripeSubscription.first((qb) =>
    //   qb.where("organization_id", org?.id)
    // );
    // if (!subscriptionObj) {
    //   // Create subscription Instance
    //   const sub = await StripeSubscription.insertAndFetch({
    //     id: org.id,
    //   });
    // }
  } catch {
    org = await Organization.insertAndFetch({
      username: undefined,
    });
  }
  return org;
};

export const updateOrganization = async (
  organization: Organization,
  params: Partial<Organization>
) => {
  return await Organization.updateAndFetch(organization.id, params);
};

export const organizationIntegrations = async (organization: Organization) => {
  return await Provider.all((qb) =>
    qb
      .leftJoin("locations", "locations.id", "providers.location_id")
      .where("locations.organization_id", organization.id)
  );
};

export const deleteOrganization = async (organization: Organization) => {
  await Organization.deleteById(organization.id);
};

export const requireOrganizationRole = (
  admin: Admin | JwtAdmin,
  minRole: OrganizationRole
) => {
  if (
    organizationRoles.indexOf(admin.role) < organizationRoles.indexOf(minRole)
  ) {
    throw new RequestError(
      `Minimum organization role ${minRole} is required`,
      403
    );
  }
};

export const organizationRoleMiddleware =
  (minRole: OrganizationRole) =>
  async (
    ctx: ParameterizedContext<{ admin?: Admin | JwtAdmin }>,
    next: Next
  ) => {
    requireOrganizationRole(ctx.state.admin!, minRole);
    return next();
  };

export const verifySender = async (
  organization: Organization,
  email: string
) => {
  const client = new Client();
  client.setApiKey(process.env.SENDGRID_API_KEY || "");

  const [response] = await client.request({
    method: "POST",
    url: "/v3/verified_senders",
    body: {
      nickname: `${organization.username} Sender`,
      from_email: email,
      from_name: organization.username,
      reply_to: email,
      reply_to_name: organization.username,
      address: "123 Elm St",
      address_2: "",
      city: "Denver",
      state: "CO",
      zip: "80202",
      country: "United States",
    },
  });

  if (response.statusCode !== 201) {
    throw new RequestError(
      (response.body as any)?.errors?.[0]?.message || "Failed to verify sender",
      400
    );
  }

  // Update organization with verified sender email
  await updateOrganization(organization, { sender_email: email });

  return { status: "success", message: "Verification email sent" };
};

/**
 * Generate a unique invite code with a prefix
 */
export async function generateUniqueCode(
  prefix: string = "BB-"
): Promise<string> {
  const randomCode = Math.floor(100000 + Math.random() * 900000).toString();
  const code = `${prefix}${randomCode}`;

  // Check if code already exists
  const existingCode = await InviteCode.query().where({ code }).first();
  if (existingCode) {
    // If code exists, try again with recursion
    return generateUniqueCode(prefix);
  }

  return code;
}

/**
 * Create a new invite code
 */
export async function createInviteCode(
  organization: Organization,
  params: { name: string; prefix?: string }
): Promise<InviteCode> {
  const code = await generateUniqueCode(params.prefix);

  return await InviteCode.insertAndFetch({
    code,
    name: params.name,
    organization_id: organization.id,
    used: false,
  });
}

/**
 * Get all invite codes for an organization
 */
export async function getInviteCodes(
  organization: Organization
): Promise<InviteCode[]> {
  return InviteCode.query()
    .where({ organization_id: organization.id })
    .orderBy("created_at", "desc");
}

/**
 * Validate an invite code
 */
export async function validateInviteCode(
  code: string
): Promise<{ valid: boolean; code?: InviteCode; error?: string }> {
  const locations = await Location.query();
  if (locations.length === 0) {
    return { valid: true };
  }

  const inviteCode = await InviteCode.query().where({ code }).first();

  if (!inviteCode) {
    return {
      valid: false,
      error: "Invalid invite code. Please check your code and try again.",
    };
  }

  if (inviteCode.used) {
    return {
      valid: false,
      error:
        "This invite code has already been used. Please contact your administrator for a new code.",
    };
  }

  return { valid: true, code: inviteCode };
}

/**
 * Delete an invite code
 */
export async function deleteInviteCode(
  organization: Organization,
  codeId: number
): Promise<boolean> {
  // Check that the code belongs to the organization
  const inviteCode = await InviteCode.query()
    .where({ id: codeId, organization_id: organization.id })
    .first();

  if (!inviteCode) {
    return false;
  }

  // Delete the code
  await InviteCode.query().where("id", codeId).delete();
  return true;
}

/**
 * Mark an invite code as used
 */
export async function markInviteCodeAsUsed(
  code: string,
  email: string
): Promise<InviteCode | null> {
  const inviteCode = await InviteCode.query().where({ code }).first();

  if (!inviteCode || inviteCode.used) {
    return null;
  }

  // Update the invite code
  await InviteCode.query().where({ id: inviteCode.id }).update({
    used: true,
    used_at: new Date(),
    used_by: email,
  });

  // Fetch and return the updated record
  const updated = await InviteCode.query().where({ id: inviteCode.id }).first();
  return updated || null;
}
