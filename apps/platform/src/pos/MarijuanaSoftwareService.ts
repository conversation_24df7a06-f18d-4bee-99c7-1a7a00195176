import axios from "axios";
import { logger } from "../config/logger";

interface MarijuanaSoftwareConfig {
  webguid: string;
  subscriptionKey: string;
  apiUrl?: string;
}

interface MarijuanaSoftwareTicket {
  id: string;
  webguid: string;
  utcDate: string;
  LocalTime: string;
  TicketType: string;
  CustomerId: string;
  EmployeeId: string;
  PaymentType: string;
  Total: string;
  Tax: string;
  SubTotal: string;
  Discount: string | null;
  IsMedical: boolean;
  Items: MarijuanaSoftwareTicketItem[];
}

interface MarijuanaSoftwareTicketItem {
  ProductId: string;
  InventoryId: string;
  Name: string;
  Quantity: string;
  SubTotal: string;
  Total: string;
  Tax: string;
  Discount: string;
  DiscountList: any;
  Cost: string;
  Profit: string | null;
  Weight: string;
}

interface MarijuanaSoftwareProduct {
  id: string;
  productid: string;
  name: string;
  label: string;
  unit: string;
  price: number;
  quantityInStock: number;
  vendor: string;
  brand: string;
  category: string;
  producttype: string;
  description: string;
  strain: string;
  thcMeasure: number;
  cbdMeasure: number;
  sku: string;
  active: boolean;
}

export class MarijuanaSoftwareService {
  private config: MarijuanaSoftwareConfig;
  private axiosInstance;

  constructor(config: MarijuanaSoftwareConfig) {
    this.config = {
      apiUrl: "https://api.marijuanasoftwarellc.com/v1",
      ...config,
    };

    this.axiosInstance = axios.create({
      baseURL: this.config.apiUrl,
      headers: {
        "Cache-Control": "no-cache",
        "Ocp-Apim-Subscription-Key": this.config.subscriptionKey,
      },
    });
  }

  async getTickets(
    startDate: Date,
    endDate: Date
  ): Promise<MarijuanaSoftwareTicket[]> {
    try {
      const response = await this.axiosInstance.get("/Tickets", {
        params: {
          webguid: this.config.webguid,
          startDate: startDate.toLocaleDateString("en-US"),
          endDate: endDate.toLocaleDateString("en-US"),
        },
      });
      return response.data;
    } catch (error) {
      logger.error("Error fetching tickets from Marijuana Software:", error);
      throw error;
    }
  }

  async getProducts(): Promise<MarijuanaSoftwareProduct[]> {
    try {
      const response = await this.axiosInstance.get("/Products", {
        params: {
          webguid: this.config.webguid,
        },
      });
      return response.data;
    } catch (error) {
      logger.error("Error fetching products from Marijuana Software:", error);
      throw error;
    }
  }

  // Helper method to normalize ticket data to match our POS schema
  normalizePosData(tickets: MarijuanaSoftwareTicket[]) {
    return tickets.flatMap((ticket) => {
      return ticket.Items.map((item) => ({
        location_id: 0, // This will be set by the import service
        location_name: "", // This will be set by the import service
        master_category: item.Name.split(" - ")[0],
        order_date: new Date(ticket.utcDate),
        customer_type: ticket.IsMedical ? "Medical" : "Recreational",
        budtender_name: ticket.EmployeeId,
        gross_sales: parseFloat(item.Total),
        returned_amount: 0,
        discounted_amount: parseFloat(item.Discount || "0"),
        loyalty_as_discount: 0,
        net_sales: parseFloat(item.SubTotal),
        inventory_cost: parseFloat(item.Cost),
        inventory_profit: item.Profit ? parseFloat(item.Profit) : 0,
        loyalty_as_payment: 0,
        tax_amount: parseFloat(item.Tax),
        invoice_total: parseFloat(ticket.Total),
        amount_paid_in_cash:
          ticket.PaymentType.toLowerCase() === "cash"
            ? parseFloat(ticket.Total)
            : 0,
        amount_paid_in_debit:
          ticket.PaymentType.toLowerCase() === "debit"
            ? parseFloat(ticket.Total)
            : 0,
        customer_name: ticket.CustomerId,
        product_name: item.Name,
      }));
    });
  }

  // Helper method to normalize product data
  normalizeProductData(products: MarijuanaSoftwareProduct[]) {
    return products.map((product) => ({
      meta_sku: product.sku,
      retailer_id: product.productid,
      brand_name: product.brand,
      product_name: product.name,
      raw_product_name: product.label,
      raw_weight_string: product.unit,
      display_weight: product.unit,
      raw_product_category: product.category,
      category: product.category,
      raw_subcategory: product.producttype,
      subcategory: product.producttype,
      percentage_thc: product.thcMeasure,
      percentage_cbd: product.cbdMeasure,
      medical: false,
      recreational: true,
      latest_price: product.price,
      data: {
        description: product.description,
        strain: product.strain,
        vendor: product.vendor,
        quantity_in_stock: product.quantityInStock,
      },
    }));
  }
}
