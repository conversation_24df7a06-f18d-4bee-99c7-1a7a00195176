import Job from "../queue/Job";
import { logger } from "../config/logger";
import { MarijuanaSoftwareService } from "./MarijuanaSoftwareService";
import { PosData } from "./PosData";
import { Product } from "../products/Product";
import Location from "../locations/Location";

interface MarijuanaSoftwareSyncConfig {
  location_id: number;
  webguid: string;
  subscription_key: string;
  sync_type: "tickets" | "products" | "both";
  start_date?: Date;
  end_date?: Date;
}

export default class MarijuanaSoftwareSyncJob extends Job {
  static type = "marijuana_software_sync";

  config!: MarijuanaSoftwareSyncConfig;

  static from(config: MarijuanaSoftwareSyncConfig) {
    return new this({ config });
  }

  async handle<T>(): Promise<T> {
    try {
      const location = await Location.first((qb) =>
        qb.where({ id: this.config.location_id })
      );
      if (!location) {
        throw new Error(`Location ${this.config.location_id} not found`);
      }

      const service = new MarijuanaSoftwareService({
        webguid: this.config.webguid,
        subscriptionKey: this.config.subscription_key,
      });

      if (
        this.config.sync_type === "tickets" ||
        this.config.sync_type === "both"
      ) {
        await this.syncTickets(service, location.id);
      }

      if (
        this.config.sync_type === "products" ||
        this.config.sync_type === "both"
      ) {
        await this.syncProducts(service, location.id);
      }

      return undefined as unknown as T;
    } catch (error) {
      logger.error("Error in MarijuanaSoftwareSyncJob:", error);
      throw error;
    }
  }

  private async syncTickets(
    service: MarijuanaSoftwareService,
    locationId: number
  ) {
    const startDate =
      this.config.start_date || new Date(Date.now() - 24 * 60 * 60 * 1000); // Default to last 24 hours
    const endDate = this.config.end_date || new Date();

    const tickets = await service.getTickets(startDate, endDate);
    const normalizedData = service.normalizePosData(tickets);

    for (const data of normalizedData) {
      try {
        await PosData.insert({
          ...data,
          location_id: locationId,
        });
      } catch (error) {
        logger.error(`Error inserting POS data:`, error);
        // Continue with next record instead of failing entire job
      }
    }

    logger.info(
      `Synced ${normalizedData.length} tickets for location ${locationId}`
    );
  }

  private async syncProducts(
    service: MarijuanaSoftwareService,
    locationId: number
  ) {
    const products = await service.getProducts();
    const normalizedProducts = service.normalizeProductData(products);

    for (const product of normalizedProducts) {
      try {
        // Try to find existing product by meta_sku
        const existingProduct = await Product.first((qb) =>
          qb.where({ meta_sku: product.meta_sku, location_id: locationId })
        );

        if (existingProduct) {
          // Update existing product
          await Product.update(
            (qb) => qb.where({ id: existingProduct.id }),
            product
          );
        } else {
          // Insert new product
          await Product.insert({
            ...product,
            location_id: locationId,
          });
        }
      } catch (error) {
        logger.error(`Error upserting product:`, error);
        // Continue with next product instead of failing entire job
      }
    }

    logger.info(
      `Synced ${products.length} products for location ${locationId}`
    );
  }
}
