/**
 * @swagger
 * components:
 *   schemas:
 *     PosCredentials:
 *       type: object
 *       required:
 *         - webguid
 *         - subscriptionKey
 *       properties:
 *         webguid:
 *           type: string
 *         subscriptionKey:
 *           type: string
 *     PosConnectionTestRequest:
 *       type: object
 *       required:
 *         - posType
 *         - credentials
 *       properties:
 *         posType:
 *           type: string
 *         credentials:
 *           $ref: '#/components/schemas/PosCredentials'
 *     PosConnectionTestResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             ticketCount:
 *               type: integer
 *             productCount:
 *               type: integer
 *         error:
 *           type: string
 *     PosConnectResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         productCount:
 *           type: integer
 *         transactionCount:
 *           type: integer
 *         error:
 *           type: string
 *     PosSaveNormalizedDataRequest:
 *       type: object
 *       required:
 *         - posSystem
 *         - credentials
 *       properties:
 *         posSystem:
 *           type: string
 *         credentials:
 *           type: object
 *           required:
 *             - webguid
 *             - hasSubscriptionKey
 *           properties:
 *             webguid:
 *               type: string
 *             hasSubscriptionKey:
 *               type: boolean
 *     PosSaveNormalizedDataResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         dataRefId:
 *           type: string
 *         error:
 *           type: string
 */

/**
 * @swagger
 * tags:
 *   name: POS Integration
 *   description: Point of Sale (POS) system integration endpoints
 */

import Router from "@koa/router";
import { AuthState, LocationState } from "../auth/AuthMiddleware";
import { requireLocationRole } from "../locations/LocationService";
import { MarijuanaSoftwareService } from "./MarijuanaSoftwareService";
import { JSONSchemaType, validate } from "../core/validate";
import { RequestError } from "../core/errors";

// Create a router for POS API endpoints
const router = new Router<LocationState>({ prefix: "/pos" });

// Schema for connection test
const testConnectionSchema: JSONSchemaType<{
  posType: string;
  credentials: {
    webguid: string;
    subscriptionKey: string;
  };
}> = {
  type: "object",
  required: ["posType", "credentials"],
  properties: {
    posType: { type: "string" },
    credentials: {
      type: "object",
      required: ["webguid", "subscriptionKey"],
      properties: {
        webguid: { type: "string" },
        subscriptionKey: { type: "string" },
      },
    },
  },
};

// Schema for save normalized data
const saveNormalizedDataSchema: JSONSchemaType<{
  posSystem: string;
  credentials: {
    webguid: string;
    hasSubscriptionKey: boolean;
  };
}> = {
  type: "object",
  required: ["posSystem", "credentials"],
  properties: {
    posSystem: { type: "string" },
    credentials: {
      type: "object",
      required: ["webguid", "hasSubscriptionKey"],
      properties: {
        webguid: { type: "string" },
        hasSubscriptionKey: { type: "boolean" },
      },
    },
  },
};

/**
 * @swagger
 * /pos/test-connection:
 *   post:
 *     summary: Test connection to a POS system
 *     tags: [POS Integration]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PosConnectionTestRequest'
 *     responses:
 *       200:
 *         description: Connection test result
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PosConnectionTestResponse'
 */
router.post("/test-connection", async (ctx) => {
  requireLocationRole(ctx, "admin");
  const { location } = ctx.state;
  const payload = validate(testConnectionSchema, ctx.request.body);

  try {
    if (payload.posType === "marijuanasoftware") {
      // Initialize MarijuanaSoftware service
      const posService = new MarijuanaSoftwareService({
        webguid: payload.credentials.webguid,
        subscriptionKey: payload.credentials.subscriptionKey,
      });

      // Test the connection with a small data request
      const now = new Date();
      const startDate = new Date();
      startDate.setDate(now.getDate() - 7); // Get 7 days of data for testing

      const tickets = await posService.getTickets(startDate, now);
      const products = await posService.getProducts();

      ctx.body = {
        success: true,
        message: "Connection to MarijuanaSoftware successful",
        data: {
          ticketCount: tickets.length,
          productCount: products.length,
        },
      };
    } else {
      ctx.body = {
        success: false,
        error: `Unsupported POS system type: ${payload.posType}`,
      };
    }
  } catch (error: any) {
    console.error("Error testing POS connection:", error);
    ctx.body = {
      success: false,
      error:
        error.message || "Unknown error occurred during POS connection test",
    };
  }
});

/**
 * @swagger
 * /pos/connect:
 *   post:
 *     summary: Connect to a POS system and retrieve data
 *     tags: [POS Integration]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PosConnectionTestRequest'
 *     responses:
 *       200:
 *         description: Connection and data retrieval result
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PosConnectResponse'
 */
router.post("/connect", async (ctx) => {
  requireLocationRole(ctx, "admin");
  const { location } = ctx.state;
  const payload = validate(testConnectionSchema, ctx.request.body);

  try {
    if (payload.posType === "marijuanasoftware") {
      // Initialize MarijuanaSoftware service
      const posService = new MarijuanaSoftwareService({
        webguid: payload.credentials.webguid,
        subscriptionKey: payload.credentials.subscriptionKey,
      });

      // Get data from the POS system
      const now = new Date();
      const startDate = new Date();
      startDate.setDate(now.getDate() - 30); // Get 30 days of data

      const tickets = await posService.getTickets(startDate, now);
      const products = await posService.getProducts();

      // Normalize the data
      const normalizedTransactions = posService.normalizePosData(tickets);
      const normalizedProducts = posService.normalizeProductData(products);

      // Save credentials to database (in a real implementation, use a secure vault)
      // await savePosCredentials(location.id, payload.posType, {
      //   webguid: payload.credentials.webguid,
      //   subscriptionKey: "[REDACTED]" // Don't store the actual key in plain text
      // });

      // Return success with product/transaction counts
      ctx.body = {
        success: true,
        message: "Successfully connected to MarijuanaSoftware",
        productCount: products.length,
        transactionCount: tickets.length,
      };
    } else {
      ctx.body = {
        success: false,
        error: `Unsupported POS system type: ${payload.posType}`,
      };
    }
  } catch (error: any) {
    console.error("Error connecting to POS system:", error);
    ctx.body = {
      success: false,
      error: error.message || "Unknown error occurred during POS connection",
    };
  }
});

/**
 * @swagger
 * /pos/save-normalized-data:
 *   post:
 *     summary: Save normalized POS data
 *     tags: [POS Integration]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PosSaveNormalizedDataRequest'
 *     responses:
 *       200:
 *         description: Data saving result
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PosSaveNormalizedDataResponse'
 */
router.post("/save-normalized-data", async (ctx) => {
  requireLocationRole(ctx, "admin");
  const { location } = ctx.state;
  const payload = validate(saveNormalizedDataSchema, ctx.request.body);

  try {
    if (payload.posSystem === "marijuanasoftware") {
      // Get stored credentials (in a real implementation, retrieve from secure storage)
      // const storedCredentials = await getPosCredentials(location.id, payload.posSystem);

      // In a production environment, we'd use the stored credentials to refresh data
      // and store it in our database, then connect it to Supabase

      ctx.body = {
        success: true,
        message: "Successfully saved normalized data",
        // Include references to the saved data
        dataRefId: `pos_data_${location.id}_${Date.now()}`,
      };
    } else {
      ctx.body = {
        success: false,
        error: `Unsupported POS system type: ${payload.posSystem}`,
      };
    }
  } catch (error: any) {
    console.error("Error saving normalized POS data:", error);
    ctx.body = {
      success: false,
      error: error.message || "Unknown error occurred while saving POS data",
    };
  }
});

export default router;
