import Model from "../core/Model";

export class PosData extends Model {
  location_id!: number;
  location_name!: string;
  master_category!: string;
  order_date!: Date;
  customer_type!: string;
  budtender_name!: string;
  gross_sales!: number;
  returned_amount!: number;
  discounted_amount!: number;
  loyalty_as_discount!: number;
  net_sales!: number;
  inventory_cost!: number;
  inventory_profit!: number;
  loyalty_as_payment!: number;
  tax_amount!: number;
  invoice_total!: number;
  amount_paid_in_cash!: number;
  amount_paid_in_debit!: number;
  birth_date?: Date | null;
  customer_name!: string;
  product_name!: string;

  // New wholesale cost and margin fields
  wholesale_cost?: number;
  profit_margin?: number;

  static tableName = "pos_data";

  flatten() {
    return {
      ...this,
      order_date: this.order_date?.toISOString(),
      birth_date: this.birth_date?.toISOString(),
    };
  }
}
