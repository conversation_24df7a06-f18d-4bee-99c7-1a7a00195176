/* eslint-disable indent */
import Router from "@koa/router";
import { locationRoleMiddleware } from "../locations/LocationService";
import { OpenAI } from "@langchain/openai";
import { PosDataVectorService } from "./PosDataVectorService";
import { logger } from "../config/logger";
import { PosData } from "./PosData";
import { VectorQueryResult } from "../core/VectorService";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";

const router = new Router({
  prefix: "/pos/analytics",
});

// Function to create OpenAI model instance
function createOpenAIModel() {
  return new OpenAI({
    modelName: "gpt-4.1-mini",
    temperature: 0.3, // Lower temperature for more precise analyses
    openAIApiKey: process.env.OPENAI_API_KEY,
    maxRetries: 3,
    timeout: 60000, // 60 seconds
  });
}

// Aggregate additional POS data for specific time periods when needed
async function aggregateAdditionalData(
  location_id: number,
  timeframe: {
    year: number | null;
    month: string | null;
    period: string | null;
  }
) {
  try {
    let query = PosData.query().where("location_id", location_id);

    // Apply time filters if provided
    if (timeframe.year) {
      const year = timeframe.year;
      query = query.whereRaw("EXTRACT(YEAR FROM order_date) = ?", [year]);
    }

    if (timeframe.month) {
      const monthIndex =
        [
          "january",
          "february",
          "march",
          "april",
          "may",
          "june",
          "july",
          "august",
          "september",
          "october",
          "november",
          "december",
        ].indexOf(timeframe.month.toLowerCase()) + 1;
      if (monthIndex > 0) {
        query = query.whereRaw("EXTRACT(MONTH FROM order_date) = ?", [
          monthIndex,
        ]);
      }
    }

    // Apply special time period filters
    if (timeframe.period) {
      const now = new Date();
      const period = timeframe.period.toLowerCase();

      if (period === "this year") {
        query = query.whereRaw("EXTRACT(YEAR FROM order_date) = ?", [
          now.getFullYear(),
        ]);
      } else if (period === "last year") {
        query = query.whereRaw("EXTRACT(YEAR FROM order_date) = ?", [
          now.getFullYear() - 1,
        ]);
      } else if (period === "this month") {
        query = query.whereRaw(
          "EXTRACT(YEAR FROM order_date) = ? AND EXTRACT(MONTH FROM order_date) = ?",
          [now.getFullYear(), now.getMonth() + 1]
        );
      } else if (period === "last month") {
        const lastMonthDate = new Date();
        lastMonthDate.setMonth(now.getMonth() - 1);
        query = query.whereRaw(
          "EXTRACT(YEAR FROM order_date) = ? AND EXTRACT(MONTH FROM order_date) = ?",
          [lastMonthDate.getFullYear(), lastMonthDate.getMonth() + 1]
        );
      } else if (period.startsWith("q") && period.length === 2) {
        // Handle quarterly queries (Q1, Q2, Q3, Q4)
        const quarter = parseInt(period.substring(1));
        if (quarter >= 1 && quarter <= 4) {
          const startMonth = (quarter - 1) * 3 + 1;
          const endMonth = quarter * 3;
          query = query.whereRaw(
            "EXTRACT(MONTH FROM order_date) >= ? AND EXTRACT(MONTH FROM order_date) <= ?",
            [startMonth, endMonth]
          );

          // If year is specified, add that filter too
          if (timeframe.year) {
            query = query.whereRaw("EXTRACT(YEAR FROM order_date) = ?", [
              timeframe.year,
            ]);
          }
        }
      }
    }

    // Get aggregate statistics
    const results = await query;

    if (results.length === 0) {
      return {
        recordCount: 0,
        totalRevenue: 0,
        averageRevenue: 0,
        totalProfit: 0,
        profitMargin: 0,
      };
    }

    // Calculate aggregates
    const totalRevenue = results.reduce(
      (sum: number, record: PosData) => sum + (record.gross_sales || 0),
      0
    );
    const totalProfit = results.reduce(
      (sum: number, record: PosData) => sum + (record.inventory_profit || 0),
      0
    );
    const profitMargin =
      totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

    // Get top products
    const productMap = new Map();
    for (const record of results) {
      if (!record.product_name) continue;

      const key = record.product_name;
      if (!productMap.has(key)) {
        productMap.set(key, {
          name: record.product_name,
          sales: 0,
          revenue: 0,
          profit: 0,
          category: record.master_category,
        });
      }

      const product = productMap.get(key);
      product.sales += 1;
      product.revenue += record.gross_sales || 0;
      product.profit += record.inventory_profit || 0;
    }

    // Convert to array and sort by revenue
    const topProducts = Array.from(productMap.values())
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    return {
      recordCount: results.length,
      totalRevenue,
      averageRevenue: totalRevenue / results.length,
      totalProfit,
      profitMargin,
      topProducts,
      timeframeSummary: generateTimeFrameSummary(timeframe),
    };
  } catch (error) {
    logger.error("Error aggregating additional POS data:", error);
    return null;
  }
}

// Generate a human-readable summary of the timeframe
function generateTimeFrameSummary(timeframe: {
  year: number | null;
  month: string | null;
  period: string | null;
}) {
  const parts = [];

  if (timeframe.period) {
    parts.push(timeframe.period);
  } else {
    if (timeframe.month) {
      parts.push(timeframe.month);
    }
    if (timeframe.year) {
      parts.push(timeframe.year.toString());
    }
  }

  return parts.length > 0 ? parts.join(" ") : "all time";
}

/**
 * @swagger
 * components:
 *   schemas:
 *     PosAnalyticsRequest:
 *       type: object
 *       required:
 *         - query
 *       properties:
 *         query:
 *           type: string
 *           description: Natural language query for POS data analysis
 *     PosAnalyticsResponse:
 *       type: object
 *       properties:
 *         analysis:
 *           type: string
 *           description: AI-generated analysis of the POS data
 *         relevantData:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               metadata:
 *                 type: object
 *                 additionalProperties: true
 *         aggregatedData:
 *           type: object
 *           properties:
 *             recordCount:
 *               type: integer
 *             totalRevenue:
 *               type: number
 *             averageRevenue:
 *               type: number
 *             totalProfit:
 *               type: number
 *             profitMargin:
 *               type: number
 *             topProducts:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                   sales:
 *                     type: integer
 *                   revenue:
 *                     type: number
 *                   profit:
 *                     type: number
 *                   category:
 *                     type: string
 *             timeframeSummary:
 *               type: string
 *         query_parameters:
 *           type: object
 *           properties:
 *             location_id:
 *               type: integer
 *             timeframes:
 *               type: object
 *               properties:
 *                 year:
 *                   type: integer
 *                   nullable: true
 *                 month:
 *                   type: string
 *                   nullable: true
 *                 period:
 *                   type: string
 *                   nullable: true
 *     PosVectorSearchRequest:
 *       type: object
 *       required:
 *         - query
 *       properties:
 *         query:
 *           type: string
 *           description: Search query for vector search
 *         limit:
 *           type: integer
 *           description: Maximum number of results to return
 *           default: 20
 *     PosVectorSearchResponse:
 *       type: object
 *       properties:
 *         query:
 *           type: string
 *         results:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               metadata:
 *                 type: object
 *                 additionalProperties: true
 *         count:
 *           type: integer
 *         location_id:
 *           type: integer
 *     PosReVectorizeRequest:
 *       type: object
 *       properties:
 *         batch_size:
 *           type: integer
 *           description: Number of records to process in each batch
 *           default: 100
 *     PosReVectorizeResponse:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *         status:
 *           type: string
 *         job_id:
 *           type: string
 *         job_type:
 *           type: string
 *     PosVectorizationStatusResponse:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *           enum: [not_started, processing, completed, failed]
 *         job_summary:
 *           type: object
 *           properties:
 *             isProcessing:
 *               type: boolean
 *             total:
 *               type: integer
 *             completed:
 *               type: integer
 *             failed:
 *               type: integer
 *             pending:
 *               type: integer
 *             processing:
 *               type: integer
 *             jobs:
 *               type: array
 *               items:
 *                 type: object
 *         data_count:
 *           type: integer
 */

/**
 * @swagger
 * tags:
 *   name: POS Analytics
 *   description: Point of Sale (POS) data analytics and vector search endpoints
 */

/**
 * @swagger
 * /pos/analytics/analyze:
 *   post:
 *     summary: Analyze POS data using natural language query
 *     tags: [POS Analytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PosAnalyticsRequest'
 *     responses:
 *       200:
 *         description: Analysis results
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PosAnalyticsResponse'
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       500:
 *         description: Analysis failed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 details:
 *                   type: string
 */
router.post("/analyze", locationRoleMiddleware("support"), async (ctx) => {
  const { query } = ctx.request.body;
  const location_id = ctx.state.location.id;

  try {
    if (!query || typeof query !== "string" || query.trim().length === 0) {
      ctx.status = 400;
      ctx.body = {
        error: "Query parameter is required and must be a non-empty string",
      };
      return;
    }

    // Query relevant POS data using vector search
    let queryResponse: VectorQueryResult[];
    try {
      queryResponse = await PosDataVectorService.queryPosData(
        query,
        location_id,
        20 // Increased to get more context
      );
    } catch (error) {
      logger.error("Error in vector search:", error);
      queryResponse = [];
    }

    // Get time period parameters from the query
    const timeframes = {
      year: extractYear(query),
      month: extractMonth(query),
      period: extractTimePeriod(query),
    };

    // Get additional aggregated data for the specified time period
    const aggregatedData = await aggregateAdditionalData(
      location_id,
      timeframes
    );

    // Format context for GPT-4
    const vectorContext = queryResponse
      .map((match) => JSON.stringify(match.metadata))
      .join("\n");

    // Generate analysis using GPT-4 with enhanced context
    const prompt = `
You are a cannabis retail analytics expert analyzing Point of Sale (POS) data for a dispensary.

USER QUERY: ${query}

RELEVANT POS RECORDS:
${vectorContext}

${
  aggregatedData
    ? `
AGGREGATED STATISTICS FOR ${
        aggregatedData.timeframeSummary?.toUpperCase() || "ALL TIME"
      }:
- Total Records: ${aggregatedData.recordCount}
- Total Revenue: $${aggregatedData.totalRevenue.toFixed(2)}
- Average Revenue Per Transaction: $${aggregatedData.averageRevenue.toFixed(2)}
- Total Profit: $${aggregatedData.totalProfit.toFixed(2)}
- Profit Margin: ${aggregatedData.profitMargin.toFixed(2)}%

${
  aggregatedData.topProducts && aggregatedData.topProducts.length > 0
    ? `
TOP PRODUCTS BY REVENUE:
${aggregatedData.topProducts
  .map(
    (p, i) =>
      `${i + 1}. ${p.name} (${
        p.category || "Unknown Category"
      }) - $${p.revenue.toFixed(2)} revenue, $${p.profit.toFixed(2)} profit`
  )
  .join("\n")}
`
    : ""
}
`
    : ""
}

INSTRUCTIONS:
1. Analyze the POS data to answer the user's query with specific numbers and insights.
2. Format all monetary values with '$' symbols and 2 decimal places.
3. Include relevant metrics like revenue, profit margins, sales trends, and product performance.
4. If the query is about specific time periods, focus your analysis accordingly.
5. Provide actionable business insights and recommendations based on the data.
6. If the data is insufficient to answer the query completely, acknowledge the limitations.
7. Structure your response in a clear, organized format with appropriate sections and bullet points as needed.

${
  timeframes.year
    ? `Focus specifically on data from the year ${timeframes.year}.`
    : ""
}
${
  timeframes.month ? `Focus specifically on data from ${timeframes.month}.` : ""
}
${timeframes.period ? `Focus on the ${timeframes.period} time period.` : ""}`;

    let analysis = "";
    try {
      const model = createOpenAIModel();
      analysis = await model.call(prompt);
    } catch (error) {
      logger.error("Error generating analysis with OpenAI:", error);
      analysis =
        "Unable to generate analysis due to an error. Please try again later.";
    }

    ctx.body = {
      analysis,
      relevantData: queryResponse,
      aggregatedData,
      query_parameters: {
        location_id,
        timeframes,
      },
    };
  } catch (error) {
    logger.error("Error in POS data analysis:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to analyze POS data",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /pos/analytics/vector-search:
 *   post:
 *     summary: Search POS data using vector similarity
 *     tags: [POS Analytics]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PosVectorSearchRequest'
 *     responses:
 *       200:
 *         description: Search results
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PosVectorSearchResponse'
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *       500:
 *         description: Search failed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 details:
 *                   type: string
 */
router.post(
  "/vector-search",
  locationRoleMiddleware("support"),
  async (ctx) => {
    const { query, limit } = ctx.request.body;
    const location_id = ctx.state.location.id;

    try {
      if (!query || typeof query !== "string" || query.trim().length === 0) {
        ctx.status = 400;
        ctx.body = {
          error: "Query parameter is required and must be a non-empty string",
        };
        return;
      }

      // Initialize index if needed
      await PosDataVectorService.ensureIndexExists();

      // Perform vector search
      const results = await PosDataVectorService.queryPosData(
        query,
        location_id,
        limit || 20
      );

      ctx.body = {
        query,
        results,
        count: results.length,
        location_id,
      };
    } catch (error) {
      logger.error("Error in POS vector search:", error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to search POS vectors",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

// Helper function to extract year from query
function extractYear(query: string): number | null {
  const yearRegex = /\b(20\d{2})\b/;
  const match = query.match(yearRegex);
  return match ? parseInt(match[1]) : null;
}

// Helper function to extract month from query
function extractMonth(query: string): string | null {
  const months = [
    "january",
    "february",
    "march",
    "april",
    "may",
    "june",
    "july",
    "august",
    "september",
    "october",
    "november",
    "december",
  ];

  const lowercaseQuery = query.toLowerCase();
  for (const month of months) {
    if (lowercaseQuery.includes(month)) {
      return month;
    }
  }
  return null;
}

// Helper function to extract time period from query
function extractTimePeriod(query: string): string | null {
  const periods = [
    "last year",
    "this year",
    "last month",
    "this month",
    "last week",
    "this week",
    "yesterday",
    "today",
    "quarter",
    "q1",
    "q2",
    "q3",
    "q4",
  ];

  const lowercaseQuery = query.toLowerCase();
  for (const period of periods) {
    if (lowercaseQuery.includes(period)) {
      return period;
    }
  }
  return null;
}

/**
 * @swagger
 * /pos/analytics/re-vectorize:
 *   post:
 *     summary: Start re-vectorization of POS data
 *     tags: [POS Analytics]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PosReVectorizeRequest'
 *     responses:
 *       200:
 *         description: Re-vectorization started
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PosReVectorizeResponse'
 *       500:
 *         description: Failed to start re-vectorization
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 details:
 *                   type: string
 */
router.post("/re-vectorize", locationRoleMiddleware("admin"), async (ctx) => {
  const location_id = ctx.state.location.id;
  const { batch_size = 100 } = ctx.request.body;

  try {
    // First ensure the index exists
    await PosDataVectorService.ensureIndexExists();

    // Start a job tracker for this process
    const jobType = "pos_data_vectorization";
    const tracker = OnboardingJobTracker.startJob(location_id, jobType);

    // Import PosDataVectorJob and ensure it's properly queued
    const PosDataVectorJob = require("../pos/PosDataVectorJob").default;

    // Create a new job with proper parameters
    const job = PosDataVectorJob.from({
      location_id,
      batch_size,
      last_processed_id: 0, // Start from the beginning
      retry_count: 0,
      failed_ids: [],
    });

    // Queue the job
    await job.queue();

    logger.info(`Re-vectorization job queued for location ${location_id}`);

    ctx.status = 200;
    ctx.body = {
      message: "POS data re-vectorization started",
      status: "processing",
      job_id: tracker.locationId,
      job_type: tracker.jobType,
    };
  } catch (error) {
    logger.error(
      `Error starting re-vectorization for location ${location_id}:`,
      error
    );
    ctx.status = 500;
    ctx.body = {
      error: "Failed to start re-vectorization",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /pos/analytics/vectorization-status:
 *   get:
 *     summary: Get vectorization status for POS data
 *     tags: [POS Analytics]
 *     responses:
 *       200:
 *         description: Vectorization status
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PosVectorizationStatusResponse'
 *       500:
 *         description: Failed to get vectorization status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 details:
 *                   type: string
 */
router.get(
  "/vectorization-status",
  locationRoleMiddleware("support"),
  async (ctx) => {
    const location_id = ctx.state.location.id;

    try {
      const jobType = "pos_data_vectorization";
      const summary = OnboardingJobTracker.getSummary(location_id);

      // Get current vectorization statistics
      const countResult = await PosData.query()
        .where("location_id", location_id)
        .count("id as total")
        .first();

      const total = countResult ? parseInt(countResult.total as string, 10) : 0;

      // Check if vectors exist in Pinecone even if no job is tracked
      // This handles cases where the job completed but the server restarted
      let status = summary.isProcessing
        ? "processing"
        : summary.failed > 0
        ? "failed"
        : summary.completed > 0
        ? "completed"
        : "not_started";

      // If status is not_started but we have data, try to verify if it's actually indexed
      if (status === "not_started" && total > 0) {
        try {
          // Check if index exists and has vectors
          await PosDataVectorService.ensureIndexExists();

          // Try a simple query to see if vectors exist
          const testResult = await PosDataVectorService.queryPosData(
            "test query to verify vectors exist",
            location_id,
            1 // Just need one result to confirm vectors exist
          );

          // If we got any results, vectors exist
          if (testResult && testResult.length > 0) {
            status = "completed";
            logger.info(
              `Detected existing vectors for location ${location_id} despite no job record`
            );
          }
        } catch (checkError) {
          // If this fails, just use the original status
          logger.warn(`Error checking vector existence: ${checkError}`);
        }
      }

      ctx.body = {
        status,
        job_summary: summary,
        data_count: total,
      };
    } catch (error) {
      logger.error(
        `Error getting vectorization status for location ${location_id}:`,
        error
      );
      ctx.status = 500;
      ctx.body = {
        error: "Failed to get vectorization status",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

export default router;
