/**
 * @swagger
 * components:
 *   schemas:
 *     PosData:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         location_id:
 *           type: integer
 *         order_date:
 *           type: string
 *           format: date-time
 *         data:
 *           type: object
 *           additionalProperties: true
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     PosImportResponse:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *         status:
 *           type: string
 *         reindexed:
 *           type: boolean
 *         errors:
 *           type: array
 *           items:
 *             type: string
 *         imported:
 *           type: integer
 *           nullable: true
 *         skipped:
 *           type: integer
 *           nullable: true
 *     PosDataListResponse:
 *       type: object
 *       properties:
 *         results:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/PosData'
 *         nextCursor:
 *           type: integer
 *           nullable: true
 *         prevCursor:
 *           type: integer
 *           nullable: true
 *         limit:
 *           type: integer
 *     PosVectorizationResponse:
 *       type: object
 *       properties:
 *         message:
 *           type: string
 *         status:
 *           type: string
 *         job_id:
 *           type: string
 *         job_type:
 *           type: string
 *         clean_start:
 *           type: boolean
 *     PosVectorizationStatus:
 *       type: object
 *       properties:
 *         status:
 *           type: string
 *         job_status:
 *           type: string
 *         job_summary:
 *           type: object
 *           properties:
 *             isProcessing:
 *               type: boolean
 *             total:
 *               type: integer
 *             completed:
 *               type: integer
 *             failed:
 *               type: integer
 *             pending:
 *               type: integer
 *             processing:
 *               type: integer
 *             jobs:
 *               type: array
 *               items:
 *                 type: object
 *         namespace:
 *           type: string
 *         vector_count:
 *           type: integer
 *         db_record_count:
 *           type: integer
 *         vector_status:
 *           type: string
 *         is_fully_indexed:
 *           type: boolean
 *         completion_percentage:
 *           type: integer
 *         error:
 *           type: string
 *           nullable: true
 *     PosIndicesStatus:
 *       type: object
 *       properties:
 *         location_id:
 *           type: integer
 *         indices_status:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               db_count:
 *                 type: integer
 *               vector_count:
 *                 type: integer
 *               status:
 *                 type: string
 *               namespace:
 *                 type: string
 *               error:
 *                 type: string
 *                 nullable: true
 *         needs_re_vectorization:
 *           type: array
 *           items:
 *             type: string
 *         has_mismatched_counts:
 *           type: boolean
 *         total_db_records:
 *           type: integer
 *         total_vector_records:
 *           type: integer
 */

/**
 * @swagger
 * tags:
 *   name: POS
 *   description: Point of Sale (POS) data management endpoints
 */

/* eslint-disable indent */
/**
 * POS Data Controller
 *
 * Provides endpoints for managing and querying Point of Sale (POS) data
 *
 * Available endpoints:
 *
 * - POST /pos/import
 *   Imports POS data from a file or POS provider integration
 *
 * - GET /pos/data
 *   Retrieves POS data records with pagination
 *
 * - POST /pos/re-vectorize
 *   Starts a vectorization job for POS data
 *
 * - GET /pos/vectorization-status
 *   Comprehensive endpoint for checking vectorization status, including:
 *   - Job status information from OnboardingJobTracker
 *   - Vector count statistics from Pinecone
 *   - Database record count for comparison
 *   - Detailed status classification and completion percentage
 *
 * - GET /pos/vector-stats (DEPRECATED)
 *   Deprecated endpoint, use /vectorization-status instead
 *
 * - GET /pos/check-indices-status
 *   Admin-only endpoint for checking all vector indices across the system
 */
import Router from "@koa/router";
import { locationRoleMiddleware } from "../locations/LocationService";
import { importFromPOS, importFromFile } from "./PosDataImportService";
import { PosData } from "./PosData";
import parse from "../storage/FileStream";
import { extractQueryParams } from "../utilities";
import { searchParamsSchema } from "../core/searchParams";
import { PosDataVectorService } from "./PosDataVectorService";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";
import { logger } from "../config/logger";
import {
  DataNormalizationService,
  NormalizedData,
} from "../core/DataNormalizationService";
import { Readable } from "stream";
import App from "../app";

const router = new Router({
  prefix: "/pos",
});

/**
 * @swagger
 * /pos/import:
 *   post:
 *     summary: Import POS data (JSON, file upload, or POS provider integration)
 *     tags: [POS]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               pos_data:
 *                 type: array
 *                 items:
 *                   type: object
 *                   additionalProperties: true
 *               pos_provider:
 *                 type: string
 *               reindex:
 *                 type: boolean
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               reindex:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: POS data import started
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PosImportResponse'
 *       400:
 *         description: Import failed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.post("/import", locationRoleMiddleware("editor"), async (ctx) => {
  const location_id = ctx.state.location.id;
  // Get reindex flag from query params or body
  const shouldReindex =
    ctx.request.query.reindex === "true" || ctx.request.body?.reindex === true;

  // Check if this is a JSON data import
  if (ctx.request.body?.pos_data) {
    try {
      // Create normalized data directly
      const normalizedData: NormalizedData = {
        type: "pos",
        data: Array.isArray(ctx.request.body.pos_data)
          ? ctx.request.body.pos_data
          : [ctx.request.body.pos_data],
        errors: [],
      };

      // Import the normalized data
      const result = await importFromFile(
        location_id,
        undefined,
        shouldReindex,
        normalizedData
      );

      ctx.status = 200;
      ctx.body = {
        message: "POS data imported successfully",
        status: "processing",
        reindexed: shouldReindex,
        ...result,
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        error:
          error instanceof Error ? error.message : "Failed to import POS data",
      };
    }
    return;
  }

  // Check if this is a POS provider integration request
  if (ctx.request.body?.pos_provider) {
    try {
      const result = await importFromPOS(
        location_id,
        ctx.request.body.pos_provider,
        shouldReindex
      );

      ctx.status = 200;
      ctx.body = {
        message: "POS integration configured successfully",
        status: "processing",
        reindexed: shouldReindex,
        ...result,
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        error:
          error instanceof Error
            ? error.message
            : "Failed to configure POS integration",
      };
    }
    return;
  }

  // Handle file upload
  try {
    const stream = await parse(ctx);

    // Import file data - validation and normalization happen inside importFromFile
    const result = await importFromFile(location_id, stream, shouldReindex);

    ctx.status = 200;
    ctx.body = {
      message: "File format validated. Import started in background.",
      status: "processing",
      reindexed: shouldReindex,
      ...result,
    };
  } catch (error) {
    ctx.status = 400;
    ctx.body = {
      error:
        error instanceof Error ? error.message : "Failed to import POS data",
    };
  }
});

/**
 * @swagger
 * /pos/data:
 *   get:
 *     summary: Get POS data records (paginated)
 *     tags: [POS]
 *     parameters:
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Sort field
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort direction
 *       - in: query
 *         name: cursor
 *         schema:
 *           type: integer
 *         description: Cursor for pagination
 *       - in: query
 *         name: page
 *         schema:
 *           type: string
 *           enum: [prev, next]
 *         description: Page direction
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Paginated list of POS data records
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PosDataListResponse'
 *       500:
 *         description: Failed to retrieve POS data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */
router.get("/data", locationRoleMiddleware("support"), async (ctx) => {
  const location_id = ctx.state.location.id;
  const params = extractQueryParams(ctx.query, searchParamsSchema);

  const query = PosData.query()
    .where("location_id", location_id)
    .orderBy(params.sort || "order_date", params.direction || "desc");

  if (params.cursor) {
    const operator = params.page === "prev" ? "<" : ">";
    query.where("id", operator, params.cursor);
  }

  const limit = params.limit || 25;
  const results = await query.limit(limit + 1);

  const hasMore = results.length > limit;
  if (hasMore) {
    results.pop(); // Remove the extra item we fetched
  }

  ctx.body = {
    results,
    nextCursor: hasMore ? results[results.length - 1].id : undefined,
    prevCursor: params.cursor,
    limit,
  };
});

/**
 * @swagger
 * /pos/re-vectorize:
 *   post:
 *     summary: Start vectorization job for POS data
 *     tags: [POS]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               batch_size:
 *                 type: integer
 *                 default: 100
 *               clean_start:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       200:
 *         description: Vectorization job started
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PosVectorizationResponse'
 *       500:
 *         description: Failed to start vectorization
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 details:
 *                   type: string
 */
// Add re-vectorization endpoint
router.post("/re-vectorize", locationRoleMiddleware("admin"), async (ctx) => {
  const location_id = ctx.state.location.id;
  const { batch_size = 100, clean_start = true } = ctx.request.body;

  try {
    // First ensure the index exists
    try {
      await PosDataVectorService.ensureIndexExists();
    } catch (initError) {
      logger.error(
        `Failed to initialize Pinecone or ensure index exists: ${initError}`
      );
      ctx.status = 500;
      ctx.body = {
        error: "Failed to initialize vector service",
        details: `Could not connect to Pinecone or create index: ${
          initError instanceof Error ? initError.message : String(initError)
        }`,
      };
      return;
    }

    // If clean_start is true, delete all existing vectors for this location first
    if (clean_start) {
      logger.info(
        `Deleting existing vectors for location ${location_id} before re-vectorization`
      );
      try {
        await PosDataVectorService.deletePosData(location_id);
        logger.info(
          `Successfully deleted existing vectors for location ${location_id}`
        );
      } catch (deleteError) {
        // Log the error but continue with the process - the namespace might not exist yet
        logger.warn(
          `Non-critical error when deleting existing vectors: ${deleteError}. Continuing with re-vectorization.`
        );
      }
    }

    // Start a job tracker for this process
    const jobType = "pos_data_vectorization";
    const tracker = OnboardingJobTracker.startJob(location_id, jobType);

    // Import PosDataVectorJob and ensure it's properly queued
    const PosDataVectorJob = require("./PosDataVectorJob").default;

    // Create a new job with proper parameters
    const job = PosDataVectorJob.from({
      location_id,
      batch_size,
      last_processed_id: 0, // Start from the beginning
      retry_count: 0,
      failed_ids: [],
    });

    // Queue the job
    await job.queue();

    logger.info(`Re-vectorization job queued for location ${location_id}`);

    ctx.status = 200;
    ctx.body = {
      message: "POS data re-vectorization started",
      status: "processing",
      job_id: tracker.locationId,
      job_type: tracker.jobType,
      clean_start,
    };
  } catch (error) {
    logger.error(
      `Error starting re-vectorization for location ${location_id}:`,
      error
    );
    ctx.status = 500;
    ctx.body = {
      error: "Failed to start re-vectorization",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /pos/vectorization-status:
 *   get:
 *     summary: Get vectorization status for POS data
 *     tags: [POS]
 *     responses:
 *       200:
 *         description: Vectorization status details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PosVectorizationStatus'
 *       500:
 *         description: Failed to get vectorization status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 details:
 *                   type: string
 */
router.get(
  "/vectorization-status",
  locationRoleMiddleware("support"),
  async (ctx) => {
    const location_id = ctx.state.location.id;

    try {
      const jobType = "pos_data_vectorization";
      const allJobsSummary = OnboardingJobTracker.getSummary(location_id);

      // Filter jobs to only include those of the pos_data_vectorization type
      const jobsOfThisType = allJobsSummary.jobs.filter(
        (job) => job.jobType === jobType
      );

      // Create filtered summary for this job type only
      const summary = {
        isProcessing: jobsOfThisType.some((job) => job.status === "processing"),
        total: jobsOfThisType.length,
        completed: jobsOfThisType.filter((job) => job.status === "completed")
          .length,
        failed: jobsOfThisType.filter((job) => job.status === "failed").length,
        pending: jobsOfThisType.filter((job) => job.status === "pending")
          .length,
        processing: jobsOfThisType.filter((job) => job.status === "processing")
          .length,
        jobs: jobsOfThisType,
      };

      // Get current vectorization statistics
      const countResult = await PosData.query()
        .where("location_id", location_id)
        .count("id as total")
        .first();

      const dbRecordCount = countResult
        ? parseInt(countResult.total as string, 10)
        : 0;

      // Get vector stats using namespace-based approach
      const vectorStats = await PosDataVectorService.getLocationStats(
        location_id
      );

      // Determine detailed vectorization status
      let vectorizationStatus = "unknown";
      if (dbRecordCount === 0) {
        vectorizationStatus = "no_data";
      } else if (vectorStats.recordCount === 0) {
        vectorizationStatus = "not_started";
      } else if (vectorStats.recordCount === dbRecordCount) {
        vectorizationStatus = "complete";
      } else if (vectorStats.recordCount < dbRecordCount) {
        vectorizationStatus = "incomplete";
      } else if (vectorStats.recordCount > dbRecordCount) {
        vectorizationStatus = "excess"; // More vectors than records
      }

      // Determine job status from tracker
      const jobStatus = summary.isProcessing
        ? "processing"
        : summary.failed > 0
        ? "failed"
        : summary.completed > 0
        ? "completed"
        : "not_started";

      // Combined status that takes both job and vector counts into account
      let combinedStatus = jobStatus;

      // If job shows completed but vectors don't match DB, override the status
      if (jobStatus === "completed" && vectorizationStatus !== "complete") {
        if (vectorStats.recordCount > 0) {
          combinedStatus = "incomplete";
          logger.info(
            `Job shows completed but only ${vectorStats.recordCount}/${dbRecordCount} vectors found for location ${location_id}`
          );
        } else {
          combinedStatus = "not_started";
          logger.info(
            `Job shows completed but no vectors found for location ${location_id}`
          );
        }
      }

      // If job not started but we have vectors, report proper status based on vector count
      if (jobStatus === "not_started" && vectorStats.recordCount > 0) {
        if (vectorStats.recordCount === dbRecordCount) {
          combinedStatus = "completed";
          logger.info(
            `No job record but full vectorization confirmed with ${vectorStats.recordCount}/${dbRecordCount} vectors for location ${location_id}`
          );
        } else {
          combinedStatus = "incomplete";
          logger.info(
            `No job record but partial vectorization detected with ${vectorStats.recordCount}/${dbRecordCount} vectors for location ${location_id}`
          );
        }
      }

      // Calculate completion percentage
      const completionPercentage =
        dbRecordCount > 0
          ? Math.round((vectorStats.recordCount / dbRecordCount) * 100)
          : 0;

      ctx.body = {
        // Combined final status
        status: combinedStatus,

        // Detailed information
        job_status: jobStatus,
        job_summary: summary,

        // Vector statistics
        namespace: vectorStats.namespace,
        vector_count: vectorStats.recordCount,
        db_record_count: dbRecordCount,
        vector_status: vectorizationStatus,
        is_fully_indexed: vectorStats.recordCount === dbRecordCount,
        completion_percentage: completionPercentage,

        // Include error information if any
        error: vectorStats.error,
      };
    } catch (error) {
      logger.error(
        `Error getting vectorization status for location ${location_id}:`,
        error
      );
      ctx.status = 500;
      ctx.body = {
        error: "Failed to get vectorization status",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /pos/check-indices-status:
 *   get:
 *     summary: Check status of all vector indices (admin only)
 *     tags: [POS]
 *     responses:
 *       200:
 *         description: Status of all vector indices
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PosIndicesStatus'
 *       500:
 *         description: Failed to check indices status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                 details:
 *                   type: string
 */
router.get(
  "/check-indices-status",
  locationRoleMiddleware("admin"),
  async (ctx) => {
    const location_id = ctx.state.location.id;

    try {
      // Import all vector services
      const PosDataVectorService =
        require("./PosDataVectorService").PosDataVectorService;
      const ProductDataVectorService =
        require("../products/ProductDataVectorService").ProductDataVectorService;
      const UserDataVectorService =
        require("../users/UserDataVectorService").UserDataVectorService;
      const CompetitorDataVectorService =
        require("../competitors/CompetitorDataVectorService").CompetitorDataVectorService;
      const RetailerDataVectorService =
        require("../retailers/RetailerDataVectorService").RetailerDataVectorService;
      const ReviewDataVectorService =
        require("../reviews/ReviewDataVectorService").ReviewDataVectorService;

      // Check POS data
      const posCountResult = await PosData.query()
        .where("location_id", location_id)
        .count("id as total")
        .first();
      const posDbCount = posCountResult
        ? parseInt(posCountResult.total as string, 10)
        : 0;
      const posStats = await PosDataVectorService.getLocationStats(location_id);

      // Check Product data
      const productCountResult = await App.main
        .db("products")
        .where("location_id", location_id)
        .count("id as total")
        .first();
      const productDbCount = productCountResult
        ? parseInt(productCountResult.total as string, 10)
        : 0;
      const productStats = await ProductDataVectorService.getLocationStats(
        location_id
      );

      // Check User data
      const userCountResult = await App.main
        .db("users")
        .where("location_id", location_id)
        .count("id as total")
        .first();
      const userDbCount = userCountResult
        ? parseInt(userCountResult.total as string, 10)
        : 0;
      const userStats = await UserDataVectorService.getLocationStats(
        location_id
      );

      // Check Competitor data
      const competitorCountResult = await App.main
        .db("location_competitors")
        .where("location_id", location_id)
        .count("id as total")
        .first();
      const competitorDbCount = competitorCountResult
        ? parseInt(competitorCountResult.total as string, 10)
        : 0;
      const competitorStats =
        await CompetitorDataVectorService.getLocationStats(location_id);

      // Check global retailer and review data (not location-specific)
      // Since these are global, we include them for information but don't count mismatches
      const retailerCountResult = await App.main
        .db("retailers")
        .count("id as total")
        .first();
      const retailerDbCount = retailerCountResult
        ? parseInt(retailerCountResult.total as string, 10)
        : 0;
      const retailerStats = await RetailerDataVectorService.getIndexStats(); // No location-specific method

      const reviewCountResult = await App.main
        .db("reviews")
        .count("id as total")
        .first();
      const reviewDbCount = reviewCountResult
        ? parseInt(reviewCountResult.total as string, 10)
        : 0;
      const reviewStats = await ReviewDataVectorService.getIndexStats(); // No location-specific method

      // Determine overall status
      const indicesStatus = [
        {
          name: "pos",
          db_count: posDbCount,
          vector_count: posStats.recordCount,
          status: getStatus(posDbCount, posStats.recordCount),
          namespace: posStats.namespace,
          error: posStats.error,
        },
        {
          name: "products",
          db_count: productDbCount,
          vector_count: productStats.recordCount,
          status: getStatus(productDbCount, productStats.recordCount),
          namespace: productStats.namespace,
          error: productStats.error,
        },
        {
          name: "users",
          db_count: userDbCount,
          vector_count: userStats.recordCount,
          status: getStatus(userDbCount, userStats.recordCount),
          namespace: userStats.namespace,
          error: userStats.error,
        },
        {
          name: "competitors",
          db_count: competitorDbCount,
          vector_count: competitorStats.recordCount,
          status: getStatus(competitorDbCount, competitorStats.recordCount),
          namespace: competitorStats.namespace,
          error: competitorStats.error,
        },
        // Include global indices for information
        {
          name: "retailers",
          db_count: retailerDbCount,
          vector_count: retailerStats.totalVectorCount || 0,
          status: "global", // These are global, not location-specific
          namespace: "global",
          error: null,
        },
        {
          name: "reviews",
          db_count: reviewDbCount,
          vector_count: reviewStats.totalVectorCount || 0,
          status: "global", // These are global, not location-specific
          namespace: "global",
          error: null,
        },
      ];

      // Determine which indices need re-vectorization
      const needsReVectorization = indicesStatus
        .filter(
          (index) =>
            index.status === "incomplete" || index.status === "not_started"
        )
        .filter((index) => index.db_count > 0) // Only include if there's data to vectorize
        .map((index) => index.name);

      // Check if any location-specific indices have mismatched counts
      const hasMismatchedCounts = indicesStatus
        .filter((index) =>
          ["pos", "products", "users", "competitors"].includes(index.name)
        )
        .some(
          (index) => index.status !== "complete" && index.status !== "no_data"
        );

      ctx.status = 200;
      ctx.body = {
        location_id,
        indices_status: indicesStatus,
        needs_re_vectorization: needsReVectorization,
        has_mismatched_counts: hasMismatchedCounts,
        total_db_records:
          posDbCount + productDbCount + userDbCount + competitorDbCount,
        total_vector_records:
          posStats.recordCount +
          productStats.recordCount +
          userStats.recordCount +
          competitorStats.recordCount,
      };
    } catch (error) {
      logger.error(
        `Error checking indices status for location ${location_id}:`,
        error
      );
      ctx.status = 500;
      ctx.body = {
        error: "Failed to check indices status",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

// Helper function to determine status
function getStatus(dbCount: number, vectorCount: number): string {
  if (dbCount === 0) {
    return "no_data";
  } else if (vectorCount === 0) {
    return "not_started";
  } else if (vectorCount === dbCount) {
    return "complete";
  } else if (vectorCount < dbCount) {
    return "incomplete";
  } else if (vectorCount > dbCount) {
    return "excess";
  }
  return "unknown";
}

export default router;
