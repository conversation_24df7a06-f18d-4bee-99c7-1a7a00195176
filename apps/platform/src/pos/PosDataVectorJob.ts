import Job from "../queue/Job";
import { PosData } from "./PosData";
import { PosDataVectorService } from "./PosDataVectorService";
import { logger } from "../config/logger";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";

const MAX_RETRIES = 3;
const FALLBACK_BATCH_SIZE = 50;

interface PosDataVectorParams {
  location_id: number;
  batch_size?: number;
  last_processed_id?: number;
  retry_count?: number;
  failed_ids?: number[];
}

export default class PosDataVectorJob extends Job {
  static $name = "pos_data_vector";

  location_id!: number;
  batch_size?: number;
  last_processed_id?: number;
  retry_count?: number;
  failed_ids?: number[];

  static from(params: PosDataVectorParams) {
    logger.info(
      `Creating POS data vector job for location ${params.location_id}`,
      {
        location_id: params.location_id,
        batch_size: params.batch_size || 100,
        last_processed_id: params.last_processed_id,
        retry_count: params.retry_count || 0,
        failed_ids_count: params.failed_ids?.length || 0,
      }
    );

    return new this({
      location_id: params.location_id,
      batch_size: params.batch_size || 100,
      last_processed_id: params.last_processed_id,
      retry_count: params.retry_count || 0,
      failed_ids: params.failed_ids || [],
    });
  }

  static async handler({
    location_id,
    batch_size,
    last_processed_id,
    retry_count = 0,
    failed_ids = [],
  }: PosDataVectorParams) {
    try {
      // Track job in OnboardingJobTracker
      const jobType = "pos_data_vectorization";

      // Ensure the Pinecone index exists before processing
      await PosDataVectorService.ensureIndexExists();

      // Two paths: retry failed IDs or process new ones
      let posData: PosData[] = [];

      if (failed_ids && failed_ids.length > 0) {
        // If we have failed IDs to retry, process those first
        logger.info(
          `Retrying vectorization for ${failed_ids.length} failed records`
        );
        posData = await PosData.query().whereIn("id", failed_ids);
      } else {
        // Process normal queue of unprocessed records
        const effectiveBatchSize = batch_size || FALLBACK_BATCH_SIZE;
        const query = PosData.query()
          .where("location_id", location_id)
          .orderBy("id", "asc")
          .limit(effectiveBatchSize);

        if (last_processed_id) {
          query.where("id", ">", last_processed_id);
        }

        posData = await query;
      }

      if (posData.length === 0) {
        logger.info(`No more POS data to process for location ${location_id}`);
        // Mark job as complete in tracker
        OnboardingJobTracker.completeJob(location_id, jobType);
        return;
      }

      // Process the batch using PosDataVectorService
      const result = await PosDataVectorService.upsertPosData(posData);

      // Log detailed results
      logger.info(
        `Vectorized ${result.successCount} POS records for location ${location_id}, ${result.errorCount} errors`
      );

      // Track the highest ID we've processed (for non-retry jobs)
      const highestProcessedId =
        !failed_ids || failed_ids.length === 0
          ? posData[posData.length - 1].id
          : last_processed_id;

      // Handle failed records with retry logic
      if (
        result.errorCount > 0 &&
        result.failedIds &&
        result.failedIds.length > 0
      ) {
        // Extract numeric IDs from the returned failedIds (which include "pos_" prefix)
        const failedNumericIds = result.failedIds
          .map((id) => {
            const match = id.match(/^pos_(\d+)$/);
            return match ? parseInt(match[1], 10) : null;
          })
          .filter((id): id is number => id !== null);

        // If we have failures and haven't exceeded max retries, queue a retry job
        if (failedNumericIds.length > 0 && (retry_count || 0) < MAX_RETRIES) {
          logger.warn(
            `Scheduling retry ${(retry_count || 0) + 1}/${MAX_RETRIES} for ${
              failedNumericIds.length
            } failed records`
          );

          await PosDataVectorJob.from({
            location_id,
            batch_size: Math.max(10, Math.floor((batch_size || 100) / 2)),
            last_processed_id: highestProcessedId,
            retry_count: (retry_count || 0) + 1,
            failed_ids: failedNumericIds,
          })
            .delay(2000 * (retry_count || 0) + 1000) // Increasing delay with each retry
            .queue();
        } else if (failedNumericIds.length > 0) {
          // We've exceeded max retries, log a critical error
          logger.error(
            `Failed to vectorize ${failedNumericIds.length} POS records after ${MAX_RETRIES} retries`
          );
          OnboardingJobTracker.failJob(
            location_id,
            jobType,
            `Failed to vectorize ${failedNumericIds.length} POS records after ${MAX_RETRIES} retries`
          );
        }
      }

      // If we're not in retry mode and have processed a full batch, queue the next job for new records
      if (
        (!failed_ids || failed_ids.length === 0) &&
        posData.length === batch_size
      ) {
        logger.info(
          `Queueing next batch of POS data after ID ${highestProcessedId}`
        );

        await PosDataVectorJob.from({
          location_id,
          batch_size,
          last_processed_id: highestProcessedId,
        })
          .delay(1000) // Add a small delay between batches
          .queue();
      } else {
        // This was the last batch, mark as complete
        OnboardingJobTracker.completeJob(location_id, jobType);
      }

      return true;
    } catch (error) {
      logger.error("Error in PosDataVectorJob:", error);

      // Track failure in job tracker
      OnboardingJobTracker.failJob(
        location_id,
        "pos_data_vectorization",
        error instanceof Error ? error.message : String(error)
      );

      // If we encounter a critical error (not just record processing failure),
      // retry the entire job with a longer delay, up to MAX_RETRIES
      if ((retry_count || 0) < MAX_RETRIES) {
        const nextRetryCount = (retry_count || 0) + 1;
        const delayMs = Math.pow(2, nextRetryCount) * 1000; // Exponential backoff

        logger.info(
          `Scheduling full job retry ${nextRetryCount}/${MAX_RETRIES} after ${delayMs}ms`
        );

        await PosDataVectorJob.from({
          location_id,
          batch_size,
          last_processed_id,
          retry_count: nextRetryCount,
          failed_ids,
        })
          .delay(delayMs)
          .queue();
      } else {
        logger.error(
          `Failed to process POS data batch after ${MAX_RETRIES} retries`
        );
      }

      throw error;
    }
  }
}
