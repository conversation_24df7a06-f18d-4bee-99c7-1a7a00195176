import { PosData } from "./PosData";
import { VectorService, VectorData } from "../core/VectorService";
import { logger } from "../config/logger";

const POS_INDEX = "pos-data";

export class PosDataVectorService {
  private static vectorService: VectorService | null = null;

  /**
   * Get the vector service instance
   */
  private static getVectorService(): VectorService {
    if (!this.vectorService) {
      this.vectorService = VectorService.getInstance();
    }
    return this.vectorService;
  }

  static async initialize() {
    await this.getVectorService().initialize();
  }

  /**
   * Converts a location ID to a namespace string
   */
  private static getNamespace(locationId: number): string {
    return `location-${locationId}`;
  }

  static async upsertPosData(posData: PosData[]) {
    try {
      if (!posData.length) {
        logger.warn("No POS data provided to upsert, skipping vectorization");
        return { successCount: 0, errorCount: 0, failedIds: [] };
      }

      // Group records by location ID for namespace-based processing
      const recordsByLocation = posData.reduce((groups, record) => {
        const namespace = this.getNamespace(record.location_id);
        if (!groups[namespace]) {
          groups[namespace] = [];
        }
        groups[namespace].push(record);
        return groups;
      }, {} as Record<string, PosData[]>);

      const totalResults = {
        successCount: 0,
        errorCount: 0,
        failedIds: [] as string[],
      };

      // Process each location group with its own namespace
      for (const [namespace, records] of Object.entries(recordsByLocation)) {
        // Convert POS data to vector format with enhanced text representation
        const vectorData: VectorData[] = records.map((record) => ({
          id: `pos_${record.id}`,
          text: this.generateEnhancedText(record),
          metadata: {
            location_id: record.location_id,
            source_type: "pos",
            source_id: record.id.toString(),
            product_name: record.product_name,
            master_category: record.master_category,
            gross_sales: record.gross_sales,
            net_sales: record.net_sales,
            order_date: record.order_date.toISOString(),
            customer_type: record.customer_type,
            budtender_name: record.budtender_name,
            inventory_profit: record.inventory_profit,
            inventory_cost: record.inventory_cost,
            discounted_amount: record.discounted_amount,
            tax_amount: record.tax_amount,
            invoice_total: record.invoice_total,
            amount_paid_in_cash: record.amount_paid_in_cash,
            amount_paid_in_debit: record.amount_paid_in_debit,
            year: new Date(record.order_date).getFullYear(),
            month: new Date(record.order_date).getMonth() + 1,
            day: new Date(record.order_date).getDate(),
            created_at: Date.now(),
            updated_at: Date.now(),
          },
        }));

        // Use centralized vector service to upsert data with namespace
        const result = await this.getVectorService().upsertVectors(
          POS_INDEX,
          vectorData,
          undefined, // Use default batch size
          namespace
        );

        // Aggregate results
        totalResults.successCount += result.successCount;
        totalResults.errorCount += result.errorCount;
        totalResults.failedIds = totalResults.failedIds.concat(
          result.failedIds
        );

        logger.info(
          `POS data vectorization for namespace ${namespace} complete: ${result.successCount} succeeded, ${result.errorCount} failed`
        );
      }

      return totalResults;
    } catch (error) {
      logger.error("Error upserting POS data vectors:", error);
      throw error;
    }
  }

  static async queryPosData(
    query: string,
    locationId: number,
    topK: number = 10
  ) {
    try {
      if (!query) {
        throw new Error("Empty query provided to POS data vector search");
      }

      // Add pre-processing for query to improve search results
      const enhancedQuery = this.enhanceSearchQuery(query);
      const namespace = this.getNamespace(locationId);

      return await this.getVectorService().queryVectors(
        POS_INDEX,
        enhancedQuery,
        {}, // No additional filter needed since we're using namespaces
        topK,
        namespace
      );
    } catch (error) {
      logger.error("Error querying POS data vectors:", error);
      throw error;
    }
  }

  /**
   * Creates an enhanced text representation of POS data for better semantic matching
   */
  private static generateEnhancedText(record: PosData): string {
    // Base text with core information
    let text =
      `Product: ${record.product_name}, Category: ${record.master_category}, ` +
      `Sales Amount: $${record.gross_sales}, Net Sales: $${record.net_sales}, ` +
      `Date: ${record.order_date}, Customer Type: ${record.customer_type}, ` +
      `Profit: $${record.inventory_profit}, Cost: $${record.inventory_cost}, ` +
      `Discount: $${record.discounted_amount}, Tax: $${record.tax_amount}, ` +
      `Budtender: ${record.budtender_name}, Location: ${record.location_name}, ` +
      `Total Invoice: $${record.invoice_total}`;

    // Add payment method information
    const paymentMethods = [];
    if (record.amount_paid_in_cash > 0) paymentMethods.push("Cash");
    if (record.amount_paid_in_debit > 0) paymentMethods.push("Debit");
    if (paymentMethods.length > 0) {
      text += `, Payment Method: ${paymentMethods.join(", ")}`;
    }

    // Add time-based keywords to improve temporal queries
    const date = new Date(record.order_date);
    const year = date.getFullYear();
    const month = date.toLocaleString("en-US", { month: "long" });
    const quarter = `Q${Math.floor(date.getMonth() / 3) + 1}`;

    text += `. This sale occurred in ${month} ${year}, ${quarter} of ${year}.`;

    // Add profitability keywords for business queries
    const profitMargin = record.inventory_profit / record.gross_sales;
    let profitabilityText = "";
    if (profitMargin > 0.5) {
      profitabilityText = "This was a high-margin sale. ";
    } else if (profitMargin > 0.3) {
      profitabilityText = "This was a medium-margin sale. ";
    } else {
      profitabilityText = "This was a low-margin sale. ";
    }

    text += ` ${profitabilityText}`;

    return text;
  }

  /**
   * Enhances search queries for better POS data retrieval
   */
  private static enhanceSearchQuery(query: string): string {
    // Convert simple product queries to more specific search terms
    // For example: "How much did we sell?" -> "total sales revenue gross"
    let enhancedQuery = query;

    // Keywords for common financial queries
    if (
      query.toLowerCase().includes("top selling") ||
      query.toLowerCase().includes("best selling") ||
      query.toLowerCase().includes("most popular")
    ) {
      enhancedQuery += " highest sales revenue product";
    }

    if (
      query.toLowerCase().includes("profit") ||
      query.toLowerCase().includes("margin")
    ) {
      enhancedQuery += " inventory profit sales margin";
    }

    if (
      query.toLowerCase().includes("revenue") ||
      query.toLowerCase().includes("sales")
    ) {
      enhancedQuery += " gross sales net revenue";
    }

    // Enhance time-based queries
    if (
      query.toLowerCase().includes("month") ||
      this.detectMonthInQuery(query)
    ) {
      enhancedQuery += " monthly data temporal time period";
    }

    return enhancedQuery;
  }

  /**
   * Detect if a month name is present in the query
   */
  private static detectMonthInQuery(query: string): boolean {
    const months = [
      "january",
      "february",
      "march",
      "april",
      "may",
      "june",
      "july",
      "august",
      "september",
      "october",
      "november",
      "december",
      "jan",
      "feb",
      "mar",
      "apr",
      "jun",
      "jul",
      "aug",
      "sep",
      "oct",
      "nov",
      "dec",
    ];

    const lowercaseQuery = query.toLowerCase();
    return months.some((month) => lowercaseQuery.includes(month));
  }

  /**
   * Direct database query for accurate monthly sales totals
   * This bypasses vector search to provide precise aggregate numbers
   */
  static async getTotalSalesByMonth(
    locationId: number,
    month: string,
    year?: number
  ): Promise<{
    totalSales: number;
    recordCount: number;
    monthName: string;
    yearValue: number;
  }> {
    try {
      // Input validation
      if (!locationId) {
        throw new Error("Location ID is required");
      }

      if (!month || typeof month !== "string") {
        throw new Error("Month name is required and must be a string");
      }

      // Import the database model
      const { PosData } = await import("./PosData");
      const { logger } = await import("../config/logger");

      // Normalize the month name (handle spelling issues like "janurary")
      const normalizedMonth = this.normalizeMonthName(month);

      // Convert month name to month index (1-12)
      const months = [
        "january",
        "february",
        "march",
        "april",
        "may",
        "june",
        "july",
        "august",
        "september",
        "october",
        "november",
        "december",
      ];
      const monthIndex =
        months.findIndex(
          (m) => m.toLowerCase() === normalizedMonth.toLowerCase()
        ) + 1;

      if (monthIndex === 0) {
        logger.warn({
          message: `Invalid month name, attempting fuzzy match: ${month}`,
          normalized: normalizedMonth,
        });

        // Try a fuzzy match for month names (handle typos)
        const fuzzyMatch = this.fuzzyMatchMonth(normalizedMonth);
        if (fuzzyMatch) {
          logger.info({
            message: `Fuzzy matched month name`,
            original: month,
            matched_to: fuzzyMatch,
          });
          const fuzzyMonthIndex =
            months.findIndex(
              (m) => m.toLowerCase() === fuzzyMatch.toLowerCase()
            ) + 1;
          if (fuzzyMonthIndex > 0) {
            return this.getTotalSalesByMonth(locationId, fuzzyMatch, year);
          }
        }

        throw new Error(`Invalid month name: ${month}`);
      }

      // If year is not provided, use current year
      const yearValue = year || new Date().getFullYear();

      logger.info({
        message: "Querying total sales by month directly from database",
        location_id: locationId,
        month: normalizedMonth,
        month_index: monthIndex,
        year: yearValue,
      });

      try {
        // Direct database query using Knex
        const query = PosData.query()
          .where("location_id", locationId)
          .whereRaw("EXTRACT(MONTH FROM order_date) = ?", [monthIndex])
          .whereRaw("EXTRACT(YEAR FROM order_date) = ?", [yearValue]);

        // Execute the query to get all matching records
        const results = await query;

        // Calculate the total sales - ensure we're working with numbers, not strings
        let totalSales = 0;
        let invalidRecords = 0;

        for (const record of results) {
          try {
            // Ensure gross_sales is treated as a number
            const saleAmount =
              typeof record.gross_sales === "string"
                ? parseFloat(record.gross_sales)
                : record.gross_sales || 0;

            // Validate amount is a number and not NaN
            if (!isNaN(saleAmount)) {
              // Add to running total
              totalSales += saleAmount;
            } else {
              invalidRecords++;
            }
          } catch (recordError) {
            // If any single record has an error, log it but continue processing
            invalidRecords++;
            logger.warn({
              message: "Error processing sales record",
              record_id: record.id,
              error:
                recordError instanceof Error
                  ? recordError.message
                  : String(recordError),
            });
          }
        }

        logger.info({
          message: "Monthly sales query results",
          record_count: results.length,
          invalid_records: invalidRecords,
          total_sales: totalSales.toFixed(2), // Format as fixed decimal for consistent logging
          month: normalizedMonth,
          year: yearValue,
          successful_records: results.length - invalidRecords,
        });

        return {
          totalSales,
          recordCount: results.length,
          monthName: normalizedMonth,
          yearValue,
        };
      } catch (dbError) {
        logger.error({
          message: "Database error in monthly sales query",
          error: dbError instanceof Error ? dbError.message : String(dbError),
          location_id: locationId,
          month: normalizedMonth,
          year: yearValue,
        });
        throw dbError;
      }
    } catch (error) {
      logger.error({
        message: "Error getting total sales by month",
        error_message: error instanceof Error ? error.message : String(error),
        error_stack: error instanceof Error ? error.stack : undefined,
        location_id: locationId,
        month,
        year,
      });
      throw error;
    }
  }

  /**
   * Normalize month name to handle common typos
   */
  private static normalizeMonthName(monthName: string): string {
    // Handle common misspellings of month names
    const monthMapping: Record<string, string> = {
      janurary: "january",
      januaury: "january",
      feburary: "february",
      febuary: "february",
      march: "march",
      april: "april",
      may: "may",
      june: "june",
      july: "july",
      august: "august",
      sept: "september",
      septmber: "september",
      ocober: "october",
      octber: "october",
      novemeber: "november",
      decemeber: "december",
      dec: "december",
      nov: "november",
      oct: "october",
      sep: "september",
      aug: "august",
      jul: "july",
      jun: "june",
      apr: "april",
      mar: "march",
      feb: "february",
      jan: "january",
    };

    const lowercased = monthName.toLowerCase().trim();
    const result = monthMapping[lowercased] || lowercased;

    // Log if we found a correction
    if (result !== lowercased) {
      logger.info({
        message: "Normalized month name spelling",
        original: monthName,
        normalized: result,
      });
    }

    return result;
  }

  /**
   * Attempt a fuzzy match on month names
   */
  private static fuzzyMatchMonth(monthName: string): string | null {
    const months = [
      "january",
      "february",
      "march",
      "april",
      "may",
      "june",
      "july",
      "august",
      "september",
      "october",
      "november",
      "december",
    ];

    // Try to match by first 3 letters
    if (monthName.length >= 3) {
      const firstThree = monthName.substring(0, 3).toLowerCase();
      for (const month of months) {
        if (month.substring(0, 3) === firstThree) {
          return month;
        }
      }
    }

    return null;
  }

  /**
   * Validates that the Pinecone index exists and creates it if needed
   */
  static async ensureIndexExists() {
    try {
      const vectorService = this.getVectorService();
      await vectorService.initialize();

      // Attempt to create the index if it doesn't exist
      await vectorService.createIndex(POS_INDEX, {
        dimension: 3072, // Updated dimension for text-embedding-3-large model (was 1536)
        metric: "cosine",
        serverless: {
          cloud: "aws", // Specify AWS instead of GCP
          region: "us-east-1", // Specify the us-east-1 region as required for free tier
        },
      });

      logger.info(`POS index '${POS_INDEX}' verification complete`);
      return true;
    } catch (error) {
      logger.error(`Failed to verify POS index: ${error}`);
      throw error;
    }
  }

  /**
   * Gets vector statistics for a specific location, including vector count
   */
  static async getLocationStats(locationId: number) {
    try {
      const vectorService = this.getVectorService();
      const namespace = this.getNamespace(locationId);
      const stats = await vectorService.getIndexStats(POS_INDEX);

      // Check if the namespace exists in the stats
      if (stats.namespaces && stats.namespaces[namespace]) {
        return {
          recordCount: stats.namespaces[namespace].recordCount || 0,
          vectorCount: stats.namespaces[namespace].recordCount || 0, // Maintain backward compatibility
          namespace,
        };
      }

      return {
        recordCount: 0,
        vectorCount: 0, // Maintain backward compatibility
        namespace,
      };
    } catch (error) {
      logger.error(`Error getting stats for location ${locationId}:`, error);
      return {
        recordCount: 0,
        vectorCount: 0, // Maintain backward compatibility
        namespace: this.getNamespace(locationId),
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  static async deletePosData(locationId: number) {
    try {
      if (!locationId) {
        logger.warn(
          "Attempted to delete POS data without specifying location_id"
        );
        return false;
      }

      // First, ensure the index exists to avoid errors
      await this.ensureIndexExists();

      // Delete all vectors in this location's namespace
      const namespace = this.getNamespace(locationId);
      logger.info(
        `Deleting POS data vectors for location ${locationId} (namespace: ${namespace})`
      );
      const vectorService = this.getVectorService();
      return await vectorService.deleteNamespace(POS_INDEX, namespace);
    } catch (error) {
      logger.error("Error deleting POS data vectors:", error);
      throw error;
    }
  }

  /**
   * Empties the entire POS index, removing all vectors across all namespaces
   * Use with caution as this will delete all POS data vectors
   */
  static async emptyIndex() {
    try {
      logger.warn(
        "Emptying entire POS index - this will delete ALL POS vectors for ALL locations"
      );
      const vectorService = this.getVectorService();
      return await vectorService.emptyIndex(POS_INDEX);
    } catch (error) {
      logger.error("Error emptying POS index:", error);
      throw error;
    }
  }

  /**
   * Hybrid search approach that combines vector search with direct database queries
   * This method intelligently routes between semantic search and direct database queries
   * based on the nature of the query without requiring explicit query classification
   */
  static async hybridSearch(
    query: string,
    locationId: number,
    options: {
      topK?: number;
      alpha?: number; // Weight between semantic (1.0) and exact (0.0) matching, default 0.5
      includeMetadata?: boolean;
    } = {}
  ) {
    try {
      const { topK = 10, alpha = 0.5, includeMetadata = true } = options;

      logger.info({
        message: "Performing hybrid search",
        query,
        location_id: locationId,
        topK,
        alpha,
      });

      // Extract potential month/year from query for direct lookups
      const { month, year } = this.extractMonthYearFromQuery(query);

      const results = [];
      let dbResults = null;
      let vectorResults = null;

      // If month is detected, try direct database query first
      if (month) {
        try {
          dbResults = await this.getTotalSalesByMonth(
            locationId,
            month,
            year !== null ? year : undefined
          );
          logger.info({
            message: "Retrieved direct database results",
            month: dbResults.monthName,
            year: dbResults.yearValue,
            total_sales: dbResults.totalSales,
            record_count: dbResults.recordCount,
          });
        } catch (dbError) {
          logger.warn({
            message: "Database query failed, falling back to vector search",
            error: dbError instanceof Error ? dbError.message : String(dbError),
          });
        }
      }

      // Perform vector search in parallel or as fallback
      try {
        vectorResults = await this.queryPosData(query, locationId, topK);
        logger.info({
          message: "Retrieved vector search results",
          match_count: vectorResults.length,
        });
      } catch (vectorError) {
        logger.error({
          message: "Vector search failed",
          error:
            vectorError instanceof Error
              ? vectorError.message
              : String(vectorError),
        });

        // If both searches failed, we're out of options
        if (!dbResults) {
          throw new Error("Both database and vector searches failed");
        }
      }

      // Combine and rank results based on alpha weight
      if (dbResults && vectorResults) {
        // For monthly totals, we create a hybrid response that includes both
        // exact figures from DB and contextual information from vector search
        return {
          directResults: dbResults,
          semanticResults: vectorResults,
          combinedResponse: this.formatHybridResponse(
            dbResults,
            vectorResults,
            query
          ),
        };
      } else if (dbResults) {
        // Only direct database results available
        return {
          directResults: dbResults,
          semanticResults: null,
          combinedResponse: this.formatDatabaseResponse(dbResults, query),
        };
      } else if (vectorResults) {
        // Only vector results available
        return {
          directResults: null,
          semanticResults: vectorResults,
          combinedResponse: null,
        };
      }

      // No results found
      return {
        directResults: null,
        semanticResults: null,
        combinedResponse: null,
      };
    } catch (error) {
      logger.error({
        message: "Error in hybrid search",
        error_message: error instanceof Error ? error.message : String(error),
        query,
        location_id: locationId,
      });
      throw error;
    }
  }

  /**
   * Format a hybrid response combining direct database results with vector search context
   */
  private static formatHybridResponse(
    dbResults: {
      totalSales: number;
      recordCount: number;
      monthName: string;
      yearValue: number;
    },
    vectorResults: any[],
    originalQuery: string
  ): string {
    // Extract key insights from vector results to supplement the exact data
    const formattedSales = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(dbResults.totalSales);

    let response = `Based on ${dbResults.recordCount} transactions, the total sales for ${dbResults.monthName} ${dbResults.yearValue} were ${formattedSales}.`;

    // Add contextual information from vector search
    if (vectorResults && vectorResults.length > 0) {
      // Extract relevant context from vector results
      const contextItems = vectorResults
        .slice(0, 3)
        .map((item) => {
          if (item.metadata) {
            // Extract key information from metadata
            const date = new Date(item.metadata.order_date);
            const product = item.metadata.product_name;
            const amount =
              typeof item.metadata.gross_sales === "number"
                ? item.metadata.gross_sales
                : parseFloat(item.metadata.gross_sales || "0");

            return {
              date: date.toLocaleDateString(),
              product,
              amount,
            };
          }
          return null;
        })
        .filter(Boolean);

      if (contextItems.length > 0) {
        response += "\n\nTop performing products during this period included:";
        contextItems.forEach((item) => {
          if (item) {
            response += `\n- ${item.product}: ${new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: "USD",
            }).format(item.amount)} (${item.date})`;
          }
        });
      }
    }

    return response;
  }

  /**
   * Format a response based solely on database results
   */
  private static formatDatabaseResponse(
    dbResults: {
      totalSales: number;
      recordCount: number;
      monthName: string;
      yearValue: number;
    },
    originalQuery: string
  ): string {
    const formattedSales = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(dbResults.totalSales);

    return `Based on ${dbResults.recordCount} transactions, the total sales for ${dbResults.monthName} ${dbResults.yearValue} were ${formattedSales}.`;
  }

  /**
   * Enhanced method to extract month and year from query with better recognition
   */
  static extractMonthYearFromQuery(query: string): {
    month: string | null;
    year: number | null;
  } {
    const lowercaseQuery = query.toLowerCase();

    // Advanced month detection with common phrases
    const monthPatterns = [
      // Direct month mentions
      /\b(january|jan|february|feb|march|mar|april|apr|may|june|jun|july|jul|august|aug|september|sept|sep|october|oct|november|nov|december|dec)\b/i,
      // "Month of" pattern
      /\bmonth of (january|jan|february|feb|march|mar|april|apr|may|june|jun|july|jul|august|aug|september|sept|sep|october|oct|november|nov|december|dec)\b/i,
      // Possessive patterns
      /\b(january'?s?|jan'?s?|february'?s?|feb'?s?|march'?s?|mar'?s?|april'?s?|apr'?s?|may'?s?|june'?s?|jun'?s?|july'?s?|jul'?s?|august'?s?|aug'?s?|september'?s?|sept'?s?|sep'?s?|october'?s?|oct'?s?|november'?s?|nov'?s?|december'?s?|dec'?s?) (sales|revenue|totals|numbers)\b/i,
      // For patterns like "in January"
      /\bin (january|jan|february|feb|march|mar|april|apr|may|june|jun|july|jul|august|aug|september|sept|sep|october|oct|november|nov|december|dec)\b/i,
    ];

    let month = null;

    // Try each pattern until we find a match
    for (const pattern of monthPatterns) {
      const match = lowercaseQuery.match(pattern);
      if (match) {
        // If pattern has a capturing group, use the last group (should contain the month)
        // Otherwise use the whole match
        month = match[match.length - 1];
        break;
      }
    }

    // Year detection with more flexibility
    const yearPattern = /\b(20\d{2}|19\d{2}|\d{2})\b/;
    const yearMatch = lowercaseQuery.match(yearPattern);
    let year = null;

    if (yearMatch) {
      const matchedYear = parseInt(yearMatch[0], 10);
      // Handle two-digit years
      if (matchedYear < 100) {
        // Assume 20xx for years below 50, 19xx for years above
        year = matchedYear < 50 ? 2000 + matchedYear : 1900 + matchedYear;
      } else {
        year = matchedYear;
      }
    }

    // If current year is implied through phrases like "this year" or "current year"
    if (
      !year &&
      (lowercaseQuery.includes("this year") ||
        lowercaseQuery.includes("current year") ||
        lowercaseQuery.includes("present year"))
    ) {
      year = new Date().getFullYear();
    }

    // If we found a month but no year, default to current year
    if (month && !year) {
      year = new Date().getFullYear();
    }

    return { month, year };
  }

  /**
   * Get the top selling products for a specific month and year
   */
  static async getTopSellingProductsByMonth(
    locationId: number,
    month: string,
    limit: number = 5,
    year?: number
  ): Promise<{
    products: Array<{
      productName: string;
      totalSales: number;
      totalQuantity: number;
      category: string;
    }>;
    monthName: string;
    yearValue: number;
  }> {
    try {
      // Input validation
      if (!locationId) {
        throw new Error("Location ID is required");
      }

      if (!month || typeof month !== "string") {
        throw new Error("Month name is required and must be a string");
      }

      // Import the database model
      const { PosData } = await import("./PosData");
      const { logger } = await import("../config/logger");

      // Normalize the month name (handle spelling issues like "janurary")
      const normalizedMonth = this.normalizeMonthName(month);

      // Convert month name to month index (1-12)
      const months = [
        "january",
        "february",
        "march",
        "april",
        "may",
        "june",
        "july",
        "august",
        "september",
        "october",
        "november",
        "december",
      ];
      const monthIndex =
        months.findIndex(
          (m) => m.toLowerCase() === normalizedMonth.toLowerCase()
        ) + 1;

      if (monthIndex === 0) {
        logger.warn({
          message: `Invalid month name, attempting fuzzy match: ${month}`,
          normalized: normalizedMonth,
        });

        // Try a fuzzy match for month names (handle typos)
        const fuzzyMatch = this.fuzzyMatchMonth(normalizedMonth);
        if (fuzzyMatch) {
          logger.info({
            message: `Fuzzy matched month name`,
            original: month,
            matched_to: fuzzyMatch,
          });
          const fuzzyMonthIndex =
            months.findIndex(
              (m) => m.toLowerCase() === fuzzyMatch.toLowerCase()
            ) + 1;
          if (fuzzyMonthIndex > 0) {
            return this.getTopSellingProductsByMonth(
              locationId,
              fuzzyMatch,
              limit,
              year
            );
          }
        }

        throw new Error(`Invalid month name: ${month}`);
      }

      // If year is not provided, use current year
      const yearValue = year || new Date().getFullYear();

      logger.info({
        message:
          "Querying top selling products by month directly from database",
        location_id: locationId,
        month: normalizedMonth,
        month_index: monthIndex,
        year: yearValue,
        limit,
      });

      try {
        // Group and aggregate sales by product
        const results = await PosData.query()
          .select(
            "product_name",
            "master_category",
            PosData.raw("SUM(gross_sales) as total_sales"),
            PosData.raw("COUNT(*) as total_quantity")
          )
          .where("location_id", locationId)
          .whereRaw("EXTRACT(MONTH FROM order_date) = ?", [monthIndex])
          .whereRaw("EXTRACT(YEAR FROM order_date) = ?", [yearValue])
          .groupBy("product_name", "master_category")
          .orderBy("total_sales", "desc")
          .limit(limit);

        // Map the results to a clean format
        const products = results.map((product: any) => ({
          productName: product.product_name,
          totalSales: parseFloat(product.total_sales || 0),
          totalQuantity: parseInt(product.total_quantity || 0, 10),
          category: product.master_category || "Unknown",
        }));

        logger.info({
          message: "Top selling products query results",
          product_count: products.length,
          month: normalizedMonth,
          year: yearValue,
          top_product:
            products.length > 0 ? products[0].productName : "None found",
        });

        return {
          products,
          monthName: normalizedMonth,
          yearValue,
        };
      } catch (dbError) {
        logger.error({
          message: "Database error in top selling products query",
          error: dbError instanceof Error ? dbError.message : String(dbError),
          location_id: locationId,
          month: normalizedMonth,
          year: yearValue,
        });
        throw dbError;
      }
    } catch (error) {
      logger.error({
        message: "Error getting top selling products by month",
        error_message: error instanceof Error ? error.message : String(error),
        error_stack: error instanceof Error ? error.stack : undefined,
        location_id: locationId,
        month,
        year,
      });
      throw error;
    }
  }
}
