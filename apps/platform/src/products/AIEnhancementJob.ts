/* eslint-disable indent */
import Job from "../queue/Job";
import { Product, ProductParams } from "./Product";
import { logger } from "../config/logger";
import { ProductAIEnhancementService } from "./ProductAIEnhancementService";
import { ProductDataVectorService } from "./ProductDataVectorService";
import ProductPatchJob from "./ProductPatchJob";
import App from "../app";
import { AIEnhancementCostOptimizer } from "./AIEnhancementCostOptimizer";
import { AIEnhancementMetrics } from "./AIEnhancementMetrics";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";

interface AIEnhancementJobParams {
  location_id: number;
  products: number[];
  options: {
    limit: number;
    skip: number;
    meta_sku?: string;
    priority?: "high" | "medium" | "low";
    force_mode?: "fast" | "batch" | "auto";
  };
  batch_id?: string;
  is_last_job_in_batch?: boolean;
  total_jobs_in_batch?: number;
}

/**
 * Asynchronous job for enhancing products with AI
 */
export default class AIEnhancementJob extends Job {
  data: AIEnhancementJobParams;

  static $name = "AIEnhancementJob";

  static async handler(data: AIEnhancementJobParams): Promise<void> {
    const job = new AIEnhancementJob({ data });
    await job.process();
  }

  constructor(params: { data: AIEnhancementJobParams }) {
    super(params);
    this.data = params.data;
  }

  static from(params: AIEnhancementJobParams): AIEnhancementJob {
    return new this({ data: params });
  }

  async process(): Promise<void> {
    const { location_id, products, options } = this.data;
    const jobId = `${location_id}-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 9)}`;
    const onboardingJobType = `ai_enhancement_${products.length}_products`;

    try {
      logger.info(
        `Starting AI enhancement job for ${products.length} products in location ${location_id}`
      );

      // Start tracking in both systems
      OnboardingJobTracker.startJob(location_id, onboardingJobType);

      // Start metrics tracking
      AIEnhancementMetrics.startJob(
        jobId,
        location_id,
        products.length,
        "mixed"
      );

      // If we have too many products, split into smaller jobs
      const MAX_PRODUCTS_PER_JOB = 100; // Configurable threshold
      if (products.length > MAX_PRODUCTS_PER_JOB) {
        logger.info(
          `Large batch detected (${products.length} products). Splitting into smaller jobs of ${MAX_PRODUCTS_PER_JOB} products each.`
        );

        await this.splitLargeJob(
          location_id,
          products,
          options,
          MAX_PRODUCTS_PER_JOB
        );
        OnboardingJobTracker.completeJob(location_id, onboardingJobType);
        AIEnhancementMetrics.completeJob(jobId);
        return;
      }

      // First, ensure the vector index exists
      await ProductDataVectorService.ensureIndexExists();

      // Retrieve the actual product objects
      const productObjects = await Product.query()
        .whereIn("id", products)
        .where("location_id", location_id);

      logger.info(
        `Retrieved ${productObjects.length} products for enhancement`
      );

      if (productObjects.length === 0) {
        logger.info("No products found for enhancement");
        OnboardingJobTracker.completeJob(location_id, onboardingJobType);
        AIEnhancementMetrics.updateJobProgress(jobId, 0, 0, products.length);
        AIEnhancementMetrics.completeJob(jobId);
        return;
      }

      // Convert to ProductParams for optimization
      const productParams = productObjects.map((product: Product) => {
        return (typeof product.toJSON === "function"
          ? product.toJSON()
          : { ...product }) as unknown as ProductParams;
      });

      // Apply intelligent optimization
      const optimizationResult = await this.optimizeProcessing(
        productParams,
        options
      );

      // Track estimated costs
      AIEnhancementMetrics.updateJobProgress(
        jobId,
        0,
        0,
        0,
        optimizationResult.totalEstimatedCost,
        optimizationResult.totalEstimatedTokens
      );

      // Process each batch type
      let totalProcessed = 0;
      let totalFailed = 0;

      // Process express batch (fastest)
      if (optimizationResult.expressBatch.length > 0) {
        logger.info(
          `Processing ${optimizationResult.expressBatch.length} products in EXPRESS mode`
        );
        const result = await this.processExpressBatch(
          optimizationResult.expressBatch,
          location_id,
          jobId
        );
        totalProcessed += result.processed;
        totalFailed += result.failed;
      }

      // Process standard batch (OpenAI Batch API)
      if (optimizationResult.standardBatch.length > 0) {
        logger.info(
          `Processing ${optimizationResult.standardBatch.length} products in STANDARD batch mode`
        );
        const result = await this.processStandardBatch(
          optimizationResult.standardBatch,
          location_id,
          jobId
        );
        totalProcessed += result.processed;
        totalFailed += result.failed;
      }

      // Process deep enhancement batch (individual processing)
      if (optimizationResult.deepEnhancementBatch.length > 0) {
        logger.info(
          `Processing ${optimizationResult.deepEnhancementBatch.length} products in DEEP enhancement mode`
        );
        const result = await this.processDeepEnhancementBatch(
          optimizationResult.deepEnhancementBatch,
          location_id,
          jobId
        );
        totalProcessed += result.processed;
        totalFailed += result.failed;
      }

      // Update final metrics
      AIEnhancementMetrics.updateJobProgress(
        jobId,
        totalProcessed,
        totalFailed,
        0
      );

      // Check if this is the last job in a batch and trigger vectorization
      if (this.data.is_last_job_in_batch) {
        logger.info(
          `This is the last job in batch ${this.data.batch_id}. Triggering bulk vectorization for enhanced products.`
        );
        await this.triggerBulkVectorization(location_id);
      } else if (!this.data.batch_id) {
        // Single job (not part of a larger batch) - trigger vectorization immediately
        logger.info(
          "Single enhancement job completed. Triggering bulk vectorization for enhanced products."
        );
        await this.triggerBulkVectorization(location_id);
      } else {
        logger.info(
          `Job completed as part of batch ${this.data.batch_id}. Vectorization will be triggered by the last job.`
        );
      }

      logger.info(
        `AI enhancement job complete for location ${location_id} - processed ${totalProcessed} products, failed ${totalFailed}`
      );

      // Complete tracking in both systems
      OnboardingJobTracker.completeJob(location_id, onboardingJobType);
      const finalMetrics = AIEnhancementMetrics.completeJob(jobId);
      if (finalMetrics) {
        logger.info(
          `Job completed with final cost: $${finalMetrics.totalCost.toFixed(4)}`
        );
      }
    } catch (error) {
      logger.error(
        `Error in AI enhancement job for location ${location_id}:`,
        error
      );
      OnboardingJobTracker.failJob(location_id, onboardingJobType, error);
      AIEnhancementMetrics.updateJobProgress(jobId, 0, products.length, 0);
      AIEnhancementMetrics.completeJob(jobId);
      throw error; // Rethrow to mark job as failed
    }
  }

  /**
   * Triggers bulk vectorization for enhanced products
   */
  private async triggerBulkVectorization(location_id: number): Promise<void> {
    try {
      logger.info(`Triggering bulk vectorization for location ${location_id}`);

      const ProductDataVectorJob = require("./ProductDataVectorJob").default;

      await App.main.queue.enqueue(
        ProductDataVectorJob.from({
          location_id,
          filter: {
            enhancement_status: "complete",
          },
          batch_size: 100,
        })
      );

      logger.info(`Bulk vectorization job queued for location ${location_id}`);
    } catch (error) {
      logger.error(
        `Failed to trigger bulk vectorization for location ${location_id}:`,
        error
      );
      // Don't throw - vectorization failure shouldn't fail the enhancement job
    }
  }

  /**
   * Split large jobs into smaller, manageable chunks
   */
  private async splitLargeJob(
    location_id: number,
    products: number[],
    options: AIEnhancementJobParams["options"],
    maxProductsPerJob: number
  ): Promise<void> {
    const chunks = [];
    for (let i = 0; i < products.length; i += maxProductsPerJob) {
      chunks.push(products.slice(i, i + maxProductsPerJob));
    }

    logger.info(`Splitting into ${chunks.length} smaller jobs`);

    // Generate a unique batch ID to track related jobs
    const batchId = `batch-${location_id}-${Date.now()}-${Math.random()
      .toString(36)
      .substr(2, 6)}`;

    logger.info(`Created batch ${batchId} with ${chunks.length} jobs`);

    // Queue all chunks with slight delays to prevent system overload
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      const chunkNumber = i + 1;
      const isLastJob = i === chunks.length - 1;

      logger.info(
        `Enqueueing chunk ${chunkNumber}/${chunks.length} with ${chunk.length} products (batch: ${batchId})`
      );

      // Add delay to prevent overwhelming the system
      const delay = i * 5000; // 5 second delay between jobs

      setTimeout(async () => {
        await App.main.queue.enqueue(
          AIEnhancementJob.from({
            location_id,
            products: chunk,
            options: {
              ...options,
              limit: chunk.length,
              skip: 0,
            },
            batch_id: batchId,
            is_last_job_in_batch: isLastJob,
            total_jobs_in_batch: chunks.length,
          })
        );
      }, delay);
    }
  }

  /**
   * Apply intelligent optimization to determine processing strategy
   */
  private async optimizeProcessing(
    products: ProductParams[],
    options: AIEnhancementJobParams["options"]
  ) {
    // Apply deduplication first
    const deduplicatedProducts =
      await AIEnhancementCostOptimizer.deduplicateProducts(products);

    // Apply intelligent batching optimization
    const optimizationResult =
      AIEnhancementCostOptimizer.optimizeBatchProcessing(deduplicatedProducts);

    // Override with forced mode if specified
    if (options.force_mode) {
      logger.info(`Forcing processing mode: ${options.force_mode}`);
      switch (options.force_mode) {
        case "fast":
          return {
            expressBatch: deduplicatedProducts,
            standardBatch: [],
            deepEnhancementBatch: [],
            totalEstimatedCost: optimizationResult.totalEstimatedCost,
            totalEstimatedTokens: optimizationResult.totalEstimatedTokens,
          };
        case "batch":
          return {
            expressBatch: [],
            standardBatch: deduplicatedProducts,
            deepEnhancementBatch: [],
            totalEstimatedCost: optimizationResult.totalEstimatedCost,
            totalEstimatedTokens: optimizationResult.totalEstimatedTokens,
          };
      }
    }

    return optimizationResult;
  }

  /**
   * Process express batch - fastest processing for simple products
   */
  private async processExpressBatch(
    products: ProductParams[],
    location_id: number,
    jobId: string
  ): Promise<{ processed: number; failed: number }> {
    const batchId = `${jobId}-express`;
    AIEnhancementMetrics.startBatch(batchId, products.length, "simple");

    try {
      // Use fast enhancement method (individual API calls in parallel)
      const enhancedProducts =
        await ProductAIEnhancementService.fastBatchEnhanceProducts(products);

      // Save all enhanced products
      logger.info(
        `Queuing ${enhancedProducts.length} enhanced products for saving`
      );

      const savePromises = enhancedProducts.map((enhancedProduct) =>
        App.main.queue.enqueue(
          ProductPatchJob.from({
            location_id,
            product: {
              ...enhancedProduct,
              location_id,
            },
          })
        )
      );

      await Promise.all(savePromises);

      AIEnhancementMetrics.completeBatch(batchId, true, 0, 0); // TODO: Track actual tokens/cost
      return { processed: enhancedProducts.length, failed: 0 };
    } catch (error) {
      logger.error(`Error in express batch processing:`, error);
      AIEnhancementMetrics.completeBatch(
        batchId,
        false,
        0,
        0,
        error instanceof Error ? error.message : "Unknown error"
      );
      return { processed: 0, failed: products.length };
    }
  }

  /**
   * Process standard batch using OpenAI Batch API
   */
  private async processStandardBatch(
    products: ProductParams[],
    location_id: number,
    jobId: string
  ): Promise<{ processed: number; failed: number }> {
    // Get optimal batch size based on complexity
    const optimalBatchSize =
      AIEnhancementCostOptimizer.getOptimalBatchSize(products);
    const totalBatches = Math.ceil(products.length / optimalBatchSize);

    let totalProcessed = 0;
    let totalFailed = 0;

    // Process in optimized batches with parallel processing
    const PARALLEL_BATCHES = 2;

    for (
      let i = 0;
      i < products.length;
      i += optimalBatchSize * PARALLEL_BATCHES
    ) {
      const parallelBatches: Promise<{ processed: number; failed: number }>[] =
        [];

      for (
        let j = 0;
        j < PARALLEL_BATCHES && i + j * optimalBatchSize < products.length;
        j++
      ) {
        const startIdx = i + j * optimalBatchSize;
        const endIdx = Math.min(startIdx + optimalBatchSize, products.length);
        const batch = products.slice(startIdx, endIdx);

        if (batch.length > 0) {
          parallelBatches.push(
            this.processSingleBatch(
              batch,
              location_id,
              jobId,
              startIdx,
              totalBatches
            )
          );
        }
      }

      const results = await Promise.all(parallelBatches);
      results.forEach((result) => {
        totalProcessed += result.processed;
        totalFailed += result.failed;
      });
    }

    return { processed: totalProcessed, failed: totalFailed };
  }

  /**
   * Process deep enhancement batch - individual processing for complex products
   */
  private async processDeepEnhancementBatch(
    products: ProductParams[],
    location_id: number,
    jobId: string
  ): Promise<{ processed: number; failed: number }> {
    let processed = 0;
    let failed = 0;

    // Process each product individually with detailed enhancement
    for (const product of products) {
      const batchId = `${jobId}-deep-${product.id}`;
      AIEnhancementMetrics.startBatch(batchId, 1, "complex");

      try {
        // Use individual enhancement for maximum quality
        const enhancedProducts =
          await ProductAIEnhancementService.fastBatchEnhanceProducts([product]);

        if (enhancedProducts.length > 0) {
          await App.main.queue.enqueue(
            ProductPatchJob.from({
              location_id,
              product: {
                ...enhancedProducts[0],
                location_id,
              },
            })
          );
          processed++;
          AIEnhancementMetrics.completeBatch(batchId, true, 0, 0);
        } else {
          failed++;
          AIEnhancementMetrics.completeBatch(
            batchId,
            false,
            0,
            0,
            "No enhanced product returned"
          );
        }
      } catch (error) {
        logger.error(`Error enhancing product ${product.meta_sku}:`, error);
        failed++;
        AIEnhancementMetrics.completeBatch(
          batchId,
          false,
          0,
          0,
          error instanceof Error ? error.message : "Unknown error"
        );
      }

      // Add small delay between individual products to prevent API rate limiting
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    return { processed, failed };
  }

  /**
   * Process a single batch with enhanced error handling and metrics
   */
  private async processSingleBatch(
    batch: ProductParams[],
    location_id: number,
    jobId: string,
    startIdx: number,
    totalBatches: number
  ): Promise<{ processed: number; failed: number }> {
    const currentBatch = Math.floor(startIdx / 20) + 1;
    const batchId = `${jobId}-batch-${currentBatch}`;

    logger.info(
      `Processing batch ${currentBatch}/${totalBatches} - Starting AI enhancement for ${batch.length} products`
    );

    // Determine batch complexity
    const estimates = batch.map((p) =>
      AIEnhancementCostOptimizer.estimateProductEnhancement(p)
    );
    const avgComplexity =
      estimates.reduce((sum, e) => {
        return (
          sum +
          (e.complexity === "simple" ? 1 : e.complexity === "medium" ? 2 : 3)
        );
      }, 0) / estimates.length;

    const complexity: "simple" | "medium" | "complex" =
      avgComplexity <= 1.5
        ? "simple"
        : avgComplexity <= 2.5
        ? "medium"
        : "complex";

    AIEnhancementMetrics.startBatch(batchId, batch.length, complexity);

    try {
      logger.info(
        `Batch ${currentBatch}/${totalBatches} - Calling OpenAI batch API`
      );

      // Use the batch API with dynamic timeout based on complexity
      const timeoutMinutes =
        complexity === "simple" ? 15 : complexity === "medium" ? 25 : 35;
      const batchPromise =
        ProductAIEnhancementService.batchEnhanceProducts(batch);
      const timeoutPromise = new Promise<never>((_resolve, reject) => {
        setTimeout(
          () =>
            reject(
              new Error(
                `Batch enhancement timeout after ${timeoutMinutes} minutes`
              )
            ),
          timeoutMinutes * 60 * 1000
        );
      });

      const enhancedBatch = await Promise.race([batchPromise, timeoutPromise]);

      logger.info(
        `Batch ${currentBatch}/${totalBatches} - AI enhancement completed successfully`
      );

      // Save the enhanced products
      const savePromises = enhancedBatch.map((enhancedProduct) =>
        App.main.queue.enqueue(
          ProductPatchJob.from({
            location_id,
            product: {
              ...enhancedProduct,
              location_id,
            },
          })
        )
      );

      await Promise.all(savePromises);

      AIEnhancementMetrics.completeBatch(batchId, true, 0, 0); // TODO: Track actual tokens/cost
      return { processed: enhancedBatch.length, failed: 0 };
    } catch (error) {
      logger.error(
        `Error enhancing batch ${currentBatch}/${totalBatches}:`,
        error
      );

      // Continue with unenhanced products instead of failing the entire job
      const fallbackBatch = batch.map((product: ProductParams) => ({
        ...product,
        enhancement_status: "failed",
        enhancement_error:
          error instanceof Error
            ? error.message.substring(0, 252)
            : "Enhancement failed",
      }));

      const savePromises = fallbackBatch.map((product) =>
        App.main.queue.enqueue(
          ProductPatchJob.from({
            location_id,
            product: {
              ...product,
              location_id,
            },
          })
        )
      );

      await Promise.all(savePromises);

      AIEnhancementMetrics.completeBatch(
        batchId,
        false,
        0,
        0,
        error instanceof Error ? error.message : "Unknown error"
      );

      return { processed: 0, failed: batch.length };
    }
  }
}
