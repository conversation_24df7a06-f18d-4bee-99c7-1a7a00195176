import Job from "../queue/Job";
import { SupabaseService } from "../supabase/SupabaseService";
import { importFromMarketplace } from "./ProductImport";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";
import { logger } from "../config/logger";

interface MarketplaceSyncJobData {
  location_id: number;
  retailer_id: string;
  delay?: number;
}

/**
 * Delayed job that imports products from the marketplace after
 * a waiting period to allow scraping to complete
 */
export default class MarketplaceSyncJob extends Job {
  static $name = "marketplace_sync_job";

  static from(data: MarketplaceSyncJobData): MarketplaceSyncJob {
    const job = new this(data);

    // Set delay if provided
    if (data.delay) {
      job.delay(data.delay);
    }

    return job;
  }

  constructor(data: MarketplaceSyncJobData) {
    super(data);

    // Set default options
    this.options = {
      ...this.options,
      attempts: 2,
      delay: data.delay || 300000, // Default 5 minutes if not specified
    };
  }

  static async handler(data: MarketplaceSyncJobData): Promise<void> {
    const { location_id, retailer_id } = data;
    const jobType = "marketplace_sync";

    logger.info(
      `Starting delayed marketplace sync job for location ${location_id} with retailer ${retailer_id}`
    );

    try {
      // Initialize Supabase service
      const supabaseService = new SupabaseService({
        url: process.env.SUPABASE_URL || "",
        key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
        bucket: process.env.SUPABASE_BUCKET || "location-data",
      });

      // Get retailer products from Supabase (should have fresh data now after scraping)
      const response = await supabaseService.getRetailerProducts(retailer_id);
      const products = response.products || [];

      if (products.length === 0) {
        logger.info(
          `No products found for retailer ${retailer_id} in marketplace after scraping`
        );
        OnboardingJobTracker.completeJob(location_id, jobType);
        return;
      }

      // Use the importFromMarketplace function to import products
      const result = await importFromMarketplace(
        location_id,
        retailer_id,
        products,
        {
          enhance_with_ai: false,
          reindex: true,
        }
      );

      logger.info(
        `Successfully synced ${result.processed} products from marketplace for location ${location_id} after scraping`
      );

      // Mark the job as complete
      OnboardingJobTracker.completeJob(location_id, jobType);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        `Failed to sync products from marketplace after scraping: ${errorMessage}`,
        error
      );

      // Mark job as failed
      OnboardingJobTracker.failJob(
        location_id,
        jobType,
        error instanceof Error ? error : new Error(errorMessage)
      );

      // Re-throw the error to trigger retry if attempts remain
      throw error;
    }
  }
}
