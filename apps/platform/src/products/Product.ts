import Model from "../core/Model";
import { ProductParams } from "./ProductParams";

export { ProductParams };

// Featured Product interface
export interface FeaturedProductParams {
  location_id: number;
  product_id: number;
  is_top_pick?: boolean;
  sort_order?: number;
  active?: boolean;
}

export interface FeaturedProductWithProduct extends FeaturedProductParams {
  id: number;
  created_at: Date;
  updated_at: Date;
  product: Product;
}

export class FeaturedProduct extends Model {
  declare id: number;
  location_id!: number;
  product_id!: number;
  is_top_pick!: boolean;
  sort_order!: number;
  active!: boolean;
  declare created_at: Date;
  declare updated_at: Date;

  static tableName = "featured_products";
}

export class Product extends Model {
  declare id: number;
  product_id?: string;
  location_id!: number;
  meta_sku!: string;
  retailer_id!: string;
  cann_sku_id?: string;
  brand_name?: string;
  brand_id?: number;
  url?: string;
  image_url?: string;
  images_urls?: string;
  raw_product_name!: string;
  product_name!: string;
  raw_weight_string?: string;
  display_weight?: string;
  raw_product_category?: string;
  category?: string;
  raw_subcategory?: string;
  subcategory?: string;
  product_tags?: string[];
  percentage_thc?: number;
  percentage_cbd?: number;
  mg_thc?: number;
  mg_cbd?: number;
  quantity_per_package?: number;
  medical!: boolean;
  recreational!: boolean;
  latest_price?: number;
  menu_provider?: string;
  review_summary?: string;
  rating?: number;
  reviews_count?: number;
  product_description?: string;
  short_description?: string;
  thc?: number;
  cbd?: number;
  variants?: any;
  enhancement_status?: string;
  ai_enhanced_fields?: string[];
  mood?: string[];
  estimated_cbd_percentage?: string;
  estimated_thc_percentage?: string;
  effects?: any;
  enhancement_error?: string;
  external_id?: string;

  // Wholesale and retail price fields
  wholesale_price?: number;
  retail_price?: number;
  msrp?: number;
  profit_margin?: number;

  // Grower and cultivar information
  grower_name?: string;
  cultivar?: string;
  batch_number?: string;
  harvest_date?: Date;
  coa_url?: string;

  // Additional fields from schema
  source!: string; // 'weedmaps' or 'cannmenus'
  slug?: string;

  // Inventory and stock management
  out_of_stock?: boolean;
  inventory_quantity?: number;
  last_restocked?: Date;

  // Product visibility
  is_active?: boolean;

  declare created_at: Date;
  declare updated_at: Date;

  data?: Record<string, unknown>;

  static jsonAttributes = [
    "data",
    "product_tags",
    "variants",
    "mood",
    "effects",
    "ai_enhanced_fields",
  ];

  static get tableName() {
    return "products";
  }

  flatten() {
    return {
      ...this,
      harvest_date: this.harvest_date?.toISOString(),
      last_restocked: this.last_restocked?.toISOString(),
      created_at: this.created_at?.toISOString(),
      updated_at: this.updated_at?.toISOString(),
    };
  }

  toJSON() {
    return this.flatten();
  }
}

export type ProductInternalParams = Partial<ProductParams> & {
  meta_sku: string;
  location_id: number;
  product_id?: string;
  source?: string;
  created_at?: string | Date;
  updated_at?: string | Date;
};
