import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";
import { ProductParams } from "./Product";
import { logger } from "../config/logger";
import env from "../config/env";
import OpenAI from "openai";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";

export class ProductAIEnhancementService {
  private static openai: OpenAI;

  static initialize() {
    this.openai = new OpenAI({
      apiKey: env().openai.apiKey,
    });
    logger.info("ProductAIEnhancementService initialized");
  }

  /**
   * Enhances product data by filling in missing fields using AI
   */
  static async enhanceProduct(product: ProductParams): Promise<ProductParams> {
    if (!this.openai) {
      this.initialize();
    }

    try {
      // Determine which fields are missing
      const missingFields = this.identifyMissingFields(product);

      if (missingFields.length === 0) {
        // No enhancement needed
        return {
          ...product,
          enhancement_status: "complete",
        };
      }

      // Create prompt for AI
      const prompt = this.createPrompt(product, missingFields);

      // Call OpenAI directly with JSON mode enabled
      const response = await this.openai.chat.completions.create({
        model: "gpt-4.1-mini",
        messages: [
          {
            role: "system",
            content:
              "You are a cannabis product data expert. Your task is to fill in missing information about cannabis products based on existing data.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.2,
        response_format: { type: "json_object" },
      });

      // Extract the JSON content
      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error("Empty response from AI model");
      }

      // Parse response
      let enhancedData;
      try {
        enhancedData = JSON.parse(content);
      } catch (parseError) {
        logger.error(
          "Failed to parse AI response for product enhancement:",
          parseError
        );
        return {
          ...product,
          enhancement_status: "failed",
          enhancement_error: "Failed to parse AI response",
        };
      }

      // Track which fields were actually enhanced
      const enhancedFields: string[] = [];
      for (const field in enhancedData) {
        if (enhancedData[field] !== undefined && enhancedData[field] !== null) {
          enhancedFields.push(field);
        }
      }

      // Merge enhanced data with original product
      const enhancedProduct = {
        ...product,
        ...enhancedData,
        enhancement_status: "complete",
        ai_enhanced_fields: enhancedFields,
      };

      logger.info(
        `Enhanced product ${
          product.meta_sku || "unknown"
        } with AI. Enhanced fields: ${enhancedFields.join(", ")}`
      );
      return enhancedProduct;
    } catch (error) {
      logger.error("Error enhancing product with AI:", error);

      // Truncate error message to fit database column (max 255 chars)
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const truncatedError =
        errorMessage.length > 252
          ? errorMessage.substring(0, 252) + "..."
          : errorMessage;

      return {
        ...product,
        enhancement_status: "failed",
        enhancement_error: truncatedError,
      };
    }
  }

  /**
   * Fast batch enhancement using individual API calls for speed
   * Use this for smaller batches where speed is more important than cost
   */
  static async fastBatchEnhanceProducts(
    products: ProductParams[]
  ): Promise<ProductParams[]> {
    if (!this.openai) {
      this.initialize();
    }

    if (products.length === 0) {
      return [];
    }

    logger.info(
      `Starting FAST batch enhancement for ${products.length} products`
    );

    // Filter out products that don't need enhancement upfront
    const productsNeedingEnhancement = products.filter((product) => {
      const missingFields = this.identifyMissingFields(product);
      return missingFields.length > 0;
    });

    logger.info(
      `${productsNeedingEnhancement.length} of ${products.length} products need enhancement`
    );

    if (productsNeedingEnhancement.length === 0) {
      return products.map((product) => ({
        ...product,
        enhancement_status: "complete",
      }));
    }

    // Process products in parallel with controlled concurrency
    const CONCURRENT_REQUESTS = 5; // Parallel requests to avoid rate limits
    const results: ProductParams[] = [];

    for (
      let i = 0;
      i < productsNeedingEnhancement.length;
      i += CONCURRENT_REQUESTS
    ) {
      const batch = productsNeedingEnhancement.slice(
        i,
        i + CONCURRENT_REQUESTS
      );

      const batchPromises = batch.map(async (product) => {
        try {
          return await this.enhanceProduct(product);
        } catch (error) {
          logger.error(`Failed to enhance product ${product.meta_sku}:`, error);
          return {
            ...product,
            enhancement_status: "failed",
            enhancement_error:
              error instanceof Error
                ? error.message.substring(0, 252)
                : "Enhancement failed",
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      logger.info(
        `Enhanced ${results.length}/${productsNeedingEnhancement.length} products`
      );
    }

    // Merge enhanced products with products that didn't need enhancement
    const enhancementMap = new Map(
      results.map((product) => [
        product.meta_sku || product.product_id,
        product,
      ])
    );

    return products.map((originalProduct) => {
      const enhanced = enhancementMap.get(
        originalProduct.meta_sku || originalProduct.product_id
      );
      return (
        enhanced || {
          ...originalProduct,
          enhancement_status: "complete",
        }
      );
    });
  }

  /**
   * Batch enhances products using OpenAI's Batch API for cost savings
   * This can save up to 50% on API costs compared to individual calls
   */
  static async batchEnhanceProducts(
    products: ProductParams[]
  ): Promise<ProductParams[]> {
    if (!this.openai) {
      this.initialize();
    }

    if (products.length === 0) {
      return [];
    }

    try {
      logger.info(`Starting batch enhancement for ${products.length} products`);

      // Create batch requests
      const batchRequests = products
        .map((product, index) => {
          const missingFields = this.identifyMissingFields(product);

          // Skip products that don't need enhancement
          if (missingFields.length === 0) {
            return null;
          }

          const prompt = this.createPrompt(product, missingFields);

          return {
            custom_id: `product-${product.meta_sku || index}`,
            method: "POST",
            url: "/v1/chat/completions",
            body: {
              model: "gpt-4.1-mini",
              messages: [
                {
                  role: "system",
                  content:
                    "You are a cannabis product data expert. Your task is to fill in missing information about cannabis products based on existing data.",
                },
                {
                  role: "user",
                  content: prompt,
                },
              ],
              temperature: 0.2,
              response_format: { type: "json_object" },
            },
          };
        })
        .filter((req) => req !== null);

      if (batchRequests.length === 0) {
        logger.info("No products need enhancement");
        return products.map((product) => ({
          ...product,
          enhancement_status: "complete",
        }));
      }

      // Create temporary batch file
      const tempDir = os.tmpdir();
      const batchFilePath = path.join(
        tempDir,
        `product-batch-${Date.now()}.jsonl`
      );

      // Write each request on a separate line (JSONL format)
      fs.writeFileSync(
        batchFilePath,
        batchRequests.map((req) => JSON.stringify(req)).join("\n")
      );

      // Upload batch file to OpenAI
      const fileUpload = await this.openai.files.create({
        file: fs.createReadStream(batchFilePath),
        purpose: "batch",
      });

      logger.info(`Batch file uploaded with ID: ${fileUpload.id}`);

      // Create batch job
      const batchJob = await this.openai.batches.create({
        input_file_id: fileUpload.id,
        endpoint: "/v1/chat/completions",
        completion_window: "24h",
      });

      logger.info(`Batch job created with ID: ${batchJob.id}`);

      // Poll for completion (with timeout)
      const maxPolls = 60; // 30 minutes max with 30-second intervals
      let pollCount = 0;
      let batchStatus;

      while (pollCount < maxPolls) {
        batchStatus = await this.openai.batches.retrieve(batchJob.id);

        if (batchStatus.status === "completed") {
          break;
        } else if (
          batchStatus.status === "failed" ||
          batchStatus.status === "expired"
        ) {
          const errorMessage =
            batchStatus.errors &&
            Array.isArray(batchStatus.errors) &&
            batchStatus.errors.length > 0
              ? batchStatus.errors[0].message
              : "Unknown error";
          throw new Error(
            `Batch job ${batchJob.id} ${batchStatus.status}: ${errorMessage}`
          );
        }

        // Wait 30 seconds before polling again
        await new Promise((resolve) => setTimeout(resolve, 30000));
        pollCount++;
      }

      if (batchStatus?.status !== "completed") {
        throw new Error("Batch job timed out or did not complete successfully");
      }

      // Get results
      const resultFile = await this.openai.files.content(
        batchStatus.output_file_id || ""
      );
      const resultContent = await resultFile.text();
      const resultLines = resultContent.trim().split("\n");

      // Parse results
      const resultMap = new Map();
      for (const line of resultLines) {
        const result = JSON.parse(line);
        const customId = result.custom_id;
        const productId = customId.replace("product-", "");

        // Check for errors
        if (result.error) {
          logger.error(`Error in batch result for ${customId}:`, result.error);
          continue;
        }

        try {
          const content = result.response.body.choices[0].message.content;
          const enhancedData = JSON.parse(content);
          resultMap.set(productId, enhancedData);
        } catch (error) {
          logger.error(`Failed to parse batch result for ${customId}:`, error);
        }
      }

      // Clean up temp file
      fs.unlinkSync(batchFilePath);

      // Apply enhancements to original products
      const enhancedProducts = products.map((product) => {
        const productKey =
          product.meta_sku || products.indexOf(product).toString();
        const enhancedData = resultMap.get(productKey);

        if (!enhancedData) {
          return {
            ...product,
            enhancement_status: product.enhancement_status || "complete",
          };
        }

        // Track which fields were enhanced
        const enhancedFields: string[] = [];
        for (const field in enhancedData) {
          if (
            enhancedData[field] !== undefined &&
            enhancedData[field] !== null
          ) {
            enhancedFields.push(field);
          }
        }

        return {
          ...product,
          ...enhancedData,
          enhancement_status: "complete",
          ai_enhanced_fields: enhancedFields,
        };
      });

      logger.info(
        `Batch enhancement completed for ${products.length} products`
      );
      return enhancedProducts;
    } catch (error) {
      logger.error("Error in batch enhancement:", error);

      // Return original products with failed status
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const truncatedError =
        errorMessage.length > 252
          ? errorMessage.substring(0, 252) + "..."
          : errorMessage;

      return products.map((product) => ({
        ...product,
        enhancement_status: "failed",
        enhancement_error: truncatedError,
      }));
    }
  }

  /**
   * Identifies which fields are missing in the product data
   */
  private static identifyMissingFields(product: ProductParams): string[] {
    const missingFields: string[] = [];

    // Check for missing essential fields
    if (!product.product_name) {
      missingFields.push("product_name");
    }
    if (!product.category) {
      missingFields.push("category");
    }
    if (!product.subcategory) {
      missingFields.push("subcategory");
    }
    if (!product.product_description) {
      missingFields.push("product_description");
    }

    // Check for missing detail fields
    if (!product.product_tags || product.product_tags.length === 0) {
      missingFields.push("product_tags");
    }
    if (!product.percentage_thc && !product.thc) {
      missingFields.push("percentage_thc");
    }
    if (!product.percentage_cbd && !product.cbd) {
      missingFields.push("percentage_cbd");
    }
    if (!product.mood || product.mood.length === 0) {
      missingFields.push("mood");
    }
    if (!product.effects) {
      missingFields.push("effects");
    }

    return missingFields;
  }

  /**
   * Creates a prompt for the AI model based on the product and missing fields
   */
  private static createPrompt(
    product: ProductParams,
    missingFields: string[]
  ): string {
    return `
      Analyze this cannabis product data and fill in the missing fields: ${missingFields.join(
        ", "
      )}.
      
      Product data:
      ${JSON.stringify(product, null, 2)}
      
      Guidelines:
      1. For product_name: Create a clear, accurate name based on available data like brand, strain, etc.
      2. For category: Use standard cannabis categories (Flower, Edible, Concentrate, etc.)
      3. For subcategory: Provide a specific type within the category
      4. For product_description: Write a detailed, accurate description of the product
      5. For product_tags: Provide an array of relevant tags (strain, effects, flavors)
      6. For percentage_thc/cbd: Estimate a realistic percentage based on product type
      7. For mood: Provide an array of moods this product may induce ["Relaxed", "Happy", etc.]
      8. For effects: Provide a detailed effects object with onset, duration, and description
      
      Return a JSON object containing ONLY the missing fields that you've filled in. 
      Your response must be a valid JSON object.
      
      Example format for your response:
      {
        "product_description": "A relaxing indica strain...",
        "product_tags": ["Indica", "Relaxing", "Sleepy"],
        "mood": ["Relaxed", "Sleepy"],
        "effects": ["fast-acting", "long-lasting", "relaxing", "sleepy"]
      }
    `;
  }
}
