import Job from "../queue/Job";
import { Product } from "./Product";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";
import { ProductDataVectorService } from "./ProductDataVectorService";
import { logger } from "../config/logger";

const MAX_RETRIES = 3;

interface ProductDataVectorParams {
  location_id: number;
  batch_size?: number;
  last_processed_id?: number;
  retry_count?: number;
  failed_ids?: number[];
  filter?: {
    enhancement_status?: string;
    updated_since?: string;
  };
}

class ProductDataVectorJob extends Job {
  static $name = "product-data-vector-job";

  location_id!: number;
  batch_size?: number;
  last_processed_id?: number;
  retry_count?: number;
  failed_ids?: number[];
  filter?: {
    enhancement_status?: string;
    updated_since?: string;
  };

  static from(params: ProductDataVectorParams) {
    logger.info(
      `Creating product data vector job for location ${params.location_id}`,
      {
        ...params,
        batch_size: params.batch_size || 100,
        last_processed_id: params.last_processed_id || 0,
        retry_count: params.retry_count || 0,
        failed_ids_count: params.failed_ids?.length || 0,
      }
    );

    return new this({
      ...params,
      batch_size: params.batch_size || 100,
      last_processed_id: params.last_processed_id || 0,
      retry_count: params.retry_count || 0,
      failed_ids: params.failed_ids || [],
    });
  }

  static async handler({
    location_id,
    batch_size = 100,
    last_processed_id = 0,
    retry_count = 0,
    failed_ids = [],
    filter,
  }: ProductDataVectorParams) {
    try {
      // Initialize services
      await ProductDataVectorService.initialize();

      // Define job type for tracking
      const jobType = "product_data_vectorization";

      logger.info(
        `Processing product data vectorization for location ${location_id}, batch starting from ID ${last_processed_id}`,
        { filter }
      );

      // Query next batch of products to process
      let products: Product[] = [];

      if (failed_ids && failed_ids.length > 0) {
        // Process failed IDs first if any
        logger.info(
          `Retrying vectorization for ${failed_ids.length} failed records`
        );
        products = await Product.query().whereIn("id", failed_ids);
      } else {
        // Build the base query
        let query = Product.query()
          .where("location_id", location_id)
          .where("id", ">", last_processed_id)
          .orderBy("id", "asc")
          .limit(batch_size);

        // Apply filters if they exist
        if (filter?.enhancement_status) {
          query = query.andWhere(
            "enhancement_status",
            filter.enhancement_status
          );
        }
        if (filter?.updated_since) {
          query = query.andWhere("updated_at", ">=", filter.updated_since);
        }

        products = await query;
      }

      if (products.length === 0) {
        logger.info(
          `No more products to process for location ${location_id}. Vectorization complete.`
        );

        // Mark job as completed
        OnboardingJobTracker.completeJob(location_id, jobType);
        return;
      }

      // Get the highest ID for the next batch
      const highestProcessedId = products[products.length - 1].id;

      // Vectorize the products
      const result = await ProductDataVectorService.upsertProductData(products);

      // Log detailed results including skipped products
      const skippedCount = (result as any).skippedCount || 0;
      const skippedIds = (result as any).skippedIds || [];

      logger.info(
        `Vectorized ${
          result.successCount
        } product records for location ${location_id}, ${
          result.errorCount
        } errors${skippedCount > 0 ? `, ${skippedCount} skipped` : ""}`
      );

      if (skippedCount > 0) {
        logger.info(
          `Skipped ${skippedCount} products due to insufficient data for vectorization`,
          { skippedProductIds: skippedIds }
        );
      }

      // Handle failures and retries - only retry actual vectorization failures, not validation failures
      if (
        result.errorCount > 0 &&
        result.failedIds &&
        result.failedIds.length > 0
      ) {
        // Extract numeric IDs from the returned failedIds
        const failedNumericIds = result.failedIds
          .map((id) => {
            const match = id.match(/^product_(\d+)$/);
            return match ? parseInt(match[1], 10) : null;
          })
          .filter((id): id is number => id !== null);

        // If we have failures and haven't exceeded max retries, queue a retry job
        if (failedNumericIds.length > 0 && retry_count < MAX_RETRIES) {
          logger.warn(
            `Scheduling retry ${retry_count + 1}/${MAX_RETRIES} for ${
              failedNumericIds.length
            } failed records (vectorization failures only)`
          );

          await ProductDataVectorJob.from({
            location_id,
            batch_size: Math.max(10, Math.floor(batch_size / 2)),
            last_processed_id: highestProcessedId,
            retry_count: retry_count + 1,
            failed_ids: failedNumericIds,
            filter,
          })
            .delay(2000 * retry_count + 1000) // Increasing delay with each retry
            .queue();
        } else if (failedNumericIds.length > 0) {
          // We've exceeded max retries, log a critical error
          logger.error(
            `Failed to vectorize ${failedNumericIds.length} product records after ${MAX_RETRIES} retries`
          );
          OnboardingJobTracker.failJob(
            location_id,
            jobType,
            `Failed to vectorize ${failedNumericIds.length} product records after ${MAX_RETRIES} retries`
          );
        }
      }

      // Queue next batch if we're not in retry mode and have a full batch
      if (
        (!failed_ids || failed_ids.length === 0) &&
        products.length === batch_size
      ) {
        logger.info(
          `Queueing next batch of product data after ID ${highestProcessedId}`
        );

        await ProductDataVectorJob.from({
          location_id,
          batch_size,
          last_processed_id: highestProcessedId,
          filter,
        })
          .delay(1000) // Small delay between batches
          .queue();
      } else {
        // This was the last batch, mark as complete
        OnboardingJobTracker.completeJob(location_id, jobType);
      }

      return true;
    } catch (error) {
      logger.error(`Error in product data vectorization job:`, error);

      // Track failure in job tracker
      OnboardingJobTracker.failJob(
        location_id,
        "product_data_vectorization",
        error instanceof Error ? error.message : String(error)
      );

      // Retry the entire job if we haven't exceeded max retries
      if (retry_count < MAX_RETRIES) {
        const nextRetryCount = retry_count + 1;
        const delayMs = Math.pow(2, nextRetryCount) * 1000; // Exponential backoff

        logger.info(
          `Scheduling full job retry ${nextRetryCount}/${MAX_RETRIES} after ${delayMs}ms`
        );

        await ProductDataVectorJob.from({
          location_id,
          batch_size,
          last_processed_id,
          retry_count: nextRetryCount,
          failed_ids,
          filter,
        })
          .delay(delayMs)
          .queue();
      } else {
        logger.error(
          `Failed to process product data batch after ${MAX_RETRIES} retries`
        );
      }

      throw error;
    }
  }
}

export default ProductDataVectorJob;
