/* eslint-disable indent */
import { Product } from "./Product";
import { VectorService, VectorData } from "../core/VectorService";
import { logger } from "../config/logger";

const PRODUCT_INDEX = "product-embeddings";

export class ProductDataVectorService {
  private static vectorService: VectorService | null = null;

  /**
   * Get the vector service instance
   */
  private static getVectorService(): VectorService {
    if (!this.vectorService) {
      this.vectorService = VectorService.getInstance();
    }
    return this.vectorService;
  }

  static async initialize() {
    await this.getVectorService().initialize();
  }

  static async upsertProductData(products: Product[]) {
    try {
      if (!products.length) {
        logger.warn(
          "No product data provided to upsert, skipping vectorization"
        );
        return { successCount: 0, errorCount: 0, failedIds: [] };
      }

      // Filter out products that don't meet minimum requirements for vectorization
      const { validProducts, invalidProducts } =
        this.validateProductsForVectorization(products);

      if (invalidProducts.length > 0) {
        logger.warn(
          `Skipping ${invalidProducts.length} products with insufficient data for vectorization`,
          {
            invalidProductIds: invalidProducts.map((p) => p.id),
            reasons: invalidProducts.map((p) =>
              this.getValidationFailureReason(p)
            ),
          }
        );
      }

      if (validProducts.length === 0) {
        logger.warn(
          "No valid products found for vectorization after validation"
        );
        // Don't include validation failures in failedIds - they should just be skipped
        return {
          successCount: 0,
          errorCount: 0, // Don't count validation failures as errors for retry purposes
          failedIds: [], // Don't retry products that fail validation
          skippedCount: invalidProducts.length, // Track skipped products separately
          skippedIds: invalidProducts.map((p) => p.id),
        };
      }

      // Group valid records by location ID for namespace-based processing
      const recordsByLocation = validProducts.reduce((groups, record) => {
        const namespace = this.getNamespace(record.location_id);
        if (!groups[namespace]) {
          groups[namespace] = [];
        }
        groups[namespace].push(record);
        return groups;
      }, {} as Record<string, Product[]>);

      const totalResults = {
        successCount: 0,
        errorCount: 0, // Only count actual vectorization failures, not validation failures
        failedIds: [] as string[], // Only include actual vectorization failures for retry
        skippedCount: invalidProducts.length, // Track validation failures separately
        skippedIds: invalidProducts.map((p) => p.id),
      };

      // Process each location group with its own namespace
      for (const [namespace, records] of Object.entries(recordsByLocation)) {
        // Convert product data to vector format with enhanced text representation
        const vectorData: VectorData[] = records.map((record) => ({
          id: `product_${record.id}`,
          text: this.generateEnhancedText(record),
          metadata: {
            location_id: record.location_id,
            source_type: "product",
            source_id: record.id.toString(),
            meta_sku: record.meta_sku,
            retailer_id: record.retailer_id,
            brand_name: record.brand_name || "",
            product_id: record.product_id,
            image_url: record.image_url || "",
            product_name: record.product_name,
            category: record.category || "",
            subcategory: record.subcategory || "",
            thc: record.percentage_thc || record.thc || 0,
            cbd: record.percentage_cbd || record.cbd || 0,
            medical: record.medical,
            recreational: record.recreational,
            rating: record.rating || 0,
            reviews_count: record.reviews_count || 0,
            latest_price: record.latest_price || null,
            description: record.product_description
              ? record.product_description.substring(0, 250)
              : "", // Limit size for metadata
            tags: record.product_tags ? record.product_tags.join(",") : "",
            mood: record.mood
              ? Array.isArray(record.mood)
                ? record.mood.join(",")
                : String(record.mood)
              : "",
            enhancement_status: record.enhancement_status || "",
            created_at: Date.now(),
            updated_at: Date.now(),
          },
        }));

        // Use centralized vector service to upsert data with namespace
        const vectorService = this.getVectorService();
        const result = await vectorService.upsertVectors(
          PRODUCT_INDEX,
          vectorData,
          undefined, // Use default batch size
          namespace
        );

        // Aggregate results - only include actual vectorization failures
        totalResults.successCount += result.successCount;
        totalResults.errorCount += result.errorCount;
        totalResults.failedIds = totalResults.failedIds.concat(
          result.failedIds
        );

        logger.info(
          `Product data vectorization for namespace ${namespace} complete: ${result.successCount} succeeded, ${result.errorCount} failed`
        );
      }

      return totalResults;
    } catch (error) {
      logger.error("Error upserting product data vectors:", error);
      throw error;
    }
  }

  /**
   * Validates products to ensure they have sufficient data for vectorization
   */
  private static validateProductsForVectorization(products: Product[]): {
    validProducts: Product[];
    invalidProducts: Product[];
  } {
    const validProducts: Product[] = [];
    const invalidProducts: Product[] = [];

    for (const product of products) {
      if (this.isProductValidForVectorization(product)) {
        validProducts.push(product);
      } else {
        invalidProducts.push(product);
      }
    }

    return { validProducts, invalidProducts };
  }

  /**
   * Checks if a product has sufficient data for vectorization
   */
  private static isProductValidForVectorization(product: Product): boolean {
    // Check for required fields
    if (!product.product_name || product.product_name.trim().length === 0) {
      return false;
    }

    if (!product.meta_sku || product.meta_sku.trim().length === 0) {
      return false;
    }

    // Check for meaningful content - product needs at least some descriptive information
    const hasDescription =
      product.product_description &&
      product.product_description.trim().length > 10;
    const hasCategory = product.category && product.category.trim().length > 0;
    const hasBrand = product.brand_name && product.brand_name.trim().length > 0;
    const hasThcCbd =
      (product.percentage_thc && product.percentage_thc > 0) ||
      (product.percentage_cbd && product.percentage_cbd > 0) ||
      (product.thc && product.thc > 0) ||
      (product.cbd && product.cbd > 0);
    const hasTags = product.product_tags && product.product_tags.length > 0;
    const hasMood = this.hasMeaningfulMood(product.mood);

    // Product needs at least 2 of these meaningful pieces of information
    const meaningfulDataCount = [
      hasDescription,
      hasCategory,
      hasBrand,
      hasThcCbd,
      hasTags,
      hasMood,
    ].filter(Boolean).length;

    if (meaningfulDataCount < 2) {
      return true;
    }

    // Check that the generated text would have sufficient content
    const enhancedText = this.generateEnhancedText(product);
    if (enhancedText.length < 50) {
      // Minimum text length for meaningful vectorization
      return true;
    }

    return true;
  }

  /**
   * Gets a human-readable reason why a product failed validation
   */
  private static getValidationFailureReason(product: Product): string {
    if (!product.product_name || product.product_name.trim().length === 0) {
      return "Missing product name";
    }

    if (!product.meta_sku || product.meta_sku.trim().length === 0) {
      return "Missing SKU";
    }

    const hasDescription =
      product.product_description &&
      product.product_description.trim().length > 10;
    const hasCategory = product.category && product.category.trim().length > 0;
    const hasBrand = product.brand_name && product.brand_name.trim().length > 0;
    const hasThcCbd =
      (product.percentage_thc && product.percentage_thc > 0) ||
      (product.percentage_cbd && product.percentage_cbd > 0) ||
      (product.thc && product.thc > 0) ||
      (product.cbd && product.cbd > 0);
    const hasTags = product.product_tags && product.product_tags.length > 0;
    const hasMood = this.hasMeaningfulMood(product.mood);

    const meaningfulDataCount = [
      hasDescription,
      hasCategory,
      hasBrand,
      hasThcCbd,
      hasTags,
      hasMood,
    ].filter(Boolean).length;

    if (meaningfulDataCount < 2) {
      return "Insufficient descriptive data (needs at least 2 of: description, category, brand, THC/CBD, tags, or mood)";
    }

    const enhancedText = this.generateEnhancedText(product);
    if (enhancedText.length < 50) {
      return "Generated text too short for meaningful vectorization";
    }

    return "Unknown validation failure";
  }

  static async queryProductData(
    query: string,
    filters: { [key: string]: any } = {},
    topK: number = 10
  ) {
    try {
      if (!query) {
        throw new Error("Empty query provided to product data vector search");
      }

      // Extract location_id from filters for namespace
      const locationId = filters.location_id;
      if (!locationId && typeof locationId !== "number") {
        logger.warn(
          "No location_id provided in filters for product data query, this may cause errors"
        );
        // Return empty results when no location ID is specified
        return [];
      }
      // Get namespace for this location
      const namespace = this.getNamespace(locationId);

      // Remove location_id from filters since we're using namespace instead
      const { location_id, ...otherFilters } = filters;

      // Add pre-processing for query to improve search results
      const enhancedQuery = this.enhanceSearchQuery(query);

      // Check if index exists first to avoid unnecessary failures
      try {
        // Try to ensure index exists first (ignore errors from this check)
        const indexExists = await this.ensureIndexExists();
        logger.info(
          `Product index status: ${
            indexExists ? "exists" : "creation attempted"
          }`
        );
      } catch (indexCheckError) {
        logger.warn({
          message: "Error checking product index existence",
          error:
            indexCheckError instanceof Error
              ? indexCheckError.message
              : String(indexCheckError),
          stack:
            indexCheckError instanceof Error
              ? indexCheckError.stack
              : undefined,
        });
        // Continue despite index check error
      }

      try {
        const vectorService = this.getVectorService();
        return await vectorService.queryVectors(
          PRODUCT_INDEX,
          enhancedQuery,
          otherFilters, // Use other filters except location_id
          topK,
          namespace // Pass namespace for location-specific search
        );
      } catch (error) {
        // Enhanced error logging
        logger.error({
          message: "Error in vector query operation",
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          query: enhancedQuery,
          namespace,
          filters: otherFilters,
          index: PRODUCT_INDEX,
        });

        // Handle dimension mismatch errors
        if (error instanceof Error && error.message.includes("dimension")) {
          logger.warn(
            `Dimension mismatch detected in product index. Attempting to fix by recreating index.`
          );

          await this.recreateIndexWithCorrectDimension();

          // Try the query again after recreating the index
          logger.info("Retrying query after index recreation");

          try {
            // Note: This will only work if the index was successfully recreated AND there is data in it
            const vectorService = this.getVectorService();
            return await vectorService.queryVectors(
              PRODUCT_INDEX,
              enhancedQuery,
              otherFilters,
              topK,
              namespace
            );
          } catch (retryError) {
            logger.error(
              "Second attempt at querying after dimension fix failed:",
              retryError
            );
            return [];
          }
        }

        // Handle 404 errors specifically
        if (error instanceof Error && error.message.includes("404")) {
          logger.warn(
            `Product index "${PRODUCT_INDEX}" not found, attempting to create it`
          );
          await this.ensureIndexExists();

          // Return empty results instead of throwing
          return [];
        }

        // Handle other specific errors
        if (error instanceof Error && error.message.includes("filter")) {
          logger.warn(`Filter error with product data query: ${error.message}`);
          // Try with a simplified filter as last resort
          try {
            logger.info("Attempting query with simplified filter");
            const vectorService = this.getVectorService();
            return await vectorService.queryVectors(
              PRODUCT_INDEX,
              enhancedQuery,
              { source_type: "product" },
              topK,
              namespace
            );
          } catch (e) {
            logger.error("Second attempt at querying product data failed:", e);
            return [];
          }
        }

        throw error;
      }
    } catch (error) {
      logger.error({
        message: "Error querying product data vectors",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        query,
      });
      // Return empty results instead of throwing
      return [];
    }
  }

  /**
   * Creates an enhanced text representation of product data for better semantic matching
   */
  private static generateEnhancedText(record: Product): string {
    // Base text with core information
    let text = `Product: ${record.product_name}, ID: ${
      record.meta_sku
    }, Brand: ${record.brand_name || "Unknown"}`;

    // Add categorization
    if (record.category) {
      text += `, Category: ${record.category}`;
      if (record.subcategory) {
        text += `, Subcategory: ${record.subcategory}`;
      }
    }

    // Add product description if available
    if (record.product_description) {
      text += `. Description: ${record.product_description}`;
    }

    // Add short description if available
    if ((record as any).short_description) {
      text += `. Short Description: ${(record as any).short_description}`;
    }

    // Add cannabinoid information
    if (record.percentage_thc || record.thc) {
      text += `. THC: ${record.percentage_thc || record.thc}%`;
    }
    if (record.percentage_cbd || record.cbd) {
      text += `. CBD: ${record.percentage_cbd || record.cbd}%`;
    }

    // Add price information
    if (record.latest_price) {
      text += `. Price: $${record.latest_price}`;
    }

    // Add inventory information
    if ((record as any).inventory_quantity !== undefined) {
      text += `. Inventory: ${(record as any).inventory_quantity} units`;
    }
    if ((record as any).out_of_stock) {
      text += `. Status: Out of Stock`;
    }

    // Add user type information
    const userTypes = [];
    if (record.medical) userTypes.push("Medical");
    if (record.recreational) userTypes.push("Recreational");
    if (userTypes.length > 0) {
      text += `. Available for: ${userTypes.join(" and ")} users`;
    }

    // Add rating information
    if (record.rating) {
      text += `. Rated ${record.rating}/5 based on ${
        record.reviews_count || 0
      } reviews`;
    }

    // Add tags if available
    if (record.product_tags && record.product_tags.length > 0) {
      text += `. Tags: ${record.product_tags.join(", ")}`;
    }

    // Add slug for SEO purposes
    if ((record as any).slug) {
      text += `. URL Slug: ${(record as any).slug}`;
    }

    // Add mood if available - handle both string and array formats
    if (record.mood) {
      let moodText = "";
      if (Array.isArray(record.mood)) {
        moodText = record.mood.join(", ");
      } else if (typeof record.mood === "string") {
        moodText = record.mood;
      }
      if (moodText.trim()) {
        text += `. Moods: ${moodText}`;
      }
    }

    // Add effects if available - handle multiple formats
    if (record.effects) {
      let effectsText = ". Effects: ";
      if (
        typeof record.effects === "object" &&
        !Array.isArray(record.effects)
      ) {
        // Handle structured effects object
        if (record.effects.description) {
          effectsText += record.effects.description;
        } else if (record.effects.onset || record.effects.duration) {
          effectsText += `${record.effects.onset || ""} onset, ${
            record.effects.duration || ""
          } duration`;
        } else {
          // Fallback for generic object - stringify it
          effectsText += JSON.stringify(record.effects);
        }
      } else if (Array.isArray(record.effects)) {
        // Handle effects array
        effectsText += record.effects.join(", ");
      } else if (typeof record.effects === "string") {
        // Handle effects string
        effectsText += record.effects;
      }

      // Only add effects text if we have meaningful content
      if (effectsText !== ". Effects: ") {
        text += effectsText;
      }
    }

    return text;
  }

  /**
   * Enhances search queries for better product data retrieval
   */
  private static enhanceSearchQuery(query: string): string {
    // Convert simple product queries to more specific search terms
    let enhancedQuery = query;

    // Keywords for common product queries
    if (
      query.toLowerCase().includes("strain") ||
      query.toLowerCase().includes("flower")
    ) {
      enhancedQuery += " cannabis marijuana product";
    }

    if (
      query.toLowerCase().includes("high thc") ||
      query.toLowerCase().includes("strong")
    ) {
      enhancedQuery += " potent percentage_thc";
    }

    if (
      query.toLowerCase().includes("edible") ||
      query.toLowerCase().includes("food") ||
      query.toLowerCase().includes("gummy")
    ) {
      enhancedQuery += " ingestible cannabis food";
    }

    if (
      query.toLowerCase().includes("effect") ||
      query.toLowerCase().includes("feel")
    ) {
      enhancedQuery += " mood experience sensation";
    }

    return enhancedQuery;
  }

  /**
   * Validates that the vector index exists and creates it if needed
   */
  static async ensureIndexExists() {
    try {
      await this.getVectorService().initialize();

      // Check if index exists first by listing indices
      const indices = await this.getVectorService().listIndices();
      const indexExists = indices.includes(PRODUCT_INDEX);

      if (!indexExists) {
        logger.info(`Product index '${PRODUCT_INDEX}' not found, creating it`);

        // Attempt to create the index if it doesn't exist
        await this.getVectorService().createIndex(PRODUCT_INDEX, {
          dimension: 3072, // Updated dimension for text-embedding-3-large model (was 1536)
          metric: "cosine",
          serverless: {
            cloud: "aws", // Specify AWS instead of GCP
            region: "us-east-1", // Specify the us-east-1 region as required for free tier
          },
        });

        logger.info(`Product index '${PRODUCT_INDEX}' created successfully`);
      } else {
        logger.info(`Product index '${PRODUCT_INDEX}' already exists`);
      }

      return true;
    } catch (error) {
      logger.error(`Failed to verify product index: ${error}`);
      // Don't throw, just return false
      return false;
    }
  }

  static async deleteProductData(locationId: number) {
    try {
      if (!locationId) {
        logger.warn(
          "Attempted to delete product data without specifying location_id"
        );
        return false;
      }

      // First, ensure the index exists to avoid errors
      await this.ensureIndexExists();

      // Delete all vectors in this location's namespace
      const namespace = this.getNamespace(locationId);
      logger.info(
        `Deleting product data vectors for location ${locationId} (namespace: ${namespace})`
      );
      const vectorService = this.getVectorService();
      return await vectorService.deleteNamespace(PRODUCT_INDEX, namespace);
    } catch (error) {
      logger.error("Error deleting product data vectors:", error);
      throw error;
    }
  }

  /**
   * Empties the entire product index, removing all vectors across all namespaces
   * Use with caution as this will delete all product data vectors
   */
  static async emptyIndex() {
    try {
      logger.warn(
        "Emptying entire product index - this will delete ALL product vectors for ALL locations"
      );
      const vectorService = this.getVectorService();
      return await vectorService.emptyIndex(PRODUCT_INDEX);
    } catch (error) {
      logger.error("Error emptying product index:", error);
      throw error;
    }
  }

  /**
   * Delete and recreate the index with the correct dimension
   */
  static async recreateIndexWithCorrectDimension() {
    try {
      logger.info(
        `Attempting to recreate product index '${PRODUCT_INDEX}' with correct dimension`
      );

      // First, try to delete the existing index
      try {
        logger.info(`Deleting existing product index '${PRODUCT_INDEX}'`);
        await this.getVectorService().deleteIndex(PRODUCT_INDEX);
        logger.info(`Successfully deleted product index '${PRODUCT_INDEX}'`);
      } catch (deleteError) {
        logger.error(`Error deleting product index: ${deleteError}`);
        // Continue even if delete fails - it might not exist
      }

      // Then create a new index with the correct dimension
      logger.info(`Creating new product index with dimension 3072`);
      await this.getVectorService().createIndex(PRODUCT_INDEX, {
        dimension: 3072, // For text-embedding-3-large
        metric: "cosine",
        serverless: {
          cloud: "aws",
          region: "us-east-1",
        },
      });

      logger.info(
        `Successfully recreated product index with correct dimension`
      );
      return true;
    } catch (error) {
      logger.error(`Failed to recreate product index: ${error}`);
      return false;
    }
  }

  /**
   * Converts a location ID to a namespace string
   */
  private static getNamespace(locationId: number): string {
    return `location-${locationId}`;
  }

  /**
   * Gets vector statistics for a specific location namespace
   */
  static async getLocationStats(locationId: number) {
    try {
      const vectorService = this.getVectorService();
      const namespace = this.getNamespace(locationId);
      const stats = await vectorService.getIndexStats(PRODUCT_INDEX);

      // Check if the namespace exists in the stats
      if (stats.namespaces && stats.namespaces[namespace]) {
        return {
          recordCount: stats.namespaces[namespace].recordCount || 0,
          vectorCount: stats.namespaces[namespace].recordCount || 0, // Maintain backward compatibility
          namespace,
        };
      }

      return {
        recordCount: 0,
        vectorCount: 0, // Maintain backward compatibility
        namespace,
      };
    } catch (error) {
      logger.error(`Error getting stats for location ${locationId}:`, error);
      return {
        recordCount: 0,
        vectorCount: 0, // Maintain backward compatibility
        namespace: this.getNamespace(locationId),
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * Helper function to check if mood has meaningful content
   */
  private static hasMeaningfulMood(mood: any): boolean {
    return (
      (Array.isArray(mood) && mood.length > 0) ||
      // @ts-ignore - TypeScript incorrectly infers 'never' type
      (typeof mood === "string" && mood.trim().length > 0)
    );
  }
}
