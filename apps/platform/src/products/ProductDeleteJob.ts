import { Job } from "../queue";
import { Product } from "./Product";

interface ProductDeleteTrigger {
  location_id: number;
  meta_sku: string;
}

export default class ProductDeleteJob extends Job {
  static $name = "product_delete";

  static from(data: ProductDeleteTrigger): ProductDeleteJob {
    return new this(data);
  }

  static async handler({
    location_id,
    meta_sku,
  }: ProductDeleteTrigger): Promise<void> {
    await Product.query().where({ location_id, meta_sku }).delete();
  }
}
