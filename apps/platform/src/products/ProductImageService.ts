import { Readable } from "stream";
import Storage from "../storage/Storage";
import { uuid } from "../utilities";
import Image from "../storage/Image";
import App from "../app";
import { logger } from "../config/logger";
import { existsSync, mkdirSync } from "fs";
import * as path from "path";

export class ProductImageService {
  private _storage?: Storage;

  private get storage(): Storage {
    if (!this._storage) {
      this._storage = App.main.storage;
    }
    return this._storage;
  }

  /**
   * Process and store an uploaded product image
   * @param locationId The location ID
   * @param imageBuffer The image buffer data
   * @param fileName Original file name
   * @param contentType MIME type of the image
   * @param fileSize Size of the file in bytes
   * @returns Promise with the stored image record and public URL
   */
  async storeProductImage(
    locationId: number,
    imageBuffer: Buffer,
    fileName: string,
    contentType: string,
    fileSize: number
  ): Promise<{ image: Image; url: string }> {
    try {
      logger.info(
        `Storing product image: ${fileName} (${contentType}, ${fileSize} bytes)`
      );

      // Generate folder path specific to products
      const folderPath = `products/${locationId}`;

      // Ensure the storage folder exists if using local storage
      if (this.storage.provider.constructor.name === "LocalStorageProvider") {
        const localPath = path.join(
          process.env.STORAGE_LOCAL_PATH || "./storage",
          folderPath
        );

        if (!existsSync(localPath)) {
          logger.info(`Creating local storage directory: ${localPath}`);
          mkdirSync(localPath, { recursive: true });
        }
      }

      // Generate a unique filename with the correct extension
      const extension = contentType.split("/")[1] || "jpg";
      const uniqueFileName = `${uuid()}.${extension}`;
      const filePath = `${folderPath}/${uniqueFileName}`;

      logger.info(
        `Generated unique file path: ${filePath} + ${this.storage.provider.constructor.name}`
      );

      // Upload the image to storage
      try {
        await this.storage.upload({
          stream: Readable.from(imageBuffer),
          url: this.storage.provider.path(filePath),
          metadata: {
            contentType,
          },
        });
        logger.info(`Successfully uploaded image to storage at ${filePath}`);
      } catch (uploadError) {
        logger.error("Error uploading image to storage:", uploadError);
        logger.error("Storage provider details:", {
          provider: this.storage.provider.constructor.name,
        });
        throw uploadError;
      }

      // Create the image record
      const image = await Image.insertAndFetch({
        location_id: locationId,
        uuid: uniqueFileName.split(".")[0],
        name: fileName,
        original_name: fileName,
        extension: `.${extension}`,
        file_size: fileSize,
        path: filePath, // Store the full path including folder structure
      });

      logger.info(
        `Successfully stored product image: ${image.uuid}, path: ${image.path}, url: ${image.url}`
      );
      return { image, url: image.url };
    } catch (error) {
      logger.error("Error storing product image:", error);
      throw error;
    }
  }

  /**
   * Download a product image from a URL and store it
   * @param locationId The location ID
   * @param imageUrl URL of the image to download
   * @param name Optional name for the image
   * @returns Promise with the stored image and its public URL
   */
  async downloadAndStoreProductImage(
    locationId: number,
    imageUrl: string,
    name?: string
  ): Promise<{ image: Image; url: string }> {
    try {
      logger.info(`Downloading product image from URL: ${imageUrl}`);

      // Download the image
      const response = await fetch(imageUrl);
      if (!response.ok) {
        logger.error(
          `Failed to download image: ${response.statusText}, Status: ${response.status}`
        );
        throw new Error(
          `Failed to download image: ${response.statusText}, Status: ${response.status}`
        );
      }

      // Get content type and validate it's an image
      const contentType = response.headers.get("content-type") || "image/jpeg";
      logger.info(`Image content type: ${contentType}`);

      if (!contentType.startsWith("image/")) {
        logger.error(`Invalid content type: ${contentType}`);
        throw new Error(`Invalid content type: ${contentType}`);
      }

      // Get the image data as a buffer
      const arrayBuffer = await response.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      logger.info(`Image buffer size: ${buffer.length} bytes`);

      // Store the image
      const fileName = name || `product-${new Date().getTime()}`;
      return await this.storeProductImage(
        locationId,
        buffer,
        fileName,
        contentType,
        buffer.length
      );
    } catch (error) {
      logger.error("Error downloading and storing product image:", error);
      throw error;
    }
  }
}

export const productImageService = new ProductImageService();
