/* eslint-disable indent */
import { Product, ProductParams } from "./Product";

/**
 * Maps product data from various sources to our standardized Product model format
 */
export class ProductMapper {
  /**
   * Maps an API product object to our internal Product model
   * @param apiProduct - Product data from the API
   * @returns Formatted product parameters ready for database insertion
   */
  static mapFromAPI(apiProduct: any): ProductParams {
    // Extract base level product data
    const product: ProductParams = {
      meta_sku: apiProduct.meta_sku,
      retailer_id: apiProduct.retailer_id?.toString() || "",
      cann_sku_id: apiProduct.cann_sku_id,
      brand_name: apiProduct.brand_name,
      brand_id: apiProduct.brand_id,
      url: apiProduct.url,
      image_url: apiProduct.image_url,
      images_urls: apiProduct.images_urls,
      raw_product_name: apiProduct.raw_product_name,
      product_name: apiProduct.product_name || apiProduct.name,
      raw_weight_string: apiProduct.raw_weight_string,
      display_weight: apiProduct.display_weight,
      raw_product_category: apiProduct.raw_product_category,
      category: apiProduct.category,
      raw_subcategory: apiProduct.raw_subcategory,
      subcategory: apiProduct.subcategory,
      product_tags: Array.isArray(apiProduct.product_tags)
        ? apiProduct.product_tags
        : typeof apiProduct.product_tags === "string"
        ? apiProduct.product_tags.split(",").map((t: string) => t.trim())
        : undefined,
      percentage_thc: apiProduct.percentage_thc,
      percentage_cbd: apiProduct.percentage_cbd,
      mg_thc: apiProduct.mg_thc,
      mg_cbd: apiProduct.mg_cbd,
      quantity_per_package: apiProduct.quantity_per_package,
      medical: !!apiProduct.medical,
      recreational: !!apiProduct.recreational,
      latest_price: apiProduct.latest_price,
      menu_provider: apiProduct.menu_provider,
      review_summary: apiProduct.review_summary,
      rating: apiProduct.rating,
      reviews_count: apiProduct.reviews_count,
      product_description: apiProduct.product_description,
      thc: apiProduct.thc,
      cbd: apiProduct.cbd,
      variants: apiProduct.variants,
      enhancement_status: apiProduct.enhancement_status,
      mood: this.formatArrayOrString(apiProduct.mood) || undefined,
      estimated_cbd_percentage:
        this.formatPercentageValue(apiProduct.estimated_cbd_percentage) ||
        undefined,
      estimated_thc_percentage:
        this.formatPercentageValue(apiProduct.estimated_thc_percentage) ||
        undefined,
      effects: apiProduct.effects,
      enhancement_error: apiProduct.enhancement_error,
      external_id: apiProduct.id,

      // Map new wholesale and retail price fields
      wholesale_price: apiProduct.wholesale_price,
      retail_price: apiProduct.retail_price,
      msrp: apiProduct.msrp,
      profit_margin: apiProduct.profit_margin,

      // Map new grower and cultivar information
      grower_name: apiProduct.grower_name,
      cultivar: apiProduct.cultivar,
      batch_number: apiProduct.batch_number,
      harvest_date: apiProduct.harvest_date,
      coa_url: apiProduct.coa_url,
    };

    return product;
  }

  /**
   * Format a value that could be either a string or an array
   */
  private static formatArrayOrString(value: any): string[] | null {
    if (Array.isArray(value)) {
      return value;
    }
    if (typeof value === "string") {
      return [value];
    }
    return null;
  }

  /**
   * Format a percentage value to ensure consistent format
   */
  private static formatPercentageValue(value: any): string | null {
    if (value === null || value === undefined) {
      return null;
    }
    if (typeof value === "number") {
      return value.toString() + "%";
    }
    if (typeof value === "string") {
      if (value.includes("%")) {
        return value;
      }
      return value + "%";
    }
    return null;
  }
}
