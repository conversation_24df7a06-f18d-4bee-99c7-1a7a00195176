export interface ProductParams {
  meta_sku: string;
  retailer_id?: string;
  cann_sku_id?: string;
  brand_name?: string;
  brand_id?: number;
  url?: string;
  image_url: string;
  images_urls?: string;
  raw_product_name?: string;
  product_name: string;
  raw_weight_string?: string;
  display_weight?: string;
  raw_product_category?: string;
  category: string;
  raw_subcategory?: string;
  subcategory?: string;
  product_tags?: string[];
  percentage_thc?: number;
  percentage_cbd?: number;
  mg_thc?: number;
  mg_cbd?: number;
  quantity_per_package?: number;
  medical?: boolean;
  recreational?: boolean;
  latest_price: number;
  menu_provider?: string;
  review_summary?: string;
  rating?: number;
  reviews_count?: number;
  product_description?: string;
  short_description?: string;
  thc?: number;
  cbd?: number;
  variants?: any;
  enhancement_status?: string;
  mood?: string[];
  estimated_cbd_percentage?: string;
  estimated_thc_percentage?: string;
  effects?: any;
  enhancement_error?: string;
  external_id?: string;

  // New wholesale and retail price fields
  wholesale_price?: number;
  retail_price?: number;
  msrp?: number;
  profit_margin?: number;

  // New grower and cultivar information
  grower_name?: string;
  cultivar?: string;
  batch_number?: string;
  harvest_date?: string | Date;
  coa_url?: string;

  source?: string;
  data?: Record<string, unknown>;

  // Product visibility
  is_active?: boolean;

  // Inventory and stock management
  out_of_stock?: boolean;
  inventory_quantity?: number;

  // Slug for URL generation
  slug?: string;

  // Add index signature to allow string indexing
  [key: string]: any;
}
