/* eslint-disable indent */
import { Job } from "../queue";
import { Product, ProductInternalParams } from "./Product";
import {
  createProduct,
  getProductFromMetaSku,
  getProductByProductIdAndRetailerId,
} from "./ProductRepository";
import { v4 as uuidv4 } from "uuid";
import { logger } from "../config/logger";

interface ProductPatchTrigger {
  location_id: number;
  product: ProductInternalParams;
}

/**
 * Format a date string to MySQL compatible format
 * This removes the 'Z' timezone indicator which can cause issues with MySQL
 */
const formatDateForMySQL = (dateString: string | Date): Date => {
  if (typeof dateString === "string") {
    // For ISO string format with Z, convert to MySQL compatible format
    const dateWithoutZ = dateString.replace("Z", "");
    return new Date(dateWithoutZ);
  }
  return dateString;
};

/**
 * Normalize image URL to fit within database constraints
 * This either removes query parameters or truncates the URL if it's too long
 */
const normalizeImageUrl = (
  imageUrl: string | undefined
): string | undefined => {
  if (!imageUrl) return undefined;

  try {
    // Maximum length that will fit in the database column
    const MAX_URL_LENGTH = 255;

    // If the URL is already short enough, return it as is
    if (imageUrl.length <= MAX_URL_LENGTH) {
      return imageUrl;
    }

    // First try: remove query parameters to shorten URL
    const urlWithoutParams = imageUrl.split("?")[0];
    if (urlWithoutParams.length <= MAX_URL_LENGTH) {
      logger.info(
        `Shortened image URL by removing query params: ${imageUrl.length} → ${urlWithoutParams.length} chars`
      );
      return urlWithoutParams;
    }

    // Last resort: truncate the URL and add an ellipsis
    const truncatedUrl =
      urlWithoutParams.substring(0, MAX_URL_LENGTH - 3) + "...";
    logger.warn(
      `Heavily truncated image URL from ${imageUrl.length} to ${truncatedUrl.length} chars`
    );
    return truncatedUrl;
  } catch (error) {
    logger.error(`Failed to normalize image URL: ${error}`);
    return undefined; // Return undefined on error
  }
};

export default class ProductPatchJob extends Job {
  static $name = "product_patch";

  static from(data: ProductPatchTrigger): ProductPatchJob {
    // Add default values for required fields if they're missing
    if (!data.product.meta_sku) {
      // Generate a UUID for meta_sku if not provided
      data.product.meta_sku = uuidv4();
    }

    if (!data.product.retailer_id) {
      // Use location ID as retailer_id if not provided
      data.product.retailer_id = `loc_${data.location_id}`;
    }

    if (data.product.medical === undefined) {
      data.product.medical = true; // Default to true
    }

    if (data.product.recreational === undefined) {
      data.product.recreational = true; // Default to true
    }

    if (!data.product.raw_product_name && data.product.product_name) {
      data.product.raw_product_name = data.product.product_name;
    }

    if (!data.product.source) {
      data.product.source = "manual"; // Default source
    }

    // If an ID was passed in and external_id is not set, store the ID as external_id
    if ((data.product as any).id && !data.product.external_id) {
      data.product.external_id = (data.product as any).id.toString();
    }

    // Remove the ID field to let the database auto-generate it
    if ((data.product as any).id) {
      delete (data.product as any).id;
    }

    // Normalize the image URL to prevent "Data too long" errors
    data.product.image_url = normalizeImageUrl(data.product.image_url);

    // Also normalize image URL in the data object if it exists
    if (
      data.product.data &&
      typeof data.product.data === "object" &&
      data.product.data.image_url
    ) {
      const productData = data.product.data as Record<string, any>;
      if (typeof productData.image_url === "string") {
        productData.image_url = normalizeImageUrl(productData.image_url);
      }
    }

    return new this(data);
  }

  static async handler(patch: ProductPatchTrigger): Promise<Product> {
    const upsert = async (
      patch: ProductPatchTrigger,
      tries = 3
    ): Promise<Product> => {
      const { location_id, product } = patch;

      // Extract fields we need to handle specially
      const {
        meta_sku,
        data,
        harvest_date,
        created_at,
        updated_at,
        ...fields
      } = product;

      // Ensure we don't pass 'id' to the createProduct function
      if ((fields as any).id) {
        delete (fields as any).id;
      }

      // Parse harvest_date if it's a string
      const parsedHarvestDate =
        harvest_date && typeof harvest_date === "string"
          ? new Date(harvest_date)
          : (harvest_date as Date | undefined);

      // Format created_at if present to be MySQL compatible
      const parsedCreatedAt = created_at
        ? formatDateForMySQL(created_at)
        : undefined;

      // Format updated_at if present to be MySQL compatible
      const parsedUpdatedAt = updated_at
        ? formatDateForMySQL(updated_at)
        : undefined;

      try {
        // First check by product_id and retailer_id if both are provided
        const productId = product.product_id;
        const retailerId = product.retailer_id;

        if (
          productId &&
          typeof productId === "string" &&
          retailerId &&
          typeof retailerId === "string"
        ) {
          const existingByProductId = await getProductByProductIdAndRetailerId(
            location_id,
            productId,
            retailerId
          );

          // If found by product_id and retailer_id, update it
          if (existingByProductId) {
            logger.info(
              `Updating existing product by product_id: ${productId} and retailer_id: ${retailerId} for location ${location_id}`
            );
            return await Product.updateAndFetch(existingByProductId.id, {
              meta_sku, // Ensure meta_sku is updated to match the new one
              data: data ? { ...existingByProductId.data, ...data } : undefined,
              ...fields,
              harvest_date: parsedHarvestDate,
              created_at: parsedCreatedAt,
              updated_at: parsedUpdatedAt || new Date(),
            });
          }
        }

        // Then check by meta_sku as a fallback
        const existingByMetaSku = await getProductFromMetaSku(
          location_id,
          meta_sku
        );

        // If found by meta_sku, update it
        if (existingByMetaSku) {
          logger.info(
            `Updating existing product by meta_sku: ${meta_sku} for location ${location_id}`
          );
          return await Product.updateAndFetch(existingByMetaSku.id, {
            data: data ? { ...existingByMetaSku.data, ...data } : undefined,
            ...fields,
            harvest_date: parsedHarvestDate,
            created_at: parsedCreatedAt,
            updated_at: parsedUpdatedAt || new Date(),
          });
        }

        // If no existing product found, create a new one
        logger.info(
          `Creating new product with meta_sku: ${meta_sku} for location ${location_id}`
        );
        return await createProduct(location_id, {
          meta_sku,
          data,
          ...fields,
          harvest_date: parsedHarvestDate,
          created_at: parsedCreatedAt,
          updated_at: parsedUpdatedAt || new Date(),
        });
      } catch (error: any) {
        if (
          error.code === "ER_DUP_ENTRY" &&
          (error.sqlMessage?.includes("products_location_id_meta_sku_unique") ||
            error.sqlMessage?.includes(
              "products_product_id_retailer_id_unique"
            ))
        ) {
          // Handle the duplicate entry error specifically
          logger.warn(
            `Duplicate entry detected: ${error.sqlMessage}. Retrying with lookup...`
          );

          // Wait a short time before retrying
          await new Promise((resolve) => setTimeout(resolve, 100));

          // Try to get the product again by product_id and retailer_id first
          const productId = product.product_id;
          const retailerId = product.retailer_id;

          let existingProduct = null;

          if (productId && retailerId) {
            existingProduct = await getProductByProductIdAndRetailerId(
              location_id,
              productId,
              retailerId
            );
          }

          // If not found by product_id and retailer_id, try by meta_sku
          if (!existingProduct) {
            existingProduct = await getProductFromMetaSku(
              location_id,
              meta_sku
            );
          }

          if (existingProduct) {
            // Update the existing product
            logger.info(
              `Updating existing product after duplicate entry resolution: ${existingProduct.id}`
            );
            return await Product.updateAndFetch(existingProduct.id, {
              data: data ? { ...existingProduct.data, ...data } : undefined,
              ...fields,
              harvest_date: parsedHarvestDate,
              created_at: parsedCreatedAt,
              updated_at: parsedUpdatedAt || new Date(),
            });
          }

          // If we still can't find it, this is unexpected
          logger.error(
            `Could not resolve duplicate entry for product_id ${productId} and retailer_id ${retailerId}`,
            error
          );
        } else if (error.code === "ER_DATA_TOO_LONG") {
          // Handle "Data too long" errors by identifying and truncating the problematic field
          logger.warn(`Data too long error: ${error.sqlMessage}`);

          let fieldFixed = false;

          // Check if enhancement_error field is too long (max 255 chars in most cases)
          if (
            fields.enhancement_error &&
            typeof fields.enhancement_error === "string" &&
            fields.enhancement_error.length > 255
          ) {
            logger.warn(
              `Truncating enhancement_error from ${fields.enhancement_error.length} chars to 255 chars`
            );
            fields.enhancement_error = fields.enhancement_error.substring(0, 252) + "...";
            fieldFixed = true;
          }

          // Check if image_url field is too long (typical VARCHAR limit is 255 or 1000)
          if (
            fields.image_url &&
            typeof fields.image_url === "string" &&
            fields.image_url.length > 255
          ) {
            logger.warn(
              `Truncating image_url from ${fields.image_url.length} chars to 255 chars`
            );
            fields.image_url = fields.image_url.split("?")[0].substring(0, 255);

            if (data && typeof data === "object") {
              const productData = data as Record<string, any>;
              if (typeof productData.image_url === "string") {
                productData.image_url = fields.image_url;
              }
            }
            fieldFixed = true;
          }

          // Check other string fields that might be too long
          const stringFields = [
            'product_name', 'brand_name', 'raw_product_name', 'category',
            'subcategory', 'raw_subcategory', 'raw_product_category',
            'display_weight', 'raw_weight_string', 'url', 'cann_sku_id',
            'menu_provider', 'grower_name', 'cultivar', 'batch_number', 'coa_url'
          ];

          for (const fieldName of stringFields) {
            if (
              fields[fieldName] &&
              typeof fields[fieldName] === "string" &&
              fields[fieldName].length > 255
            ) {
              logger.warn(
                `Truncating ${fieldName} from ${fields[fieldName].length} chars to 255 chars`
              );
              fields[fieldName] = fields[fieldName].substring(0, 252) + "...";
              fieldFixed = true;
            }
          }

          // If we fixed a field, retry the operation
          if (fieldFixed && tries > 0) {
            logger.info(`Retrying upsert after truncating long fields`);
            return upsert(
              {
                location_id,
                product: {
                  meta_sku,
                  data,
                  harvest_date,
                  created_at,
                  updated_at,
                  ...fields,
                },
              },
              tries - 1
            );
          } else {
            logger.error(
              `Could not identify which field is too long. Error: ${error.sqlMessage}`
            );
          }
        }

        // For other errors or if we've tried too many times
        if (tries <= 0) {
          logger.error(
            `Failed to upsert product after multiple attempts: ${error.message}`,
            error
          );
          throw error;
        }

        // Wait a short time before retrying to allow any transactions to complete
        await new Promise((resolve) => setTimeout(resolve, 100));
        return upsert(patch, --tries);
      }
    };

    return await upsert(patch);
  }
}
