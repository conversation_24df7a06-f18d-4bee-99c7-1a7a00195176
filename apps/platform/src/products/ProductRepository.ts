import { PageParams } from "../core/searchParams";
import { Product, ProductInternalParams } from "./Product";

export const getProduct = async (
  id: number,
  locationId?: number
): Promise<Product | undefined> => {
  return await Product.find(id, (qb) => {
    if (locationId) {
      qb.where("location_id", locationId);
    }
    return qb;
  });
};

export const getProductFromMetaSku = async (
  locationId: number,
  metaSku: string
): Promise<Product | undefined> => {
  return await Product.first((qb) =>
    qb.where("meta_sku", metaSku).where("location_id", locationId)
  );
};

export const getProductByProductIdAndRetailerId = async (
  locationId: number,
  productId: string,
  retailerId: string
): Promise<Product | undefined> => {
  return await Product.first((qb) =>
    qb
      .where("location_id", locationId)
      .where("product_id", productId)
      .where("retailer_id", retailerId)
  );
};

export const pagedProducts = async (params: PageParams, locationId: number) => {
  // Build the base query for both count and search
  const baseQuery = (qb: any) => qb.where("location_id", locationId);

  // Get the search results
  const searchResults = await Product.search(
    {
      ...params,
      fields: ["meta_sku", "product_name", "brand_name"],
      mode: "exact",
    },
    baseQuery
  );

  // Get the total count
  const total = await Product.count(baseQuery);

  // Return search results with total count
  return {
    ...searchResults,
    total,
  };
};

export const createProduct = async (
  locationId: number,
  {
    meta_sku,
    retailer_id,
    harvest_date,
    created_at,
    updated_at,
    location_id,
    ...fields
  }: ProductInternalParams
) => {
  // Convert harvest_date string to Date object if it's a string
  const parsedHarvestDate =
    harvest_date && typeof harvest_date === "string"
      ? new Date(harvest_date)
      : (harvest_date as Date | undefined);

  // Convert created_at string to Date object if it's a string
  const parsedCreatedAt =
    created_at && typeof created_at === "string"
      ? new Date(created_at)
      : (created_at as Date | undefined);

  // Convert updated_at string to Date object if it's a string
  const parsedUpdatedAt =
    updated_at && typeof updated_at === "string"
      ? new Date(updated_at)
      : (updated_at as Date | undefined);

  return await Product.insertAndFetch({
    location_id: locationId,
    meta_sku,
    retailer_id,
    ...fields,
    harvest_date: parsedHarvestDate,
    created_at: parsedCreatedAt,
    updated_at: parsedUpdatedAt,
  });
};
