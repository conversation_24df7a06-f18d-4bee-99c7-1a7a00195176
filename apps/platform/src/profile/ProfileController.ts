/**
 * @swagger
 * components:
 *   schemas:
 *     AdminProfile:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Admin ID
 *         email:
 *           type: string
 *           format: email
 *           description: Admin email address
 *         name:
 *           type: string
 *           description: Admin's full name
 *         role:
 *           type: string
 *           description: Ad<PERSON>'s role
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Account creation timestamp
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *         metadata:
 *           type: object
 *           description: Additional admin metadata
 *           additionalProperties: true
 */

/**
 * @swagger
 * tags:
 *   name: Profile
 *   description: Admin profile management endpoints
 */

/**
 * @swagger
 * /profile:
 *   get:
 *     summary: Get Admin Profile
 *     description: Retrieve the profile information of the currently authenticated admin
 *     tags: [Profile]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Admin profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AdminProfile'
 *       401:
 *         description: Unauthorized - Admin not authenticated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Unauthorized"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 */

import Router from "@koa/router";
import Admin from "../auth/Admin";
import { AuthState } from "../auth/AuthMiddleware";

const router = new Router<AuthState>({
  prefix: "/profile",
});

router.get("/", async (ctx) => {
  ctx.body = await Admin.find(ctx.state.admin!.id);
});

export default router;
