{"constraints": ["Return a single JSON object containing an items dictionary directly at the root level", "Do not nest the response under an automationPlan key", "Do not include any comments in the JSON output", "Use only valid JSON syntax", "Never use undefined values - use empty string \"\" instead of null for optional strings", "For timestamps, use ISO date strings (e.g. \"2025-01-02T00:00:00Z\")", "Do not include any markdown code block delimiters in the JSON output", "Lists must be referenced using either list_refs (array of keys) or list_ids (array of numbers), never both", "Templates must reference campaigns in ONE of these two ways (never both):", "  1. For new campaigns created in your plan: campaign_ref: \"campaignKey\" where campaignK<PERSON> is the key of the campaign in the items dictionary", "  2. For existing campaigns in the database: campaign_id: number", "Campaign and template channel types must match (email/text/push)", "Items must be created in sequential order: lists -> campaigns -> templates", "Template items must reference campaign items that come BEFORE them in the dictionary", "All required fields must be present", "No null values - use empty string \"\" instead", "Email templates must have from.name, from.address, and reply_to", "Text templates must have text content", "Push templates must have title and body", "Journey entrance nodes must be at coordinates (0, 0)", "Journey steps must have unique x,y coordinates", "Maintain ~150-200 pixel vertical spacing between journey levels", "Maintain ~200-300 pixel horizontal spacing between parallel paths", "Gate steps must have 'yes' and 'no' paths", "Experiment step variants must have ratios that sum to 100", "All journey paths must eventually connect to an exit node", "Journey step coordinates must use negative x for left paths, positive x for right paths", "Journey y coordinates must increase as paths flow downward", "Each journey step must have proper children array with external_id references", "Exit nodes must reference their entrance_uuid", "Action steps must have valid campaign_id or template_id", "Delay steps must specify duration+unit or until_time", "Entrance steps must have proper trigger configuration (schedule/event/api)"]}