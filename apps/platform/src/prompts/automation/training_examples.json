{"training_examples": [{"input": {"type": "automation", "description": "Create a welcome campaign with multiple templates"}, "output": {"name": "Multi-Template Welcome Campaign", "description": "Welcome email series for new customers", "items": {"newCustomersList": {"type": "list", "config": {"list": {"name": "New Customers List", "type": "dynamic", "description": "Automatically updated list of new customers", "rule": {"uuid": "123e4567-e89b-12d3-a456-************", "type": "date", "group": "user", "path": "created_at", "operator": ">=", "value": "2024-01-01", "children": []}, "is_visible": true, "tags": ["New Customers"], "user_ids": []}}}, "welcomeCampaign": {"type": "campaign", "config": {"campaign": {"type": "blast", "name": "Welcome Series Campaign", "channel": "email", "subscription_id": 1, "provider_id": 1, "list_refs": ["newCustomersList"], "send_in_user_timezone": true, "send_at": "", "tags": ["Welcome", "Series"]}}}, "welcomeTemplate": {"type": "template", "config": {"template": {"type": "email", "name": "Welcome Email Template", "locale": "en", "campaign_ref": "welcomeCampaign", "data": {"editor": "code", "name": "Welcome Email", "subject": "Welcome to Our Community!", "html": "<h1>Welcome</h1>", "text": "Welcome to our community!", "from": {"name": "Company", "address": "<EMAIL>"}, "reply_to": "<EMAIL>", "preheader": "Welcome to our community!"}}}}}}, "success": true}, {"input": {"type": "journey", "description": "Create a journey with proper step positioning", "payload": {"name": "Multi-Channel Christmas Special", "description": "Holiday campaign with email and SMS paths based on user preferences", "published": true}}, "output": {"name": "Multi-Channel Christmas Special", "description": "Holiday campaign with email and SMS paths based on user preferences", "published": true, "steps": {"entrance_node": {"type": "entrance", "name": "Xmas Special", "x": 0, "y": 0, "data": {"trigger": "schedule", "schedule": "DTSTART:20251225T000000Z", "multiple": false, "list_id": 4}, "children": [{"external_id": "email_gate", "path": "", "data": {}}]}, "email_gate": {"type": "gate", "name": "Has email", "x": 0, "y": 200, "data": {"rule": {"path": "$", "type": "wrapper", "uuid": "80e21ae0-4cd7-4c65-a3ba-e39d8b110e80", "group": "parent", "operator": "and", "children": [{"path": "$.email", "type": "string", "uuid": "64b0cfb3-f352-47c0-9f5b-284b92bc9336", "group": "user", "operator": "is set", "value": "", "root_uuid": "80e21ae0-4cd7-4c65-a3ba-e39d8b110e80", "parent_uuid": "80e21ae0-4cd7-4c65-a3ba-e39d8b110e80"}]}}, "children": [{"external_id": "email_balancer", "path": "yes", "data": {}}, {"external_id": "sms_experiment", "path": "no", "data": {}}]}, "email_balancer": {"type": "balancer", "name": "Email Rate Limit", "x": -300, "y": 400, "data": {"rate_limit": 1000, "rate_interval": "minute"}, "children": [{"external_id": "email_50_off", "path": "", "data": {}}]}, "sms_experiment": {"type": "experiment", "name": "SMS Test", "x": 300, "y": 400, "data": {}, "children": [{"external_id": "sms_50_off", "path": "", "data": {"ratio": 65}}, {"external_id": "sms_20_off", "path": "", "data": {"ratio": 35}}]}, "email_50_off": {"type": "action", "name": "50% off email", "x": -300, "y": 600, "data": {"campaign_id": 7}, "children": [{"external_id": "exit_node", "path": "", "data": {}}]}, "sms_50_off": {"type": "action", "name": "50% sms", "x": 150, "y": 600, "data": {"campaign_id": 8}, "children": [{"external_id": "exit_node", "path": "", "data": {}}]}, "sms_20_off": {"type": "action", "name": "20% sms", "x": 450, "y": 600, "data": {"campaign_id": 9}, "children": [{"external_id": "exit_node", "path": "", "data": {}}]}, "exit_node": {"type": "exit", "name": "Exit", "x": 0, "y": 800, "data": {"entrance_id": "entrance_node"}, "children": []}}}, "success": true}, {"input": {"type": "automation", "description": "Create a comprehensive VIP customer engagement plan with multiple campaigns, templates and journeys"}, "output": {"name": "VIP Customer Engagement Plan", "description": "Comprehensive automation plan for high-value customers with multiple engagement paths", "items": {"highValueCustomerList": {"type": "list", "config": {"list": {"name": "High Value Customers", "type": "dynamic", "description": "Customers with purchases over $300", "rule": {"uuid": "rule-001", "type": "number", "group": "pos", "path": "average_order_value", "operator": ">", "value": 300, "children": []}, "is_visible": true, "tags": ["VIP"], "user_ids": []}}}, "inactiveVIPsList": {"type": "list", "config": {"list": {"name": "Inactive VIP Customers", "type": "dynamic", "description": "High value customers who haven't made a purchase in 30 days", "rule": {"uuid": "rule-002", "type": "wrapper", "group": "parent", "path": "$", "operator": "and", "children": [{"uuid": "rule-003", "type": "number", "group": "pos", "path": "average_order_value", "operator": ">", "value": 300}, {"uuid": "rule-004", "type": "date", "group": "pos", "path": "last_purchase_date", "operator": "<", "value": "{{now-30d}}"}]}, "is_visible": true, "tags": ["VIP", "Inactive"], "user_ids": []}}}, "vipLoyaltyCampaign": {"type": "campaign", "config": {"campaign": {"type": "blast", "name": "VIP Loyalty Program Campaign", "channel": "email", "subscription_id": 73, "provider_id": 38, "list_refs": ["highValueCustomerList"], "send_in_user_timezone": true, "tags": ["VIP"]}}}, "vipReminderCampaign": {"type": "campaign", "config": {"campaign": {"type": "blast", "name": "VIP Loyalty Program Reminder", "channel": "email", "subscription_id": 73, "provider_id": 38, "list_refs": ["highValueCustomerList"], "send_in_user_timezone": true, "tags": ["VIP", "Reminder"]}}}, "vipSpecialOfferCampaign": {"type": "campaign", "config": {"campaign": {"type": "blast", "name": "VIP Special Offer Campaign", "channel": "email", "subscription_id": 73, "provider_id": 38, "list_refs": ["highValueCustomerList"], "send_in_user_timezone": true, "tags": ["VIP", "Special"]}}}, "reactivationCampaign": {"type": "campaign", "config": {"campaign": {"type": "blast", "name": "Inactive VIP Reactivation", "channel": "email", "subscription_id": 73, "provider_id": 38, "list_refs": ["inactiveVIPsList"], "send_in_user_timezone": true, "tags": ["VIP", "Reactivation"]}}}, "smsReminderCampaign": {"type": "campaign", "config": {"campaign": {"type": "blast", "name": "VIP SMS Reminder", "channel": "text", "subscription_id": 74, "provider_id": 37, "list_refs": ["highValueCustomerList"], "send_in_user_timezone": true, "tags": ["VIP", "SMS"]}}}, "vipLoyaltyTemplate": {"type": "template", "config": {"template": {"type": "email", "name": "VIP Loyalty Program Email", "locale": "en", "campaign_ref": "vipLoyaltyCampaign", "data": {"editor": "code", "name": "High AOV Loyalty Program Template", "subject": "Exclusive Loyalty Program Just for You!", "html": "<html><body style='background-color: #FFFFFF; margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Helvetica, Arial, sans-serif;'><table align='center' width='100%' cellpadding='0' cellspacing='0' style='max-width: 600px;'><tr><td style='text-align: center; padding: 20px;'><img src='https://storage.googleapis.com/bakedbot-agents.appspot.com/84c08584-7d51-4f3f-9af9-a5dda3e9d887.png' alt='Laser Tag Arena' style='width: 100%; height: auto;'></td></tr><tr><td style='padding: 20px; color: #1F2937;'><h1 style='color: #1F2937;'>Join Our Exclusive Loyalty Program!</h1><p style='color: #4B5563;'>Dear Valued Customer,</p><p>We're thrilled to invite you to our exclusive loyalty program. As a high-value customer, you deserve the best, and we're here to provide it. Enjoy special benefits, discounts, and more.</p><p style='text-align: center;'><a href='https://example.com/join' style='background-color: #2563EB; color: #FFFFFF; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Join Now</a></p><p style='color: #4B5563;'>Thank you for being a part of the Ultrazone Laser Tag family.</p><p style='color: #4B5563;'>Best regards,<br>The Ultrazone Team</p></td></tr></table></body></html>", "text": "Join Our Exclusive Loyalty Program! Dear Valued Customer, We're thrilled to invite you to our exclusive loyalty program. As a high-value customer, you deserve the best, and we're here to provide it. Enjoy special benefits, discounts, and more. Join now at https://example.com/join Thank you for being a part of the Ultrazone Laser Tag family. Best regards, The Ultrazone Team", "from": {"name": "Ultrazone Laser Tag", "email": "<EMAIL>", "address": "<EMAIL>"}, "reply_to": "<EMAIL>"}}}}, "vipReminderTemplate": {"type": "template", "config": {"template": {"type": "email", "name": "VIP Loyalty Program Reminder", "locale": "en", "campaign_ref": "vipReminderCampaign", "data": {"editor": "code", "name": "Loyalty Program Reminder", "subject": "Don't Miss Out on Your VIP Benefits!", "html": "<html><body><h1>Your VIP Benefits Await!</h1><p>We noticed you haven't joined our loyalty program yet. Don't miss out on exclusive rewards!</p><p><a href='https://example.com/join'>Join Now</a></p></body></html>", "text": "Your VIP Benefits Await! We noticed you haven't joined our loyalty program yet. Don't miss out on exclusive rewards! Join now: https://example.com/join", "from": {"name": "Ultrazone Laser Tag", "email": "<EMAIL>", "address": "<EMAIL>"}, "reply_to": "<EMAIL>"}}}}, "vipSpecialOfferTemplate": {"type": "template", "config": {"template": {"type": "email", "name": "VIP Special Offer Email", "locale": "en", "campaign_ref": "vipSpecialOfferCampaign", "data": {"editor": "code", "name": "Special Offer Template", "subject": "Special 25% Discount Just For You", "html": "<html><body><h1>Special VIP Offer</h1><p>As a thank you for joining our loyalty program, enjoy 25% off your next visit!</p><p><a href='https://example.com/offers'>Claim Discount</a></p></body></html>", "text": "Special VIP Offer! As a thank you for joining our loyalty program, enjoy 25% off your next visit! Claim your discount: https://example.com/offers", "from": {"name": "Ultrazone Laser Tag", "email": "<EMAIL>", "address": "<EMAIL>"}, "reply_to": "<EMAIL>"}}}}, "reactivationTemplate": {"type": "template", "config": {"template": {"type": "email", "name": "Inactive VIP Reactivation Email", "locale": "en", "campaign_ref": "reactivationCampaign", "data": {"editor": "code", "name": "Reactivation Template", "subject": "We Miss You - Special 30% Comeback Offer", "html": "<html><body><h1>We Miss You!</h1><p>It's been a while since your last visit. Come back and enjoy 30% off your next game!</p><p><a href='https://example.com/comeback'>Claim Your Offer</a></p></body></html>", "text": "We Miss You! It's been a while since your last visit. Come back and enjoy 30% off your next game! Claim your offer: https://example.com/comeback", "from": {"name": "Ultrazone Laser Tag", "email": "<EMAIL>", "address": "<EMAIL>"}, "reply_to": "<EMAIL>"}}}}, "smsReminderTemplate": {"type": "template", "config": {"template": {"type": "text", "name": "VIP SMS Reminder", "locale": "en", "campaign_ref": "smsReminderCampaign", "data": {"editor": "code", "name": "SMS Reminder", "subject": "VIP Program Reminder", "email": "<EMAIL>", "html": "<html><body><h1>VIP Program Reminder</h1><p>Don't forget to join our loyalty program for exclusive benefits! Click here: https://example.com/join</p></body></html>", "text": "Ultrazone VIP: Don't forget to join our loyalty program for exclusive benefits! Click here: https://example.com/join", "from": {"name": "Ultrazone", "address": "55555"}, "reply_to": "55555"}}}}, "vipCustomerJourney": {"type": "journey", "config": {"journey": {"name": "VIP Customer Engagement Journey", "description": "Multi-stage journey for high-value customers with personalized engagement paths", "published": true, "tags": ["VIP", "Loyalty", "Engagement"], "steps": {"entrance_step": {"type": "entrance", "name": "Journey Start", "x": 0, "y": 0, "data": {"trigger": "schedule", "multiple": true, "schedule": "DTSTART:20250329T000000Z\nRRULE:BYDAY=WE;UNTIL=20250430T000000Z;FREQ=DAILY", "list_id": "highValueCustomerList"}, "children": [{"external_id": "rate_balancer"}]}, "rate_balancer": {"type": "balancer", "name": "Rate Limit Distribution", "x": 0, "y": 300, "data": {"rate_limit": 100, "rate_interval": "hour"}, "children": [{"external_id": "initial_email", "path": "", "data": {}}, {"external_id": "trigger_event", "path": "", "data": {}}]}, "trigger_event": {"type": "event", "name": "Track VIP Entry", "x": 300, "y": 600, "data": {"event_name": "vip_journey_started", "template": "{\"customer_type\": \"high_value\", \"entry_date\": \"{{now}}\"}"}, "children": [{"external_id": "link_to_survey"}]}, "link_to_survey": {"type": "link", "name": "Link to Survey Journey", "x": 300, "y": 900, "data": {"target_id": "vipSurveyJourney", "delay": "1 hour"}, "children": [{"external_id": "initial_email"}]}, "initial_email": {"type": "action", "name": "Send Initial Loyalty Email", "x": -300, "y": 600, "data": {"campaign_ref": "vipLoyaltyCampaign", "data_key": "initial_email_data"}, "children": [{"external_id": "wait_3_days"}]}, "wait_3_days": {"type": "delay", "name": "3 Day Wait", "x": 0, "y": 600, "data": {"days": 3, "format": "duration", "duration": 259200}, "children": [{"external_id": "email_opened_gate"}]}, "email_opened_gate": {"type": "gate", "name": "Email Opened?", "x": 0, "y": 900, "data": {"rule": {"path": "$", "type": "wrapper", "uuid": "email-opened-gate-uuid", "group": "parent", "operator": "and", "children": [{"path": "$.email_opened", "type": "boolean", "uuid": "email-opened-condition-uuid", "group": "event", "operator": "=", "value": "true", "root_uuid": "email-opened-gate-uuid", "parent_uuid": "email-opened-gate-uuid"}]}}, "children": [{"external_id": "clicked_gate", "path": "yes", "data": {}}, {"external_id": "reminder_experiment", "path": "no", "data": {}}]}, "clicked_gate": {"type": "gate", "name": "<PERSON> Clicked?", "x": -500, "y": 1200, "data": {"rule": {"path": "$", "type": "wrapper", "uuid": "link-clicked-gate-uuid", "group": "parent", "operator": "and", "children": [{"path": "$.link_clicked", "type": "boolean", "uuid": "link-clicked-condition-uuid", "group": "event", "operator": "=", "value": "true", "root_uuid": "link-clicked-gate-uuid", "parent_uuid": "link-clicked-gate-uuid"}]}}, "children": [{"external_id": "wait_for_conversion", "path": "yes", "data": {}}, {"external_id": "wait_2_days", "path": "no", "data": {}}]}, "wait_for_conversion": {"type": "delay", "name": "Wait 5 Days for Conversion", "x": -750, "y": 1500, "data": {"days": 5, "format": "duration", "duration": 432000}, "children": [{"external_id": "conversion_gate"}]}, "conversion_gate": {"type": "gate", "name": "Converted?", "x": -750, "y": 1800, "data": {"rule": {"path": "$", "type": "wrapper", "uuid": "conversion-gate-uuid", "group": "parent", "operator": "and", "children": [{"path": "$.purchase", "type": "boolean", "uuid": "purchase-condition-uuid", "group": "event", "operator": "=", "value": "true", "root_uuid": "conversion-gate-uuid", "parent_uuid": "conversion-gate-uuid"}]}}, "children": [{"external_id": "send_thank_you", "path": "yes", "data": {}}, {"external_id": "exit_step", "path": "no", "data": {}}]}, "send_thank_you": {"type": "action", "name": "Send Thank You Email", "x": -750, "y": 2100, "data": {"campaign_ref": "vipSpecialOfferCampaign", "data_key": "thank_you_data"}, "children": [{"external_id": "exit_step"}]}, "wait_2_days": {"type": "delay", "name": "2 Day Wait", "x": -250, "y": 1500, "data": {"days": 2, "format": "duration", "duration": 172800}, "children": [{"external_id": "send_reminder"}]}, "send_reminder": {"type": "action", "name": "Send <PERSON><PERSON>", "x": -250, "y": 1800, "data": {"campaign_ref": "vipReminderCampaign", "data_key": "reminder_data"}, "children": [{"external_id": "wait_for_conversion2"}]}, "wait_for_conversion2": {"type": "delay", "name": "Wait 5 Days for Conversion", "x": -250, "y": 2100, "data": {"days": 5, "format": "duration", "duration": 432000}, "children": [{"external_id": "conversion_gate2"}]}, "conversion_gate2": {"type": "gate", "name": "Converted?", "x": -250, "y": 2400, "data": {"rule": {"path": "$", "type": "wrapper", "uuid": "conversion-gate2-uuid", "group": "parent", "operator": "and", "children": [{"path": "$.purchase", "type": "boolean", "uuid": "purchase-condition2-uuid", "group": "event", "operator": "=", "value": "true", "root_uuid": "conversion-gate2-uuid", "parent_uuid": "conversion-gate2-uuid"}]}}, "children": [{"external_id": "send_thank_you", "path": "yes", "data": {}}, {"external_id": "exit_step", "path": "no", "data": {}}]}, "reminder_experiment": {"type": "experiment", "name": "Reminder Channel Test", "x": 500, "y": 1200, "data": {}, "children": [{"external_id": "email_reminder", "path": "email", "data": {"ratio": 50}}, {"external_id": "sms_reminder", "path": "sms", "data": {"ratio": 50}}]}, "email_reminder": {"type": "action", "name": "Send <PERSON><PERSON>", "x": 250, "y": 1500, "data": {"campaign_ref": "vipReminderCampaign", "data_key": "email_reminder_data"}, "children": [{"external_id": "wait_3_days_after_reminder"}]}, "sms_reminder": {"type": "action", "name": "Send SMS Reminder", "x": 750, "y": 1500, "data": {"campaign_ref": "smsReminderCampaign", "data_key": "sms_reminder_data"}, "children": [{"external_id": "wait_3_days_after_reminder"}]}, "wait_3_days_after_reminder": {"type": "delay", "name": "Wait 3 Days After Reminder", "x": 500, "y": 1800, "data": {"days": 3, "format": "duration", "duration": 259200}, "children": [{"external_id": "final_response_gate"}]}, "final_response_gate": {"type": "gate", "name": "Any Response?", "x": 500, "y": 2100, "data": {"rule": {"path": "$", "type": "wrapper", "uuid": "final-response-gate-uuid", "group": "parent", "operator": "or", "children": [{"path": "$.email_opened", "type": "boolean", "uuid": "email-response-condition-uuid", "group": "event", "operator": "=", "value": "true", "root_uuid": "final-response-gate-uuid", "parent_uuid": "final-response-gate-uuid"}, {"path": "$.sms_clicked", "type": "boolean", "uuid": "sms-response-condition-uuid", "group": "event", "operator": "=", "value": "true", "root_uuid": "final-response-gate-uuid", "parent_uuid": "final-response-gate-uuid"}]}}, "children": [{"external_id": "send_special_offer", "path": "yes", "data": {}}, {"external_id": "mark_inactive", "path": "no", "data": {}}]}, "send_special_offer": {"type": "action", "name": "Send Special Offer", "x": 250, "y": 2400, "data": {"campaign_ref": "vipSpecialOfferCampaign", "data_key": "special_offer_data"}, "children": [{"external_id": "exit_step"}]}, "mark_inactive": {"type": "update", "name": "<PERSON> as Inactive VIP", "x": 750, "y": 2400, "data": {"template": "{\"activeVIP\":false}"}, "children": [{"external_id": "send_reactivation"}]}, "send_reactivation": {"type": "action", "name": "Send Reactivation Campaign", "x": 750, "y": 2700, "data": {"campaign_ref": "reactivationCampaign", "data_key": "reactivation_data"}, "children": [{"external_id": "exit_step"}]}, "exit_step": {"type": "exit", "name": "Journey Complete", "x": 0, "y": 3000, "data": {"entrance_uuid": "entrance_step"}, "children": []}}}}}, "vipSurveyJourney": {"type": "journey", "config": {"journey": {"name": "VIP Customer Survey Journey", "description": "Simple survey journey for VIP customers", "published": true, "tags": ["VIP", "Survey"], "steps": {"survey_entrance": {"type": "entrance", "name": "Survey Start", "x": 0, "y": 0, "data": {"trigger": "event", "event_name": "vip_journey_started", "multiple_entries": false, "simultaneous_entries": false}, "children": [{"external_id": "survey_email"}]}, "survey_email": {"type": "action", "name": "Send Survey Email", "x": 0, "y": 300, "data": {"campaign_ref": "vipSurveyCampaign", "data_key": "survey_data"}, "children": [{"external_id": "survey_exit"}]}, "survey_exit": {"type": "exit", "name": "Survey Complete", "x": 0, "y": 600, "data": {"entrance_uuid": "survey_entrance"}, "children": []}}}}}, "vipSurveyCampaign": {"type": "campaign", "config": {"campaign": {"type": "blast", "name": "VIP Customer Survey", "channel": "email", "subscription_id": 73, "provider_id": 38, "list_refs": ["highValueCustomerList"], "send_in_user_timezone": true, "tags": ["VIP", "Survey"]}}}, "vipSurveyTemplate": {"type": "template", "config": {"template": {"type": "email", "name": "VIP Customer Survey Email", "locale": "en", "campaign_ref": "vipSurveyCampaign", "data": {"editor": "code", "name": "VIP Survey Template", "subject": "We Value Your Opinion - VIP Survey", "html": "<html><body><h1>Your Opinion Matters</h1><p>As a valued VIP customer, we'd love to hear your thoughts about your experience with us.</p><p><a href='https://example.com/survey'>Take Survey</a></p></body></html>", "text": "Your Opinion Matters - As a valued VIP customer, we'd love to hear your thoughts about your experience with us. Take survey: https://example.com/survey", "from": {"name": "Ultrazone Laser Tag", "email": "<EMAIL>", "address": "<EMAIL>"}, "reply_to": "<EMAIL>"}}}}}}, "success": true}]}