{"version": "1.0", "services": {"automation_service": {"base_prompt": "You are a marketing automation expert creating step-by-step automation plans and journeys. Your plans will be executed by our system in sequential order. For journeys, follow these specific rules:\n\n1. Journey Steps Must Follow These Rules:\n   - Entrance node must be at (x: 0, y: 0)\n   - Each step must have unique coordinates (x, y)\n   - Vertical spacing: ~150-200 pixels between levels\n   - Horizontal spacing: ~200-300 pixels between parallel paths\n   - All paths must eventually connect to an exit node\n\n2. Step Types and Requirements:\n   - entrance: Requires trigger type (schedule/event/api) and related config\n   - gate: Requires valid rule configuration\n   - experiment: Requires variants with ratios that sum to 100\n   - balancer: Used for rate limiting between paths\n   - action: Requires campaign_id or template_id\n   - delay: Requires duration and unit or until_time\n   - exit: Must reference entrance_uuid\n\n3. Step Connections:\n   - Each step must have children array with external_id references\n   - Gates must have 'yes'/'no' paths\n   - Experiments must have ratio data for each path\n   - All paths must eventually merge to a single exit\n\n4. Visual Layout:\n   - Left paths: Use negative x values\n   - Right paths: Use positive x values\n   - Increase y values as you go down\n   - Maintain proper spacing between parallel paths\n\nReturn the journey configuration as a JSON object with steps dictionary containing properly configured and positioned nodes.", "constraints": ["Return a single JSON object containing an items dictionary directly at the root level", "Do not nest the response under an automationPlan key", "Do not include any comments in the JSON output", "Use only valid JSON syntax", "Never use undefined values - use empty string \"\" instead of null for optional strings", "For timestamps, use ISO date strings (e.g. \"2025-01-02T00:00:00Z\")", "Do not include any markdown code block delimiters in the JSON output", "Lists must be referenced using either list_refs (array of keys) or list_ids (array of numbers), never both", "Templates must reference campaigns in ONE of these two ways (never both):", "  1. For new campaigns created in your plan: campaign_ref: \"campaignKey\" where campaignK<PERSON> is the key of the campaign in the items dictionary", "  2. For existing campaigns in the database: campaign_id: number", "Campaign and template channel types must match (email/text/push)", "Items must be created in sequential order: lists -> campaigns -> templates", "Template items must reference campaign items that come BEFORE them in the dictionary", "All required fields must be present", "No null values - use empty string \"\" instead", "Email templates must have from.name, from.address, and reply_to", "Text templates must have text content", "Push templates must have title and body", "Journey entrance nodes must be at coordinates (0, 0)", "Journey steps must have unique x,y coordinates", "Maintain ~150-200 pixel vertical spacing between journey levels", "Maintain ~200-300 pixel horizontal spacing between parallel paths", "Gate steps must have 'yes' and 'no' paths", "Experiment step variants must have ratios that sum to 100", "All journey paths must eventually connect to an exit node", "Journey step coordinates must use negative x for left paths, positive x for right paths", "Journey y coordinates must increase as paths flow downward", "Each journey step must have proper children array with external_id references", "Exit nodes must reference their entrance_uuid", "Action steps must have valid campaign_id or template_id", "Delay steps must specify duration+unit or until_time", "Entrance steps must have proper trigger configuration (schedule/event/api)"], "training_examples": [{"input": {"type": "automation", "description": "Create a welcome campaign with multiple templates"}, "output": {"name": "Multi-Template Welcome Campaign", "description": "Welcome email series for new customers", "items": {"newCustomersList": {"type": "list", "config": {"list": {"name": "New Customers List", "type": "dynamic", "description": "Automatically updated list of new customers", "rule": {"uuid": "123e4567-e89b-12d3-a456-************", "type": "date", "group": "user", "path": "created_at", "operator": ">=", "value": "2024-01-01", "children": []}, "is_visible": true, "tags": ["New Customers"], "user_ids": []}}}, "welcomeCampaign": {"type": "campaign", "config": {"campaign": {"type": "blast", "name": "Welcome Series Campaign", "channel": "email", "subscription_id": 1, "provider_id": 1, "list_refs": ["newCustomersList"], "send_in_user_timezone": true, "send_at": "", "tags": ["Welcome", "Series"]}}}, "welcomeTemplate": {"type": "template", "config": {"template": {"type": "email", "name": "Welcome Email Template", "locale": "en", "campaign_ref": "welcomeCampaign", "data": {"editor": "code", "name": "Welcome Email", "subject": "Welcome to Our Community!", "html": "<h1>Welcome</h1>", "text": "Welcome to our community!", "from": {"name": "Company", "address": "<EMAIL>"}, "reply_to": "<EMAIL>", "preheader": "Welcome to our community!"}}}}}}, "success": true}, {"input": {"type": "journey", "description": "Create a journey with proper step positioning", "payload": {"name": "Multi-Channel Christmas Special", "description": "Holiday campaign with email and SMS paths based on user preferences", "published": true}}, "output": {"name": "Multi-Channel Christmas Special", "description": "Holiday campaign with email and SMS paths based on user preferences", "published": true, "steps": {"entrance_node": {"type": "entrance", "name": "Xmas Special", "x": 0, "y": 0, "data": {"trigger": "schedule", "schedule": "DTSTART:20251225T000000Z", "multiple": false, "list_id": 4}, "children": [{"external_id": "email_gate", "path": "", "data": {}}]}, "email_gate": {"type": "gate", "name": "Has email", "x": 0, "y": 200, "data": {"rule": {"path": "$", "type": "wrapper", "uuid": "80e21ae0-4cd7-4c65-a3ba-e39d8b110e80", "group": "parent", "operator": "and", "children": [{"path": "$.email", "type": "string", "uuid": "64b0cfb3-f352-47c0-9f5b-284b92bc9336", "group": "user", "operator": "is set", "value": "", "root_uuid": "80e21ae0-4cd7-4c65-a3ba-e39d8b110e80", "parent_uuid": "80e21ae0-4cd7-4c65-a3ba-e39d8b110e80"}]}}, "children": [{"external_id": "email_balancer", "path": "yes", "data": {}}, {"external_id": "sms_experiment", "path": "no", "data": {}}]}, "email_balancer": {"type": "balancer", "name": "Email Rate Limit", "x": -300, "y": 400, "data": {"rate_limit": 1000, "rate_interval": "minute"}, "children": [{"external_id": "email_50_off", "path": "", "data": {}}]}, "sms_experiment": {"type": "experiment", "name": "SMS Test", "x": 300, "y": 400, "data": {}, "children": [{"external_id": "sms_50_off", "path": "", "data": {"ratio": 65}}, {"external_id": "sms_20_off", "path": "", "data": {"ratio": 35}}]}, "email_50_off": {"type": "action", "name": "50% off email", "x": -300, "y": 600, "data": {"campaign_id": 7}, "children": [{"external_id": "exit_node", "path": "", "data": {}}]}, "sms_50_off": {"type": "action", "name": "50% sms", "x": 150, "y": 600, "data": {"campaign_id": 8}, "children": [{"external_id": "exit_node", "path": "", "data": {}}]}, "sms_20_off": {"type": "action", "name": "20% sms", "x": 450, "y": 600, "data": {"campaign_id": 9}, "children": [{"external_id": "exit_node", "path": "", "data": {}}]}, "exit_node": {"type": "exit", "name": "Exit", "x": 0, "y": 800, "data": {"entrance_uuid": "entrance_node"}, "children": []}}}, "success": true}], "error_patterns": [{"pattern": "Invalid list reference", "fix": "Lists must be referenced using either list_refs (array of keys from items dictionary) or list_ids (array of numbers), never both", "count": 1}, {"pattern": "Invalid campaign reference", "fix": "Template must use either campaign_ref (campaign_ref: \"campaignKey\" where campaignKey is the key of the campaign in the items dictionary) or campaign_id (a number for existing campaigns). NO other formats allowed.", "count": 1}, {"pattern": "Missing step reference", "fix": "When referencing a campaign step, use campaign_ref: \"campaignKey\" where campaignKey is the key of the campaign in the items dictionary", "count": 1}, {"pattern": "Invalid step index", "fix": "Referenced campaign key must exist in the items dictionary and must be defined before the template that references it", "count": 1}, {"pattern": "String campaign ID", "fix": "campaign_id must be a number, never a string or name. For step references, use campaign_ref instead.", "count": 1}, {"pattern": "Both ref and ID", "fix": "Never use both list_refs and list_ids or both campaign_ref and campaign_id. Choose one approach.", "count": 1}, {"pattern": "Invalid journey coordinates", "fix": "Entrance must be at (0,0), maintain ~150-200px vertical spacing between levels, and ~200-300px horizontal spacing between parallel paths", "count": 1}, {"pattern": "Missing path reference", "fix": "Gate steps must specify 'yes'/'no' paths, experiment steps must specify ratios in path data", "count": 1}, {"pattern": "Invalid step connection", "fix": "Each step must have children array with valid external_id references to other steps", "count": 1}, {"pattern": "Missing entrance reference", "fix": "Exit nodes must reference their entrance_uuid to properly close the journey", "count": 1}]}}}