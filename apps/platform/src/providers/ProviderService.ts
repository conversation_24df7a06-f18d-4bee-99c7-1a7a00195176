import Router from "@koa/router";
import { LocationState } from "../auth/AuthMiddleware";
import { PageParams } from "../core/searchParams";
import { JSONSchemaType, validate } from "../core/validate";
import Provider, {
  ProviderControllers,
  ProviderGroup,
  ProviderMeta,
  ProviderParams,
} from "./Provider";
import {
  createProvider,
  loadProvider,
  updateProvider,
} from "./ProviderRepository";
import App from "../app";

export const allProviders = async (locationId: number) => {
  return await Provider.all((qb) => qb.where("location_id", locationId));
};

export const allGlobalProviders = async () => {
  return await Provider.all((qb) => qb.whereNull("location_id"));
};

export const hasProvider = async (locationId: number) => {
  return await Provider.exists((qb) => qb.where("location_id", locationId));
};

export const pagedProviders = async (
  params: PageParams,
  locationId: number
) => {
  return await Provider.search(
    { ...params, fields: ["name", "group"] },
    (b) => b.where("location_id", locationId),
    App.main.db,
    (item) => {
      item.setup = item.loadSetup(App.main);
      return item;
    }
  );
};

export const pagedGlobalProviders = async (params: PageParams) => {
  return await Provider.search(
    { ...params, fields: ["name", "group"] },
    (b) => b.whereNull("location_id"),
    App.main.db,
    (item) => {
      item.setup = item.loadSetup(App.main);
      return item;
    }
  );
};

export const loadController = (
  routers: ProviderControllers,
  provider: typeof Provider
): ProviderMeta => {
  const { admin: adminRouter, public: publicRouter } = provider.controllers();
  if (routers.admin && adminRouter) {
    routers.admin.use(adminRouter.routes(), adminRouter.allowedMethods());
  }
  if (routers.public && publicRouter) {
    routers.public.use(publicRouter.routes(), publicRouter.allowedMethods());
  }
  return {
    ...provider.meta,
    group: provider.group,
    type: provider.namespace,
    schema: provider.schema,
  };
};

export const createController = (
  group: ProviderGroup,
  type: typeof Provider
): Router => {
  const router = new Router<LocationState & { provider?: Provider }>({
    prefix: `/${group}/${type.namespace}`,
  });

  router.post("/", async (ctx) => {
    const payload = validate(
      type.schema as JSONSchemaType<ProviderParams>,
      ctx.request.body
    );

    ctx.body = await createProvider(ctx.state.location.id, {
      ...payload,
      type: type.namespace,
      group,
    });
  });

  router.param("providerId", async (value, ctx, next) => {
    const map = (record: any) => {
      return type.fromJson(record);
    };
    ctx.state.provider = await loadProvider(
      parseInt(value, 10),
      map,
      ctx.state.location.id
    );
    if (!ctx.state.provider) {
      ctx.throw(404);
      return;
    }
    return await next();
  });

  router.get("/:providerId", async (ctx) => {
    ctx.body = ctx.state.provider;
  });

  router.patch("/:providerId", async (ctx) => {
    const payload = validate(
      type.schema as JSONSchemaType<ProviderParams>,
      ctx.request.body
    );
    ctx.body = updateProvider(ctx.state.provider!.id, payload);
  });

  return router;
};
