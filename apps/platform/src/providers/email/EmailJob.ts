import Job from "../../queue/Job";
import { MessageTrigger } from "../MessageTrigger";
import { updateSendState } from "../../campaigns/CampaignService";
import { loadEmailChannel } from "./index";
import {
  failSend,
  finalizeSend,
  loadSendJob,
  messageLock,
  prepareSend,
} from "../MessageTriggerService";
import { EmailTemplate } from "../../render/Template";
import { EncodedJob } from "../../queue";
import App from "../../app";
import { releaseLock } from "../../core/Lock";

export default class EmailJob extends Job {
  static $name = "email";

  static from(data: MessageTrigger): EmailJob {
    return new this(data);
  }

  static async handler(trigger: MessageTrigger, raw: EncodedJob) {
    console.log('🔍 EMAIL JOB HANDLER STARTED');
    console.log('Trigger:', JSON.stringify(trigger, null, 2));

    const data = await loadSendJob<EmailTemplate>(trigger);
    if (!data) {
      console.log('❌ EMAIL JOB: loadSendJob returned null/undefined');
      return;
    }
    console.log('✅ EMAIL JOB: loadSendJob successful');
    console.log('User email:', data.user.email);
    console.log('Campaign ID:', data.campaign.id);
    console.log('Template ID:', data.template.id);

    const { campaign, template, user, location } = data;

    // Load email channel so its ready to send
    console.log('🔍 EMAIL JOB: Loading email channel for provider', campaign.provider_id);
    const channel = await loadEmailChannel(campaign.provider_id, location.id);
    if (!channel) {
      console.log('❌ EMAIL JOB: No email channel available');
      await updateSendState({
        campaign,
        user,
        reference_id: trigger.reference_id,
        state: "aborted",
      });
      App.main.error.notify(
        new Error("Unabled to send when there is no channel available.")
      );
      return;
    }
    console.log('✅ EMAIL JOB: Email channel loaded successfully');

    // Check current send rate and if the send is locked
    console.log('🔍 EMAIL JOB: Checking prepareSend');
    const isReady = await prepareSend(channel, data, raw);
    if (!isReady) {
      console.log('❌ EMAIL JOB: prepareSend returned false (rate limited or locked)');
      return;
    }
    console.log('✅ EMAIL JOB: prepareSend successful');

    try {
      console.log('🔍 EMAIL JOB: Calling channel.send()');
      const result = await channel.send(template, data);
      console.log('✅ EMAIL JOB: channel.send() successful');
      console.log('Send result:', result);

      await finalizeSend(data, result);
      console.log('✅ EMAIL JOB: finalizeSend successful');
    } catch (error: any) {
      console.log('❌ EMAIL JOB: Error in channel.send()');
      console.log('Error:', error.message);
      console.log('Stack:', error.stack);
      await failSend(data, error);
    } finally {
      await releaseLock(messageLock(campaign, user));
      console.log('✅ EMAIL JOB: Lock released');
    }

    console.log('🎯 EMAIL JOB HANDLER COMPLETED');
  }
}
