import sgMail from "@sendgrid/mail";
import EmailProvider from "./EmailProvider";
import Router = require("@koa/router");
import Provider, {
  ExternalProviderParams,
  ProviderControllers,
  ProviderSchema,
  ProviderSetupMeta,
} from "../Provider";
import { createController } from "../ProviderService";
import { decodeHashid, encodeHashid } from "../../utilities";
import { getUserFromEmail } from "../../users/UserRepository";
import { getCampaign } from "../../campaigns/CampaignService";
import { trackMessageEvent } from "../../render/LinkService";
import App from "../../app";
import { Email } from "./Email";

interface SendGridDataParams {
  api_key: string;
}

type SendGridEmailProviderParams = Pick<
  SendGridEmailProvider,
  keyof ExternalProviderParams
>;

interface SendGridEvent {
  email: string;
  event: string;
  "X-Campaign-Id": string;
}

export default class SendGridEmailProvider extends EmailProvider {
  api_key!: string;

  static namespace = "sendgrid";
  static meta = {
    name: "SendGrid",
    url: "https://sendgrid.com",
    icon: "https://beta.bakedbot.ai/images/providers/sendgrid.svg",
    paths: {
      "Webhook URL": `/${this.namespace}`,
    },
  };

  static schema = ProviderSchema<
    SendGridEmailProviderParams,
    SendGridDataParams
  >("SendGridProviderParams", {
    type: "object",
    required: ["api_key"],
    properties: {
      api_key: {
        type: "string",
        title: "API Key",
      },
    },
    additionalProperties: false,
  });

  loadSetup(app: App): ProviderSetupMeta[] {
    return [
      {
        name: "Webhook URL",
        value: `${app.env.apiBaseUrl}/providers/${encodeHashid(this.id)}/${
          (this.constructor as any).namespace
        }`,
      },
    ];
  }

  boot() {
    sgMail.setApiKey(this.api_key);
  }

  async send(message: Email): Promise<any> {
    const transformedMessage = {
      ...message,
      from:
        typeof message.from === "string" ? message.from : message.from.address, // SendGrid SDK expects string format
      custom_args: message.headers,
      unique_args: message.headers,
      // Transform attachments to ensure content is string (base64) for SendGrid
      attachments: message.attachments?.map((attachment) => ({
        ...attachment,
        content: Buffer.isBuffer(attachment.content)
          ? attachment.content.toString("base64")
          : attachment.content,
      })),
    };

    console.log("=== SENDGRID PROVIDER DEBUG ===");
    console.log("Original message:", JSON.stringify(message, null, 2));
    console.log(
      "Transformed message:",
      JSON.stringify(transformedMessage, null, 2)
    );
    console.log(
      "API Key:",
      this.api_key
        ? `${this.api_key.slice(0, 4)}...${this.api_key.slice(-4)}`
        : "undefined"
    );

    try {
      // Use direct SendGrid SDK instead of nodemailer
      const result = await sgMail.send(transformedMessage);
      console.log(
        "✅ SendGrid send SUCCESS:",
        result[0]?.statusCode,
        result[0]?.headers?.["x-message-id"]
      );
      return result;
    } catch (error: any) {
      console.error("❌ SendGrid send FAILED:", error.message);
      if (error.response) {
        console.error("Error response:", error.response.body);
      }
      throw error;
    }
  }

  static controllers(): ProviderControllers {
    const admin = createController("email", this);

    const router = new Router<{ provider: Provider }>();
    router.post(`/${this.namespace}`, async (ctx) => {
      ctx.status = 204;

      const provider = ctx.state.provider;
      const events = ctx.request.body as SendGridEvent[];
      for (const event of events) {
        if (!["dropped", "bounce", "spamreport"].includes(event.event)) {
          continue;
        }

        const type =
          event.event === "dropped" || event.event === "bounce"
            ? "bounced"
            : "complained";

        // Get values from webhook to identify user and campaign
        const campaignId = decodeHashid(event["X-Campaign-Id"]);
        if (!event.email || !campaignId) return;

        const locationId = provider.location_id;
        const user = await getUserFromEmail(locationId, event.email);
        const campaign = await getCampaign(campaignId, locationId);
        if (!user || !campaign) return;

        // Create an event and process the unsubscribe
        await trackMessageEvent({ user, campaign }, type, "unsubscribe");
      }
    });

    return { admin, public: router };
  }

  async verify(): Promise<boolean> {
    try {
      // Test with a simple API call to verify the key works
      const testMessage = {
        to: "<EMAIL>",
        from: "<EMAIL>",
        subject: "Test",
        text: "Test",
      };

      // We don't actually send this, just validate the API key format
      if (!this.api_key || !this.api_key.startsWith("SG.")) {
        return false;
      }

      return true;
    } catch (error: any) {
      console.error("SendGrid verification failed:", {
        error: error.message,
        apiKey: this.api_key,
        maskedKey: this.api_key
          ? `${this.api_key.slice(0, 4)}...${this.api_key.slice(-4)}`
          : "undefined",
      });
      return false;
    }
  }
}
