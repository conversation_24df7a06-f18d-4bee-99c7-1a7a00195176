import axios from "axios";
import PosProvider, { PosProduct, PosTicket } from "./PosProvider";
import { ProviderSchema, ExternalProviderParams } from "../Provider";

interface CovaDataParams {
  company_id: number;
  location_id: number;
  bearer_token: string;
}

type CovaProviderParams = Pick<CovaProvider, keyof ExternalProviderParams>;

export default class CovaProvider extends PosProvider {
  company_id!: number;
  cova_location_id!: number;
  bearer_token!: string;
  private api!: ReturnType<typeof axios.create>;

  static namespace = "cova";
  static meta = {
    name: "COVA",
    description: "Integration with COVA POS system",
    url: "https://covasoft.net",
  };

  static schema = ProviderSchema<CovaProviderParams, CovaDataParams>(
    "CovaProviderParams",
    {
      type: "object",
      required: ["company_id", "location_id", "bearer_token"],
      properties: {
        company_id: {
          type: "number",
          title: "Company ID",
        },
        location_id: {
          type: "number",
          title: "Location ID",
        },
        bearer_token: {
          type: "string",
          title: "Bearer Token",
        },
      },
      additionalProperties: false,
    }
  );

  parseJson(json: any): void {
    super.parseJson(json);
    // Map location_id from data to cova_location_id
    this.cova_location_id = json.data.location_id;
  }

  boot() {
    this.api = axios.create({
      baseURL: "https://api.covasoft.net",
      headers: {
        Authorization: `Bearer ${this.bearer_token}`,
        Accept: "application/json",
        "Content-Type": "application/json",
      },
    });
  }

  async verify(): Promise<boolean> {
    try {
      await this.api.get(
        `/pointofsale/Companies(${this.company_id})/Locations(${this.cova_location_id})`
      );
      return true;
    } catch (error) {
      return false;
    }
  }

  async fetchTickets(startDate: Date, endDate: Date): Promise<PosTicket[]> {
    const response = await this.api.get(
      `/pointofsale/Companies(${this.company_id})/Locations(${this.cova_location_id})/CompletedOrders`,
      {
        params: {
          $filter: `Created ge datetimeoffset'${startDate.toISOString()}' and Created le datetimeoffset'${endDate.toISOString()}'`,
          $top: 100,
          $skip: 0,
        },
      }
    );

    return response.data.value.map((order: any) => {
      // Calculate totals from payments
      const payments = order.Payments || [];
      const cashPayment =
        payments.find((p: any) => p.Type === "Cash")?.Amount || 0;
      const debitPayment =
        payments.find((p: any) => p.Type === "Debit")?.Amount || 0;
      const loyaltyPayment =
        payments.find((p: any) => p.Type === "Loyalty")?.Amount || 0;

      // Calculate discounts
      const discounts = order.Discounts || [];
      const totalDiscounts = discounts.reduce(
        (sum: number, d: any) => sum + d.Amount,
        0
      );
      const loyaltyDiscount =
        discounts.find((d: any) => d.Type === "Loyalty")?.Amount || 0;

      // Calculate tax
      const totalTax =
        order.Taxes?.reduce((sum: number, t: any) => sum + t.Amount, 0) || 0;

      return {
        id: order.Id.toString(),
        date: new Date(order.Created),
        total: order.Total,
        customer_name: order.Customer?.Name,
        customer_id: order.Customer?.Id?.toString(),
        customer_type: order.Customer?.Type || "Unknown",
        customer_birth_date: order.Customer?.BirthDate
          ? new Date(order.Customer.BirthDate)
          : undefined,
        budtender_name: order.Employee?.Name || "Unknown",
        location_name: order.Location?.Name || "",
        returned_amount:
          order.Returns?.reduce((sum: number, r: any) => sum + r.Amount, 0) ||
          0,
        discounted_amount: totalDiscounts - loyaltyDiscount, // Regular discounts
        loyalty_as_discount: loyaltyDiscount,
        loyalty_as_payment: loyaltyPayment,
        tax_amount: totalTax,
        amount_paid_in_cash: cashPayment,
        amount_paid_in_debit: debitPayment,
        items: (order.Items || []).map((item: any) => ({
          id: item.Id.toString(),
          name: item.Name,
          quantity: item.Quantity,
          price: item.Price,
          category: item.Category,
          subcategory: item.Subcategory,
          cost: item.Cost,
        })),
      };
    });
  }

  async fetchProducts(): Promise<PosProduct[]> {
    const response = await this.api.post(
      `/dataplatform/v1/Companies/${this.company_id}/DetailedProductData`,
      {
        LocationId: this.cova_location_id,
        IncludeProductSkusAndUpcs: true,
        IncludeProductSpecifications: true,
        IncludeClassifications: true,
        IncludeProductAssets: true,
        IncludeAvailability: true,
        IncludePackageDetails: true,
        IncludePricing: true,
        IncludeTaxes: true,
        InStockOnly: true,
        IncludeAllLifecycles: false,
        SellingRoomOnly: true,
        Skip: 0,
        Top: 100,
      }
    );

    return response.data.map((product: any) => ({
      id: product.Id.toString(),
      name: product.Name,
      description: product.Description,
      category: product.Category,
      subcategory: product.Subcategory,
      price: product.Price,
      inventory_quantity: product.QuantityAvailable,
      sku: product.SKU,
      barcode: product.UPC,
      brand: product.Brand,
      vendor: product.Vendor,
      is_active: product.IsActive,
    }));
  }
}
