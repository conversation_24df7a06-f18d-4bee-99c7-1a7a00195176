import PosProvider, { PosProduct, PosTicket } from "./PosProvider";
import { ProviderSchema, ExternalProviderParams } from "../Provider";
import { FileStream } from "../../storage/FileStream";
import { DataNormalizationService } from "../../core/DataNormalizationService";
import { RequestError } from "../../core/errors";
import { PosData } from "../../pos/PosData";

interface CsvPosDataParams {
  // We'll handle the file stream separately from the schema
  name?: string;
}

type CsvPosProviderParams = Pick<CsvPosProvider, keyof ExternalProviderParams>;

export default class CsvPosProvider extends PosProvider {
  file_stream?: FileStream;

  static namespace = "csv";
  static meta = {
    name: "CSV Upload",
    description: "Import POS data from CSV files",
  };

  static schema = ProviderSchema<CsvPosProviderParams, CsvPosDataParams>(
    "CsvPosProviderParams",
    {
      type: "object",
      properties: {
        name: {
          type: "string",
          nullable: true,
        },
      },
      additionalProperties: false,
    }
  );

  boot() {
    // No boot needed for CSV provider
  }

  async verify(): Promise<boolean> {
    return true; // CSV provider is always valid
  }

  setFileStream(stream: FileStream) {
    this.file_stream = stream;
  }

  async processFileStream(stream: FileStream) {
    const normalizedData = await DataNormalizationService.normalizeData(stream);

    // Verify this is POS data
    if (normalizedData.type !== "pos") {
      throw new RequestError(
        "The uploaded file appears to be customer data. Please use the customer data import endpoint."
      );
    }

    // Report any errors found during normalization
    if (normalizedData.errors.length > 0) {
      const errorMessages = normalizedData.errors.map(
        (err) => `Row ${err.row}: ${err.error}`
      );
      throw new RequestError(
        `Data validation errors:\n${errorMessages.join("\n")}`
      );
    }

    return normalizedData.data as Array<Partial<PosData>>;
  }

  async fetchTickets(startDate: Date, endDate: Date): Promise<PosTicket[]> {
    if (!this.file_stream) {
      return [];
    }

    const data = await this.processFileStream(this.file_stream);

    return data.map((row) => ({
      id: `${row.order_date?.toISOString()}_${row.customer_name}`,
      date: row.order_date || new Date(),
      total: row.invoice_total || 0,
      customer_name: row.customer_name,
      items: [
        {
          id: "1",
          name: row.product_name || "",
          quantity: 1, // CSV doesn't have quantity, assume 1
          price: row.gross_sales || 0,
          category: row.master_category,
        },
      ],
    }));
  }

  async fetchProducts(): Promise<PosProduct[]> {
    if (!this.file_stream) {
      return [];
    }

    const data = await this.processFileStream(this.file_stream);

    // Extract unique products from the CSV data
    const uniqueProducts = new Map<string, PosProduct>();

    data.forEach((row) => {
      const productName = row.product_name;
      if (productName && !uniqueProducts.has(productName)) {
        uniqueProducts.set(productName, {
          id: productName,
          name: productName,
          category: row.master_category,
          price: row.gross_sales || 0,
          is_active: true,
        });
      }
    });

    return Array.from(uniqueProducts.values());
  }
}
