import axios from "axios";
import PosProvider, { PosProduct, PosTicket } from "./PosProvider";
import { ProviderSchema, ExternalProviderParams } from "../Provider";

interface MarijuanaSoftwareDataParams {
  webguid: string;
  subscription_key: string;
}

type MarijuanaSoftwareProviderParams = Pick<
  MarijuanaSoftwareProvider,
  keyof ExternalProviderParams
>;

export default class MarijuanaSoftwareProvider extends PosProvider {
  webguid!: string;
  subscription_key!: string;
  private api!: ReturnType<typeof axios.create>;

  static namespace = "marijuana_software";
  static meta = {
    name: "Marijuana Software",
    description: "Integration with Marijuana Software POS system",
    url: "https://marijuanasoftwarellc.com",
  };

  static schema = ProviderSchema<
    MarijuanaSoftwareProviderParams,
    MarijuanaSoftwareDataParams
  >("MarijuanaSoftwareProviderParams", {
    type: "object",
    required: ["webguid", "subscription_key"],
    properties: {
      webguid: {
        type: "string",
        title: "Web GUID",
        description: "Your Marijuana Software account ID",
      },
      subscription_key: {
        type: "string",
        title: "Subscription Key",
        description: "Your Marijuana Software API key",
      },
    },
    additionalProperties: false,
  });

  parseJson(json: any): void {
    super.parseJson(json);
    this.boot();
  }

  boot() {
    this.api = axios.create({
      baseURL: "https://api.marijuanasoftwarellc.com/v1",
      headers: {
        "Cache-Control": "no-cache",
        "Ocp-Apim-Subscription-Key": this.subscription_key,
      },
    });
  }

  async fetchTickets(startDate: Date, endDate: Date): Promise<PosTicket[]> {
    const response = await this.api.get("/Tickets", {
      params: {
        webguid: this.webguid,
        startDate: startDate.toLocaleDateString("en-US"),
        endDate: endDate.toLocaleDateString("en-US"),
      },
    });

    return response.data.map((ticket: any) => ({
      id: ticket.id.toString(),
      date: new Date(ticket.date),
      total: ticket.total,
      customer_name: ticket.customer?.name,
      customer_id: ticket.customer?.id?.toString(),
      customer_type: "Unknown",
      budtender_name: ticket.employee?.name || "Unknown",
      location_name: ticket.location?.name || "",
      returned_amount: 0,
      discounted_amount:
        ticket.discounts?.reduce((sum: number, d: any) => sum + d.amount, 0) ||
        0,
      loyalty_as_discount:
        ticket.discounts?.find((d: any) => d.type === "loyalty")?.amount || 0,
      loyalty_as_payment:
        ticket.payments?.find((p: any) => p.type === "loyalty")?.amount || 0,
      tax_amount: ticket.tax || 0,
      amount_paid_in_cash:
        ticket.payments?.find((p: any) => p.type === "cash")?.amount || 0,
      amount_paid_in_debit:
        ticket.payments?.find((p: any) => p.type === "debit")?.amount || 0,
      items: ticket.items.map((item: any) => ({
        id: item.id.toString(),
        name: item.name,
        quantity: item.quantity,
        price: item.price,
        category: item.category,
        subcategory: item.subcategory,
        cost: item.cost || 0,
      })),
    }));
  }

  async fetchProducts(): Promise<PosProduct[]> {
    const response = await this.api.get("/Products", {
      params: {
        webguid: this.webguid,
      },
    });

    return response.data.map((product: any) => ({
      id: product.id.toString(),
      name: product.name,
      description: product.description,
      category: product.category,
      subcategory: product.subcategory,
      price: product.price,
      inventory_quantity: product.inventory_quantity,
      sku: product.sku,
      barcode: product.barcode,
      brand: product.brand,
      vendor: product.vendor,
      is_active: product.is_active ?? true,
    }));
  }

  async verify(): Promise<boolean> {
    try {
      await this.api.get("/Tickets", {
        params: {
          webguid: this.webguid,
          startDate: new Date().toLocaleDateString("en-US"),
          endDate: new Date().toLocaleDateString("en-US"),
        },
      });
      return true;
    } catch (error) {
      return false;
    }
  }
}
