import PosProvider from "./PosProvider";
import { PosData } from "../../pos/PosData";
import { User } from "../../users/User";
import UserPatchJob from "../../users/UserPatchJob";
import PosDataVectorJob from "../../pos/PosDataVectorJob";
import App from "../../app";

export default class PosChannel {
  readonly provider: PosProvider;

  constructor(provider?: PosProvider) {
    if (provider) {
      this.provider = provider;
      this.provider.boot?.();
    } else {
      throw new Error("A valid POS provider must be defined!");
    }
  }

  async syncTickets(startDate: Date, endDate: Date) {
    const tickets = await this.provider.fetchTickets(startDate, endDate);
    const results = {
      processed: 0,
      errors: [] as Array<{ error: string; data?: any }>,
    };

    for (const ticket of tickets) {
      try {
        // Convert ticket to POS data format
        const posData = this.provider.normalizeTicketToPosData(ticket);

        // Insert POS data
        await PosData.insert(posData);
        results.processed++;

        // Process customer data if available
        if (ticket.customer_name) {
          const userParams = {
            external_id: ticket.customer_id || ticket.customer_name,
            data: {
              full_name: ticket.customer_name,
            },
          };

          await App.main.queue.enqueue(
            UserPatchJob.from({
              location_id: this.provider.location_id,
              user: userParams,
            })
          );
        }
      } catch (error: any) {
        results.errors.push({
          error: error.message,
          data: ticket,
        });
      }
    }

    // Start vector processing job
    if (results.processed > 0) {
      await PosDataVectorJob.from({
        location_id: this.provider.location_id,
        batch_size: 100,
      }).queue();
    }

    return results;
  }

  async syncProducts() {
    const products = await this.provider.fetchProducts();
    return {
      processed: products.length,
      products,
    };
  }

  async verify(): Promise<boolean> {
    return await this.provider.verify();
  }
}
