import Provider from "../Provider";
import { PosData } from "../../pos/PosData";

export interface PosTicket {
  id: string;
  date: Date;
  total: number;
  customer_name?: string;
  customer_id?: string;
  customer_type?: string;
  customer_birth_date?: Date;
  budtender_name?: string;
  location_name?: string;
  returned_amount?: number;
  discounted_amount?: number;
  loyalty_as_discount?: number;
  loyalty_as_payment?: number;
  tax_amount?: number;
  amount_paid_in_cash?: number;
  amount_paid_in_debit?: number;
  inventory_cost?: number;
  inventory_profit?: number;
  items: PosTicketItem[];
}

export interface PosTicketItem {
  id: string;
  name: string;
  quantity: number;
  price: number;
  category?: string;
  subcategory?: string;
  cost?: number;
}

export interface PosProduct {
  id: string;
  name: string;
  description?: string;
  category?: string;
  subcategory?: string;
  price: number;
  inventory_quantity?: number;
  sku?: string;
  barcode?: string;
  brand?: string;
  vendor?: string;
  is_active: boolean;
}

export default abstract class PosProvider extends Provider {
  static group = "pos" as const;

  abstract fetchTickets(startDate: Date, endDate: Date): Promise<PosTicket[]>;
  abstract fetchProducts(): Promise<PosProduct[]>;

  // Optional method for providers that support real-time inventory updates
  async syncInventory?(): Promise<void>;

  // Make boot and verify methods required for all POS providers
  abstract boot(): void;
  abstract verify(): Promise<boolean>;

  // Make this public since it's used by PosChannel
  normalizeTicketToPosData(ticket: PosTicket): Partial<PosData> {
    const gross_sales = ticket.items.reduce(
      (sum, item) => sum + item.price * item.quantity,
      0
    );

    const inventory_cost = ticket.items.reduce(
      (sum, item) => sum + (item.cost || 0) * item.quantity,
      0
    );

    return {
      location_id: this.location_id,
      location_name: ticket.location_name || "",
      master_category: ticket.items[0]?.category || "",
      order_date: ticket.date,
      customer_type: ticket.customer_type || "",
      budtender_name: ticket.budtender_name || "",
      gross_sales,
      returned_amount: ticket.returned_amount || 0,
      discounted_amount: ticket.discounted_amount || 0,
      loyalty_as_discount: ticket.loyalty_as_discount || 0,
      net_sales:
        gross_sales -
        (ticket.returned_amount || 0) -
        (ticket.discounted_amount || 0) -
        (ticket.loyalty_as_discount || 0),
      inventory_cost: ticket.inventory_cost || inventory_cost,
      inventory_profit: ticket.inventory_profit || gross_sales - inventory_cost,
      loyalty_as_payment: ticket.loyalty_as_payment || 0,
      tax_amount: ticket.tax_amount || 0,
      invoice_total: ticket.total,
      amount_paid_in_cash: ticket.amount_paid_in_cash || 0,
      amount_paid_in_debit: ticket.amount_paid_in_debit || 0,
      birth_date: ticket.customer_birth_date,
      customer_name: ticket.customer_name || "",
      product_name: ticket.items[0]?.name || "",
    };
  }
}
