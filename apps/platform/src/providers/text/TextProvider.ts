import Router from "@koa/router";
import { loadTextChannel } from ".";
import { unsubscribeSms } from "../../subscriptions/SubscriptionService";
import Provider, { ProviderGroup } from "../Provider";
import { InboundTextMessage, TextMessage, TextResponse } from "./TextMessage";
import { Context } from "koa";
import { getUserFromPhone } from "../../users/UserRepository";
import { getLocation } from "../../locations/LocationService";
import { EventPostJob } from "../../jobs";

export type TextProviderName = "nexmo" | "plivo" | "twilio" | "logger";

export abstract class TextProvider extends Provider {
  abstract send(message: TextMessage): Promise<TextResponse>;
  abstract parseInbound(inbound: any): InboundTextMessage;

  static get group() {
    return "text" as ProviderGroup;
  }

  static inbound(namespace: string) {
    const router = new Router<{ provider: Provider }>();

    const inbound = async (ctx: Context) => {
      const provider = ctx.state.provider;

      // Load in the required components to properly parse the message
      const channel = await loadTextChannel(provider.id);
      const location = await getLocation(provider.location_id);
      const message = channel?.provider.parseInbound(ctx.request.body);
      if (!channel || !location || !message) ctx.throw(404);

      // Find the user from the inbound text message
      const user = await getUserFromPhone(provider.location_id, message.from);
      if (!user) ctx.throw(404);

      // If we've made it this far, always respond with success so webhooks
      // don't double trigger
      ctx.status = 204;

      // If the message includes the word STOP unsubscribe immediately
      if (message.text.toLowerCase().includes("stop")) {
        await unsubscribeSms(location.id, user);

        // If the message includes the word HELP, send the help message
      } else if (
        message.text.toLowerCase().includes("help") &&
        location.text_help_message
      ) {
        channel.provider.send({
          to: message.from,
          text: location.text_help_message,
        });

        // Otherwise create an event so journeys can trigger off of the message
      } else {
        await EventPostJob.from({
          location_id: location.id,
          event: {
            name: "text_inbound",
            external_id: user.external_id,
            anonymous_id: user.anonymous_id,
            data: { message },
          },
        }).queue();
      }
    };

    // Register for general `inbound` path but also deprecated `unsubscribe`
    router.post(`/${namespace}/inbound`, inbound);
    router.post(`/${namespace}/unsubscribe`, inbound);

    return router;
  }
}
