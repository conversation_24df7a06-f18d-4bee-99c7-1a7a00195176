import {
  MetricsTime,
  Queue as <PERSON><PERSON><PERSON><PERSON>,
  Worker,
  JobsOptions,
  DelayedError,
} from "bullmq";
import { subMinutes } from "date-fns";
import { logger } from "../config/logger";
import { batch } from "../utilities";
import { EncodedJob } from "./Job";
import Queue, { QueueTypeConfig } from "./Queue";
import QueueProvider, { MetricPeriod, QueueMetric } from "./QueueProvider";
import { DefaultRedis, Redis, RedisConfig } from "../config/redis";

// Circuit breaker states
enum CircuitState {
  CLOSED = "CLOSED",
  OPEN = "OPEN",
  HALF_OPEN = "HALF_OPEN",
}

interface CircuitBreakerConfig {
  failureThreshold: number;
  timeout: number;
  retryTimeout: number;
}

interface QueueHealthMetrics {
  totalJobs: number;
  failedJobs: number;
  successfulJobs: number;
  averageProcessingTime: number;
  errorRate: number;
  lastHealthCheck: number;
}

export interface RedisQueueConfig extends QueueTypeConfig, RedisConfig {
  driver: "redis";
  concurrency: number;
  stalledInterval?: number; // In minutes
  maxStalledCount?: number;
  circuitBreaker?: CircuitBreakerConfig;
  healthCheck?: {
    enabled: boolean;
    interval: number; // milliseconds
  };
  autoRecovery?: {
    enabled: boolean;
    maxRetries: number;
    backoffMultiplier: number;
  };
}

export default class RedisQueueProvider implements QueueProvider {
  redis: Redis;
  queue: Queue;
  bull: BullQueue;
  worker?: Worker;
  concurrency: number;
  stalledInterval: number;
  maxStalledCount: number;
  batchSize = 60 as const;
  queueName = "bakedBot" as const;

  // Circuit breaker properties
  private circuitState: CircuitState = CircuitState.CLOSED;
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private circuitConfig: CircuitBreakerConfig;

  // Health monitoring
  private healthMetrics: QueueHealthMetrics = {
    totalJobs: 0,
    failedJobs: 0,
    successfulJobs: 0,
    averageProcessingTime: 0,
    errorRate: 0,
    lastHealthCheck: Date.now(),
  };

  private healthCheckInterval?: NodeJS.Timeout;
  private autoRecoveryConfig: Required<RedisQueueConfig["autoRecovery"]>;

  constructor(
    {
      concurrency,
      stalledInterval = 45,
      maxStalledCount = 1,
      circuitBreaker = {
        failureThreshold: 10, // Increased for AI jobs - more tolerant of temporary failures
        timeout: 120000, // 2 minutes - longer timeout for complex operations
        retryTimeout: 180000, // 3 minutes - faster recovery time
      },
      healthCheck = {
        enabled: true,
        interval: 30000, // 30 seconds
      },
      autoRecovery = {
        enabled: true,
        maxRetries: 3,
        backoffMultiplier: 2,
      },
      ...config
    }: RedisQueueConfig,
    queue: Queue
  ) {
    this.queue = queue;
    this.concurrency = concurrency;
    this.stalledInterval = stalledInterval;
    this.maxStalledCount = maxStalledCount;
    this.circuitConfig = circuitBreaker;
    this.autoRecoveryConfig = {
      enabled: autoRecovery.enabled,
      maxRetries: autoRecovery.maxRetries,
      backoffMultiplier: autoRecovery.backoffMultiplier,
    };

    this.redis = DefaultRedis(config, {
      // Enhanced Redis configuration for reliability
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      lazyConnect: true,
      connectTimeout: 30000, // Increased from 10000ms to 30000ms (30 seconds)
      commandTimeout: 30000, // Increased from 5000ms to 30000ms (30 seconds)
      // Add additional resilience settings
      retryDelayOnClusterDown: 300,
      retryDelayOnCommand: 100,
      maxRetriesPerRequest: null,
      enableOfflineQueue: false,
    });

    this.bull = new BullQueue(this.queueName, {
      connection: this.redis,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: "exponential",
          delay: 1000,
        },
        removeOnComplete: 10, // Keep last 10 completed jobs
        removeOnFail: 50, // Keep last 50 failed jobs for analysis
      },
    });

    // Start health monitoring if enabled
    if (healthCheck.enabled) {
      this.startHealthMonitoring(healthCheck.interval);
    }

    // Set up Redis connection monitoring
    this.setupRedisMonitoring();
  }

  async enqueue(job: EncodedJob): Promise<void> {
    if (this.circuitState === CircuitState.OPEN) {
      logger.warn("Circuit breaker is OPEN, rejecting job enqueue");
      throw new Error("Queue circuit breaker is OPEN");
    }

    try {
      const { name, data, opts } = this.adaptJob(job);
      await this.bull.add(name, data, opts);

      // Record successful operation
      this.onSuccess();
    } catch (error) {
      this.onFailure(error);
      logger.error(error, "redis:error:enqueue");
      throw error;
    }
  }

  async enqueueBatch(jobs: EncodedJob[]): Promise<void> {
    if (this.circuitState === CircuitState.OPEN) {
      logger.warn("Circuit breaker is OPEN, rejecting batch enqueue");
      throw new Error("Queue circuit breaker is OPEN");
    }

    try {
      for (const part of batch(jobs, this.batchSize)) {
        await this.bull.addBulk(part.map((item) => this.adaptJob(item)));
      }

      // Record successful operation
      this.onSuccess();
    } catch (error) {
      this.onFailure(error);
      logger.error(error, "redis:error:enqueueBatch");
      throw error;
    }
  }

  async delay(job: EncodedJob, milliseconds: number): Promise<void> {
    // If we are missing required fields, just enqueue the job manually
    if (!job.options.jobId || !job.token) {
      job.options.delay = milliseconds;
      await this.enqueue(job);
      return;
    }

    try {
      // If we are able to fetch the job, properly move it to delayed
      const bullJob = await this.bull.getJob(job.options.jobId);
      await bullJob?.moveToDelayed(Date.now() + milliseconds, job.token);

      // Special error so job stays in queue instead of being removed
      throw new DelayedError();
    } catch (error) {
      if (error instanceof DelayedError) {
        throw error; // Re-throw DelayedError as expected
      }
      this.onFailure(error);
      throw error;
    }
  }

  private adaptJob(job: EncodedJob): {
    name: string;
    data: any;
    opts: JobsOptions | undefined;
  } {
    return {
      name: job.name,
      data: job,
      opts: {
        removeOnComplete: 10,
        removeOnFail: {
          count: 50,
          age: 24 * 3600, // keep up to 24 hours
        },
        delay: job.options.delay,
        attempts: job.options.attempts || 3,
        jobId: job.options.jobId,
        // Enhanced job options
        backoff: {
          type: "exponential",
          delay: 2000,
        },
      },
    };
  }

  start(): void {
    this.worker = new Worker(
      "bakedBot",
      async (job, token) => {
        const startTime = Date.now();

        try {
          await this.queue.dequeue({
            ...job.data,
            options: {
              ...job.data.options,
              jobId: job.id,
            },
            token,
          });

          // Record successful job processing
          const processingTime = Date.now() - startTime;
          this.updateHealthMetrics(true, processingTime);
        } catch (error) {
          // Record failed job processing
          this.updateHealthMetrics(false, Date.now() - startTime);
          throw error;
        }
      },
      {
        connection: this.redis,
        concurrency: this.concurrency,
        stalledInterval: this.stalledInterval * 60 * 1000, // Convert minutes to milliseconds
        maxStalledCount: this.maxStalledCount,
        metrics: {
          maxDataPoints: MetricsTime.TWO_WEEKS,
        },
        // Add heartbeat and health monitoring
        autorun: true,
        removeOnComplete: { count: 10 },
        removeOnFail: { count: 50 },
      }
    );

    // Enhanced error handling
    this.worker.on("failed", (job, error) => {
      logger.error(`Job ${job?.id} failed:`, {
        jobId: job?.id,
        jobName: job?.name,
        attempts: job?.attemptsMade,
        error: error.message,
        stack: error.stack,
      });

      this.onFailure(error);
      this.queue.errored(error, job?.data as EncodedJob);
    });

    this.worker.on("error", (error) => {
      logger.error("Worker error:", error);
      this.onFailure(error);
      this.queue.errored(error);
    });

    this.worker.on("completed", (job) => {
      logger.debug(`Job ${job.id} completed successfully`);
    });

    this.worker.on("stalled", (jobId) => {
      logger.warn(`Job ${jobId} stalled and will be retried`);
    });

    // Add heartbeat logging
    this.worker.on("ready", () => {
      logger.info(`Queue worker started with concurrency: ${this.concurrency}`);
    });
  }

  close(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    this.bull.close();
    this.worker?.close();
    logger.info("Queue worker closed");
  }

  async clearAll(): Promise<void> {
    await this.bull.obliterate({ force: true });
    logger.info("redis:queue:cleared");
  }

  async metrics(period: MetricPeriod): Promise<QueueMetric> {
    const waiting = await this.bull.getWaitingCount();
    const completed = await this.bull.getMetrics("completed");
    const data = completed.data.slice(0, period).map((count, index) => ({
      date: subMinutes(Date.now(), index),
      count: Math.floor(count),
    }));
    data.reverse();
    return {
      data,
      waiting,
    };
  }

  async failed() {
    return this.bull.getFailed();
  }

  // Circuit breaker implementation
  private onSuccess(): void {
    if (this.circuitState === CircuitState.HALF_OPEN) {
      logger.info("Circuit breaker: Operation succeeded, closing circuit");
      this.circuitState = CircuitState.CLOSED;
      this.failureCount = 0;
    }
  }

  private onFailure(error: any): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();

    logger.warn(
      `Circuit breaker: Failure ${this.failureCount}/${this.circuitConfig.failureThreshold}`,
      {
        error: error.message,
        circuitState: this.circuitState,
      }
    );

    if (this.failureCount >= this.circuitConfig.failureThreshold) {
      this.circuitState = CircuitState.OPEN;
      logger.error("Circuit breaker: OPENED due to failure threshold reached");

      // Schedule circuit breaker retry
      setTimeout(() => {
        this.circuitState = CircuitState.HALF_OPEN;
        logger.info(
          "Circuit breaker: Moved to HALF_OPEN, allowing test requests"
        );
      }, this.circuitConfig.retryTimeout);
    }
  }

  // Health monitoring
  private startHealthMonitoring(interval: number): void {
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, interval);
  }

  private async performHealthCheck(): Promise<void> {
    try {
      const stats = await this.bull.getJobCounts();
      const errorRate =
        this.healthMetrics.totalJobs > 0
          ? this.healthMetrics.failedJobs / this.healthMetrics.totalJobs
          : 0;

      logger.debug("Queue health check:", {
        waiting: stats.waiting,
        active: stats.active,
        completed: stats.completed,
        failed: stats.failed,
        errorRate: `${(errorRate * 100).toFixed(2)}%`,
        circuitState: this.circuitState,
        averageProcessingTime: `${this.healthMetrics.averageProcessingTime}ms`,
      });

      // Check for concerning patterns
      if (errorRate > 0.2) {
        // 20% error rate
        logger.warn(
          `High error rate detected: ${(errorRate * 100).toFixed(2)}%`
        );
      }

      if (stats.waiting && stats.waiting > 1000) {
        logger.warn(`High queue backlog: ${stats.waiting} jobs waiting`);
      }

      this.healthMetrics.lastHealthCheck = Date.now();
    } catch (error) {
      logger.error("Health check failed:", error);
      this.onFailure(error);
    }
  }

  private updateHealthMetrics(success: boolean, processingTime: number): void {
    this.healthMetrics.totalJobs++;

    if (success) {
      this.healthMetrics.successfulJobs++;
    } else {
      this.healthMetrics.failedJobs++;
    }

    // Update rolling average processing time
    this.healthMetrics.averageProcessingTime =
      (this.healthMetrics.averageProcessingTime + processingTime) / 2;

    this.healthMetrics.errorRate =
      this.healthMetrics.failedJobs / this.healthMetrics.totalJobs;
  }

  private setupRedisMonitoring(): void {
    this.redis.on("connect", () => {
      logger.info("Redis connection established");
    });

    this.redis.on("ready", () => {
      logger.info("Redis connection ready");
    });

    this.redis.on("error", (error) => {
      logger.error("Redis connection error:", error);
      this.onFailure(error);
    });

    this.redis.on("close", () => {
      logger.warn("Redis connection closed");
    });

    this.redis.on("reconnecting", (delay: number) => {
      logger.info(`Redis reconnecting in ${delay}ms`);
    });
  }

  private getJobTimeout(jobName: string): number {
    // Set different timeouts based on job type
    const timeouts: Record<string, number> = {
      AIEnhancementJob: 45 * 60 * 1000, // 45 minutes
      ProductDataVectorJob: 30 * 60 * 1000, // 30 minutes
      ProductPatchJob: 5 * 60 * 1000, // 5 minutes
      default: 10 * 60 * 1000, // 10 minutes default
    };

    return timeouts[jobName] || timeouts.default;
  }

  // Public method to get health status
  getHealthStatus(): QueueHealthMetrics & { circuitState: CircuitState } {
    return {
      ...this.healthMetrics,
      circuitState: this.circuitState,
    };
  }
}
