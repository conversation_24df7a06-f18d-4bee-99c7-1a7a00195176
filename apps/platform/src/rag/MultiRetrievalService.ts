import { logger } from "../config/logger";
import { QueryPlan } from "./QueryPlannerService";
import { PosDataVectorService } from "../pos/PosDataVectorService";
import { ProductDataVectorService } from "../products/ProductDataVectorService";
import { ReviewDataVectorService } from "../reviews/ReviewDataVectorService";
import { CompetitorDataVectorService } from "../competitors/CompetitorDataVectorService";
import { RetailerDataVectorService } from "../retailers/RetailerDataVectorService";
import { UserDataVectorService } from "../users/UserDataVectorService";

/**
 * Represents a retrieval result from any source
 */
export interface RetrievalResult {
  // Source of the data
  source: string;

  // Content of the retrieval
  content: string;

  // Confidence or relevance score (0-1)
  score: number;

  // Metadata from the source
  metadata?: Record<string, any>;

  // Original query that produced this result
  query?: string;

  // Retrieval method used
  method: string;
}

/**
 * MultiRetrievalService implements a flexible retrieval system that can:
 * 1. Use multiple retrieval methods based on the query type
 * 2. Implement fallback mechanisms when primary methods fail
 * 3. Combine results from different sources
 * 4. Re-rank results to prioritize the most relevant information
 */
export class MultiRetrievalService {
  constructor() {
    logger.info("MultiRetrievalService initialized");
  }

  /**
   * Main method to retrieve data based on a query plan
   */
  async retrieveData(
    plan: QueryPlan,
    locationId: number,
    options: {
      maxResults?: number;
      timeoutMs?: number;
    } = {}
  ): Promise<RetrievalResult[]> {
    const { maxResults = 10, timeoutMs = 5000 } = options;

    try {
      logger.info(
        {
          primary_method: plan.primaryMethod,
          data_sources: plan.dataSources,
          query: plan.enhancedQuery,
        },
        "Starting data retrieval"
      );

      // Set up a timeout promise
      const timeoutPromise = new Promise<RetrievalResult[]>(
        (_resolve, _reject) => {
          setTimeout(() => _reject(new Error("Retrieval timeout")), timeoutMs);
        }
      );

      // Main retrieval promise
      const retrievalPromise = this.executeRetrievalPlan(
        plan,
        locationId,
        maxResults
      );

      // Race between retrieval and timeout
      let results: RetrievalResult[] = [];
      try {
        results = await Promise.race([retrievalPromise, timeoutPromise]);
      } catch (error) {
        logger.warn(
          {
            error: error instanceof Error ? error.message : String(error),
            query: plan.enhancedQuery,
            primary_method: plan.primaryMethod,
          },
          "Error with primary retrieval method, trying fallbacks"
        );
      }

      // If no results or error, try fallback methods
      if (results.length === 0) {
        logger.warn(
          {
            query: plan.enhancedQuery,
            primary_method: plan.primaryMethod,
          },
          "No results found with primary method, trying fallbacks"
        );

        // Try fallback methods sequentially
        for (const fallbackMethod of plan.fallbackMethods) {
          if (fallbackMethod === plan.primaryMethod) continue;

          try {
            const fallbackPlan = {
              ...plan,
              primaryMethod: fallbackMethod as typeof plan.primaryMethod,
            };

            const fallbackResults = await this.executeRetrievalPlan(
              fallbackPlan,
              locationId,
              maxResults
            );

            if (fallbackResults.length > 0) {
              logger.info(
                {
                  method: fallbackMethod,
                  result_count: fallbackResults.length,
                },
                "Got results from fallback method"
              );
              return fallbackResults;
            }
          } catch (fallbackError) {
            logger.warn(
              {
                error:
                  fallbackError instanceof Error
                    ? fallbackError.message
                    : String(fallbackError),
                method: fallbackMethod,
              },
              "Fallback method failed"
            );
            // Continue to the next fallback
          }
        }
      }

      return results;
    } catch (error) {
      logger.error(
        {
          error:
            error instanceof Error
              ? { message: error.message, stack: error.stack }
              : String(error),
          query: plan.enhancedQuery,
        },
        "Error in data retrieval"
      );
      return [];
    }
  }

  /**
   * Execute the retrieval plan with the specified method
   */
  private async executeRetrievalPlan(
    plan: QueryPlan,
    locationId: number,
    maxResults: number
  ): Promise<RetrievalResult[]> {
    try {
      // Based on the primary method, call the appropriate retrieval function
      switch (plan.primaryMethod) {
        case "direct_query":
          return await this.performDirectQuery(plan, locationId, maxResults);

        case "vector_search":
          return await this.performVectorSearch(plan, locationId, maxResults);

        case "hybrid":
          return await this.performHybridSearch(plan, locationId, maxResults);

        case "keyword_search":
          return await this.performKeywordSearch(plan, locationId, maxResults);

        default:
          logger.warn(
            { method: plan.primaryMethod },
            "Unknown retrieval method"
          );
          return [];
      }
    } catch (error) {
      logger.error(
        {
          error:
            error instanceof Error
              ? { message: error.message, stack: error.stack }
              : String(error),
          method: plan.primaryMethod,
          query: plan.enhancedQuery,
        },
        "Error executing retrieval plan"
      );
      return [];
    }
  }

  /**
   * Perform direct database queries for specific kinds of data
   */
  private async performDirectQuery(
    plan: QueryPlan,
    locationId: number,
    maxResults: number
  ): Promise<RetrievalResult[]> {
    const results: RetrievalResult[] = [];

    try {
      // Check for monthly sales queries which can use direct DB lookup
      const { month, year } = this.extractMonthYearFromQuery(
        plan.enhancedQuery
      );

      if (month && plan.dataSources.includes("pos_data")) {
        try {
          // Check if this is a product-specific query
          const isProductQuery = this.isProductQuery(plan.enhancedQuery);

          if (isProductQuery) {
            // Get top selling products for the month
            const productResults =
              await PosDataVectorService.getTopSellingProductsByMonth(
                locationId,
                month,
                maxResults,
                year ?? undefined
              );

            if (productResults && productResults.products.length > 0) {
              // Create a formatted content string for the top products
              const topProduct = productResults.products[0];
              const formattedSales = new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: "USD",
              }).format(topProduct.totalSales);

              // Build response content
              let content = `The top selling product in ${productResults.monthName} ${productResults.yearValue} was "${topProduct.productName}" (${topProduct.category}) with ${formattedSales} in sales.\n\n`;

              // Add table of top products
              content += "Top selling products:\n";
              productResults.products.forEach((product, index) => {
                const productSales = new Intl.NumberFormat("en-US", {
                  style: "currency",
                  currency: "USD",
                }).format(product.totalSales);

                content += `${index + 1}. ${
                  product.productName
                } - ${productSales} (${product.totalQuantity} units)\n`;
              });

              results.push({
                source: "pos_data_direct",
                content,
                score: 1.0, // Direct DB results get highest score
                metadata: {
                  topProducts: productResults.products,
                  monthName: productResults.monthName,
                  yearValue: productResults.yearValue,
                  queryType: "product_sales",
                },
                method: "direct_query",
                query: plan.enhancedQuery,
              });

              logger.info(
                {
                  found: true,
                  month: productResults.monthName,
                  year: productResults.yearValue,
                  query: plan.enhancedQuery,
                  top_product: topProduct.productName,
                },
                "Found top products for monthly sales query"
              );
            }
          } else {
            // Get total sales for the month (original behavior)
            const dbResults = await PosDataVectorService.getTotalSalesByMonth(
              locationId,
              month,
              year ?? undefined
            );

            if (dbResults) {
              // Format the monthly total data into a useful context
              const formattedSales = new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: "USD",
              }).format(dbResults.totalSales);

              const content =
                `Based on ${dbResults.recordCount.toLocaleString()} transactions, ` +
                `the total sales for ${dbResults.monthName} ${dbResults.yearValue} ` +
                `were ${formattedSales}.`;

              results.push({
                source: "pos_data_direct",
                content,
                score: 1.0, // Direct DB results get highest score
                metadata: {
                  totalSales: dbResults.totalSales,
                  recordCount: dbResults.recordCount,
                  monthName: dbResults.monthName,
                  yearValue: dbResults.yearValue,
                },
                method: "direct_query",
                query: plan.enhancedQuery,
              });

              logger.info(
                {
                  found: true,
                  month: dbResults.monthName,
                  year: dbResults.yearValue,
                  query: plan.enhancedQuery,
                },
                "Found direct query results for monthly sales"
              );
            }
          }
        } catch (error) {
          logger.error(
            {
              error:
                error instanceof Error
                  ? { message: error.message, stack: error.stack }
                  : String(error),
              month,
              year,
            },
            "Error getting direct monthly sales data"
          );
        }
      }

      return results;
    } catch (error) {
      logger.error(
        {
          error:
            error instanceof Error
              ? { message: error.message, stack: error.stack }
              : String(error),
          query: plan.enhancedQuery,
        },
        "Error in direct query retrieval"
      );
      return [];
    }
  }

  /**
   * Determines if a query is asking about products (as opposed to just total sales)
   */
  private isProductQuery(query: string): boolean {
    const lowercaseQuery = query.toLowerCase();

    // List of patterns indicating product-specific queries
    const productKeywords = [
      "product",
      "item",
      "sold the most",
      "top selling",
      "best seller",
      "most popular",
      "best performing",
      "highest sales",
      "most sold",
      "what did we sell",
      "what product",
    ];

    return productKeywords.some((keyword) => lowercaseQuery.includes(keyword));
  }

  /**
   * Parse month and year from a query string
   */
  private extractMonthYearFromQuery(query: string): {
    month: string | null;
    year: number | null;
  } {
    const lowercaseQuery = query.toLowerCase();

    // Extract month
    const months = [
      "january",
      "february",
      "march",
      "april",
      "may",
      "june",
      "july",
      "august",
      "september",
      "october",
      "november",
      "december",
    ];

    const monthAbbreviations: Record<string, string> = {
      jan: "january",
      feb: "february",
      mar: "march",
      apr: "april",
      jun: "june",
      jul: "july",
      aug: "august",
      sep: "september",
      oct: "october",
      nov: "november",
      dec: "december",
    };

    // Find month name in query
    let month: string | null = null;

    // Check for full month names
    for (const m of months) {
      if (lowercaseQuery.includes(m)) {
        month = m;
        break;
      }
    }

    // If no full month name found, check for abbreviations
    if (!month) {
      for (const [abbr, fullMonth] of Object.entries(monthAbbreviations)) {
        if (lowercaseQuery.includes(abbr)) {
          month = fullMonth;
          break;
        }
      }
    }

    // Extract year (4-digit or 2-digit)
    const yearMatch =
      lowercaseQuery.match(/\b(20\d{2})\b/) ||
      lowercaseQuery.match(/\b(19\d{2})\b/);
    const twoDigitYearMatch = !yearMatch && lowercaseQuery.match(/\b(\d{2})\b/);

    let year: number | null = null;

    if (yearMatch) {
      year = parseInt(yearMatch[1], 10);
    } else if (twoDigitYearMatch) {
      const twoDigitYear = parseInt(twoDigitYearMatch[1], 10);
      // Assume 20xx for years less than 50, 19xx for years 50+
      year = twoDigitYear < 50 ? 2000 + twoDigitYear : 1900 + twoDigitYear;
    } else if (
      /this year|current year|ytd|year to date/i.test(lowercaseQuery)
    ) {
      year = new Date().getFullYear();
    }

    return { month, year };
  }

  /**
   * Perform vector (semantic) search
   */
  private async performVectorSearch(
    plan: QueryPlan,
    locationId: number,
    maxResults: number
  ): Promise<RetrievalResult[]> {
    const results: RetrievalResult[] = [];

    try {
      // Create promises for all data sources to search in parallel
      const searchPromises = plan.dataSources.map(async (source) => {
        try {
          let sourceResults: any[] = [];

          switch (source) {
            case "pos_data":
              sourceResults = await PosDataVectorService.queryPosData(
                plan.enhancedQuery,
                locationId,
                maxResults
              );
              break;

            case "product_data":
              sourceResults = await ProductDataVectorService.queryProductData(
                plan.enhancedQuery,
                { location_id: locationId },
                maxResults
              );
              break;

            case "review_data":
              sourceResults = await ReviewDataVectorService.queryReviewData(
                plan.enhancedQuery,
                { source_type: "review" },
                maxResults
              );
              break;

            case "competitor_data":
              sourceResults =
                await CompetitorDataVectorService.queryCompetitorData(
                  plan.enhancedQuery,
                  locationId,
                  maxResults
                );
              break;

            case "retailer_data":
              sourceResults = await RetailerDataVectorService.queryRetailerData(
                plan.enhancedQuery,
                {},
                maxResults
              );
              break;

            case "customer_data":
              // Typically customer data requires a customer ID, which we don't have in this context
              // This would need to be extended if customer data is needed without ID
              break;
          }

          return this.formatSourceResults(
            sourceResults,
            source,
            "vector_search",
            plan.enhancedQuery
          );
        } catch (error) {
          logger.error(
            {
              error:
                error instanceof Error
                  ? { message: error.message, stack: error.stack }
                  : String(error),
              source,
              query: plan.enhancedQuery,
            },
            "Error in vector search for source"
          );
          return [];
        }
      });

      // Wait for all searches to complete
      const allResults = await Promise.all(searchPromises);

      // Combine and flatten results
      return allResults.flat();
    } catch (error) {
      logger.error(
        {
          error:
            error instanceof Error
              ? { message: error.message, stack: error.stack }
              : String(error),
          query: plan.enhancedQuery,
        },
        "Error in vector search"
      );
      return [];
    }
  }

  /**
   * Perform hybrid search (vector + keyword)
   */
  private async performHybridSearch(
    plan: QueryPlan,
    locationId: number,
    maxResults: number
  ): Promise<RetrievalResult[]> {
    try {
      const results: RetrievalResult[] = [];

      // For data sources that support hybrid search, use it directly
      if (plan.dataSources.includes("pos_data")) {
        try {
          const hybridResults = await PosDataVectorService.hybridSearch(
            plan.enhancedQuery,
            locationId,
            {
              topK: maxResults,
              alpha: 0.5,
              includeMetadata: true,
            }
          );

          // Process hybrid search results
          if (hybridResults.combinedResponse) {
            // If we have a pre-formatted combined response, use it
            results.push({
              source: "pos_data",
              content: hybridResults.combinedResponse,
              score: 1.0,
              method: "hybrid",
              query: plan.enhancedQuery,
            });
          } else {
            // Otherwise format the semantic results
            if (
              hybridResults.semanticResults &&
              hybridResults.semanticResults.length > 0
            ) {
              const vectorResults = this.formatSourceResults(
                hybridResults.semanticResults,
                "pos_data",
                "hybrid_semantic",
                plan.enhancedQuery
              );
              results.push(...vectorResults);
            }

            // Add direct results if available
            if (hybridResults.directResults) {
              const formattedSales = new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: "USD",
              }).format(hybridResults.directResults.totalSales);

              const content =
                `Based on ${hybridResults.directResults.recordCount.toLocaleString()} transactions, ` +
                `the total sales for ${hybridResults.directResults.monthName} ${hybridResults.directResults.yearValue} ` +
                `were ${formattedSales}.`;

              results.push({
                source: "pos_data_direct",
                content,
                score: 1.0,
                metadata: {
                  totalSales: hybridResults.directResults.totalSales,
                  recordCount: hybridResults.directResults.recordCount,
                  monthName: hybridResults.directResults.monthName,
                  yearValue: hybridResults.directResults.yearValue,
                },
                method: "hybrid_direct",
                query: plan.enhancedQuery,
              });
            }
          }
        } catch (error) {
          logger.error(
            {
              error:
                error instanceof Error
                  ? { message: error.message, stack: error.stack }
                  : String(error),
              query: plan.enhancedQuery,
            },
            "Error in POS hybrid search"
          );
        }
      }

      // For other data sources that don't have hybrid search built in,
      // fall back to vector search for now
      const otherDataSources = plan.dataSources.filter(
        (source) => source !== "pos_data" || results.length === 0
      );

      if (otherDataSources.length > 0) {
        try {
          // Create a type-safe vectorPlan with the correct primaryMethod type
          const vectorPlan: QueryPlan = {
            ...plan,
            primaryMethod: "vector_search",
            dataSources: otherDataSources,
          };

          const vectorResults = await this.performVectorSearch(
            vectorPlan,
            locationId,
            maxResults
          );
          results.push(...vectorResults);
        } catch (vectorError) {
          logger.error(
            {
              error:
                vectorError instanceof Error
                  ? vectorError.message
                  : String(vectorError),
              query: plan.enhancedQuery,
              data_sources: otherDataSources,
            },
            "Error in vector search fallback during hybrid search"
          );
        }
      }

      return results;
    } catch (error) {
      logger.error(
        {
          error:
            error instanceof Error
              ? { message: error.message, stack: error.stack }
              : String(error),
          query: plan.enhancedQuery,
        },
        "Error in hybrid search"
      );
      return [];
    }
  }

  /**
   * Perform keyword-based search
   */
  private async performKeywordSearch(
    plan: QueryPlan,
    locationId: number,
    maxResults: number
  ): Promise<RetrievalResult[]> {
    // Since we don't have a dedicated keyword search implementation yet,
    // we're using vector search with keyword enhancements
    try {
      // Enhance the query with exact terms and keywords
      const keywordEnhancedQuery = this.enhanceWithKeywords(plan.enhancedQuery);
      const keywordPlan = { ...plan, enhancedQuery: keywordEnhancedQuery };

      return this.performVectorSearch(keywordPlan, locationId, maxResults);
    } catch (error) {
      logger.error(
        {
          error:
            error instanceof Error
              ? { message: error.message, stack: error.stack }
              : String(error),
          query: plan.enhancedQuery,
        },
        "Error in keyword search"
      );
      return [];
    }
  }

  /**
   * Enhance a query with exact keywords to improve matching
   */
  private enhanceWithKeywords(query: string): string {
    // Extract key terms using basic NLP-like approach
    const words = query
      .toLowerCase()
      .split(/\W+/)
      .filter(
        (word) =>
          word.length > 2 &&
          ![
            "the",
            "and",
            "for",
            "was",
            "that",
            "this",
            "with",
            "have",
            "from",
          ].includes(word)
      );

    // Add exact match notation
    const exactTerms = words
      .filter((word) => word.length > 3)
      .slice(0, 3) // Take just a few important terms
      .map((word) => `"${word}"`)
      .join(" ");

    return `${query} ${exactTerms}`;
  }

  /**
   * Format results from a source into a consistent structure
   */
  private formatSourceResults(
    results: any[],
    source: string,
    method: string,
    query: string
  ): RetrievalResult[] {
    if (!results || results.length === 0) {
      return [];
    }

    return results.map((item, index) => {
      // Extract metadata from item
      const metadata = item.metadata || {};

      // Generate content based on metadata and source type
      let content = "";
      let date: Date;
      let reviewDate: string;

      switch (source) {
        case "pos_data":
          date = new Date(metadata.order_date || Date.now());

          content =
            `Product: ${metadata.product_name || "Unknown"}\n` +
            `Category: ${metadata.master_category || "Unknown"}\n` +
            `Date: ${date.toLocaleDateString()}\n` +
            `Sales: $${parseFloat(metadata.gross_sales || 0).toFixed(2)}\n` +
            `Net Sales: $${parseFloat(metadata.net_sales || 0).toFixed(2)}\n` +
            `Profit: $${parseFloat(metadata.inventory_profit || 0).toFixed(2)}`;
          break;

        case "product_data":
          content =
            `Product: ${metadata.product_name || "Unknown"}\n` +
            `SKU: ${metadata.meta_sku || "Unknown"}\n` +
            `Brand: ${metadata.brand_name || "Unknown"}\n` +
            `Category: ${metadata.category || "Unknown"}\n` +
            `THC: ${metadata.percentage_thc || "Unknown"}%\n` +
            `CBD: ${metadata.percentage_cbd || "Unknown"}%\n` +
            `Price: $${metadata.latest_price || "Unknown"}\n` +
            `Description: ${metadata.product_description || "No description"}`;
          break;

        case "review_data":
          reviewDate = metadata.review_date
            ? new Date(metadata.review_date).toLocaleDateString()
            : "Unknown";

          content =
            `Product: ${metadata.product_id || "Unknown"}\n` +
            `Rating: ${metadata.rating || "Unknown"}/5\n` +
            `Author: ${metadata.author || "Anonymous"}\n` +
            `Date: ${reviewDate}\n` +
            `Content: "${metadata.text || "No text"}"`;
          break;

        case "competitor_data":
          content =
            `Competitor: ${metadata.name || "Unknown"}\n` +
            `Address: ${metadata.address || "Unknown"}\n` +
            `Distance: ${
              metadata.distance_km
                ? parseFloat(String(metadata.distance_km)).toFixed(2) + " km"
                : "Unknown"
            }\n` +
            `Products: ${metadata.productCount || "Unknown"}\n` +
            `Special offers: ${metadata.specialCount || "Unknown"}`;
          break;

        case "retailer_data":
          content =
            `Retailer: ${metadata.name || "Unknown"}\n` +
            `Address: ${metadata.address || "Unknown"}, ${
              metadata.city || ""
            }, ${metadata.state || ""}\n` +
            `Phone: ${metadata.phone || "Unknown"}\n` +
            `Medical: ${metadata.serves_medical_users ? "Yes" : "No"}\n` +
            `Recreational: ${
              metadata.serves_recreational_users ? "Yes" : "No"
            }\n` +
            `Rating: ${metadata.rating || "Unknown"}\n` +
            `Reviews: ${metadata.reviews_count || "Unknown"}`;
          break;

        case "customer_data":
          content =
            `Customer: ${metadata.full_name || "Unknown"}\n` +
            `Email: ${metadata.email || "Unknown"}\n` +
            `Phone: ${metadata.phone || "Unknown"}\n` +
            `Timezone: ${metadata.timezone || "Unknown"}\n` +
            `Language: ${metadata.locale || "Unknown"}`;
          break;

        default:
          // Generic formatting for unknown sources
          content = Object.entries(metadata)
            .filter(
              ([key]) => !["id", "created_at", "updated_at"].includes(key)
            )
            .map(([key, value]) => `${key.replace(/_/g, " ")}: ${value}`)
            .join("\n");
      }

      // Adjust score for ranking purposes
      const adjustedScore = item.score
        ? item.score * (1 - index * 0.01)
        : 0.9 - index * 0.01;

      return {
        source,
        content,
        score: adjustedScore,
        metadata,
        method,
        query,
      };
    });
  }

  /**
   * Re-rank results based on relevance to the original query
   */
  async rerank(
    results: RetrievalResult[],
    query: string,
    options: {
      topK?: number;
    } = {}
  ): Promise<RetrievalResult[]> {
    const { topK = 5 } = options;

    // If no results or just one, no need to rerank
    if (results.length <= 1) {
      return results;
    }

    try {
      // For now, use a simple reranking approach based on:
      // 1. Priority to direct query results (highest score)
      // 2. Results with higher initial score
      // 3. Results with more complete/detailed content
      // 4. Results that have more keywords from the query

      const queryWords = new Set(
        query
          .toLowerCase()
          .split(/\W+/)
          .filter((word) => word.length > 3)
      );

      const rerankedResults = [...results].sort((a, b) => {
        // Direct query results always come first
        if (a.method.includes("direct") && !b.method.includes("direct")) {
          return -1;
        }
        if (!a.method.includes("direct") && b.method.includes("direct")) {
          return 1;
        }

        // Calculate keyword overlap score
        const aKeywordMatch = this.calculateKeywordOverlap(
          a.content,
          queryWords
        );
        const bKeywordMatch = this.calculateKeywordOverlap(
          b.content,
          queryWords
        );

        // Calculate richness score (content with more detail is better)
        const aRichness = a.content.length / 100;
        const bRichness = b.content.length / 100;

        // Combined score with weights
        const aScore = a.score * 0.5 + aKeywordMatch * 0.3 + aRichness * 0.2;
        const bScore = b.score * 0.5 + bKeywordMatch * 0.3 + bRichness * 0.2;

        return bScore - aScore;
      });

      // Take top K results
      return rerankedResults.slice(0, topK);
    } catch (error) {
      logger.error(
        {
          error: error instanceof Error ? error.message : String(error),
          query,
        },
        "Error reranking results"
      );
      return results.slice(0, topK);
    }
  }

  /**
   * Calculate keyword overlap between content and query words
   */
  private calculateKeywordOverlap(
    content: string,
    queryWords: Set<string>
  ): number {
    const contentWords = new Set(
      content
        .toLowerCase()
        .split(/\W+/)
        .filter((word) => word.length > 3)
    );

    let matchCount = 0;
    for (const word of queryWords) {
      if (contentWords.has(word)) {
        matchCount++;
      }
    }

    return queryWords.size > 0 ? matchCount / queryWords.size : 0;
  }
}
