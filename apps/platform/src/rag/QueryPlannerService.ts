import { OpenAI } from "openai";
import { logger } from "../config/logger";

/**
 * QueryPlan represents the strategy for retrieving data
 */
export interface QueryPlan {
  // Primary retrieval method to use
  primaryMethod: "vector_search" | "direct_query" | "hybrid" | "keyword_search";

  // Secondary/fallback methods in order of preference
  fallbackMethods: (
    | "vector_search"
    | "direct_query"
    | "hybrid"
    | "keyword_search"
  )[];

  // Required data sources
  dataSources: string[];

  // Enhanced query for better retrieval
  enhancedQuery: string;

  // Original query for reference
  originalQuery: string;

  // Sub-queries if the original was decomposed
  subQueries?: string[];

  // Query type classification
  queryType: "factual" | "analytical" | "temporal" | "comparative" | "general";

  // Whether to use re-ranking
  useReranking: boolean;

  // Additional filters to apply
  filters?: Record<string, any>;

  // A confidence score for this plan
  confidence: number;
}

/**
 * Query Planner Service
 *
 * This service implements advanced RAG techniques:
 * 1. Query planning: Determines optimal retrieval strategy
 * 2. Query refinement: Enhances queries for better results
 * 3. Query decomposition: Breaks complex queries into simpler ones
 */
export class QueryPlannerService {
  private openai: OpenAI;
  private defaultModel: string = "gpt-4.1-mini";

  constructor(
    openai: OpenAI,
    options?: {
      defaultModel?: string;
    }
  ) {
    this.openai = openai;

    if (options?.defaultModel) {
      this.defaultModel = options.defaultModel;
    }

    logger.info(
      {
        model: this.defaultModel,
      },
      "QueryPlannerService initialized"
    );
  }

  /**
   * Creates a query plan for optimal data retrieval
   */
  async createQueryPlan(query: string): Promise<QueryPlan> {
    try {
      logger.info({ query }, "Creating query plan");

      // Step 1: Correct spelling and standardize the query
      const normalizedQuery = await this.normalizeQuery(query);

      // Step 2: Use LLM to create a comprehensive query plan
      const plan = await this.generatePlanWithLLM(normalizedQuery);

      // Step 3: Apply special business rules and overrides
      this.applyBusinessRules(plan, normalizedQuery);

      logger.info(
        {
          original_query: query,
          enhanced_query: plan.enhancedQuery,
          primary_method: plan.primaryMethod,
          data_sources: plan.dataSources,
          query_type: plan.queryType,
          confidence: plan.confidence,
        },
        "Query plan created"
      );

      return plan;
    } catch (error) {
      logger.error({ error, query }, "Error creating query plan");

      // Fallback to a basic plan in case of errors
      return this.createFallbackPlan(query);
    }
  }

  /**
   * Normalizes a query by correcting spelling and standardizing terms
   */
  private async normalizeQuery(query: string): Promise<string> {
    try {
      // Simple case: check for common misspellings of months
      const monthMisspellings: Record<string, string> = {
        janurary: "january",
        feburary: "february",
        febuary: "february",
        marh: "march",
        apil: "april",
        aprl: "april",
        junuary: "january",
        agust: "august",
        sepember: "september",
        septmber: "september",
        ocober: "october",
        octobar: "october",
        noveber: "november",
        deceber: "december",
        decembre: "december",
      };

      let normalizedQuery = query;

      // Check for month misspellings
      Object.entries(monthMisspellings).forEach(([misspelling, correction]) => {
        const regex = new RegExp(`\\b${misspelling}\\b`, "gi");
        if (regex.test(normalizedQuery)) {
          normalizedQuery = normalizedQuery.replace(regex, correction);
          logger.info(
            {
              original: misspelling,
              corrected: correction,
              query: normalizedQuery,
            },
            "Corrected month spelling"
          );
        }
      });

      // For more complex normalization, use LLM
      if (normalizedQuery === query && query.length > 10) {
        const completion = await this.openai.chat.completions.create({
          model: this.defaultModel,
          messages: [
            {
              role: "system",
              content: `Correct any spelling mistakes or standardize terms in the user's query. If the query is already correct, return it exactly as is. Only fix obvious spelling issues or standardize terms like month names, product categories, or cannabis-related terminology.`,
            },
            {
              role: "user",
              content: query,
            },
          ],
          temperature: 0.1,
          max_tokens: 100,
        });

        const correctedQuery =
          completion.choices[0]?.message?.content?.trim() || query;

        // Only use the corrected version if it's different but similar
        if (
          correctedQuery !== query &&
          this.calculateSimilarity(query, correctedQuery) > 0.7
        ) {
          logger.info(
            {
              original: query,
              corrected: correctedQuery,
            },
            "Applied LLM query correction"
          );
          return correctedQuery;
        }
      }

      return normalizedQuery;
    } catch (error) {
      logger.warn({ error, query }, "Error normalizing query, using original");
      return query;
    }
  }

  /**
   * Very basic similarity check between two strings (0-1 score)
   */
  private calculateSimilarity(str1: string, str2: string): number {
    const s1 = str1.toLowerCase();
    const s2 = str2.toLowerCase();

    // If lengths are very different, similarity is low
    if (Math.abs(s1.length - s2.length) > s1.length * 0.3) {
      return 0.5;
    }

    // Count matching characters
    let matches = 0;
    for (let i = 0; i < s1.length; i++) {
      if (s2.includes(s1[i])) {
        matches++;
      }
    }

    return matches / Math.max(s1.length, s2.length);
  }

  /**
   * Generates a query plan using LLM
   */
  private async generatePlanWithLLM(query: string): Promise<QueryPlan> {
    try {
      const completion = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system",
            content: `You are a query planning system for a cannabis retail analytics platform.
            
Your job is to analyze a user query and create an optimal retrieval plan.

Available retrieval methods:
- vector_search: Semantic search using embeddings, good for meaning and concepts
- direct_query: SQL database queries, best for exact numbers, totals, or time-based aggregations
- hybrid: Combines vector search with keyword search, good for complex queries
- keyword_search: BM25/keyword matching, good for specific terms or product names

Available data sources:
- pos_data: Point of sale transactions, sales, revenue
- product_data: Products, inventory, prices, THC/CBD content
- customer_data: Customer profiles and purchase history
- review_data: Product reviews and ratings
- competitor_data: Competitor information, pricing, products
- retailer_data: Store information, locations, hours

Return a JSON object with this structure:
{
  "primaryMethod": "vector_search"|"direct_query"|"hybrid"|"keyword_search",
  "fallbackMethods": ["method1", "method2"],
  "dataSources": ["source1", "source2"],
  "enhancedQuery": "improved version of query",
  "originalQuery": "original query",
  "subQueries": ["sub-query1", "sub-query2"], // optional
  "queryType": "factual"|"analytical"|"temporal"|"comparative"|"general",
  "useReranking": true|false,
  "filters": {}, // optional
  "confidence": 0.1-1.0
}

Guidelines:
- For monthly sales totals or specific time period aggregations, use direct_query
- For product recommendations or semantic questions, use vector_search
- For complex questions with multiple aspects, consider query decomposition
- Always enhance the query with relevant terms to improve retrieval
- Include all necessary data sources, but no more than needed
- Set confidence based on how clear the query intent is`,
          },
          {
            role: "user",
            content: query,
          },
        ],
        temperature: 0.2,
        response_format: { type: "json_object" },
        max_tokens: 800,
      });

      const planData = JSON.parse(
        completion.choices[0]?.message?.content || "{}"
      );

      // Ensure all required fields exist
      const plan: QueryPlan = {
        primaryMethod: planData.primaryMethod || "hybrid",
        fallbackMethods: planData.fallbackMethods || [
          "vector_search",
          "keyword_search",
        ],
        dataSources: planData.dataSources || ["pos_data"],
        enhancedQuery: planData.enhancedQuery || query,
        originalQuery: query,
        queryType: planData.queryType || "general",
        useReranking:
          planData.useReranking !== undefined ? planData.useReranking : true,
        confidence: planData.confidence || 0.7,
      };

      // Optional fields
      if (planData.subQueries && Array.isArray(planData.subQueries)) {
        plan.subQueries = planData.subQueries;
      }

      if (planData.filters && typeof planData.filters === "object") {
        plan.filters = planData.filters;
      }

      return plan;
    } catch (error) {
      logger.error({ error, query }, "Error generating plan with LLM");
      return this.createFallbackPlan(query);
    }
  }

  /**
   * Applies business-specific rules to override LLM decisions when needed
   */
  private applyBusinessRules(plan: QueryPlan, query: string): void {
    // Check for monthly sales queries with direct regex patterns
    const monthNames = [
      "january",
      "february",
      "march",
      "april",
      "may",
      "june",
      "july",
      "august",
      "september",
      "october",
      "november",
      "december",
    ];

    // Match month name in query
    const hasMonth = monthNames.some((month) =>
      query.toLowerCase().includes(month)
    );

    const monthlySalesPattern = new RegExp(
      `(sales|revenue|sold|earned).*(in|for|during|of).*${monthNames.join(
        "|"
      )}|` +
        `(${monthNames.join("|")}).*sales|` +
        `(monthly|month).*(sales|revenue|total)`,
      "i"
    );

    // 1. Monthly sales totals MUST use direct query as primary method
    if (
      (hasMonth &&
        query.toLowerCase().match(/(sales|revenue|sold|earned|total)/)) ||
      monthlySalesPattern.test(query)
    ) {
      plan.primaryMethod = "direct_query";
      if (!plan.dataSources.includes("pos_data")) {
        plan.dataSources.push("pos_data");
      }
      logger.info(
        "Applied business rule: Monthly sales query detected, forcing direct_query"
      );
    }

    // 2. Best sellers should always use hybrid search and include both pos_data and product_data
    if (query.toLowerCase().match(/(best|top|popular).*sell|most sold/i)) {
      plan.primaryMethod = "hybrid";
      if (!plan.dataSources.includes("pos_data")) {
        plan.dataSources.push("pos_data");
      }
      if (!plan.dataSources.includes("product_data")) {
        plan.dataSources.push("product_data");
      }
      logger.info(
        "Applied business rule: Best sellers query detected, using hybrid search"
      );
    }

    // 3. Comparison queries should always use reranking
    if (query.toLowerCase().match(/(compar|better|worse|versus|vs\.)/i)) {
      plan.useReranking = true;
      logger.info(
        "Applied business rule: Comparison query detected, forcing reranking"
      );
    }
  }

  /**
   * Creates a fallback plan when all else fails
   */
  private createFallbackPlan(query: string): QueryPlan {
    return {
      primaryMethod: "hybrid",
      fallbackMethods: ["vector_search", "keyword_search"],
      dataSources: ["pos_data", "product_data"],
      enhancedQuery: query + " sales products inventory information data",
      originalQuery: query,
      queryType: "general",
      useReranking: true,
      confidence: 0.5,
    };
  }

  /**
   * Decomposes a complex query into multiple simpler queries
   * This helps with retrieval of different aspects of the same question
   */
  async decomposeQuery(query: string): Promise<string[]> {
    try {
      // Only decompose longer, complex queries
      if (query.length < 50 || !query.includes(" and ")) {
        return [query];
      }

      const completion = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system",
            content: `You are a query decomposition system. Your job is to break down complex queries into simpler, individual queries that can be processed independently.

If the query is already simple, return it as a single item. Only decompose when the query has multiple distinct information needs.

Return a JSON array of decomposed queries.

Example 1:
Query: "What were our top-selling products in January and how much revenue did they generate?"
Result: ["What were our top-selling products in January?", "How much revenue did our top-selling products in January generate?"]

Example 2:
Query: "Show me sales performance for indica strains"
Result: ["Show me sales performance for indica strains"]`,
          },
          {
            role: "user",
            content: query,
          },
        ],
        temperature: 0.1,
        response_format: { type: "json_object" },
        max_tokens: 400,
      });

      const response = JSON.parse(
        completion.choices[0]?.message?.content || "{}"
      );

      if (
        response.queries &&
        Array.isArray(response.queries) &&
        response.queries.length > 0
      ) {
        logger.info(
          {
            original_query: query,
            decomposed_queries: response.queries,
          },
          "Query decomposed"
        );
        return response.queries;
      }

      return [query];
    } catch (error) {
      logger.error({ error, query }, "Error decomposing query");
      return [query];
    }
  }
}
