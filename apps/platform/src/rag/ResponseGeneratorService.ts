import { OpenAI } from "openai";
import { logger } from "../config/logger";
import { RetrievalResult } from "./MultiRetrievalService";

/**
 * Response Generator Service
 *
 * This service takes retrieval results and generates coherent, natural
 * language responses for the user. It combines multiple data sources
 * and integrates direct database results with contextual information.
 */
export class ResponseGeneratorService {
  private openai: OpenAI;
  private defaultModel: string = "gpt-4.1-mini";
  private defaultTemperature: number = 0.7;

  constructor(
    openai: OpenAI,
    options?: {
      defaultModel?: string;
      defaultTemperature?: number;
    }
  ) {
    this.openai = openai;

    if (options?.defaultModel) {
      this.defaultModel = options.defaultModel;
    }

    if (options?.defaultTemperature !== undefined) {
      this.defaultTemperature = options.defaultTemperature;
    }

    logger.info(
      {
        model: this.defaultModel,
        temperature: this.defaultTemperature,
      },
      "ResponseGeneratorService initialized"
    );
  }

  /**
   * Generate a response based on retrieval results
   */
  async generateResponse(
    query: string,
    results: RetrievalResult[],
    options?: {
      agentInfo?: {
        name: string;
        role: string;
        capabilities: string[];
      };
      additionalContext?: string;
      contextType?: string;
    }
  ): Promise<string> {
    try {
      if (results.length === 0) {
        return this.generateFallbackResponse(query);
      }

      // Format results for the LLM
      const formattedContext = this.formatRetrievalContext(results);

      // Create system prompt
      const systemPrompt = this.createSystemPrompt(options);

      // Add special handling for direct database results
      const hasDirectResults = results.some((r) => r.method.includes("direct"));

      // Add context about the types of data we're including
      const sourceTypes = [...new Set(results.map((r) => r.source))];
      const contextDescription = this.describeContext(
        sourceTypes,
        hasDirectResults
      );

      // Generate the response
      const completion = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system",
            content: systemPrompt,
          },
          {
            role: "user",
            content: `${contextDescription}\n\n${formattedContext}\n\nQuestion: ${query}`,
          },
        ],
        temperature: this.defaultTemperature,
      });

      const response =
        completion.choices[0]?.message?.content ||
        "I'm sorry, I couldn't generate a response based on the available information.";

      return response;
    } catch (error) {
      logger.error({ error, query }, "Error generating response");
      return this.generateFallbackResponse(query);
    }
  }

  /**
   * Format retrieval results into a context string for the LLM
   */
  private formatRetrievalContext(results: RetrievalResult[]): string {
    let context = "";

    // Group results by source for better organization
    const groupedResults = this.groupResultsBySource(results);

    // Format each group
    for (const [source, sourceResults] of Object.entries(groupedResults)) {
      // Add a section header for each source type
      context += `### ${this.formatSourceName(source)}\n\n`;

      // Add individual results
      sourceResults.forEach((result, index) => {
        context += `RESULT ${index + 1}:\n${result.content}\n\n`;
      });

      context += "\n";
    }

    return context;
  }

  /**
   * Group results by their source
   */
  private groupResultsBySource(
    results: RetrievalResult[]
  ): Record<string, RetrievalResult[]> {
    const grouped: Record<string, RetrievalResult[]> = {};

    for (const result of results) {
      if (!grouped[result.source]) {
        grouped[result.source] = [];
      }

      grouped[result.source].push(result);
    }

    return grouped;
  }

  /**
   * Format a source name for better readability
   */
  private formatSourceName(source: string): string {
    switch (source) {
      case "pos_data":
        return "Point of Sale Data";
      case "pos_data_direct":
        return "Point of Sale Data (Direct Database Results)";
      case "product_data":
        return "Product Information";
      case "review_data":
        return "Customer Reviews";
      case "competitor_data":
        return "Competitor Information";
      case "retailer_data":
        return "Retailer Details";
      case "customer_data":
        return "Customer Information";
      default:
        return source
          .split("_")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
    }
  }

  /**
   * Create a system prompt for the response generation
   */
  private createSystemPrompt(options?: {
    agentInfo?: {
      name: string;
      role: string;
      capabilities: string[];
    };
    additionalContext?: string;
    contextType?: string;
  }): string {
    // Base prompt
    let prompt = `You are an AI assistant for a cannabis dispensary. Your job is to answer questions accurately based on the provided context information.

Guidelines:
1. Always use ONLY the information provided in the context to formulate your answer
2. If the context doesn't contain all the information needed, acknowledge the limitations
3. When reporting exact numbers, use the figures from database results when available
4. For sales data, always specify the number of transactions processed when that information is available
5. Format currency values appropriately (e.g., $1,234.56)
6. Maintain a professional, helpful tone
7. Keep answers concise and directly relevant to the question
8. Do not make up information or speculate beyond what's in the context
9. When quoting statistics or data, always attribute the source
10. For cannabis products, include relevant THC/CBD percentages when available`;

    // Add agent-specific information if provided
    if (options?.agentInfo) {
      prompt += `\n\nYou are ${options.agentInfo.name}, ${
        options.agentInfo.role
      }. Your capabilities include: ${options.agentInfo.capabilities.join(
        ", "
      )}.`;
    }

    // Add any additional context
    if (options?.additionalContext) {
      prompt += `\n\n${options.additionalContext}`;
    }

    return prompt;
  }

  /**
   * Generate a description of the context we're providing
   */
  private describeContext(
    sourceTypes: string[],
    hasDirectResults: boolean
  ): string {
    let description = "I'm providing you with ";

    if (hasDirectResults) {
      description += "exact database results ";

      if (sourceTypes.length > 1) {
        description += "along with ";
      }
    }

    if (sourceTypes.length > 0) {
      const typeNames = sourceTypes.map((type) => this.formatSourceName(type));

      if (typeNames.length === 1) {
        description += typeNames[0];
      } else if (typeNames.length === 2) {
        description += `${typeNames[0]} and ${typeNames[1]}`;
      } else {
        const last = typeNames.pop();
        description += `${typeNames.join(", ")}, and ${last}`;
      }
    }

    description +=
      ". Please use this information to answer the question accurately and completely.";

    return description;
  }

  /**
   * Generate a fallback response when no retrieval results are available
   */
  private async generateFallbackResponse(query: string): Promise<string> {
    try {
      const completion = await this.openai.chat.completions.create({
        model: this.defaultModel,
        messages: [
          {
            role: "system",
            content: `You are an AI assistant for a cannabis dispensary. The user has asked a question, but we could not find specific data to answer it.

Your job is to:
1. Acknowledge that you don't have the specific data requested
2. Provide a helpful, generic response that's relevant to the question domain
3. Suggest what information the user might want to provide to get a better answer
4. Never make up specific data like sales figures, product details, or customer information
5. Keep your response concise and helpful`,
          },
          {
            role: "user",
            content: `Question: ${query}`,
          },
        ],
        temperature: 0.7,
      });

      return (
        completion.choices[0]?.message?.content ||
        "I don't have enough information to answer that question accurately. Could you provide more details or ask something else?"
      );
    } catch (error) {
      logger.error({ error, query }, "Error generating fallback response");
      return "I don't have enough information to answer that question accurately. Could you provide more details or ask something else?";
    }
  }
}
