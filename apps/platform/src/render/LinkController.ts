import Router from "@koa/router";
import App from "../app";
import { encodedLinkToParts, trackMessageEvent } from "./LinkService";
import Organization from "../organizations/Organization";
import { cacheGet, cacheSet } from "../config/redis";

/**
 * @swagger
 * tags:
 *   name: Links
 *   description: Link tracking and redirection endpoints
 */

const router = new Router<{
  app: App;
  organization?: Organization;
}>();

/**
 * @swagger
 * /c:
 *   get:
 *     summary: Track link clicks and redirect
 *     tags: [Links]
 *     parameters:
 *       - in: query
 *         name: r
 *         schema:
 *           type: string
 *         description: Encoded redirect URL
 *     responses:
 *       200:
 *         description: Default page when no redirect URL is provided
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *       303:
 *         description: Redirect to the target URL
 *         headers:
 *           Location:
 *             schema:
 *               type: string
 *             description: The URL to redirect to
 */
router.get("/c", async (ctx) => {
  // If no redirect, just show a default page
  if (!ctx.query.r) {
    ctx.body = "It looks like this link doesn't work properly!";
    ctx.status = 200;
    return;
  }

  const parts = await encodedLinkToParts(ctx.URL);
  await trackMessageEvent(parts, "clicked");
  ctx.redirect(parts.redirect);
  ctx.status = 303;
});

/**
 * @swagger
 * /o:
 *   get:
 *     summary: Track link opens
 *     tags: [Links]
 *     parameters:
 *       - in: query
 *         name: r
 *         schema:
 *           type: string
 *         description: Encoded redirect URL
 *     responses:
 *       204:
 *         description: Link open event tracked successfully
 */
router.get("/o", async (ctx) => {
  const parts = await encodedLinkToParts(ctx.URL);
  await trackMessageEvent(parts, "opened");
  ctx.status = 204;
});

/**
 * @swagger
 * /.well-known/{file}:
 *   get:
 *     summary: Get well-known file from organization's tracking deeplink mirror
 *     tags: [Links]
 *     parameters:
 *       - in: path
 *         name: file
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the well-known file to retrieve
 *     responses:
 *       200:
 *         description: Well-known file content
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       404:
 *         description: Organization not found or no tracking deeplink mirror URL configured
 */
router.get("/.well-known/:file", async (ctx) => {
  const organization = ctx.state.organization;
  const url = organization?.tracking_deeplink_mirror_url;
  const file = ctx.params.file;
  if (!url) {
    ctx.status = 404;
    return;
  }

  const key = `well-known:${organization.id}:${file}`;
  const value = await cacheGet<any>(App.main.redis, key);
  if (value) {
    ctx.body = value;
  } else {
    const response = await fetch(`${url}/.well-known/${file}`);
    const value = await response.json();
    await cacheSet(App.main.redis, key, value, 60 * 60 * 5);
    ctx.body = value;
  }
});

export default router;
