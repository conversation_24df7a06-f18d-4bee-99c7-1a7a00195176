import Router from "@koa/router";
import { LocationState } from "../auth/AuthMiddleware";
import { JSONSchemaType, validate } from "../core/validate";
import Resource, { ResourceParams, ResourceType } from "./Resource";
import {
  allResources,
  createResource,
  deleteResource,
  getResource,
} from "./ResourceService";

/**
 * @swagger
 * components:
 *   schemas:
 *     Resource:
 *       type: object
 *       required:
 *         - id
 *         - location_id
 *         - type
 *         - name
 *         - value
 *       properties:
 *         id:
 *           type: integer
 *           description: The unique identifier of the resource
 *         location_id:
 *           type: integer
 *           description: The ID of the location this resource belongs to
 *         type:
 *           type: string
 *           enum: [font, snippet]
 *           description: The type of resource
 *         name:
 *           type: string
 *           description: The name of the resource
 *         value:
 *           type: object
 *           description: The resource value object
 *           additionalProperties: true
 */

/**
 * @swagger
 * tags:
 *   name: Resources
 *   description: Resource management endpoints for fonts and snippets
 */

const router = new Router<LocationState & { resource?: Resource }>({
  prefix: "/resources",
});

/**
 * @swagger
 * /resources:
 *   get:
 *     summary: Get all resources for a location
 *     tags: [Resources]
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [font, snippet]
 *         description: Filter resources by type
 *     responses:
 *       200:
 *         description: List of resources
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Resource'
 */
router.get("/", async (ctx) => {
  const type = ctx.query.type as ResourceType;
  ctx.body = await allResources(ctx.state.location.id, type);
});

const resourceCreateParams: JSONSchemaType<ResourceParams> = {
  $id: "resourceCreateParams",
  type: "object",
  required: ["type", "name", "value"],
  properties: {
    type: {
      type: "string",
      enum: ["font", "snippet"],
    },
    name: { type: "string" },
    value: {
      type: "object",
      additionalProperties: true,
    } as any,
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /resources:
 *   post:
 *     summary: Create a new resource
 *     tags: [Resources]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - name
 *               - value
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [font, snippet]
 *                 description: The type of resource
 *               name:
 *                 type: string
 *                 description: The name of the resource
 *               value:
 *                 type: object
 *                 description: The resource value object
 *                 additionalProperties: true
 *     responses:
 *       200:
 *         description: The created resource
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Resource'
 */
router.post("/", async (ctx) => {
  const payload = validate(resourceCreateParams, ctx.request.body);
  ctx.body = await createResource(ctx.state.location.id, payload);
});

router.param("resourceId", async (value: string, ctx, next) => {
  ctx.state.resource = await getResource(
    parseInt(value, 10),
    ctx.state.location.id
  );
  if (!ctx.state.resource) {
    ctx.throw(404);
    return;
  }
  return await next();
});

/**
 * @swagger
 * /resources/{resourceId}:
 *   delete:
 *     summary: Delete a resource
 *     tags: [Resources]
 *     parameters:
 *       - in: path
 *         name: resourceId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the resource to delete
 *     responses:
 *       200:
 *         description: Resource successfully deleted
 *         content:
 *           application/json:
 *             schema:
 *               type: boolean
 *               example: true
 *       404:
 *         description: Resource not found
 */
router.delete("/:resourceId", async (ctx) => {
  const { id, location_id } = ctx.state.resource!;
  await deleteResource(id, location_id);
  ctx.body = true;
});

export default router;
