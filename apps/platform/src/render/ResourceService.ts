import Resource, { ResourceParams, ResourceType } from "./Resource";

export const allResources = async (
  locationId: number,
  type?: ResourceType
): Promise<Resource[]> => {
  return await Resource.all((qb) => {
    if (type) {
      qb.where("type", type);
    }
    return qb.where("location_id", locationId);
  });
};

export const getResource = async (id: number, locationId: number) => {
  return await Resource.find(id, (qb) => qb.where("location_id", locationId));
};

export const createResource = async (
  locationId: number,
  params: ResourceParams
) => {
  return await Resource.insertAndFetch({
    ...params,
    location_id: locationId,
  });
};

export const deleteResource = async (id: number, locationId: number) => {
  return await Resource.deleteById(id, (qb) =>
    qb.where("location_id", locationId)
  );
};
