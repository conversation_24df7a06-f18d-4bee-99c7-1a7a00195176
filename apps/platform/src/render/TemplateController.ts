import Router from "@koa/router";
import { LocationState } from "../auth/AuthMiddleware";
import { JSONSchemaType, validate } from "../core/validate";
import { searchParamsSchema } from "../core/searchParams";
import { extractQueryParams } from "../utilities";
import Template, { TemplateParams, TemplateUpdateParams } from "./Template";
import {
  createTemplate,
  deleteTemplate,
  getTemplate,
  pagedTemplates,
  sendProof,
  updateTemplate,
} from "./TemplateService";
import { Variables } from ".";
import { User } from "../users/User";
import { UserEvent } from "../users/UserEvent";

/**
 * @swagger
 * components:
 *   schemas:
 *     TemplateEmailData:
 *       type: object
 *       properties:
 *         from:
 *           type: object
 *           nullable: true
 *           properties:
 *             name:
 *               type: string
 *               nullable: true
 *             email:
 *               type: string
 *               nullable: true
 *         cc:
 *           type: string
 *           nullable: true
 *         bcc:
 *           type: string
 *           nullable: true
 *         reply_to:
 *           type: string
 *           nullable: true
 *         subject:
 *           type: string
 *           nullable: true
 *         text:
 *           type: string
 *           nullable: true
 *         html:
 *           type: string
 *           nullable: true
 *         mjml:
 *           type: string
 *           nullable: true
 *       nullable: true
 *     TemplateTextData:
 *       type: object
 *       properties:
 *         text:
 *           type: string
 *           nullable: true
 *       nullable: true
 *     TemplatePushData:
 *       type: object
 *       required:
 *         - title
 *         - body
 *       properties:
 *         title:
 *           type: string
 *         body:
 *           type: string
 *         custom:
 *           type: object
 *           nullable: true
 *           additionalProperties: true
 *       nullable: true
 *     TemplateWebhookData:
 *       type: object
 *       required:
 *         - method
 *         - endpoint
 *       properties:
 *         method:
 *           type: string
 *         endpoint:
 *           type: string
 *         body:
 *           type: object
 *           nullable: true
 *           additionalProperties: true
 *         headers:
 *           type: object
 *           nullable: true
 *           additionalProperties: true
 *       nullable: true
 *     Template:
 *       type: object
 *       required:
 *         - id
 *         - location_id
 *         - type
 *         - campaign_id
 *         - locale
 *       properties:
 *         id:
 *           type: integer
 *           description: The unique identifier of the template
 *         location_id:
 *           type: integer
 *           description: The ID of the location this template belongs to
 *         type:
 *           type: string
 *           enum: [email, text, push, webhook]
 *           description: The type of template
 *         campaign_id:
 *           type: integer
 *           description: The ID of the campaign this template belongs to
 *         locale:
 *           type: string
 *           description: The locale of the template
 *         data:
 *           oneOf:
 *             - $ref: '#/components/schemas/TemplateEmailData'
 *             - $ref: '#/components/schemas/TemplateTextData'
 *             - $ref: '#/components/schemas/TemplatePushData'
 *             - $ref: '#/components/schemas/TemplateWebhookData'
 */

/**
 * @swagger
 * tags:
 *   name: Templates
 *   description: Template management endpoints for email, text, push, and webhook templates
 */

const router = new Router<LocationState & { template?: Template }>({
  prefix: "/templates",
});

/**
 * @swagger
 * /templates:
 *   get:
 *     summary: Get paged list of templates
 *     tags: [Templates]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: List of templates
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 items:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Template'
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 */
router.get("/", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await pagedTemplates(params, ctx.state.location.id);
});

const templateDataEmailParams = {
  type: "object",
  properties: {
    from: {
      type: "object",
      nullable: true,
      properties: {
        name: {
          type: "string",
          nullable: true,
        },
        email: {
          type: "string",
          nullable: true,
        },
      },
    },
    cc: {
      type: "string",
      nullable: true,
    },
    bcc: {
      type: "string",
      nullable: true,
    },
    reply_to: {
      type: "string",
      nullable: true,
    },
    subject: {
      type: "string",
      nullable: true,
    },
    text: {
      type: "string",
      nullable: true,
    },
    html: {
      type: "string",
      nullable: true,
    },
    mjml: {
      type: "string",
      nullable: true,
    },
  },
  nullable: true,
};

const templateDataTextParams = {
  type: "object",
  properties: {
    text: {
      type: "string",
      nullable: true,
    },
  },
  nullable: true,
};

const templateDataPushParams = {
  type: "object",
  required: ["title", "body"],
  properties: {
    title: { type: "string" },
    body: { type: "string" },
    custom: {
      type: "object",
      nullable: true,
      additionalProperties: true,
    },
  },
  nullable: true,
};

const templateDataWebhookParams = {
  type: "object",
  required: ["method", "endpoint"],
  properties: {
    method: { type: "string" },
    endpoint: { type: "string" },
    body: {
      type: "object",
      nullable: true,
      additionalProperties: true,
    },
    headers: {
      type: "object",
      nullable: true,
      additionalProperties: true,
    },
  },
  nullable: true,
};

const templateCreateParams: JSONSchemaType<TemplateParams> = {
  $id: "templateCreateParams",
  oneOf: [
    {
      type: "object",
      required: ["type", "campaign_id", "locale"],
      properties: {
        type: {
          type: "string",
          enum: ["email"],
        },
        campaign_id: {
          type: "integer",
        },
        locale: {
          type: "string",
        },
        data: templateDataEmailParams as any,
      },
      additionalProperties: false,
    },
    {
      type: "object",
      required: ["type", "campaign_id", "locale"],
      properties: {
        type: {
          type: "string",
          enum: ["text"],
        },
        campaign_id: {
          type: "integer",
        },
        locale: {
          type: "string",
        },
        data: templateDataTextParams as any,
      },
      additionalProperties: false,
    },
    {
      type: "object",
      required: ["type", "campaign_id", "locale"],
      properties: {
        type: {
          type: "string",
          enum: ["push"],
        },
        campaign_id: {
          type: "integer",
        },
        locale: {
          type: "string",
        },
        data: templateDataPushParams as any,
      },
      additionalProperties: false,
    },
    {
      type: "object",
      required: ["type", "campaign_id", "locale"],
      properties: {
        type: {
          type: "string",
          enum: ["webhook"],
        },
        campaign_id: {
          type: "integer",
        },
        locale: {
          type: "string",
        },
        data: templateDataWebhookParams as any,
      },
      additionalProperties: false,
    },
  ],
};

/**
 * @swagger
 * /templates:
 *   post:
 *     summary: Create a new template
 *     tags: [Templates]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - type: object
 *                 required:
 *                   - type
 *                   - campaign_id
 *                   - locale
 *                 properties:
 *                   type:
 *                     type: string
 *                     enum: [email]
 *                   campaign_id:
 *                     type: integer
 *                   locale:
 *                     type: string
 *                   data:
 *                     $ref: '#/components/schemas/TemplateEmailData'
 *               - type: object
 *                 required:
 *                   - type
 *                   - campaign_id
 *                   - locale
 *                 properties:
 *                   type:
 *                     type: string
 *                     enum: [text]
 *                   campaign_id:
 *                     type: integer
 *                   locale:
 *                     type: string
 *                   data:
 *                     $ref: '#/components/schemas/TemplateTextData'
 *               - type: object
 *                 required:
 *                   - type
 *                   - campaign_id
 *                   - locale
 *                 properties:
 *                   type:
 *                     type: string
 *                     enum: [push]
 *                   campaign_id:
 *                     type: integer
 *                   locale:
 *                     type: string
 *                   data:
 *                     $ref: '#/components/schemas/TemplatePushData'
 *               - type: object
 *                 required:
 *                   - type
 *                   - campaign_id
 *                   - locale
 *                 properties:
 *                   type:
 *                     type: string
 *                     enum: [webhook]
 *                   campaign_id:
 *                     type: integer
 *                   locale:
 *                     type: string
 *                   data:
 *                     $ref: '#/components/schemas/TemplateWebhookData'
 *     responses:
 *       200:
 *         description: The created template
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Template'
 */
router.post("/", async (ctx) => {
  const payload = validate(templateCreateParams, ctx.request.body);
  ctx.body = await createTemplate(ctx.state.location.id, payload);
});

router.param("templateId", async (value, ctx, next) => {
  ctx.state.template = await getTemplate(
    parseInt(value),
    ctx.state.location.id
  );
  if (!ctx.state.template) {
    ctx.throw(404);
    return;
  }
  return await next();
});

/**
 * @swagger
 * /templates/{templateId}:
 *   get:
 *     summary: Get a specific template
 *     tags: [Templates]
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the template to retrieve
 *     responses:
 *       200:
 *         description: The requested template
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Template'
 *       404:
 *         description: Template not found
 */
router.get("/:templateId", async (ctx) => {
  ctx.body = ctx.state.template;
});

const templateUpdateParams: JSONSchemaType<TemplateUpdateParams> = {
  $id: "templateUpdateParams",
  oneOf: [
    {
      type: "object",
      required: ["type", "data"],
      properties: {
        type: {
          type: "string",
          enum: ["email"],
        },
        data: templateDataEmailParams as any,
      },
      additionalProperties: false,
    },
    {
      type: "object",
      required: ["type", "data"],
      properties: {
        type: {
          type: "string",
          enum: ["text"],
        },
        data: templateDataTextParams as any,
      },
      additionalProperties: false,
    },
    {
      type: "object",
      required: ["type", "data"],
      properties: {
        type: {
          type: "string",
          enum: ["push"],
        },
        data: templateDataPushParams as any,
      },
      additionalProperties: false,
    },
    {
      type: "object",
      required: ["type", "data"],
      properties: {
        type: {
          type: "string",
          enum: ["webhook"],
        },
        data: templateDataWebhookParams as any,
      },
      additionalProperties: false,
    },
  ],
};

/**
 * @swagger
 * /templates/{templateId}:
 *   patch:
 *     summary: Update a template
 *     tags: [Templates]
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the template to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - type: object
 *                 required:
 *                   - type
 *                   - data
 *                 properties:
 *                   type:
 *                     type: string
 *                     enum: [email]
 *                   data:
 *                     $ref: '#/components/schemas/TemplateEmailData'
 *               - type: object
 *                 required:
 *                   - type
 *                   - data
 *                 properties:
 *                   type:
 *                     type: string
 *                     enum: [text]
 *                   data:
 *                     $ref: '#/components/schemas/TemplateTextData'
 *               - type: object
 *                 required:
 *                   - type
 *                   - data
 *                 properties:
 *                   type:
 *                     type: string
 *                     enum: [push]
 *                   data:
 *                     $ref: '#/components/schemas/TemplatePushData'
 *               - type: object
 *                 required:
 *                   - type
 *                   - data
 *                 properties:
 *                   type:
 *                     type: string
 *                     enum: [webhook]
 *                   data:
 *                     $ref: '#/components/schemas/TemplateWebhookData'
 *     responses:
 *       200:
 *         description: The updated template
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Template'
 *       404:
 *         description: Template not found
 */
router.patch("/:templateId", async (ctx) => {
  const payload = validate(templateUpdateParams, ctx.request.body);
  ctx.body = await updateTemplate(ctx.state.template!.id, payload);
});

/**
 * @swagger
 * /templates/{templateId}:
 *   delete:
 *     summary: Delete a template
 *     tags: [Templates]
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the template to delete
 *     responses:
 *       200:
 *         description: Template successfully deleted
 *         content:
 *           application/json:
 *             schema:
 *               type: boolean
 *               example: true
 *       404:
 *         description: Template not found
 */
router.delete("/:templateId", async (ctx) => {
  const template = ctx.state.template!;
  ctx.body = await deleteTemplate(template.id, template.location_id);
});

/**
 * @swagger
 * /templates/{templateId}/preview:
 *   post:
 *     summary: Preview a template with variables
 *     tags: [Templates]
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the template to preview
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user:
 *                 type: object
 *                 description: User data for template variables
 *               event:
 *                 type: object
 *                 description: Event data for template variables
 *               context:
 *                 type: object
 *                 description: Additional context data for template variables
 *     responses:
 *       200:
 *         description: The compiled template preview
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               additionalProperties: true
 *       404:
 *         description: Template not found
 */
router.post("/:templateId/preview", async (ctx) => {
  const payload = ctx.request.body as Variables;
  const template = ctx.state.template!.map();

  ctx.body = template.compile({
    user: User.fromJson({ ...payload.user, data: payload.user }),
    event: UserEvent.fromJson(payload.event || {}),
    context: payload.context || {},
    location: ctx.state.location,
  });
});

interface TemplateProofParams {
  variables: any;
  recipient: string;
}

const templateProofParams: JSONSchemaType<TemplateProofParams> = {
  $id: "templateProof",
  type: "object",
  required: ["recipient"],
  properties: {
    variables: {
      type: "object",
      nullable: true,
      additionalProperties: true,
    },
    recipient: { type: "string" },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /templates/{templateId}/proof:
 *   post:
 *     summary: Send a proof of the template
 *     tags: [Templates]
 *     parameters:
 *       - in: path
 *         name: templateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the template to send proof for
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - recipient
 *             properties:
 *               variables:
 *                 type: object
 *                 nullable: true
 *                 additionalProperties: true
 *                 description: Variables to use in the template
 *               recipient:
 *                 type: string
 *                 description: Email address to send the proof to
 *     responses:
 *       200:
 *         description: Proof sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               additionalProperties: true
 *       404:
 *         description: Template not found
 */
router.post("/:templateId/proof", async (ctx) => {
  const { variables, recipient } = validate(
    templateProofParams,
    ctx.request.body
  );
  const template = ctx.state.template!.map();

  ctx.body = await sendProof(template, variables, recipient);
});

export default router;
