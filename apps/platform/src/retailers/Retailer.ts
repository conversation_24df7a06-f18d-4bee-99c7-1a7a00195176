import Model from "../core/Model";

export class Retailer extends Model {
  retailer_id!: string;
  name!: string;
  slug?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  phone?: string;
  email?: string;
  website_url?: string;
  latitude?: string;
  longitude?: string;
  rating?: number;
  reviews_count?: number;
  description?: string;
  hours?: string;
  is_open?: boolean;
  license_type?: string;
  serves_medical_users?: boolean;
  serves_recreational_users?: boolean;
  services?: string[];
  online_ordering?: string;
  avatar?: string;
  is_active?: boolean;
  data?: Record<string, unknown>;

  static jsonAttributes = ["data", "services"];
}

export type RetailerParams = Omit<Retailer, keyof Model>;
