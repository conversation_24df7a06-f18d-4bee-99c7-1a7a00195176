/* eslint-disable indent */
import Router from "@koa/router";
import { locationRoleMiddleware } from "../locations/LocationService";
import {
  importFromFile,
  importFromAPI,
  importFromNormalizedData,
} from "./RetailerDataImportService";
import { Retailer } from "./Retailer";
import parse from "../storage/FileStream";
import { extractQueryParams } from "../utilities";
import { searchParamsSchema } from "../core/searchParams";
import { RetailerDataVectorService } from "./RetailerDataVectorService";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";
import { logger } from "../config/logger";
import { DataNormalizationService } from "../core/DataNormalizationService";

const router = new Router({
  prefix: "/retailers",
});

/**
 * @swagger
 * tags:
 *   name: RetailerData
 *   description: Retailer data management endpoints
 */

/**
 * @swagger
 * /retailers/import:
 *   post:
 *     summary: Import Retailer Data
 *     description: Imports retailer data from file, API, or direct JSON.
 *     tags: [RetailerData]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Retailer data imported successfully
 *       400:
 *         description: Failed to import retailer data
 */
router.post("/import", locationRoleMiddleware("editor"), async (ctx) => {
  // Get reindex flag from query params or body
  const shouldReindex =
    ctx.request.query.reindex === "true" || ctx.request.body?.reindex === true;

  // Check if this is a JSON data import
  if (ctx.request.body?.retailer_data) {
    try {
      // Normalize the data first
      const normalizedData = DataNormalizationService.normalizeRetailerData(
        ctx.request.body.retailer_data
      );

      // Import the normalized data
      const result = await importFromNormalizedData(
        normalizedData,
        undefined,
        shouldReindex
      );

      ctx.status = 200;
      ctx.body = {
        message: "Retailer data imported successfully",
        status: "processing",
        reindexed: shouldReindex,
        ...result,
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        error:
          error instanceof Error
            ? error.message
            : "Failed to import retailer data",
      };
    }
    return;
  }

  // Check if this is an API integration request
  if (ctx.request.body?.data_source) {
    try {
      const result = await importFromAPI(
        ctx.request.body.data_source,
        shouldReindex
      );

      ctx.status = 200;
      ctx.body = {
        message: "Retailer data integration import started",
        status: "processing",
        reindexed: shouldReindex,
        ...result,
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        error:
          error instanceof Error
            ? error.message
            : "Failed to import retailer data from integration",
      };
    }
    return;
  }

  // Handle file upload
  try {
    const stream = await parse(ctx);

    // Import file data
    const result = await importFromFile(stream, undefined, shouldReindex);

    ctx.status = 200;
    ctx.body = {
      message: "File format validated. Import started in background.",
      status: "processing",
      reindexed: shouldReindex,
      ...result,
    };
  } catch (error) {
    ctx.status = 400;
    ctx.body = {
      error:
        error instanceof Error
          ? error.message
          : "Failed to import retailer data",
    };
  }
});

/**
 * @swagger
 * /retailers/data:
 *   get:
 *     summary: List Retailer Data
 *     description: Retrieves a paginated list of retailer data for the current location
 *     tags: [RetailerData]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort direction
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/RetailerData'
 *                 total:
 *                   type: integer
 *                 page:
 *                   type: integer
 *                 limit:
 *                   type: integer
 *       500:
 *         description: Failed to retrieve retailer data
 */
router.get("/data", locationRoleMiddleware("support"), async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);

  const query = Retailer.query().orderBy(
    params.sort || "name",
    params.direction || "asc"
  );

  if (params.cursor) {
    const operator = params.page === "prev" ? "<" : ">";
    query.where("id", operator, params.cursor);
  }

  const limit = params.limit || 25;
  const results = await query.limit(limit + 1);

  const hasMore = results.length > limit;
  if (hasMore) {
    results.pop(); // Remove the extra item we fetched
  }

  ctx.body = {
    results,
    nextCursor: hasMore ? results[results.length - 1].id : undefined,
    prevCursor: params.cursor,
    limit,
  };
});

/**
 * @swagger
 * /retailers/search:
 *   post:
 *     summary: Search Retailer Data
 *     description: Searches retailer data using vector search.
 *     tags: [RetailerData]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               query:
 *                 type: string
 *               filters:
 *                 type: object
 *               limit:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       400:
 *         description: Search query is required
 *       500:
 *         description: Error searching retailer data
 */
router.post("/search", locationRoleMiddleware("support"), async (ctx) => {
  const { query, filters = {}, limit = 10 } = ctx.request.body;

  if (!query) {
    ctx.status = 400;
    ctx.body = {
      error: "Search query is required",
    };
    return;
  }

  try {
    const results = await RetailerDataVectorService.queryRetailerData(
      query,
      filters,
      limit
    );

    ctx.status = 200;
    ctx.body = {
      results,
      query,
      filters,
      limit,
    };
  } catch (error) {
    logger.error("Error searching retailer data:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Error searching retailer data",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /retailers/data/re-vectorize:
 *   post:
 *     summary: Re-vectorize Retailer Data
 *     description: Triggers re-vectorization of retailer data
 *     tags: [RetailerData]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               batch_size:
 *                 type: integer
 *                 description: Number of records to process in each batch
 *               clean_start:
 *                 type: boolean
 *                 description: Whether to delete existing vectors before re-vectorization
 *     responses:
 *       200:
 *         description: Re-vectorization started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 status:
 *                   type: string
 *                 job_id:
 *                   type: string
 *                 job_type:
 *                   type: string
 *                 clean_start:
 *                   type: boolean
 *       500:
 *         description: Internal server error
 */
router.post("/re-vectorize", locationRoleMiddleware("admin"), async (ctx) => {
  const { batch_size = 100, clean_start = true } = ctx.request.body;

  try {
    // First ensure the index exists
    await RetailerDataVectorService.ensureIndexExists();

    // If clean_start is true, delete all existing vectors for retailer data
    // Since retailers are global (not location-specific), this will delete all retailer vectors
    if (clean_start) {
      logger.info(
        "Deleting all existing retailer vectors before re-vectorization"
      );
      await RetailerDataVectorService.emptyIndex();
      logger.info("Successfully deleted all existing retailer vectors");
    }

    // Start a job tracker for this process
    const jobType = "retailer_data_vectorization";
    const tracker = OnboardingJobTracker.startJob(0, jobType);

    // Import RetailerDataVectorJob and ensure it's properly queued
    const RetailerDataVectorJob = require("./RetailerDataVectorJob").default;

    // Create a new job with proper parameters
    const job = RetailerDataVectorJob.from({
      batch_size,
      last_processed_id: 0, // Start from the beginning
      retry_count: 0,
      failed_ids: [],
    });

    // Queue the job
    await job.queue();

    logger.info("Re-vectorization job queued for retailer data");

    ctx.status = 200;
    ctx.body = {
      message: "Retailer data re-vectorization started",
      status: "processing",
      job_id: tracker.locationId,
      job_type: tracker.jobType,
      clean_start,
    };
  } catch (error) {
    logger.error("Error starting retailer re-vectorization:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to start re-vectorization",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /retailers/data/vectorization-status:
 *   get:
 *     summary: Get Vectorization Status
 *     description: Retrieves the status of retailer data vectorization
 *     tags: [RetailerData]
 *     responses:
 *       200:
 *         description: Vectorization status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isProcessing:
 *                   type: boolean
 *                 total:
 *                   type: integer
 *                 completed:
 *                   type: integer
 *                 failed:
 *                   type: integer
 *                 pending:
 *                   type: integer
 *                 processing:
 *                   type: integer
 *                 jobs:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       jobType:
 *                         type: string
 *                       status:
 *                         type: string
 *                       startedAt:
 *                         type: string
 *                         format: date-time
 *                       completedAt:
 *                         type: string
 *                         format: date-time
 *                       error:
 *                         type: string
 *       500:
 *         description: Internal server error
 */
router.get(
  "/vectorization-status",
  locationRoleMiddleware("support"),
  async (ctx) => {
    try {
      const jobType = "retailer_data_vectorization";
      const summary = OnboardingJobTracker.getSummary(0);

      // Get current vectorization statistics
      const countResult = await Retailer.query().count("id as total").first();

      const total = countResult ? parseInt(countResult.total as string, 10) : 0;

      // Check vectorization status
      let status = summary.isProcessing
        ? "processing"
        : summary.failed > 0
        ? "failed"
        : summary.completed > 0
        ? "completed"
        : "not_started";

      // If status is not_started but we have data, try to verify if it's actually indexed
      if (status === "not_started" && total > 0) {
        try {
          // Check if index exists and has vectors
          await RetailerDataVectorService.ensureIndexExists();

          // Try a simple query to see if vectors exist
          const testResult = await RetailerDataVectorService.queryRetailerData(
            "test query to verify vectors exist",
            {},
            1 // Just need one result to confirm vectors exist
          );

          // If we got any results, vectors exist
          if (testResult && testResult.length > 0) {
            status = "completed";
            logger.info(
              `Detected existing vectors for retailers despite no job record`
            );
          }
        } catch (checkError) {
          // If this fails, just use the original status
          logger.warn(`Error checking vector existence: ${checkError}`);
        }
      }

      ctx.body = {
        status,
        job_summary: summary,
        data_count: total,
      };
    } catch (error) {
      logger.error(`Error getting vectorization status for retailers:`, error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to get vectorization status",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

export default router;
