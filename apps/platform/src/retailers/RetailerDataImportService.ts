import { FileStream } from "../storage/FileStream";
import { RequestError } from "../core/errors";
import { RetailerParams, Retailer } from "./Retailer";
import {
  DataNormalizationService,
  NormalizedData,
} from "../core/DataNormalizationService";
import { logger } from "../config/logger";
import RetailerDataVectorJob from "./RetailerDataVectorJob";

/**
 * Validates and normalizes retailer data from an uploaded file stream
 * @param stream The uploaded file stream
 * @returns The normalized data ready for import
 */
export const validateRetailerFileData = async (
  stream: FileStream
): Promise<NormalizedData> => {
  if (!stream) {
    throw new RequestError("No file stream provided");
  }

  // Normalize the data from the stream
  const normalizedData = await DataNormalizationService.normalizeData(stream);

  // Verify this is retailer data
  if (normalizedData.type !== "retailer") {
    throw new RequestError(
      "The uploaded file is not recognized as retailer data. Please use the appropriate import endpoint."
    );
  }

  // Report any errors found during normalization
  if (normalizedData.errors.length > 0) {
    const errorMessages = normalizedData.errors.map(
      (err) => `Row ${err.row}: ${err.error}`
    );
    throw new RequestError(
      `Data validation errors:\n${errorMessages.join("\n")}`
    );
  }

  return normalizedData;
};

/**
 * Imports retailer data from a normalized data structure
 * @param normalizedData Previously normalized retailer data
 * @param onProgress Optional callback for progress updates
 * @param reindex Whether to reindex the data in the vector database
 * @returns Result of the import operation
 */
export const importFromNormalizedData = async (
  normalizedData: NormalizedData,
  onProgress?: (count: number) => void,
  reindex: boolean = true
): Promise<{ processed: number; errors: any[] }> => {
  if (normalizedData.type !== "retailer") {
    throw new RequestError("Invalid data type. Expected retailer data.");
  }

  try {
    // Process the data rows
    let rowCount = 0;
    const errors: any[] = [];

    for (const row of normalizedData.data) {
      rowCount++;
      const retailerRow = row as Partial<RetailerParams>;

      try {
        // Ensure required fields are present
        if (!retailerRow.retailer_id || !retailerRow.name) {
          errors.push({
            row: rowCount,
            error: "Missing required fields: retailer_id and name are required",
          });
          continue;
        }

        // Check if retailer already exists
        const existingRetailer = await Retailer.first((qb) =>
          qb.where({ retailer_id: retailerRow.retailer_id })
        );

        if (existingRetailer) {
          // Update existing retailer
          await Retailer.update(
            (qb) => qb.where({ id: existingRetailer.id }),
            retailerRow
          );
          logger.info(
            `Updated existing retailer ${existingRetailer.id} (${retailerRow.retailer_id})`
          );
        } else {
          // Insert new retailer
          await Retailer.insert(retailerRow);
          logger.info(
            `Created new retailer with retailer_id: ${retailerRow.retailer_id}`
          );
        }

        if (onProgress && rowCount % 100 === 0) {
          onProgress(rowCount);
        }
      } catch (error) {
        logger.error(`Error processing retailer row ${rowCount}:`, error);
        errors.push({
          row: rowCount,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    if (onProgress) {
      onProgress(rowCount);
    }

    // Start the vector processing job after successful import if reindex is true
    if (reindex) {
      try {
        await RetailerDataVectorJob.from({
          batch_size: 100,
        }).queue();

        logger.info(
          `Queued retailer data vectorization job with ${rowCount} records`
        );
      } catch (vectorError) {
        logger.error(`Failed to queue retailer vector job:`, vectorError);
        // Continue - don't fail the import if just the vector job fails
      }
    } else {
      logger.info(`Skipping vectorization as reindex=false was specified`);
    }

    return {
      processed: rowCount,
      errors,
    };
  } catch (error) {
    logger.error("Error in retailer data import:", error);
    throw error;
  }
};

/**
 * Imports retailer data from a file upload
 * @param stream The file upload stream to process
 * @param onProgress Optional callback for progress updates
 * @param reindex Whether to reindex the data in the vector database
 * @returns Result of the import operation
 */
export const importFromFile = async (
  stream: FileStream,
  onProgress?: (count: number) => void,
  reindex: boolean = true
): Promise<{ processed: number; errors: any[] }> => {
  if (!stream) {
    throw new RequestError("No file stream provided");
  }

  try {
    // First validate and normalize the data
    const processedData = await validateRetailerFileData(stream);
    console.log(
      `Normalization complete. Found ${processedData.data.length} valid retailer records`
    );

    // Process the normalized data
    return await importFromNormalizedData(processedData, onProgress, reindex);
  } catch (error) {
    logger.error("Error in retailer data import:", error);
    throw error;
  }
};

/**
 * Imports retailer data from API or other external source
 * @param source The source configuration
 * @param reindex Whether to reindex the data in the vector database
 * @returns Result of the import operation
 */
export const importFromAPI = async (
  source: {
    type: string;
    config: any;
  },
  reindex: boolean = true
): Promise<{ processed: number; errors: any[] }> => {
  try {
    let retailerData: any;

    // Implement API-specific import logic based on source type
    switch (source.type) {
      case "weedmaps":
        retailerData = await fetchFromWeedmaps(source.config);
        break;
      case "dutchie":
        retailerData = await fetchFromDutchie(source.config);
        break;
      case "leafly":
        retailerData = await fetchFromLeafly(source.config);
        break;
      default:
        throw new RequestError(
          `Unsupported retailer data source: ${source.type}`
        );
    }

    // Use DataNormalizationService to normalize the API data
    const normalizedData =
      DataNormalizationService.normalizeRetailerData(retailerData);

    // Import the normalized data
    return await importFromNormalizedData(normalizedData, undefined, reindex);
  } catch (error) {
    logger.error("Error importing retailer data from API:", error);
    throw error;
  }
};

// Example API fetch functions
async function fetchFromWeedmaps(config: any): Promise<any> {
  // Implementation for Weedmaps API
  throw new Error("Weedmaps API integration not implemented yet");
}

async function fetchFromDutchie(config: any): Promise<any> {
  // Implementation for Dutchie API
  throw new Error("Dutchie API integration not implemented yet");
}

async function fetchFromLeafly(config: any): Promise<any> {
  // Implementation for Leafly API
  throw new Error("Leafly API integration not implemented yet");
}
