import { Retailer } from "./Retailer";
import { VectorService, VectorData } from "../core/VectorService";
import { logger } from "../config/logger";

const RETAILER_INDEX = "retailer-embeddings";

export class RetailerDataVectorService {
  private static vectorService: VectorService | null = null;

  /**
   * Get the vector service instance
   */
  private static getVectorService(): VectorService {
    if (!this.vectorService) {
      this.vectorService = VectorService.getInstance();
    }
    return this.vectorService;
  }

  static async initialize() {
    await this.getVectorService().initialize();
  }

  static async upsertRetailerData(retailers: Retailer[]) {
    try {
      if (!retailers.length) {
        logger.warn(
          "No retailer data provided to upsert, skipping vectorization"
        );
        return { successCount: 0, errorCount: 0, failedIds: [] };
      }

      // Convert retailer data to vector format with enhanced text representation
      const vectorData: VectorData[] = retailers.map((record) => ({
        id: `retailer_${record.id}`,
        text: this.generateEnhancedText(record),
        metadata: {
          location_id: 0, // Default location ID for global data
          source_type: "retailer",
          source_id: record.id.toString(),
          retailer_id: record.retailer_id,
          name: record.name,
          slug: record.slug,
          address: record.address,
          city: record.city,
          state: record.state,
          zip_code: record.zip_code,
          country: record.country,
          phone: record.phone,
          email: record.email,
          website_url: record.website_url,
          latitude: record.latitude,
          longitude: record.longitude,
          rating: record.rating,
          reviews_count: record.reviews_count,
          description: record.description
            ? record.description.substring(0, 250)
            : "", // Limit size for metadata
          is_active: record.is_active,
          serves_medical_users: record.serves_medical_users,
          serves_recreational_users: record.serves_recreational_users,
          created_at: Date.now(),
          updated_at: Date.now(),
        },
      }));

      // Use centralized vector service to upsert data without namespace
      // This keeps data accessible to all locations
      const result = await this.getVectorService().upsertVectors(
        RETAILER_INDEX,
        vectorData
      );

      logger.info(
        `Retailer data vectorization complete: ${result.successCount} succeeded, ${result.errorCount} failed`
      );
      return result;
    } catch (error) {
      logger.error("Error upserting retailer data vectors:", error);
      throw error;
    }
  }

  static async queryRetailerData(
    query: string,
    filters: { [key: string]: any } = {},
    topK: number = 10
  ) {
    try {
      if (!query) {
        throw new Error("Empty query provided to retailer data vector search");
      }

      // Add pre-processing for query to improve search results
      const enhancedQuery = this.enhanceSearchQuery(query);

      return await this.getVectorService().queryVectors(
        RETAILER_INDEX,
        enhancedQuery,
        filters,
        topK
      );
    } catch (error) {
      logger.error("Error querying retailer data vectors:", error);
      throw error;
    }
  }

  /**
   * Creates an enhanced text representation of retailer data for better semantic matching
   */
  private static generateEnhancedText(record: Retailer): string {
    // Base text with core information
    let text =
      `Retailer: ${record.name}, ID: ${record.retailer_id}, ` +
      `Location: ${record.address || ""}, ${record.city || ""}, ${
        record.state || ""
      }, ${record.zip_code || ""}, ${record.country || ""}`;

    // Add description if available
    if (record.description) {
      text += `. Description: ${record.description}`;
    }

    // Add services information
    if (record.serves_medical_users || record.serves_recreational_users) {
      const services = [];
      if (record.serves_medical_users) services.push("Medical");
      if (record.serves_recreational_users) services.push("Recreational");
      text += `. Services: ${services.join(" and ")}`;
    }

    // Add rating information if available
    if (record.rating) {
      text += `. Rated ${record.rating}/5 based on ${
        record.reviews_count || 0
      } reviews`;
    }

    // Add hours information if available
    if (record.hours) {
      text += `. Hours: ${record.hours}`;
    }

    // Add contact information
    const contacts = [];
    if (record.phone) contacts.push(`Phone: ${record.phone}`);
    if (record.email) contacts.push(`Email: ${record.email}`);
    if (record.website_url) contacts.push(`Website: ${record.website_url}`);

    if (contacts.length > 0) {
      text += `. Contact information: ${contacts.join(", ")}`;
    }

    return text;
  }

  /**
   * Enhances search queries for better retailer data retrieval
   */
  private static enhanceSearchQuery(query: string): string {
    // Convert simple retailer queries to more specific search terms
    let enhancedQuery = query;

    // Keywords for common retailer queries
    if (
      query.toLowerCase().includes("dispensary") ||
      query.toLowerCase().includes("store") ||
      query.toLowerCase().includes("shop")
    ) {
      enhancedQuery += " retailer cannabis marijuana";
    }

    if (
      query.toLowerCase().includes("medical") ||
      query.toLowerCase().includes("recreational")
    ) {
      enhancedQuery += " cannabis marijuana dispensary services";
    }

    if (
      query.toLowerCase().includes("near") ||
      query.toLowerCase().includes("close") ||
      query.toLowerCase().includes("nearby")
    ) {
      enhancedQuery += " location address city state";
    }

    return enhancedQuery;
  }

  /**
   * Validates that the Pinecone index exists and creates it if needed
   */
  static async ensureIndexExists() {
    try {
      await this.getVectorService().initialize();

      // Attempt to create the index if it doesn't exist
      await this.getVectorService().createIndex(RETAILER_INDEX, {
        dimension: 3072, // Updated dimension for text-embedding-3-large model (was 1536)
        metric: "cosine",
        serverless: {
          cloud: "aws", // Specify AWS instead of GCP
          region: "us-east-1", // Specify the us-east-1 region as required for free tier
        },
      });

      logger.info(`Retailer index '${RETAILER_INDEX}' verification complete`);
      return true;
    } catch (error) {
      logger.error(`Failed to verify retailer index: ${error}`);
      throw error;
    }
  }

  static async deleteRetailerData(retailerId?: string) {
    try {
      const filter = retailerId ? { retailer_id: retailerId } : {};
      return await this.getVectorService().deleteVectors(
        RETAILER_INDEX,
        filter
      );
    } catch (error) {
      logger.error("Error deleting retailer data vectors:", error);
      throw error;
    }
  }

  /**
   * Empties the entire retailer index, removing all vectors
   * Use with caution as this will delete ALL retailer data vectors
   */
  static async emptyIndex() {
    try {
      logger.warn(
        "Emptying entire retailer index - this will delete ALL retailer vectors"
      );
      return await this.getVectorService().emptyIndex(RETAILER_INDEX);
    } catch (error) {
      logger.error("Error emptying retailer index:", error);
      throw error;
    }
  }

  /**
   * Gets statistics about the retailer index
   */
  static async getIndexStats() {
    try {
      const stats = await this.getVectorService().getIndexStats(RETAILER_INDEX);
      return {
        totalVectorCount: stats.totalVectorCount || 0,
        namespaces: stats.namespaces || {},
      };
    } catch (error) {
      logger.error("Error getting retailer index stats:", error);
      return {
        totalVectorCount: 0,
        namespaces: {},
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
}
