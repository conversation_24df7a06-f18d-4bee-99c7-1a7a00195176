/* eslint-disable indent */
import Router from "@koa/router";
import { locationRoleMiddleware } from "../locations/LocationService";
import {
  importFromFile,
  importFromProductData,
  importFromNormalizedData,
} from "./ReviewDataImportService";
import { Review } from "./Review";
import parse from "../storage/FileStream";
import { extractQueryParams } from "../utilities";
import { searchParamsSchema } from "../core/searchParams";
import { ReviewDataVectorService } from "./ReviewDataVectorService";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";
import { logger } from "../config/logger";
import {
  DataNormalizationService,
  NormalizedData,
} from "../core/DataNormalizationService";

const router = new Router({
  prefix: "/reviews",
});

/**
 * @swagger
 * tags:
 *   name: Review
 *   description: Review data management endpoints
 */

/**
 * @swagger
 * /reviews/import:
 *   post:
 *     summary: Import Review Data
 *     description: Imports review data from file, product data, or direct JSON.
 *     tags: [ReviewData]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: Review data imported successfully
 *       400:
 *         description: Failed to import review data
 */
router.post("/import", locationRoleMiddleware("editor"), async (ctx) => {
  // Get reindex flag from query params or body
  const shouldReindex =
    ctx.request.query.reindex === "true" || ctx.request.body?.reindex === true;

  // Check if this is a direct JSON data import
  if (ctx.request.body?.review_data) {
    try {
      // Create normalized data directly
      const normalizedData: NormalizedData = {
        type: "review",
        data: Array.isArray(ctx.request.body.review_data)
          ? ctx.request.body.review_data
          : [ctx.request.body.review_data],
        errors: [],
      };

      // Import the normalized data
      const result = await importFromNormalizedData(
        normalizedData,
        undefined,
        shouldReindex
      );

      ctx.status = 200;
      ctx.body = {
        message: "Review data imported successfully",
        status: "processing",
        reindexed: shouldReindex,
        ...result,
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        error:
          error instanceof Error
            ? error.message
            : "Failed to import review data",
      };
    }
    return;
  }

  // Check if this is a product data import (extracting reviews from products)
  if (ctx.request.body?.product_data) {
    try {
      const result = await importFromProductData(
        ctx.request.body.product_data,
        shouldReindex
      );

      ctx.status = 200;
      ctx.body = {
        message: "Reviews extracted from product data, import started",
        status: "processing",
        reindexed: shouldReindex,
        ...result,
      };
    } catch (error) {
      ctx.status = 400;
      ctx.body = {
        error:
          error instanceof Error
            ? error.message
            : "Failed to extract reviews from product data",
      };
    }
    return;
  }

  // Handle file upload
  try {
    const stream = await parse(ctx);

    // Import file data
    const result = await importFromFile(stream, shouldReindex);

    ctx.status = 200;
    ctx.body = {
      message: "File format validated. Import started in background.",
      status: "processing",
      reindexed: shouldReindex,
      ...result,
    };
  } catch (error) {
    ctx.status = 400;
    ctx.body = {
      error:
        error instanceof Error ? error.message : "Failed to import review data",
    };
  }
});

/**
 * @swagger
 * /reviews/data:
 *   get:
 *     summary: List Review Data
 *     description: Retrieves a list of review data for the current location.
 *     tags: [ReviewData]
 *     responses:
 *       200:
 *         description: List of review data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       500:
 *         description: Failed to retrieve review data
 */
router.get("/data", locationRoleMiddleware("support"), async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);

  // Add filtering by product_id if provided
  const productId = ctx.query.product_id as string | undefined;
  const retailerId = ctx.query.retailer_id as string | undefined;

  const query = Review.query().orderBy(
    params.sort || "review_date",
    params.direction || "desc"
  );

  if (productId) {
    query.where("product_id", productId);
  }

  if (retailerId) {
    query.where("retailer_id", retailerId);
  }

  if (params.cursor) {
    const operator = params.page === "prev" ? "<" : ">";
    query.where("id", operator, params.cursor);
  }

  const limit = params.limit || 25;
  const results = await query.limit(limit + 1);

  const hasMore = results.length > limit;
  if (hasMore) {
    results.pop(); // Remove the extra item we fetched
  }

  ctx.body = {
    results,
    nextCursor: hasMore ? results[results.length - 1].id : undefined,
    prevCursor: params.cursor,
    limit,
  };
});

/**
 * @swagger
 * /reviews/search:
 *   post:
 *     summary: Search Review Data
 *     description: Searches review data using vector search.
 *     tags: [ReviewData]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               query:
 *                 type: string
 *               filters:
 *                 type: object
 *               limit:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Search results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       400:
 *         description: Search query is required
 *       500:
 *         description: Error searching review data
 */
router.post("/search", locationRoleMiddleware("support"), async (ctx) => {
  const { query, filters = {}, limit = 10 } = ctx.request.body;

  if (!query) {
    ctx.status = 400;
    ctx.body = {
      error: "Search query is required",
    };
    return;
  }

  try {
    const results = await ReviewDataVectorService.queryReviewData(
      query,
      filters,
      limit
    );

    ctx.status = 200;
    ctx.body = {
      results,
      query,
      filters,
      limit,
    };
  } catch (error) {
    logger.error("Error searching review data:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Error searching review data",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /reviews/stats/product/{productId}:
 *   get:
 *     summary: Get Product Review Statistics
 *     description: Retrieves review statistics for a specific product.
 *     tags: [ReviewData]
 *     parameters:
 *       - in: path
 *         name: productId
 *         required: true
 *         schema:
 *           type: string
 *         description: Product ID
 *     responses:
 *       200:
 *         description: Product review statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       400:
 *         description: Product ID is required
 */
router.get(
  "/stats/product/:productId",
  locationRoleMiddleware("support"),
  async (ctx) => {
    const { productId } = ctx.params;

    if (!productId) {
      ctx.status = 400;
      ctx.body = {
        error: "Product ID is required",
      };
      return;
    }

    try {
      // Get count of reviews by rating
      const ratingStats = await Review.query()
        .select("rating")
        .count("* as count")
        .where("product_id", productId)
        .groupBy("rating")
        .orderBy("rating", "desc");

      // Get overall stats
      const avgRating = await Review.query()
        .avg("rating as average")
        .where("product_id", productId)
        .first();

      const totalReviews = await Review.query()
        .count("* as total")
        .where("product_id", productId)
        .first();

      // Format the response
      const ratingDistribution = {
        5: 0,
        4: 0,
        3: 0,
        2: 0,
        1: 0,
      };

      ratingStats.forEach((stat: any) => {
        const rating = stat.rating.toString();
        if (rating in ratingDistribution) {
          ratingDistribution[rating as keyof typeof ratingDistribution] =
            parseInt(stat.count);
        }
      });

      ctx.body = {
        product_id: productId,
        average_rating: avgRating ? parseFloat(avgRating.average) : 0,
        total_reviews: totalReviews
          ? parseInt(totalReviews.total as string)
          : 0,
        rating_distribution: ratingDistribution,
      };
    } catch (error) {
      logger.error(
        `Error getting review stats for product ${productId}:`,
        error
      );
      ctx.status = 500;
      ctx.body = {
        error: "Error retrieving review statistics",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

/**
 * @swagger
 * /reviews/re-vectorize:
 *   post:
 *     summary: Re-vectorize Review Data
 *     description: Re-vectorizes all review data for the current location.
 *     tags: [ReviewData]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               clean_start:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Review data re-vectorization completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       500:
 *         description: Failed to re-vectorize review data
 */
router.post("/re-vectorize", locationRoleMiddleware("admin"), async (ctx) => {
  const { batch_size = 100, clean_start = true } = ctx.request.body;

  try {
    // First ensure the index exists
    await ReviewDataVectorService.ensureIndexExists();

    // If clean_start is true, delete all existing vectors for review data
    // Since reviews are global (not location-specific), this will delete all review vectors
    if (clean_start) {
      logger.info(
        "Deleting all existing review vectors before re-vectorization"
      );
      await ReviewDataVectorService.emptyIndex();
      logger.info("Successfully deleted all existing review vectors");
    }

    // Start a job tracker for this process
    const jobType = "review_data_vectorization";
    const tracker = OnboardingJobTracker.startJob(0, jobType);

    // Import ReviewDataVectorJob and ensure it's properly queued
    const ReviewDataVectorJob = require("./ReviewDataVectorJob").default;

    // Create a new job with proper parameters
    const job = ReviewDataVectorJob.from({
      batch_size,
      last_processed_id: 0, // Start from the beginning
      retry_count: 0,
      failed_ids: [],
    });

    // Queue the job
    await job.queue();

    logger.info("Re-vectorization job queued for review data");

    ctx.status = 200;
    ctx.body = {
      message: "Review data re-vectorization started",
      status: "processing",
      job_id: tracker.locationId,
      job_type: tracker.jobType,
      clean_start,
    };
  } catch (error) {
    logger.error("Error starting review re-vectorization:", error);
    ctx.status = 500;
    ctx.body = {
      error: "Failed to start re-vectorization",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

/**
 * @swagger
 * /reviews/vectorization-status:
 *   get:
 *     summary: Get Review Data Vectorization Status
 *     description: Retrieves the status of review data vectorization for the current location.
 *     tags: [ReviewData]
 *     responses:
 *       200:
 *         description: Vectorization status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *       500:
 *         description: Failed to retrieve vectorization status
 */
router.get(
  "/vectorization-status",
  locationRoleMiddleware("support"),
  async (ctx) => {
    try {
      const jobType = "review_data_vectorization";
      const summary = OnboardingJobTracker.getSummary(0);

      // Get current vectorization statistics
      const countResult = await Review.query().count("id as total").first();

      const total = countResult ? parseInt(countResult.total as string, 10) : 0;

      // Check vectorization status
      let status = summary.isProcessing
        ? "processing"
        : summary.failed > 0
        ? "failed"
        : summary.completed > 0
        ? "completed"
        : "not_started";

      // If status is not_started but we have data, try to verify if it's actually indexed
      if (status === "not_started" && total > 0) {
        try {
          // Check if index exists and has vectors
          await ReviewDataVectorService.ensureIndexExists();

          // Try a simple query to see if vectors exist
          const testResult = await ReviewDataVectorService.queryReviewData(
            "test query to verify vectors exist",
            {},
            1 // Just need one result to confirm vectors exist
          );

          // If we got any results, vectors exist
          if (testResult && testResult.length > 0) {
            status = "completed";
            logger.info(
              `Detected existing vectors for reviews despite no job record`
            );
          }
        } catch (checkError) {
          // If this fails, just use the original status
          logger.warn(`Error checking vector existence: ${checkError}`);
        }
      }

      ctx.body = {
        status,
        job_summary: summary,
        data_count: total,
      };
    } catch (error) {
      logger.error(`Error getting vectorization status for reviews:`, error);
      ctx.status = 500;
      ctx.body = {
        error: "Failed to get vectorization status",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

export default router;
