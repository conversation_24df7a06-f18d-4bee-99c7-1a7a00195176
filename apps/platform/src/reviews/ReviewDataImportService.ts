import { FileStream } from "../storage/FileStream";
import { RequestError } from "../core/errors";
import { Review, ReviewParams } from "./Review";
import {
  DataNormalizationService,
  NormalizedData,
} from "../core/DataNormalizationService";
import { logger } from "../config/logger";
import ReviewDataVectorJob from "./ReviewDataVectorJob";
import { Product } from "../products/Product";

/**
 * Validates and normalizes review data from an uploaded file stream
 * @param stream The uploaded file stream
 * @returns The normalized data ready for import
 */
export const validateReviewFileData = async (
  stream: FileStream
): Promise<NormalizedData> => {
  if (!stream) {
    throw new RequestError("No file stream provided");
  }

  // Normalize the data from the stream
  const normalizedData = await DataNormalizationService.normalizeData(stream);

  // Verify this is review data
  if (normalizedData.type !== "review") {
    throw new RequestError(
      "The uploaded file is not recognized as review data. Please use the appropriate import endpoint."
    );
  }

  // Report any errors found during normalization
  if (normalizedData.errors.length > 0) {
    const errorMessages = normalizedData.errors.map(
      (err) => `Row ${err.row}: ${err.error}`
    );
    throw new RequestError(
      `Data validation errors:\n${errorMessages.join("\n")}`
    );
  }

  return normalizedData;
};

/**
 * Imports review data from a normalized data structure
 * @param normalizedData Previously normalized review data
 * @param onProgress Optional callback for progress updates
 * @param reindex Whether to reindex the data in the vector database
 * @returns Result of the import operation
 */
export const importFromNormalizedData = async (
  normalizedData: NormalizedData,
  onProgress?: (count: number) => void,
  reindex: boolean = true
): Promise<{ processed: number; errors: any[] }> => {
  if (normalizedData.type !== "review") {
    throw new RequestError("Invalid data type. Expected review data.");
  }

  try {
    // Process the data rows
    let rowCount = 0;
    const errors: any[] = [];

    for (const row of normalizedData.data) {
      rowCount++;
      const reviewRow = row as Partial<ReviewParams>;

      try {
        // Ensure required fields are present
        if (!reviewRow.product_id || reviewRow.rating === undefined) {
          errors.push({
            row: rowCount,
            error:
              "Missing required fields: product_id and rating are required",
          });
          continue;
        }

        // Validate product exists
        const product = await Product.first((qb) =>
          qb.where({ meta_sku: reviewRow.product_id })
        );

        if (!product) {
          logger.warn(
            `Product with ID ${reviewRow.product_id} not found for review import`
          );
          // Continue with import anyway, as product might be added later
        }

        // Check if review already exists (by ID if available, or by product_id + author + created_at)
        let existingReview: Review | undefined;

        if ((reviewRow as any).id) {
          existingReview = await Review.first((qb) =>
            qb.where({ id: (reviewRow as any).id })
          );
        } else if (reviewRow.author && reviewRow.review_date) {
          existingReview = await Review.first((qb) =>
            qb.where({
              product_id: reviewRow.product_id,
              author: reviewRow.author,
              review_date: reviewRow.review_date,
            })
          );
        }

        if (existingReview) {
          // Update existing review
          await Review.update(
            (qb) => qb.where({ id: existingReview!.id }),
            reviewRow
          );
          logger.info(`Updated existing review ${existingReview!.id}`);
        } else {
          // Insert new review
          await Review.insert(reviewRow);
          logger.info(
            `Created new review for product: ${reviewRow.product_id}`
          );
        }

        if (onProgress && rowCount % 100 === 0) {
          onProgress(rowCount);
        }
      } catch (error) {
        logger.error(`Error processing review row ${rowCount}:`, error);
        errors.push({
          row: rowCount,
          error: error instanceof Error ? error.message : String(error),
        });
      }
    }

    if (onProgress) {
      onProgress(rowCount);
    }

    // Start the vector processing job after successful import if reindex is true
    if (reindex) {
      try {
        await ReviewDataVectorJob.from({
          batch_size: 100,
        }).queue();

        logger.info(
          `Queued review data vectorization job with ${rowCount} records`
        );
      } catch (vectorError) {
        logger.error(`Failed to queue review vector job:`, vectorError);
        // Continue - don't fail the import if just the vector job fails
      }
    } else {
      logger.info(`Skipping vectorization as reindex=false was specified`);
    }

    return {
      processed: rowCount,
      errors,
    };
  } catch (error) {
    logger.error("Error in review data import:", error);
    throw error;
  }
};

/**
 * Imports review data from a file upload
 * @param stream The file upload stream to process
 * @param reindex Whether to reindex the data in the vector database
 * @param onProgress Optional callback for progress updates
 * @returns Result of the import operation
 */
export const importFromFile = async (
  stream: FileStream,
  reindex: boolean = true,
  onProgress?: (count: number) => void
): Promise<{ processed: number; errors: any[] }> => {
  if (!stream) {
    throw new RequestError("No file stream provided");
  }

  try {
    // First validate and normalize the data
    const processedData = await validateReviewFileData(stream);
    console.log(
      `Normalization complete. Found ${processedData.data.length} valid review records`
    );

    // Process the normalized data
    return await importFromNormalizedData(processedData, onProgress, reindex);
  } catch (error) {
    logger.error("Error in review data import:", error);
    throw error;
  }
};

/**
 * Extracts and imports reviews from product data
 * @param productData Product data containing review information
 * @param reindex Whether to reindex the data in the vector database
 * @returns Result of the import operation
 */
export const importFromProductData = async (
  productData: any,
  reindex: boolean = true
): Promise<{ processed: number; errors: any[] }> => {
  try {
    // Use the DataNormalizationService to extract reviews from product data
    const normalizedData =
      DataNormalizationService.extractReviewsFromProducts(productData);

    logger.info(
      `Extracted ${normalizedData.data.length} reviews from product data`
    );

    // Import the normalized review data
    return await importFromNormalizedData(normalizedData, undefined, reindex);
  } catch (error) {
    logger.error("Error importing reviews from product data:", error);
    throw error;
  }
};
