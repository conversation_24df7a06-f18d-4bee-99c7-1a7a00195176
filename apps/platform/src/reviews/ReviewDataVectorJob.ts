import Job from "../queue/Job";
import { Review } from "./Review";
import { ReviewDataVectorService } from "./ReviewDataVectorService";
import { logger } from "../config/logger";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";

const MAX_RETRIES = 3;
const FALLBACK_BATCH_SIZE = 50;

interface ReviewDataVectorParams {
  batch_size?: number;
  last_processed_id?: number;
  retry_count?: number;
  failed_ids?: number[];
}

export default class ReviewDataVectorJob extends Job {
  static $name = "review_data_vector";

  batch_size?: number;
  last_processed_id?: number;
  retry_count?: number;
  failed_ids?: number[];

  static from(params: ReviewDataVectorParams) {
    logger.info(`Creating review data vector job`, {
      batch_size: params.batch_size || 100,
      last_processed_id: params.last_processed_id,
      retry_count: params.retry_count || 0,
      failed_ids_count: params.failed_ids?.length || 0,
    });

    return new this({
      batch_size: params.batch_size || 100,
      last_processed_id: params.last_processed_id,
      retry_count: params.retry_count || 0,
      failed_ids: params.failed_ids || [],
    });
  }

  static async handler({
    batch_size,
    last_processed_id,
    retry_count = 0,
    failed_ids = [],
  }: ReviewDataVectorParams) {
    try {
      // Track job in OnboardingJobTracker
      const jobType = "review_data_vectorization";

      // Ensure the Pinecone index exists before processing
      await ReviewDataVectorService.ensureIndexExists();

      // Two paths: retry failed IDs or process new ones
      let reviewData: Review[] = [];

      if (failed_ids && failed_ids.length > 0) {
        // If we have failed IDs to retry, process those first
        logger.info(
          `Retrying vectorization for ${failed_ids.length} failed review records`
        );
        reviewData = await Review.query().whereIn("id", failed_ids);
      } else {
        // Process normal queue of unprocessed records
        const effectiveBatchSize = batch_size || FALLBACK_BATCH_SIZE;
        const query = Review.query()
          .orderBy("id", "asc")
          .limit(effectiveBatchSize);

        if (last_processed_id) {
          query.where("id", ">", last_processed_id);
        }

        reviewData = await query;
      }

      if (reviewData.length === 0) {
        logger.info(`No more review data to process`);
        // Mark job as complete in tracker
        OnboardingJobTracker.completeJob(0, jobType);
        return;
      }

      // Process the batch using ReviewDataVectorService
      const result = await ReviewDataVectorService.upsertReviewData(reviewData);

      // Log detailed results
      logger.info(
        `Vectorized ${result.successCount} review records, ${result.errorCount} errors`
      );

      // Track the highest ID we've processed (for non-retry jobs)
      const highestProcessedId =
        !failed_ids || failed_ids.length === 0
          ? reviewData[reviewData.length - 1].id
          : last_processed_id;

      // Handle failed records with retry logic
      if (
        result.errorCount > 0 &&
        result.failedIds &&
        result.failedIds.length > 0
      ) {
        // Extract numeric IDs from the returned failedIds (which include "review_" prefix)
        const failedNumericIds = result.failedIds
          .map((id: string) => {
            const match = id.match(/^review_(\d+)$/);
            return match ? parseInt(match[1], 10) : null;
          })
          .filter((id: number | null): id is number => id !== null);

        // If we have failures and haven't exceeded max retries, queue a retry job
        if (failedNumericIds.length > 0 && (retry_count || 0) < MAX_RETRIES) {
          logger.warn(
            `Scheduling retry ${(retry_count || 0) + 1}/${MAX_RETRIES} for ${
              failedNumericIds.length
            } failed review records`
          );

          await ReviewDataVectorJob.from({
            batch_size: Math.max(10, Math.floor((batch_size || 100) / 2)),
            last_processed_id: highestProcessedId,
            retry_count: (retry_count || 0) + 1,
            failed_ids: failedNumericIds,
          })
            .delay(2000 * (retry_count || 0) + 1000) // Increasing delay with each retry
            .queue();
        } else if (failedNumericIds.length > 0) {
          // We've exceeded max retries, log a critical error
          logger.error(
            `Failed to vectorize ${failedNumericIds.length} review records after ${MAX_RETRIES} retries`
          );
          OnboardingJobTracker.failJob(
            0,
            jobType,
            `Failed to vectorize ${failedNumericIds.length} review records after ${MAX_RETRIES} retries`
          );
        }
      }

      // If we're not in retry mode and have processed a full batch, queue the next job for new records
      if (
        (!failed_ids || failed_ids.length === 0) &&
        reviewData.length === batch_size
      ) {
        logger.info(
          `Queueing next batch of review data after ID ${highestProcessedId}`
        );

        await ReviewDataVectorJob.from({
          batch_size,
          last_processed_id: highestProcessedId,
        })
          .delay(1000) // Add a small delay between batches
          .queue();
      } else {
        // This was the last batch, mark as complete
        OnboardingJobTracker.completeJob(0, jobType);
      }

      return true;
    } catch (error) {
      logger.error("Error in ReviewDataVectorJob:", error);

      // Track failure in job tracker
      OnboardingJobTracker.failJob(
        0,
        "review_data_vectorization",
        error instanceof Error ? error.message : String(error)
      );

      // If we encounter a critical error, retry the entire job with a longer delay
      if ((retry_count || 0) < MAX_RETRIES) {
        const nextRetryCount = (retry_count || 0) + 1;
        const delayMs = Math.pow(2, nextRetryCount) * 1000; // Exponential backoff

        logger.info(
          `Scheduling full job retry ${nextRetryCount}/${MAX_RETRIES} after ${delayMs}ms`
        );

        await ReviewDataVectorJob.from({
          batch_size,
          last_processed_id,
          retry_count: nextRetryCount,
          failed_ids,
        })
          .delay(delayMs)
          .queue();
      } else {
        logger.error(
          `Failed to process review data batch after ${MAX_RETRIES} retries`
        );
      }

      throw error;
    }
  }
}
