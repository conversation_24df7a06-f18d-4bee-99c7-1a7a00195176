import { Review } from "./Review";
import { VectorService, VectorData } from "../core/VectorService";
import { logger } from "../config/logger";

const REVIEW_INDEX = "review-data";

export class ReviewDataVectorService {
  private static vectorService: VectorService | null = null;

  /**
   * Get the vector service instance
   */
  private static getVectorService(): VectorService {
    if (!this.vectorService) {
      this.vectorService = VectorService.getInstance();
    }
    return this.vectorService;
  }

  static async initialize() {
    await this.getVectorService().initialize();
  }

  static async upsertReviewData(reviews: Review[]) {
    try {
      if (!reviews.length) {
        logger.warn(
          "No review data provided to upsert, skipping vectorization"
        );
        return { successCount: 0, errorCount: 0, failedIds: [] };
      }

      // Convert review data to vector format with enhanced text representation
      const vectorData: VectorData[] = reviews.map((record) => ({
        id: `review_${record.id}`,
        text: this.generateEnhancedText(record),
        metadata: {
          location_id: 0, // Reviews aren't tied to a specific location
          source_type: "review",
          source_id: record.id.toString(),
          product_id: record.product_id,
          retailer_id: record.retailer_id || "",
          rating: record.rating,
          author: record.author || "",
          review_date:
            record.review_date?.toISOString() || new Date().toISOString(),
          verified_purchase: record.verified_purchase,
          helpful_votes: record.helpful_votes || 0,
          title: record.title || "",
          text_length: record.text ? record.text.length : 0,
          sentiment_score: this.calculateSentimentScore(record),
          created_at: Date.now(),
          updated_at: Date.now(),
        },
      }));

      // Use centralized vector service to upsert data without namespace
      // This keeps review data accessible to all locations
      const vectorService = this.getVectorService();
      const result = await vectorService.upsertVectors(
        REVIEW_INDEX,
        vectorData
      );

      logger.info(
        `Review data vectorization complete: ${result.successCount} succeeded, ${result.errorCount} failed`
      );
      return result;
    } catch (error) {
      logger.error("Error upserting review data vectors:", error);
      throw error;
    }
  }

  static async queryReviewData(
    query: string,
    filters: { [key: string]: any } = {},
    topK: number = 10
  ) {
    try {
      if (!query) {
        throw new Error("Empty query provided to review data vector search");
      }

      // Ensure we have at least one key-value pair in the filter object
      // This fixes the "You must enter a `filter` object with at least one key-value pair" error
      if (!filters || Object.keys(filters).length === 0) {
        logger.warn(
          "No filters provided for review data query, adding default filter"
        );
        // Add a default filter that will match all reviews
        filters = { source_type: "review" };
      }

      // Add pre-processing for query to improve search results
      const enhancedQuery = this.enhanceSearchQuery(query);

      const vectorService = this.getVectorService();
      try {
        return await vectorService.queryVectors(
          REVIEW_INDEX,
          enhancedQuery,
          filters,
          topK
        );
      } catch (error) {
        // Handle specific errors
        if (error instanceof Error) {
          if (error.message.includes("404")) {
            logger.warn(
              `Review index "${REVIEW_INDEX}" not found, attempting to create it`
            );
            await this.ensureIndexExists();
            return [];
          } else if (error.message.includes("filter")) {
            logger.warn(
              `Filter error with review data query: ${error.message}`
            );
            // Try with a different filter as last resort
            try {
              return await vectorService.queryVectors(
                REVIEW_INDEX,
                enhancedQuery,
                { source_type: "review" },
                topK
              );
            } catch (e) {
              logger.error("Second attempt at querying review data failed:", e);
              return [];
            }
          }
        }
        throw error;
      }
    } catch (error) {
      logger.error("Error querying review data vectors:", error);
      // Return empty results instead of throwing
      return [];
    }
  }

  /**
   * Creates an enhanced text representation of review data for better semantic matching
   */
  private static generateEnhancedText(record: Review): string {
    // Base text with core information
    let text = "";

    if (record.title) {
      text += `Title: ${record.title}. `;
    }

    if (record.text) {
      text += `Review: ${record.text}. `;
    } else {
      text += "No review text provided. ";
    }

    text += `Rating: ${record.rating} out of 5 stars. `;

    if (record.author) {
      text += `Written by: ${record.author}. `;
    }

    if (record.review_date) {
      text += `Posted on: ${record.review_date.toLocaleDateString()}. `;
    }

    if (record.verified_purchase) {
      text += "This is a verified purchase. ";
    }

    if (record.helpful_votes && record.helpful_votes > 0) {
      text += `${record.helpful_votes} people found this review helpful. `;
    }

    // Add product context if available
    if (record.product_id) {
      text += `Review for product ID: ${record.product_id}. `;
    }

    // Add retailer context if available
    if (record.retailer_id) {
      text += `Purchased from retailer ID: ${record.retailer_id}. `;
    }

    // Add sentiment analysis keywords
    const sentiment = this.calculateSentimentScore(record);
    if (sentiment > 0.6) {
      text += "This is a very positive review. Customer is highly satisfied. ";
    } else if (sentiment > 0.2) {
      text +=
        "This is a somewhat positive review. Customer is generally satisfied. ";
    } else if (sentiment > -0.2) {
      text += "This is a neutral review. Customer has mixed feelings. ";
    } else if (sentiment > -0.6) {
      text += "This is a somewhat negative review. Customer is dissatisfied. ";
    } else {
      text +=
        "This is a very negative review. Customer is highly dissatisfied. ";
    }

    return text;
  }

  /**
   * Simple sentiment analysis based on rating and text
   * Returns score between -1 (very negative) and 1 (very positive)
   */
  private static calculateSentimentScore(record: Review): number {
    // Base sentiment on rating (convert 1-5 scale to -1 to 1)
    const sentiment = (record.rating - 3) / 2;

    // Further refinements could be done by analyzing the text
    // but that would require a more complex sentiment analysis

    return sentiment;
  }

  /**
   * Enhances search queries for better review data retrieval
   */
  private static enhanceSearchQuery(query: string): string {
    // Convert simple review queries to more specific search terms
    let enhancedQuery = query;

    // Keywords for common review queries
    if (
      query.toLowerCase().includes("negative") ||
      query.toLowerCase().includes("bad") ||
      query.toLowerCase().includes("poor")
    ) {
      enhancedQuery += " low rating dissatisfied unhappy 1-star 2-star";
    }

    if (
      query.toLowerCase().includes("positive") ||
      query.toLowerCase().includes("good") ||
      query.toLowerCase().includes("great")
    ) {
      enhancedQuery += " high rating satisfied happy 4-star 5-star";
    }

    if (
      query.toLowerCase().includes("verified") ||
      query.toLowerCase().includes("confirmed")
    ) {
      enhancedQuery += " verified purchase confirmed buyer";
    }

    return enhancedQuery;
  }

  /**
   * Validates that the Pinecone index exists and creates it if needed
   */
  static async ensureIndexExists() {
    try {
      const vectorService = this.getVectorService();
      await vectorService.initialize();

      // Check if index exists first by listing indices
      const indices = await vectorService.listIndices();
      const indexExists = indices.includes(REVIEW_INDEX);

      if (!indexExists) {
        logger.info(`Review index '${REVIEW_INDEX}' not found, creating it`);

        // Attempt to create the index if it doesn't exist
        await vectorService.createIndex(REVIEW_INDEX, {
          dimension: 3072, // Updated dimension for text-embedding-3-large model (was 1536)
          metric: "cosine",
          serverless: {
            cloud: "aws", // Specify AWS instead of GCP
            region: "us-east-1", // Specify the us-east-1 region as required for free tier
          },
        });

        logger.info(`Review index '${REVIEW_INDEX}' created successfully`);
      } else {
        logger.info(`Review index '${REVIEW_INDEX}' already exists`);
      }

      return true;
    } catch (error) {
      logger.error(`Failed to verify review index: ${error}`);
      // Don't throw, just return false
      return false;
    }
  }

  static async deleteReviewData(filters: { [key: string]: any } = {}) {
    try {
      const vectorService = this.getVectorService();
      return await vectorService.deleteVectors(REVIEW_INDEX, filters);
    } catch (error) {
      logger.error("Error deleting review data vectors:", error);
      throw error;
    }
  }

  /**
   * Empties the entire review index, removing all vectors
   * Use with caution as this will delete ALL review data vectors
   */
  static async emptyIndex() {
    try {
      const vectorService = this.getVectorService();
      logger.warn(
        "Emptying entire review index - this will delete ALL review vectors"
      );
      return await vectorService.emptyIndex(REVIEW_INDEX);
    } catch (error) {
      logger.error("Error emptying review index:", error);
      throw error;
    }
  }

  /**
   * Gets statistics about the review index
   */
  static async getIndexStats() {
    try {
      const vectorService = this.getVectorService();
      const stats = await vectorService.getIndexStats(REVIEW_INDEX);
      return {
        totalVectorCount: stats.totalVectorCount || 0,
        namespaces: stats.namespaces || {},
      };
    } catch (error) {
      logger.error("Error getting review index stats:", error);
      return {
        totalVectorCount: 0,
        namespaces: {},
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
}
