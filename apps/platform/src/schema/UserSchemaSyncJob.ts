import Location from "../locations/Location";
import { Job } from "../queue";
import { syncUserDataPaths } from "./UserSchemaService";

interface UserSchemaSyncTrigger {
  location_id?: number;
  delta?: Date;
}

export default class UserSchemaSyncJob extends Job {
  static $name = "user_schema_sync";

  static from(data: UserSchemaSyncTrigger): UserSchemaSyncJob {
    return new this(data);
  }

  static async handler({ delta, location_id }: UserSchemaSyncTrigger) {
    if (delta && !(delta instanceof Date)) {
      delta = new Date(delta);
    }

    // specific location only, or all locations
    const locationIds: number[] = location_id
      ? [location_id]
      : await Location.query()
          .select("id")
          .then((rs) => rs.map((r: any) => r.id));

    for (const location_id of locationIds) {
      await syncUserDataPaths({
        location_id,
        updatedAfter: delta,
      });
    }
  }
}
