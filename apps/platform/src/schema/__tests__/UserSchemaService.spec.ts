import Location from "../../locations/Location";
import { LocationRulePath } from "../../rules/LocationRulePath";
import { User } from "../../users/User";
import { UserEvent } from "../../users/UserEvent";
import { addLeafPaths, syncUserDataPaths } from "../UserSchemaService";
import { sleep } from "../../utilities";
import { startOfSecond } from "date-fns";
import { reservedPaths } from "../../rules/RuleHelpers";

describe("UserSchemaService", () => {
  describe("path extraction", () => {
    test("collects all leaf node paths where value is not undefined", () => {
      const data = {
        one: 1,
        two: [2, true],
        three: {
          four: "something",
          five: [
            {
              six: "x",
            },
            {
              six: "y",
            },
          ],
          eight: undefined, // shouldn't include this
          nine: null, // should include this
        },
      };

      const set = new Set<string>();

      addLeafPaths(set, data);

      const arr = Array.from(set.values());

      expect(arr).not.toContain("$");
      expect(arr).toContain("$.one");
      expect(arr).not.toContain("$.two");
      expect(arr).toContain("$.two[*]");
      expect(arr).toContain("$.three.four");
      expect(arr).not.toContain("$.three.five[*]");
      expect(arr).toContain("$.three.five[*].six");
      expect(arr).not.toContain("$.three.eight");
      expect(arr).toContain("$.three.nine");
    });
  });
  describe("sync paths", () => {
    const setup = async () => {
      const location_id = await Location.insert({
        name: `Test Location ${Date.now()}`,
      });

      let user_id = await User.insert({
        location_id,
        data: {
          x: 1,
          y: [
            {
              z: "hi",
            },
          ],
        },
      });

      await UserEvent.insert({
        location_id,
        user_id,
        name: "ate",
        data: {
          food: "cake",
        },
      });

      user_id = await User.insert({
        location_id,
        data: {
          x: 2,
          y: null,
          z: undefined,
        },
      });

      await UserEvent.insert({
        location_id,
        user_id,
        name: "ate",
        data: {
          food: "pizza",
        },
      });

      await UserEvent.insert({
        location_id,
        user_id,
        name: "drive",
        data: {
          vehicle: "car",
        },
      });

      return { location_id };
    };

    test("extract and save all user+event paths for location", async () => {
      const { location_id } = await setup();

      await syncUserDataPaths({ location_id });

      const paths = await LocationRulePath.all((q) =>
        q.where("location_id", location_id)
      );

      // exactly one copy of path added
      expect(
        paths.filter((p) => p.type === "user" && p.path === "$.x").length
      ).toEqual(1);
      expect(
        paths.find(
          (p) => p.type === "event" && p.name === "ate" && p.path === "$.food"
        )
      ).not.toBeUndefined();
    });

    test("merge in delta fields", async () => {
      const { location_id } = await setup();

      await syncUserDataPaths({
        location_id,
      });

      await sleep(2000);

      const dt = new Date();

      const user_id = await User.insert({
        location_id,
        data: {
          a: 1,
        },
      });

      await UserEvent.insert({
        location_id,
        user_id,
        name: "test",
        data: {
          feature: "schema-sync",
        },
      });

      await syncUserDataPaths({
        location_id,
        updatedAfter: startOfSecond(dt),
      });

      const paths = await LocationRulePath.all((q) =>
        q.where("location_id", location_id)
      );

      // make sure new path is added
      expect(
        paths.filter((p) => p.type === "user" && p.path === "$.a").length
      ).toEqual(1);

      // make sure old paths aren't removed
      expect(
        paths.filter(
          (p) =>
            p.type === "event" && p.name === "drive" && p.path === "$.vehicle"
        ).length
      ).toEqual(1);
    });

    test("sync w/o delta removes unused paths", async () => {
      const { location_id } = await setup();

      await syncUserDataPaths({
        location_id,
      });

      await User.update((q) => q.where("location_id", location_id), {
        data: {
          f: 1,
        },
      });

      await UserEvent.delete((q) => q.where("location_id", location_id));

      await syncUserDataPaths({
        location_id,
      });

      const paths = await LocationRulePath.all((q) =>
        q.where("location_id", location_id)
      );

      const count = reservedPaths.user.length + 1;
      expect(paths.length).toEqual(count); // only '$.f'
    });
  });
});
