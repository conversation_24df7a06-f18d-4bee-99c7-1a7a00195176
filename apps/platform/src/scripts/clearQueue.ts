import App from "../app";
import env from "../config/env";

/**
 * <PERSON>ript to clear all jobs from the queue
 * Usage:
 *  - npm run script:clearQueue
 *  - ts-node apps/platform/src/scripts/clearQueue.ts
 */
async function clearQueue() {
  try {
    console.log("🧹 Clearing queue...");

    const app = await App.init(env());

    const result = await app.queue.clearAll();

    if (result) {
      console.log("✅ Queue cleared successfully!");
    } else {
      console.log(
        "❌ Failed to clear queue. The provider may not support clearing."
      );
    }

    await app.close();
    process.exit(0);
  } catch (error) {
    console.error("❌ Error clearing queue:", error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  clearQueue();
}

export default clearQueue;
