import { ChatOpenAI } from "@langchain/openai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";
import { logger } from "../config/logger";

interface AnalysisResult {
  text: string;
  summary: string;
  insights: string[];
}

export class OpenAIService {
  private model: ChatOpenAI;

  constructor() {
    this.model = new ChatOpenAI({
      modelName: "gpt-4.1-mini", // Using GPT-4.1 Mini for cost-efficiency, adjust as needed
      temperature: 0.2, // Low temperature for more factual responses
    });
    logger.debug("OpenAIService initialized");
  }

  /**
   * Analyzes document content and returns structured insights
   *
   * @param content The text content of the document to analyze
   * @param fileName Optional file name for context
   * @returns AnalysisResult containing summary and insights
   */
  async analyze(content: string, fileName?: string): Promise<AnalysisResult> {
    try {
      logger.info(
        `Analyzing document${fileName ? ` ${fileName}` : ""} with ${
          content.length
        } characters`
      );

      // Truncate content if it's too long (LLM context limit protection)
      const maxContentLength = 15000;
      const truncatedContent =
        content.length > maxContentLength
          ? `${content.substring(
              0,
              maxContentLength
            )}... [content truncated due to length]`
          : content;

      const systemPrompt = `You are an expert document analyzer for a business. 
Analyze the provided document content and extract key insights, focusing on:
1. Main themes and topics
2. Key business metrics or data points
3. Strategic recommendations or action items
4. Potential risks or opportunities
5. Relevant market or competitor information

Provide your analysis in a structured format.`;

      const fileContext = fileName ? `Document name: ${fileName}\n\n` : "";
      const userPrompt = `${fileContext}Please analyze the following document content and provide a detailed summary and key insights:

${truncatedContent}`;

      const response = await this.model.invoke([
        new SystemMessage(systemPrompt),
        new HumanMessage(userPrompt),
      ]);

      // Process the response into our expected format
      const responseText = response.content.toString();

      // Extract a summary from the response (first paragraph or two)
      const summary = this.extractSummary(responseText);

      // Extract insights from the response
      const insights = this.extractInsights(responseText);

      const result: AnalysisResult = {
        text: responseText,
        summary,
        insights,
      };

      logger.info("Document analysis complete");
      return result;
    } catch (error) {
      logger.error(
        `Error analyzing document content: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      throw error;
    }
  }

  /**
   * Extracts a summary from the full analysis text
   */
  private extractSummary(text: string): string {
    // Get first paragraph that's reasonably sized
    const paragraphs = text.split("\n\n");

    for (const paragraph of paragraphs) {
      const cleaned = paragraph.trim();
      if (
        cleaned.length > 50 &&
        !cleaned.startsWith("#") &&
        !cleaned.startsWith("*")
      ) {
        return cleaned;
      }
    }

    // Fallback to first 150 characters if no good paragraph found
    return text.substring(0, 150) + "...";
  }

  /**
   * Extracts insights from the full analysis text
   */
  private extractInsights(text: string): string[] {
    const insights: string[] = [];

    // Look for bullet points or numbered lists
    const lines = text.split("\n");

    for (const line of lines) {
      const trimmed = line.trim();

      // Match bullet points, numbered lists, or lines starting with "Insight:"
      if (trimmed.match(/^[*\-•]|^\d+[.)]\s|^Insight:/)) {
        // Clean up the insight text
        const cleanInsight = trimmed
          .replace(/^[*\-•]\s*/, "")
          .replace(/^\d+[.)]\s*/, "")
          .replace(/^Insight:\s*/, "");

        if (cleanInsight.length > 5) {
          insights.push(cleanInsight);
        }
      }
    }

    // If no insights were found using the pattern, use sentences from the text
    if (insights.length === 0) {
      const sentences = text.match(/[^.!?]+[.!?]+/g) || [];
      for (let i = 0; i < Math.min(5, sentences.length); i++) {
        const sentence = sentences[i].trim();
        if (sentence.length > 15) {
          insights.push(sentence);
        }
      }
    }

    return insights.slice(0, 10); // Return up to 10 insights
  }
}
