/* eslint-disable indent */
import Router from "@koa/router";
import { LocationState } from "../auth/AuthMiddleware";
import path from "path";
import { uploadImage, getImage } from "../storage/ImageService";
import parseFileStream from "../storage/FileStream";
import axios from "axios";

/**
 * @swagger
 * components:
 *   schemas:
 *     SharedAssetUploadResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         filename:
 *           type: string
 *         originalName:
 *           type: string
 *         url:
 *           type: string
 *           description: Branded URL for sharing
 *         directUrl:
 *           type: string
 *           description: Direct storage URL
 *         type:
 *           type: string
 *         businessName:
 *           type: string
 *         userId:
 *           type: integer
 *           nullable: true
 *         imageId:
 *           type: integer
 *         extension:
 *           type: string
 *     SharedAssetErrorResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         error:
 *           type: string
 */

/**
 * @swagger
 * tags:
 *   name: Shared Assets
 *   description: Endpoints for uploading and serving shared images/assets
 */

const router = new Router<LocationState, any>({
  prefix: "/shared-assets",
});

/**
 * @swagger
 * /shared-assets/upload:
 *   post:
 *     summary: Upload a shared image asset
 *     tags: [Shared Assets]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               businessName:
 *                 type: string
 *               folder:
 *                 type: string
 *               type:
 *                 type: string
 *               locationId:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Image uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SharedAssetUploadResponse'
 *       500:
 *         description: File upload failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SharedAssetErrorResponse'
 */
// Get the base URL for the app
const getBaseUrl = () => {
  const baseUrl =
    process.env.FRONTEND_URL ||
    process.env.BASE_URL ||
    "https://beta.bakedbot.ai";
  return baseUrl.endsWith("/") ? baseUrl.slice(0, -1) : baseUrl;
};

// Handle file upload
router.post("/upload", async (ctx) => {
  try {
    // Get metadata from query parameters
    const businessName =
      (ctx.query.businessName as string) ||
      ctx.request.body?.businessName ||
      "unknown";
    const folderName =
      (ctx.query.folder as string) ||
      ctx.request.body?.folder ||
      "shared_images";
    const type =
      (ctx.query.type as string) || ctx.request.body?.type || "market_analysis";

    // Either use the current user's location ID or a default one for shared assets
    // Note: A locationId is required by ImageService
    const locationId =
      ctx.state.locationId ||
      parseInt(ctx.query.locationId as string, 10) ||
      parseInt(ctx.request.body?.locationId, 10) ||
      1; // Default location ID if none provided

    // Parse the multipart form and get the file stream
    if (!ctx.is("multipart")) {
      throw new Error("Expected multipart form");
    }

    // Use the parse function from FileStream
    const fileStream = await parseFileStream(ctx);

    // Upload the image using ImageService
    const image = await uploadImage(locationId, fileStream);

    // Determine the file extension from the uploaded file or default to .png
    const fileExtension =
      image.extension ||
      (image.original_name ? path.extname(image.original_name) : ".png");

    // Create a branded URL that points to our domain WITH proper file extension
    const baseUrl = getBaseUrl();
    const brandedUrl = `${baseUrl}/api/shared-assets/image/${image.id}${fileExtension}`;

    // Return both URLs - branded for sharing and direct for fallback
    ctx.status = 200;
    ctx.body = {
      success: true,
      filename: image.name,
      originalName: image.original_name,
      url: brandedUrl, // Use our branded URL with extension for sharing
      directUrl: image.url, // Keep the direct URL as fallback
      type,
      businessName,
      userId: ctx.state.user?.id,
      imageId: image.id,
      extension: fileExtension,
    };
  } catch (error: any) {
    console.error("File upload error:", error);
    ctx.status = 500;
    ctx.body = {
      success: false,
      error: error.message || "File upload failed",
    };
  }
});

/**
 * @swagger
 * /shared-assets/image/{imageId}.{ext}:
 *   get:
 *     summary: Serve an image by ID and extension (branded URL)
 *     tags: [Shared Assets]
 *     parameters:
 *       - in: path
 *         name: imageId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Image ID
 *       - in: path
 *         name: ext
 *         required: true
 *         schema:
 *           type: string
 *         description: File extension (e.g., png, jpg)
 *       - in: query
 *         name: locationId
 *         schema:
 *           type: integer
 *         description: Location ID (optional)
 *     responses:
 *       200:
 *         description: The image file
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *           image/jpeg:
 *             schema:
 *               type: string
 *               format: binary
 *           image/gif:
 *             schema:
 *               type: string
 *               format: binary
 *           image/webp:
 *             schema:
 *               type: string
 *               format: binary
 *       302:
 *         description: Redirect to the direct image URL if proxy fails
 *       400:
 *         description: Invalid image ID
 *       404:
 *         description: Image not found
 *       500:
 *         description: Failed to serve file
 */
// New endpoint for serving images directly with our domain - with extension support
router.get("/image/:imageId.:ext", async (ctx) => {
  try {
    const imageId = parseInt(ctx.params.imageId, 10);
    if (isNaN(imageId)) {
      ctx.status = 400;
      ctx.body = { error: "Invalid image ID" };
      return;
    }

    // Use a default locationId or try to get it from query
    const locationId =
      ctx.state.locationId || parseInt(ctx.query.locationId as string, 10) || 1; // Default location ID

    // Find image using ImageService
    const image = await getImage(locationId, imageId);

    if (!image) {
      ctx.status = 404;
      ctx.body = { error: "Image not found" };
      return;
    }

    // Always serve the actual image content for URLs with extensions
    try {
      // Proxy the image to maintain our domain in social shares
      const response = await axios.get(image.url, {
        responseType: "arraybuffer",
      });

      // Set appropriate headers based on extension or response
      const ext = ctx.params.ext.toLowerCase();
      const contentType =
        ext === "jpg" || ext === "jpeg"
          ? "image/jpeg"
          : ext === "png"
          ? "image/png"
          : ext === "gif"
          ? "image/gif"
          : ext === "webp"
          ? "image/webp"
          : response.headers["content-type"] || "image/png";

      ctx.set("Content-Type", contentType);
      ctx.set("Cache-Control", "public, max-age=31536000"); // 1 year cache
      ctx.set("Access-Control-Allow-Origin", "*");

      // Send the image data
      ctx.body = response.data;
    } catch (proxyError) {
      console.error("Error proxying image:", proxyError);
      // If proxying fails, fall back to redirect
      ctx.redirect(image.url);
    }
  } catch (error: any) {
    console.error("File serving error:", error);
    ctx.status = 500;
    ctx.body = { error: "Failed to serve file" };
  }
});

/**
 * @swagger
 * /shared-assets/image/{imageId}:
 *   get:
 *     summary: Serve an image by ID (extensionless, browser-aware)
 *     tags: [Shared Assets]
 *     parameters:
 *       - in: path
 *         name: imageId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Image ID
 *       - in: query
 *         name: locationId
 *         schema:
 *           type: integer
 *         description: Location ID (optional)
 *       - in: query
 *         name: direct
 *         schema:
 *           type: string
 *         description: If 'true', always redirect to direct URL
 *     responses:
 *       200:
 *         description: The image file (proxied for browsers)
 *         content:
 *           image/png:
 *             schema:
 *               type: string
 *               format: binary
 *           image/jpeg:
 *             schema:
 *               type: string
 *               format: binary
 *           image/gif:
 *             schema:
 *               type: string
 *               format: binary
 *           image/webp:
 *             schema:
 *               type: string
 *               format: binary
 *       302:
 *         description: Redirect to the direct image URL
 *       400:
 *         description: Invalid image ID
 *       404:
 *         description: Image not found
 *       500:
 *         description: Failed to serve file
 */
// Support requests without extension too (for backward compatibility)
router.get("/image/:imageId", async (ctx) => {
  try {
    const imageId = parseInt(ctx.params.imageId, 10);
    if (isNaN(imageId)) {
      ctx.status = 400;
      ctx.body = { error: "Invalid image ID" };
      return;
    }

    // Use a default locationId or try to get it from query
    const locationId =
      ctx.state.locationId || parseInt(ctx.query.locationId as string, 10) || 1; // Default location ID

    // Find image using ImageService
    const image = await getImage(locationId, imageId);

    if (!image) {
      ctx.status = 404;
      ctx.body = { error: "Image not found" };
      return;
    }

    // Check for browser user-agent to determine if this is a direct request
    const userAgent = ctx.request.headers["user-agent"] || "";
    const isBrowser =
      userAgent.includes("Mozilla") ||
      userAgent.includes("Chrome") ||
      userAgent.includes("Safari") ||
      userAgent.includes("Edge") ||
      userAgent.includes("Firefox");

    if (isBrowser && ctx.query.direct !== "true") {
      try {
        // For browsers, proxy the image to maintain our domain in social shares
        const response = await axios.get(image.url, {
          responseType: "arraybuffer",
        });

        // Set appropriate headers
        ctx.set(
          "Content-Type",
          response.headers["content-type"] || "image/png"
        );
        ctx.set("Cache-Control", "public, max-age=31536000"); // 1 year cache
        ctx.set("Access-Control-Allow-Origin", "*");

        // Send the image data
        ctx.body = response.data;
      } catch (proxyError) {
        console.error("Error proxying image:", proxyError);
        // If proxying fails, fall back to redirect
        ctx.redirect(image.url);
      }
    } else {
      // For non-browser clients or when direct=true is specified, just redirect
      ctx.redirect(image.url);
    }
  } catch (error: any) {
    console.error("File serving error:", error);
    ctx.status = 500;
    ctx.body = { error: "Failed to serve file" };
  }
});

/**
 * @swagger
 * /shared-assets/{imageId}:
 *   get:
 *     summary: Serve an image by ID (legacy endpoint)
 *     tags: [Shared Assets]
 *     parameters:
 *       - in: path
 *         name: imageId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Image ID
 *       - in: query
 *         name: locationId
 *         schema:
 *           type: integer
 *         description: Location ID (optional)
 *     responses:
 *       302:
 *         description: Redirect to the direct image URL
 *       400:
 *         description: Invalid image ID
 *       404:
 *         description: Image not found
 *       500:
 *         description: Failed to serve file
 */
// Keep the get endpoint for backward compatibility
router.get("/:imageId", async (ctx) => {
  try {
    const imageId = parseInt(ctx.params.imageId, 10);
    if (isNaN(imageId)) {
      ctx.status = 400;
      ctx.body = { error: "Invalid image ID" };
      return;
    }

    // Use a default locationId or try to get it from query
    const locationId =
      ctx.state.locationId || parseInt(ctx.query.locationId as string, 10) || 1; // Default location ID

    // Find image using ImageService
    const image = await getImage(locationId, imageId);

    if (!image) {
      ctx.status = 404;
      ctx.body = { error: "Image not found" };
      return;
    }

    // Redirect to the direct URL instead of proxying
    ctx.redirect(image.url);
  } catch (error: any) {
    console.error("File serving error:", error);
    ctx.status = 500;
    ctx.body = { error: "Failed to serve file" };
  }
});

export default router;
