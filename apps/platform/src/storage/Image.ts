import Model from "../core/Model";
import Storage from "./Storage";

export default class Image extends Model {
  location_id!: number;
  uuid!: string;
  name!: string;
  original_name!: string;
  extension!: string;
  alt!: string;
  file_size!: number;
  path?: string; // Optional field for full storage path including folder structure

  get filename(): string {
    return `${this.uuid}${this.extension}`;
  }

  get url(): string {
    // Use the path if available (includes folder structure), otherwise fallback to filename
    return Storage.url(this.path || this.filename);
  }

  toJSON() {
    return {
      ...this,
      url: this.url,
    };
  }
}

export interface ImageParams {
  name: string;
  alt?: string;
}
