/**
 * @swagger
 * components:
 *   schemas:
 *     Image:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         location_id:
 *           type: integer
 *         name:
 *           type: string
 *         alt:
 *           type: string
 *           nullable: true
 *         url:
 *           type: string
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     ImageListResponse:
 *       type: object
 *       properties:
 *         data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Image'
 *         pagination:
 *           type: object
 *           properties:
 *             total:
 *               type: integer
 *             page:
 *               type: integer
 *             limit:
 *               type: integer
 *             pages:
 *               type: integer
 *     ImageUpdateRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *         alt:
 *           type: string
 *           nullable: true
 */

/**
 * @swagger
 * tags:
 *   name: Images
 *   description: Image management endpoints
 */

import Router from "@koa/router";
import { JSONSchemaType, validate } from "../core/validate";
import parse, { FileMetadata } from "./FileStream";
import {
  getImage,
  pagedImages,
  updateImage,
  uploadImage,
} from "./ImageService";
import Image, { ImageParams } from "./Image";
import { LocationState } from "../auth/AuthMiddleware";
import { extractQueryParams } from "../utilities";
import { SearchSchema } from "../core/searchParams";

const router = new Router<LocationState & { image?: Image }>({
  prefix: "/images",
});

const uploadMetadata: JSONSchemaType<FileMetadata> = {
  $id: "uploadMetadata",
  type: "object",
  required: ["fieldName", "fileName", "mimeType"],
  properties: {
    fieldName: {
      type: "string",
    },
    fileName: {
      type: "string",
    },
    mimeType: {
      type: "string",
      enum: ["image/jpeg", "image/gif", "image/png", "image/jpg", "image/webp"],
    },
    size: {
      type: "number",
    },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /images:
 *   post:
 *     summary: Upload a new image
 *     tags: [Images]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *     responses:
 *       200:
 *         description: Image uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Image'
 *       400:
 *         description: Invalid file format or size
 */
router.post("/", async (ctx) => {
  const stream = await parse(ctx);

  // Validate but we don't need the response since we already have it
  validate(uploadMetadata, stream.metadata);

  ctx.body = await uploadImage(ctx.state.location.id, stream);
});

/**
 * @swagger
 * /images:
 *   get:
 *     summary: List images (paginated)
 *     tags: [Images]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Sort field
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort direction
 *     responses:
 *       200:
 *         description: Paginated list of images
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ImageListResponse'
 */
router.get("/", async (ctx) => {
  const searchSchema = SearchSchema("imagesSearchSchema", {
    sort: "id",
    direction: "desc",
  });
  const params = extractQueryParams(ctx.query, searchSchema);
  ctx.body = await pagedImages(params, ctx.state.location.id);
});

router.param("imageId", async (value, ctx, next) => {
  ctx.state.image = await getImage(parseInt(value), ctx.state.location.id);
  if (!ctx.state.image) {
    ctx.throw(404);
    return;
  }
  return await next();
});

/**
 * @swagger
 * /images/{imageId}:
 *   get:
 *     summary: Get image by ID
 *     tags: [Images]
 *     parameters:
 *       - in: path
 *         name: imageId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Image ID
 *     responses:
 *       200:
 *         description: Image details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Image'
 *       404:
 *         description: Image not found
 */
router.get("/:imageId", async (ctx) => {
  ctx.body = ctx.state.image;
});

const imageUpdateMetadata: JSONSchemaType<ImageParams> = {
  $id: "imageUpdateMetadata",
  type: "object",
  required: ["name"],
  properties: {
    name: {
      type: "string",
    },
    alt: {
      type: "string",
      nullable: true,
    },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /images/{imageId}:
 *   patch:
 *     summary: Update image metadata
 *     tags: [Images]
 *     parameters:
 *       - in: path
 *         name: imageId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Image ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ImageUpdateRequest'
 *     responses:
 *       200:
 *         description: Image updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Image'
 *       404:
 *         description: Image not found
 */
router.patch("/:imageId", async (ctx) => {
  const payload = validate(imageUpdateMetadata, ctx.request.body);
  ctx.body = await updateImage(ctx.state.image!.id, payload);
});

export default router;
