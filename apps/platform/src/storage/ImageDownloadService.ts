import { Readable } from "stream";
import Storage from "./Storage";
import { uuid } from "../utilities";
import Image from "./Image";
import App from "../app";
import { logger } from "../config/logger";

export class ImageDownloadService {
  private _storage?: Storage;

  private get storage(): Storage {
    if (!this._storage) {
      this._storage = App.main.storage;
    }
    return this._storage;
  }

  async downloadAndStoreImage(
    locationId: number,
    imageUrl: string,
    name?: string,
    folderPath?: string
  ): Promise<Image> {
    try {
      logger.info(`Downloading image from URL: ${imageUrl}`);

      // Download the image
      const response = await fetch(imageUrl);
      if (!response.ok) {
        logger.error(
          `Failed to download image: ${response.statusText}, Status: ${response.status}`
        );
        throw new Error(
          `Failed to download image: ${response.statusText}, Status: ${response.status}`
        );
      }

      // Get content type and validate it's an image
      const contentType = response.headers.get("content-type");
      logger.info(`Image content type: ${contentType}`);

      if (!contentType?.startsWith("image/")) {
        logger.error(`Invalid content type: ${contentType}`);
        throw new Error(`Invalid content type: ${contentType}`);
      }

      // Generate a unique filename with the correct extension
      const extension = contentType.split("/")[1] || "jpg";
      const baseFilename = `${uuid()}.${extension}`;
      const filename = folderPath
        ? `${folderPath}/${baseFilename}`
        : baseFilename;
      logger.info(
        `Generated filename: ${filename}, Extension: ${extension}, Folder path: ${
          folderPath || "none"
        }`
      );

      if (!response.body) {
        logger.error("No image data received in response body");
        throw new Error("No image data received");
      }

      // Get the image data as a buffer
      try {
        const arrayBuffer = await response.arrayBuffer();
        const buffer = Buffer.from(arrayBuffer);
        logger.info(`Image buffer size: ${buffer.length} bytes`);

        // Upload the buffer directly
        try {
          logger.info(
            `Attempting to upload image to storage provider: ${this.storage.provider.constructor.name}`
          );
          await this.storage.upload({
            stream: Readable.from(buffer),
            url: this.storage.provider.path(filename),
          });
          logger.info(`Successfully uploaded image to storage`);
          console.log("uploaded image to storage");
        } catch (uploadError) {
          logger.error("Error uploading image to storage:", uploadError);
          logger.error("Storage provider details:", {
            provider: this.storage.provider.constructor.name,
          });
          console.log("uploadError", uploadError);
          throw uploadError;
        }

        // Create the image record
        const image = await Image.insertAndFetch({
          location_id: locationId,
          uuid: baseFilename.split(".")[0],
          name: name || baseFilename,
          original_name: name || baseFilename,
          extension: `.${extension}`,
          file_size: buffer.length,
          path: filename, // Store the full path including folder structure
        });

        logger.info(
          `Successfully stored image: ${image.uuid}, path: ${image.path}, url: ${image.url}`
        );
        return image;
      } catch (bufferError) {
        logger.error("Error processing image buffer:", bufferError);
        throw bufferError;
      }
    } catch (error) {
      logger.error("Error downloading and storing image:", error);
      // If we have a fallback image URL, we could return it here
      const defaultImageURL =
        "https://storage.googleapis.com/marketing-auto/defaults/default-hero.jpg";
      logger.info(`Falling back to default image: ${defaultImageURL}`);
      throw error;
    }
  }
}

export const imageDownloadService = new ImageDownloadService();
