import App from "../app";
import { snakeCase } from "../utilities";
import Image, { ImageParams } from "./Image";
import { FileStream } from "./FileStream";
import { PageParams } from "../core/searchParams";

export const uploadImage = async (
  locationId: number,
  stream: FileStream
): Promise<Image> => {
  const upload = await App.main.storage.save(stream);
  return await Image.insertAndFetch({
    location_id: locationId,
    name: upload.original_name ? snakeCase(upload.original_name) : "",
    ...upload,
  });
};

export const allImages = async (locationId: number): Promise<Image[]> => {
  return await Image.all((qb) => qb.where("location_id", locationId));
};

export const pagedImages = async (params: PageParams, locationId: number) => {
  return await Image.search({ ...params, fields: ["name"] }, (b) =>
    b.where("location_id", locationId)
  );
};

export const getImage = async (
  locationId: number,
  id: number
): Promise<Image | undefined> => {
  return await Image.find(id, (qb) => qb.where("location_id", locationId));
};

export const updateImage = async (
  id: number,
  params: ImageParams
): Promise<Image | undefined> => {
  return await Image.updateAndFetch(id, params);
};
