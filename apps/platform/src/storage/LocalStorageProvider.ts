import fs from "fs/promises";
import path from "path";
import { StorageTypeConfig } from "./Storage";
import { ImageUploadTask, StorageProvider } from "./StorageProvider";
import { createWriteStream } from "fs";

export interface LocalConfig extends StorageTypeConfig {
  driver: "local";
}

export class LocalStorageProvider implements StorageProvider {
  path(filename: string, userId?: string): string {
    // For local storage, we'll just use the filename
    const extension = filename.includes(".") ? "" : ".png";
    return path.join(process.cwd(), "public", "uploads", filename + extension);
  }

  async upload(task: ImageUploadTask) {
    // Determine the full file path
    let filePath: string;

    if (path.isAbsolute(task.url)) {
      // If URL is already an absolute path, use it directly
      filePath = task.url;
    } else {
      // If URL is relative, construct the full path within the uploads directory
      filePath = path.join(process.cwd(), "public", "uploads", task.url);
    }

    // Ensure the directory exists (create nested directories if needed)
    const fileDir = path.dirname(filePath);
    await fs.mkdir(fileDir, { recursive: true });

    // Create a write stream to the file
    const writeStream = createWriteStream(filePath);

    // Pipe the stream and handle events
    return new Promise<void>((resolve, reject) => {
      task.stream
        .pipe(writeStream)
        .on("finish", resolve)
        .on("error", (error) => {
          writeStream.destroy();
          reject(error);
        });
    });
  }

  async delete(filename: string): Promise<void> {
    // Determine the full file path
    let filePath: string;

    if (path.isAbsolute(filename)) {
      // If filename is already an absolute path, use it directly
      filePath = filename;
    } else {
      // If filename is relative, construct the full path within the uploads directory
      filePath = path.join(process.cwd(), "public", "uploads", filename);
    }

    await fs.unlink(filePath);
  }

  async download(filename: string): Promise<Buffer> {
    try {
      // Determine the full file path
      let filePath: string;

      if (path.isAbsolute(filename)) {
        // If filename is already an absolute path, use it directly
        filePath = filename;
      } else {
        // If filename is relative, construct the full path within the uploads directory
        filePath = path.join(process.cwd(), "public", "uploads", filename);
      }

      return await fs.readFile(filePath);
    } catch (error) {
      throw new Error(`Failed to download file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
