import { Context } from "koa";
import <PERSON><PERSON> from "busboy";
import { Stream } from "stream";
import { FileStream } from "./FileStream";
import { logger } from "../config/logger";
import { RequestError } from "../core/errors";

interface FormField {
  [key: string]: string;
}

interface MultipartFormData {
  fields: FormField;
  files: Record<string, FileStream>;
}

/**
 * Parse a multipart form data request to extract both form fields and multiple files
 * @param ctx The Koa context containing the request
 * @returns A promise that resolves to an object containing fields and files
 */
export default function parseMultipartForm(
  ctx: Context
): Promise<MultipartFormData> {
  console.log("parseMultipartForm: Starting to parse multipart form data");
  console.log("Content-Type header:", ctx.request.headers["content-type"]);

  return new Promise<MultipartFormData>((resolve, reject) => {
    // Check if the content type is multipart/form-data (more permissive check)
    const contentType = ctx.request.headers["content-type"] || "";
    if (!contentType.includes("multipart/form-data")) {
      console.log(
        "parseMultipartForm: Content type is not multipart/form-data"
      );
      return reject(
        new RequestError({
          message: "Content type must be multipart/form-data",
          code: 400,
          statusCode: 400,
        })
      );
    }

    console.log(
      "parseMultipartForm: Content type check passed, setting up Busboy"
    );

    const fields: FormField = {};
    const files: Record<string, FileStream> = {};
    let fileCount = 0;
    let fieldCount = 0;

    try {
      // Configure Busboy with the request headers
      const busboy = Busboy({
        headers: ctx.req.headers,
        preservePath: true, // Preserve the full path in the filename
      });
      console.log("parseMultipartForm: Busboy instance created");

      // Handle form fields
      busboy.on("field", (fieldname, val) => {
        console.log(`parseMultipartForm: Found field "${fieldname}"`);
        fields[fieldname] = val;
        fieldCount++;
      });

      // Handle file uploads
      busboy.on("file", (fieldname, file, info) => {
        const { filename, encoding, mimeType } = info;
        console.log(
          `parseMultipartForm: Found file "${filename}" with type ${mimeType} in field "${fieldname}"`
        );

        // Collect the file data in memory
        const chunks: Buffer[] = [];
        let fileSize = 0;

        file.on("data", (chunk) => {
          chunks.push(chunk);
          fileSize += chunk.length;
          console.log(
            `parseMultipartForm: Received ${chunk.length} bytes of file data`
          );
        });

        file.on("end", () => {
          // Create a new readable stream from the collected chunks
          const fileBuffer = Buffer.concat(chunks);
          const { Readable } = require("stream");
          const fileStream = new Readable();
          fileStream._read = () => {}; // Implement _read method
          fileStream.push(fileBuffer);
          fileStream.push(null); // Signal end of the stream

          // Create a FileStream object
          files[fieldname] = {
            file: fileStream,
            metadata: {
              fieldName: fieldname,
              fileName: filename || "unknown-filename",
              mimeType: mimeType || "application/octet-stream",
              size: fileSize,
            },
          };

          fileCount++;
          console.log(
            `parseMultipartForm: File ${filename} (${fileSize} bytes) processed and ready`
          );
        });

        // Handle file errors
        file.on("error", (err) => {
          console.log(
            `parseMultipartForm: Error processing file ${filename}:`,
            err
          );
          logger.error(`Error processing file ${filename}:`, err);
        });
      });

      // Handle completion
      busboy.on("finish", () => {
        console.log(
          `parseMultipartForm: Finished parsing form data. Found ${fileCount} files and ${fieldCount} fields`
        );
        logger.info(
          `Parsed ${fileCount} files and ${fieldCount} fields from multipart form`
        );
        resolve({ fields, files });
      });

      // Handle errors
      busboy.on("error", (err: any) => {
        console.log(`parseMultipartForm: Error during parsing:`, err);
        logger.error("Error parsing multipart form:", err);
        reject(
          new RequestError({
            message: `Error parsing form data: ${err.message}`,
            code: 400,
            statusCode: 400,
          })
        );
      });

      // Pipe the request to busboy for processing
      console.log("parseMultipartForm: Piping request to busboy");
      ctx.req.pipe(busboy);
    } catch (error: any) {
      console.log(`parseMultipartForm: Exception during setup:`, error);
      logger.error("Exception during multipart form parsing:", error);
      reject(
        new RequestError({
          message: `Failed to parse multipart form: ${error.message}`,
          code: 400,
          statusCode: 400,
        })
      );
    }
  });
}
