import { S3 } from "@aws-sdk/client-s3";
import { Upload } from "@aws-sdk/lib-storage";
import { PassThrough } from "stream";
import { AWSConfig } from "../core/aws";
import { StorageTypeConfig } from "./Storage";
import { ImageUploadTask, StorageProvider } from "./StorageProvider";
import App from "../app";
import { combineURLs } from "../utilities";
import { lookup } from "mime-types";

export interface S3Config extends StorageTypeConfig, AWSConfig {
  driver: "s3";
  bucket: string;
  endpoint: string;
  forcePathStyle: boolean;
}

export class S3StorageProvider implements StorageProvider {
  config: S3Config;

  constructor(config: S3Config) {
    this.config = config;
  }

  path(filename: string, userId?: string): string {
    const extension = filename.includes(".") ? "" : ".png";
    if (userId) {
      return `images/${userId}/${filename}${extension}`;
    }
    return filename + extension;
  }

  async upload(task: ImageUploadTask) {
    const pass = new PassThrough();
    const s3 = new S3(this.config);

    const promise = new Upload({
      client: s3,
      params: {
        Key: task.url,
        Body: pass,
        Bucket: this.config.bucket,
        Metadata: {
          creator: "BakedBot.ai",
          ...task.metadata,
        },
        ContentType: lookup(task.url) || "image/jpeg",
        CacheControl: "public, max-age=31536000", // Cache for 1 year
      },
    }).done();

    task.stream.pipe(pass);

    await promise;
  }

  async delete(filename: string): Promise<void> {
    const s3 = new S3(this.config);
    await s3.deleteObject({
      Bucket: this.config.bucket,
      Key: filename,
    });
  }

  async download(filename: string): Promise<Buffer> {
    try {
      const s3 = new S3(this.config);
      const response = await s3.getObject({
        Bucket: this.config.bucket,
        Key: filename,
      });

      if (!response.Body) {
        throw new Error('No data received from S3 download');
      }

      // Convert stream to buffer
      const chunks: Buffer[] = [];
      for await (const chunk of response.Body as any) {
        chunks.push(Buffer.from(chunk));
      }
      return Buffer.concat(chunks);
    } catch (error) {
      throw new Error(`Failed to download file from S3: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static url(path: string) {
    const config = App.main.env.storage as S3Config;
    return config.endpoint && config.forcePathStyle
      ? combineURLs([config.endpoint, config.bucket, path])
      : combineURLs([`https://${config.bucket}.s3.amazonaws.com`, path]);
  }
}
