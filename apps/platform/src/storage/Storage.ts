import { DriverConfig } from "../config/env";
import { FileStream } from "./FileStream";
import Image from "./Image";
import { S3Config, S3StorageProvider } from "./S3StorageProvider";
import {
  ImageUploadTask,
  StorageProvider,
  StorageProviderName,
} from "./StorageProvider";
import path from "path";
import { combineURLs, uuid } from "../utilities";
import { InternalError } from "../core/errors";
import StorageError from "./StorageError";
import App from "../app";
import { LocalConfig, LocalStorageProvider } from "./LocalStorageProvider";
import {
  FirebaseConfig,
  FirebaseStorageProvider,
} from "./FirebaseStorageProvider";
import {
  SupabaseConfig,
  SupabaseStorageProvider,
} from "./SupabaseStorageProvider";

export type StorageConfig =
  | S3Config
  | LocalConfig
  | FirebaseConfig
  | SupabaseConfig;
export interface StorageTypeConfig extends DriverConfig {
  driver: StorageProviderName;
  baseUrl?: string;
}

export interface ImageUpload {
  extension: string;
  userId?: string;
}

export default class Storage {
  provider: StorageProvider;

  constructor(config?: StorageConfig) {
    if (config?.driver === "s3") {
      this.provider = new S3StorageProvider(config);
    } else if (config?.driver === "local") {
      this.provider = new LocalStorageProvider();
    } else if (config?.driver === "firebase") {
      this.provider = new FirebaseStorageProvider(config);
    } else if (config?.driver === "supabase") {
      this.provider = new SupabaseStorageProvider(config);
    } else {
      throw new InternalError(StorageError.UndefinedStorageMethod);
    }
  }

  async save(
    image: FileStream & { userId?: string; prompt?: string }
  ): Promise<Partial<Image>> {
    const key = uuid();
    const originalPath = path.parse(image.metadata.fileName);
    const extension = originalPath.ext;
    const fileName = originalPath.name;
    const url = this.provider.path(`${key}${extension}`, image.userId);

    await this.upload({
      stream: image.file as any,
      url,
      metadata: image.prompt ? { prompt: image.prompt } : undefined,
    });

    return {
      uuid: key,
      original_name: fileName,
      extension,
      file_size: image.metadata.size,
    };
  }

  async upload(task: ImageUploadTask) {
    await this.provider.upload(task);
  }

  async download(filename: string): Promise<Buffer> {
    if (!this.provider.download) {
      throw new Error(`Download not supported for storage provider: ${this.provider.constructor.name}`);
    }
    return await this.provider.download(filename);
  }

  static url(path: string): string {
    // If an override is provided, utilize that
    if (App.main.env.storage.baseUrl) {
      return combineURLs([App.main.env.storage.baseUrl, path]);
    }

    // If we are using S3, provide a path based on endpoint if needed
    if (App.main.env.storage.driver === "s3") {
      return S3StorageProvider.url(path);
    }

    // If we are using Firebase, provide a Firebase Storage URL
    if (App.main.env.storage.driver === "firebase") {
      return FirebaseStorageProvider.url(path);
    }

    // If we are using Supabase, provide a Supabase Storage URL
    if (App.main.env.storage.driver === "supabase") {
      return SupabaseStorageProvider.url(path);
    }

    // Otherwise default back to local path
    return `/uploads/${path}`;
  }
}
