import { Stream } from "stream";

export type StorageProviderName = "s3" | "local" | "firebase" | "supabase";

export interface ImageUploadTask {
  stream: Stream;
  url: string;
  metadata?: {
    prompt?: string;
    [key: string]: any;
  };
}

export interface StorageProvider {
  path(filename: string, userId?: string): string;
  upload(task: ImageUploadTask): Promise<void>;
  delete(filename: string): Promise<void>;
  download?(filename: string): Promise<Buffer>;
}
