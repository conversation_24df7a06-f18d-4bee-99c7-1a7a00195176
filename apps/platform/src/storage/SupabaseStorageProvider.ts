import { createClient, SupabaseClient } from "@supabase/supabase-js";
import { Stream } from "stream";
import { ImageUploadTask, StorageProvider } from "./StorageProvider";
import { DriverConfig } from "../config/env";
import { uuid } from "../utilities";
import StorageError from "./StorageError";

export interface SupabaseConfig extends DriverConfig {
  driver: "supabase";
  url: string;
  key: string;
  bucket?: string;
  baseUrl?: string;
}

export class SupabaseStorageProvider implements StorageProvider {
  private client: SupabaseClient;
  private bucket: string;

  constructor(config: SupabaseConfig) {
    if (!config.url || !config.key) {
      throw new Error("Supabase URL and key are required");
    }

    this.client = createClient(config.url, config.key);
    this.bucket = config.bucket || "location-data";

    // Make sure the bucket exists
    this.initializeBucket();
  }

  private async initializeBucket() {
    try {
      // Check if bucket exists, create if not
      const { data: buckets } = await this.client.storage.listBuckets();
      const bucketExists = buckets?.some(
        (bucket) => bucket.name === this.bucket
      );

      if (!bucketExists) {
        // Create the bucket with public access
        await this.client.storage.createBucket(this.bucket, {
          public: true,
        });
      }
    } catch (error) {
      console.error("Error initializing Supabase storage bucket:", error);
    }
  }

  path(filename: string, userId?: string): string {
    const prefix = userId ? `users/${userId}` : "public";
    return `${prefix}/${filename}`;
  }

  async upload(task: ImageUploadTask): Promise<void> {
    try {
      // Convert the stream to a buffer
      const chunks: Buffer[] = [];
      for await (const chunk of task.stream as any) {
        chunks.push(Buffer.from(chunk));
      }
      const buffer = Buffer.concat(chunks);

      // Upload to Supabase
      const filePath = task.url;
      const { error } = await this.client.storage
        .from(this.bucket)
        .upload(filePath, buffer, {
          contentType: this.getContentType(filePath),
          upsert: true,
          cacheControl: "3600",
          ...(task.metadata && { metadata: task.metadata }),
        });

      if (error) {
        throw new Error(`Failed to upload file to Supabase: ${error.message}`);
      }
    } catch (error: any) {
      throw new Error(`Error uploading to Supabase: ${error.message}`);
    }
  }

  async delete(filename: string): Promise<void> {
    try {
      const { error } = await this.client.storage
        .from(this.bucket)
        .remove([filename]);

      if (error) {
        throw new Error(
          `Failed to delete file from Supabase: ${error.message}`
        );
      }
    } catch (error: any) {
      throw new Error(`Error deleting from Supabase: ${error.message}`);
    }
  }

  async download(filename: string): Promise<Buffer> {
    try {
      const { data, error } = await this.client.storage
        .from(this.bucket)
        .download(filename);

      if (error) {
        throw new Error(
          `Failed to download file from Supabase: ${error.message}`
        );
      }

      if (!data) {
        throw new Error('No data received from Supabase download');
      }

      // Convert Blob to Buffer
      const arrayBuffer = await data.arrayBuffer();
      return Buffer.from(arrayBuffer);
    } catch (error: any) {
      throw new Error(`Error downloading from Supabase: ${error.message}`);
    }
  }

  // Get content type based on file extension
  private getContentType(filePath: string): string {
    const extension = filePath.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "jpg":
      case "jpeg":
        return "image/jpeg";
      case "png":
        return "image/png";
      case "gif":
        return "image/gif";
      case "pdf":
        return "application/pdf";
      case "doc":
        return "application/msword";
      case "docx":
        return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
      case "xls":
        return "application/vnd.ms-excel";
      case "xlsx":
        return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
      case "json":
        return "application/json";
      case "txt":
        return "text/plain";
      default:
        return "application/octet-stream";
    }
  }

  static url(path: string, bucket?: string): string {
    const supabaseUrl = process.env.SUPABASE_URL;
    const bucketName = bucket || "location-data";
    if (!supabaseUrl) {
      throw new Error("Supabase URL is not defined");
    }
    return `${supabaseUrl}/storage/v1/object/public/${bucketName}/${path}`;
  }
}
