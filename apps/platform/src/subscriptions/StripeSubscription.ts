import { Data } from "ws";
import Model from "../core/Model";

export enum SubscriptionStatus {
  incomplete = "incomplete",
  incompleteExpired = "incomplete_expired",
  trailing = "trailing",
  active = "active",
  pastDue = "past_due",
  canceled = "canceled",
  unpaid = "unpaid",
  paused = "paused",
}

export default class StripeSubscription extends Model {
  organization_id!: number;
  subscription_id?: string;
  customer_id?: string;
  price_id?: string;
  product_id?: string;
  status!: SubscriptionStatus;
  current_period_start?: Date;
  current_period_end?: Date;
  canceled_at?: Date;
  trial_start?: Date;
  trial_end?: Date;
  quantity?: number; // Number of locations this subscription allows
}
