/**
 * @swagger
 * components:
 *   schemas:
 *     Subscription:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         location_id:
 *           type: integer
 *         name:
 *           type: string
 *         channel:
 *           type: string
 *           enum: [email, text, push, webhook]
 *         from_name:
 *           type: string
 *           nullable: true
 *         from_email:
 *           type: string
 *           nullable: true
 *         reply_to:
 *           type: string
 *           nullable: true
 *         cc:
 *           type: string
 *           nullable: true
 *         bcc:
 *           type: string
 *           nullable: true
 *         from_phone:
 *           type: string
 *           nullable: true
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     SubscriptionCreateRequest:
 *       type: object
 *       required:
 *         - name
 *         - channel
 *       properties:
 *         name:
 *           type: string
 *         channel:
 *           type: string
 *           enum: [email, text, push, webhook]
 *         from_name:
 *           type: string
 *           nullable: true
 *         from_email:
 *           type: string
 *           nullable: true
 *         reply_to:
 *           type: string
 *           nullable: true
 *         cc:
 *           type: string
 *           nullable: true
 *         bcc:
 *           type: string
 *           nullable: true
 *         from_phone:
 *           type: string
 *           nullable: true
 *     SubscriptionUpdateRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           nullable: true
 *         channel:
 *           type: string
 *           enum: [email, text, push, webhook]
 *           nullable: true
 *         from_name:
 *           type: string
 *           nullable: true
 *         from_email:
 *           type: string
 *           nullable: true
 *         reply_to:
 *           type: string
 *           nullable: true
 *         cc:
 *           type: string
 *           nullable: true
 *         bcc:
 *           type: string
 *           nullable: true
 *         from_phone:
 *           type: string
 *           nullable: true
 *     SubscriptionListResponse:
 *       type: object
 *       properties:
 *         data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Subscription'
 *         pagination:
 *           type: object
 *           properties:
 *             total:
 *               type: integer
 *             page:
 *               type: integer
 *             limit:
 *               type: integer
 *             pages:
 *               type: integer
 *     LocationCreationResponse:
 *       type: object
 *       properties:
 *         canCreate:
 *           type: boolean
 *         message:
 *           type: string
 *     SubscriptionCancelResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         error:
 *           type: string
 */

/**
 * @swagger
 * tags:
 *   name: Subscriptions
 *   description: Subscription management endpoints
 */

import Router, { RouterContext } from "@koa/router";
import App from "../app";
import { RequestError } from "../core/errors";
import { JSONSchemaType, validate } from "../core/validate";
import Subscription, {
  SubscriptionParams,
  SubscriptionState,
  SubscriptionUpdateParams,
  UserSubscription,
} from "./Subscription";
import {
  createSubscription,
  getStripeSubscriptionById,
  getSubscription,
  pagedSubscriptions,
  toggleSubscription,
  unsubscribe,
  updateSubscription,
  canCreateLocation,
} from "./SubscriptionService";
import SubscriptionError from "./SubscriptionError";
import { encodedLinkToParts } from "../render/LinkService";
import { LocationState } from "../auth/AuthMiddleware";
import { decodeHashid, extractQueryParams } from "../utilities";
import { searchParamsSchema } from "../core/searchParams";
import { locationRoleMiddleware } from "../locations/LocationService";
import { compileTemplate } from "../render";
import { getUser } from "../users/UserRepository";
import { User } from "users/User";
import Stripe from "stripe";
import StripeSubscription, { SubscriptionStatus } from "./StripeSubscription";

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string, {
  apiVersion: "2020-08-27" as any,
});

/**
 ***
 * Public routes for webhooks & unsubscribe links
 ***
 */
const publicRouter = new Router<{ app: App }>({
  prefix: "/unsubscribe",
});

interface EmailUnsubscribeParams {
  campaign_id: number;
  user_id: number;
}

export const emailUnsubscribeSchema: JSONSchemaType<EmailUnsubscribeParams> = {
  $id: "emailUnsubscribe",
  type: "object",
  required: ["campaign_id", "user_id"],
  properties: {
    campaign_id: {
      type: "integer",
    },
    user_id: {
      type: "integer",
    },
  },
  additionalProperties: false,
};

publicRouter.get("/email", async (ctx) => {
  const { user, campaign } = await encodedLinkToParts(ctx.URL);

  if (!user) throw new RequestError(SubscriptionError.UnsubscribeInvalidUser);
  if (!campaign) {
    throw new RequestError(SubscriptionError.UnsubscribeInvalidCampaign);
  }

  await unsubscribe(user.id, campaign.subscription_id);
  ctx.headers["content-type"] = "text/html";
  ctx.body = "<html><body><h3>You have been unsubscribed!</h3></body></html>";
});

publicRouter.post("/email", async (ctx) => {
  const { user, campaign } = await encodedLinkToParts(ctx.URL);

  if (!user) throw new RequestError(SubscriptionError.UnsubscribeInvalidUser);
  if (!campaign) {
    throw new RequestError(SubscriptionError.UnsubscribeInvalidCampaign);
  }

  await unsubscribe(user.id, campaign.subscription_id);
  ctx.status = 200;
});

/**
 ***
 * User-facing subscription preferences page
 ***
 */
const preferencesPage = new Router<{
  app: App;
  user?: User;
  subscriptions?: SubscriptionPreferencesArgs["subscriptions"];
}>({
  prefix: "/preferences/:encodedUserId",
});

preferencesPage.param("encodedUserId", async (value, ctx, next) => {
  const userId = decodeHashid(value);
  if (!userId) throw new RequestError(SubscriptionError.UnsubscribeInvalidUser);
  const user = await getUser(userId);
  if (!user) throw new RequestError(SubscriptionError.UnsubscribeInvalidUser);

  ctx.state.user = user;
  ctx.state.subscriptions = await UserSubscription.query()
    .select("subscriptions.id as id")
    .select("subscriptions.name as name")
    .select("state")
    .join("subscriptions", "subscription_id", "subscriptions.id")
    .where("user_id", user.id)
    .orderBy("subscriptions.name", "asc");

  return await next();
});

interface SubscriptionPreferencesArgs {
  url: string;
  subscriptions: Array<{
    id: number;
    name: string;
    state: SubscriptionState;
  }>;
  showUpdatedMessage?: boolean;
}

const subscriptionPreferencesTemplate =
  compileTemplate<SubscriptionPreferencesArgs>(`
<!DOCTYPE html>
<html lang="en">
    <head>
        <title>Subscription Preferences</title>
        <style>
            body {
                font-family: 'Inter', 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
                font-size: 15px;
                margin: 0;
                padding: 0;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }
            main {
                margin: 50px auto;
                padding: 15px;
                max-width: 500px;
            }
            label {
                display: block;
                margin-bottom: 10px;
            }
            input[type="submit"] {
                display: inline-block;
                padding: 10px 20px;
                border-radius: 8px;
                background-color: #151c2d;
                color: #fff;
                font-size: 15px;
                border: 0;
                cursor: pointer;
                margin-top: 15px;
            }
            .alert-success {
                background-color: #d1fadf;
                color: #039855;
                padding: 10px;
                margin: 10px 0;
                border-radius: 8px;
            }
        </style>
    </head>
    <body>
        <main>
            {{#if subscriptions}}
            <form action="{{url}}" method="post">
                <h1>Subscription Preferences</h1>
                <p>Choose which notifications you would like to continue to receive.</p>
                {{#if showUpdatedMessage}}
                <div class="alert-success">
                    Your preferences have been updated!
                </div>
                {{/if}}
                {{#each subscriptions}}
                <label>
                    <input
                        type="checkbox"
                        name="subscriptionIds"
                        value="{{this.id}}"
                        {{#ifEquals this.state 1}}checked{{/ifEquals}}
                    />
                    <span>
                        {{this.name}}
                    </span>
                </label>
                {{/each}}
                <input type="submit" value="Save Preferences" />
            </form>
            {{else}}
            <div>
                You are not subscribed to any notifications.
            </div>
            {{/if}}
        </main>
    </body>
</html>
`);

preferencesPage.get("/", async (ctx) => {
  ctx.headers["content-type"] = "text/html";
  ctx.body = subscriptionPreferencesTemplate({
    subscriptions: ctx.state.subscriptions ?? [],
    url: App.main.env.baseUrl + ctx.URL.pathname,
    showUpdatedMessage: ctx.query.u === "1",
  });
});

preferencesPage.post("/", async (ctx) => {
  const { subscriptionIds } = ctx.request.body;
  const ids =
    (Array.isArray(subscriptionIds)
      ? subscriptionIds
      : [subscriptionIds as string]
    )
      ?.map(Number)
      .filter((n) => !isNaN(n)) ?? [];
  for (const sub of ctx.state.subscriptions ?? []) {
    await toggleSubscription(
      ctx.state.user!.id,
      sub.id,
      ids.includes(sub.id)
        ? SubscriptionState.subscribed
        : SubscriptionState.unsubscribed
    );
  }
  return ctx.redirect(App.main.env.baseUrl + ctx.URL.pathname + "?u=1");
});

publicRouter.use(preferencesPage.routes(), preferencesPage.allowedMethods());

export { publicRouter };

/**
 ***
 * Client router for things like push which will come direct from
 * our client side libraries
 ***
 */
const clientRouter = new Router<{ app: App }>({
  prefix: "/unsubscribe",
});
clientRouter.post("/push", async (ctx) => {
  // TODO: Unsubscribe for push types
  // Since this is coming from a client it should probably
  // contain a token and may not belong here. How to
  // structure location for client endpoints is tricky

  ctx.status = 204;
});
export { clientRouter };

/**
 ***
 * Private admin routes for managing subscription types
 ***
 */
const router = new Router<LocationState & { subscription?: Subscription }>({
  prefix: "/subscriptions",
});

/**
 * @swagger
 * /subscriptions/{organizationId}:
 *   get:
 *     summary: Get subscription by organization ID
 *     tags: [Subscriptions]
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Organization ID
 *     responses:
 *       200:
 *         description: Subscription details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Subscription'
 *       404:
 *         description: Subscription not found
 *       500:
 *         description: Failed to retrieve subscription data
 */
router.get("/:organizationId", async (ctx) => {
  try {
    const organizationId = ctx.params.organizationId;

    console.log(`Handling request for organization: ${organizationId}`);
    const subscriptionData = await getStripeSubscriptionById(
      parseInt(organizationId)
    );
    if (!subscriptionData) {
      ctx.status = 404;
      ctx.body = { message: "Subscription not found" };
    } else {
      ctx.status = 200;
      ctx.body = subscriptionData;
    }
  } catch (error) {
    ctx.status = 500;
    ctx.body = { error: "Failed to retrieve subscription data" };
  }
});

/**
 * @swagger
 * /subscriptions:
 *   get:
 *     summary: List subscriptions (paginated)
 *     tags: [Subscriptions]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Sort field
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort direction
 *     responses:
 *       200:
 *         description: Paginated list of subscriptions
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SubscriptionListResponse'
 */
router.get("/", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await pagedSubscriptions(params, ctx.state.location.id);
});

export const subscriptionCreateSchema: JSONSchemaType<SubscriptionParams> = {
  $id: "subscriptionCreate",
  type: "object",
  required: ["name", "channel"],
  properties: {
    name: {
      type: "string",
    },
    channel: {
      type: "string",
      enum: ["email", "text", "push", "webhook"],
    },
    from_name: {
      type: "string",
      nullable: true,
    },
    from_email: {
      type: "string",
      nullable: true,
    },
    reply_to: {
      type: "string",
      nullable: true,
    },
    cc: {
      type: "string",
      nullable: true,
    },
    bcc: {
      type: "string",
      nullable: true,
    },
    from_phone: {
      type: "string",
      nullable: true,
    },
  },
  additionalProperties: false,
};

/**
 * @swagger
 * /subscriptions:
 *   post:
 *     summary: Create a new subscription
 *     tags: [Subscriptions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SubscriptionCreateRequest'
 *     responses:
 *       200:
 *         description: Subscription created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Subscription'
 *       401:
 *         description: Unauthorized - Admin role required
 */
router.post("/", locationRoleMiddleware("admin"), async (ctx) => {
  const payload = validate(subscriptionCreateSchema, ctx.request.body);
  ctx.body = await createSubscription(ctx.state.location.id, payload);
});

router.param("subscriptionId", async (value, ctx, next) => {
  ctx.state.subscription = await getSubscription(
    parseInt(value),
    ctx.state.location.id
  );
  if (!ctx.state.subscription) {
    ctx.throw(404);
    return;
  }
  return await next();
});

/**
 * @swagger
 * /subscriptions/{subscriptionId}:
 *   get:
 *     summary: Get subscription by ID
 *     tags: [Subscriptions]
 *     parameters:
 *       - in: path
 *         name: subscriptionId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Subscription ID
 *     responses:
 *       200:
 *         description: Subscription details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Subscription'
 *       404:
 *         description: Subscription not found
 */
router.get("/:subscriptionId", async (ctx) => {
  ctx.body = ctx.state.subscription;
});

export const subscriptionUpdateSchema: JSONSchemaType<SubscriptionUpdateParams> =
  {
    $id: "subscriptionUpdate",
    type: "object",
    required: [],
    properties: {
      name: {
        type: "string",
        nullable: true,
      },
      channel: {
        type: "string",
        enum: ["email", "text", "push", "webhook"],
        nullable: true,
      },
      from_name: {
        type: "string",
        nullable: true,
      },
      from_email: {
        type: "string",
        nullable: true,
      },
      reply_to: {
        type: "string",
        nullable: true,
      },
      cc: {
        type: "string",
        nullable: true,
      },
      bcc: {
        type: "string",
        nullable: true,
      },
      from_phone: {
        type: "string",
        nullable: true,
      },
    },
    additionalProperties: false,
  };
/**
 * @swagger
 * /subscriptions/{subscriptionId}:
 *   patch:
 *     summary: Update subscription
 *     tags: [Subscriptions]
 *     parameters:
 *       - in: path
 *         name: subscriptionId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Subscription ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SubscriptionUpdateRequest'
 *     responses:
 *       200:
 *         description: Subscription updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Subscription'
 *       404:
 *         description: Subscription not found
 */
router.patch("/:subscriptionId", async (ctx) => {
  const payload = validate(subscriptionUpdateSchema, ctx.request.body);
  ctx.body = await updateSubscription(ctx.state.subscription!.id, payload);
});

/**
 * @swagger
 * /subscriptions/can-create-location/{organizationId}:
 *   get:
 *     summary: Check if organization can create more locations
 *     tags: [Subscriptions]
 *     parameters:
 *       - in: path
 *         name: organizationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Organization ID
 *     responses:
 *       200:
 *         description: Location creation eligibility check result
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LocationCreationResponse'
 *       400:
 *         description: Invalid organization ID
 *       403:
 *         description: Unauthorized access to organization
 *       500:
 *         description: Failed to check location creation eligibility
 */
router.get("/can-create-location/:organizationId", async (ctx) => {
  try {
    const organizationId = parseInt(ctx.params.organizationId);
    if (isNaN(organizationId)) {
      ctx.status = 400;
      ctx.body = { message: "Invalid organization ID" };
      return;
    }

    if (ctx.state.admin && ctx.state.admin.organization_id !== organizationId) {
      ctx.status = 403;
      ctx.body = {
        message: "You don't have permission to access this organization",
      };
      return;
    }

    const canCreate = await canCreateLocation(organizationId);

    ctx.status = 200;
    ctx.body = {
      canCreate,
      message: canCreate
        ? "Organization can create more locations"
        : "Subscription limit reached. Please purchase additional subscriptions to create more locations.",
    };
  } catch (error) {
    ctx.status = 500;
    ctx.body = { error: "Failed to check location creation eligibility" };
  }
});

// Add endpoint to cancel a Stripe subscription
/**
 * @swagger
 * /subscriptions/cancel/{subscriptionId}:
 *   post:
 *     summary: Cancel a subscription
 *     tags: [Subscriptions]
 *     parameters:
 *       - in: path
 *         name: subscriptionId
 *         required: true
 *         schema:
 *           type: string
 *         description: Stripe subscription ID
 *     responses:
 *       200:
 *         description: Subscription cancellation initiated
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SubscriptionCancelResponse'
 *       400:
 *         description: Subscription ID is required
 *       403:
 *         description: Unauthorized to cancel subscription
 *       500:
 *         description: Failed to cancel subscription
 */
router.post("/cancel/:subscriptionId", async (ctx) => {
  try {
    const { subscriptionId } = ctx.params;

    if (!subscriptionId) {
      ctx.status = 400;
      ctx.body = { error: "Subscription ID is required" };
      return;
    }

    // Check if the user has permission to cancel this subscription
    // We would ideally check if this subscription belongs to their organization
    if (
      ctx.state.admin &&
      ctx.state.admin.role !== "owner" &&
      ctx.state.admin.role !== "superAdmin"
    ) {
      ctx.status = 403;
      ctx.body = {
        error: "You don't have permission to cancel this subscription",
      };
      return;
    }

    // Call Stripe API to cancel the subscription at period end
    const canceledSubscription = await stripe.subscriptions.update(
      subscriptionId,
      {
        cancel_at_period_end: true,
      }
    );

    // Update our database record
    if (canceledSubscription) {
      await StripeSubscription.update(
        (qb: any) => qb.where({ subscription_id: subscriptionId }),
        {
          status: SubscriptionStatus.canceled,
          canceled_at: new Date(),
        }
      );
    }

    ctx.status = 200;
    ctx.body = {
      success: true,
      message:
        "Subscription will be canceled at the end of the current billing period",
    };
  } catch (error) {
    console.error("Error canceling subscription:", error);
    ctx.status = 500;
    ctx.body = { error: "Failed to cancel subscription" };
  }
});

// Add endpoint to get a single Stripe subscription by ID
router.get("/:subscriptionId", async (ctx) => {
  try {
    const { subscriptionId } = ctx.params;

    if (!subscriptionId) {
      ctx.status = 400;
      ctx.body = { error: "Subscription ID is required" };
      return;
    }

    // Get subscription from Stripe
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    ctx.status = 200;
    ctx.body = subscription;
  } catch (error) {
    console.error("Error retrieving subscription:", error);
    ctx.status = 500;
    ctx.body = { error: "Failed to retrieve subscription" };
  }
});

export default router;
