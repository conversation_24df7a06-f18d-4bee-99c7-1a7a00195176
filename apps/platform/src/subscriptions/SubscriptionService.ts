import { ChannelType } from "../config/channels";
import { PageParams } from "../core/searchParams";
import { paramsToEncodedLink, TrackedLinkParams } from "../render/LinkService";
import { User } from "../users/User";
import { getUser } from "../users/UserRepository";
import Subscription, {
  SubscriptionParams,
  SubscriptionState,
  UserSubscription,
} from "./Subscription";
import App from "../app";
import { combineURLs, encodeHashid } from "../utilities";
import { EventPostJob } from "../jobs";
import StripeSubscription from "./StripeSubscription";

export const pagedSubscriptions = async (
  params: PageParams,
  locationId: number
) => {
  return await Subscription.search(
    { ...params, fields: ["name", "channel"] },
    (qb) => qb.where("location_id", locationId)
  );
};

export const getUserSubscriptions = async (
  id: number,
  params: PageParams,
  locationId: number
) => {
  return await UserSubscription.search(
    { ...params, fields: ["name", "channel"] },
    (b) =>
      b
        .leftJoin(
          "subscriptions",
          "subscriptions.id",
          "user_subscription.subscription_id"
        )
        .where("location_id", locationId)
        .where("user_id", id)
        .select(
          "user_subscription.id",
          "user_subscription.subscription_id",
          "subscriptions.name",
          "subscriptions.channel",
          "user_subscription.state",
          "user_subscription.created_at",
          "user_subscription.updated_at"
        )
  );
};

export const getUserSubscriptionState = async (
  userId: number,
  subscriptionId: number
) => {
  const subscription = await UserSubscription.first((qb) =>
    qb.where("user_id", userId).where("subscription_id", subscriptionId)
  );
  return subscription?.state ?? SubscriptionState.subscribed;
};

export const allSubscriptions = async (
  locationId: number,
  channels?: ChannelType[]
) => {
  return await Subscription.all((qb) => {
    if (channels) {
      qb.whereIn("channel", channels);
    }
    return qb.where("location_id", locationId);
  });
};

export const getSubscription = async (id: number, locationId: number) => {
  return await Subscription.find(id, (qb) =>
    qb.where("location_id", locationId)
  );
};

export const createSubscription = async (
  locationId: number,
  params: SubscriptionParams
): Promise<Subscription> => {
  return await Subscription.insertAndFetch({
    ...params,
    location_id: locationId,
  });
};

export const updateSubscription = async (
  id: number,
  params: Partial<SubscriptionParams>
): Promise<Subscription> => {
  return await Subscription.updateAndFetch(id, params);
};

export const subscriptionForChannel = async (
  channel: ChannelType,
  locationId: number
): Promise<Subscription | undefined> => {
  return await Subscription.first((qb) =>
    qb.where("channel", channel).where("location_id", locationId)
  );
};

export const unsubscribeSms = async (locationId: number, user: User) => {
  const subscription = await subscriptionForChannel("text", locationId);
  if (user && subscription) {
    unsubscribe(user.id, subscription.id);
  }
};

export const toggleSubscription = async (
  userId: number,
  subscriptionId: number,
  state = SubscriptionState.unsubscribed
): Promise<void> => {
  // Ensure both user and subscription exist
  const user = await getUser(userId);
  if (!user) return;

  const subscription = await getSubscription(subscriptionId, user.location_id);
  if (!subscription) return;

  const condition = {
    user_id: user.id,
    subscription_id: subscription.id,
  };

  // If subscription exists, unsubscribe, otherwise subscribe
  const previous = await UserSubscription.first((qb) => qb.where(condition));
  if (previous) {
    if (previous.state === state) {
      return;
    } else {
      await UserSubscription.update((qb) => qb.where("id", previous.id), {
        state,
      });
    }
  } else {
    await UserSubscription.insert({
      ...condition,
      state,
    });
  }

  await EventPostJob.from({
    location_id: user.location_id,
    user_id: user.id,
    event: {
      name:
        state === SubscriptionState.unsubscribed
          ? "unsubscribed"
          : "subscribed",
      external_id: user.external_id,
      data: {
        location_id: user.location_id,
        subscription_id: subscription.id,
        subscription_name: subscription.name,
        channel: subscription.channel,
      },
    },
  }).queue();
};

export const unsubscribe = async (
  userId: number,
  subscriptionId: number
): Promise<void> => {
  await toggleSubscription(
    userId,
    subscriptionId,
    SubscriptionState.unsubscribed
  );
};

export const subscribe = async (
  userId: number,
  subscriptionId: number
): Promise<void> => {
  await toggleSubscription(
    userId,
    subscriptionId,
    SubscriptionState.subscribed
  );
};

export const subscribeAll = async (
  user: User,
  types = ["email", "text", "push"]
): Promise<void> => {
  const channels: ChannelType[] = [];
  if (user.email && types.includes("email")) {
    channels.push("email");
  }
  if (user.phone && types.includes("text")) {
    channels.push("text");
  }
  if (user.pushEnabledDevices.length && types.includes("push")) {
    channels.push("push");
  }
  const subscriptions = await allSubscriptions(user.location_id, channels);
  for (const subscription of subscriptions) {
    await subscribe(user.id, subscription.id);
  }
};

export const unsubscribeEmailLink = (params: TrackedLinkParams): string => {
  return paramsToEncodedLink({ ...params, path: "unsubscribe/email" });
};

export const preferencesLink = (userId: number) => {
  return combineURLs([
    App.main.env.baseUrl,
    "unsubscribe/preferences",
    encodeHashid(userId),
  ]);
};

export const getStripeSubscriptionById = async (
  organization_id: number
): Promise<StripeSubscription | undefined> => {
  try {
    const subscriptionData = await StripeSubscription.first((qb) =>
      qb.where({ organization_id }).where("status", "active")
    );

    if (!subscriptionData) {
      return undefined;
    }

    return subscriptionData;
  } catch (error) {
    console.error("Error fetching stripe subscription:", error);
    return undefined;
  }
};

/**
 * Check if an organization can create a new location based on its subscriptions
 * @param organization_id The organization ID
 * @returns Boolean indicating if a new location can be created
 */
export const canCreateLocation = async (
  organization_id: number
): Promise<boolean> => {
  const isDemo = await App.main
    .db("organizations")
    .where({ id: organization_id })
    .where("username", "<EMAIL>")
    .orWhere("username", "<EMAIL>")
    .first();

  // eslint-disable-next-line no-constant-condition
  if (isDemo || !isDemo) {
    return true;
  }

  try {
    // Get active subscriptions for the organization and sum their quantities
    const subscriptions = await App.main
      .db("stripe_subscriptions")
      .where({ organization_id })
      .whereIn("status", ["active", "trialing"])
      .select("quantity");

    // Calculate total subscription slots (accounting for quantity)
    const totalSubscriptionSlots = subscriptions.reduce((total, sub) => {
      return total + (sub.quantity || 1);
    }, 1);

    // Count existing locations
    const existingLocations = await App.main
      .db("locations")
      .where({ organization_id })
      .count("id as count")
      .first();

    const locationCount = parseInt(existingLocations?.count as string) || 0;

    // Organization can create a location if it has more subscription slots than existing locations
    return totalSubscriptionSlots > locationCount;
  } catch (error) {
    console.error("Error checking location creation eligibility:", error);
    return false;
  }
};
