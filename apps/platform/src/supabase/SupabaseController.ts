/**
 * @swagger
 * components:
 *   schemas:
 *     SupabaseUploadRequest:
 *       type: object
 *       required:
 *         - data_sources
 *       properties:
 *         data_sources:
 *           type: object
 *           properties:
 *             documents:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   documentId:
 *                     type: string
 *                   content:
 *                     type: string
 *                   processed:
 *                     type: boolean
 *                   ingestionTimestamp:
 *                     type: string
 *                     format: date-time
 *                   documentIndex:
 *                     type: integer
 *     SupabaseUploadResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         tableName:
 *           type: string
 *         recordCount:
 *           type: integer
 *         error:
 *           type: string
 *     SupabaseStatusResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         status:
 *           type: object
 *           additionalProperties: true
 *         error:
 *           type: string
 */

/**
 * @swagger
 * tags:
 *   name: Supabase
 *   description: Supabase integration endpoints
 */

import Router from "@koa/router";
import { requireLocationRole } from "../locations/LocationService";
import { SupabaseService } from "./SupabaseService";

const router = new Router();

/**
 * @swagger
 * /supabase/upload:
 *   post:
 *     summary: Upload data to Supabase
 *     tags: [Supabase]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SupabaseUploadRequest'
 *     responses:
 *       200:
 *         description: Data uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SupabaseUploadResponse'
 *       401:
 *         description: Unauthorized - Admin role required
 *       500:
 *         description: Failed to upload data
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SupabaseUploadResponse'
 */
router.post("/upload", async (ctx: any) => {
  requireLocationRole(ctx, "admin");
  const { location } = ctx.state;
  const payload = ctx.request.body;

  try {
    // Initialize the Supabase service
    const supabaseService = new SupabaseService({
      url: process.env.SUPABASE_URL || "",
      key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
      bucket: process.env.SUPABASE_BUCKET || "location-data",
    });

    // Process document metadata if present
    if (
      payload.data_sources.documents &&
      payload.data_sources.documents.length > 0
    ) {
      console.log(
        `Processing ${payload.data_sources.documents.length} document references`
      );

      // For documents coming from the UI (not the document service), ensure they have unique IDs
      payload.data_sources.documents = payload.data_sources.documents.map(
        (doc: any, index: any) => {
          // Make sure we preserve any existing document content
          const content = doc.content || "";

          return {
            ...doc,
            processed: true,
            ingestionTimestamp: new Date().toISOString(),
            documentIndex: index,
            // Ensure documents have a documentId if they don't already
            documentId: doc.documentId || `doc_${Date.now()}_${index}`,
            // Keep content field if present to ensure search capability
            content,
          };
        }
      );
    }

    // Upload the data to Supabase
    const result = await supabaseService.uploadLocationData(
      location.id,
      payload.data_sources
    );

    ctx.body = {
      success: true,
      tableName: result.tableName,
      recordCount: result.recordCount,
    };
  } catch (error: any) {
    console.error("Error uploading to Supabase:", error);
    ctx.body = {
      success: false,
      error: error.message || "Failed to upload data to Supabase",
    };
  }
});

/**
 * @swagger
 * /supabase/status:
 *   get:
 *     summary: Get Supabase integration status
 *     tags: [Supabase]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Integration status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SupabaseStatusResponse'
 *       401:
 *         description: Unauthorized - Admin role required
 *       500:
 *         description: Failed to get integration status
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SupabaseStatusResponse'
 */
router.get("/status", async (ctx: any) => {
  requireLocationRole(ctx, "admin");
  const { location } = ctx.state;

  try {
    // Initialize the Supabase service
    const supabaseService = new SupabaseService({
      url: process.env.SUPABASE_URL || "",
      key: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
      bucket: process.env.SUPABASE_BUCKET || "location-data",
    });

    // Get the integration status
    const status = await supabaseService.getIntegrationStatus(location.id);

    ctx.body = {
      success: true,
      status,
    };
  } catch (error: any) {
    console.error("Error getting Supabase status:", error);
    ctx.body = {
      success: false,
      error: error.message || "Failed to get Supabase status",
    };
  }
});

export default router;
