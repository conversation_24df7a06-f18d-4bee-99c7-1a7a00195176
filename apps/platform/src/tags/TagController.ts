/**
 * @swagger
 * components:
 *   schemas:
 *     Tag:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *         location_id:
 *           type: integer
 *         name:
 *           type: string
 *         created_at:
 *           type: string
 *           format: date-time
 *         updated_at:
 *           type: string
 *           format: date-time
 *     TagListResponse:
 *       type: object
 *       properties:
 *         data:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/Tag'
 *         pagination:
 *           type: object
 *           properties:
 *             total:
 *               type: integer
 *             page:
 *               type: integer
 *             limit:
 *               type: integer
 *             pages:
 *               type: integer
 *     TagCreateRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *     TagUpdateRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 */

/**
 * @swagger
 * tags:
 *   name: Tags
 *   description: Tag management endpoints
 */

import Router from "@koa/router";
import { JSONSchemaType } from "ajv";
import { searchParamsSchema } from "../core/searchParams";
import { validate } from "../core/validate";
import { extractQueryParams } from "../utilities";
import { LocationState } from "../auth/AuthMiddleware";
import { Tag, TagParams } from "./Tag";
import { getUsedTags } from "./TagService";
import { locationRoleMiddleware } from "../locations/LocationService";

const router = new Router<
  LocationState & {
    tag?: Tag;
  }
>({
  prefix: "/tags",
});

/**
 * @swagger
 * /tags:
 *   get:
 *     summary: List tags (paginated)
 *     tags: [Tags]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Sort field
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort direction
 *     responses:
 *       200:
 *         description: Paginated list of tags
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/TagListResponse'
 */
router.get("/", async (ctx) => {
  const params = extractQueryParams(ctx.request.query, searchParamsSchema);
  ctx.body = await Tag.search({ ...params, fields: ["name"] }, (qb) =>
    qb.where("location_id", ctx.state.location!.id)
  );
});

/**
 * @swagger
 * /tags/all:
 *   get:
 *     summary: List all tags (non-paginated)
 *     tags: [Tags]
 *     responses:
 *       200:
 *         description: List of all tags
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Tag'
 */
router.get("/all", async (ctx) => {
  ctx.body = await Tag.all((q) =>
    q.where("location_id", ctx.state.location!.id).orderBy("name", "asc")
  );
});

/**
 * @swagger
 * /tags/used/{entity}:
 *   get:
 *     summary: Get used tags for a specific entity
 *     tags: [Tags]
 *     parameters:
 *       - in: path
 *         name: entity
 *         required: true
 *         schema:
 *           type: string
 *         description: Entity type (e.g., 'products', 'users')
 *     responses:
 *       200:
 *         description: List of used tags for the entity
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Tag'
 */
router.get("/used/:entity", async (ctx) => {
  ctx.body = await getUsedTags(ctx.state.location!.id, ctx.params.entity);
});

const tagParams: JSONSchemaType<TagParams> = {
  $id: "tagParams",
  type: "object",
  required: ["name"],
  properties: {
    name: {
      type: "string",
    },
  },
};

/**
 * @swagger
 * /tags:
 *   post:
 *     summary: Create a new tag
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TagCreateRequest'
 *     responses:
 *       200:
 *         description: Tag created or retrieved if already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Tag'
 *       401:
 *         description: Unauthorized - Editor role required
 */
router.post("/", locationRoleMiddleware("editor"), async (ctx) => {
  const params = validate(tagParams, ctx.request.body);

  // First check if tag already exists
  const existingTag = await Tag.first((b) =>
    b.where({
      location_id: ctx.state.location!.id,
      name: params.name,
    })
  );

  if (existingTag) {
    ctx.body = existingTag;
    return;
  }

  // If tag doesn't exist, create it
  ctx.body = await Tag.insertAndFetch({
    location_id: ctx.state.location!.id,
    ...validate(tagParams, ctx.request.body),
  });
});

router.param("tagId", async (value, ctx, next) => {
  ctx.state.tag = await Tag.first((b) =>
    b.where({
      location_id: ctx.state.location.id,
      id: value,
    })
  );
  if (!ctx.state.tag) {
    return ctx.throw(404);
  }
  return await next();
});

/**
 * @swagger
 * /tags/{tagId}:
 *   get:
 *     summary: Get tag by ID
 *     tags: [Tags]
 *     parameters:
 *       - in: path
 *         name: tagId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Tag ID
 *     responses:
 *       200:
 *         description: Tag details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Tag'
 *       404:
 *         description: Tag not found
 */
router.get("/:tagId", async (ctx) => {
  ctx.body = ctx.state.tag!;
});

/**
 * @swagger
 * /tags/{tagId}:
 *   patch:
 *     summary: Update tag
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tagId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Tag ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TagUpdateRequest'
 *     responses:
 *       200:
 *         description: Tag updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Tag'
 *       401:
 *         description: Unauthorized - Editor role required
 *       404:
 *         description: Tag not found
 */
router.patch("/:tagId", locationRoleMiddleware("editor"), async (ctx) => {
  ctx.body = await Tag.updateAndFetch(
    ctx.state.tag!.id,
    validate(tagParams, ctx.request.body)
  );
});

/**
 * @swagger
 * /tags/{tagId}:
 *   delete:
 *     summary: Delete tag
 *     tags: [Tags]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: tagId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Tag ID
 *     responses:
 *       200:
 *         description: Tag deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: boolean
 *       401:
 *         description: Unauthorized - Editor role required
 *       404:
 *         description: Tag not found
 */
router.delete("/:tagId", locationRoleMiddleware("editor"), async (ctx) => {
  await Tag.delete((b) => b.where("id", ctx.state.tag!.id));
  ctx.body = true;
});

export default router;
