import { SmokeyAIService } from "../chats/SmokeyAIService";
import { OpenAI } from "openai";
import knex from "knex";
import dotenv from "dotenv";

// Load environment variables
dotenv.config();

// Setup OpenAI client
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

// Setup database connection
const db = knex({
  client: "mysql2",
  connection: {
    host: process.env.DB_HOST || "localhost",
    port: Number(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || "root",
    password: process.env.DB_PASSWORD || "",
    database: process.env.DB_NAME || "cannabis_platform",
  },
});

// Mock Pinecone client
const mockPineconeClient = {
  index: (indexName: string) => ({
    namespace: (namespace: string) => ({
      query: async (params: any) => ({
        matches: [
          {
            id: "doc1",
            score: 0.92,
            metadata: {
              text: "Cannabis flower products typically contain 15-25% THC and are available in various strains that offer different effects.",
              source: "Product Knowledge Base",
            },
          },
          {
            id: "doc2",
            score: 0.87,
            metadata: {
              text: "Michigan regulations require all cannabis products to be tested for potency, heavy metals, pesticides, and microbial contaminants.",
              source: "Regulatory Guidelines",
            },
          },
        ],
      }),
    }),
  }),
};

async function runTest() {
  try {
    console.log("Initializing SmokeyAIService...");

    // Initialize the service
    const smokeyService = new SmokeyAIService(openai, db, {
      defaultModel: "gpt-3.5-turbo", // Use a faster, cheaper model for testing
      pineconeClient: mockPineconeClient,
      pineconeIndexName: "test-index",
    });

    // Get the default agent
    const agent = smokeyService.getDefaultAgent();
    console.log(`Using agent: ${agent.name} (${agent.role})`);

    // Mock chat and message
    const chat = {
      chat_id: "test-chat-123",
      location_id: 1, // This should match a real location in your database
    };

    // Test different query types
    const queries = [
      "What are your top-selling products this month?",
      "Tell me about the regulations for cannabis edibles in Michigan",
      "How do our prices compare to competitors?",
      "What is the profit margin for pre-rolls vs flower?",
    ];

    // Process each query
    for (const query of queries) {
      console.log("\n========================================");
      console.log(`QUERY: ${query}`);
      console.log("========================================");

      const message = {
        content: query,
        role: "user",
      };

      const response = await smokeyService.generateResponse(
        chat,
        message,
        agent
      );
      console.log("RESPONSE:");
      console.log(response);
    }

    // Generate a title for the chat
    const messages = queries.map((q) => ({ content: q, role: "user" }));
    const title = await smokeyService.generateTitle(messages);
    console.log("\nGenerated chat title:", title);

    // Cleanup
    await smokeyService.cleanup();
    await db.destroy();

    console.log("\nTest completed successfully");
  } catch (error) {
    console.error("Test failed:", error);
    process.exit(1);
  }
}

// Run the test
runTest();
