import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, RequestContext } from "./interfaces";
import { logger } from "../config/logger";
import { z } from "zod";
import { SchemaAwareSegmentationService } from "./segmentation/SchemaAwareSegmentationService";

// Basic validation schema for analysis params
const analysisParamsSchema = z.object({
  analysis_type: z.enum([
    "sales_trends",
    "sales_forecast",
    "customer_segmentation",
    "price_elasticity",
    "basket_analysis",
    "product_performance",
  ]),
  location_id: z.number(),
  start_date: z.string(),
  end_date: z.string(),
  interval: z.enum(["day", "week", "month", "quarter", "year"]).optional(),
  category: z.string().optional(),
  additional_params: z.record(z.string(), z.any()).optional(),
});

// Enhanced interface for time-series analysis results
interface TimeSeriesPoint {
  date: string;
  value?: number;
  sales: number;
  netSales?: number;
  transactions: number;
  category?: string;
}

// Customer data interface
interface CustomerData {
  customer_name: string;
  total_spend: string;
  visit_count: string;
  avg_transaction: string;
}

// Segment interface
interface CustomerSegment {
  customers: CustomerData[];
  spend: number;
  visits: number;
}

// Product data interface
interface ProductData {
  productName: string;
  category: string;
  sales: number;
  profit: number;
  unitsSold: number;
  profitMargin: number;
}

export class AnalysisTool implements Tool {
  name = "analysis.run";
  description = "Run various analyses on sales, customer, and product data";

  parameters: ToolParameter[] = [
    {
      name: "analysis_type",
      description:
        "Type of analysis to run (sales_trends, sales_forecast, customer_segmentation, price_elasticity, basket_analysis, product_performance)",
      type: "string",
      required: true,
      enum: [
        "sales_trends",
        "sales_forecast",
        "customer_segmentation",
        "price_elasticity",
        "basket_analysis",
        "product_performance",
      ],
    },
    {
      name: "location_id",
      description: "Location ID to analyze",
      type: "number",
      required: true,
    },
    {
      name: "start_date",
      description: "Start date for analysis (YYYY-MM-DD)",
      type: "string",
      required: true,
    },
    {
      name: "end_date",
      description: "End date for analysis (YYYY-MM-DD)",
      type: "string",
      required: true,
    },
    {
      name: "interval",
      description: "Time interval for analysis",
      type: "string",
      required: false,
      enum: ["day", "week", "month", "quarter", "year"],
    },
    {
      name: "category",
      description: "Product category to filter by",
      type: "string",
      required: false,
    },
    {
      name: "additional_params",
      description: "Additional parameters specific to the analysis type",
      type: "object",
      required: false,
    },
  ];

  private analysisExecutors: Map<string, (params: any) => Promise<any>>;
  private dataHubTool: any;
  private schemaAwareSegmentationService: SchemaAwareSegmentationService;

  constructor(dataHubTool: any) {
    this.dataHubTool = dataHubTool;
    this.analysisExecutors = new Map();
    this.schemaAwareSegmentationService = new SchemaAwareSegmentationService(dataHubTool);

    // Register analysis executors
    this.registerExecutors();

    // Initialize schema-aware service
    this.initializeSchemaService();
  }

  private async initializeSchemaService() {
    try {
      await this.schemaAwareSegmentationService.initialize();
      logger.info("Schema-aware segmentation service initialized successfully");
    } catch (error) {
      logger.error("Failed to initialize schema-aware segmentation service", { error });
    }
  }

  async execute(
    params: Record<string, unknown>,
    ctx?: RequestContext
  ): Promise<ToolResult> {
    try {
      // Validate parameters
      const validation = analysisParamsSchema.safeParse(params);
      if (!validation.success) {
        return {
          status: "error",
          error: `Invalid parameters: ${validation.error.message}`,
        };
      }

      const { analysis_type, ...validParams } = validation.data;

      // Check if executor exists
      const executor = this.analysisExecutors.get(analysis_type);
      if (!executor) {
        return {
          status: "error",
          error: `Analysis type '${analysis_type}' not implemented`,
        };
      }

      // Run the analysis
      logger.info(`Running ${analysis_type} analysis`, { params, ctx });
      const result = await executor(validParams);

      return {
        status: "success",
        data: result,
        metadata: {
          analysis_type,
          params: validParams,
        },
      };
    } catch (error) {
      logger.error("Error in AnalysisTool execution", { error, params });
      return {
        status: "error",
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  private registerExecutors() {
    // Register sales trends analysis
    this.analysisExecutors.set(
      "sales_trends",
      this.runSalesTrendsAnalysis.bind(this)
    );

    // Register sales forecast analysis
    this.analysisExecutors.set(
      "sales_forecast",
      this.runSalesForecastAnalysis.bind(this)
    );

    // Register customer segmentation analysis
    this.analysisExecutors.set(
      "customer_segmentation",
      this.runCustomerSegmentationAnalysis.bind(this)
    );

    // Register price elasticity analysis
    this.analysisExecutors.set(
      "price_elasticity",
      this.runPriceElasticityAnalysis.bind(this)
    );

    // Register basket analysis
    this.analysisExecutors.set(
      "basket_analysis",
      this.runBasketAnalysis.bind(this)
    );

    // Register product performance analysis
    this.analysisExecutors.set(
      "product_performance",
      this.runProductPerformanceAnalysis.bind(this)
    );
  }

  private async runSalesTrendsAnalysis(params: any): Promise<any> {
    const {
      location_id,
      start_date,
      end_date,
      interval = "day",
      category,
    } = params;

    // Use dataHubTool to get the base data
    const queryParams = {
      table: "pos_data",
      columns: [
        `DATE_FORMAT(order_date, '${this.getDateFormat(interval)}') as period`,
        "SUM(gross_sales) as sales",
        "SUM(net_sales) as net_sales",
        "COUNT(*) as transactions",
      ],
      filters: {
        location_id,
        order_date: {
          gte: start_date,
          lte: end_date,
        },
        ...(category ? { master_category: category } : {}),
      },
      group_by: [`DATE_FORMAT(order_date, '${this.getDateFormat(interval)}')`],
      order_by: [{ column: "period", direction: "asc" }],
    };

    const result = await this.dataHubTool.execute(queryParams);

    if (result.status === "error") {
      throw new Error(result.error);
    }

    // Process the data for trend analysis
    const timeSeriesData = result.data.map((row: any) => ({
      date: row.period,
      sales: parseFloat(row.sales),
      netSales: parseFloat(row.net_sales),
      transactions: row.transactions,
    }));

    // Calculate metrics
    const totalSales = timeSeriesData.reduce(
      (sum: number, point: TimeSeriesPoint) => sum + point.sales,
      0
    );
    const avgSales = totalSales / timeSeriesData.length;
    const totalTransactions = timeSeriesData.reduce(
      (sum: number, point: TimeSeriesPoint) => sum + point.transactions,
      0
    );

    // Calculate growth rates
    const growthRates = this.calculateGrowthRates(
      timeSeriesData.map((d: TimeSeriesPoint) => d.sales)
    );

    return {
      timeSeriesData,
      metrics: {
        totalSales,
        avgSales,
        totalTransactions,
        avgTransactionValue: totalSales / totalTransactions,
      },
      growthRates,
      interval,
      category: category || "All Categories",
    };
  }

  private async runSalesForecastAnalysis(params: any): Promise<any> {
    // Implement a simple forecast using historical data and trend extension
    const historicalData = await this.runSalesTrendsAnalysis(params);

    if (
      !historicalData.timeSeriesData ||
      historicalData.timeSeriesData.length < 3
    ) {
      throw new Error("Insufficient historical data for forecasting");
    }

    const { interval = "day", additional_params = {} } = params;
    const { forecast_periods = 4 } = additional_params;

    // Use a simple moving average forecast (this would be replaced with a proper algorithm)
    const timeSeriesData = historicalData.timeSeriesData;
    const lastNPoints = timeSeriesData.slice(-3);
    const averageSales =
      lastNPoints.reduce(
        (sum: number, p: TimeSeriesPoint) => sum + p.sales,
        0
      ) / lastNPoints.length;

    // Calculate a simple trend
    const trend =
      (timeSeriesData[timeSeriesData.length - 1].sales -
        timeSeriesData[0].sales) /
      timeSeriesData.length;

    // Generate forecast points
    const forecastStart = new Date(
      timeSeriesData[timeSeriesData.length - 1].date
    );
    const forecasts = [];

    for (let i = 1; i <= forecast_periods; i++) {
      const forecastDate = this.addInterval(forecastStart, interval, i);
      forecasts.push({
        date: forecastDate.toISOString().split("T")[0],
        sales: Math.max(0, averageSales + trend * i),
        isForecast: true,
      });
    }

    return {
      historicalData: timeSeriesData,
      forecastData: forecasts,
      metrics: historicalData.metrics,
      forecastMethod: "Simple Trend Extension",
      confidence: 0.7, // This would be calculated based on forecast algorithm
    };
  }

  private async runCustomerSegmentationAnalysis(params: any): Promise<any> {
    const { location_id, start_date, end_date, additional_params = {} } = params;
    const {
      segmentation_strategy = "hybrid",
      use_schema_aware = true,
      custom_rules
    } = additional_params;

    try {
      // Use schema-aware segmentation if available and requested
      if (use_schema_aware && this.schemaAwareSegmentationService) {
        logger.info("Running schema-aware customer segmentation", {
          location_id,
          start_date,
          end_date,
          segmentation_strategy,
        });

        const schemaAwareResult = await this.schemaAwareSegmentationService.runSchemaAwareSegmentation({
          location_id,
          start_date,
          end_date,
          segmentation_strategy,
          custom_rules,
        });

        // Transform schema-aware result to match expected format
        const segments: Record<string, any> = {};
        let totalCustomers = 0;
        let totalSpend = 0;

        schemaAwareResult.segments.forEach((segment, index) => {
          const segmentKey = segment.name.toLowerCase().replace(/\s+/g, '_');
          segments[segmentKey] = {
            id: segment.id,
            name: segment.name,
            description: segment.description,
            customers: segment.customers || [],
            spend: segment.metrics?.totalSpend || 0,
            visits: segment.customers?.length || 0,
            avgTransactionValue: segment.metrics?.avgTransactionValue || 0,
            frequency: segment.metrics?.frequency || 0,
            metadata: segment.metadata,
            rules: segment.rules,
          };

          totalCustomers += segment.customers?.length || 0;
          totalSpend += segment.metrics?.totalSpend || 0;
        });

        // Calculate segment distribution
        const segmentDistribution: Record<string, number> = {};
        Object.keys(segments).forEach(key => {
          segmentDistribution[key] = totalCustomers > 0
            ? segments[key].customers.length / totalCustomers
            : 0;
        });

        // Calculate revenue by segment
        const revenueBySegment: Record<string, number> = {};
        Object.keys(segments).forEach(key => {
          revenueBySegment[key] = segments[key].spend;
        });

        return {
          segments,
          totalCustomers,
          segmentDistribution,
          revenueBySegment,
          schemaAware: true,
          metadata: schemaAwareResult.metadata,
          schemaVersion: schemaAwareResult.metadata.schemaVersion,
          dataQuality: schemaAwareResult.metadata.dataQuality,
          generatedRules: schemaAwareResult.metadata.generatedRules,
        };
      }

      // Fallback to legacy segmentation
      logger.info("Running legacy customer segmentation", {
        location_id,
        start_date,
        end_date,
      });

      return await this.runLegacyCustomerSegmentation({ location_id, start_date, end_date });
    } catch (error) {
      logger.error("Error in customer segmentation analysis", { error, params });

      // Fallback to legacy segmentation on error
      logger.info("Falling back to legacy segmentation due to error");
      return await this.runLegacyCustomerSegmentation({ location_id, start_date, end_date });
    }
  }

  /**
   * Legacy customer segmentation implementation (fallback)
   */
  private async runLegacyCustomerSegmentation(params: {
    location_id: number;
    start_date: string;
    end_date: string;
  }): Promise<any> {
    const { location_id, start_date, end_date } = params;

    // Get customer purchase data
    const queryParams = {
      table: "pos_data",
      columns: [
        "customer_name",
        "SUM(gross_sales) as total_spend",
        "COUNT(*) as visit_count",
        "AVG(gross_sales) as avg_transaction",
      ],
      filters: {
        location_id,
        order_date: {
          gte: start_date,
          lte: end_date,
        },
        // Ensure we have customer information
        customer_name: {
          not: null,
        },
      },
      group_by: ["customer_name"],
      order_by: [{ column: "total_spend", direction: "desc" }],
      limit: 500,
    };

    const result = await this.dataHubTool.execute(queryParams);

    if (result.status === "error") {
      throw new Error(result.error);
    }

    const data = result.data.results || result.data || [];

    // Simple segmentation logic
    const segments: Record<string, CustomerSegment> = {
      vip: { customers: [], spend: 0, visits: 0 },
      regular: { customers: [], spend: 0, visits: 0 },
      occasional: { customers: [], spend: 0, visits: 0 },
    };

    data.forEach((customer: CustomerData) => {
      const totalSpend = parseFloat(customer.total_spend);
      const visitCount = parseInt(customer.visit_count);

      if (totalSpend > 500 || visitCount > 10) {
        segments.vip.customers.push(customer);
        segments.vip.spend += totalSpend;
        segments.vip.visits += visitCount;
      } else if (totalSpend > 200 || visitCount > 5) {
        segments.regular.customers.push(customer);
        segments.regular.spend += totalSpend;
        segments.regular.visits += visitCount;
      } else {
        segments.occasional.customers.push(customer);
        segments.occasional.spend += totalSpend;
        segments.occasional.visits += visitCount;
      }
    });

    return {
      segments,
      totalCustomers: data.length,
      segmentDistribution: {
        vip: data.length > 0 ? segments.vip.customers.length / data.length : 0,
        regular: data.length > 0 ? segments.regular.customers.length / data.length : 0,
        occasional: data.length > 0 ? segments.occasional.customers.length / data.length : 0,
      },
      revenueBySegment: {
        vip: segments.vip.spend,
        regular: segments.regular.spend,
        occasional: segments.occasional.spend,
      },
      schemaAware: false,
      legacy: true,
    };
  }

  private async runPriceElasticityAnalysis(params: any): Promise<any> {
    // Mock implementation - would require actual historical price changes and volume data
    const { category, additional_params = {} } = params;
    const { product_id } = additional_params;

    // For this demo, return simulated price elasticity data
    return {
      elasticity: -1.2, // Value less than -1 means elastic (sensitive to price)
      confidence: 0.8,
      recommendation:
        "Consider running promotional pricing, as demand is elastic",
      pricePoints: [
        { price: 10, estimatedSales: 150 },
        { price: 15, estimatedSales: 120 },
        { price: 20, estimatedSales: 90 },
        { price: 25, estimatedSales: 75 },
        { price: 30, estimatedSales: 60 },
      ],
      optimalPrice: {
        price: 15,
        projectedRevenue: 1800,
        projectedSales: 120,
      },
    };
  }

  private async runBasketAnalysis(params: any): Promise<any> {
    // Simple implementation of basket analysis (product associations)
    const { location_id, start_date, end_date } = params;

    // This would require a more complex implementation to analyze baskets
    // Returning mock data for demonstration
    return {
      frequentItemsets: [
        { items: ["Pre-roll", "Lighter"], support: 0.12, confidence: 0.72 },
        { items: ["Flower", "Grinder"], support: 0.08, confidence: 0.65 },
        { items: ["Vape Cart", "Battery"], support: 0.15, confidence: 0.85 },
        { items: ["Edible", "Beverage"], support: 0.07, confidence: 0.42 },
      ],
      recommendations: [
        {
          type: "bundle",
          products: ["Pre-roll", "Lighter"],
          supportLevel: "High",
          discountSuggestion: "10% off bundle",
        },
        {
          type: "cross-sell",
          mainProduct: "Vape Cart",
          suggestedProducts: ["Battery"],
          supportLevel: "Very High",
          discountSuggestion: "Free battery with 2+ cart purchase",
        },
      ],
    };
  }

  private async runProductPerformanceAnalysis(params: any): Promise<any> {
    const { location_id, start_date, end_date, category } = params;

    // Query to get product performance
    const queryParams = {
      table: "pos_data",
      columns: [
        "product_name",
        "master_category",
        "SUM(gross_sales) as total_sales",
        "SUM(inventory_profit) as total_profit",
        "COUNT(*) as units_sold",
      ],
      filters: {
        location_id,
        order_date: {
          gte: start_date,
          lte: end_date,
        },
        ...(category ? { master_category: category } : {}),
      },
      group_by: ["product_name", "master_category"],
      order_by: [{ column: "total_sales", direction: "desc" }],
      limit: 25,
    };

    const result = await this.dataHubTool.execute(queryParams);

    if (result.status === "error") {
      throw new Error(result.error);
    }

    // Process the data for performance analysis
    const productData = result.data.map((row: any) => ({
      productName: row.product_name,
      category: row.master_category,
      sales: parseFloat(row.total_sales),
      profit: parseFloat(row.total_profit),
      unitsSold: row.units_sold,
      profitMargin:
        (parseFloat(row.total_profit) / parseFloat(row.total_sales)) * 100,
    }));

    // Calculate category totals
    const categoryTotals: Record<
      string,
      { sales: number; profit: number; units: number }
    > = {};
    productData.forEach((product: ProductData) => {
      if (!categoryTotals[product.category]) {
        categoryTotals[product.category] = { sales: 0, profit: 0, units: 0 };
      }
      categoryTotals[product.category].sales += product.sales;
      categoryTotals[product.category].profit += product.profit;
      categoryTotals[product.category].units += product.unitsSold;
    });

    // Top performers by category
    const topPerformersByCategory: Record<string, any[]> = {};
    Object.keys(categoryTotals).forEach((cat) => {
      topPerformersByCategory[cat] = productData
        .filter((p: ProductData) => p.category === cat)
        .sort((a: ProductData, b: ProductData) => b.sales - a.sales)
        .slice(0, 5);
    });

    return {
      topProducts: productData.slice(0, 10),
      categoryPerformance: Object.entries(categoryTotals).map(
        ([category, data]) => ({
          category,
          sales: data.sales,
          profit: data.profit,
          units: data.units,
          profitMargin: (data.profit / data.sales) * 100,
        })
      ),
      topPerformersByCategory,
      recommendations: this.generateProductRecommendations(productData),
    };
  }

  // Helper methods
  private getDateFormat(interval: string): string {
    switch (interval) {
      case "day":
        return "%Y-%m-%d";
      case "week":
        return "%x-%v"; // Year-Week
      case "month":
        return "%Y-%m";
      case "quarter":
        return "%Y-Q%q";
      case "year":
        return "%Y";
      default:
        return "%Y-%m-%d";
    }
  }

  private calculateGrowthRates(data: number[]): {
    overall: number;
    periodToperiod: number[];
  } {
    if (data.length < 2) {
      return { overall: 0, periodToperiod: [] };
    }

    const first = data[0];
    const last = data[data.length - 1];

    // Overall growth rate
    const overallGrowth = first !== 0 ? (last - first) / first : 0;

    // Period-to-period growth rates
    const periodGrowth = [];
    for (let i = 1; i < data.length; i++) {
      const prevValue = data[i - 1];
      const growth = prevValue !== 0 ? (data[i] - prevValue) / prevValue : 0;
      periodGrowth.push(growth);
    }

    return {
      overall: overallGrowth,
      periodToperiod: periodGrowth,
    };
  }

  private addInterval(date: Date, interval: string, periods: number): Date {
    const result = new Date(date);

    switch (interval) {
      case "day":
        result.setDate(result.getDate() + periods);
        break;
      case "week":
        result.setDate(result.getDate() + periods * 7);
        break;
      case "month":
        result.setMonth(result.getMonth() + periods);
        break;
      case "quarter":
        result.setMonth(result.getMonth() + periods * 3);
        break;
      case "year":
        result.setFullYear(result.getFullYear() + periods);
        break;
    }

    return result;
  }

  private generateProductRecommendations(productData: ProductData[]): any[] {
    // Simple logic for product recommendations
    const recommendations = [];

    // Find products with high profit margin but low sales
    const highMarginLowSales = productData
      .filter(
        (p) => p.profitMargin > 50 && p.sales < productData[0].sales * 0.2
      )
      .slice(0, 3);

    if (highMarginLowSales.length > 0) {
      recommendations.push({
        type: "promotion",
        products: highMarginLowSales.map((p) => p.productName),
        reason: "High profit margin products with potential for more sales",
        action: "Consider promotional campaign to increase visibility",
      });
    }

    // Find top-selling products that could benefit from bundling
    const topSellers = productData.slice(0, 5);
    if (topSellers.length > 0) {
      recommendations.push({
        type: "bundling",
        products: topSellers.map((p) => p.productName),
        reason:
          "Top-selling products could drive additional sales through bundling",
        action: "Create bundles with complementary products",
      });
    }

    return recommendations;
  }
}
