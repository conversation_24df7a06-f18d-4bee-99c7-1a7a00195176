import { Tool, <PERSON><PERSON><PERSON><PERSON><PERSON>, Request<PERSON>ontext, <PERSON>lParameter } from "./interfaces";
import { logger } from "../config/logger";
import { ProductDataVectorService } from "../products/ProductDataVectorService";

export class ProductRecommendationTool implements Tool {
  name = "product_recommendation.search";
  description =
    "Search for product recommendations using enhanced product data and semantic matching";

  parameters: ToolParameter[] = [
    {
      name: "query",
      type: "string",
      description:
        "The product search query (e.g., 'indica strains for sleep', 'high THC edibles')",
      required: true,
    },
    {
      name: "topK",
      type: "number",
      description: "Number of products to return (default: 5, max: 20)",
      required: false,
    },
  ];

  async execute(
    params: Record<string, unknown>,
    ctx?: RequestContext
  ): Promise<ToolResult> {
    try {
      const { query, topK = 5 } = params;
      const locationId = ctx?.locationId;

      // Validation
      if (!query || typeof query !== "string") {
        return {
          status: "error",
          error: "Query is required and must be a string",
        };
      }

      if (!locationId) {
        return {
          status: "error",
          error: "Location ID is required for product recommendations",
        };
      }

      const searchLimit = Math.min(Math.max(1, Number(topK) || 5), 20);

      logger.info({
        message: "Executing ProductRecommendationTool",
        query,
        locationId,
        topK: searchLimit,
        toolName: this.name,
      });

      // Call ProductDataVectorService directly
      const productResults = await ProductDataVectorService.queryProductData(
        query,
        { location_id: locationId },
        searchLimit
      );

      logger.info({
        message: "ProductDataVectorService query completed",
        resultsCount: productResults.length,
        query,
        locationId,
      });

      // Format results with rich metadata for the frontend
      const recommendations = productResults.map(
        (result: any, index: number) => ({
          // Core product information
          product_name: result.metadata.product_name,
          meta_sku: result.metadata.meta_sku,
          retailer_id: result.metadata.retailer_id,
          brand_name: result.metadata.brand_name,
          category: result.metadata.category,
          subcategory: result.metadata.subcategory,

          // Pricing and availability
          price: result.metadata.latest_price,
          latest_price: result.metadata.latest_price, // Alias for compatibility

          // Cannabinoid content
          thc_percentage: result.metadata.thc,
          cbd_percentage: result.metadata.cbd,
          thc: result.metadata.thc, // Alias
          cbd: result.metadata.cbd, // Alias

          // Product details
          description: result.metadata.description,
          image_url: result.metadata.image_url,

          // User experience data
          rating: result.metadata.rating,
          reviews_count: result.metadata.reviews_count,

          // Product attributes
          tags: result.metadata.tags,
          mood: result.metadata.mood,

          // Medical/recreational availability
          medical: result.metadata.medical,
          recreational: result.metadata.recreational,

          // Recommendation context
          recommendation_reason: this.generateRecommendationReason(
            result,
            query,
            index + 1
          ),
          score: result.score,

          // Source information
          source: "product_recommendation_tool",

          // Include all metadata for debugging/future use
          metadata: {
            ...result.metadata,
            source_type: "product_recommendation_tool",
            search_query: query,
            search_score: result.score,
            rank: index + 1,
          },
        })
      );

      return {
        status: "success",
        data: {
          recommendations,
          query_info: {
            original_query: query,
            search_type: "product_search",
            location_id: locationId,
            total_results: recommendations.length,
            using_service: "ProductDataVectorService_Direct",
          },
        },
      };
    } catch (error) {
      logger.error({
        message: "Error in ProductRecommendationTool",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        query: params.query,
        locationId: ctx?.locationId,
      });

      return {
        status: "error",
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Generates a human-readable recommendation reason based on the search result
   */
  private generateRecommendationReason(
    result: any,
    query: string,
    rank: number
  ): string {
    const metadata = result.metadata;
    const score = Math.round(result.score * 100);

    // Base reason with similarity score
    let reason = `${score}% match for "${query}"`;

    // Add specific reasons based on product attributes
    const reasons = [];

    if (metadata.category) {
      reasons.push(`${metadata.category} product`);
    }

    if (metadata.thc && metadata.thc > 0) {
      reasons.push(`${metadata.thc}% THC`);
    }

    if (metadata.cbd && metadata.cbd > 0) {
      reasons.push(`${metadata.cbd}% CBD`);
    }

    if (metadata.rating && metadata.rating > 4) {
      reasons.push(`highly rated (${metadata.rating}/5)`);
    }

    if (
      metadata.mood &&
      Array.isArray(metadata.mood) &&
      metadata.mood.length > 0
    ) {
      reasons.push(`good for ${metadata.mood.slice(0, 2).join(", ")}`);
    }

    if (reasons.length > 0) {
      reason += ` - ${reasons.join(", ")}`;
    }

    return reason;
  }

  /**
   * Test method to verify the tool works correctly
   */
  async test(
    locationId: number,
    query: string = "indica strains for sleep"
  ): Promise<any> {
    try {
      const result = await this.execute({ query, topK: 3 }, { locationId });

      logger.info({
        message: "ProductRecommendationTool test completed",
        status: result.status,
        recommendationsCount:
          result.status === "success"
            ? (result.data as any)?.recommendations?.length
            : 0,
        query,
        locationId,
      });

      return result;
    } catch (error) {
      logger.error("ProductRecommendationTool test failed:", error);
      throw error;
    }
  }
}
