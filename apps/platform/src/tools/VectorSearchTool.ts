import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>quest<PERSON>ontext, ToolParameter } from "./interfaces";
import { OpenAI } from "openai";
import { logger } from "../config/logger";
import { ProductDataVectorService } from "../products/ProductDataVectorService";

export class VectorSearchTool implements Tool {
  name = "vector_search.query";
  description =
    "Search for similar items in the vector database, including products, documents, regulations, strain information, etc.";

  parameters: ToolParameter[] = [
    {
      name: "query",
      type: "string",
      description: "The search query",
      required: true,
    },
    {
      name: "namespace",
      type: "string",
      description:
        "The namespace to search in (products, documents, strains, regulations, marketing_templates)",
      required: false,
    },
    {
      name: "content_type",
      type: "string",
      description:
        "Filter by specific content type (e.g., product, sop, policy, compliance, strain_info, marketing)",
      required: false,
    },
    {
      name: "search_type",
      type: "string",
      description:
        "Type of search to perform (product_search, document_search, general_search)",
      required: false,
    },
    {
      name: "location_id",
      type: "number",
      description: "Filter by a specific location",
      required: false,
    },
    {
      name: "topK",
      type: "number",
      description: "Number of results to return (default: 5)",
      required: false,
    },
  ];

  private pineconeClient: any;
  private openai: OpenAI;
  private indexName: string;

  constructor(pineconeClient: any, openai: OpenAI, indexName: string) {
    this.pineconeClient = pineconeClient;
    this.openai = openai;
    this.indexName = indexName;
  }

  /**
   * Get the appropriate namespace based on the content type
   */
  private getNamespace(namespace?: string, contentType?: string): string {
    // If namespace is explicitly provided, use it
    if (namespace) {
      return namespace;
    }

    // Map content type to appropriate namespace
    if (contentType) {
      const contentTypeMap: Record<string, string> = {
        sop: "documents",
        policy: "documents",
        compliance: "regulations",
        strain_info: "strains",
        marketing: "marketing_templates",
        campaign: "marketing_templates",
        product_description: "product_content",
      };

      if (contentTypeMap[contentType]) {
        return contentTypeMap[contentType];
      }
    }

    // Default namespace
    return "documents";
  }

  async execute(
    params: Record<string, unknown>,
    ctx?: RequestContext
  ): Promise<ToolResult> {
    try {
      const {
        query,
        namespace: explicitNamespace,
        content_type: contentType,
        search_type: searchType,
        location_id: explicitLocationId,
        topK = 5,
      } = params;

      if (!query) {
        return {
          status: "error",
          error: "query is required",
        };
      }

      // Use location context if available
      const locationId = explicitLocationId || ctx?.locationId;

      // Check if this is a product search based on explicit parameters
      // This is now determined by the intent analysis, not local keyword detection
      const isProductSearch =
        searchType === "product_search" ||
        explicitNamespace === "products" ||
        contentType === "product";

      if (isProductSearch && locationId) {
        logger.info({
          message: "Using ProductDataVectorService for product search",
          query,
          locationId,
          topK,
          searchType,
          triggeredBy:
            searchType === "product_search"
              ? "intent_analysis"
              : "explicit_params",
        });

        // Use ProductDataVectorService for better product results
        const productResults = await ProductDataVectorService.queryProductData(
          query as string,
          { location_id: parseInt(locationId.toString()) },
          topK as number
        );

        // Format product results to match VectorSearchTool format
        const formattedResults = productResults.map((result: any) => ({
          id: result.id,
          score: result.score,
          metadata: result.metadata,
          // Create rich content for products
          content: this.formatProductContent(result.metadata),
          source: `${result.metadata.brand_name || "Unknown"} - ${
            result.metadata.product_name
          }`,
          type: "product",
          // Include all product metadata for frontend
          product_data: {
            product_name: result.metadata.product_name,
            brand_name: result.metadata.brand_name,
            meta_sku: result.metadata.meta_sku,
            price: result.metadata.latest_price,
            thc: result.metadata.thc,
            cbd: result.metadata.cbd,
            category: result.metadata.category,
            subcategory: result.metadata.subcategory,
            image_url: result.metadata.image_url,
            rating: result.metadata.rating,
            reviews_count: result.metadata.reviews_count,
            description: result.metadata.description,
            tags: result.metadata.tags,
            mood: result.metadata.mood,
            medical: result.metadata.medical,
            recreational: result.metadata.recreational,
          },
        }));

        return {
          status: "success",
          data: {
            results: formattedResults,
            query_info: {
              original_query: query,
              search_type: "product_search",
              location_id: locationId,
              total_results: formattedResults.length,
              using_service: "ProductDataVectorService",
            },
          },
        };
      }

      // For non-product searches, use the original implementation
      // Generate vector embedding for the query
      const embedding = await this.generateEmbedding(query as string);

      // Determine the appropriate namespace
      const namespace = this.getNamespace(
        explicitNamespace as string,
        contentType as string
      );

      logger.info({
        message: "Executing generic vector search",
        query,
        namespace,
        topK,
        locationId,
        contentType,
      });

      // Get the Pinecone index
      const index = this.pineconeClient.Index(this.indexName);

      // Build the query filter
      const filter: Record<string, any> = {};

      // Add location filter if specified
      if (locationId) {
        filter.location_id = { $eq: parseInt(locationId.toString()) };
      }

      // Add content type filter if specified
      if (contentType) {
        filter.content_type = { $eq: contentType };
      }

      // Execute query
      const queryResponse = await index.query({
        vector: embedding,
        topK: topK as number,
        namespace,
        filter: Object.keys(filter).length > 0 ? filter : undefined,
        includeMetadata: true,
        includeValues: false,
      });

      // Extract and format results
      const results = queryResponse.matches.map((match: any) => ({
        id: match.id,
        score: match.score,
        metadata: match.metadata,
        // Extract summary or snippet if available
        content:
          match.metadata.snippet ||
          match.metadata.summary ||
          "No content preview available",
        // Add source information
        source:
          match.metadata.source ||
          match.metadata.filename ||
          match.metadata.url ||
          "Unknown source",
        // Add content type
        type: match.metadata.content_type || namespace,
      }));

      return {
        status: "success",
        data: {
          results,
          query_info: {
            original_query: query,
            namespace,
            content_type: contentType,
            location_id: locationId,
            total_results: results.length,
          },
        },
      };
    } catch (error) {
      logger.error("Error in VectorSearchTool:", error);
      return {
        status: "error",
        error:
          error instanceof Error
            ? error.message
            : "Unknown error in vector search",
      };
    }
  }

  /**
   * Generate an embedding for the query text
   */
  private async generateEmbedding(text: string): Promise<number[]> {
    try {
      const response = await this.openai.embeddings.create({
        model: "text-embedding-ada-002",
        input: text,
      });

      return response.data[0].embedding;
    } catch (error) {
      logger.error("Error generating embedding:", error);
      throw new Error("Failed to generate embedding for query");
    }
  }

  /**
   * Search documents specifically
   */
  async searchDocuments(
    query: string,
    locationId?: number,
    documentType?: string,
    limit: number = 5
  ): Promise<ToolResult> {
    return this.execute({
      query,
      namespace: "documents",
      content_type: documentType,
      location_id: locationId,
      topK: limit,
    });
  }

  /**
   * Search strain information
   */
  async searchStrains(query: string, limit: number = 5): Promise<ToolResult> {
    return this.execute({
      query,
      namespace: "strains",
      topK: limit,
    });
  }

  /**
   * Search regulations
   */
  async searchRegulations(
    query: string,
    locationId?: number,
    limit: number = 5
  ): Promise<ToolResult> {
    return this.execute({
      query,
      namespace: "regulations",
      location_id: locationId,
      topK: limit,
    });
  }

  /**
   * Search marketing templates
   */
  async searchMarketingContent(
    query: string,
    contentType?: string,
    limit: number = 5
  ): Promise<ToolResult> {
    return this.execute({
      query,
      namespace: "marketing_templates",
      content_type: contentType,
      topK: limit,
    });
  }

  /**
   * Format product metadata into readable content
   */
  private formatProductContent(metadata: any): string {
    let content = `${metadata.product_name}`;

    if (metadata.brand_name) {
      content += ` by ${metadata.brand_name}`;
    }

    if (metadata.category || metadata.subcategory) {
      content += ` - ${metadata.category || ""}${
        metadata.subcategory ? ` (${metadata.subcategory})` : ""
      }`;
    }

    if (metadata.thc || metadata.cbd) {
      content += ` | THC: ${metadata.thc || 0}%, CBD: ${metadata.cbd || 0}%`;
    }

    if (metadata.latest_price) {
      content += ` | Price: $${metadata.latest_price}`;
    }

    if (metadata.rating) {
      content += ` | Rated: ${metadata.rating}/5`;
    }

    if (metadata.description) {
      content += ` | ${metadata.description.substring(0, 100)}...`;
    }

    return content;
  }
}
