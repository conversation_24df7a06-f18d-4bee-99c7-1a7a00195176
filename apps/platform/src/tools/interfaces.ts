export interface RequestContext {
  userId?: number;
  locationId?: number;
  chatId?: string;
  messageId?: string;
}

export interface ToolParameter {
  name: string;
  description: string;
  type: "string" | "number" | "boolean" | "array" | "object";
  required: boolean;
  default?: unknown;
  enum?: unknown[];
}

export interface ToolResult {
  status: "success" | "error";
  data?: unknown;
  error?: string;
  metadata?: Record<string, unknown>;
}

export interface Tool {
  name: string;
  description: string;
  parameters: ToolParameter[];
  execute(
    params: Record<string, unknown>,
    ctx?: RequestContext
  ): Promise<ToolResult>;
}

// JSON Schema generation utility
export function generateJsonSchema(tool: Tool): Record<string, unknown> {
  const properties: Record<string, unknown> = {};
  const required: string[] = [];

  tool.parameters.forEach((param) => {
    properties[param.name] = {
      type: param.type,
      description: param.description,
    };

    if (param.enum) {
      (properties[param.name] as Record<string, unknown>).enum =
        param.enum as unknown[];
    }

    if (param.default !== undefined) {
      (properties[param.name] as Record<string, unknown>).default =
        param.default as unknown;
    }

    // Add items definition for array type parameters
    if (param.type === "array") {
      // Default to string items if not specified
      (properties[param.name] as Record<string, unknown>).items = {
        type: "string",
      };
    }

    if (param.required) {
      required.push(param.name);
    }
  });

  return {
    name: tool.name,
    description: tool.description,
    parameters: {
      type: "object",
      properties,
      required,
    },
  };
}
