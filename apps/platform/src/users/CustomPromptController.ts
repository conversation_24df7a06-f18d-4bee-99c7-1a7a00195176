/**
 * @swagger
 * components:
 *   schemas:
 *     CustomPrompt:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: Prompt ID
 *         admin_id:
 *           type: integer
 *           description: ID of the admin who owns this prompt
 *         name:
 *           type: string
 *           description: Name of the prompt
 *         content:
 *           type: string
 *           description: Content of the prompt
 *         is_active:
 *           type: boolean
 *           description: Whether this prompt is currently active
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 *     CustomPromptPayload:
 *       type: object
 *       required: [name, content, is_active]
 *       properties:
 *         name:
 *           type: string
 *           description: Name of the prompt
 *         content:
 *           type: string
 *           description: Content of the prompt
 *         is_active:
 *           type: boolean
 *           description: Whether this prompt should be active
 *     CustomPromptListResponse:
 *       type: array
 *       items:
 *         $ref: '#/components/schemas/CustomPrompt'
 */

/**
 * @swagger
 * tags:
 *   name: Custom Prompts
 *   description: Endpoints for managing custom prompts
 */

/**
 * @swagger
 * /custom-prompts:
 *   get:
 *     summary: List Custom Prompts
 *     description: Retrieve all custom prompts for the current admin
 *     tags: [Custom Prompts]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of custom prompts retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CustomPromptListResponse'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       500:
 *         description: Internal server error
 *   post:
 *     summary: Create Custom Prompt
 *     description: Create a new custom prompt. If the prompt is set as active, all other prompts will be deactivated.
 *     tags: [Custom Prompts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CustomPromptPayload'
 *           example:
 *             name: "Product Recommendation"
 *             content: "Recommend products based on user preferences"
 *             is_active: true
 *     responses:
 *       201:
 *         description: Custom prompt created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CustomPrompt'
 *       400:
 *         description: Invalid request body
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       500:
 *         description: Internal server error
 *
 * /custom-prompts/{id}:
 *   get:
 *     summary: Get Custom Prompt
 *     description: Retrieve a specific custom prompt by ID
 *     tags: [Custom Prompts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the custom prompt
 *     responses:
 *       200:
 *         description: Custom prompt retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CustomPrompt'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Custom prompt not found
 *       500:
 *         description: Internal server error
 *   put:
 *     summary: Update Custom Prompt
 *     description: Update an existing custom prompt. If the prompt is set as active, all other prompts will be deactivated.
 *     tags: [Custom Prompts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the custom prompt
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CustomPromptPayload'
 *     responses:
 *       200:
 *         description: Custom prompt updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CustomPrompt'
 *       400:
 *         description: Invalid request body
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Custom prompt not found
 *       500:
 *         description: Internal server error
 *   delete:
 *     summary: Delete Custom Prompt
 *     description: Delete a custom prompt
 *     tags: [Custom Prompts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the custom prompt
 *     responses:
 *       204:
 *         description: Custom prompt deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Custom prompt not found
 *       500:
 *         description: Internal server error
 *
 * /custom-prompts/{id}/toggle:
 *   post:
 *     summary: Toggle Custom Prompt
 *     description: Toggle the active state of a custom prompt. If activated, all other prompts will be deactivated.
 *     tags: [Custom Prompts]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the custom prompt
 *     responses:
 *       200:
 *         description: Custom prompt toggled successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CustomPrompt'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       404:
 *         description: Custom prompt not found
 *       500:
 *         description: Internal server error
 */

import Router = require("@koa/router");
import { DefaultContext, DefaultState, ParameterizedContext } from "koa";
import { AppContext } from "../core/types";
import { locationRoleMiddleware } from "../locations/LocationService";
import { JSONSchemaType, validate } from "../core/validate";
import { logger } from "../config/logger";
import { Knex } from "knex";
import App from "../app";
import UserCustomPrompt from "./UserCustomPrompt";

interface CustomPromptPayload {
  name: string;
  content: string;
  is_active: boolean;
}

const customPromptSchema: JSONSchemaType<CustomPromptPayload> = {
  type: "object",
  required: ["name", "content", "is_active"],
  properties: {
    name: { type: "string" },
    content: { type: "string" },
    is_active: { type: "boolean" },
  },
  additionalProperties: false,
};

export class CustomPromptController {
  private readonly app: App;
  private db!: Knex;

  constructor(app: App) {
    this.app = app;
    if (app.db) {
      this.db = app.db;
    }
  }

  private ensureDatabase() {
    if (!this.db) {
      if (!this.app.db) {
        throw new Error("Database is not initialized");
      }
      this.db = this.app.db;
    }
  }

  // Get all custom prompts for the current user
  async listCustomPrompts(
    ctx: ParameterizedContext<any, DefaultContext & AppContext>
  ) {
    try {
      this.ensureDatabase();
      const adminId = ctx.state.admin.id;

      // Get all custom prompts for this admin
      const prompts = await this.db
        .from("user_custom_prompts")
        .where({ admin_id: adminId })
        .select("*");

      ctx.body = prompts;
    } catch (error) {
      logger.error(error, "Error fetching custom prompts");
      ctx.status = 500;
      ctx.body = { error: "Failed to fetch custom prompts" };
    }
  }

  // Get a specific custom prompt by ID
  async getCustomPrompt(
    ctx: ParameterizedContext<any, DefaultContext & AppContext>
  ) {
    try {
      this.ensureDatabase();
      const adminId = ctx.state.admin.id;
      const promptId = ctx.params.id;

      // Get the prompt with validation that it belongs to the admin
      const prompt = await this.db
        .from("user_custom_prompts")
        .where({ id: promptId, admin_id: adminId })
        .first();

      if (!prompt) {
        ctx.status = 404;
        ctx.body = { error: "Custom prompt not found" };
        return;
      }

      ctx.body = prompt;
    } catch (error) {
      logger.error(error, "Error fetching custom prompt");
      ctx.status = 500;
      ctx.body = { error: "Failed to fetch custom prompt" };
    }
  }

  // Create a new custom prompt
  async createCustomPrompt(
    ctx: ParameterizedContext<any, DefaultContext & AppContext>
  ) {
    try {
      this.ensureDatabase();
      const adminId = ctx.state.admin.id;

      // Log the request body to debug
      logger.info({
        message: "Creating custom prompt",
        body: ctx.request.body,
      });

      // Manual validation as a fallback
      const body = ctx.request.body;
      if (!body || typeof body !== "object") {
        ctx.status = 400;
        ctx.body = { error: "Invalid request body" };
        return;
      }

      const name = body.name;
      const content = body.content;
      const is_active = body.is_active !== undefined ? body.is_active : true;

      if (!name || typeof name !== "string") {
        ctx.status = 400;
        ctx.body = { error: "Name is required and must be a string" };
        return;
      }

      if (!content || typeof content !== "string") {
        ctx.status = 400;
        ctx.body = { error: "Content is required and must be a string" };
        return;
      }

      if (typeof is_active !== "boolean") {
        ctx.status = 400;
        ctx.body = { error: "is_active must be a boolean" };
        return;
      }

      // If this prompt is being created as active, deactivate all other prompts
      if (is_active) {
        await this.db
          .from("user_custom_prompts")
          .where({ admin_id: adminId, is_active: true })
          .update({ is_active: false, updated_at: new Date() });
      }

      // Create the new prompt
      const [promptId] = await this.db.from("user_custom_prompts").insert({
        admin_id: adminId,
        name,
        content,
        is_active,
      });

      // Fetch the newly created prompt
      const prompt = await this.db
        .from("user_custom_prompts")
        .where({ id: promptId })
        .first();

      ctx.status = 201;
      ctx.body = prompt;
    } catch (error) {
      logger.error(error, "Error creating custom prompt");
      ctx.status = 500;
      ctx.body = { error: "Failed to create custom prompt" };
    }
  }

  // Update an existing custom prompt
  async updateCustomPrompt(
    ctx: ParameterizedContext<any, DefaultContext & AppContext>
  ) {
    try {
      this.ensureDatabase();
      const adminId = ctx.state.admin.id;
      const promptId = ctx.params.id;

      // Log the request body to debug
      logger.info({
        message: "Updating custom prompt",
        body: ctx.request.body,
        promptId,
      });

      // Manual validation as a fallback
      const body = ctx.request.body;
      if (!body || typeof body !== "object") {
        ctx.status = 400;
        ctx.body = { error: "Invalid request body" };
        return;
      }

      const name = body.name;
      const content = body.content;
      const is_active = body.is_active;

      if (!name || typeof name !== "string") {
        ctx.status = 400;
        ctx.body = { error: "Name is required and must be a string" };
        return;
      }

      if (!content || typeof content !== "string") {
        ctx.status = 400;
        ctx.body = { error: "Content is required and must be a string" };
        return;
      }

      if (is_active !== undefined && typeof is_active !== "boolean") {
        ctx.status = 400;
        ctx.body = { error: "is_active must be a boolean" };
        return;
      }

      // Check if the prompt exists and belongs to the admin
      const existingPrompt = await this.db
        .from("user_custom_prompts")
        .where({ id: promptId, admin_id: adminId })
        .first();

      if (!existingPrompt) {
        ctx.status = 404;
        ctx.body = { error: "Custom prompt not found" };
        return;
      }

      // If this prompt is being set to active, deactivate all other prompts
      if (is_active === true && !existingPrompt.is_active) {
        await this.db
          .from("user_custom_prompts")
          .where({ admin_id: adminId, is_active: true })
          .whereNot({ id: promptId })
          .update({ is_active: false, updated_at: new Date() });
      }

      // Update the prompt
      await this.db
        .from("user_custom_prompts")
        .where({ id: promptId, admin_id: adminId })
        .update({
          name,
          content,
          is_active:
            is_active !== undefined ? is_active : existingPrompt.is_active,
          updated_at: new Date(),
        });

      // Fetch the updated prompt
      const updatedPrompt = await this.db
        .from("user_custom_prompts")
        .where({ id: promptId })
        .first();

      ctx.body = updatedPrompt;
    } catch (error) {
      logger.error(error, "Error updating custom prompt");
      ctx.status = 500;
      ctx.body = { error: "Failed to update custom prompt" };
    }
  }

  // Delete a custom prompt
  async deleteCustomPrompt(
    ctx: ParameterizedContext<any, DefaultContext & AppContext>
  ) {
    try {
      this.ensureDatabase();
      const adminId = ctx.state.admin.id;
      const promptId = ctx.params.id;

      // Check if the prompt exists and belongs to the admin
      const existingPrompt = await this.db
        .from("user_custom_prompts")
        .where({ id: promptId, admin_id: adminId })
        .first();

      if (!existingPrompt) {
        ctx.status = 404;
        ctx.body = { error: "Custom prompt not found" };
        return;
      }

      // Delete the prompt
      await this.db
        .from("user_custom_prompts")
        .where({ id: promptId, admin_id: adminId })
        .delete();

      ctx.status = 204; // No content
    } catch (error) {
      logger.error(error, "Error deleting custom prompt");
      ctx.status = 500;
      ctx.body = { error: "Failed to delete custom prompt" };
    }
  }

  // Enable/disable a custom prompt
  async toggleCustomPrompt(
    ctx: ParameterizedContext<any, DefaultContext & AppContext>
  ) {
    try {
      this.ensureDatabase();
      const adminId = ctx.state.admin.id;
      const promptId = ctx.params.id;

      // Get the current state of the prompt
      const existingPrompt = await this.db
        .from("user_custom_prompts")
        .where({ id: promptId, admin_id: adminId })
        .first();

      if (!existingPrompt) {
        ctx.status = 404;
        ctx.body = { error: "Custom prompt not found" };
        return;
      }

      // If this prompt is being activated, deactivate all other prompts
      if (!existingPrompt.is_active) {
        await this.db
          .from("user_custom_prompts")
          .where({ admin_id: adminId, is_active: true })
          .whereNot({ id: promptId })
          .update({ is_active: false, updated_at: new Date() });
      }

      // Toggle the active state
      await this.db
        .from("user_custom_prompts")
        .where({ id: promptId, admin_id: adminId })
        .update({
          is_active: !existingPrompt.is_active,
          updated_at: new Date(),
        });

      // Fetch the updated prompt
      const updatedPrompt = await this.db
        .from("user_custom_prompts")
        .where({ id: promptId })
        .first();

      ctx.body = updatedPrompt;
    } catch (error) {
      logger.error(error, "Error toggling custom prompt");
      ctx.status = 500;
      ctx.body = { error: "Failed to toggle custom prompt" };
    }
  }

  setupRoutes(router: Router) {
    router.get("/", this.listCustomPrompts.bind(this));
    router.get("/:id", this.getCustomPrompt.bind(this));
    router.post("/", this.createCustomPrompt.bind(this));
    router.put("/:id", this.updateCustomPrompt.bind(this));
    router.delete("/:id", this.deleteCustomPrompt.bind(this));
    router.post("/:id/toggle", this.toggleCustomPrompt.bind(this));
  }
}

// Setup routes
export function setupCustomPromptRoutes(app: App) {
  const controller = new CustomPromptController(app);
  const router = new Router({
    prefix: "/custom-prompts",
  });

  // Require at least support role
  router.use(locationRoleMiddleware("support"));

  // Setup routes
  controller.setupRoutes(router);

  return router;
}
