import { Job } from "../queue";
import { aliasUser } from "./UserRepository";
import { ClientAliasParams } from "../client/Client";

type UserAliasTrigger = ClientAliasParams & {
  location_id: number;
};

export default class UserAliasJob extends Job {
  static $name = "user_alias";

  static from(data: UserAliasTrigger): UserAliasJob {
    return new this(data);
  }

  static async handler({ location_id, ...alias }: UserAliasTrigger) {
    await aliasUser(location_id, alias);
  }
}
