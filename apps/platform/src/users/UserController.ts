/**
 * @swagger
 * tags:
 *   name: User
 *   description: User management endpoints
 */

/* eslint-disable indent */
import Router from "@koa/router";
import App from "../app";
import { LocationState } from "../auth/AuthMiddleware";
import UserDeleteJob from "./UserDeleteJob";
import UserPatchJob from "./UserPatchJob";
import { JSONSchemaType, validate } from "../core/validate";
import { User, UserParams } from "./User";
import { extractQueryParams } from "../utilities";
import { searchParamsSchema, SearchSchema } from "../core/searchParams";
import { getUser, getUserFromContext, pagedUsers } from "./UserRepository";
import { getUserLists } from "../lists/ListService";
import {
  getUserSubscriptions,
  toggleSubscription,
} from "../subscriptions/SubscriptionService";
import { SubscriptionState } from "../subscriptions/Subscription";
import { getUserEvents } from "./UserEventRepository";
import { locationRoleMiddleware } from "../locations/LocationService";
import { pagedEntrancesByUser } from "../journey/JourneyRepository";
import parse from "../storage/FileStream";
import { importUsers } from "./UserImportService";
import { UserDataVectorService } from "./UserDataVectorService";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";
import { logger } from "../config/logger";

// Debug log to verify the controller is loaded
logger.info("UserController loaded with vectorization endpoints");

const router = new Router<LocationState & { user?: User }>({
  prefix: "/users",
});

/**
 * @swagger
 * /users:
 *   get:
 *     summary: List Users
 *     description: Retrieves a paginated list of users for the current location with search and filter capabilities
 *     tags: [User]
 *     parameters:
 *       - in: query
 *         name: cursor
 *         schema:
 *           type: string
 *         description: Cursor for pagination
 *       - in: query
 *         name: page
 *         schema:
 *           type: string
 *           enum: [prev, next]
 *         description: Page direction
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: Sort direction
 *       - in: query
 *         name: q
 *         schema:
 *           type: string
 *         description: Search query for user name, email, phone, etc.
 *       - in: query
 *         name: filter
 *         schema:
 *           type: object
 *         description: Filter object for specific field filtering
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/User'
 *                 nextCursor:
 *                   type: string
 *                 prevCursor:
 *                   type: string
 *                 limit:
 *                   type: integer
 */
router.get("/", async (ctx) => {
  try {
    const locationId = ctx.state.location.id;

    logger.info({
      message: "Getting users",
      locationId,
      query: ctx.query,
      path: ctx.path,
    });

    // Extract cursor pagination parameters
    const cursor = ctx.query.cursor as string;
    const page = (ctx.query.page as string) || "next";
    const limit = Math.min(
      100,
      Math.max(1, parseInt(ctx.query.limit as string) || 25)
    );

    // Handle search parameters
    const searchQuery = (ctx.query.q as string)?.trim();
    const sort = (ctx.query.sort as string) || "created_at";
    const direction = (ctx.query.direction as string) || "desc";

    // Handle filter object (from frontend)
    let filterObj: any = {};
    try {
      if (ctx.query.filter && typeof ctx.query.filter === "string") {
        filterObj = JSON.parse(ctx.query.filter);
      } else if (ctx.query.filter && typeof ctx.query.filter === "object") {
        filterObj = ctx.query.filter;
      }
    } catch (e) {
      logger.warn("Invalid filter parameter:", ctx.query.filter);
    }

    // Build query
    let query = App.main.db("users").where("location_id", locationId);

    // Apply filters from filter object
    if (filterObj) {
      Object.keys(filterObj).forEach((key) => {
        if (
          filterObj[key] !== null &&
          filterObj[key] !== undefined &&
          filterObj[key] !== ""
        ) {
          query = query.where(key, filterObj[key]);
        }
      });
    }

    // Apply text search if provided
    if (searchQuery) {
      query = query.where((builder) => {
        builder
          .where("external_id", "like", `%${searchQuery}%`)
          .orWhere("email", "like", `%${searchQuery}%`)
          .orWhere("phone", "like", `%${searchQuery}%`)
          .orWhereRaw("JSON_EXTRACT(data, '$.first_name') LIKE ?", [
            `%${searchQuery}%`,
          ])
          .orWhereRaw("JSON_EXTRACT(data, '$.last_name') LIKE ?", [
            `%${searchQuery}%`,
          ])
          .orWhereRaw(
            "CONCAT(JSON_EXTRACT(data, '$.first_name'), ' ', JSON_EXTRACT(data, '$.last_name')) LIKE ?",
            [`%${searchQuery}%`]
          );
      });
    }

    // Apply sorting
    const validSortFields = [
      "id",
      "external_id",
      "email",
      "phone",
      "created_at",
      "updated_at",
    ];
    const sortField = validSortFields.includes(sort) ? sort : "created_at";
    const sortDirection = direction.toLowerCase() === "asc" ? "asc" : "desc";
    query = query.orderBy(sortField, sortDirection);

    // Apply cursor pagination
    if (cursor) {
      const operator = page === "prev" ? "<" : ">";
      query = query.where("id", operator, cursor);
    }

    // Get one extra item to check if there are more results
    const users = await query
      .limit(limit + 1)
      .select("*")
      .then((users) =>
        users.map((user) => ({
          ...user,
          name: `${user.data.first_name} ${user.data.last_name}`,
        }))
      );

    const hasMore = users.length > limit;
    if (hasMore) {
      users.pop(); // Remove the extra item we fetched
    }

    logger.info({
      message: "Retrieved users",
      count: users.length,
      hasMore,
      locationId,
      searchQuery,
      filterObj,
      sort: sortField,
      direction: sortDirection,
    });

    ctx.body = {
      results: users,
      nextCursor: hasMore ? users[users.length - 1]?.id : undefined,
      prevCursor: cursor,
      limit,
    };
  } catch (error) {
    logger.error({
      message: "Error fetching users",
      error: error instanceof Error ? error.message : "Unknown error",
      locationId: ctx.state.location?.id,
    });

    ctx.status = 500;
    ctx.body = { error: "Failed to fetch users" };
  }
});

const patchUsersRequest: JSONSchemaType<UserParams[]> = {
  $id: "patchUsers",
  type: "array",
  items: {
    type: "object",
    properties: {
      anonymous_id: {
        type: "string",
        nullable: true,
      },
      external_id: {
        type: "string",
        nullable: true,
      },
      email: {
        type: "string",
        nullable: true,
      },
      phone: {
        type: "string",
        nullable: true,
      },
      timezone: {
        type: "string",
        nullable: true,
      },
      locale: {
        type: "string",
        nullable: true,
      },
      data: {
        type: "object",
        nullable: true,
        additionalProperties: true,
      },
    },
    anyOf: [{ required: ["anonymous_id"] }, { required: ["external_id"] }],
  },
  minItems: 1,
} as any;
/**
 * @swagger
 * /users:
 *   patch:
 *     summary: Update Users
 *     description: Updates multiple users in the current location
 *     tags: [User]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               $ref: '#/components/schemas/UserParams'
 *     responses:
 *       204:
 *         description: Users updated successfully
 *       400:
 *         description: Invalid input
 */
router.patch("/", locationRoleMiddleware("editor"), async (ctx) => {
  const users = validate(patchUsersRequest, ctx.request.body);

  for (const user of users) {
    await App.main.queue.enqueue(
      UserPatchJob.from({
        location_id: ctx.state.location.id,
        user,
      })
    );
  }

  ctx.status = 204;
  ctx.body = "";
});

const deleteUsersRequest: JSONSchemaType<string[]> = {
  $id: "deleteUsers",
  type: "array",
  items: {
    type: "string",
  },
  minItems: 1,
};
/**
 * @swagger
 * /users:
 *   delete:
 *     summary: Delete Users
 *     description: Deletes multiple users from the current location
 *     tags: [User]
 *     parameters:
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Array of user IDs to delete
 *     responses:
 *       204:
 *         description: Users deleted successfully
 *       400:
 *         description: Invalid input
 */
router.delete("/", locationRoleMiddleware("editor"), async (ctx) => {
  let userIds = ctx.request.query.user_id || [];
  if (!Array.isArray(userIds)) userIds = userIds.length ? [userIds] : [];

  userIds = validate(deleteUsersRequest, userIds);

  for (const externalId of userIds) {
    await App.main.queue.enqueue(
      UserDeleteJob.from({
        location_id: ctx.state.location.id,
        external_id: externalId,
      })
    );
  }

  ctx.status = 204;
  ctx.body = "";
});

// Add re-vectorization endpoint
/**
 * @swagger
 * /users/re-vectorize:
 *   post:
 *     summary: Re-vectorize User Data
 *     description: Triggers re-vectorization of user data for the current location
 *     tags: [User]
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               batch_size:
 *                 type: integer
 *                 description: Number of users to process in each batch
 *               clean_start:
 *                 type: boolean
 *                 description: Whether to delete existing vectors before re-vectorization
 *     responses:
 *       200:
 *         description: Re-vectorization started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 status:
 *                   type: string
 *                 job_id:
 *                   type: string
 *                 job_type:
 *                   type: string
 *                 clean_start:
 *                   type: boolean
 *       500:
 *         description: Internal server error
 */
router.post("/re-vectorize", locationRoleMiddleware("admin"), async (ctx) => {
  const location_id = ctx.state.location.id;
  const { batch_size = 100, clean_start = true } = ctx.request.body;

  try {
    // First ensure the index exists
    await UserDataVectorService.ensureIndexExists();

    // If clean_start is true, delete all existing vectors for this location first
    if (clean_start) {
      logger.info(
        `Deleting existing vectors for location ${location_id} before re-vectorization`
      );
      await UserDataVectorService.deleteUserData(location_id);
      logger.info(
        `Successfully deleted existing vectors for location ${location_id}`
      );
    }

    // Start a job tracker for this process
    const jobType = "user_data_vectorization";
    OnboardingJobTracker.startJob(location_id, jobType);

    // Import UserDataVectorJob and ensure it's properly queued
    const UserDataVectorJob = require("./UserDataVectorJob").default;

    // Create and queue a new job with proper parameters
    await UserDataVectorJob.from({
      location_id,
      batch_size,
      last_processed_id: 0, // Start from the beginning
    }).queue();

    logger.info(`Re-vectorization job queued for location ${location_id}`);

    ctx.status = 200;
    ctx.body = {
      message: "User data re-vectorization started",
      status: "processing",
      job_id: location_id,
      job_type: jobType,
      clean_start,
    };
  } catch (error) {
    logger.error(
      `Error starting re-vectorization for location ${location_id}:`,
      error
    );
    ctx.status = 500;
    ctx.body = {
      error: "Failed to start re-vectorization",
      details: error instanceof Error ? error.message : String(error),
    };
  }
});

// Add endpoint to check vectorization status
/**
 * @swagger
 * /users/vectorization-status:
 *   get:
 *     summary: Get Vectorization Status
 *     description: Retrieves the status of user data vectorization for the current location
 *     tags: [User]
 *     responses:
 *       200:
 *         description: Vectorization status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 isProcessing:
 *                   type: boolean
 *                 total:
 *                   type: integer
 *                 completed:
 *                   type: integer
 *                 failed:
 *                   type: integer
 *                 pending:
 *                   type: integer
 *                 processing:
 *                   type: integer
 *                 jobs:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       jobType:
 *                         type: string
 *                       status:
 *                         type: string
 *                       startedAt:
 *                         type: string
 *                         format: date-time
 *                       completedAt:
 *                         type: string
 *                         format: date-time
 *                       error:
 *                         type: string
 *       500:
 *         description: Internal server error
 */
router.get(
  "/vectorization-status",
  locationRoleMiddleware("support"),
  async (ctx) => {
    const location_id = ctx.state.location.id;

    try {
      const jobType = "user_data_vectorization";
      const allJobsSummary = OnboardingJobTracker.getSummary(location_id);

      // Filter jobs to only include those of the user_data_vectorization type
      const jobsOfThisType = allJobsSummary.jobs.filter(
        (job) => job.jobType === jobType
      );

      // Create filtered summary for this job type only
      const summary = {
        isProcessing: jobsOfThisType.some((job) => job.status === "processing"),
        total: jobsOfThisType.length,
        completed: jobsOfThisType.filter((job) => job.status === "completed")
          .length,
        failed: jobsOfThisType.filter((job) => job.status === "failed").length,
        pending: jobsOfThisType.filter((job) => job.status === "pending")
          .length,
        processing: jobsOfThisType.filter((job) => job.status === "processing")
          .length,
        jobs: jobsOfThisType,
      };

      // Get current vectorization statistics
      const countResult = await User.query()
        .where("location_id", location_id)
        .count("id as total")
        .first();

      const dbRecordCount = countResult
        ? parseInt(countResult.total as string, 10)
        : 0;

      // Get vector stats using namespace-based approach
      const vectorStats = await UserDataVectorService.getLocationStats(
        location_id
      );

      // Determine detailed vectorization status
      let vectorizationStatus = "unknown";
      if (dbRecordCount === 0) {
        vectorizationStatus = "no_data";
      } else if (vectorStats.recordCount === 0) {
        vectorizationStatus = "not_started";
      } else if (vectorStats.recordCount === dbRecordCount) {
        vectorizationStatus = "complete";
      } else if (vectorStats.recordCount < dbRecordCount) {
        vectorizationStatus = "incomplete";
      } else if (vectorStats.recordCount > dbRecordCount) {
        vectorizationStatus = "excess"; // More vectors than records
      }

      // Determine job status from tracker
      const jobStatus = summary.isProcessing
        ? "processing"
        : summary.failed > 0
        ? "failed"
        : summary.completed > 0
        ? "completed"
        : "not_started";

      // Combined status that takes both job and vector counts into account
      let combinedStatus = jobStatus;

      // If job shows completed but vectors don't match DB, override the status
      if (jobStatus === "completed" && vectorizationStatus !== "complete") {
        if (vectorStats.recordCount > 0) {
          combinedStatus = "incomplete";
          logger.info(
            `Job shows completed but only ${vectorStats.recordCount}/${dbRecordCount} vectors found for location ${location_id}`
          );
        } else {
          combinedStatus = "not_started";
          logger.info(
            `Job shows completed but no vectors found for location ${location_id}`
          );
        }
      }

      // If job not started but we have vectors, report proper status based on vector count
      if (jobStatus === "not_started" && vectorStats.recordCount > 0) {
        if (vectorStats.recordCount === dbRecordCount) {
          combinedStatus = "completed";
          logger.info(
            `No job record but full vectorization confirmed with ${vectorStats.recordCount}/${dbRecordCount} vectors for location ${location_id}`
          );
        } else {
          combinedStatus = "incomplete";
          logger.info(
            `No job record but partial vectorization detected with ${vectorStats.recordCount}/${dbRecordCount} vectors for location ${location_id}`
          );
        }
      }

      // Calculate completion percentage
      const completionPercentage =
        dbRecordCount > 0
          ? Math.round((vectorStats.recordCount / dbRecordCount) * 100)
          : 0;

      ctx.body = {
        // Combined final status
        status: combinedStatus,

        // Detailed information
        job_status: jobStatus,
        job_summary: summary,

        // Vector statistics
        namespace: vectorStats.namespace,
        vector_count: vectorStats.recordCount,
        db_record_count: dbRecordCount,
        vector_status: vectorizationStatus,
        is_fully_indexed: vectorStats.recordCount === dbRecordCount,
        completion_percentage: completionPercentage,

        // Include error information if any
        error: vectorStats.error,
      };
    } catch (error) {
      logger.error(
        `Error getting vectorization status for location ${location_id}:`,
        error
      );
      ctx.status = 500;
      ctx.body = {
        error: "Failed to get vectorization status",
        details: error instanceof Error ? error.message : String(error),
      };
    }
  }
);

router.param("userId", async (value, ctx, next) => {
  ctx.state.user = await getUserFromContext(ctx);
  if (!ctx.state.user) {
    ctx.throw(404);
    return;
  }
  return await next();
});

router.get("/:userId", async (ctx) => {
  ctx.body = ctx.state.user;
});

/**
 * @swagger
 * /users/{userId}:
 *   patch:
 *     summary: Update User
 *     description: Updates a single user by ID
 *     tags: [User]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UserParams'
 *     responses:
 *       200:
 *         description: User updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/User'
 *       400:
 *         description: Invalid input
 *       404:
 *         description: User not found
 */
router.patch("/:userId", locationRoleMiddleware("editor"), async (ctx) => {
  const userParams = ctx.request.body as UserParams;

  // Queue the user update job
  await App.main.queue.enqueue(
    UserPatchJob.from({
      location_id: ctx.state.location.id,
      user: {
        ...userParams,
        external_id: ctx.state.user!.external_id, // Ensure we use the existing external_id
      },
    })
  );

  // Return the updated user
  ctx.body = await getUser(ctx.state.user!.id);
});

router.delete("/:userId", locationRoleMiddleware("editor"), async (ctx) => {
  await App.main.queue.enqueue(
    UserDeleteJob.from({
      location_id: ctx.state.location.id,
      external_id: ctx.state.user!.external_id,
    })
  );

  ctx.status = 204;
  ctx.body = "";
});

router.get("/:userId/lists", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await getUserLists(
    ctx.state.user!.id,
    params,
    ctx.state.location.id
  );
});

router.get("/:userId/events", async (ctx) => {
  const searchSchema = SearchSchema("userEventSearchSchema", {
    sort: "id",
    direction: "desc",
  });
  const params = extractQueryParams(ctx.query, searchSchema);
  ctx.body = await getUserEvents(
    ctx.state.user!.id,
    params,
    ctx.state.location.id
  );
});

router.get("/:userId/subscriptions", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await getUserSubscriptions(
    ctx.state.user!.id,
    params,
    ctx.state.location.id
  );
});

router.patch("/:userId/subscriptions", async (ctx) => {
  const subscriptions = ctx.request.body as Array<{
    subscription_id: number;
    state: SubscriptionState;
  }>;
  for (const subscription of subscriptions) {
    await toggleSubscription(
      ctx.state.user!.id,
      subscription.subscription_id,
      subscription.state
    );
  }
  ctx.body = await getUser(ctx.state.user!.id);
});

router.get("/:userId/automations", async (ctx) => {
  const params = extractQueryParams(ctx.query, searchParamsSchema);
  ctx.body = await pagedEntrancesByUser(ctx.state.user!.id, params);
});

router.post("/", locationRoleMiddleware("editor"), async (ctx) => {
  const stream = await parse(ctx);

  const result = await importUsers({
    location_id: ctx.state.location.id,
    stream,
  });

  ctx.status = 200;
  ctx.body = {
    message: `Successfully processed ${result.processed} users`,
    errors: result.errors,
  };
});

export default router;
