import Job from "../queue/Job";
import { User } from "./User";
import { OnboardingJobTracker } from "../locations/OnboardingJobTracker";
import { UserDataVectorService } from "./UserDataVectorService";
import { logger } from "../config/logger";

const MAX_RETRIES = 3;

interface UserDataVectorParams {
  location_id: number;
  batch_size?: number;
  last_processed_id?: number;
  retry_count?: number;
  failed_ids?: number[];
}

class UserDataVectorJob extends Job {
  static $name = "user-data-vector-job";

  location_id!: number;
  batch_size?: number;
  last_processed_id?: number;
  retry_count?: number;
  failed_ids?: number[];

  static from(params: UserDataVectorParams) {
    logger.info(
      `Creating user data vector job for location ${params.location_id}`,
      {
        location_id: params.location_id,
        batch_size: params.batch_size || 100,
        last_processed_id: params.last_processed_id || 0,
        retry_count: params.retry_count || 0,
        failed_ids_count: params.failed_ids?.length || 0,
      }
    );

    return new this({
      location_id: params.location_id,
      batch_size: params.batch_size || 100,
      last_processed_id: params.last_processed_id || 0,
      retry_count: params.retry_count || 0,
      failed_ids: params.failed_ids || [],
    });
  }

  static async handler({
    location_id,
    batch_size = 100,
    last_processed_id = 0,
    retry_count = 0,
    failed_ids = [],
  }: UserDataVectorParams) {
    try {
      // Initialize services
      await UserDataVectorService.initialize();

      // Define job type for tracking
      const jobType = "user_data_vectorization";

      logger.info(
        `Processing user data vectorization for location ${location_id}, batch starting from ID ${last_processed_id}`
      );

      // Query next batch of users to process
      let users: User[] = [];

      if (failed_ids && failed_ids.length > 0) {
        // Process failed IDs first if any
        logger.info(
          `Retrying vectorization for ${failed_ids.length} failed records`
        );
        users = await User.query().whereIn("id", failed_ids);
      } else {
        // Process normal queue of users
        users = await User.query()
          .where("location_id", location_id)
          .where("id", ">", last_processed_id)
          .orderBy("id", "asc")
          .limit(batch_size);
      }

      if (users.length === 0) {
        logger.info(
          `No more users to process for location ${location_id}. Vectorization complete.`
        );

        // Mark job as completed
        OnboardingJobTracker.completeJob(location_id, jobType);
        return;
      }

      // Get the highest ID for the next batch
      const highestProcessedId = users[users.length - 1].id;

      // Vectorize the users
      const result = await UserDataVectorService.upsertUserData(users);

      // Log detailed results
      logger.info(
        `Vectorized ${result.successCount} user records for location ${location_id}, ${result.errorCount} errors`
      );

      // Handle failures and retries
      if (
        result.errorCount > 0 &&
        result.failedIds &&
        result.failedIds.length > 0
      ) {
        // Extract numeric IDs from the returned failedIds
        const failedNumericIds = result.failedIds
          .map((id) => {
            const match = id.match(/^user_(\d+)$/);
            return match ? parseInt(match[1], 10) : null;
          })
          .filter((id): id is number => id !== null);

        // If we have failures and haven't exceeded max retries, queue a retry job
        if (failedNumericIds.length > 0 && retry_count < MAX_RETRIES) {
          logger.warn(
            `Scheduling retry ${retry_count + 1}/${MAX_RETRIES} for ${
              failedNumericIds.length
            } failed records`
          );

          await UserDataVectorJob.from({
            location_id,
            batch_size: Math.max(10, Math.floor(batch_size / 2)),
            last_processed_id: highestProcessedId,
            retry_count: retry_count + 1,
            failed_ids: failedNumericIds,
          })
            .delay(2000 * retry_count + 1000) // Increasing delay with each retry
            .queue();
        } else if (failedNumericIds.length > 0) {
          // We've exceeded max retries, log a critical error
          logger.error(
            `Failed to vectorize ${failedNumericIds.length} user records after ${MAX_RETRIES} retries`
          );
          OnboardingJobTracker.failJob(
            location_id,
            jobType,
            `Failed to vectorize ${failedNumericIds.length} user records after ${MAX_RETRIES} retries`
          );
        }
      }

      // Queue next batch if we're not in retry mode and have a full batch
      if (
        (!failed_ids || failed_ids.length === 0) &&
        users.length === batch_size
      ) {
        logger.info(
          `Queueing next batch of user data after ID ${highestProcessedId}`
        );

        await UserDataVectorJob.from({
          location_id,
          batch_size,
          last_processed_id: highestProcessedId,
        })
          .delay(1000) // Small delay between batches
          .queue();
      } else {
        // This was the last batch, mark as complete
        OnboardingJobTracker.completeJob(location_id, jobType);
      }

      return true;
    } catch (error) {
      logger.error(`Error in user data vectorization job:`, error);

      // Track failure in job tracker
      OnboardingJobTracker.failJob(
        location_id,
        "user_data_vectorization",
        error instanceof Error ? error.message : String(error)
      );

      // Retry the entire job if we haven't exceeded max retries
      if (retry_count < MAX_RETRIES) {
        const nextRetryCount = retry_count + 1;
        const delayMs = Math.pow(2, nextRetryCount) * 1000; // Exponential backoff

        logger.info(
          `Scheduling full job retry ${nextRetryCount}/${MAX_RETRIES} after ${delayMs}ms`
        );

        await UserDataVectorJob.from({
          location_id,
          batch_size,
          last_processed_id,
          retry_count: nextRetryCount,
          failed_ids,
        })
          .delay(delayMs)
          .queue();
      } else {
        logger.error(
          `Failed to process user data batch after ${MAX_RETRIES} retries`
        );
      }

      throw error;
    }
  }
}

export default UserDataVectorJob;
