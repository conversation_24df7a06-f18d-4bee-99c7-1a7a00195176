import { User } from "./User";
import { VectorService, VectorData } from "../core/VectorService";
import { logger } from "../config/logger";

const USER_INDEX = "user-embeddings";

export class UserDataVectorService {
  private static vectorService: VectorService | null = null;

  /**
   * Get the vector service instance
   */
  private static getVectorService(): VectorService {
    if (!this.vectorService) {
      this.vectorService = VectorService.getInstance();
    }
    return this.vectorService;
  }

  static async initialize() {
    await this.getVectorService().initialize();
  }

  /**
   * Converts a location ID to a namespace string
   */
  private static getNamespace(locationId: number): string {
    return `location-${locationId}`;
  }

  static async upsertUserData(users: User[]) {
    try {
      if (!users.length) {
        logger.warn("No user data provided to upsert, skipping vectorization");
        return { successCount: 0, errorCount: 0, failedIds: [] };
      }

      // Group records by location ID for namespace-based processing
      const recordsByLocation = users.reduce((groups, record) => {
        const namespace = this.getNamespace(record.location_id);
        if (!groups[namespace]) {
          groups[namespace] = [];
        }
        groups[namespace].push(record);
        return groups;
      }, {} as Record<string, User[]>);

      const totalResults = {
        successCount: 0,
        errorCount: 0,
        failedIds: [] as string[],
      };

      // Process each location group with its own namespace
      for (const [namespace, records] of Object.entries(recordsByLocation)) {
        // Convert user data to vector format with enhanced text representation
        const vectorData: VectorData[] = records.map((record) => ({
          id: `user_${record.id}`,
          text: this.generateEnhancedText(record),
          metadata: {
            location_id: record.location_id,
            source_type: "user",
            source_id: record.id.toString(),
            external_id: record.external_id,
            email: record.email || "",
            phone: record.phone || "",
            timezone: record.timezone || "",
            locale: record.locale || "",
            first_name: record.firstName || "",
            last_name: record.lastName || "",
            full_name: record.fullName || "",
            created_at: Date.now(),
            updated_at: Date.now(),
          },
        }));

        // Use centralized vector service to upsert data with namespace
        const result = await this.getVectorService().upsertVectors(
          USER_INDEX,
          vectorData,
          undefined, // Use default batch size
          namespace
        );

        // Aggregate results
        totalResults.successCount += result.successCount;
        totalResults.errorCount += result.errorCount;
        totalResults.failedIds = totalResults.failedIds.concat(
          result.failedIds
        );

        logger.info(
          `User data vectorization for namespace ${namespace} complete: ${result.successCount} succeeded, ${result.errorCount} failed`
        );
      }

      return totalResults;
    } catch (error) {
      logger.error("Error upserting user data vectors:", error);
      throw error;
    }
  }

  static async queryUserData(
    query: string,
    filters: { [key: string]: any } = {},
    topK: number = 10
  ) {
    try {
      if (!query) {
        throw new Error("Empty query provided to user data vector search");
      }

      // Extract location_id from filters for namespace
      const locationId = filters.location_id;
      if (!locationId && typeof locationId !== "number") {
        logger.warn(
          "No location_id provided in filters for user data query, this may cause errors"
        );
        // Return empty results when no location ID is specified
        return [];
      }

      // Get namespace for this location
      const namespace = this.getNamespace(locationId);

      // Remove location_id from filters since we're using namespace instead
      const { location_id, ...otherFilters } = filters;

      // Check if index exists first to avoid unnecessary failures
      try {
        // Try to ensure index exists first (ignore errors from this check)
        const indexExists = await this.ensureIndexExists();
        logger.info(
          `User index status: ${indexExists ? "exists" : "creation attempted"}`
        );
      } catch (indexCheckError) {
        logger.warn({
          message: "Error checking user index existence",
          error:
            indexCheckError instanceof Error
              ? indexCheckError.message
              : String(indexCheckError),
          stack:
            indexCheckError instanceof Error
              ? indexCheckError.stack
              : undefined,
        });
        // Continue despite index check error
      }

      try {
        return await this.getVectorService().queryVectors(
          USER_INDEX,
          query,
          otherFilters, // Use other filters except location_id
          topK,
          namespace // Pass namespace for location-specific search
        );
      } catch (error) {
        // Enhanced error logging
        logger.error({
          message: "Error in vector query operation",
          error: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined,
          query,
          namespace,
          filters: otherFilters,
          index: USER_INDEX,
        });

        // Handle dimension mismatch errors
        if (error instanceof Error && error.message.includes("dimension")) {
          logger.warn(
            `Dimension mismatch detected in user index. Attempting to fix by recreating index.`
          );

          await this.recreateIndexWithCorrectDimension();

          // Try the query again after recreating the index
          logger.info("Retrying query after index recreation");

          try {
            // Note: This will only work if the index was successfully recreated AND there is data in it
            return await this.getVectorService().queryVectors(
              USER_INDEX,
              query,
              otherFilters,
              topK,
              namespace
            );
          } catch (retryError) {
            logger.error(
              "Second attempt at querying after dimension fix failed:",
              retryError
            );
            return [];
          }
        }

        // Handle 404 errors specifically
        if (error instanceof Error && error.message.includes("404")) {
          logger.warn(
            `User index "${USER_INDEX}" not found, attempting to create it`
          );
          await this.ensureIndexExists();

          // Return empty results instead of throwing
          return [];
        }

        // Handle other specific errors
        if (error instanceof Error && error.message.includes("filter")) {
          logger.warn(`Filter error with user data query: ${error.message}`);
          // Try with a simplified filter as last resort
          try {
            logger.info("Attempting query with simplified filter");
            return await this.getVectorService().queryVectors(
              USER_INDEX,
              query,
              { source_type: "user" },
              topK,
              namespace
            );
          } catch (e) {
            logger.error("Second attempt at querying user data failed:", e);
            return [];
          }
        }

        throw error;
      }
    } catch (error) {
      logger.error({
        message: "Error querying user data vectors",
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        query,
      });
      // Return empty results instead of throwing
      return [];
    }
  }

  /**
   * Creates an enhanced text representation of user data for better semantic matching
   */
  private static generateEnhancedText(record: User): string {
    // Base text with core information
    let text = `User: ${record.fullName || "Unknown"}, ID: ${
      record.external_id
    }`;

    // Add contact information
    if (record.email) {
      text += `, Email: ${record.email}`;
    }
    if (record.phone) {
      text += `, Phone: ${record.phone}`;
    }

    // Add preferences
    if (record.timezone) {
      text += `, Timezone: ${record.timezone}`;
    }
    if (record.locale) {
      text += `, Language: ${record.locale}`;
    }

    // Add custom attributes from data object
    if (record.data && typeof record.data === "object") {
      Object.entries(record.data).forEach(([key, value]) => {
        // Skip name fields since they're already included
        if (
          ![
            "first_name",
            "firstName",
            "last_name",
            "lastName",
            "full_name",
            "fullName",
          ].includes(key)
        ) {
          // Format the key for better readability
          const formattedKey = key
            .replace(/_/g, " ")
            .replace(/\b\w/g, (l) => l.toUpperCase());

          // Add the attribute to the text
          if (value !== null && value !== undefined && value !== "") {
            text += `, ${formattedKey}: ${value}`;
          }
        }
      });
    }

    return text;
  }

  /**
   * Validates that the vector index exists and creates it if needed
   */
  static async ensureIndexExists() {
    try {
      await this.getVectorService().initialize();

      // Check if index exists first by listing indices
      const indices = await this.getVectorService().listIndices();
      const indexExists = indices.includes(USER_INDEX);

      if (!indexExists) {
        logger.info(`User index '${USER_INDEX}' not found, creating it`);

        // Attempt to create the index if it doesn't exist
        await this.getVectorService().createIndex(USER_INDEX, {
          dimension: 3072, // Updated dimension for text-embedding-3-large model
          metric: "cosine",
          serverless: {
            cloud: "aws",
            region: "us-east-1",
          },
        });

        logger.info(`User index '${USER_INDEX}' created successfully`);
      } else {
        logger.info(`User index '${USER_INDEX}' already exists`);
      }

      return true;
    } catch (error) {
      logger.error(`Failed to verify user index: ${error}`);
      // Don't throw, just return false
      return false;
    }
  }

  static async deleteUserData(locationId?: number, externalId?: string) {
    try {
      if (locationId) {
        const namespace = this.getNamespace(locationId);

        if (externalId) {
          // Delete specific user in a namespace
          return await this.getVectorService().deleteVectors(USER_INDEX, {
            external_id: externalId,
          });
        } else {
          // Delete all vectors in this location's namespace
          return await this.getVectorService().deleteNamespace(USER_INDEX, namespace);
        }
      } else {
        // Without a location ID, we can't determine the namespace
        logger.warn(
          "Attempted to delete user data without specifying location_id"
        );
        return false;
      }
    } catch (error) {
      logger.error("Error deleting user data vectors:", error);
      throw error;
    }
  }

  /**
   * Empties the entire user index, removing all vectors across all namespaces
   * Use with caution as this will delete all user data vectors
   */
  static async emptyIndex() {
    try {
      logger.warn(
        "Emptying entire user index - this will delete ALL user vectors for ALL locations"
      );
      return await this.getVectorService().emptyIndex(USER_INDEX);
    } catch (error) {
      logger.error("Error emptying user index:", error);
      throw error;
    }
  }

  /**
   * Delete and recreate the index with the correct dimension
   */
  static async recreateIndexWithCorrectDimension() {
    try {
      logger.info(
        `Attempting to recreate user index '${USER_INDEX}' with correct dimension`
      );

      // First, try to delete the existing index
      try {
        logger.info(`Deleting existing user index '${USER_INDEX}'`);
        await this.getVectorService().deleteIndex(USER_INDEX);
        logger.info(`Successfully deleted user index '${USER_INDEX}'`);
      } catch (deleteError) {
        logger.error(`Error deleting user index: ${deleteError}`);
        // Continue even if delete fails - it might not exist
      }

      // Then create a new index with the correct dimension
      logger.info(`Creating new user index with dimension 3072`);
      await this.getVectorService().createIndex(USER_INDEX, {
        dimension: 3072, // For text-embedding-3-large
        metric: "cosine",
        serverless: {
          cloud: "aws",
          region: "us-east-1",
        },
      });

      logger.info(`Successfully recreated user index with correct dimension`);
      return true;
    } catch (error) {
      logger.error(`Failed to recreate user index: ${error}`);
      return false;
    }
  }

  /**
   * Gets vector statistics for a specific location namespace
   */
  static async getLocationStats(locationId: number) {
    try {
      const namespace = this.getNamespace(locationId);
      const stats = await this.getVectorService().getIndexStats(USER_INDEX);

      // Check if the namespace exists in the stats
      if (stats.namespaces && stats.namespaces[namespace]) {
        return {
          recordCount: stats.namespaces[namespace].recordCount || 0,
          vectorCount: stats.namespaces[namespace].recordCount || 0, // Backward compatibility
          namespace,
        };
      }

      return {
        recordCount: 0,
        vectorCount: 0, // Backward compatibility
        namespace,
      };
    } catch (error) {
      logger.error(`Error getting stats for location ${locationId}:`, error);
      return {
        recordCount: 0,
        vectorCount: 0, // Backward compatibility
        namespace: this.getNamespace(locationId),
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
}
