import App from "../app";
import { Job } from "../queue";

interface UserDeleteTrigger {
  location_id: number;
  external_id: string;
}

export default class UserDeleteJob extends Job {
  static $name = "user_delete";

  static from(data: UserDeleteTrigger): UserDeleteJob {
    return new this(data);
  }

  static async handler({ location_id, external_id }: UserDeleteTrigger) {
    await App.main.db.transaction(async (trx) =>
      trx("users").where({ location_id, external_id }).delete()
    );
  }
}
