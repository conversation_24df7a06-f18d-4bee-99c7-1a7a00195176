import { FileStream } from "../storage/FileStream";
import { parse, Options } from "csv-parse";
import UserPatchJob from "./UserPatchJob";
import ListStatsJob from "../lists/ListStatsJob";
import { RequestError } from "../core/errors";
import App from "../app";
import { Chunker } from "../utilities";
import { DataCleaningService } from "../core/DataCleaningService";

export interface UserImport {
  location_id: number;
  stream: FileStream;
  list_id?: number;
}

export const importUsers = async ({
  location_id,
  stream,
  list_id,
}: UserImport) => {
  const options: Options = {
    columns: true,
    cast: true,
    skip_empty_lines: true,
    bom: true,
  };

  const chunker = new Chunker<UserPatchJob>(
    (items) => App.main.queue.enqueueBatch(items),
    App.main.queue.batchSize
  );

  const originalRows: Record<string, any>[] = [];
  const cleanedRows: Record<string, any>[] = [];

  const parser = stream.file.pipe(parse(options));
  for await (const row of parser) {
    originalRows.push({ ...row });
    const cleanedRow = DataCleaningService.cleanRow(row);
    cleanedRows.push(cleanedRow);

    const { external_id, email, phone, timezone, locale, created_at, ...data } = cleanedRow;

    if (!external_id) {
      throw new RequestError(
        "Every upload must contain a column `external_id` which contains the identifier for that user."
      );
    }

    await chunker.add(
      UserPatchJob.from({
        location_id,
        user: {
          external_id: `${external_id}`,
          email,
          phone,
          timezone,
          locale,
          data,
          created_at,
        },
        options: {
          join_list_id: list_id,
        },
      })
    );
  }

  await chunker.flush();

  // Log cleaning statistics
  const stats = DataCleaningService.getCleaningStats(originalRows, cleanedRows);
  console.log("User import cleaning statistics:", {
    totalRows: stats.totalRows,
    fieldsWithDefaults: stats.fieldsWithDefaults,
    emptyFieldsFound: stats.emptyFieldsFound,
  });

  // Generate preliminary list count
  if (list_id) {
    await ListStatsJob.from(list_id, location_id, true).delay(2000).queue();
  }
};


