import { FileStream } from "../storage/FileStream";
import { RequestError } from "../core/errors";
import App from "../app";
import { User, UserParams } from "./User";
import UserPatchJob from "./UserPatchJob";
import { DataNormalizationService } from "../core/DataNormalizationService";

interface UserImportParams {
  location_id: number;
  stream: FileStream;
  onProgress?: (count: number) => void;
}

export const importUsers = async ({
  location_id,
  stream,
  onProgress,
}: UserImportParams) => {
  if (!location_id || !stream) {
    throw new RequestError("Missing required parameters");
  }

  try {
    // Use the normalization service to process the data
    const normalizedData = await DataNormalizationService.normalizeData(stream);

    // Verify this is customer data
    if (normalizedData.type !== "customer") {
      throw new RequestError(
        "The uploaded file appears to be POS data. Please use the POS data import endpoint."
      );
    }

    // Report any errors found during normalization
    if (normalizedData.errors.length > 0) {
      const errorMessages = normalizedData.errors.map(
        (err) => `Row ${err.row}: ${err.error}`
      );
      throw new RequestError(
        `Data validation errors:\n${errorMessages.join("\n")}`
      );
    }

    let rowCount = 0;
    for (const row of normalizedData.data) {
      rowCount++;
      try {
        // Queue user update job
        await App.main.queue.enqueue(
          UserPatchJob.from({
            location_id,
            user: row as UserParams,
          })
        );

        if (onProgress && rowCount % 100 === 0) {
          onProgress(rowCount);
        }
      } catch (error: any) {
        console.error(`Error processing row ${rowCount}:`, error);
        continue; // Skip failed rows instead of stopping
      }
    }

    if (onProgress) {
      onProgress(rowCount);
    }

    return {
      processed: rowCount,
      errors: normalizedData.errors,
    };
  } catch (error) {
    console.error("Error in user import:", error);
    throw error;
  }
};
