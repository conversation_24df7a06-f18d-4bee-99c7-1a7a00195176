import { User, UserInternalParams } from "./User";
import { Job } from "../queue";
import { createUser, getUsersFromIdentity } from "./UserRepository";
import { addUserToList, getList, updateUsersLists } from "../lists/ListService";
import { ClientIdentity } from "../client/Client";
import { matchingRulesForUser } from "../rules/RuleService";

interface UserPatchTrigger {
  location_id: number;
  user: UserInternalParams;
  options?: {
    join_list_id?: number;
    skip_list_updating?: boolean;
  };
}

export default class UserPatchJob extends Job {
  static $name = "user_patch";

  static from(data: UserPatchTrigger): UserPatchJob {
    return new this(data);
  }

  static async handler(patch: UserPatchTrigger): Promise<User> {
    console.log("Starting UserPatchJob handler with data:", {
      location_id: patch.location_id,
      external_id: patch.user.external_id,
      email: patch.user.email,
      phone: patch.user.phone,
    });

    const upsert = async (
      patch: UserPatchTrigger,
      tries = 3
    ): Promise<User> => {
      const {
        location_id,
        user: { external_id, anonymous_id, data, ...fields },
      } = patch;
      const identity = { external_id, anonymous_id } as ClientIdentity;

      console.log(`Checking for existing user with identity:`, {
        location_id,
        external_id,
        anonymous_id,
      });

      // Check for existing user
      const { anonymous, external } = await getUsersFromIdentity(
        location_id,
        identity
      );
      const existing = external ?? anonymous;

      console.log("User lookup results:", {
        existingUserFound: !!existing,
        existingUserId: existing?.id,
        existingExternalId: existing?.external_id,
        isAnonymousUser: !!anonymous && !external,
      });

      // TODO: Utilize phone and email as backup identifiers
      // to decrease the likelihood of future duplicates

      // If user, update otherwise insert
      try {
        let result;
        if (existing) {
          console.log(`Updating existing user ${existing.id}`);
          result = await User.updateAndFetch(existing.id, {
            data: data ? { ...existing.data, ...data } : undefined,
            ...fields,
            ...(!anonymous ? { anonymous_id } : {}),
          });
          console.log(`Successfully updated user ${result.id}`);
        } else {
          console.log(`Creating new user for external_id: ${external_id}`);
          result = await createUser(location_id, {
            ...identity,
            data,
            ...fields,
          });
          console.log(`Successfully created new user ${result.id}`);
        }
        return result;
      } catch (error: any) {
        // If there is an error (such as constraints,
        // retry up to three times)
        console.error(
          `Error during user upsert (tries left: ${tries - 1}):`,
          error
        );
        if (tries <= 0) throw error;
        return upsert(patch, --tries);
      }
    };

    const user = await upsert(patch);

    const { join_list_id, skip_list_updating = false } = patch.options ?? {};

    // Use updated user to check for list membership
    if (!skip_list_updating) {
      const results = await matchingRulesForUser(user);
      await updateUsersLists(user, results);
    }

    // If provided a list to join, add user to it
    if (join_list_id) {
      const list = await getList(join_list_id, patch.location_id);
      if (list) await addUserToList(user, list);
    }

    return user;
  }
}
