require('dotenv').config();

console.log('Environment variables:');
console.log('DB_CLIENT:', process.env.DB_CLIENT);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_USERNAME:', process.env.DB_USERNAME);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_DATABASE:', process.env.DB_DATABASE);
console.log('DB_FILENAME:', process.env.DB_FILENAME);

// Test the env.ts configuration
const env = require('./src/config/env.ts').default();
console.log('\nParsed database config:');
console.log(JSON.stringify(env.db, null, 2));
