require('dotenv').config({ path: '.env' });
require('dotenv').config({ path: '../../.env' });

console.log('Firebase Environment Variables:');
console.log('FIREBASE_PROJECT_ID:', process.env.FIREBASE_PROJECT_ID || '(missing)');
console.log('FIREBASE_CLIENT_EMAIL:', process.env.FIREBASE_CLIENT_EMAIL || '(missing)');
console.log('FIREBASE_PRIVATE_KEY:', process.env.FIREBASE_PRIVATE_KEY ? 'Present (length: ' + process.env.FIREBASE_PRIVATE_KEY.length + ')' : '(missing)');
console.log('FIREBASE_STORAGE_BUCKET:', process.env.FIREBASE_STORAGE_BUCKET || '(missing)');

// Test the storage configuration
const env = require('./src/config/env.ts').default();
console.log('\nStorage configuration:');
console.log('Driver:', env.storage.driver);
if (env.storage.driver === 'firebase') {
  console.log('Storage credentials:');
  console.log('- project_id:', env.storage.credentials?.project_id || '(missing)');
  console.log('- client_email:', env.storage.credentials?.client_email || '(missing)');
  console.log('- private_key:', env.storage.credentials?.private_key ? 'Present' : '(missing)');
  console.log('- bucket:', env.storage.bucket || '(missing)');
}
