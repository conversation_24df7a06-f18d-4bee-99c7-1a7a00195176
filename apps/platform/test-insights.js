// Test script to demonstrate new agent-based insight generation
const {
  agentBasedInsightService,
} = require("./build/insights/AgentBasedInsightService");

async function testAgentInsights() {
  console.log("🚀 Testing Agent-Based Insight Generation\n");

  try {
    // Test with a sample location ID
    const locationId = 1;
    const timeframe = "30d";

    console.log(`📊 Generating insights for location ${locationId}...`);
    const insights = await agentBasedInsightService.generateInsightsWithAgents(
      locationId,
      timeframe
    );

    console.log(`✅ Generated ${insights.length} insights!\n`);

    // Display insights with agent attribution
    insights.forEach((insight, index) => {
      console.log(`${index + 1}. ${insight.title}`);
      console.log(`   Agent: ${insight.agent_name} (${insight.agent_id})`);
      console.log(`   Impact: ${insight.impact}`);
      console.log(`   Type: ${insight.type}`);
      console.log(`   Description: ${insight.description}`);
      console.log(`   Actions: ${insight.actions}`);
      console.log("   ---");
    });
  } catch (error) {
    console.error("❌ Error generating insights:", error);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  testAgentInsights()
    .then(() => {
      console.log("\n🎉 Test completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Test failed:", error);
      process.exit(1);
    });
}

module.exports = { testAgentInsights };
