{"extends": "./tsconfig.base.json", "compilerOptions": {"strict": true, "module": "commonjs", "moduleResolution": "node", "baseUrl": "./src", "outDir": "./build", "rootDir": "./src", "typeRoots": ["node_modules/@types", "@types"], "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true}, "include": ["./src/**/*", "@types", "db/migrations/20240116_create_location_competitors.ts", "PromptService.ts"], "exclude": ["node_modules", "./**/*.spec.ts", "./**/__mocks__/*"]}