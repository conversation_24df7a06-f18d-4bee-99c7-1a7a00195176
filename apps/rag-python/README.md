# RAG Python Microservice

This Python microservice provides advanced RAG (Retrieval Augmented Generation) capabilities that are easier to implement in Python than in Node.js.

## Features

- **Re-ranking**: Cross-encoder re-ranking for more accurate document relevance scoring
- **LangGraph**: Complex multi-step agents using state-machine workflows (future)
- **LlamaIndex**: Advanced document indexing and retrieval (future)
- **Analysis**: Statistical and time-series analysis using Python libraries (future)

## Integration with Node.js

This microservice is integrated with the Node.js application through the `RemotePythonTool` located at `apps/platform/src/tools/RemotePythonTool.ts`.

## Getting Started

### Running in Docker

The easiest way to run this service is using Docker Compose:

```bash
docker compose up rag-python
```

### Development

For development, you can use the dev container with hot-reload:

```bash
docker compose -f docker-compose.dev.yml up rag-python
```

All code changes will automatically reload the service.

### Testing

You can test the service with the included test client:

```bash
docker exec -it marketing_auto-rag-python-1 python test_client.py
```

## API Endpoints

- `GET /health` - Health check endpoint
- `POST /api/v1/rerank` - Re-rank documents with cross-encoder
- `POST /api/v1/analyze` - Run analysis using Python tools

## API Usage

### Re-ranking Example

```typescript
// In Node.js using the RemotePythonTool
const remoteTool = new RemotePythonTool();

const results = await remoteTool.rerank(
  "customer question",
  vectorSearchResults,
  { top_k: 5 }
);
```

## Architecture

This microservice is designed to complement the Node.js API by providing:

1. Access to Python-specific ML libraries
2. Optimization for compute-intensive tasks
3. Isolation for resource-intensive processes

## Environment Variables

- `OPENAI_API_KEY` - OpenAI API key
- `PINECONE_API_KEY` - Pinecone API key
- `MYSQL_HOST` - MySQL host
- `MYSQL_DATABASE` - MySQL database name
- `MYSQL_USER` - MySQL username
- `MYSQL_PASSWORD` - MySQL password
- `SUPABASE_URL` - Supabase URL
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key

## Adding New Tools

To add a new tool:

1. Create a new module in the `tools/` directory
2. Implement your Python-specific logic
3. Add an endpoint in `main.py`
4. Add a convenience method in `RemotePythonTool.ts` in the Node.js API

## Deployment

In production, this service is deployed as a background worker in Render alongside the main application.
