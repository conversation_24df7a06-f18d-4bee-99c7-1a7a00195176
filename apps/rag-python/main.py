import os
from typing import Dict, List, Any, Optional
from fastapi import Fast<PERSON><PERSON>, HTTPException
from pydantic import BaseModel
import logging

# Import our tools
from tools.rerank import ReRankTool

# Initialize logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize the FastAPI app
app = FastAPI(
    title="RAG Python Microservice",
    description="Python microservice for advanced RAG capabilities",
    version="0.1.0",
)

# Initialize tools
rerank_tool = ReRankTool()


# Models for request and response
class AnalysisRequest(BaseModel):
    query: str
    context: Optional[Dict[str, Any]] = None
    locationId: Optional[int] = None
    parameters: Optional[Dict[str, Any]] = None


class AnalysisResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@app.get("/")
async def root():
    return {"status": "healthy", "service": "rag-python"}


@app.get("/health")
async def health_check():
    return {"status": "healthy"}


@app.post("/api/v1/analyze", response_model=AnalysisResponse)
async def analyze(request: AnalysisRequest):
    try:
        logger.info(f"Received analysis request: {request.query}")

        # This is where you would implement your actual analysis logic
        # For now, we'll just return a stub response

        return AnalysisResponse(
            success=True,
            data={
                "query": request.query,
                "result": "This is a stub response. Implement actual analysis logic here.",
                "context": request.context,
            },
        )
    except Exception as e:
        logger.error(f"Error processing analysis request: {str(e)}")
        return AnalysisResponse(success=False, error=str(e))


@app.post("/api/v1/rerank", response_model=AnalysisResponse)
async def rerank(request: AnalysisRequest):
    try:
        logger.info(f"Received reranking request with query: {request.query}")

        # Extract parameters from the request
        query = request.query
        parameters = request.parameters or {}
        documents = parameters.get("documents", [])
        top_k = parameters.get("top_k", 5)
        score_key = parameters.get("score_key", "score")
        content_key = parameters.get("content_key", "content")

        logger.info(f"Reranking {len(documents)} documents with top_k={top_k}")

        # Use the ReRankTool to perform the reranking
        reranked_docs = rerank_tool.rerank(
            query=query,
            documents=documents,
            score_key=score_key,
            content_key=content_key,
            top_k=top_k,
        )

        return AnalysisResponse(
            success=True,
            data={
                "reranked_documents": reranked_docs,
                "original_count": len(documents),
                "reranked_count": len(reranked_docs),
                "query": query,
            },
        )
    except Exception as e:
        logger.error(f"Error processing reranking request: {str(e)}")
        return AnalysisResponse(success=False, error=str(e))


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000)
