import requests
import json
import argparse
from typing import List, Dict, Any


def test_health():
    """Test the health endpoint"""
    response = requests.get("http://localhost:8000/health")
    print(f"Health check status: {response.status_code}")
    print(json.dumps(response.json(), indent=2))
    return response.status_code == 200


def test_rerank(query: str):
    """Test the reranking endpoint with sample documents"""
    # Sample documents to rerank
    documents = [
        {
            "id": "doc1",
            "content": "Cannabis strains differ in THC and CBD content, affecting potency and effects.",
            "score": 0.75,
        },
        {
            "id": "doc2",
            "content": "Regular inventory management helps prevent product aging and loss.",
            "score": 0.65,
        },
        {
            "id": "doc3",
            "content": "Customer segmentation allows for targeted marketing campaigns.",
            "score": 0.70,
        },
        {
            "id": "doc4",
            "content": "THC content varies widely between different cannabis products and strains.",
            "score": 0.60,
        },
        {
            "id": "doc5",
            "content": "Proper storage extends the shelf life of cannabis products.",
            "score": 0.55,
        },
    ]

    # Send reranking request
    response = requests.post(
        "http://localhost:8000/api/v1/rerank",
        json={
            "query": query,
            "parameters": {
                "documents": documents,
                "top_k": 3,
                "score_key": "score",
                "content_key": "content",
            },
        },
    )

    print(f"Reranking status: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print("\nReranked documents:")
        if result["success"]:
            for i, doc in enumerate(result["data"]["reranked_documents"]):
                print(f"{i+1}. [Score: {doc['score']:.4f}] {doc['content']}")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
    else:
        print(f"Request failed: {response.text}")


def test_analyze(query: str):
    """Test the analysis endpoint"""
    response = requests.post(
        "http://localhost:8000/api/v1/analyze",
        json={"query": query, "context": {"locationId": 123}},
    )

    print(f"Analysis status: {response.status_code}")
    print(json.dumps(response.json(), indent=2))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test the RAG Python microservice")
    parser.add_argument(
        "--query",
        default="Tell me about THC content in cannabis",
        help="Query to use for testing",
    )
    args = parser.parse_args()

    print("=== Testing RAG Python Microservice ===")
    if test_health():
        print("\n=== Testing Reranking ===")
        test_rerank(args.query)

        print("\n=== Testing Analysis ===")
        test_analyze(args.query)
    else:
        print("Health check failed, service may not be running")
