import logging
from typing import List, Dict, Any, Optional
from sentence_transformers import CrossEncoder

logger = logging.getLogger(__name__)


class ReRankTool:
    """
    Tool for re-ranking retrieval results using a cross-encoder model.
    This provides more accurate relevance scoring than the initial vector search.
    """

    def __init__(self, model_name: str = "cross-encoder/ms-marco-MiniLM-L-6-v2"):
        """
        Initialize the re-ranking tool with a cross-encoder model.

        Args:
            model_name: Name of the cross-encoder model to use
        """
        try:
            logger.info(f"Loading cross-encoder model: {model_name}")
            self.model = CrossEncoder(model_name)
            logger.info(f"Successfully loaded model: {model_name}")
        except Exception as e:
            logger.error(f"Error loading cross-encoder model: {str(e)}")
            raise RuntimeError(f"Failed to load cross-encoder model: {str(e)}")

    def rerank(
        self,
        query: str,
        documents: List[Dict[str, Any]],
        score_key: str = "score",
        content_key: str = "content",
        top_k: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """
        Re-rank documents based on their relevance to the query.

        Args:
            query: The search query
            documents: List of document dictionaries
            score_key: Key to store the new score in
            content_key: Key containing the document content to score
            top_k: Number of top results to return, returns all if None

        Returns:
            Re-ranked documents with updated scores
        """
        try:
            if not documents:
                logger.warning("No documents provided for re-ranking")
                return []

            # Prepare document pairs for scoring
            pairs = [(query, doc.get(content_key, "")) for doc in documents]

            # Get cross-encoder scores
            scores = self.model.predict(pairs)

            # Update documents with new scores
            for i, doc in enumerate(documents):
                doc[score_key] = float(scores[i])

            # Sort by score in descending order
            reranked_docs = sorted(documents, key=lambda x: x[score_key], reverse=True)

            # Return top-k if specified
            if top_k is not None and top_k > 0:
                reranked_docs = reranked_docs[:top_k]

            return reranked_docs

        except Exception as e:
            logger.error(f"Error during re-ranking: {str(e)}")
            # Fall back to original documents on error
            return documents
