# Use Node.js as base
FROM node:18

# Set working directory
WORKDIR /usr/src/app/apps/ui

# Copy package files first for better caching
COPY package*.json ./

# Install dependencies
RUN npm install

# Set environment variables for better hot-reload support
ENV CHOKIDAR_USEPOLLING=true
ENV WATCHPACK_POLLING=true
ENV FAST_REFRESH=true
ENV WDS_SOCKET_PORT=3000
ENV HOST=0.0.0.0
ENV DANGEROUSLY_DISABLE_HOST_CHECK=true
ENV PUBLIC_URL=http://localhost:3000
ENV PORT=3000
ENV CI=true
ENV GENERATE_SOURCEMAP=true
ENV BROWSER=none

# Expose port 3000
EXPOSE 3000

# Start the development server
CMD ["npm", "start"] 