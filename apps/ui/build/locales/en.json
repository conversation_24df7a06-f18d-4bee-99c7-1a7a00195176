{"abort_campaign": "Abort Campaign", "aborted": "Aborted", "action": "Action", "add_admin": "Add Admin", "add_admin_description": "Add a new admin to this organization. Admins have full access to all locations and settings, members can only access locations they are a part of.", "add_font": "Add Font", "add_list": "Add List", "add_locale": "Add Locale", "add_team_member": "Add Team Member", "add_template": "Add Template", "add_translation": "Add Translation", "admin": "Admin", "admins": "Admins", "advanced": "Advanced", "api_keys": "API Keys", "api_triggered": "API Triggered", "automatically_triggered": "Automatically Triggered", "archive": "Archive", "back": "Back", "balancer": "Balancer", "balancer_desc": "Randomly split users across paths and rate limit traffic.", "balancer_desc_empty": "Randomly split users between paths", "balancer_desc_values": "Randomly split users between paths. Allow up to {{rate_limit}} users per {{rate_interval}} to go down each path before throttling.", "balancer_edit_desc": "Randomly split users across paths. Configure an optional rate limit to limit the number of users that go down a path over a given time period.", "bcc": "BCC", "blast": "Blast", "body": "Body", "bounced": "Bounced", "campaign_alert_pending": "This campaign has not been sent yet! Once the campaign is live or scheduled this tab will show the progress and results.", "campaign_alert_scheduled": "This campaign is pending delivery. It will begin to roll out at", "campaign_delivery_trigger_description": "Delivery for trigger campaigns is activated via automation actions.", "campaign_form_channel_description": "This campaign is being sent over the {{channel}} channel. Set the subscription group this message will be associated to.", "campaign_form_channel_instruction": "Setup the channel this campaign will go out on. The medium is the type of message, provider the sender that will process the message and subscription group the unsubscribe group associated to the campaign.", "campaign_form_lists": "Select what lists to send this campaign to and what user lists you want to exclude from getting the campaign.", "campaign_form_select_list": "Select one or more lists using the button above.", "campaign_form_type": "Should a campaign be sent as a blast to a list of users or triggered individually via API.", "campaign_list_generating": "This list is still generating. Sending before it has completed could result in this campaign not sending to all users who will enter the list. Are you sure you want to continue?", "campaign_name": "Campaign Name", "campaigns": "Campaigns", "campaign": "Campaign", "cancel": "Cancel", "cc": "CC", "change_schedule": "Change Schedule", "channel": "Channel", "click_rate": "Click Rate", "clicks": "<PERSON>licks", "code": "Code", "create": "Create", "create_campaign": "Create Campaign", "create_journey": "Create Journey", "create_automation": "Create Automation", "create_list": "Create List", "segments": "Segments", "create_segment": "Create Segment", "create_locale": "Create Locale", "create_location": "Create Location", "create_subscription": "Create Subscription", "create_tag": "Create Tag", "create_template": "Create Template", "create_template_description": "Each campaign can have one template per locale. Pick a locale to create a template for it.", "created_at": "Created At", "dark_mode": "Use Dark Theme", "data_key": "Data Key", "data_key_description": "Makes data stored at this step available in user update and campaign templates.", "day": "Day", "day_one": "{{count}} Day", "day_other": "{{count}} Days", "deeplink": "Deeplink", "default_locale": "Default Language", "default_locale_description": "This locale will be used as the default when creating campaigns and when a users locale does not match any available ones.", "defaults": "De<PERSON>ults", "delay": "Delay", "delay_date_desc": "Delay until the specified date in the user's timezone.", "delay_desc": "Add a delay between the previous and next step.", "delay_time_desc": "Delay until the specified time in the user's timezone.", "delay_until": "Delay Until", "delete": "Delete", "delete_admin_confirmation": "Are you sure you want to delete this admin?", "resend_invitation": "Resend Invitation", "sending_invitation": "Sending...", "cannot_resend_invitation_to_yourself": "You cannot resend an invitation to yourself", "failed_to_resend_invitation": "Failed to resend invitation. Please try again.", "delete_user": "Delete User", "delete_user_confirmation": "Are you sure you want to delete this user? All existing data will be removed.\n\nNote: If new data is sent for this user, they will be re-created with whatever data is sent.", "delivery": "Delivery", "delivery_rate": "Delivery Rate", "description": "Description", "design": "Design", "details": "Details", "does_user_match": "Does user match", "draft": "Draft", "duplicate": "Duplicate", "dynamic": "Dynamic", "edit": "Edit", "edit_admin": "Edit Admin", "edit_admin_description": "Update the details of this admin.", "edit_campaign": "Edit Campaign", "edit_design": "Edit Design", "edit_details": "Edit Details", "edit_journey_details": "Edit Journey Details", "edit_journey_steps": "Edit Journey Steps", "edit_list": "Edit List", "edit_template_details": "Edit Template Details", "editor_type": "Editor Type", "email": "Email", "address": "Address", "city": "City", "website": "Website", "facebook": "Facebook", "instagram": "Instagram", "twitter": "Twitter", "youtube": "Youtube", "country": "Country", "zip_code": "Zip Code", "ended_at": "Ended At", "endpoint": "Endpoint", "engagement": "Engagement", "enter_email": "Enter email...", "entrance": "Entrance", "entrance_add_everyone_from": "Add everyone from", "entrance_add_everyone_when": "Add everyone when", "entrance_desc": "How users are added to this journey.", "entrance_empty": "No automated trigger", "entrance_list_desc": "All users from this list will start this journey on the configured schedule.", "entrance_matching": "Matching", "entrance_multiple_entries": "Multiple Entries", "entrance_multiple_entries_desc": "Should people enter this journey multiple times?", "entrance_occurs": " occurs", "entrance_simultaneous_entries": "Simultaneous Entries", "entrance_simultaneous_entries_desc": "If enabled, user could join this journey multiple times before finishing previous ones.", "entrance_trigger": "Trigger Entrance", "entrance_trigger_desc": "This entrance can be triggered directly via API. An example request is available below. Data from the <1>event</1> field will be available for use in the journey and campaign templates under <1>journey.DATA_KEY_OF_THIS_STEP.*</1>(for example, <1>journey.my_entrance.purchaseAmount</1>).", "event_name": "Event Name", "events": "Events", "exclusion_lists": "Exclusion Lists", "existing_team_member": "Existing Team Member", "exit": "Exit", "exit_desc": "Remove users from a selected journey flow.", "exit_step_default": "Exit users from {{name}}", "exit_entrance_label": "Entrance Flow", "exit_entrance_desc": "Select the entrance flow to end for the user.", "experiment": "Experiment", "experiment_default": "Proportionally split users between paths.", "experiment_desc": "Randomly send users down different paths.", "experiment_edit_desc": "Connect this step to others and configure ratios to control what proportion of users will be sent down each path.", "experiment_ratio": "<PERSON><PERSON>", "experiment_ratio_desc": "{{percentage}}% of users will go down this path.", "external_id": "External ID", "failed": "Failed", "file": "File", "finished": "Finished", "first_name": "First Name", "fonts": "Fonts", "for_duration": "For a Duration", "from_email": "From Email", "from_name": "From Name", "gate": "Gate", "gate_desc": "Split a user between paths depending on the result of a condition.", "general": "General", "get_started": "Get Started", "group": "Group", "has_done": "Has done", "headers": "Headers", "hour": "Hour", "hour_one": "{{count}} Hour", "hour_other": "{{count}} Hours", "images": "Images", "import_users": "Import Users", "advanced_settings": "Advanced Settings", "skip_for_now": "Skip for Now", "online_presence": "Online Presence", "contact_info": "Contact Info", "basic_info": "Basic Info", "in_timezone": "In Timezone", "integrations": "Integrations", "invite_to_location": "Invite to Location", "joined_list_at": "Joined List At", "journey": "Journey", "journey_saved": "Journey Saved!", "journeys": "Journeys", "key": "Key", "label": "Label", "language": "Language", "default_language": "Default Language", "last_name": "Last Name", "launch_campaign": "Launch Campaign", "launched_at": "Launched At", "light_mode": "Use Light Theme", "link": "Link", "link_desc": "Send users to another journey.", "link_empty": "Send users to", "link_wrapping_email": "Email Link Wrapping", "link_wrapping_email_subtitle": "Enable link wrapping for all links in emails.", "link_wrapping_push": "Push Notification Link Wrapping", "link_wrapping_push_subtitle": "Enable link wrapping for all links in push notifications.", "list_generation_dialog_description": "List generation will happen in the background. Please reload the page to see the latest status.", "list_name": "List Name", "list_save": "Save List", "list": "List", "lists": "Lists", "load_user": "Load User", "loading": "Loading", "locale": "Locale", "locale_delete_confirmation": "Are you sure you want to delete this locale? No existing templates will be deleted, this language option will just not be present for future templates.", "locale_field_subtitle": "The abbreviated language code i.e. en, es, fr", "locales": "Locales", "create_language": "Create Language", "languages": "Languages", "login_basic_instructions": "Enter your email and password to continue.", "login_email_available_methods": "What is your email address?", "login_email_confirmation": "An email has been sent to the address you indicated with a link to continue.", "login_email_instructions": "Next, please enter your email to receive an authentication link.", "login_method_not_available": "This login method is not available for this email address or an account with this email address does not exist.", "login_select_method": "Select an authentication method below to continue.", "medium": "Medium", "member_added": "Member Added", "member_added_description": "You have successfully added a member to this location. They will have access to this location as soon as they log in for the first time. Would you like to send them an email about their new access?", "message": "Message", "message_settings": "Message Settings", "method": "Method", "minute": "Minute", "minute_one": "{{count}} Minute", "minute_other": "{{count}} Minutes", "missing": "Missing", "name": "Name", "automations": "Automations", "new_team_member": "New Team Member", "no_providers": "No Providers", "no_template_alert_body": "There are no templates yet for this campaign. Add a locale above or use the button below to get started.", "onboarding_installation_success": "Are you new here? Lets get you setup and ready to grow your business!", "onboarding_location_setup_description": "<PERSON><PERSON> needs to know more about your location before we can get started. Let's set that up now.", "onboarding_location_setup_title": "Location Setup", "open_rate": "Open Rate", "opened_at": "Opened At", "options": "Options", "pending": "Pending", "period": "Period", "phone": "Phone", "plain_text": "Plain Text", "preheader": "Preheader", "preview": "Preview", "location": "Location", "location_settings_saved": "Location settings saved!", "provider": "Provider", "publish": "Publish", "published": "Published", "push": "<PERSON><PERSON>", "rate_interval": "Rate Interval", "rate_limit": "Rate Limit", "raw_json": "Raw JSON", "ready": "Ready", "rebuild": "Rebuild", "rebuild_path_suggestions": "Rebuild Path Suggestions", "rebuild_path_suggestions_desc": "Would you like to recreate all of the event and user data autocomplete paths used for creating rules?", "rebuild_path_suggestions_success": "Path suggestions are rebuilding. This might take a few minutes depending on how many users and events you have.", "remove": "Remove", "remove_locale_warning": "Are you sure you want to delete this locale? The template cannot be recovered.", "reply_to": "Reply To", "restart": "<PERSON><PERSON>", "restart_campaign": "Restart Campaign", "role": "Role", "role_cant_change": "You cannot change your own roles.", "rule_add_condition": "Add Condition", "rule_add_event_condition": "Add Event Condition", "rule_add_user_condition": "Add User Condition", "rule_did": "Did", "rule_include_users_matching": "Include users matching ", "rule_matching": " matching ", "rule_of_the_following": "of the following", "rules": "Rules", "rules_save": "Save Rules", "running": "Running", "save": "Save", "save_settings": "Save Settings", "schedule": "Schedule", "scheduled": "Scheduled", "search": "Search", "search_users": "Search by ID, email or phone", "second": "Second", "send": "Send", "send_at": "Send At", "send_campaign_desc": "Send this campaign when users reach this step.", "send_desc": "Trigger a send (email, sms, push notification, webhook) to a user.", "send_lists": "Send Lists", "send_proof": "Send Proof", "sent": "<PERSON><PERSON>", "settings": "Settings", "setup": "Setup", "setup_integration": "Setup Integration", "setup_integration_description": "Campaigns depend on message integrations to be able to send. Configure an integration to start sending messages!", "setup_integration_no_providers": "There are no providers configured for this channel. Please add a provider to continue.", "sign_out": "Sign Out", "sms_opt_out_message": "SMS Opt Out Message", "sms_opt_out_message_subtitle": "Instructions on how to opt out of SMS that will be appended to every text.", "sms_help_message": "SMS Help Message", "sms_help_message_subtitle": "Instructions on how to receive help that are auto sent to users if they reply with HELP.", "start_journey": "Start journey: ", "state": "State", "sales_data": "Sales Data", "static": "Static", "status": "Status", "step_date": "Step Date", "subject": "Subject", "submit": "Submit", "subscribed": "Subscribed", "subscription_group": "Subscription Group", "subscriptions": "Subscriptions", "tags": "Tags", "tags_description": "Use tags to organize and report on your campaigns, journeys, lists, and users.", "target_journey": "Target Journey", "target_journey_desc": "Send users to this journey when they reach this step.", "team": "Team", "team_edit_description_add": "Add an existing team member to this location or invite someone new.", "team_edit_description_update": "Update the permissions for this team member.", "template_save": "Save Template", "template_saved": "Template saved!", "test_webhook": "Test Webhook", "text": "Text", "throttled": "Throttled", "time": "Time", "timezone": "Timezone", "title": "Title", "topic": "Topic", "translations": "Translations", "translations_description": "Manage the translations your campaign supports and will send to.", "trigger": "<PERSON><PERSON>", "trigger_event": "Trigger Event", "trigger_event_desc": "Trigger an analytic event for the user.", "trigger_event_desc1": "Write a Handlebars template that renders JSON that will be sent as fields on the event.", "trigger_event_desc2": " The user's current profile data is available in the ", "trigger_event_desc3": " variable, and data collected at other steps are available in ", "trigger_event_empty": "(click to see event details)", "type": "Type", "social_connection_success": "Successfully connected {{platform}}", "social_connection_failed": "Failed to connect {{platform}}", "connecting": "Connecting...", "connected": "Connected", "connect_platform": "Connect {{platform}}", "enter_profile_url": "Enter profile URL", "facebook_profile": "Facebook Profile", "twitter_profile": "Twitter Profile", "instagram_profile": "Instagram Profile", "linkedin_profile": "LinkedIn Profile", "unsubscribe_all": "Unsubscribe From All", "until_date": "Until a Date", "until_time": "Until a Time", "update_permissions": "Update Permissions", "update_subscription": "Update Subscription", "update_tag": "Update Tag", "updated_at": "Updated At", "upload": "Upload", "upload_user_instructions": "Please select a CSV of users to upload. Download the example file below to see the required format.", "upload_product_instructions": "Please select a CSV of products to upload. Download the example file below to see the required format.", "upload_pos_data_instructions": "Please select a CSV of POS data to upload. Download the example file below to see the required format.", "upload_list": "Upload List", "usage": "Usage", "user_lookup": "User Lookup", "user_update": "User Update", "user_update_desc": "Make updates to a user's profile.", "user_update_edit_desc1": "Write a Handlebars template that renders JSON that will be shallow merged into the user's profile data.", "user_update_edit_desc2": " The user's current profile data is available in the ", "user_update_edit_desc3": "variable, and data collected at other steps are available in ", "user_update_empty": "(click to see updated fields)", "users": "Users", "users_change_subscription_status": "Are you sure you want to change the status of this subscription?", "users_count": "Users Count", "users_unsubscribe_all": "Are you sure you want to unsubscribe from all?", "view_all": "View All", "visual": "Visual", "wait": "Wait", "wait_until": "Wait until", "webhook": "Webhook", "welcome": "Welcome!", "upload_pos_data": "Upload POS Data", "no_pos_data_found": "No POS data found.", "please_select_a_file": "Please select a file.", "uploading": "Uploading...", "pos_data_uploaded_successfully": "POS data uploaded successfully.", "failed_to_upload_pos_data": "Failed to upload POS data.", "chat": "Cha<PERSON>", "messages": "Messages", "thinking": "Thinking...", "type_message": "Type a message...", "type_message_or_mention": "Type a message or use @ to mention an agent...", "sending": "Sending...", "customer_segments": "Customer Segments", "customers": "Customers", "ai_insights": "AI Insights", "location_name": "Location Name", "master_category": "Master Category", "order_date": "Order Date", "order_number": "Order Number", "order_total": "Order Total", "product_name": "Product Name", "quantity": "Quantity", "unit_price": "Unit Price", "customer_type": "Customer Type", "budtender_name": "Budtender Name", "close": "Close", "gross_sales": "Gross Sales", "dashboard": "Dashboard", "products": "Products", "best_selling_products": "Best Selling Products", "revenue_over_time": "Revenue Over Time", "sales_over_time": "Sales Over Time", "active_campaigns": "Active Campaigns", "no_campaigns": "No Campaigns", "no_campaigns_description": "Create a campaign to get started.", "dashboard_description": "View key performance indicators and metrics for your business.", "average_order": "Average Order", "acquisition_cost": "Acquisition Cost", "revenue": "Revenue", "view_actions": "View Actions", "new_chat": "New Chat", "recent_chats": "Recent Chats", "welcome_to_smokey_ai": "Welcome to Smokey AI", "setup_automation": "Setup Automation", "recommended_actions": "Recommended Actions", "lets_go": "Let's Go!", "login": "<PERSON><PERSON>", "register": "Register", "password": "Password", "have_account_login": "Already have an account? <PERSON>gin", "no_account_register": "Don't have an account? Register", "login_with_google": "Login with Google", "login_error": "An error occurred during login.", "login_google_error": "An error occurred during Google login.", "registration_successful": "Registration successful!", "registration": "Registration", "or": "Or", "forgot_password_link": "Forgot Password?", "confirm_password": "Confirm Password", "send_reset_link": "Send Reset Link", "back_to_login": "Back to Login", "forgot_password": "Forgot Password", "create_with_ai": "Create with AI", "ai_suggestions": "AI Suggestions", "segment_name": "Segment Name", "actions": "Actions", "ai_generation_error": "An error occurred while generating suggestions.", "list_creation_error": "An error occurred while creating the list.", "use_segment": "Use Segment", "login_successful": "Login successful!", "social_media": "Social Media", "advanced_options": "Advanced Options", "smokeys_insights": "<PERSON><PERSON>'s Insights", "no_sales_data": "No sales data available", "upload_sales_data_description": "Upload your POS data to see sales analytics and insights", "upload_sales_data": "Upload Sales Data", "no_insights_available": "No insights available", "generate_insights_description": "Use AI to analyze your data and generate actionable insights", "analyze_with_ai": "Analyze with AI", "generating_insights": "Generating insights...", "no_data_available": "No data available", "no_data_available_description": "No data available for this location. Please upload your POS data to see sales analytics and insights.", "last_7_days": "Last 7 Days", "last_30_days": "Last 30 Days", "last_90_days": "Last 90 Days", "last_year": "Last Year", "customer_value": "Customer Value", "no_pos_data": "No POS Data Available", "no_pos_data_description": "Upload your POS data to see insights and analytics", "no_data": "No data", "no_revenue_data_description": "No revenue data available for the selected timeframe", "no_sales_data_description": "No sales data available for the selected timeframe", "no_product_data": "No Product Data", "no_product_data_description": "No product data available for the selected timeframe", "no_insights": "No Insights Available", "no_insights_description": "Generate insights to see recommendations", "change_timeframe": "Change Timeframe", "generate_insights": "Generate Insights", "sales": "sales", "no_revenue_data": "No Revenue Data", "no_data_timeframe": "No data available for {{timeframe}}", "no_users": "No Users Found", "no_users_description": "Start by importing your customer data to see insights and create targeted campaigns.", "sales_by_time": "Sales by Time of Day", "morning": "Morning (9AM-12PM)", "afternoon": "Afternoon (1PM-5PM)", "evening": "Evening (6PM-11PM)", "category_performance": "Category Performance", "uncategorized": "Uncategorized", "customer_metrics": "Customer Metrics", "new_customers": "New Customers", "returning_customers": "Returning Customers", "loyalty_usage": "Loyalty Program Usage", "no_time_data": "No time distribution data available", "no_category_data": "No category data available", "no_customer_data": "No customer data available", "emails_sent": "Emails Sent", "sms_sent": "SMS Sent", "items_sold": "Items Sold", "email_failed": "Email Failed", "email_delivered": "Email Delivered", "email_opened": "Email Opened", "email_clicked": "<PERSON><PERSON>licked", "email_unsubscribed": "<PERSON><PERSON>subscribed", "email_complained": "<PERSON><PERSON>lained", "campaign_performance": "Campaign Performance", "average_open_rate": "Avg. Open Rate", "average_click_rate": "Avg. <PERSON>lick Rate", "average_delivery_rate": "Avg. Delivery Rate", "total_campaigns": "Total Campaigns", "localization": "Localization", "step_list": "Create new segment: {{name}}", "step_campaign": "Create new campaign: {{name}}", "step_journey": "Create new automation: {{name}}", "step_tag": "Create new tag: {{name}}", "step_template": "Create new template: {{name}}", "plan_details": "Plan Details", "execute_plan": "Execute Plan", "automation_created_successfully": "Automation has been created successfully!", "executing_steps": "Executing steps...", "view_details": "View Details", "creating_step": "Creating {{step}}...", "all_time": "All Time", "age_distribution": "Age Distribution", "gender_distribution": "Gender Distribution", "customer_value_distribution": "Customer Value Distribution", "customer_segment_distribution": "Customer Segment Distribution", "total_sales": "Total Sales", "average_sale": "Average Sale", "profit_margin": "<PERSON><PERSON>", "profitability_analysis": "Profitability Analysis", "total_profit": "Total Profit", "total_discounts": "Total Discounts", "loyalty_discounts": "Loyalty Discounts", "total_revenue": "Total Revenue", "total_cost": "Total Cost", "total_inventory": "Total Inventory", "total_customers": "Total Customers", "budtender": "<PERSON><PERSON><PERSON>", "customer_type_analysis": "Customer Type Analysis", "under_30": "Under 30", "30_to_40": "30 to 40", "30_to_45": "40 to 45", "46_to_60": "46 to 60", "over_60": "Over 60", "orders": "Orders", "average_order_value": "Average Order Value", "average_sale_value": "Average Sale Value", "average_customer_value": "Average Customer Value", "customer_retention_rate": "Customer Retention Rate", "customer_acquisition_cost": "Customer Acquisition Cost", "total_customers_all_time": "Total Customers All Time", "new_customers_timeframe": "New Customers ({{timeframe}})", "customers_purchased_timeframe": "Customers Purchased ({{timeframe}})", "returning_customers_timeframe": "Returning Customers ({{timeframe}})", "loyal_customers_timeframe": "Loyal Customers ({{timeframe}})", "loyal_customers_percentage": "Loyal Customers Percentage", "loyal_customers_percentage_description": "Percentage of customers who have made purchases in the last 30 days", "loyal_customers_percentage_timeframe": "Loyal Customers Percentage ({{timeframe}})", "loyal_customers_percentage_timeframe_description": "Percentage of customers who have made purchases in the last {{timeframe}} days", "budtender_performance": "Budtender Performance", "generate_plan": "Generate Plan", "has_plan": "Has Plan", "plan_generated": "Plan Generated", "dismiss": "<PERSON><PERSON><PERSON>", "view_plan": "View Plan", "mark_completed": "Mark Completed", "generate_automation_plan": "Generate Plan", "generate_automation_plan_description": "See what smokey has in mind for you", "rename_chat": "<PERSON><PERSON>", "enter_chat_name": "Enter Chat Name", "regenerate_plan": "Regenerate Plan", "download_example": "Download Example", "upload_products": "Upload Products", "passwords_do_not_match": "Passwords do not match", "suggested_automation": "Suggested Automation", "registration_error": "An error occurred during registration.", "password_reset_email_sent": "Password reset email sent successfully.", "password_reset_error": "An error occurred while sending password reset email.", "auth_error_email_already_in_use": "This email address is already in use. Please use a different email or try logging in instead.", "auth_error_weak_password": "Password is too weak. Please choose a stronger password.", "auth_error_invalid_email": "The email address is not valid. Please check your email and try again.", "auth_error_user_disabled": "This account has been disabled. Please contact support for assistance.", "auth_error_user_not_found": "No account found with this email address. Please check your email or create a new account.", "auth_error_wrong_password": "Incorrect password. Please try again or reset your password.", "auth_error_too_many_requests": "Too many failed attempts. Please wait a moment before trying again.", "auth_error_operation_not_allowed": "This operation is not allowed. Please contact support.", "auth_error_invalid_credential": "Invalid credentials provided. Please try again.", "auth_error_requires_recent_login": "This operation requires recent authentication. Please log in again.", "auth_error_popup_closed": "Sign-in popup was closed before completing authentication.", "auth_error_popup_blocked": "Sign-in popup was blocked by your browser. Please allow popups and try again.", "auth_error_popup_cancelled": "Sign-in was cancelled. Please try again.", "search_business": "Search for your business", "search_business_placeholder": "Enter your business name", "loading_places": "Loading Google Places...", "searching": "Searching...", "show_more_results": "Show More Results", "no_businesses_found": "No businesses found", "enter_manually": "Enter Manually", "no_competitors_found": "No competitors found in your area. You can add them manually later.", "refresh": "Refresh", "onboarding": {"create_location": "Create Location", "welcome": "Welcome to Your Business Setup", "subtitle": "Let's get your business profile set up to make the most of our platform", "progress": {"title": "Setup Progress", "steps_completed": "{{completed}} of {{total}} steps completed"}, "navigation": {"back": "Back to Previous Step", "save_and_continue": "Save & Continue", "location_required": "Please complete location setup first", "continue": "Continue"}, "manual_entry": {"title": "Enter Business Details Manually"}, "documents": {"title": "Upload Business Documents", "description": "Upload your business documents to help us understand your business better"}, "skip_dialog": {"title": "Skip This Step?", "description": "You can always complete this step later from your settings. Are you sure you want to skip?", "continue_button": "Continue Setup", "skip_button": "Skip for Now"}, "steps": {"completed": "Step Completed", "continue": "Continue", "next_step": "Next Step", "skip_for_now": "Skip for Now", "finish_setup": "Finish Setup", "company_info": {"title": "Company Information", "edit": "Edit Company Information", "manual_entry": "Enter Business Details Manually", "lookup_intro": "Search for your business to quickly import information", "business_name_placeholder": "Enter business name", "description_placeholder": "Describe your business", "business_selected": "Business Selected", "change_selection": "Change", "google_api_down": "Google Places API is currently unavailable. Please try again later or enter your business details manually.", "edit_business_details": "Edit Business Details", "enter_business_details": "Enter Business Details", "new_search": "New Search", "business_details": "Business Details", "business_name": "Business Name", "website": "Website", "phone": "Phone", "contact_info": "Contact Information", "contact_email": "Contact Email", "location": "Location", "address": "Address", "validate": "Validate", "city": "City", "state": "State", "zip": "Zip", "country": "Country", "coordinates": "Coordinates", "required_field": "This field is required", "manual_form": {"business_name": "Business Name", "business_name_required": "Business name is required", "description": "Description", "description_placeholder": "Tell us about your business", "language": "Language", "timezone": "Timezone", "location": "Location", "latitude": "Latitude", "longitude": "Longitude", "back_to_search": "Back to Search", "save_continue": "Save & Continue", "required_field": "This field is required"}, "enter_manually": "Enter manually"}, "data_sources": {"title": "Data Sources", "description": "Connect your data sources to unlock powerful AI features", "connected_message": "Data sources connected successfully.", "coming_soon_title": "Coming Soon", "uploaded_data": "Uploaded Data:", "pos_connected": "Connected POS System", "pos_integration": {"title": "Point of Sale Integration", "description": "Connect to your POS system to sync sales and customer data"}, "manual_upload": {"title": "Manual Data Upload", "description": "Upload your customer data files directly", "drag_drop": "Drag and drop files here or click to browse", "file_types": "CSV, Excel, or JSON files", "upload_button": "Upload Files"}, "pos_section": {"title": "Point of Sale System", "select_placeholder": "Select your POS system", "coming_soon_title": "Coming Soon", "coming_soon_message": "POS system integration will be available soon. Check back later!", "back_button": "Back"}, "customer_data": {"title": "Customer Data", "description": "Upload your customer data to enable personalized marketing", "upload_button": "Upload Customer Data"}, "continue_button": "Continue", "skip_button": "Skip for Now"}, "competitors": {"title": "Competitors", "description": "Add your competitors to track market positioning", "added_competitors": "Added Competitors ({{count}})", "no_competitors": "No competitors added.", "search_label": "Search for competitors", "search_button": "Search Nearby Competitors", "search_tip": "Search for competitors by name or address", "search_for_competitors_instruction": "Search for competitors by name or location", "competitors_found": "competitors found", "add_competitors": "Add Competitors", "search_nearby": "Find Competitors Nearby", "search_nearby_instructions": "Click the button to find competitors near your location", "selected": "selected", "loading_competitors": "Loading competitors...", "no_competitors_yet": "No competitors yet", "add_competitors_hint": "Add competitors to track market positioning", "confirm_remove_competitor": "Are you sure you want to remove this competitor?", "failed_to_load_competitors": "Failed to load competitors", "failed_to_add_competitors": "Failed to add competitors", "failed_to_remove_competitor": "Failed to remove competitor", "adding": "Adding...", "add": "Add", "already_added": "Already Added", "remove_competitor": "Remove Competitor", "added_list": "Added Competitors", "auto_populate": "Find Nearby Competitors", "add_manually": "Add Manually", "name_and_address_required": "Name and address are required", "manual_entry_help": "Enter competitor details manually", "load_more": "Load More Competitors", "add_competitors_for": "Adding competitors for your business", "search_placeholder": "Search for competitors by name or location", "auto_populate_tip": "Automatically find dispensaries near your location"}, "documents": {"title": "Business Documents", "description": "Upload your business documents to help us understand your business better. These could include licenses, permits, or other relevant documentation.", "upload_instructions": "Drag and drop your files here, or click to select files", "supported_formats": "PDF, Word, and Excel files up to 10MB", "select_files": "Select Files", "uploading": "Uploading...", "continue": "Continue", "completed_message": "You've completed this step. You can always come back later to upload documents.", "uploaded_documents": "Uploaded Documents:", "uploaded_documents_count": "Uploaded Documents ({{count}})", "and_more": "and {{count}} more..."}, "social": {"title": "Social Media", "description": "Connect your social media accounts to enhance your marketing reach", "connected_accounts": "Connected {{count}} social accounts", "no_connected_accounts": "No social accounts connected. This step is optional.", "connected_accounts_label": "Connected Accounts ({{count}})", "connect": "Connect", "disconnect": "Disconnect", "connect_twitter": "Connect Twitter", "connect_facebook": "Connect Facebook", "connect_instagram": "Connect Instagram", "continue": "Continue Setup", "coming_soon_title": "Coming Soon", "continue_button": "Continue"}, "communication": {"title": "Communication Settings", "description": "Set up how you'd like to communicate with your customers", "completed_message": "Communication settings completed.", "email_settings": "<PERSON><PERSON>s", "sms_settings": "SMS Settings", "notification_frequency": "Notification Frequency", "marketing_preferences": "Marketing Preferences", "save_button": "Save Preferences", "skip_button": "Skip for Now", "sender_section": {"title": "Sender Information"}, "sender_name": "Sender Name", "sender_email": "Sender <PERSON><PERSON>", "sender_email_help": "This email will be used as the 'from' address for your marketing emails", "contact_section": {"title": "Contact Information"}, "contact_email": "Contact Email", "contact_email_help": "This email will be used for customer replies and inquiries", "contact_phone": "Contact Phone Number"}}, "pricing": {"title": "Pricing"}}, "helpful_links": {"chat_help": "Chat Help", "chat_help_description": "Learn how to use the chat feature to interact with your customers"}, "helpful": "Helpful", "not_helpful": "Not Helpful", "summary": "Summary", "insights": "Insights", "files": "Files", "no_files_yet": "No files uploaded yet", "delete_chat": "Delete Chat", "select_chat": "Select a Chat", "chats": "Chats", "defaults_to_subscription_name": "Defaults to subscription name: {{name}}", "defaults_to_subscription_email": "Defaults to subscription email: {{email}}", "defaults_to_subscription_reply_to": "Defaults to subscription reply-to: {{email}}", "defaults_to_subscription_cc": "Defaults to subscription CC: {{email}}", "defaults_to_subscription_bcc": "Defaults to subscription BCC: {{email}}", "defaults_to_subscription_phone": "Defaults to subscription phone: {{phone}}", "defaults_to_location_name": "Defaults to location name", "defaults_to_location_phone": "Defaults to location phone number", "documents": {"title": "Documents", "upload": "Upload Document", "drag_drop": "Drag and drop files here or click to upload", "no_documents": "No documents uploaded yet", "upload_success": "Document uploaded successfully", "upload_error": "Failed to upload document", "delete": "Delete Document", "delete_confirmation": "Are you sure you want to delete this document?", "delete_success": "Document deleted successfully", "delete_error": "Failed to delete document", "download": "Download", "view": "View", "analyze": "Analyze", "status": {"pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed"}, "analysis": {"title": "Document Analysis", "insights": "Key Insights", "relevance": "Relevance", "completion": "Analysis Completion", "no_analysis": "No analysis available for this document", "generating": "Generating analysis...", "request_analysis": "Request Analysis"}, "types": {"pdf": "PDF Document", "docx": "Word Document", "xlsx": "Excel Spreadsheet", "csv": "CSV File", "jpg": "Image", "png": "Image", "txt": "Text File", "other": "Document"}, "size": {"bytes": "{{size}} B", "kb": "{{size}} KB", "mb": "{{size}} MB", "gb": "{{size}} GB"}, "filter": {"all": "All Documents", "recent": "Recently Uploaded", "analyzed": "Analyzed Documents", "not_analyzed": "Not Analyzed"}, "sort": {"name_asc": "Name (A-Z)", "name_desc": "Name (Z-A)", "date_asc": "Date (Oldest First)", "date_desc": "Date (Newest First)", "size_asc": "<PERSON>ze (Smallest First)", "size_desc": "<PERSON><PERSON> (Largest First)"}}, "data_hub": "Data Hub", "business_name": "Business Name", "edit_information": "Edit Information", "chat_history": "Chat History", "chat_history_placeholder": "Search chat history", "chat_history_empty": "No chat history found", "send_message_to_start": "Send a message to start a new chat", "start_chat_to_see_insights": "Start a chat to see insights", "insights_explanation": "After you send a message, insights like summaries and key points will appear here", "discover_new_insights": "Discover New Insights", "no_insights_yet": "No Insights Yet", "insights_description": "Generate AI-powered insights to uncover opportunities to grow your business and improve customer engagement.", "analyzing_your_data": "Analyzing your data...", "no_data_found": "No Data Found", "empty_table_message": "There are no items to display. Try adjusting your filters or search criteria.", "brand_name": "Brand Name", "category": "Category", "price": "Price", "medical": "Medical", "recreational": "Recreational", "menu": "<PERSON><PERSON>", "campaign_form": {"delivery_method": "Delivery Method", "sms": "SMS", "email": "Email", "campaign_type": "Campaign Type", "blast": "Blast", "trigger": "<PERSON><PERSON>", "medium": "Medium", "type": "Type", "campaign_details": "Campaign Details", "audience_targeting": "Audience Targeting", "include_audiences": "Include Audiences", "exclude_audiences": "Exclude Audiences", "campaign_form_lists": "Lists", "campaign_form_select_list": "Select a list to include in the campaign", "campaign_form_type": "Type", "campaign_form_type_description": "Select the type of campaign you want to create", "campaign_form_medium": "Medium", "campaign_form_medium_description": "Select the medium of the campaign you want to create"}, "campaign_type": "Campaign Type", "campaign_details": "Campaign Details", "delivery_method": "Delivery Method", "sms": "SMS", "audience_targeting": "Audience Targeting", "include_audiences": "Include Audiences", "exclude_audiences": "Exclude Audiences", "upcoming_campaigns": "Upcoming Campaigns", "new_campaign": "New Campaign", "active_customers": "Active Customers", "shared_files": "Shared Files", "documents_and_files": "Documents", "today": "Today", "start_date": "Start Date", "end_date": "End Date", "opens": "Opens", "view_automation": "View Automation", "active": "Active", "automation": "Automation", "view_campaign": "View Campaign", "hide_sidebar": "<PERSON><PERSON>bar", "all_status": "All Status", "pause": "Pause", "error": "Error", "drafts": "Drafts", "completed": "Completed", "add_new_campaign": "Add New Campaign", "showing": "Showing", "results": "Results", "previous": "Previous", "next": "Next", "no_campaigns_found": "No campaigns found", "no_automations_found": "No automations found", "ask_anything": "Ask Anything", "updated": "Updated", "schedule_call": "Schedule Call", "schedule_onboarding_call": "Schedule Onboarding", "created": "Created", "emails": "Emails", "show_chat": "Show Chat", "hide_chat": "<PERSON><PERSON>", "open_menu": "Open Menu", "close_sidebar": "Close Sidebar", "no_chats_yet": "No chats yet", "start_new_conversation": "Start a new conversation", "show_menu": "Show Menu", "insights_notifications": "Insights Notifications", "welcome_to_chat": "Welcome to Chat", "like": "Like", "dislike": "Dislike", "feedback": "<PERSON><PERSON><PERSON>", "collapse_sidebar": "Collapse Sidebar", "file_manager": "File Manager", "upload_file": "Upload File", "file_name": "File Name", "size": "Size", "uploaded_on": "Uploaded On", "download": "Download", "upload_file_instructions": "Select a file to upload to your library. Supported formats: JSON, CSV, XLS, PDF, MD, DOC, HTML.", "drag_drop_or_click": "Drag and drop or click to select a file", "supported_formats": "Supported formats: JSON, CSV, XLS, PDF, MD, DOC, HTML", "confirm_delete": "Confirm Delete", "delete_file_confirmation": "Are you sure you want to delete this file? This action cannot be undone.", "loading_files": "Loading files...", "sender_email": "Sender <PERSON><PERSON>", "sender_email_subtitle": "This email will be used for communications sent from your business", "expand_sidebar": "Expand Sidebar", "selected_file": "Selected File", "file_library": "File Library", "view_all_insights": "View All Insights", "select_campaign_to_view_details": "Select a campaign to view details", "create_new_chat": "Create New Chat", "onboarding_processing": "Processing Your Onboarding Data", "onboarding_processing_description": "We're currently processing the data you uploaded during onboarding.", "onboarding_files_uploading": "Your files are being processed in the background.", "onboarding_files_background": "You can continue using the app while we process your data. This may take a few minutes depending on the file size.", "onboarding_documents_processing": "Documents processing", "onboarding_pos_data_processing": "POS data files processing", "demo_only": "Demo Only", "disable": "Disable", "disabled": "Disabled", "discount": "Discount", "onboarding_processing_jobs": "Jobs processing", "onboarding_completed_jobs": "Jobs completed", "onboarding_failed_jobs": "Jobs failed", "complete": "complete", "create_first_campaign": "Create your first campaign", "system_health": "System Health", "status_warning": "Warning", "latency": "Latency", "index": "Index", "exists": "Exists", "vector_data_health": "Vector Data Health", "last_checked": "Last Checked", "status_healthy": "Healthy", "public_key_description": "Public keys have read-only access to public endpoints. Use these for displaying data in applications.", "copy_to_clipboard": "Copy to Clipboard", "document_analysis": "Document Analysis", "content_summary": "Content Summary", "agent_contributions": "Agent Contributions", "view_analysis": "View Analysis", "api_keys_description": "API keys allow external applications to access data from this location. Public keys have read-only access, while secret keys have write access based on their assigned role.", "upgrade": "Upgrade", "unlock_premium_features": "Unlock premium features to access advanced marketing tools", "upgrade_required": "Premium Feature - Upgrade Required", "total_items": "Total Items: {{count}}", "add_product": "Add Product", "import_products": "Import Products", "sync_products": "Sync Products", "syncing_products": "Syncing products...", "sync_products_description": "This will sync all products for this location from the marketplace using retailer ID:", "import": "Import", "sync_from_marketplace": "Sync from Marketplace", "upload_csv": "Upload CSV", "add_new_product": "Add New Product", "product_description": "Product Description", "enter_product_description": "Enter a detailed description of the product", "product_image": "Product Image", "select_image": "Select Image", "no_image_selected": "No image selected", "or_enter_image_url": "Or enter image URL", "subcategory": "Subcategory", "thc_percentage": "THC %", "cbd_percentage": "CBD %", "meta_sku": "Meta SKU", "retailer_id": "Retailer ID", "create_product": "Create Product", "not_set": "Not set", "no_retailer_id_description": "No retailer ID is associated with this location. Please set it in location settings first.", "additional_details": "Additional Details", "product_details": "Product Details", "brand": "Brand", "weight": "Weight", "THC": "THC", "CBD": "CBD", "mood": "<PERSON><PERSON>", "effects": "Effects", "sku": "SKU", "menu_provider": "<PERSON><PERSON> Provider", "quantity_per_package": "Quantity Per Package", "raw_product_name": "Raw Product Name", "raw_product_category": "Raw Product Category", "raw_subcategory": "Raw Subcategory", "raw_weight_string": "Raw Weight String", "url": "URL", "api_response": "API Response", "edit_product": "Edit Product", "THC_percentage": "THC Percentage", "CBD_percentage": "CBD Percentage", "delete_product": "Delete Product", "delete_product_confirmation": "Are you sure you want to delete this product?", "upgrade_now": "Upgrade Now", "remind_me_later": "Remind Me Later", "data_processing": "Data Processing", "competitors": {"title": "Competitors", "add_competitor": "Add Competitor", "loading_competitors": "Loading competitors...", "search_for_competitors_instruction": "Search for competitors by name or location", "remove_competitor": "Remove Competitor", "search_nearby": "Find Competitors Nearby", "search_nearby_instructions": "Click the button to find competitors near your location", "competitors_found": "competitors found", "already_added": "Already Added", "load_more": "Load More", "add_manually": "Add Manually", "business_name": "Business Name", "manual_entry_help": "Enter competitor details manually", "confirm_remove_competitor": "Are you sure you want to remove this competitor?", "no_results_found": "No results found", "loading_more": "Loading more results...", "no_competitors_yet": "No competitors yet", "add_competitors_hint": "Add competitors to track market positioning"}, "zip": "Zip", "rating": "Rating", "starting_sync_process": "Starting sync process...", "please_wait": "Please wait...", "business_added_successfully": "Business added successfully!", "search_orders": "Search Orders", "all_statuses": "All Statuses", "processing": "Processing", "cancelled": "Cancelled", "order_id": "Order ID", "customer": "Customer", "total": "Total", "no_orders_found": "No Orders Found", "current": "Current", "warning": "Warning", "confirm_status_change": "Confirm Status Change", "confirm_change": "Confirm Change", "status_change_warning": "Are you sure you want to change the status of order", "to": "to", "irreversible_action_warning": "This action cannot be undone. Once an order is marked as completed or cancelled, the status cannot be changed.", "status_change_irreversible_note": "This status change cannot be undone once confirmed."}