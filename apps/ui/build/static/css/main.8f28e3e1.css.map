{"version": 3, "file": "static/css/main.8f28e3e1.css", "mappings": "AAuCA,gBAQA,CC/CA,UAII,iCAAkC,CAFlC,kCAAmC,CAGnC,0BAA2B,CAF3B,iBAAkB,CAFlB,YAKJ,CAEA,aACI,QACJ,CAEA,YACI,YACJ,CAEA,yBACI,eACJ,CAEA,eACI,iCAAkC,CAClC,4BACJ,CAEA,kBACI,kCAAmC,CACnC,6BACJ,CAEA,gBACI,gCAAiC,CACjC,2BACJ,CAEA,eACI,mCAAoC,CACpC,8BACJ,CCtCA,WAgBI,kBAAmB,CAbnB,gBAAuB,CADvB,sBAA6B,CAE7B,kCAAmC,CAMnC,8BAA8C,CAE9C,cAAe,CAXf,mBAAoB,CAUpB,aAAc,CAHd,cAAe,CADf,eAAgB,CAUhB,OAAQ,CARR,gBAAiB,CAHjB,QAAS,CADT,iBAAkB,CAUlB,iBAAkB,CADlB,oBAAqB,CADrB,cAKJ,CAEA,4BAEI,WAAY,CADZ,UAEJ,CAEA,wBAII,oBAAqB,CACrB,aAAc,CAHd,WAAY,CADZ,gBAAiB,CAEjB,aAGJ,CAEA,wBACI,oBAAqB,CACrB,WACJ,CAEA,8CACI,QACJ,CAEA,iBACI,YACJ,CAEA,iBACI,0BAA2B,CAC3B,SACJ,CAEA,kBACI,oBACJ,CAEA,oBAGI,4BAA6B,CAF7B,kBAAmB,CACnB,oBAEJ,CAEA,kBACI,YACJ,CAEA,0BACI,cACJ,CAEA,sBACI,iBACJ,CAEA,6BAWI,wCAAyC,CADzC,uCAAqC,CAJrC,iBAAkB,CAIlB,8BAAqC,CATrC,UAAW,CAEX,aAAc,CAEd,WAAY,CAEZ,SAAU,CALV,iBAAkB,CAMlB,QAAS,CAJT,UAQJ,CAEA,mBACI,+BAAgC,CAEhC,iCAAkC,CADlC,6BAEJ,CAEA,yBAGI,kCAAmC,CAInC,WAAY,CANZ,UAAW,CAGX,SAAU,CAFV,iBAAkB,CAIlB,UAAW,CADX,QAGJ,CAEA,qCACI,oCAAqC,CACrC,wBACJ,CAEA,8CACI,sBACJ,CAEA,iBACI,eAEJ,CAEA,iCAHI,wCAMJ,CAHA,gBACI,eAEJ,CAEA,4BACI,iBACJ,CAEA,mCAEI,WAAY,CACZ,SAAU,CACV,OAAQ,CAHR,UAIJ,CAEA,qBACI,kCAAmC,CACnC,kCAAmC,CACnC,0BACJ,CAEA,2BACI,uCAAwC,CACxC,mCACJ,CAEA,oCACI,kCAAmC,CACnC,kCACJ,CAEA,uBACI,2BAA4B,CAC5B,wBACJ,CAEA,6BACI,gCACJ,CAEA,iBAEI,kCAAmC,CACnC,eAAgB,CAFhB,0BAGJ,CAEA,uBAEI,iCAAkC,CADlC,0BAEJ,CAEA,qBACI,GAAO,uBAA0B,CACrC,CAEA,iBACI,GAEI,WAAY,CADZ,UAEJ,CACJ,CClLA,YAGI,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CACvB,YACJ,CAEA,cACI,eACJ,CCVA,iBAGI,kCAAmC,CADnC,8BAA8C,CAD9C,YAGJ,CAEA,6GAGI,eAAgB,CAChB,QACJ,CAEA,gKAII,4BAA6B,CAD7B,yBAEJ,CAEA,mKAII,2BAA4B,CAD5B,wBAGJ,CAMA,8IAEI,gBACJ,CCnCA,YACI,cAAe,CAEf,YAAa,CACb,kBAAmB,CAEnB,WAAY,CADZ,cAAe,CAEf,kBACJ,CAEA,uBACI,YAAa,CACb,WACJ,CAEA,sBACI,YAAa,CACb,qBACJ,CAEA,yCACI,YACI,qBACJ,CACJ,CCxBA,UAII,kCAAmC,CADnC,kCAAmC,CADnC,aAAc,CAGd,eAAgB,CAJhB,UAKJ,CAEA,6CACI,iBACJ,CAMA,+DACI,uCACJ,CAEA,oDACI,iCACJ,CAEA,iCACI,cACJ,CAEA,mDAEI,yCAA0C,CAD1C,kBAEJ,CAEA,6BACI,gBAAiB,CAEjB,qBAAsB,CADtB,kBAEJ,CAEA,kDAGI,kBAAmB,CAFnB,YAAa,CACb,OAEJ,CAEA,0CAEI,wCAAyC,CAEzC,cAAe,CAHf,mBAAoB,CAEpB,WAEJ,CAEA,gDACI,4BACJ,CAEA,sBACI,gBACJ,CAEA,2EACI,iBACJ,CAEA,yEACI,kBACJ,CAEA,4CACI,eAAkB,CAClB,kBACJ,CAEA,oCAEI,kBAAmB,CADnB,YAAa,CAEb,OACJ,CAEA,6BACI,eACJ,CAEA,sBAEI,sBAAuB,CADvB,qBAEJ,CAEA,kCAKI,oBAAgB,CADhB,kBAAmB,CACnB,eAAgB,CAJhB,YAAa,CACb,gCAAiC,CACjC,+BAGJ,CAEA,2CACI,+BACJ,CAEA,wCACI,cACJ,CAEA,yCACI,eACJ,CAEA,4CAEI,+BAAgC,CADhC,cAAe,CAEf,cACJ,CAEA,wFAEI,6CAA8C,CAO9C,wCAAyC,CANzC,4BAA6B,CAC7B,eAAgB,CAEhB,WAAY,CACZ,gBAAiB,CACjB,mBAAwB,CAHxB,UAKJ,CAEA,+CAGI,kBAAmB,CAFnB,YAAa,CACb,sBAEJ,CAEA,qDAEI,WAAY,CADZ,UAEJ,CAEA,8BAWI,yCAA0C,CAV1C,uCAAwC,CAIxC,uFAGkD,CAElD,0BAA2B,CAD3B,yBAA0B,CAN1B,iBAAkB,CADlB,WAAY,CAUZ,UAAY,CARZ,UASJ,CAEA,+CACI,gBAAuB,CACvB,cACJ,CAEA,mBACI,GACI,0BACJ,CACJ,CCjKA,OAEI,YAAa,CAGb,MAAO,CAJP,cAAe,CAGf,KAAM,CADN,WAGJ,CAEA,eAQI,kBAAmB,CADnB,YAAa,CADb,WAAY,CAGZ,sBAAuB,CANvB,MAAO,CAFP,iBAAkB,CAGlB,KAAM,CAMN,2BAA6B,CAL7B,UAAW,CAHX,WASJ,CAEA,aAEI,kCAAmC,CADnC,wCAAyC,CAKzC,kCAA6C,CAC7C,eAAgB,CAHhB,eAAgB,CAIhB,iBAAkB,CAHlB,YAAa,CAIb,iBAAkB,CANlB,UAOJ,CAOA,4BACI,eACJ,CAEA,0BACI,eACJ,CAEA,+BAKI,eAAgB,CAEhB,YAAa,CACb,qBAAsB,CALtB,YAAa,CAFb,gBAAiB,CACjB,eAAgB,CAIhB,SAAU,CAFV,WAKJ,CAEA,2BAII,kBAAmB,CAFnB,YAAa,CACb,kBAAmB,CAFnB,kBAIJ,CAEA,gCACI,eACJ,CAEA,+BACI,gBACJ,CAEA,gCAEI,yCAA0C,CAD1C,iBAEJ,CAEA,0DAGI,kBAAmB,CAFnB,YAAa,CACb,QAEJ,CAEA,iCACI,WAAY,CAEZ,aAAc,CADd,iBAEJ,CAEA,oBACI,iBAAkB,CAElB,UAAW,CADX,QAEJ,CAEA,sEAGI,WAAY,CADZ,UAEJ,CAEA,8BAGI,WAAY,CADZ,eAAgB,CADhB,YAGJ,CAEA,2BACI,eACJ,CAEA,eACI,oBAA2B,CAK3B,QAAS,CAFT,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAIN,WACJ,CAEA,yBACI,qEACJ,CAEA,yBACI,oDACJ,CAEA,0DAEI,SACJ,CAEA,0DAEI,SACJ,CAEA,oCACI,qBACJ,CAGA,4BAEE,cAAe,CADf,WAEF,CAGA,yBACE,eAAgB,CAChB,eACF,CCzJA,SAEI,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,aACJ,CAEA,uBACI,YAAa,CACb,qBACJ,CAEA,oCAEI,aAAc,CADd,QAEJ,CAEA,kBAGI,gBAAiB,CAFjB,YAAa,CAGb,QAAS,CAFT,wBAGJ,CAEA,eACI,cAAe,CACf,cACJ,CAEA,oBACI,QAAS,CACT,mBACJ,CAEA,oBACI,kBACJ,CAEA,6BACI,aACJ,CAEA,oBACI,aACJ,CAEA,uBACI,aACJ,CAEA,8BACI,QACJ,CCpDA,eACI,kCAAmC,CACnC,kCAAmC,CACnC,kBACJ,CAEA,yBAKI,yCAA0C,CAJ1C,aAAc,CAEd,YAAa,CAGb,QAAS,CAFT,6BAA8B,CAF9B,YAKJ,CAEA,2BACI,aACJ,CAEA,2BAEI,iBAAkB,CADlB,gBAEJ,CAEA,mCAEI,kBAAmB,CADnB,eAEJ,CAEA,oCACI,qBACJ,CAEA,0BAEI,QAAS,CACT,eAAgB,CAFhB,YAAa,CAIb,cAAe,CADf,eAEJ,CAEA,oCAII,kBAAmB,CAHnB,eAAkB,CAElB,QAAS,CADT,aAGJ,CAEA,2DAGI,kBAAmB,CADnB,6CAA8C,CAD9C,UAAW,CAGX,iBACJ,CAEA,+CACI,oBACJ,CAEA,gDACI,cACJ,CAEA,gDACI,eACJ,CCjEA,wBACI,0BACJ,CCFA,SACI,kCAAmC,CACnC,kCAAmC,CACnC,kCAAmC,CACnC,8DAAsF,CAItF,cAAe,CAFf,eAAgB,CAGhB,eAAgB,CAFhB,aAAc,CAFd,WAKJ,CAEA,cAII,kBAAmB,CACnB,cAAe,CAHf,YAAa,CACb,OAAQ,CAFR,gBAKJ,CAEA,yBACI,qBACJ,CAEA,oBACI,uCACJ,CAEA,kBAEI,WAAY,CAEZ,gBAAiB,CADjB,eAAgB,CAFhB,UAIJ,CCjCA,+DAEI,kBAAmB,CACnB,oBACJ,CAGA,2EAEI,8BACJ,CAEA,4BACI,iBACJ,CAEA,uEAEI,iBACJ,CAEA,oBAII,WAAY,CADZ,SAAU,CAFV,iBAAkB,CAClB,OAAQ,CAIR,0BAA6B,CAD7B,UAEJ,CAEA,8BAEI,YAAa,CADb,UAEJ,CAEA,oCAEI,4BAA6B,CAD7B,yBAA0B,CAG1B,WAAY,CADZ,UAEJ,CAEA,sBAEI,kCAAoB,CAEpB,+CAAgD,CAFhD,mBAAoB,CACpB,4CAA6C,CAE7C,iBACJ,CChDA,eACI,kCAAmC,CACnC,kCAAmC,CAEnC,kCAAmC,CACnC,8BAA8C,CAF9C,mBAAoB,CAGpB,eAAgB,CAChB,eACJ,CAEA,iBAEI,QAAS,CACT,wCAAyC,CAFzC,iBAGJ,CAEA,kCACI,kCAAmC,CAKnC,0BAA2B,CAF3B,cAAe,CAFf,cAAe,CACf,gBAAiB,CAIjB,QAAS,CAFT,oBAGJ,CAEA,wCACI,iCACJ,CAEA,2CACI,+BAAgC,CAChC,kBACJ,CAEA,iDACI,kCACJ,CAEA,2CACI,iCAAkC,CAClC,eACJ,CAEA,0CAGI,kBAAmB,CADnB,YAAa,CAEb,OACJ,CAEA,kDAEI,UACJ,CAEA,qBACI,oBACJ,CAEA,yBACI,UACJ,CC9DA,UACI,cAAe,CACf,YAAa,CACb,kBAAmB,CACnB,kBAAmB,CACnB,4BACJ,CAEA,4BACI,qBACJ,CCFA,oDANE,YAAa,CACb,qBAAsB,CACtB,UAWF,CAPA,6BAIE,qCAAsC,CACtC,iBAAkB,CAClB,eACF,CAGA,kBAGE,+BAAgC,CAChC,4CAA6C,CAE7C,uBAAwB,CALxB,YAAa,CAIb,eAAgB,CAEhB,iBAAkB,CALlB,UAMF,CAEA,mBAGE,kBAAmB,CADnB,YAAa,CAEb,QAAO,CAHP,aAIF,CAGA,mBAGE,kBAAmB,CAEnB,wCAAyC,CAJzC,YAAa,CACb,sBAAuB,CAIvB,gBAAiB,CAFjB,iBAAkB,CAGlB,UACF,CAEA,qBAIE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAGtB,aAAc,CADd,iBAEF,CAEA,kBACE,kBACF,CAEA,sBAGE,yDAA0D,CAF1D,YAAa,CACb,UAEF,CAEA,mBAIE,uBAAwB,CAHxB,cAAe,CACf,eAAgB,CAChB,iBAEF,CAEA,qBACE,8BAA+B,CAC/B,cAAe,CACf,eACF,CAGA,gEAEE,mBAAqB,CADrB,2BAEF,CAEA,oEACE,UACF,CAGA,gEACE,kBAAmB,CAGnB,MAAO,CADP,iBAAkB,CAElB,OAAQ,CAHR,UAIF,CAGA,yBACE,mBACE,iBACF,CAEA,sBACG,YACH,CAEA,mBACE,cACF,CAEA,qBACE,cACF,CACF,CAGA,mBACE,UACF,CAEA,+BAEE,wBAAyB,CADzB,UAEF,CAEA,sBAGE,+BAAgC,CAChC,eAAgB,CAHhB,YAAa,CACb,eAGF,CAEA,6BACE,cACF,CAEA,yCAEE,kBAAmB,CADnB,YAAa,CAEb,6BACF,CAEA,qCACE,iBAAkB,CAClB,iBACF,CAEA,wCAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAGtB,QAAS,CADT,sBAEF,CAEA,qCAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAEA,yCAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAEA,sCAIE,aAAc,CAHd,iBAAkB,CAClB,eAAgB,CAChB,QAEF,CAEA,wCACE,aAAc,CAEd,aAAc,CADd,eAEF,CAGA,qBAEE,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,eACF,CAEA,0BAEE,aAAc,CADd,iBAAmB,CAEnB,eACF,CAGA,yBACE,qBACE,qBAAsB,CACtB,SACF,CACF,CC1LA,SAUI,YAAa,CACb,qBAAsB,CAFtB,YAGJ,CAGA,eACI,aAAc,CAEd,kBAAmB,CADnB,eAAgB,CAEhB,mBACJ,CAGA,mBACI,UACJ,CAEA,yCAEI,YAAa,CACb,sBAAuB,CAFvB,UAGJ,CAEA,6CACI,WAAY,CACZ,UACJ,CAEA,yBAGI,yCAA0C,CAE1C,QAAS,CADT,iBAAkB,CAFlB,iBAKJ,CAEA,+CAHI,kBAAmB,CALnB,YAWJ,CAEA,0BAGI,yBAA0B,CAD1B,aAAc,CADd,WAGJ,CAGA,IACI,YAAa,CACb,UACJ,CAEA,kBAGI,qBAAsB,CAFtB,cAIJ,CAEA,wBAHI,kBAAmB,CAFnB,YAgBJ,CAXA,MAKI,kCAAmC,CADnC,0BAA2B,CAE3B,cAAe,CACf,eAAgB,CAGhB,OAAQ,CAFR,iBAAkB,CANlB,YAAa,CACb,oBAQJ,CAEA,yBAGI,YAAa,CACb,sBAAuB,CACvB,kBAAmB,CAJnB,cAAe,CACf,UAIJ,CAEA,8BACI,YAAa,CAGb,WAAY,CAFZ,SAAU,CAGV,eAAgB,CAFhB,iBAGJ,CAEA,mCAEI,WAAY,CACZ,cAAe,CAFf,UAGJ,CAGA,YACI,0BAGJ,CAOA,gBAGI,YAAa,CADb,WAAY,CAEZ,gBAAiB,CAHjB,UAIJ,CAGA,yBAII,kCAAmC,CAFnC,kCAAmC,CAInC,aAAc,CALd,oBAA0B,CAE1B,eAAgB,CAIhB,iBAAkB,CAFlB,uBAGJ,CAEA,+BACI,8BAAwC,CAExC,cAAe,CADf,0BAEJ,CAEA,yBAEI,kBAAmB,CADnB,YAAa,CAEb,iBACJ,CAEA,qBACI,iBACJ,CAEA,aAEI,WAAY,CAEZ,iBAAkB,CAHlB,UAIJ,CAEA,uBAUI,kBAAmB,CANnB,kBAAoB,CAEpB,iBAAkB,CAJlB,WAAY,CAWZ,0BAAwC,CARxC,UAAY,CAIZ,YAAa,CAGb,cAAe,CAJf,WAAY,CAGZ,sBAAuB,CAVvB,iBAAkB,CAElB,SAAU,CAIV,UAOJ,CAEA,2BAEI,WAAY,CADZ,UAEJ,CAEA,aACI,QACJ,CAEA,gBAII,0BAA2B,CAF3B,cAAe,CACf,eAAgB,CAFhB,cAIJ,CAEA,eAGI,4BAA6B,CAD7B,cAAe,CADf,QAGJ,CAGA,2BAGI,YAAa,CACb,sBAAuB,CAHvB,oBAA0B,CAC1B,UAGJ,CAEA,mCACI,YAAa,CACb,sBAAuB,CACvB,YACJ,CAEA,wCACI,cACJ,CAEA,gDACI,iBACJ,CAEA,kDACI,WAAY,CAGZ,WAAY,CAFZ,UAAW,CACX,UAEJ,CAEA,sDAEI,WAAY,CADZ,UAEJ,CAGA,gBAMI,kCAAmC,CALnC,sCAAuC,CAIvC,QAAS,CAGT,+BAA0C,CAL1C,eAAgB,CAChB,eAAgB,CAFhB,UAAW,CAKX,SAEJ,CAOA,2BACI,YAAa,CACb,sBAAuB,CACvB,cACJ,CAEA,wCAEI,kBAAmB,CADnB,YAAa,CAMb,WAAY,CAJZ,6BAA8B,CAE9B,eAAgB,CAChB,iBAAkB,CAFlB,UAIJ,CAEA,8CACI,6CACJ,CAEA,gCAKI,4BAA6B,CAC7B,kBAAmB,CALnB,aAAc,CAMd,eAAgB,CAHhB,WAAY,CAFZ,gBAAiB,CAMjB,eAAgB,CALhB,UAMJ,CAEA,0CACI,cACJ,CAEA,oCAEI,WAAY,CACZ,gBAAiB,CAFjB,UAGJ,CAEA,+BACI,WAAY,CAKZ,cAAe,CADf,cAAe,CAHf,eAAgB,CAKhB,iBAAkB,CAJlB,sBAAuB,CACvB,kBAIJ,CAEA,+BAMI,YAAa,CALb,aAAc,CAGd,cAAe,CACf,eAAgB,CAFhB,cAAe,CADf,eAAgB,CAKhB,iBACJ,CAEA,gCACI,aAAc,CAEd,eAAgB,CADhB,eAEJ,CAGA,sBAEE,YAAa,CADb,iBAEF,CAQA,qBAEE,kBAAmB,CAKnB,eAAgB,CAChB,WAAY,CAFZ,+BAAgC,CAIhC,cAAe,CATf,YAAa,CAQb,cAAe,CANf,6BAA8B,CAE9B,iBAAkB,CADlB,UAOF,CAEA,mBACE,iBACF,CAEA,qBAEE,cAAe,CADf,gBAEF,CAGA,yBACE,sDAAoF,CACpF,0BAAwC,CACxC,iBAAkB,CAElB,WAAY,CADZ,YAAa,CAEb,iBACF,CAEA,cAEE,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CACvB,iBACF,CAEA,eAGE,uBAAwB,CAExB,cAAe,CAJf,cAAe,CACf,eAAgB,CAEhB,QAEF,CAEA,uBACE,YAAa,CAEb,OAAQ,CADR,sBAAuB,CAEvB,iBACF,CAEA,YAIE,oBAAkC,CAElC,iBAAkB,CAHlB,0BAA2B,CAF3B,cAAe,CACf,eAAgB,CAGhB,eAEF,CAEA,eACE,yBAA0B,CAE1B,aAAc,CADd,eAAgB,CAEhB,iBACF,CAEA,gBAEE,wBAA0B,CAE1B,qBAAuB,CAHvB,wBAA0B,CAE1B,0BAEF,CAEA,2BAEE,kBAAmB,CAInB,sDAAoF,CACpF,iBAAkB,CAGlB,0BAA2B,CAT3B,YAAa,CAOb,cAAe,CACf,eAAgB,CANhB,sBAAuB,CAEvB,aAAc,CADd,cAOF,CAEA,YACE,gBACF,CAGA,oBACE,YAAa,CACb,iBACF,CAEA,sBAGE,uBAAwB,CADxB,eAAgB,CADhB,kBAGF,CAEA,kBACE,oBAAmC,CACnC,iBAAkB,CAElB,aAAc,CADd,YAEF,CAEA,oBAEE,eAAgB,CADhB,kBAEF,CAEA,sBACE,YAAa,CAEb,QAAS,CADT,sBAAuB,CAEvB,eACF,CCjdA,eACI,kBACJ,CAEA,wBAII,kCAAmC,CACnC,kCAAmC,CAEnC,8BAA8C,CAL9C,YAAa,CACb,QAAS,CAGT,WAAY,CALZ,UAOJ,CAEA,8BACI,QACJ,CAEA,uBAEI,wCAAyC,CAEzC,cAAe,CAHf,WAAY,CAEZ,gBAAiB,CAEjB,iBACJ,CAEA,+BAII,aAAc,CAFd,cAAe,CADf,eAAgB,CAEhB,iBAEJ,CAEA,2DACI,iCAAkC,CAClC,mCACJ,CAEA,gCACI,+BAAgC,CAEhC,iCAAkC,CADlC,6BAEJ,CAEA,gCACI,kBACJ,CC1CA,gBAOI,yCAEJ,CAEA,cASI,6BAAoC,CAFpC,oBAAqB,CAJrB,cAAe,CACf,iBAAkB,CAElB,gBAQJ,CAEA,oBAEI,yCACJ,CAEA,uBAEI,kDACJ,CAEA,uBAEI,YAAa,CADb,UAEJ,CC5CA,QAOI,kBAAmB,CACnB,iCAAkC,CAJlC,kCAAmC,CAKnC,0BAA2B,CAJ3B,mBAAoB,CAHpB,cAAe,CAQf,eAAgB,CAJhB,OAAQ,CAHR,gBAAiB,CAQjB,wBAAyB,CAVzB,eAWJ,CAEA,YAEI,WAAY,CADZ,UAEJ,CAEA,aACI,iCAAkC,CAClC,4BACJ,CAEA,gBACI,kCAAmC,CACnC,6BACJ,CAEA,cACI,gCAAiC,CACjC,2BACJ,CAEA,aACI,mCAAoC,CACpC,8BACJ,CAEA,cAEI,kBAAmB,CADnB,mBAAoB,CAEpB,OACJ,CAEA,aAII,wCAAyC,CAHzC,cAAe,CAEf,OAAQ,CADR,eAGJ,CAEA,cACI,YACJ,CAEA,mBAEI,WAAY,CADZ,UAEJ,CC3DA,cAEI,aAAS,CADT,YAAa,CACb,QAAS,CACT,aACJ,CAEA,SAOI,kBAAmB,CANnB,kCAAmC,CACnC,kCAAmC,CAGnC,YAAa,CACb,QAAS,CAHT,YAAa,CACb,iCAIJ,CAEA,qBAAuB,cAAiB,CACxC,2BACI,+BACJ,CAEA,kBACI,qCACJ,CAEA,aACI,kCAAmC,CAGnC,aAAc,CADd,cAAe,CADf,UAGJ,CAEA,cACI,WACJ,CAEA,YACI,cACJ,CAEA,WAGI,+BAAgC,CAFhC,cAAe,CACf,QAEJ,CAEA,eACI,iBACJ,CAEA,kBACI,cAAe,CACf,eACJ,CAEA,yCACI,cACI,mCACJ,CACJ,CC3DA,cACI,kCAAmC,CAMnC,QAAS,CAJT,YAAa,CACb,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CACR,KAAM,CAEN,SACJ,CAEA,wBAKI,WAAa,CAFb,SAAW,CAFX,iBAAkB,CAGlB,UAAY,CAFZ,QAIJ,CAEA,qBAKI,kBAAmB,CAFnB,yCAA0C,CAF1C,YAAa,CACb,QAAS,CAET,iBAEJ,CAEA,kCAQI,kBAAmB,CAPnB,wCAAyC,CAIzC,4BAA6B,CAE7B,YAAa,CAHb,cAAe,CAFf,wBAAyB,CAOzB,sBAAuB,CANvB,gBAAiB,CAGjB,kBAIJ,CAEA,wBAGI,WAAY,CADZ,QAAS,CADT,SAGJ,CAEA,qBAUI,kBAAmB,CATnB,kCAAmC,CAInC,wCAAyC,CAFzC,WAAY,CAGZ,2BAAyC,CAEzC,YAAa,CACb,QAAS,CAFT,YAAa,CALb,cAAe,CAEf,UAOJ,CAEA,sCACI,gBACJ,CAEA,wCACI,aACJ,CAEA,qDAEI,cAAe,CACf,eAAgB,CAFhB,wBAGJ,CAEA,uBAII,WAAY,CADZ,YAAa,CAFb,iBAAkB,CAClB,kBAGJ,CAEA,8BACI,YAAa,CACb,WACJ,CAEA,aAII,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAA8B,CAE9B,gBAAiB,CAEjB,aAAc,CACd,YAAa,CAFb,iBAAkB,CALlB,UAQJ,CAEA,eACI,8BAA+B,CAE/B,wBAAyB,CADzB,kCAAmC,CAInC,YAAa,CACb,qBAAsB,CAHtB,eAAgB,CAChB,SAGJ,CAEA,wBACI,eACJ,CAEA,6BACI,wBACJ,CAEA,yEAGI,+BAAgC,CADhC,wBAEJ,CAEA,sCACI,qBACJ,CAEA,+BACI,YAAa,CAEb,QAAS,CADT,wBAAyB,CAEzB,YACJ,CAEA,gBACI,YACJ,CAEA,uCACI,kCACJ,CAEA,uBAEI,WAAY,CADZ,UAEJ,CC7IA,WACI,iBACJ,CAEA,eAEI,cAAe,CADf,aAEJ,CAEA,0BAgBI,kBAAmB,CAdnB,uBAAgB,CAAhB,eAAgB,CADhB,kCAAmC,CAEnC,sBAA6B,CAI7B,0BAA2B,CAI3B,cAAe,CAGf,YAAa,CALb,cAAe,CADf,eAAgB,CAShB,OAAQ,CAFR,6BAA8B,CAL9B,gBAAiB,CAJjB,QAAS,CAOT,+BAAgC,CAThC,iBAAkB,CAQlB,iBAAkB,CAPlB,UAaJ,CAEA,gCACI,8BAA+B,CAE/B,kCAAmC,CADnC,8BAEJ,CAEA,sCACI,mCAAoC,CACpC,SACJ,CAEA,wCACI,6CACJ,CAEA,gCAEI,wCAAyC,CADzC,eAEJ,CAEA,oCAEI,WAAY,CADZ,UAEJ,CAEA,+CAEI,aAAc,CADd,WAAY,CAEZ,eACJ,CAEA,8CACI,mBAAoB,CACpB,sBAAuB,CACvB,cACJ,CAEA,6BACI,gCACJ,CAEA,6BACI,+BACJ,CAEA,kEAEI,SACJ,CAEA,kEAEI,SACJ,CAEA,mCACI,WACJ,CAEA,2BACI,kCAAmC,CACnC,kCAAmC,CAEnC,kCAAmC,CADnC,8BAA6C,CAE7C,eAAgB,CAIhB,QAAS,CAFT,gBAAiB,CACjB,eAAgB,CAFhB,WAAY,CAIZ,WACJ,CAEA,0BAKI,kBAAmB,CAEnB,wCAAyC,CADzC,cAAe,CAHf,YAAa,CAKb,eAAgB,CAEhB,QAAS,CANT,6BAA8B,CAF9B,eAAgB,CAOhB,YAAa,CARb,iBAUJ,CAEA,sCACI,YACJ,CAEA,qCACI,eACJ,CAEA,+BACI,gBACJ,CAEA,uCACI,YACJ,CAEA,iCACI,uCACJ,CAEA,mCACI,iCACJ,CAEA,gDACI,aAAc,CACd,WACJ,CAEA,uCACI,WAAY,CACZ,UACJ,CAEA,mCACI,4BAA6B,CAC7B,kBACJ,CtBtJA,qBACE,YAAa,CACb,qBAAsB,CACtB,QAAS,CACT,UACF,CAGA,kBAGE,kBAAmB,CAKnB,kBAAmB,CAPnB,YAAa,CAGb,cAAe,CACf,QAAS,CAHT,6BAA8B,CAI9B,kBAAmB,CACnB,oBAEF,CAEA,kBAEE,iBAAkB,CADlB,WAEF,CAEA,wBAKE,qCAAsC,CADtC,kCAAmC,CADnC,kCAAmC,CAGnC,eAAiB,CAJjB,+BAAkC,CADlC,UAMF,CAEA,8BACE,uCAAwC,CACxC,YACF,CAEA,yBACE,YAAa,CAMb,eAAiB,CAJjB,WAAa,CAGb,UAAY,CAJZ,iBAAkB,CAElB,OAAQ,CACR,0BAGF,CAGA,gBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,mCAAqC,CAGrC,gBAAkB,CADlB,UAEF,CAGA,eAGE,qCAAsC,CACtC,qCAAsC,CACtC,iBAAkB,CAGlB,cAAe,CAPf,YAAa,CACb,qBAAsB,CAOtB,WAAY,CAHZ,eAAgB,CAChB,sCAGF,CAEA,qBACE,6CACF,CAGA,sBAGE,kBAAmB,CAGnB,wCAAyC,CADzC,4CAA6C,CAJ7C,YAAa,CACb,6BAA8B,CAE9B,cAGF,CAQA,qDAJE,kBAAmB,CADnB,YAAa,CAEb,sBAWF,CARA,iCAME,wCAAyC,CACzC,wCAAyC,CAFzC,WAAY,CADZ,UAIF,CAGA,uBACE,YAAa,CACb,qBAAsB,CAEtB,WAAY,CACZ,SAAW,CAFX,cAGF,CAEA,qBAIE,sBAAuB,CAFvB,YAAa,CACb,6BAA8B,CAF9B,eAIF,CAEA,wBASE,oBAAqB,CACrB,2BAA4B,CAN5B,gCAAiC,CAIjC,mBAAoB,CANpB,cAAe,CACf,eAAgB,CAEhB,eAAgB,CAJhB,QAAS,CAKT,eAAgB,CAChB,sBAIF,CAEA,wBAEE,+BAAgC,CADhC,eAAiB,CAEjB,iBACF,CAGA,uBAGE,eAAY,CAFZ,YAAa,CAEb,UAAY,CADZ,6BAA8B,CAE9B,eACF,CAEA,cAIE,WACF,CAEA,cAIE,oBACF,CAEA,cACE,gBAAkB,CAGlB,eAAgB,CAChB,sBAAuB,CAFvB,kBAGF,CAEA,iBACE,eAEF,CAGA,sBAGE,yCAA0C,CAE1C,+BAAgC,CADhC,gBAAkB,CAHlB,eAAgB,CAChB,iBAIF,CAGA,4BAKE,wCAAyC,CACzC,kCAAmC,CAHnC,YAKF,CAGA,sBAGE,kBAAmB,CAInB,yCAA0C,CAN1C,YAAa,CAIb,gBAAkB,CAHlB,6BAA8B,CAI9B,gBAAkB,CAFlB,gBAIF,CAEA,qBAEE,SACF,CAEA,4BAIE,wCAAyC,CAFzC,wCAAyC,CAIzC,eAAiB,CALjB,oBAMF,CAEA,iDACE,6CACF,CAQA,0BACE,gBACE,mCACF,CACF,CAEA,0BACE,gBACE,mCACF,CACF,CAEA,yBACE,kBAEE,mBAAoB,CADpB,qBAEF,CAEA,kBACE,UACF,CAEA,gBACE,yBACF,CACF,CAEA,yBACE,sBACE,qBAAsB,CACtB,UACF,CACF,CAGA,SAIE,kBAAmB,CAFnB,YAAa,CACb,SAAW,CAFX,iBAIF,CAEA,gBAOE,4CAA6C,CAJ7C,QAAS,CAFT,UAAW,CAGX,UAAW,CACX,MAAO,CACP,iBAAkB,CAJlB,UAAW,CAMX,SACF,CAEA,cACE,eAAgB,CAKhB,QAAS,CACT,6BAAoC,CALpC,+BAAgC,CAMhC,cAAe,CALf,eAAiB,CACjB,eAAgB,CAChB,oBAAuB,CAMvB,iBAAkB,CAFlB,oBAAqB,CACrB,UAEF,CAMA,2CAHE,0BAMF,CAHA,uBAEE,kDACF,CAGA,iBACE,2CAA4C,CAE5C,WAAY,CACZ,kCAAmC,CAFnC,UAAY,CAIZ,eAAgB,CADhB,kBAAoB,CAEpB,+BACF,CAEA,uBACE,wCACF,CAIA,6BAIE,iBAAkB,CAFlB,eAAiB,CACjB,eAAgB,CAEhB,iBAAmB,CAJnB,oBAAuB,CAKvB,kBACF,CAEA,mCACE,uCAAwC,CACxC,+BACF,CAEA,qCACE,wCAAyC,CACzC,6BACF,CAEA,kCACE,uCAAwC,CACxC,4BACF,CAEA,mCACE,sCAAuC,CACvC,2BACF,CuBrVA,+BAGE,WAAY,CADZ,SAAU,CAEV,SACF,CCVA,gBAEE,oBAAqB,CADrB,iBAEF,CAEA,+BAKE,kBAAmB,CAKnB,iBAAkB,CAElB,gCAA0C,CAN1C,UAAY,CAEZ,cAAe,CACf,eAAiB,CAKjB,mBAAqB,CACrB,WAAa,CARb,gBAAiB,CANjB,iBAAkB,CAElB,WAAY,CAUZ,wBAAyB,CAXzB,QAAS,CAST,uBAAwB,CAPxB,SAYF,CCrBA,iBACI,kCAAmC,CACnC,kCAAmC,CACnC,YACJ,CAEA,6BACI,uCACJ,CAEA,sBACI,eACJ,CAEA,uBACI,YACJ,CAEA,mBACI,aACJ,CAEA,8BACI,cACJ,CCxBA,uBAGI,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,mCAAqC,CAGrC,cAAe,CADf,iBAEJ,CAEA,sBAII,cAAe,CAEf,wCAAyC,CACzC,cAAe,CAJf,YAAa,CAEb,eAAgB,CAHhB,iBAAkB,CADlB,UAQJ,CAEA,4BAEI,gBAAiC,CAMjC,qCAAsC,CACtC,wCAAyC,CAHzC,QAAS,CALT,UAAW,CAGX,MAAO,CAMP,SAAU,CAPV,iBAAkB,CAElB,OAAQ,CAER,KAAM,CAIN,+BACJ,CAEA,kCACI,SACJ,CAEA,0BAEI,WAAY,CACZ,gBAAiB,CAFjB,UAGJ,CCzCA,SAMI,mBAAoB,CALpB,6BAA8B,CAM9B,kCAAmC,CALnC,kCAAmC,CAEnC,YAAa,CAIb,WAAY,CAHZ,sBAAuB,CAIvB,gBAAiB,CANjB,eAOJ,CAEA,gBAGI,WAAY,CADZ,gBAAiB,CADjB,aAGJ,CAEA,aACI,YAAa,CACb,qBAAsB,CAGtB,gBAAiB,CADjB,aAAc,CADd,UAGJ,CAEA,oBACI,6BAA8B,CAC9B,wBAAyB,CACzB,YACJ,CAEA,gCACI,eACJ,CAEA,yBACI,aACJ,CAEA,oBAEI,eAAgB,CAChB,gBAAiB,CACjB,aAAc,CAHd,UAIJ,CAEA,wBAEI,WAAY,CADZ,cAEJ,CAEA,aAOI,qCAAsC,CADtC,kBAAmB,CAJnB,YAAa,CADb,WAAY,CAEZ,eAAgB,CAEhB,gBAAiB,CAGjB,eAAgB,CAJhB,UAKJ,CAEA,YACI,wCAAyC,CAEzC,qBAAsB,CACtB,QAAS,CAFT,0BAGJ,CAEA,+BAOI,kBAAmB,CAJnB,uCAAwC,CACxC,8CAA+C,CAC/C,YAAa,CAHb,WAAY,CAIZ,sBAAuB,CALvB,UAOJ,CAEA,yDAMI,kBAAmB,CALnB,iCAAkC,CASlC,uCAAwC,CANxC,kBAAmB,CAWnB,6BAA8B,CAV9B,YAAa,CASb,cAAe,CAXf,WAAY,CAIZ,sBAAuB,CAEvB,eAAgB,CADhB,gBAAiB,CANjB,UASJ,CAOA,gCACI,cAAe,CACf,iBACJ,CAEA,yBAEI,iCAAkC,CAClC,kBAAmB,CAGnB,cAAe,CADf,aAAc,CADd,YAAa,CAHb,uBAMJ,CAEA,YAGI,kBAAmB,CAFnB,kBAAuB,CACvB,6DAAkG,CAElG,sBACJ,CAEA,mBAWI,aAAS,CATT,oBAA0B,CAE1B,kBAAmB,CADnB,wBAAyB,CAIzB,YAAa,CAKb,cAAe,CADf,QAAS,CADT,6CAA8C,CAF9C,+BAAgC,CAChC,4BAA6B,CAH7B,WAAY,CADZ,YAAa,CAJb,UAYJ,CAEA,sCAMI,iBAAkB,CAFlB,oBAAiC,CACjC,kBAAmB,CAJnB,cAAe,CAEf,WAAY,CADZ,UAKJ,CAEA,wCAEI,YAAa,CACb,QAAS,CAFT,gBAGJ,CAEA,uCACI,eACJ,CAEA,sCACI,cACJ,CAEA,eACI,YAAa,CACb,UACJ,CAEA,yBAII,YAAa,CAHb,QAAO,CAIP,qBAAsB,CAFtB,gBAAiB,CADjB,aAIJ,CAGA,WAEI,WAAY,CADZ,cAEJ,CC9KA,eACI,8CAA+C,CAC/C,0DAA2D,CAC3D,kDAAmD,CACnD,oDAAqD,CACrD,8DACJ,CCNA,cACI,kCAAmC,CAGnC,kBAAmB,CADnB,YAEJ,CAEA,wBALI,kCAYJ,CAPA,UAII,sBAAuB,CAFvB,YAAa,CACb,qBAAsB,CAEtB,mBAAoB,CAJpB,UAMJ,CAEA,0BACI,kCACJ,CAEA,2BAGI,kBAAmB,CADnB,YAAa,CAGb,OAAQ,CADR,0BAA2B,CAH3B,UAKJ,CAEA,0BACI,YAAa,CACb,qBAAsB,CAEtB,gBAAiB,CADjB,aAEJ,CAEA,4BACI,YAAa,CACb,OAAQ,CAER,gBAAiB,CADjB,cAEJ,CAEA,2CACI,kBACJ,CAEA,sFAEI,aACJ,CAEA,MAEI,YAAa,CAEb,QAAS,CADT,0BAA2B,CAE3B,gBAAiB,CACjB,gBAAiB,CALjB,iBAOJ,CAEA,kBAHI,uCAaJ,CAVA,YAEI,yCAA0C,CAE1C,8CAA+C,CAH/C,UAAW,CAQX,WAAY,CAHZ,SAAU,CADV,iBAAkB,CAElB,KAAM,CACN,UAEJ,CAEA,iBACI,uBACJ,CAEA,YACI,iBAGJ,CAEA,0BAJI,YAAa,CACb,0BASJ,CANA,cAEI,kBAAmB,CAEnB,OAAQ,CACR,WACJ,CAEA,iBACI,QAAS,CACT,eACJ,CAEA,uBACI,+BACJ,CAEA,wBACI,eAAgB,CAChB,UACJ,CAEA,iBACI,eACJ,CAEA,sBACI,eACJ,CC/GA,uBAII,mBAAoB,CAHpB,8BAA+B,CAI/B,kCAAmC,CAHnC,YAAa,CAOb,WAAY,CADZ,gBAAiB,CAFjB,aAAc,CACd,cAAe,CAJf,iBAOJ,CAEA,+BACI,qBACJ,CAEA,8BAEI,kBAAmB,CADnB,UAEJ,CAEA,6CACI,WACJ,CCtBA,MACE,yCACF,CAGA,oBACE,YAAa,CACb,qBAAsB,CACtB,UAAW,CACX,UACF,CAGA,iBAGE,kBAAmB,CAGnB,4CAA6C,CAL7C,YAAa,CACb,6BAA8B,CAE9B,oBAAqB,CAGrB,oBAAsB,CAFtB,UAGF,CAGA,4BAOE,kBAAmB,CAFnB,WAAY,CAHZ,oBAAqB,CADrB,wBAAyB,CAKzB,YAAa,CAFb,eAAgB,CAIhB,SAAW,CALX,qBAAuB,CAMvB,6CACF,CAEA,kCACE,wCAAyC,CACzC,0BACF,CAEA,gCAEE,WAAY,CADZ,UAEF,CAGA,iBAEE,iBAAkB,CADlB,WAEF,CAEA,uBAME,qCAAsC,CACtC,qCAAsC,CAJtC,iBAAkB,CAElB,gCAAiC,CADjC,eAAiB,CAFjB,mBAAqB,CADrB,UAOF,CAEA,oCACE,+BACF,CAEA,6BAEE,uCAAwC,CADxC,YAEF,CAGA,eAGE,gBAAY,CAFZ,YAAa,CAEb,WAAY,CADZ,mCAAqC,CAErC,UACF,CAGA,cAGE,qCAAsC,CACtC,qCAAsC,CACtC,kBAAmB,CAGnB,cAAe,CAPf,YAAa,CACb,qBAAsB,CAItB,eAAgB,CAChB,iEAEF,CAEA,oBAEE,yCAA0C,CAD1C,0BAEF,CAGA,qBAGE,kBAAmB,CAEnB,+CAAgD,CAChD,4CAA6C,CAL7C,YAAa,CACb,6BAA8B,CAE9B,YAGF,CAQA,mDAJE,kBAAmB,CADnB,YAAa,CAEb,sBAYF,CATA,gCAME,2CAA4C,CAE5C,iBAAkB,CADlB,wBAAyB,CAFzB,WAAY,CADZ,UAKF,CAEA,oCAEE,WAAY,CADZ,UAEF,CAEA,sBACE,+BACF,CAGA,sBAEE,YAAa,CACb,qBAAsB,CACtB,QAAS,CAHT,YAIF,CAEA,oBAGE,sBAAuB,CAFvB,YAAa,CACb,6BAEF,CAEA,uBAIE,gCAAiC,CAFjC,gBAAiB,CACjB,eAAgB,CAEhB,eAAgB,CAJhB,QAKF,CAEA,qBAGE,+BAAgC,CADhC,iBAAmB,CAEnB,eAAgB,CAHhB,gBAIF,CAGA,4BAIE,iBAAkB,CAGlB,aAAc,CALd,gBAAkB,CAClB,eAAgB,CAEhB,gBAAiB,CAJjB,qBAAwB,CAKxB,kBAEF,CAEA,kCACE,uCAAwC,CACxC,+BACF,CAEA,oCACE,wCAAyC,CACzC,6BACF,CAGA,sBAME,yCAA0C,CAL1C,YAAa,CACb,cAAe,CACf,UAAW,CAEX,gBAAkB,CADlB,iBAGF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,cAEE,+BAAgC,CADhC,gBAAkB,CAGlB,oBAAsB,CADtB,wBAEF,CAEA,cAEE,gCAAiC,CADjC,iBAAmB,CAEnB,eACF,CAEA,iBAGE,iBACF,CAGA,sCALE,+BAAgC,CADhC,gBAYF,CANA,qBAGE,yCAA0C,CAF1C,eAAgB,CAChB,kBAIF,CAEA,YACE,iBACF,CAGA,4BAKE,qCAAsC,CAGtC,qCAAsC,CAFtC,kBAAmB,CACnB,+BAAgC,CALhC,gBAAmB,CACnB,YAAa,CACb,iBAKF,CAGA,qBAGE,kBAAmB,CAGnB,yCAA0C,CAE1C,+BAAgC,CAPhC,YAAa,CAIb,eAAiB,CAHjB,6BAA8B,CAK9B,eAAgB,CAHhB,cAKF,CAEA,qBAEE,UACF,CAEA,4BAIE,qCAAsC,CADtC,qCAAsC,CADtC,iBAAkB,CAGlB,gCAAiC,CACjC,cAAe,CACf,gBAAkB,CANlB,kBAAoB,CAOpB,+BACF,CAEA,iDACE,+CACF,CAEA,qCAEE,kBAAmB,CADnB,UAEF,CAGA,yBACE,eACE,mCACF,CACF,CAEA,yBACE,eACE,mCACF,CACF,CAEA,0BACE,eACE,mCACF,CACF,CAEA,0BACE,eACE,mCACF,CACF,CC/SA,cACI,iBACJ,CAEA,kBAEI,uCAAwC,CADxC,kCAAmC,CAMnC,QAAS,CAFT,eAAgB,CADhB,wBAAyB,CAEzB,YAAa,CAHb,mBAKJ,CAEA,uBACI,wBACJ,CAEA,2BACI,iBAAkB,CAClB,UAAW,CACX,QACJ,CCtBA,eACI,wCAAyC,CACzC,eAAgB,CAChB,iBACJ,CAEA,sBAEI,eAAgB,CADhB,yBAEJ,CAEA,qBAMI,QAAS,CALT,UAAW,CACX,aAAc,CAGd,MAAO,CAFP,iBAAkB,CAIlB,OAAQ,CAHR,KAIJ,CAEA,yCACI,eACI,YACJ,CACJ,CCzBA,SAEI,mBAAoB,CACpB,WACJ,CAEA,0BALI,YAOJ,CAEA,iBACI,yBAA0B,CAC1B,WACJ,CAEA,iBAEI,8BAA+B,CAC/B,kBAAmB,CAEnB,sBAAsB,CAJtB,eAAgB,CAMhB,eAAgB,CADhB,iBAEJ,CAEA,sBACI,iBACJ,CAEA,0BACI,cACJ,CAEA,oBAEI,QAAS,CADT,iBAEJ,CAEA,kCACI,iBACJ,CAEA,4BAMI,oBAAgB,CAFhB,yCAA0C,CAE1C,eAAgB,CAChB,WAAY,CANZ,YAAa,CACb,gDAAiD,CACjD,8BAA+B,CAE/B,YAGJ,CAEA,kCACI,6CACJ,CAEA,uCACI,eACJ,CAEA,sCACI,iBACJ,CAEA,yCACI,cAAe,CACf,gBACJ,CAEA,kBAQI,kBAAmB,CALnB,gBAAiB,CAFjB,kCAAmC,CAGnC,iBAAkB,CAGlB,YAAa,CALb,gBAAiB,CAIjB,WAAY,CAGZ,sBAAuB,CAJvB,UAKJ,CAEA,sBAEI,WAAY,CADZ,UAEJ,CAEA,sJAII,sCAAuC,CACvC,2BACJ,CAEA,uFACI,gBACJ,CAEA,yEACI,yCAA0C,CAC1C,8BACJ,CAEA,2EACI,uCAAwC,CACxC,4BACJ,CAEA,uEACI,wCAAyC,CACzC,6BACJ,CAEA,gDAEI,0BACJ,CAEA,kEAEI,qBACJ,CAEA,uBACI,6BACJ,CAEA,gCACI,wBACJ,CAEA,wBACI,2BACJ,CAEA,iCACI,sBACJ,CAEA,sBACI,4BACJ,CAEA,+BACI,uBACJ,CAEA,8BACI,6CACJ,CAEA,mCACI,4BAA6B,CAC7B,UACJ,CAEA,iBAEI,eAAiB,CADjB,eAEJ,CAEA,gBACI,cACJ,CAEA,iBACI,uCACJ,CAEA,0BACI,4BACJ,CAEA,cACI,kCAAmC,CACnC,kCAAmC,CAEnC,iBAAkB,CADlB,+BAA+C,CAE/C,eACJ,CAEA,uBACI,UACJ,CAEA,+BACI,SACJ,CAEA,4DAEI,kCACJ,CAEA,4BACI,oCACJ,CAEA,8BACI,mCACJ,CAEA,6BACI,qCACJ,CAEA,wBAMI,kBAAmB,CALnB,6CAA8C,CAE9C,wCAAyC,CAIzC,+BAAgC,CAHhC,YAAa,CACb,kBAAmB,CAHnB,gBAMJ,CAEA,4BACI,WAAY,CACZ,gBACJ,CAEA,0BAGI,kBAAmB,CAEnB,WAAY,CAHZ,YAAa,CAEb,4BAA6B,CAH7B,iBAAkB,CAKlB,UACJ,CAEA,yBAII,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,OAAQ,CAJR,iBAKJ,CAEA,2CAOI,kCAAmC,CADnC,oCAAqC,CAJrC,iBAAkB,CADlB,UAAW,CAGX,WAAY,CADZ,UAAW,CAEX,SAGJ,CAEA,qBAII,kBAAmB,CAFnB,yCAA0C,CAC1C,YAAa,CAEb,QAAS,CAJT,iBAKJ,CAEA,uCAOI,kBAAmB,CALnB,iBAAkB,CAGlB,YAAa,CADb,WAAY,CAEZ,sBAAuB,CALvB,WAAY,CAEZ,UAKJ,CAEA,wCACI,WACJ,CAEA,0CACI,aACJ,CAEA,wBAGI,cAAe,CACf,gBAAiB,CAFjB,QAAS,CADT,aAIJ,CAEA,wCAEI,kBAAmB,CAEnB,uCAAwC,CACxC,wCAAyC,CAJzC,YAAa,CAEb,QAAS,CAGT,gBACJ,CAEA,oDACI,0BACJ,CAEA,8CAEI,kBAAmB,CAGnB,+BAAgC,CAJhC,YAAa,CAGb,eAAgB,CADhB,OAGJ,CAEA,mBACI,YACJ,CAEA,wBAEI,eAAiB,CADjB,kBAEJ,CAEA,mBACI,wCAAyC,CAEzC,kCAAmC,CACnC,iBAAkB,CAFlB,YAAa,CAGb,kBACJ,CAEA,6BAEI,WAAY,CADZ,UAEJ,CAEA,wCACI,2BACJ,CAEA,mCACI,6BACJ,CAEA,sEAEI,cACJ,CAEA,+BACI,mBACJ,CAEA,mBAEI,WAAY,CADZ,iBAAkB,CAElB,yBACJ,CAEA,yBACI,YACJ,CC3VA,uBAGE,WAAY,CAEZ,MAAO,CAJP,iBAAkB,CAGlB,KAAM,CAFN,UAIF,CACA,kBAGE,WAAY,CAFZ,SAGF,CACA,4BACI,cACF,CACF,2BAEI,eACF,CACF,sBAGE,mBAAoB,CAFpB,oBAAqB,CACrB,SAEF,CACA,sBACE,SACF,CACA,uBACE,SACF,CACA,sFAEE,YACF,CACA,+BAEE,gBAAiB,CADjB,mBAEF,CACA,oDAEE,cAAe,CACf,cAAe,CACf,SACF,CACA,kBAEE,cAAe,CADf,4BAEF,CACA,gCACI,kBAAmB,CAEX,sCACV,CACF,6DACI,qBAAsB,CAEd,cACV,CACF,2BACI,mBACF,CACF,mFAGI,YACF,CACF,wJAGI,WACF,CACF,8BACI,kBACF,CACF,yBACI,SACF,CACF,yCACI,mBAAoB,CACpB,wBAAyB,CAEjB,gBACV,CACF,wBACE,mBACF,CACA,kCACI,kBAAmB,CAEX,sCACV,CACF,4BACE,YACF,CACA,mBACE,mBAAoB,CACpB,oBACF,CACA,kBAOE,qBAAsB,CAEtB,WAAY,CAJZ,kBAAmB,CAJnB,iBAAkB,CAKlB,oBAAqB,CAJrB,wBAAyB,CAEjB,gBAMV,CACA,2BAEI,eACF,CACF,4BAGE,mBAAoB,CADpB,yBAA0B,CAD1B,SAGF,CACA,iCAII,WAAY,CAFZ,kBAAmB,CADnB,iBAIF,CACF,oBAOE,kBAAmB,CACnB,qBAAuB,CACvB,kBAAmB,CAHnB,UAAW,CAFX,cAAe,CADf,aAAc,CADd,mBAAoB,CADpB,iBAAkB,CAIlB,SAKF,CACA,wCAEI,gBAAiB,CADjB,kBAEF,CACF,2BAGI,WAAY,CADZ,QAAS,CADT,QAAS,CAGT,yBACF,CACF,wBACI,QAAS,CACT,QAAS,CACT,yBACF,CACF,yBAEI,SAAU,CADV,OAAQ,CAER,0BACF,CACF,0BACI,UAAW,CACX,OAAQ,CACR,0BACF,CACF,yBACE,WAAY,CACZ,kBACF,CACA,mBAGE,WAAY,CAFZ,iBAAkB,CAClB,SAEF,CACA,uBACI,KACF,CACF,0BACI,QACF,CACF,wBACI,MACF,CACF,yBACI,OACF,CACF,0BACI,QAAS,CACT,0BACF,CACF,yBAEE,oBAAoC,CADpC,cAAe,CAGf,QAAS,CADT,eAEF,CACA,2BAEI,UAAW,CADX,oBAEF,CAMF,oBACE,GACE,oBACF,CACF,CACA,gCAGE,WAAY,CACZ,mBAAoB,CAHpB,iBAAkB,CAIlB,wBAAyB,CAEjB,gBAAiB,CALzB,UAMF,CACA,kDACM,WACF,CACJ,uBACI,cACF,CACF,8EAEI,YACF,CACF,mGAaE,qBAAuB,CADvB,wBAAqB,CAPrB,iBAAkB,CAGlB,UAAW,CADX,cAAe,CAHf,YAAa,CAKb,iBAAkB,CAHlB,WAQF,CACA,uKACM,kCACF,CACJ,iiBAYM,6BACF,CACJ,wBACE,0BACF,CACA,wDAEE,oBAAkC,CAClC,2BACF,CACA,wJAII,YACF,CACF,sBACE,gCACF,CACA,6BAOI,kBAAmB,CALnB,kBAAmB,CACnB,WAA6B,CAA7B,4BAA6B,CAC7B,kBAAuB,CAMvB,cAAe,CALf,YAAa,CAIb,WAAY,CAHZ,sBAAuB,CAQvB,WAAY,CAHZ,wBAAyB,CAEjB,gBAAiB,CALzB,UAOF,CACF,mCACM,kBACF,CACJ,iCAGM,eAAgB,CADhB,cAAe,CADf,UAGF,CACJ,sCACM,mBACF,CACJ,0CACQ,eACF,CACN,qBACE,qBACF,CACA,4BACE,iBACF,CACA,mEAEE,gBACF,CACA,mEAEE,gBACF,CACA,8EAEE,kBACF,CACA,8EAEE,kBACF,CAEA,mCAKE,wBAAyB,CAFzB,qBAAsB,CACtB,iBAAkB,CAFlB,UAAW,CAIX,8BAAgC,CALhC,SAMF,CACA,wCACE,MAAO,CACP,OACF,CACA,yCACE,SAAU,CACV,OACF,CACA,uCACE,QAAS,CACT,KACF,CACA,0CACE,QAAS,CACT,QACF,CAIA,2FACE,MACF,CAIA,6FACE,SACF,CAEA,iCAGE,sBACF,CACA,6EAKE,WAAY,CADZ,KAAM,CADN,yBAA6B,CAD7B,SAIF,CACA,sCAEE,qBAAsB,CADtB,MAEF,CACA,uCAEE,sBAAuB,CADvB,SAEF,CACA,6EAEE,UAAW,CAEX,MAAO,CADP,0BAA6B,CAE7B,UACF,CACA,qCAEE,oBAAqB,CADrB,KAEF,CACA,wCACE,uBAAwB,CACxB,QACF,CC/YA,oEACI,kCAAmC,CACnC,kCAAmC,CACnC,YACJ,CAEA,mBAEI,iBAAkB,CADlB,eAEJ,CAEA,kBAEI,+BAAgC,CAEhC,cAAe,CADf,eAAgB,CAFhB,iBAIJ,CChBA,eAGI,kBAAmB,CAFnB,YAAa,CACb,QAEJ,CAEA,mBAGI,kCAAmC,CADnC,cAAe,CADf,UAGJ,CAEA,kBACI,QACJ,CAEA,iBAEI,+BAAgC,CADhC,cAAe,CAEf,QACJ,CCpBA,MAGI,kBAAmB,CAFnB,uCAAwC,CACxC,YAAa,CAIb,qBAAsB,CACtB,QAAS,CAHT,sBAAuB,CACvB,gBAAiB,CAGjB,cACJ,CAEA,gBACI,WACJ,CAEA,WAGI,kCAAmC,CACnC,uCAAwC,CACxC,wCAAyC,CAHzC,eAAgB,CADhB,eAAgB,CAKhB,YACJ,CAEA,uBAGI,eAAgB,CAFhB,eAAgB,CAChB,UAEJ,CAEA,4BACI,QACJ,CAEA,iBACI,cACJ,CAEA,cACI,YAAa,CACb,qBAAsB,CACtB,QACJ,CAEA,gDAEI,UACJ,CAEA,iBAEI,YAAa,CACb,qBAAsB,CACtB,QAAS,CAHT,kBAIJ,CAEA,cACI,eAIJ,CAEA,0BAJI,qBAAsB,CACtB,QAQJ,CALA,YAII,kBAAmB,CAHnB,YAIJ,CAEA,sBACI,UACJ,CAEA,qCACI,0BACJ,CAEA,SACI,aAAc,CAEd,iBAAkB,CADlB,iBAEJ,CAEA,+BAII,iCAAkC,CAFlC,UAAW,CACX,UAAW,CAEX,iBAAkB,CAClB,OAAQ,CACR,SACJ,CAEA,gBACI,MACJ,CAEA,eACI,OACJ,CAEA,yCACI,MACI,iBACJ,CAEA,WACI,cAAe,CACf,UACJ,CACJ,CAEA,cAEE,qBAAsB,CAGtB,cACF,CAEA,iCALE,kBAAmB,CAFnB,YAAa,CAGb,sBAcF,CAVA,mBASE,uCAAyC,CALzC,6BAA8B,CAD9B,iBAAkB,CADlB,WAAY,CAMZ,kBAAmB,CAPnB,UASF,CAEA,uBAGE,WAAa,CACb,cAAe,CACf,oBAAqB,CACrB,qBAAsB,CAEtB,6CAAgD,CANhD,WAAY,CAKZ,kBAAmB,CANnB,UAQF,CAEA,mBACE,GAAO,kBAAqB,CAC5B,GAAK,kBAAqB,CAC5B,CAEA,qBACE,GAAO,kBAAqB,CAC5B,GAAK,kBAAqB,CAC5B,CCvJA,MAEI,kCAAkD,CAClD,4DAA+D,CAC/D,6BAAiC,CACjC,mCAAmD,CACnD,+BAA+C,CAC/C,iCAA8C,CAI9C,6CAA8C,CAC9C,0BAA2B,CAG3B,qCAAoD,CACpD,qCAAoD,CACpD,wCAAwD,CAGxD,gCAA+C,CAG/C,kCAAmC,CACnC,6CAA8C,CAC9C,mCACF,CAGA,kBAEE,kCAA+C,CAC/C,4DAA+D,CAC/D,gCAAiC,CACjC,mCAAgD,CAChD,+BAA4C,CAC5C,iCAA+C,CAG/C,0BACF,CAEA,sBAEE,WAAY,CADZ,UAEF,CAGA,mBACE,oBACF,CAGA,4BACE,YAAa,CACb,UACF,CAEA,oCACE,QACF,CAGA,iBAEE,YAAa,CADb,iBAAkB,CAElB,UACF,CAGA,yCACE,4BACE,qBACF,CAEA,iBACE,YACF,CACF,CACA,cAEE,cAAiB,CADjB,eAEF,CAGA,WAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,oBACF,CAEA,WACE,eAAyC,CAAzC,wCAAyC,CAIzC,qCAAsC,CAHtC,kBAAmB,CAEnB,2BAA4B,CAE5B,YAAa,CACb,qBAAsB,CACtB,6BAA8B,CAL9B,YAMF,CAEA,YAEE,aAA8B,CAA9B,6BAA8B,CAD9B,cAEF,CAEA,YACE,cAAe,CACf,eAAgB,CAChB,cACF,CAGA,iBAIE,0BAA2B,CAH3B,cAAe,CAEf,kBAEF,CAGA,mBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,+BAEF,CAEA,UACE,eAAyC,CAAzC,wCAAyC,CAGzC,qCAAsC,CAFtC,kBAAmB,CAGnB,2BAA4B,CAF5B,YAGF,CAEA,aACE,kBACF,CAEA,eAGE,YAAQ,CAFR,YAAa,CAEb,OAAQ,CADR,mCAAqC,CAErC,iBACF,CAEA,cAEE,oBAAsC,CAAtC,qCAAsC,CACtC,iBAAkB,CAClB,+BAA+B,CAA/B,8BAA+B,CAH/B,aAIF,CAEA,cACE,+BAAgC,CAChC,UACF,CAGA,kBACE,eAAyC,CAAzC,wCAAyC,CAGzC,qCAAsC,CAFtC,kBAAmB,CAGnB,2BAA4B,CAF5B,YAGF,CAEA,gBACE,cAAe,CAEf,iBACF,CAEA,gBACE,cAAe,CACf,kBACF,CAEA,sBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAA8B,CAE9B,eACF,CAEA,kBAME,kBAAmB,CAHnB,oBAAsC,CAAtC,qCAAsC,CACtC,iBAAkB,CAHlB,YAAa,CACb,qBAAsB,CAKtB,sBAAuB,CAFvB,YAGF,CAEA,mBACE,cAAe,CACf,eAAgB,CAChB,iBACF,CAGA,yBACE,mBACE,yBACF,CACF,CAKA,kBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,+BAAgC,CAGhC,cAAe,CADf,iBAEF,CAEA,gBAIE,aAAc,CADd,QAAS,CAFT,eAAgB,CAChB,SAGF,CAGA,6BACE,QACF,CAEA,wBAEE,YAAa,CACb,qBAAsB,CACtB,aAAc,CACd,yBAA0B,CAG1B,eAAgB,CAFhB,eAAgB,CAChB,QAAS,CANT,WAQF,CAEA,4BACE,YAAa,CACb,qBAAsB,CACtB,WAAY,CACZ,eACF,CAEA,0BACE,WAIF,CAGA,sBAKE,4CAA6C,CAD7C,gBAEF,CAEA,wBAME,qCAAsC,CAFtC,qCAAsC,CADtC,iBAAkB,CAIlB,uBAAwB,CAFxB,iBAAmB,CAHnB,gBAMF,CAQA,oBAME,qBAA+C,CAA/C,8CAA+C,CAC/C,8BACF,CASA,gBAEE,4CACF,CAEA,eAIE,aAA8B,CAA9B,6BAA8B,CAF9B,iBAAmB,CADnB,iBAAkB,CAKlB,uBACF,CAEA,qBACE,uBACF,CAEA,sBAEE,4CACF,CAGA,aAGE,iBAAkB,CAClB,iBAAmB,CAHnB,WAAY,CACZ,YAGF,CAEA,oBACE,0BAAwC,CACxC,6BACF,CAEA,kBACE,0BAAyC,CACzC,6BACF,CAEA,qBACE,0BAAyC,CACzC,6BACF,CAEA,cAEE,kBAAmB,CADnB,YAAa,CAEb,eAAgB,CAChB,iBACF,CAEA,kBACE,gBACF,CAEA,mBACE,gBAAkB,CAClB,iBACF,CAEA,kBACE,gBAAkB,CAClB,iBACF,CAGA,0BACE,kBACE,+BACF,CACF,CAEA,0BACE,kBACE,yBACF,CAEA,gBACE,aACF,CAEA,wBACE,YACF,CACF,CAGA,mBAIE,kBAAmB,CAGnB,2BAA4B,CAD5B,kBAAmB,CALnB,gBAAiB,CAIjB,eAAgB,CAFhB,YAKF,CAEA,gBAQE,kBAAmB,CAHnB,iDAAsC,CAAtC,qCAAsC,CACtC,4CAA6C,CAC7C,YAAa,CAGb,cAAe,CADf,sBAAuB,CANvB,kBAAmB,CACnB,YAOF,CAEA,eAME,6BAAoC,CAFpC,yEAA8E,CAA9E,2EAA8E,CAC9E,4BAA6B,CAJ7B,cAAe,CAEf,iBAIF,CAEA,kBAEE,8BAA+B,CAC/B,eACF,CAEA,cAIE,uCAAwC,CAHxC,WAAY,CACZ,eAAgB,CAChB,iBAEF,CAEA,iBACE,GAAK,uBAA4B,CACjC,IAAM,2BAA8B,CACpC,GAAO,uBAA4B,CACrC,CAEA,8BAGE,sBAAuB,CAFvB,YAAa,CACb,qBAAsB,CAEtB,eACF,CAGA,kBACE,eAAyC,CAAzC,wCAAyC,CAKzC,4CAAsC,CACtC,qCAA6C,CAL7C,kBAAmB,CAGnB,2BAA4B,CAD5B,kBAAmB,CADnB,YAKF,CAEA,cAIE,kBACF,CAEA,oBALE,kBAAmB,CADnB,YAAa,CAEb,sBAcF,CAVA,MAQE,oBAAsC,CAAtC,qCAAsC,CALtC,iBAAkB,CAMlB,+BAA+B,CAA/B,8BAA+B,CAF/B,eAAgB,CALhB,WAAY,CADZ,UASF,CAEA,aACE,6BAAgC,CAAhC,+BAAgC,CAChC,0BACF,CAEA,gBACE,+BAAgC,CAChC,0BACF,CAEA,WAGE,+BAAgC,CAFhC,UAAW,CAGX,aAAc,CAFd,UAGF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,mCAEF,CAEA,WAGE,oBAAsC,CAAtC,qCAAsC,CACtC,qCAAsC,CAFtC,iBAAkB,CAIlB,WAAY,CALZ,YAAa,CAIb,uBAEF,CAEA,kBACE,0CACF,CAEA,gBACE,0CACF,CAEA,qBACE,gDAAiD,CACjD,UACF,CAEA,cAIE,0BAA2B,CAH3B,cAAe,CACf,eAAgB,CAChB,kBAEF,CAEA,aACE,+BAA+B,CAA/B,8BAA+B,CAC/B,kBACF,CAEA,eACE,yEAA8E,CAA9E,2EAA8E,CAE9E,iBAAkB,CAGlB,0BAA2B,CAD3B,eAAgB,CAIhB,OAAQ,CALR,iBAAkB,CAOlB,uBAAyB,CACzB,iBACF,CAEA,qBAEE,+BAAkD,CAAlD,iDAAkD,CADlD,0BAEF,CAEA,mBAGE,WAAY,CAFZ,gBAAiB,CACjB,UAEF,CAGA,gBACE,eAAyC,CAAzC,wCAAyC,CAKzC,qCAAsC,CAJtC,kBAAmB,CAGnB,2BAA4B,CAD5B,kBAAmB,CADnB,YAIF,CAEA,mBAKE,0BAA2B,CAH3B,cAAe,CACf,eAAgB,CAChB,kBAAmB,CAHnB,iBAKF,CAEA,oBAEE,+BAA+B,CAA/B,8BAA+B,CAC/B,kBAAmB,CAFnB,iBAGF,CAEA,aAGE,aAAS,CAAT,QAAS,CADT,mCAAqC,CAErC,kBACF,CAEA,YACE,eAAyC,CAMzC,qCAAsC,CALtC,kBAAmB,CAInB,eAAgB,CAHhB,YAAa,CAEb,iBAAkB,CADlB,uBAIF,CAEA,qBACE,yCACF,CAEA,oBACE,yCACF,CAEA,mBACE,4BAA6C,CAA7C,4CAA6C,CAC7C,UACF,CAEA,kBAEE,oBAA0C,CAA1C,yCAA0C,CAD1C,0BAEF,CAEA,cAGE,kBAAmB,CACnB,kBACF,CAEA,YAIE,kBAAmB,CAGnB,eAAyC,CAAzC,wCAAyC,CADzC,iBAAkB,CAElB,0BAA2B,CAL3B,YAAa,CADb,WAAY,CAGZ,sBAAuB,CAJvB,UAQF,CAEA,gBAGE,iBAAkB,CADlB,WAAY,CAEZ,gBAAiB,CAHjB,UAIF,CAEA,cAIE,iBAAkB,CAHlB,cAAe,CACf,eAAgB,CAChB,eAEF,CAEA,iBACE,oBAA4C,CAA5C,2CAA4C,CAC5C,wBAA2B,CAA3B,0BACF,CAEA,gBACE,oBAA4C,CAA5C,2CAA4C,CAC5C,yBAA2B,CAA3B,0BACF,CAEA,eACE,oBAA+C,CAA/C,8CAA+C,CAC/C,aAA8B,CAA9B,6BACF,CAEA,eAIE,0BAA2B,CAH3B,cAAe,CACf,eAAgB,CAChB,iBAEF,CAEA,YAEE,0BAA2B,CAD3B,cAAe,CAEf,kBACF,CAEA,mBAEE,+BAA+B,CAA/B,8BAA+B,CAD/B,cAAe,CAGf,eAAgB,CADhB,kBAEF,CAEA,oBACE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CACR,eACF,CAEA,aAGE,kBAAmB,CAEnB,iBAAkB,CAJlB,YAAa,CAKb,cAAe,CAJf,6BAA8B,CAE9B,gBAGF,CAEA,iBACE,oBAA4C,CAA5C,2CACF,CAEA,mBACE,oBAA+C,CAA/C,8CACF,CAEA,mBACE,eACF,CAEA,mBAUE,kBAAmB,CAJnB,oBAAwC,CAAxC,uCAAwC,CACxC,kBAAmB,CAEnB,YAAa,CAJb,WAAY,CAMZ,sBAAuB,CARvB,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAIX,SAIF,CAEA,wBACE,oBAAsC,CAAtC,qCAAsC,CAOtC,qCAAsC,CAHtC,iBAAkB,CAHlB,uBAAwB,CACxB,eAAgB,CAIhB,kBAAmB,CAHnB,gBAAiB,CAEjB,wBAGF,CAEA,YACE,YAAa,CACb,sBAAuB,CACvB,eACF,CAGA,oBACE,eAAyC,CAAzC,wCAAyC,CAIzC,qCAAsC,CAHtC,kBAAmB,CAEnB,2BAA4B,CAD5B,YAGF,CAEA,uBAIE,0BAA2B,CAH3B,cAAe,CACf,eAAgB,CAChB,kBAAmB,CAEnB,iBACF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,mCAEF,CAEA,cACE,eAAyC,CAAzC,wCAAyC,CAGzC,qCAAsC,CAFtC,kBAAmB,CACnB,YAAa,CAGb,iBAAkB,CADlB,uBAEF,CAEA,oBAEE,oBAA0C,CAA1C,yCAA0C,CAD1C,0BAEF,CAEA,cAKE,kBAAmB,CAEnB,0BAA2B,CAH3B,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CAHvB,kBAAmB,CAFnB,UAOF,CAEA,iBAIE,0BAA2B,CAH3B,cAAe,CACf,eAAgB,CAChB,kBAEF,CAEA,gBAEE,+BAA+B,CAA/B,8BAA+B,CAD/B,cAEF,CAGA,0BACE,eACE,mCACF,CACF,CAEA,yBACE,eACE,yBACF,CAEA,cACE,YACF,CAEA,4BAEE,yBACF,CAEA,gBACE,qBAAsB,CACtB,YACF,CAEA,cAEE,kBAAmB,CADnB,cAEF,CAEA,8BACE,kBAAmB,CACnB,iBACF,CACF,CAGA,0BACE,aACE,mCACF,CACF,CAEA,yBACE,aACE,yBACF,CAMA,4BACE,YACF,CACF,CAGA,WAEE,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAGZ,eACF,CAEA,mBACE,WAAY,CACZ,aACF,CASA,eACE,WAGF,CAYA,qBAEE,WAAY,CADZ,UAGF,CAeA,+CACE,YAAa,CACb,qBAAsB,CACtB,WAAY,CACZ,eACF,CAEA,oBACE,WAAY,CAEZ,YACF,CAwBA,mCAHE,iCAUF,CAPA,sBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,iBAGF,CAEA,8DAJE,wBAMF,CAEA,wBAIE,WAAY,CADZ,mBAAoB,CAFpB,QAAO,CAIP,cAAe,CAGf,WAAY,CANZ,iBAOF,CAEA,kEALE,qBAAuB,CACvB,UAOF,CAGA,cAEE,kBAAmB,CADnB,YAAa,CAEb,eACF,CAEA,oBAME,wBAAyB,CAHzB,iBAAkB,CADlB,WAAY,CAGZ,gBAAiB,CADjB,eAAgB,CAGhB,iBAAkB,CANlB,UAOF,CAEA,wBAEE,WAAY,CACZ,gBAAiB,CAFjB,UAGF,CAEA,mBAIE,kBAAmB,CAHnB,wBAAyB,CACzB,UAAW,CAIX,cAAe,CAHf,YAAa,CAEb,sBAEF,CAGA,gBAEE,wBAAyB,CADzB,YAEF,CAEA,kCACE,wBACF,CAEA,eAIE,0BAA2B,CAC3B,cAAe,CAHf,cAAe,CACf,eAAgB,CAFhB,iBAAkB,CAMlB,iBAAkB,CADlB,wBAEF,CAEA,qBACE,eACF,CAEA,sBACE,0BACF,CAEA,4BAOE,wBAAyB,CAJzB,QAAS,CAFT,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAGF,CAGA,gBAEE,+BAAsD,CAAtD,oDAAsD,CADtD,iBAEF,CAEA,kCACE,2BAAkD,CAAlD,gDACF,CAEA,eAGE,kBAAmB,CAGnB,iBAAkB,CAGlB,aAAiC,CAAjC,+BAAiC,CAFjC,cAAe,CALf,YAAa,CAMb,eAAgB,CAJhB,6BAA8B,CAC9B,iBAAkB,CAJlB,iBASF,CAEA,iCACE,aAAiC,CAAjC,+BACF,CAEA,eAKE,kBAAmB,CAFnB,aAAuC,CAAvC,qCAAuC,CACvC,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CALvB,UAMF,CAEA,eAKE,qBAAuB,CACvB,wBAA+C,CAA/C,6CAA+C,CAC/C,iBAAkB,CAClB,+BAAyC,CALzC,MAAO,CADP,oBAAqB,CAErB,UAAW,CAKX,YACF,CAEA,iCACE,qBAAwD,CAAxD,sDAAwD,CACxD,oBAA2C,CAA3C,yCAA2C,CAC3C,+BACF,CAEA,eACE,iBAAkB,CAElB,qCACF,CAEA,qBACE,0BAA0E,CAA1E,yDACF,CAEA,uCACE,0BAAmE,CAAnE,qDACF,CC7lCF,SACE,aAAc,CACd,YAAa,CACb,QCEF,CDCA,eAGE,2CAAkB,CAClB,cAAe,CAHf,gBAAiB,CACjB,mBCIF,CDCA,yBACE,kBCEF,CDCA,mCACE,QAAS,CACT,SCEF,CClBA,cAKE,mBAAoB,CAJpB,qBAAsB,CAEtB,YAAa,CACb,qBAAsB,CAFtB,WDwBF,CClBA,oBACE,kBDqBF,CClBA,YACE,WDqBF,CClBA,2DAGE,kBDqBF,CClBA,0BAME,QAAS,CAFT,MAAO,CAHP,eAAgB,CAChB,iBAAkB,CAGlB,OAAQ,CAFR,KDwBF,CClBA,kFACE,aAAc,CACd,eAAgB,CAChB,sBAAuB,CACvB,kBDqBF,CClBA,SACE,aDqBF,CClBA,eACE,UDqBF,CClBA,kBACE,kBDqBF,CClBA,YAWE,6BATA,QAAS,CAOT,aAAc,CADd,eAAiB,CAEjB,YAAa,CATb,eAAgB,CAIhB,aAAc,CACd,iBAAkB,CAHlB,sBAAuB,CAIvB,qBAAsB,CAHtB,kBD4BF,CCnBE,wBACE,0BDqBJ,CClBE,iCACE,mBAAoB,CACpB,2BDoBJ,CChBI,yDAGE,aAAc,CACd,oBDgBN,CCXA,iBAEE,eAAgB,CAGhB,WAAY,CAJZ,aAAc,CAKd,cAAe,CAHf,QAAS,CACT,SAAU,CAGV,yCDcF,CCXA,iBACE,iBAAkB,CAClB,gBAAiB,CACjB,wBAAyB,CACzB,SDcF,CCXA,4BACE,YAAa,CACb,qBAAsB,CACtB,WDcF,CCZE,8DAME,uBAAwB,CALxB,WAAY,CACZ,iBAAkB,CAKlB,oBDcJ,CCXI,iFACE,YDaN,CCRA,WACE,wBDWF,CElIA,aAME,cAAe,CAHf,sBAAuB,CAEvB,kBFsIF,CEnIE,gCACE,YACA,cAAe,CACf,iBFqIJ,CElIE,oBAME,eAAgB,CAChB,qBAAsB,CACtB,sBAEA,iBAAkB,CATlB,aCGQ,CDFR,oBAAqB,CASrB,kBAAmB,CARnB,QAAS,CAMT,qBALA,iBAAkB,CAClB,qBAAsB,CAOtB,kBFoIJ,CElII,0DAIE,wBAnCc,CAiCd,qBAAsB,CAGtB,oBAnCU,CAiCV,2CFqIN,CEjIM,4IAGE,yBACA,qBAFA,aFoIR,CExHI,oDAJE,wBAhDc,CAiDd,oBAhDU,CA8CV,aFwIN,CEnII,0BAEE,cFiIN,CE1HA,eACE,oBAAqB,CACrB,kBF6HF,CE3HE,mDAEE,4BAA6B,CAD7B,yBF8HJ,CE1HE,mDAEE,2BAA4B,CAD5B,wBF6HJ,CEzHE,4DACE,iBAAkB,CAElB,2BAA4B,CAD5B,wBF4HJ,CExHE,4DACE,iBAAkB,CAElB,4BAA6B,CAD7B,yBF2HJ,CEvHE,yDACE,eFyHJ,CEtHE,6BACE,gBFwHJ,CErHE,sCACE,aAAc,CACd,iBFuHJ,CEpHE,oDAEE,gBFqHJ,CEjHA,yBACE,aACE,qBFoHF,CACF,CIjOA,+CAME,wBDOS,CCZT,WAAY,CAMZ,iBDUoB,CCdpB,eAAgB,CADhB,qBAAsB,CAMtB,UDQY,CCPZ,cAAe,CALf,QAAS,CACT,eDac,CCPd,eAAgB,CADhB,UJoOF,CIjOE,+IACE,cAAe,CACf,mBJmOJ,CIhOE,yEACE,wBJkOJ,CI/NE,2DACE,wBJiOJ,CI7NA,iBAEE,aJ+NF,CI5NA,oBACE,mCJ+NF,CI5NA,2BAEE,2BAA4B,CAD5B,wBJgOF,CI7NA,2BAEE,4BAA6B,CAD7B,yBJiOF,CI5NA,6BACE,wBAAyB,CACzB,yBJ+NF,CI7NA,2BACE,2BAA4B,CAC5B,4BJgOF,CKpRA,SACE,YAAa,CACb,kBLuRF,CKpRA,iBACE,iBLuRF,CKhRA,mBACE,0BLkRF,CK/QA,eAEE,0BAA0C,CAM1C,aFZS,CEST,aAAc,CADd,eAAiB,CAEjB,WAAY,CACZ,kBAAmB,CAJnB,SLsRF,CKhRE,0CAEE,aLiRJ,CK7QA,gBAEE,sBACA,YAAa,CAEb,QAAS,CADT,qBAAsB,CAMtB,WAAY,CATZ,iBAAkB,CAMlB,gBAAiB,CACjB,wBAAyB,CAFzB,ULmRF,CK5QA,kBACE,YAAa,CACb,kBL+QF,CK5QA,eACE,YAAa,CAGb,QAAS,CACT,cAAe,CAFf,qBAAsB,CAKtB,WAAY,CAFZ,eAAgB,CAJhB,iBLoRF,CK5QE,8BACE,yBL8QJ,CK1QA,eACE,QAAS,CACT,WAAY,CACZ,iBAAkB,CAClB,gBL6QF,CK3QE,uBACE,eL6QJ,CKzQI,kEAGE,aAAc,CACd,oBLyQN,CKpQA,YAEE,YAAa,CAEb,QAAS,CADT,kBAAmB,CAEnB,eAAgB,CAChB,SLsQF,CKnQA,YACE,QLsQF,CKpQE,wBACE,0BLsQJ,CKnQE,iCACE,mBAAoB,CACpB,2BLqQJ,CKjQA,aAIE,qBAAsB,CADtB,wBAAyB,CAEzB,+BAA0C,CAC1C,YAAa,CALb,iBAAkB,CAClB,SLwQF,CKlQE,iBACE,cLoQJ,CKhQA,oBACE,+BAAgC,CAChC,sBAA6B,CAC7B,gBLmQF,CM9XA,iBACE,YAAa,CAEb,QAAS,CADT,qBAAsB,CAEtB,aNiYF,CM/XE,wCAEE,sBAEA,wBAAyB,CADzB,gBAAiB,CAFjB,UNoYJ,CM/XI,oDACE,gBAAiB,CACjB,kBNiYN,CM9XI,8DACE,iBAAkB,CAClB,kBAAmB,CACnB,wBNgYN,CM7XI,uDACE,0BN+XN,CM3XM,gEACE,mBAAoB,CACpB,2BN6XR,CMzXI,oDACE,yBN2XN,CMxXI,oDAGE,6BAFA,eAAgB,CAChB,eN2XN,CMxXM,6DACE,gBN0XR,CMpXA,sBACE,wBNuXF,CMrXE,iDACE,YNuXJ,CMrXE,kDACE,YNuXJ,CMnXA,4CAEE,kBNsXF,CMjXA,uBACE,UNoXF,COzbA,iBACE,YAAa,CACb,qBAAsB,CACtB,eP4bF,CO1bE,qCACE,QP4bJ,COvbA,oBACE,6BAEA,YAAa,CACb,uBAAwB,CAFxB,eP4bF,COvbA,oCAEE,SP0bF,COvbA,WACE,aP0bF,COvbA,cACE,iBP0bF,COxbE,oCACE,QAAS,CACT,MAAO,CAGP,iBAAkB,CAFlB,iBAAkB,CAClB,OAAQ,CAER,KP0bJ,COxbI,4CACE,SAAU,CACV,OP0bN,COtbE,6DAME,sBAAuB,CALvB,yBACA,YAAa,CAGb,qBAAsB,CAFtB,eAAgB,CAChB,eAAgB,CAGhB,eAAgB,CAChB,iBPwbJ,COrbE,oCAEE,WPsbJ,COnbE,+BACE,SAAU,CACV,iBAAkB,CAClB,UPqbJ,COlbE,iCAGE,oBAAqB,CADrB,QAAS,CAGT,WAAY,CADZ,aAAc,CAEd,cAAe,CALf,UPybJ,COjbE,6BACE,4BPmbJ,CO9aE,2FAIE,qBAAuB,CACvB,4BAFA,MAAO,CAIP,iBAAkB,CALlB,eAAgB,CAIhB,UPkbJ,CO9aE,0CACE,ePgbJ,CO7aE,kDAEE,QAAS,CACT,cAAe,CAFf,cPibJ,CO5aE,0DACE,YP8aJ,CO3aE,uCACE,eP6aJ,CO1aE,0EAIE,SACA,eAAgB,CAHhB,WP8aJ,COvaA,kDACE,gBP0aF,COvaA,eACE,QP0aF,COxaE,uBACE,eP0aJ,COtaA,gBACE,iBPyaF,CQ3iBA,oBAGE,0BLMwB,CKLxB,ULIqB,CKHrB,aAAc,CAEd,WAAY,CALZ,iBAAkB,CAIlB,UAAW,CALX,URojBF,CQ3iBA,oBACE,WR8iBF,CQ3iBA,eAKE,sBAJA,YAAa,CAEb,SADA,qBAAsB,CAItB,YAAa,CAFb,URgjBF,CQ5iBE,gCAEE,gBAAiB,CADjB,kBR+iBJ,CQ3iBE,gCACE,kBAAuB,CAEvB,WAAY,CACZ,iBAAkB,CAFlB,UR+iBJ,CQ3iBE,iDACE,0BR6iBJ,CQ1iBE,kCACE,iBAAkB,CAClB,SR4iBJ,CQziBE,wBACE,qBAAsB,CACtB,eR2iBJ,CQviBA,iBACE,YAAa,CACb,aAAc,CACd,kBR0iBF,CQxiBE,iCACE,2BR0iBJ,CQviBE,0CAEE,2BADA,oBR0iBJ,CQliBE,iFACE,4BRuiBJ,CQ3hBA,iCACE,YR8hBF,CQ3hBA,yBAKE,2BAHA,YAAa,CADb,SAGA,qBAAsB,CADtB,WRgiBF,CQ5hBE,kCACE,mBAAoB,CACpB,2BR8hBJ,CQ3hBE,mDACE,6BACA,aR6hBJ,CQzhBA,kBAGE,sBAAuB,CAEvB,0BAJA,YAAa,CACb,QAAS,CAIT,eAAgB,CAChB,iBAAkB,CAHlB,UR+hBF,CQ1hBE,mCACE,SR4hBJ,CQzhBE,wBACE,0BR2hBJ,CQxhBE,iCACE,mBAAoB,CACpB,2BR0hBJ,CQvhBE,gCAEE,gBAAiB,CACjB,wBAAyB,CAFzB,UR2hBJ,CQrhBA,4BAOE,wBLjHmB,CK+GnB,UAAW,CAFX,MAAO,CAKP,mBAAoB,CAPpB,iBAAkB,CAGlB,OAAQ,CAFR,SR6hBF,CQnhBE,+CACE,YAAa,CACb,qBRshBJ,CQnhBE,4CACE,WRqhBJ,CStqBA,0BACE,kBACF,CACA,eACI,aAA4C,CAA5C,0CACJ,CAEA,kBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,oBACF,CACA,aACI,wCACJ,CAEA,iBACE,kBAAmB,CACnB,eAAgB,CAChB,QACF,CAEA,kBAEE,kBAAmB,CAEnB,wBAAuD,CAAvD,qDAAuD,CAEvD,WAAY,CACZ,qBAAuB,CAFvB,UAAY,CAMZ,cAAe,CAVf,YAAa,CAQb,iBAAmB,CACnB,eAAgB,CAPhB,SAAW,CAKX,kBAAoB,CAIpB,+BACF,CAEA,wBACE,wBAAyD,CAAzD,uDACF,CAGA,iCAUE,wBAAqD,CAArD,mDAAqD,CACrD,mBAAqB,CARrB,qBAAsB,CAGtB,QAAS,CACT,cAAe,CAKf,iBACF,CAEA,uDAVE,kBAAmB,CAGnB,aAAuC,CAAvC,qCAAuC,CANvC,YAAa,CAOb,iBAAmB,CALnB,sBAmBF,CARA,sBAOE,iBAAkB,CAHlB,WAIF,CAGA,yBAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,+BAEF,CAGA,iBACE,qBAA6C,CAA7C,0CAA6C,CAE7C,wBAA8C,CAA9C,4CAA8C,CAD9C,mBAAqB,CAGrB,gBAAiB,CADjB,eAEF,CAGA,eACE,YAAa,CACb,SACF,CAGA,aAGE,kBAAmB,CAFnB,YAAa,CAIb,cAAe,CACf,UAAY,CAJZ,6BAA8B,CAE9B,oBAAsB,CAGtB,kBACF,CAEA,mBACE,cAAe,CACf,eAAgB,CAEhB,oBAAsB,CADtB,wBAEF,CAEA,kBACE,YAAa,CACb,SACF,CAGA,YACE,kCAA6D,CAA7D,0DAA6D,CAC7D,oBACF,CAEA,WACE,yDACF,CAEA,WACE,kCAAkE,CAAlE,+DAAkE,CAClE,8BAAgE,CAAhE,6DACF,CAEA,cACE,kCAAgE,CAAhE,6DACF,CAEA,yBACE,uCAAqE,CAArE,kEACF,CAEA,sBACE,gCACF,CAEA,iCACE,kCAAkE,CAAlE,+DAAkE,CAElE,8BAAgE,CAAhE,6DAAgE,CADhE,oBAEF,CAEA,4BACE,kCAA2D,CAA3D,wDAA2D,CAC3D,uBAAyD,CAAzD,sDACF,CAGA,uBAGE,kBAAmB,CAMnB,aAAuC,CAAvC,qCAAuC,CARvC,YAAa,CACb,qBAAsB,CAGtB,QAAS,CAGT,WAAY,CAJZ,sBAAuB,CAEvB,iBAAkB,CAClB,iBAGF,CAEA,yBAEE,iBAAmB,CADnB,QAEF,CAEA,0BAGE,kBAAmB,CAGnB,aAAuC,CAAvC,qCAAuC,CALvC,YAAa,CAIb,WAAY,CAHZ,sBAAuB,CAEvB,YAGF,CAGA,yBAKE,yCACE,yBACF,CAEA,aAEE,sBAAuB,CADvB,qBAEF,CAEA,mBACE,mBACF,CAOA,iCAHE,mBAAqB,CADrB,UAQF,CAJA,kBAGE,6BACF,CAEA,kBAEE,sBAAuB,CADvB,UAEF,CAEA,yBACE,WAAY,CACZ,gBACF,CACF,CAGA,yBACE,qBAA6C,CAA7C,0CAA6C,CAE7C,wBAA8C,CAA9C,4CAA8C,CAD9C,mBAAqB,CAIrB,YAAa,CACb,qBAAsB,CAFtB,gBAAiB,CADjB,eAIF,CAEA,kBAIE,YAAa,CACb,qBAAsB,CAHtB,WAAY,CACZ,eAAgB,CAFhB,eAKF,CAEA,iBACE,qBACF,CAEA,gBACE,iBAAkB,CAClB,eAAgB,CAChB,iBACF,CAEA,0BACE,cAAe,CAEf,oBAAqB,CADrB,oBAEF,CAEA,gCAEE,aAA4C,CAA5C,0CAA4C,CAD5C,yBAEF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAGb,cAAe,CADf,SAEF,CAEA,cAEE,kBAAmB,CAKnB,wBAAqD,CAArD,mDAAqD,CAHrD,oBAAqB,CAIrB,aAAuC,CAAvC,qCAAuC,CAPvC,mBAAoB,CAIpB,gBAAkB,CAFlB,oBAMF,CAEA,qBACE,wBAAqD,CAArD,mDAAqD,CACrD,UACF,CAEA,wBACE,wBAAgD,CAAhD,8CAAgD,CAChD,aAA8C,CAA9C,4CACF,CAEA,oBACE,wBAAqD,CAArD,mDACF,CAEA,wBACE,wBAAuD,CAAvD,qDAAuD,CACvD,UACF,CAEA,eAEE,kBAAmB,CAEnB,oBAAqB,CAHrB,mBAAoB,CAIpB,gBAAkB,CAClB,eAAgB,CAHhB,oBAIF,CAEA,qBACE,0BACF,CAEA,mBACE,0BACF,CAEA,oBACE,0BACF,CAEA,0BACE,0BACF,CAEA,gBAGE,aAAS,CAGT,wBAAqD,CAArD,mDAAqD,CACrD,mBAAqB,CANrB,YAAa,CAEb,QAAS,CADT,6BAA8B,CAE9B,qBAAsB,CACtB,YAGF,CAEA,WACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,YAEE,aAAuC,CAAvC,qCAAuC,CADvC,gBAEF,CAEA,YACE,eACF,CAEA,sBACE,qBACF,CAEA,yBACE,iBAAmB,CACnB,eAAgB,CAChB,gBACF,CAEA,wBAGE,aAA2C,CAA3C,yCAA2C,CAF3C,iBAAmB,CACnB,eAAgB,CAEhB,QACF,CAEA,eACE,YAAa,CACb,cAAe,CACf,SAAW,CACX,qBACF,CAEA,KAGE,wBAAgD,CAAhD,8CAAgD,CAEhD,oBAAsB,CADtB,aAA4C,CAA5C,0CAA4C,CAH5C,mBAAoB,CAKpB,gBAAkB,CAJlB,oBAKF,CAEA,gBAGE,eAAY,CAEZ,wBAAqD,CAArD,mDAAqD,CACrD,mBAAqB,CALrB,YAAa,CAEb,UAAY,CADZ,mCAAqC,CAErC,qBAAsB,CAGtB,YACF,CAEA,WACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,YAEE,aAAuC,CAAvC,qCAAuC,CADvC,gBAEF,CAEA,YAEE,iBAAmB,CADnB,eAEF,CAEA,kBACE,YAAa,CACb,UAAY,CACZ,iBACF,CAEA,6BACE,QACF,CAEA,eAKE,qBAAuB,CACvB,iBAAmB,CACnB,eAAgB,CAHhB,kBAMF,CAEA,uBACE,wBAAuD,CAAvD,qDAAuD,CAEvD,WAAY,CADZ,UAEF,CAEA,6BACE,wBAAyD,CAAzD,uDACF,CAEA,yBACE,wBAA6B,CAE7B,wBAAuD,CAAvD,qDAAuD,CADvD,aAA4C,CAA5C,0CAEF,CAEA,+BACE,wBAAgD,CAAhD,8CACF,CAGA,wBAEE,4BAA8B,CAG9B,kCAAkE,CAAlE,+DAAkE,CAJlE,6BAA+B,CAG/B,iBAAmB,CADnB,gCAGF,CAEA,8BACE,kCAAoE,CAApE,iEACF,CAEA,0EAGE,4BAA8B,CAD9B,6BAEF,CCrdA,yBAEE,aAAc,CADd,UAEF,CAGA,qBAGE,aAA4C,CAA5C,0CAA4C,CAC5C,eAAiB,CAFjB,eAAiB,CAIjB,oBAAsB,CADtB,mBAAqB,CAJrB,iBAMF,CAGA,oBACE,YAAa,CACb,cAAe,CACf,UAAY,CAEZ,sBAAuB,CADvB,oBAEF,CAEA,iBAKE,wBAAgD,CAAhD,8CAAgD,CADhD,WAAY,CADZ,oBAAqB,CAGrB,aAA8C,CAA9C,4CAA8C,CAC9C,cAAe,CALf,eAAiB,CAOjB,eAAgB,CARhB,qBAAwB,CAOxB,uBAEF,CAEA,uBACE,wBAAqD,CAArD,mDAAqD,CACrD,UACF,CAEA,wBACE,wBAAuD,CAAvD,qDAAuD,CACvD,UACF,CAGA,sBASE,uBAAwB,CAFxB,qBAAuB,CAFvB,wBAA8C,CAA9C,4CAA8C,CAD9C,iBAAkB,CAElB,mBAAqB,CAHrB,gBAAiB,CAFjB,eAAgB,CAChB,eAAgB,CAMhB,oBAEF,CAEA,yCAEE,UAAW,CADX,SAEF,CAEA,+CACE,oBAA+B,CAC/B,iBACF,CAEA,+CACE,oBAA+B,CAC/B,iBACF,CAEA,qDACE,oBACF,CAGA,eAEE,wBAAyB,CACzB,iBAAmB,CAFnB,UAGF,CAGA,qBACE,eAAgB,CAChB,KAAM,CACN,SACF,CAEA,kBACE,wBAAuD,CAAvD,qDAAuD,CACvD,UAAY,CAGZ,eAAgB,CADhB,mBAAqB,CADrB,eAAgB,CAGhB,kBACF,CAGA,kBAEE,+BAAqD,CAArD,mDAAqD,CADrD,mBAEF,CAEA,kBAEE,cAAe,CADf,qCAEF,CAEA,wBACE,wBAAgD,CAAhD,8CACF,CAEA,gCACE,kBACF,CAEA,gCACE,wBAAgD,CAAhD,8CAAgD,CAChD,UACF,CAGA,cACE,eACF,CAEA,qBAEE,aAAuC,CAAvC,qCAAuC,CADvC,gBAAkB,CAKlB,eAAgB,CAFhB,eAAgB,CAChB,sBAAuB,CAFvB,kBAIF,CAGA,cAEE,eAAgB,CADhB,gBAEF,CACA,aACE,cACF,CACA,aACE,eACF,CAEA,eAGE,4BAAkC,CADlC,aAA0C,CAA1C,wCAA0C,CAC1C,iCAAkC,CAFlC,eAAgB,CAGhB,oBACF,CAEA,wBAIE,4BAAkC,CAHlC,gBAAkB,CAGlB,iCAAkC,CADlC,4BAEF,CAGA,6CANE,aAAuC,CAAvC,qCAWF,CALA,qBAIE,iBAAmB,CAFnB,YAAa,CADb,iBAIF,CAGA,uCACE,aAA0C,CAA1C,wCACF,CAEA,mCACE,0BAAmE,CAAnE,qDAAmE,CACnE,aAAuC,CAAvC,qCACF,CAEA,yCACE,wBAAqD,CAArD,mDAAqD,CACrD,UACF,CAEA,wCACE,wBAAwD,CAAxD,sDAAwD,CACxD,oBAA2C,CAA3C,yCACF,CAEA,oCACE,2BAAkD,CAAlD,gDACF,CAEA,kDACE,0BAAmE,CAAnE,qDACF,CAEA,0CACE,0BAAmE,CAAnE,qDACF,CAGA,yBACE,sBACE,gBACF,CAEA,oCAGE,eAAiB,CADjB,oBAEF,CAEA,qBACE,eACF,CAEA,iBAEE,gBAAkB,CADlB,mBAEF,CACF,CC9NA,UAGE,wCAAyC,CACzC,gCAAiC,CACjC,4BAAoD,CAApD,iDAAoD,CAEpD,iBACF,CAGA,wBAVE,YAAa,CACb,WAAY,CAIZ,eAiBF,CAZA,cAIE,+CAAgD,CAChD,2CAA4C,CAM5C,8CAA+C,CAT/C,qBAAsB,CAKtB,iBAAkB,CADlB,4CAAgD,CAHhD,WAAY,CAMZ,UAGF,CAEA,yBAME,oBACE,YACF,CACF,CAEA,qBAEE,2BAA4B,CAD5B,OAEF,CAEA,mBACE,uBAAwB,CACxB,WACF,CAEA,gBAGE,kBAAmB,CAEnB,4CAA6C,CAJ7C,YAAa,CACb,6BAA8B,CAE9B,YAEF,CAEA,mBAIE,QAAO,CAFP,cAAe,CACf,eAAgB,CAFhB,QAAS,CAIT,iBACF,CAEA,sBAOE,kBAAmB,CANnB,gBAAuB,CACvB,WAAY,CACZ,+BAAgC,CAMhC,cAAe,CAHf,YAAa,CADb,WAAY,CAGZ,sBAAuB,CAGvB,gBAAiB,CADjB,oBAAsB,CANtB,UAQF,CAEA,4BACE,gCACF,CAEA,iBAQE,kBAAmB,CAPnB,qCAAsC,CAEtC,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAQZ,cAAe,CAHf,YAAa,CADb,WAAY,CAGZ,sBAAuB,CAEvB,+BAAiC,CANjC,UAOF,CAEA,uBACE,wCACF,CAEA,WACE,QAAO,CACP,eAAgB,CAChB,WACF,CAEA,WAGE,kBAAmB,CAEnB,iBAAkB,CAElB,cAAe,CANf,YAAa,CACb,6BAA8B,CAI9B,iBAAkB,CAFlB,YAAa,CAIb,+BACF,CAEA,iBACE,6CACF,CAEA,kBACE,0BAAyC,CACzC,gCACF,CAEA,mBACE,QAAO,CACP,eACF,CAEA,WACE,eAAgB,CAChB,iBAAkB,CAElB,eAAgB,CAChB,sBAAuB,CAFvB,kBAGF,CAEA,WACE,cAEF,CAEA,6BAHE,+BAYF,CATA,kBACE,gBAAuB,CACvB,WAAY,CAIZ,cAAe,CADf,cAAe,CAEf,UAAY,CAHZ,WAAY,CAIZ,sBACF,CAEA,wBACE,SACF,CAGA,WAQE,wCAAyC,CAJzC,YAAa,CAHb,QAAO,CAIP,qBAAsB,CAHtB,aAAc,CAId,eAAgB,CAChB,iBAAkB,CAElB,+BAAiC,CANjC,UAOF,CAGA,aAGE,4CAEF,CAEA,0BALE,kBAAmB,CADnB,YAgBF,CAVA,aACE,gBAAuB,CACvB,WAAY,CAOZ,gCAAiC,CAJjC,cAAe,CAFf,cAAe,CAKf,sBAAuB,CAJvB,iBAMF,CAEA,mBACE,gCACF,CAEA,YAEE,cAAe,CACf,eAAgB,CAFhB,QAAS,CAGT,eAAgB,CAChB,sBAAuB,CACvB,kBACF,CAEA,eACE,QAAO,CAEP,eAAgB,CADhB,YAIF,CAGA,+BALE,YAAa,CACb,qBAWF,CAPA,gBAGE,kBAAmB,CAEnB,WAAY,CADZ,sBAAuB,CAEvB,iBACF,CAEA,cACE,kBACF,CAEA,kBACE,WAAY,CACZ,kBAAmB,CACnB,gBACF,CAEA,eAIE,gCAAiC,CAHjC,cAAe,CACf,eAAgB,CAChB,cAEF,CAEA,kBAEE,+BAAgC,CADhC,cAAe,CAEf,QAAS,CACT,eACF,CAEA,eAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAG3D,eAAgB,CADhB,cAEF,CAEA,gBAGE,sBAAuB,CAIvB,eAAiB,CAFjB,wBAAyB,CACzB,kBAAmB,CAEnB,8BAAyC,CACzC,cAAe,CARf,YAAa,CACb,qBAAsB,CAWtB,OAAQ,CADR,eAAgB,CARhB,iBAAkB,CAOlB,eAAgB,CADhB,uBAIF,CAEA,sBAGE,oBAAqB,CADrB,+BAA0C,CAD1C,0BAGF,CAEA,wBAGE,yCAA0C,CAD1C,cAAe,CADf,UAGF,CAEA,6BAGE,aAAc,CAFd,cAAe,CAGf,eAAgB,CAFhB,eAGF,CAEA,8BAEE,aAAc,CADd,cAAe,CAEf,eAAgB,CAEhB,cAAe,CADf,UAEF,CAGA,SACE,YAAa,CACb,qBAAsB,CACtB,kBAAmB,CACnB,aACF,CAEA,cACE,mBACF,CAEA,mBACE,qBACF,CAEA,mBACE,YAAa,CACb,iBACF,CAEA,iCACE,0BACF,CAEA,gBAQE,aAAc,CANd,WAAY,CAEZ,YAAa,CAHb,UAQF,CAEA,oCALE,kBAAmB,CAHnB,iBAAkB,CAElB,YAAa,CAEb,sBAeF,CAXA,oBAME,2CAA4C,CAC5C,UAAY,CAGZ,cAAe,CAFf,eAAgB,CANhB,WAAY,CADZ,UAUF,CAEA,iCACE,oCACF,CAEA,gBAEE,kBAAmB,CACnB,2BAA4B,CAG5B,qBAAsB,CADtB,eAAgB,CADhB,wBAAyB,CAHzB,iBAMF,CAEA,8BACE,2CAA4C,CAE5C,2BAA4B,CAD5B,UAGF,CAEA,mCACE,+CAAgD,CAEhD,0BAA2B,CAD3B,gCAEF,CACA,oBACI,aACJ,CAEA,yBACE,cAAe,CACf,eACF,CAEA,2BACE,eACF,CAEA,sCACE,eACF,CAEA,cAEE,kBAAmB,CAInB,+BAAgC,CALhC,YAAa,CAIb,cAAe,CAFf,wBAAyB,CACzB,cAGF,CAEA,4BACE,eACF,CAEA,cACE,eACF,CAGA,kBAEE,kBAAmB,CADnB,YAAa,CAIb,OAAQ,CAFR,sBAAuB,CACvB,aAEF,CAEA,uBAKE,mDAAoD,CAFpD,0CAA2C,CAC3C,iBAAkB,CAFlB,UAAW,CADX,SAKF,CAEA,mCACE,qBACF,CAEA,oCACE,qBACF,CAEA,sBACE,UAAuC,UAAY,CAAnC,mBAAqC,CACrD,IAA6B,SAAU,CAAjC,kBAAmC,CAC3C,CAEA,eAME,2BAA4B,CAF5B,2CAA4C,CAH5C,oBAAqB,CAErB,WAAY,CAEZ,eAAgB,CAEhB,qBAAsB,CALtB,SAMF,CAEA,iBACE,MAAW,SAAY,CACvB,IAAM,SAAY,CACpB,CAGA,iBAEE,kBAAmB,CADnB,YAAa,CAGb,WAAY,CADZ,sBAAuB,CAEvB,UACF,CAEA,SAME,sCAAuC,CADvC,0BAA4C,CAA5C,+CAA4C,CAH5C,WAAY,CADZ,UAMF,CAOA,oBAGE,wCAAyC,CADzC,yCAA0C,CAD1C,YAGF,CAEA,iBAEE,kBAAmB,CACnB,+CAAgD,CAChD,qCAAsC,CACtC,kBAAmB,CAJnB,YAAa,CAKb,oBAAqB,CACrB,2BACF,CAEA,8BACE,uCACF,CAEA,eAGE,gBAAuB,CADvB,WAAY,CAIZ,gCAAiC,CALjC,QAAO,CAIP,cAAe,CAIf,eAAgB,CAFhB,YAAa,CAHb,cAAe,CAIf,WAEF,CAEA,4BACE,+BACF,CAEA,aAIE,kBAAmB,CAEnB,2CAA4C,CAE5C,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CAPf,YAAa,CADb,WAAY,CAGZ,sBAAuB,CAOvB,eAAgB,CADhB,+BAAiC,CAVjC,UAYF,CAEA,mBACE,wCACF,CAEA,sBACE,kCAAmC,CACnC,kBACF,CAGA,cAUE,4BAA8B,CAR9B,qCAAsC,CACtC,qCAAsC,CACtC,iBAAkB,CAClB,yCAA0C,CAE1C,eAAgB,CANhB,cAAe,CAQf,yBAA0B,CAD1B,WAAY,CAFZ,WAKF,CAEA,iBACE,GAA+B,SAAU,CAAlC,oBAAoC,CAC3C,GAA0B,SAAU,CAA/B,kBAAiC,CACxC,CAEA,mBAEE,kBAAmB,CAGnB,eAAgB,CAChB,WAAY,CAGZ,gCAAiC,CACjC,cAAe,CATf,YAAa,CAOb,cAAe,CAJf,iBAAkB,CAGlB,eAAgB,CAIhB,+BAAiC,CARjC,UASF,CAEA,yBACE,6CACF,CAEA,gCACE,sCAAuC,CACvC,sBACF,CAEA,uBAGE,WAAY,CAFZ,gBAAiB,CACjB,UAEF,CAGA,iBACE,YAAa,CACb,wBAAyB,CACzB,cAAe,CACf,cACF,CAEA,eAUE,kBAAmB,CATnB,gBAAuB,CACvB,WAAY,CAKZ,iBAAkB,CAJlB,+BAAgC,CAGhC,cAAe,CAGf,YAAa,CAEb,sBAAuB,CANvB,eAAgB,CADhB,WAAY,CAIZ,kBAIF,CAEA,qBAEE,6CAA8C,CAD9C,gCAEF,CAMA,+CACE,gCACF,CAGA,oBACE,YAAa,CACb,cAAe,CACf,OAAQ,CACR,eACF,CAEA,mBACE,qCAAsC,CACtC,qCAAsC,CACtC,kBAAmB,CAGnB,gCAAiC,CACjC,cAAe,CAFf,cAAe,CADf,gBAAiB,CAIjB,kBACF,CAEA,yBACE,6CAA8C,CAC9C,uCACF,CAGA,gBACE,YAAa,CACb,cAAe,CACf,OAAQ,CACR,cACF,CAEA,eAEE,iBAAkB,CAElB,cAAe,CADf,eAAgB,CAFhB,iBAIF,CAEA,mBAGE,iBAAkB,CADlB,aAAc,CADd,cAGF,CAEA,cAKE,0BAAoC,CAHpC,QAAS,CAIT,UAAY,CAEZ,cAAe,CALf,MAAO,CAIP,WAAY,CANZ,iBAAkB,CAGlB,OAKF,CAGA,cAOE,wCAAyC,CAJzC,qCAAsC,CACtC,iBAAkB,CAIlB,gCAAiC,CAFjC,cAAe,CADf,kBAAmB,CAHnB,YAAa,CADb,UAQF,CAGA,yBAKE,oBACE,cACJ,CAEE,eACE,qBAAsB,CACtB,cACF,CAEA,SACE,aACF,CAEA,eACE,cACF,CAEA,kBACE,cACF,CAEA,gBACE,YACF,CAEA,mBACE,cACF,CAEA,sBACE,gBACF,CACF,CAGA,2BACE,sBAAsC,CACtC,2CACF,CAEA,8CACE,+BACF,CAGA,6BACE,mCAAoC,CACpC,iBAAkB,CAGlB,YAAa,CADb,eAAgB,CADhB,YAGF,CAEA,8BAGE,wBAAyB,CAFzB,8CAAwD,CACxD,cAEF,CAEA,2BACE,gCAAiC,CACjC,oBACF,CAEA,iCACE,yBACF,CAGA,yBACE,wBACE,iBACF,CACF,CAGA,oBACE,aAAc,CACd,UACF,CAEA,iBACE,eAAgB,CAChB,gBAAiB,CACjB,eACF,CAEA,4BAEE,iBAAkB,CAElB,cAAe,CADf,iBAAkB,CAFlB,YAAa,CAIb,+BACF,CAEA,kCACE,6CACF,CAGA,wBACE,iBACF,CAEA,yBACE,wBACE,aACF,CACF,CAGA,aACE,iBACF,CAGA,+BAGE,YAAa,CACb,qBAAsB,CAFtB,WAAY,CADZ,UAIF,CA4BA,yBACE,uBACE,gBACF,CAEA,wCAEE,aACF,CAGA,+BAGE,2BAA4B,CAC5B,oBAAqB,CAFrB,mBAAoB,CAKpB,eAAgB,CANhB,eAAgB,CAKhB,sBAAuB,CADvB,kBAGF,CACF,CAGA,uBAIE,kBAAmB,CAHnB,YAAa,CACb,WAAY,CAGZ,sBAAuB,CACvB,YAAa,CAHb,UAIF,CAEA,qBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAGtB,eAAgB,CADhB,iBAEF,CAEA,qBAEE,cAAe,CADf,eAAgB,CAEhB,mBACF,CAEA,sBACE,iBAAmB,CACnB,oBACF,CAGA,0BACE,YAAa,CAEb,QAAO,CADP,qBAEF,CAGA,gBAGE,0BAA2B,CAF3B,cAAe,CAGf,eAAgB,CAFhB,iBAGF,CAEA,2BACE,gCACF,CAEA,YAEE,+BAAgC,CAEhC,eAAmB,CADnB,eAEF,CAGA,qBAGE,0BAA2B,CAF3B,cAAe,CAGf,eAAgB,CAFhB,iBAGF,CAEA,gCACE,gCACF,CAEA,iBAEE,+BAAgC,CADhC,cAAe,CAEf,eAEF,CA0DA,yBACE,6BACE,cACF,CAEA,8BACE,cACF,CAEA,kCACE,cACF,CAEA,mCACE,aACF,CACF,CAYA,qDAKE,aAAc,CACd,iBAAkB,CAHlB,gBAAmB,CACnB,YAAa,CAFb,iBAKF,CAGA,sBAEE,aAAc,CADd,gBAAkB,CAElB,eAAmB,CACnB,eACF,CAiBA,2BAEE,kBAAmB,CAMnB,aAAc,CAPd,YAAa,CAEb,cAAe,CAGf,iBAAmB,CACnB,eAAgB,CAHhB,OAAQ,CACR,iBAIF,CCviCA,oBAGE,+BAAgC,CAIhC,sBAAuB,CALvB,aAAc,CAId,iBAAkB,CADlB,2BAA6B,CAJ7B,UAOF,CAEA,8BAGE,kBAAmB,CAFnB,YAAa,CAGb,gBAAiB,CAFjB,SAGF,CAEA,iBACE,YAAa,CACb,wBAAyB,CACzB,cAAe,CACf,iBAAkB,CAClB,OAAQ,CACR,KAAM,CACN,UACF,CAEA,+CAME,iCAA0B,CAA1B,yBAA0B,CAF1B,iBAAkB,CAClB,WAAY,CAJZ,cAAe,CACf,UAAW,CACX,QAIF,CAEA,qBAME,kBAAmB,CALnB,gBAAuB,CACvB,WAAY,CAOZ,aAAc,CADd,cAAe,CAHf,YAAa,CAKb,cAAe,CANf,WAAY,CAGZ,sBAAuB,CAKvB,QAAS,CACT,UAAY,CAFZ,SAAU,CAGV,sBAAwB,CAXxB,UAYF,CAEA,2BACE,SACF,CAEA,sBAME,uBAAwB,CALxB,YAAa,CAEb,OAAQ,CAKR,WAAY,CANZ,eAAgB,CAEhB,cAAe,CAGf,+BAA+C,CAF/C,oBAIF,CAEA,yCACE,UACF,CAEA,+CACE,gBACF,CAEA,+CACE,iBACF,CAEA,cAUE,wBAAyB,CANzB,kBAAmB,CAEnB,8BAAyC,CACzC,YAAa,CANb,aAAc,CAOd,qBAAsB,CALtB,eAAgB,CADhB,eAAgB,CAGhB,gBAAiB,CAIjB,iBAEF,CAEA,gBAGE,iBACF,CAEA,8BAJE,kBAAmB,CADnB,YAcF,CATA,cAGE,iBAAkB,CAKlB,cAAe,CANf,WAAY,CAIZ,sBAAuB,CACvB,gBAAiB,CANjB,UAQF,CAEA,0BACE,wBAAyB,CACzB,aACF,CAEA,uBACE,wBAAyB,CACzB,aACF,CAEA,qBACE,wBAAyB,CACzB,aACF,CAEA,0BACE,wBAAyB,CACzB,aACF,CAEA,oBACE,wBAAyB,CACzB,UACF,CAEA,mBACE,cAAe,CACf,eAAgB,CAChB,QACF,CAEA,iBAGE,aAAc,CAEd,WAAY,CAJZ,cAAe,CACf,eAAgB,CAEhB,eAEF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,eACF,CAEA,oBACE,YAAa,CACb,OACF,CAEA,WAGE,wBAAyB,CACzB,iBAAkB,CAFlB,UAAW,CADX,SAIF,CAEA,kBACE,wBAAyB,CACzB,UACF,CAEA,gBAEE,eAAgB,CAChB,WAAY,CACZ,aAAc,CAGd,cAAe,CAFf,cAAe,CACf,eAAgB,CALhB,SAAU,CAOV,oBACF,CAEA,sBACE,yBACF,CC1LA,WACE,YAAa,CACb,qBAAsB,CACtB,WAAY,CAEZ,eAAgB,CADhB,UAEF,CAGA,cAKE,eAAgB,CADhB,YAEF,CAGA,mCANE,YAAa,CAFb,QAAO,CACP,eAYF,CALA,qBAEE,UAGF,CAGA,oBAGE,iCAA2C,CAF3C,aAAc,CACd,iBAEF,CAGA,oCACE,cACE,wBACF,CACF,CCpCA,cACE,wCAAyC,CACzC,oCAAqC,CACrC,iBAAkB,CAGlB,8BAAwC,CADxC,kBAAmB,CADnB,SAAU,CAGV,yBACF,CAEA,oBACE,+BACF,CAGA,sBAEE,kBAAmB,CAGnB,yCAA0C,CAD1C,2CAA4C,CAH5C,YAAa,CAEb,iBAGF,CAEA,oBACE,cAAe,CACf,iBACF,CAEA,oBAEE,YAAa,CADb,QAAO,CAEP,qBACF,CAEA,oBAGE,uBAAwB,CADxB,cAAe,CADf,eAGF,CAEA,oBAEE,8BAA+B,CAD/B,cAEF,CAGA,sBAEE,kBAAmB,CACnB,cAAe,CACf,eAAgB,CAHhB,gBAAiB,CAIjB,wBACF,CAEA,2BACE,gCAAiC,CACjC,sBACF,CAEA,6BACE,mCAAoC,CACpC,8BACF,CAEA,0BACE,kCAAmC,CACnC,wBACF,CAGA,iBACE,YACF,CAEA,eAIE,uBAAwB,CAHxB,cAAe,CACf,eAAgB,CAChB,cAEF,CAEA,qBAGE,iCAAkC,CAFlC,cAAe,CACf,eAAgB,CAEhB,eACF,CAGA,iBACE,eACF,CAEA,uBAGE,8BAA+B,CAF/B,cAAe,CACf,eAAgB,CAIhB,mBAAqB,CAFrB,cAAiB,CACjB,wBAEF,CAEA,wBACE,YAAa,CACb,OACF,CAGA,sBAQE,sBAAuB,CANvB,iBAAkB,CAOlB,cAAe,CAHf,YAAa,CAFb,cAAe,CAGf,6BAA8B,CAJ9B,iBAAkB,CAFlB,iBAAkB,CAIlB,iBAAkB,CAKlB,+BACF,CAEA,4BACE,sBACF,CAEA,yBACE,QAAO,CAEP,eAAgB,CADhB,kBAEF,CAEA,uBAEE,cAAe,CACf,eAAgB,CAChB,eAAgB,CAHhB,QASF,CAEA,oDAPE,aAAc,CAEd,eAAgB,CAChB,sBAAuB,CACvB,kBAAmB,CAHnB,UAgBF,CAVA,6BAIE,eAAyB,CAFzB,cAAe,CACf,eAAgB,CAFhB,cASF,CAEA,yBAeE,kBAAmB,CAXnB,eAAgB,CAChB,WAAY,CAMZ,iBAAkB,CAFlB,eAAyB,CACzB,cAAe,CAIf,YAAa,CARb,cAAe,CAOf,WAAY,CAGZ,sBAAuB,CATvB,aAAc,CACd,WAAY,CAPZ,iBAAkB,CAElB,UAAW,CADX,OAAQ,CAUR,UAKF,CAEA,+BACE,0BAAoC,CACpC,WACF,CAGA,8BACE,0BAAmE,CAAnE,kDACF,CAEA,4BACE,0BAAiE,CAAjE,gDACF,CAEA,2BACE,0BAAoE,CAApE,mDACF,CAGA,gCACE,wCAAyC,CACzC,gCAAiC,CACjC,8BACF,CAEA,sCACE,2BACF,CAEA,wCACE,oBAAqC,CACrC,uCACF,CAEA,6CACE,oBAAkC,CAClC,aACF,CAEA,+CACE,oBAAkC,CAClC,aACF,CAEA,4CACE,oBAAgC,CAChC,aACF,CAEA,+CACE,eACF,CAEA,2CACE,eACF,CAEA,iDACE,0BAA0C,CAC1C,WACF,CAEA,gDACE,0BACF,CAEA,8CACE,0BACF,CAEA,6CACE,0BACF,CCtPA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,sBAAuB,CAEvB,gBACF,CAEA,wBAEE,eAAgB,CAChB,cAAe,CAFf,iBAGF,CAEA,qBACE,kBACF,CAEA,yBACE,WACF,CAEA,cACE,gCAAiC,CACjC,cAAe,CACf,qCACF,CAEA,sBAGE,uBAAwB,CAFxB,cAAe,CACf,kBAEF,CAEA,4BAEE,iCAAkC,CAClC,cAAe,CACf,eAAgB,CAHhB,kBAIF,CAGA,oBACE,yCAA0C,CAI1C,oCAAqC,CAHrC,kBAAmB,CAEnB,kBAAmB,CADnB,YAGF,CAEA,0BAIE,uBAAwB,CAHxB,cAAe,CACf,eAAgB,CAChB,iBAEF,CAEA,gCACE,iCAAkC,CAElC,eAAgB,CADhB,kBAEF,CAGA,aAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,YAEE,kBAAmB,CACnB,wCAAyC,CACzC,oCAAqC,CACrC,iBAAkB,CAJlB,YAAa,CAKb,YAAa,CACb,kBACF,CAEA,kBAEE,+BAAyC,CADzC,0BAEF,CAEA,YACE,cAAe,CACf,iBACF,CAEA,YACE,QACF,CAEA,YAIE,uBAAwB,CAFxB,cAAe,CADf,eAAgB,CAEhB,QAEF,CAEA,YAEE,8BAA+B,CAD/B,cAAe,CAEf,QACF,CAGA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,QAAS,CACT,kBAAmB,CAEnB,eAAgB,CADhB,UAEF,CAEA,sBAEE,uBAAwB,CADxB,eAAgB,CAEhB,kBACF,CAGA,0BAQE,kBAAmB,CAPnB,2EAA8E,CAC9E,WAAY,CACZ,iBAAkB,CAGlB,mCAAoC,CAIpC,cAAe,CAHf,YAAa,CAFb,eAAgB,CAIhB,OAAQ,CALR,iBAAkB,CAOlB,uBACF,CAEA,gCAEE,iDAAkD,CADlD,0BAEF,CAGA,mBAME,QAAS,CADT,gBAEF,CAEA,cAGE,8BAA+B,CAD/B,cAAe,CADf,eAGF,CAEA,gBAEE,QAAS,CACT,eACF,CAEA,eACE,cAEF,CAKA,4BAA8B,mBAAuB,CAQrD,iBACE,kBACF,CAEA,sBAEE,iCAAkC,CADlC,cAEF,CAGA,eACE,iBACF,CAGA,4BAEE,eAAgB,CADhB,iBAEF,CAEA,eACE,gBAAiB,CACjB,mBACF,CAEA,iBAEE,kBAAmB,CADnB,UAEF,CAEA,iBAQE,kBAAmB,CACnB,oBAAoC,CACpC,iBAAkB,CALlB,QAAS,CACT,YAAa,CACb,sBAAuB,CAJvB,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CASN,UACF,CAEA,iBAEE,eAAiB,CAEjB,iBAAkB,CAClB,+BAAyC,CACzC,aAAc,CAHd,YAAa,CAFb,iBAAkB,CAMlB,WACF,CAEA,oBAGE,yBAA0B,CAF1B,cAAe,CACf,kBAEF,CAEA,mBAEE,2BAA4B,CAD5B,kBAEF,CAOA,sCACE,oBAAqC,CACrC,gCACF,CAEA,8BACE,wCACF,CAEA,oCACE,+BACF,CAEA,mCACE,oBACF,CAEA,mCACE,wCACF,CAGA,yBACE,gBACE,iBACF,CAEA,yBACE,UACF,CAEA,sBACE,cACF,CAEA,4BACE,cACF,CAEA,gBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,OACF,CAEA,aACE,yBACF,CAEA,oBACE,YACF,CACF,CC/SA,kBAEE,kBAAmB,CADnB,mBAAoB,CAEpB,iBACF,CAEA,gBAEE,kBAAmB,CAInB,+BAAgC,CAHhC,cAAe,CAFf,YAAa,CAIb,cAAe,CADf,wBAAiB,CAAjB,gBAGF,CAEA,yBACE,kBAAmB,CACnB,UACF,CAEA,gBAGE,uBAAgB,CAAhB,eAAgB,CAKhB,wCAAyC,CAFzC,oCAAqC,CACrC,iBAAkB,CALlB,cAAe,CAGf,WAAY,CAJZ,gBAAiB,CASjB,YAAa,CADb,iBAAkB,CAElB,gDAAoD,CAPpD,UAQF,CAEA,wBACE,iDAAkD,CAClD,6CACF,CAEA,8BASE,iBAAyB,CAAzB,wBAAyB,CARzB,UAAW,CAEX,aAAc,CAId,WAAY,CAHZ,SAAU,CAFV,iBAAkB,CAGlB,OAAQ,CAKR,uBAAwB,CAJxB,SAKF,CAEA,sBACE,iCAAkC,CAClC,sDACF,CAEA,eACE,eACF,CAEA,aACE,8BAA+B,CAC/B,cAAe,CACf,cACF,CCjEA,cAEE,kBAAmB,CACnB,UACF,CAEA,gBAGE,qCAAsC,CADtC,WAAY,CAEZ,eAAiB,CAHjB,SAIF,CAEA,mBAEE,kBAAmB,CADnB,YAAa,CAEb,UACF,CAEA,oBACE,iBACF,CAEA,iBAEE,kBAAmB,CADnB,YAAa,CAEb,SACF,CAEA,cACE,mBAAoB,CACpB,6BACF,CAEA,0BACE,wBACF,CAEA,qBACE,6BACF,CAEA,gCACE,wBACF,CAEA,eAOE,qCAAsC,CACtC,qCAAsC,CACtC,kCAAmC,CACnC,6CAA8C,CAR9C,iBAAkB,CAElB,iBAAmB,CAEnB,eAAgB,CAKhB,eAAgB,CAVhB,iBAAkB,CAElB,QAAS,CAET,UAOF,CAEA,eAKE,eAAgB,CAChB,WAAY,CAEZ,0BAA2B,CAD3B,cAAe,CANf,aAAc,CAQd,iBAAmB,CALnB,mBAAqB,CADrB,eAAgB,CAOhB,+BAAiC,CARjC,UASF,CAEA,gCACE,4CACF,CAEA,qBACE,6CACF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,cACE,+BAAgC,CAChC,iBAAmB,CACnB,eACF,CAEA,qBACE,eACF,CAEA,iBACE,6CAAgD,CAChD,yCAA0C,CAG1C,wCAAyC,CAFzC,8BAA+B,CAG/B,iBAAmB,CAFnB,mBAGF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAGb,SAAW,CADX,sBAAuB,CAEvB,cACF,CAEA,eAGE,SAAW,CACX,gBACF,CAGA,yBAEE,4BAEE,SAAU,CAEV,4BAA6B,CAH7B,OAAQ,CAER,WAEF,CAGA,WAEE,mBAAoB,CADpB,gBAEF,CAGA,kDAGE,sBAAuB,CAFvB,cAAe,CACf,UAEF,CACF,CAGA,yBACE,2EACE,eAAgB,CAChB,kBACF,CACF,CCvJA,0BAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAEF,CAEA,yBAEE,qBACF,CAEA,uBAKE,kBAAmB,CAFnB,YAAa,CADb,YAAa,CAEb,sBAAuB,CAEvB,eAAgB,CAChB,YAAa,CANb,UAOF,CAEA,eAEE,eAEF,CAEA,wBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,gBACE,YAAa,CACb,OACF,CAEA,aAGE,wBAAyB,CADzB,+BAAgC,CADhC,YAGF,CAEA,gBAEE,aAAc,CACd,cAAe,CACf,eAAgB,CAHhB,QAIF,CAEA,cACE,YACF,CAEA,aACE,aAAc,CACd,oBAAqB,CACrB,oBACF,CAEA,mBACE,yBACF,CAGA,WACE,UACF,CAOA,iBAIE,UAAW,CAHX,gBAAiB,CAEjB,kBAEF,CAEA,sBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,UAGE,UACF,CAMA,mBAKE,kBACF,CAOA,wBACE,yBAA0B,CAC1B,iBAAkB,CAElB,kBAAmB,CADnB,YAEF,CAEA,qBAGE,kBACF,CAWA,yBAME,iBAAkB,CAFlB,kBAGF,CAaA,sBAEE,cAAe,CADf,WAEF,CAGA,eAOE,QAAS,CAHT,eAAgB,CAChB,gBAAiB,CACjB,eAEF,CAQA,qBACE,YAAa,CACb,iBACF,CAEA,uBACE,YACF,CAEA,4BACE,aACF,CAGA,cAME,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAPlB,YAAa,CAEb,QAAS,CADT,wBAAyB,CAEzB,eAAgB,CAChB,YAIF,CAGA,mCAEE,yBAA2B,CAD3B,SAEF,CAGA,yBACE,0BACE,yBACF,CAEA,UACE,qBAAsB,CACtB,OACF,CACF,CAGA,mCAEE,cACE,wBAAyB,CACzB,oBACF,CACF,CAGA,UAKE,wBAAyB,CAIzB,qBAAuB,CALvB,iBAAmB,CADnB,oBAOF,CAGA,uCAXE,kBAAmB,CAInB,UAAY,CALZ,mBAAoB,CAMpB,gBAAkB,CAClB,eAaF,CARA,6BAIE,kBAAmB,CADnB,qBAKF,CAGA,SAME,iCAAkC,CAHlC,0BAAyB,CADzB,iBAAkB,CAClB,wBAAyB,CAEzB,WAAY,CADZ,UAGF,CAQA,qBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAEF,CAEA,yBACE,qBACE,yBACF,CACF,CAGA,yBAOE,kBAAmB,CANnB,qBAAuB,CACvB,mBAAqB,CAErB,8BAAwC,CACxC,YAAa,CACb,sBAAuB,CAHvB,YAKF,CAEA,eAEE,gBAAiB,CADjB,cAAe,CAEf,kBACF,CAGA,cACE,YAAa,CACb,SACF,CC5SA,kBAGE,aAAc,CADd,gBAAiB,CADjB,UAGF,CAEA,cACE,cACF,CAEA,aACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,oBACE,YAAa,CACb,QAAS,CACT,cACF,CAEA,cAGE,QAAS,CACT,eACF,CAEA,eAIE,wBAAyB,CAEzB,6BAA8B,CAL9B,aAMF,CAEA,gCAJE,iBAAkB,CAHlB,eAAgB,CAChB,YAaF,CAPA,iBAIE,wBAAyB,CAEzB,6BAA8B,CAL9B,aAMF,CAGA,wBAME,kDAAmD,CAHnD,oCAAqC,CACrC,wCAAyC,CAHzC,kBAAmB,CAInB,YAAa,CAHb,UAKF,CAEA,qBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,oBAEE,aAAc,CADd,eAEF,CAEA,sBACE,YAAa,CACb,OACF,CAEA,mBACE,YACF,CAEA,eAGE,qCAAsC,CAGtC,wCAAyC,CAFzC,UAAY,CAGZ,cAAe,CANf,oBAAqB,CAIrB,iBAAmB,CAHnB,kBAAoB,CAMpB,+BACF,CAEA,qBACE,0CACF,CAEA,oBAEE,oCAAqC,CAGrC,WAAY,CACZ,wCAAyC,CAHzC,+BAAgC,CAIhC,cAAe,CAHf,iBAAmB,CAHnB,kBAAoB,CAOpB,+BACF,CAEA,0BACE,+CAAgD,CAChD,8BACF,CAEA,yBAOE,kBAAmB,CAJnB,yBAA0B,CAC1B,iBAAkB,CAClB,YAAa,CAHb,YAAa,CAIb,sBAAuB,CAGvB,kBAAmB,CADnB,eAAgB,CAPhB,UASF,CAEA,eAEE,eAAgB,CADhB,cAAe,CAEf,kBACF,CAEA,mBACE,aAAc,CACd,cACF,CAEA,iBACE,eACF,CAGA,cAEE,wBAAyB,CACzB,iBAAkB,CAFlB,kBAAmB,CAGnB,eACF,CAEA,iBAGE,wBAAyB,CAIzB,+BAAgC,CADhC,aAAc,CAFd,cAAe,CACf,eAAgB,CAJhB,QAAS,CACT,iBAMF,CAEA,sBACE,YACF,CAEA,UACE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,YACE,QACF,CAEA,mBAGE,kBAAmB,CACnB,cAAe,CAHf,YAAa,CACb,6BAGF,CAEA,iBAEE,kBAAmB,CAEnB,aAAc,CAHd,YAAa,CAEb,iBAEF,CAGA,yBACE,UACE,qBAAsB,CACtB,OACF,CACF,CAGA,mCACE,eACE,sDACF,CAEA,iBACE,kDACF,CAEA,wBACE,0BACF,CAEA,oBACE,0BACF,CAEA,0BACE,sDACF,CAEA,yBACE,sBAAoC,CACpC,gCACF,CACF,CCvNA,WACE,eAAgB,CAEhB,eAAgB,CADhB,UAEF,CAEA,YAEE,wBAAyB,CACzB,eAAiB,CAFjB,UAGF,CAEA,eAGE,kDAAmD,CAEnD,eAAgB,CAJhB,eAKF,CAEA,8BAJE,2CAA4C,CAF5C,iBASF,CAEA,gBAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,gBAEE,WAAY,CACZ,gBAAiB,CAFjB,UAGF,CAEA,cACE,YAAa,CACb,OACF,CAEA,cAGE,iBAAkB,CAFlB,oBAAqB,CAGrB,eAAiB,CACjB,eAAgB,CAHhB,eAAgB,CAIhB,yBACF,CAGA,gBACE,wBAAyB,CACzB,aACF,CAEA,mBACE,wBAAyB,CACzB,aACF,CAEA,kBACE,wBAAyB,CACzB,aACF,CAEA,eACE,wBAAyB,CACzB,aACF,CAEA,mBAME,iCAAkC,CADlC,YAEF,CAEA,aAKE,iBAEF,CAEA,qBACE,eACF,CAEA,kBAEE,YAAa,CACb,sBAAuB,CAFvB,kBAGF,CAEA,sBAGE,gCAAiC,CADjC,WAAY,CADZ,UAGF,CAEA,mBAGE,+BAAgC,CAFhC,gBAAiB,CACjB,kBAEF,CAEA,yBACE,iCAAkC,CAClC,kBACF,CAEA,sBACE,cACF,CAEA,qBAEE,iCAAkC,CADlC,kBAEF,CAEA,iBACE,kBACF,CAEA,YACE,YACF,CAEA,UASE,kBAAmB,CARnB,qCAAsC,CACtC,iBAAkB,CAGlB,cAAe,CAEf,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CAPvB,iBAAkB,CAClB,iBAAkB,CAElB,uBAKF,CAEA,gBAEE,8CAA+C,CAD/C,iCAEF,CAEA,cAIE,gCAAiC,CACjC,aAAc,CAHd,WAAY,CACZ,kBAAmB,CAFnB,UAKF,CAEA,YAEE,+BAAgC,CADhC,iBAEF,CAEA,YAGE,gCAAiC,CAFjC,aAAc,CACd,gBAEF,CAEA,eAME,kBAAmB,CAHnB,kDAAmD,CACnD,iBAAkB,CAClB,YAAa,CAEb,OAAQ,CANR,aAAc,CACd,iBAMF,CAEA,oBACE,eACF,CAEA,eACE,YAAa,CAEb,QAAS,CADT,wBAAyB,CAEzB,eACF,CAEA,sBACE,cACF,CAEA,kBAGE,kDAAmD,CACnD,iBAAkB,CAHlB,aAAc,CACd,iBAGF,CAGA,gBAQE,iCAAkC,CADlC,0BAAyB,CADzB,iBAAkB,CAClB,wBAAyB,CANzB,oBAAqB,CAErB,WAAY,CACZ,gBAAiB,CAFjB,UAOF,CASA,mBAEE,gCAAiC,CADjC,eAAiB,CAIjB,iBAAkB,CAFlB,kBAAmB,CACnB,gBAEF,CAGA,sBAEE,kBAAmB,CAKnB,eAAgB,CAChB,WAAY,CAFZ,iBAAkB,CAGlB,aAAc,CAGd,cAAe,CAXf,mBAAoB,CASpB,cAAe,CALf,WAAY,CAFZ,sBAAuB,CAQvB,eAAgB,CAEhB,UAAY,CACZ,sBAAwB,CAVxB,UAWF,CAEA,4BACE,SACF,CAGA,wBACE,YACF,CAEA,2BAGE,cAAe,CADf,kBAAmB,CADnB,YAGF,CAEA,kBACE,eACF,CAEA,qBAGE,cAAe,CADf,kBAAmB,CADnB,YAGF,CAEA,eACE,wBAAyB,CACzB,iBAAkB,CAIlB,qBAAsB,CAEtB,cAAe,CACf,eAAgB,CAJhB,gBAAiB,CADjB,aAAc,CADd,YAAa,CAIb,oBAGF,CAGA,kBACE,kBACF,CAEA,qBAGE,+BAAgC,CAFhC,cAAe,CAGf,eAAgB,CAFhB,kBAGF,CAEA,eAKE,iBAAkB,CAHlB,eAAgB,CAChB,kBAAmB,CAFnB,oBAMF,CAEA,mCAHE,wBAAyB,CAFzB,YAUF,CALA,oBAGE,iBAAkB,CAClB,kBACF,CAEA,cAKE,+BAAgC,CAJhC,YAAa,CAKb,cAAe,CAJf,6BAA8B,CAC9B,iBAAkB,CAClB,kBAGF,CAEA,iBAEE,iBAAkB,CAClB,cAAe,CAFf,eAGF,CAEA,cACE,cACF,CAEA,SAIE,cAAe,CAFf,eAAgB,CAChB,iBAAkB,CAFlB,oBAIF,CAEA,uBAGE,0BAA2B,CAF3B,cAAe,CACf,eAEF,CCvVA,8CAKE,qBAAuB,CACvB,eAAgB,CAEhB,eAAgB,CAJhB,YAAa,CAGb,eAAgB,CAEhB,iBAGF,CAEA,kFAEE,wBAAwD,CAAxD,sDAAwD,CACxD,eACF,CAGA,yBASE,kBAAmB,CAHnB,gBAAiB,CAEjB,YAAa,CAHb,WAAY,CAKZ,sBAAuB,CARvB,SAAU,CADV,iBAAkB,CAElB,KAAM,CACN,UAAW,CAGX,UAIF,CAEA,+BAKE,iBAAkB,CAJlB,UAAW,CACX,aAAc,CAEd,WAAY,CAIZ,2BAA6B,CAL7B,SAMF,CAGA,+DANE,wBAAuD,CAAvD,qDAAuD,CACvD,SAuBF,CAlBA,gCAYE,kBAAmB,CAFnB,iBAAkB,CAMlB,0BAAwC,CAPxC,UAAY,CARZ,WAAY,CAUZ,YAAa,CAGb,cAAe,CAPf,WAAY,CAMZ,sBAAuB,CAVvB,MAAO,CADP,iBAAkB,CAElB,OAAQ,CACR,wCAAyC,CAYzC,8CAAkD,CAXlD,UAYF,CAEA,sCACE,UAAY,CACZ,mDACF,CAEA,uCACE,SAAU,CACV,iDACF,CAEA,qCACE,UACF,CAEA,sCACE,UACF,CAGA,kBAKE,qCAAsC,CADtC,+BAAqD,CAArD,mDAAqD,CAHrD,YAAa,CACb,qBAAsB,CAItB,aAAc,CACd,OAAQ,CAJR,iBAAkB,CAKlB,SACF,CAEA,oCACE,wBAAwD,CAAxD,sDAAwD,CACxD,2BAAkD,CAAlD,gDACF,CAEA,qBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,UACF,CAEA,sBAEE,kBAAmB,CADnB,YAAa,CAEb,eACF,CAEA,oBAIE,kBAAmB,CAFnB,cAAe,CACf,YAAa,CAGb,eAAgB,CADhB,OAAQ,CAJR,iBAAkB,CAMlB,UACF,CAEA,oBACE,QAAO,CAEP,eAAgB,CAChB,sBAAuB,CAFvB,kBAGF,CAEA,sCACE,aAAoC,CAApC,kCACF,CAEA,yBAGE,aAAuC,CAAvC,qCAAuC,CADvC,WAAY,CADZ,UAGF,CAEA,4BAME,qBAAuB,CACvB,iBAAkB,CAClB,+BAA0C,CAL1C,MAAO,CAMP,cAAe,CACf,gBAAiB,CACjB,eAAgB,CAVhB,iBAAkB,CAGlB,OAAQ,CAFR,QAAS,CAGT,YAOF,CAEA,8CACE,wBAAwD,CAAxD,sDAAwD,CACxD,+BACF,CAEA,yBAEE,cAAe,CADf,gBAAiB,CAEjB,qCACF,CAEA,+BACE,wBAAgD,CAAhD,8CACF,CAEA,iDACE,0BAAmE,CAAnE,qDACF,CAEA,4BAEE,wBAA8C,CAA9C,4CAA8C,CAD9C,UAAW,CAEX,YACF,CAEA,8CACE,wBAA+C,CAA/C,6CACF,CAQA,0CAHE,kBAAmB,CADnB,YAkBF,CAdA,wBAIE,wBAAuD,CAAvD,qDAAuD,CAQvD,qBAAuB,CATvB,iBAAkB,CAUlB,kBAAuB,CALvB,UAAY,CAEZ,cAAe,CADf,eAAgB,CAPhB,WAAY,CAKZ,sBAAuB,CAIvB,iBAAkB,CAVlB,UAaF,CAEA,0CACE,oBAAoD,CAApD,kDACF,CAEA,4BAGE,iBAAkB,CADlB,WAAY,CAEZ,gBAAiB,CAHjB,UAIF,CAEA,yBACE,cAAe,CACf,eACF,CAEA,sBAEE,kBAAmB,CAEnB,wBAAyB,CASzB,qBAAuB,CAHvB,iBAAkB,CAElB,kBAAuB,CAPvB,UAAY,CAJZ,YAAa,CAMb,cAAe,CADf,eAAgB,CAGhB,WAAY,CANZ,sBAAuB,CAQvB,gBAAiB,CAHjB,UAAW,CAMX,SACF,CAEA,wCACE,oBAAoD,CAApD,kDACF,CAEA,kDACE,aACF,CAEA,oBAKE,qBAAsB,CAItB,iBAAkB,CAPlB,WAAY,CAIZ,UAAY,CAKZ,YAAa,CAJb,cAAe,CAKf,iBAAkB,CAJlB,eAAgB,CAEhB,kBAAmB,CAGnB,UACF,CAEA,8CAbE,QAAS,CAFT,iBAAkB,CAGlB,0BAqBF,CATA,0BAQE,sBAAsD,CAAtD,qBAAsD,CAPtD,UAAW,CAEX,QAMF,CAEA,iBACE,eACF,CAEA,iBACE,cAAe,CACf,UACF,CAGA,oBAKE,YAAa,CAJb,QAAO,CAKP,qBAAsB,CACtB,QAAS,CACT,6BAA8B,CAL9B,iBAAkB,CADlB,eAAgB,CAEhB,YAKF,CAEA,mBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAKtB,WAAY,CAHZ,sBAAuB,CAEvB,YAAa,CADb,iBAGF,CAEA,qCACE,aAAuC,CAAvC,qCACF,CAEA,mBACE,WAAY,CAEZ,kBAAmB,CADnB,eAEF,CAEA,uBAEE,WAAY,CACZ,gBAAiB,CAFjB,UAGF,CAEA,sBAIE,gCAAiC,CAHjC,cAAe,CACf,eAAgB,CAChB,cAEF,CAEA,qBAEE,+BAAgC,CADhC,cAAe,CAEf,eAAgB,CAChB,eACF,CAGA,oBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAG3D,eAAgB,CADhB,cAEF,CAEA,qBAGE,sBAAuB,CAIvB,eAAiB,CAFjB,wBAAyB,CACzB,kBAAmB,CAEnB,8BAAyC,CACzC,cAAe,CARf,YAAa,CACb,qBAAsB,CAYtB,cAAe,CADf,OAAQ,CADR,eAAgB,CARhB,iBAAkB,CAOlB,eAAgB,CADhB,uBAKF,CAEA,2BAGE,oBAAqB,CADrB,8BAAwC,CADxC,0BAGF,CAEA,6BAKE,kBAAmB,CAFnB,yCAA0C,CAD1C,cAAe,CAEf,sBAAuB,CAHvB,UAAY,CAKZ,iBACF,CAEA,kCAGE,aAAc,CAFd,cAAe,CAGf,eAAgB,CAFhB,eAGF,CAEA,mCAEE,aAAc,CADd,cAAe,CAEf,eAAgB,CAEhB,cAAe,CADf,UAEF,CAGA,iBACE,MACE,UACF,CACA,IACE,UACF,CACF,CAGA,6BAIE,aAAc,CAEd,cAAe,CADf,iBAAkB,CAHlB,gBAAmB,CACnB,YAAa,CAFb,iBAMF,CAGA,yBACE,oBAEE,OAAQ,CADR,yBAEF,CAEA,qBAEE,eAAgB,CADhB,gBAEF,CAEA,kCACE,cACF,CAEA,mCACE,aACF,CACF,CAGA,uBACE,YAAa,CACb,kBACF,CAEA,kBACE,wBACF,CAEA,iBACE,0BACF,CAEA,qBAKE,kBAAmB,CAFnB,iBAAkB,CAClB,YAAa,CAGb,aAAc,CALd,WAAY,CAIZ,sBAAuB,CALvB,UAOF,CAEA,YACE,wBAAiD,CAAjD,+CAAiD,CACjD,UAAY,CACZ,gBACF,CAEA,aACE,wBAA+C,CAA/C,6CAA+C,CAC/C,aAA6C,CAA7C,2CAA6C,CAC7C,eACF,CAEA,iCACE,cAAe,CACf,eACF,CAEA,2BAIE,sBAAuB,CAFvB,YAAa,CACb,qBAAsB,CAFtB,aAIF,CAEA,6CACE,oBACF,CAGA,qBACE,cAAe,CACf,eAAgB,CAChB,iBAAkB,CAClB,aACF,CAEA,gCACE,aAA4C,CAA5C,0CACF,CAEA,iCACE,aAA0C,CAA1C,wCAA0C,CAC1C,gBACF,CAEA,oBACE,cAAe,CACf,eAAmB,CACnB,UACF,CAGA,wBAOE,UAAW,CAJX,oBAAqB,CAFrB,cAAe,CAGf,cAAe,CAFf,UAAY,CAIZ,iBAAkB,CAElB,UAAW,CAHX,wBAAiB,CAAjB,gBAIF,CAEA,mBAGE,kBAAmB,CAInB,8BAAyC,CAGzC,YAAa,CACb,qBAAsB,CAPtB,cAAe,CACf,eAAgB,CAGhB,iBAAkB,CAIlB,eAAgB,CAVhB,qBAAoB,CAOpB,iBAAkB,CAHlB,qBAQF,CAEA,kBAIE,qBAAsB,CAHtB,wBAAuD,CAAvD,qDAAuD,CAEvD,0BAA2B,CAD3B,UAAY,CAGZ,cACF,CAEA,oCACE,0BAAmE,CAAnE,qDAAmE,CACnE,aAAoC,CAApC,kCACF,CAGA,uBACE,YAAa,CACb,OAAQ,CACR,iBACF,CAEA,4BAME,oDAAqD,CAHrD,wBAAgD,CAAhD,8CAAgD,CAEhD,iBAAkB,CAHlB,UAAW,CAEX,UAAY,CAHZ,SAMF,CAEA,wCACE,kBACF,CAEA,yCACE,mBACF,CAEA,yCACE,mBACF,CAEA,4BACE,UACE,uBACF,CACA,IACE,0BACF,CACF,CAEA,8CACE,wBAAkD,CAAlD,gDACF,CAGA,uBASE,qBAAsB,CARtB,YAAa,CAEb,OAAQ,CADR,wBAAyB,CAEzB,cAAe,CAEf,UAAY,CADZ,eAAgB,CAEhB,2BAA6B,CAC7B,UAEF,CAEA,wDACE,SACF,CAEA,sBAWE,kBAAmB,CAPnB,0BAA0C,CAD1C,WAAY,CAMZ,iBAAkB,CAJlB,eAA+B,CAC/B,cAAe,CAIf,YAAa,CARb,WAAY,CAUZ,sBAAuB,CAJvB,QAAS,CADT,SAAU,CAMV,uBAAyB,CAZzB,UAaF,CAEA,4BAEE,sBAA0C,CAD1C,UAEF,CAEA,qCAEE,wBAAqD,CAArD,mDAAqD,CADrD,UAEF,CAGA,8BAME,kBAAmB,CAJnB,qBAAuB,CACvB,4BAAkD,CAAlD,gDAAkD,CAClD,YAAa,CAGb,aAAc,CAFd,OAAQ,CAJR,iBAOF,CAEA,gDACE,wBAAwD,CAAxD,sDAAwD,CACxD,wBAA+C,CAA/C,6CACF,CAEA,oBAOE,wBAAoD,CAApD,kDAAoD,CALpD,wBAA8C,CAA9C,4CAA8C,CAC9C,kBAAmB,CAKnB,aAAoC,CAApC,kCAAoC,CAPpC,QAAO,CAIP,cAAe,CACf,YAAa,CAFb,gBAKF,CAEA,sCAEE,0BAAmE,CAAnE,qDAAmE,CADnE,oBAA2C,CAA3C,yCAA2C,CAE3C,aAAsC,CAAtC,oCACF,CAEA,0BACE,oBAA2C,CAA3C,yCACF,CAEA,kBAQE,kBAAmB,CAPnB,wBAAuD,CAAvD,qDAAuD,CAEvD,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAQZ,cAAe,CAHf,YAAa,CAKb,aAAc,CANd,WAAY,CAGZ,sBAAuB,CAEvB,+BAAiC,CANjC,UAQF,CAEA,wBACE,wBAAyD,CAAzD,uDACF,CAEA,2BACE,wBAAgD,CAAhD,8CAAgD,CAChD,kBAAmB,CACnB,UACF,CAEA,6CACE,wBAA+C,CAA/C,6CACF,CAWA,6CALE,WAAY,CACZ,eASF,CALA,oBACE,YAAa,CACb,qBAGF,CAEA,eACE,QAAO,CAIP,eAAgB,CAFhB,iBAAkB,CADlB,eAAgB,CAEhB,aAEF,CAGA,sBAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAGtB,WAAY,CADZ,sBAAuB,CAGvB,YAAa,CADb,iBAEF,CAEA,yBACE,aAA4C,CAA5C,0CACF,CAEA,qBAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAEA,yBAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAGA,eAME,sCAAuC,CADvC,0BAAuB,CADvB,iBAAkB,CAClB,qBAAuB,CAEvB,oBAAqB,CALrB,WAAY,CAOZ,mBAAqB,CADrB,qBAAsB,CAPtB,UASF,CASA,uDAGE,4BAA8B,CAD9B,sBAAwB,CADxB,qBAAuB,CAGvB,gCACF,CAGA,iBAOE,uBAAwB,CAHxB,iBAAkB,CAClB,YAAa,CAFb,gBAAiB,CADjB,eAAgB,CAIhB,oBAEF,CAEA,oCACE,UAAW,CACX,SACF,CAEA,0CACE,oBAA+B,CAC/B,iBACF,CAEA,0CACE,oBAA+B,CAC/B,iBACF,CAEA,gDACE,oBACF,CAGA,uBAEE,wBAAyB,CADzB,UAEF,CAEA,uBAGE,wBAAuD,CAAvD,qDAAuD,CACvD,UAAY,CAHZ,eAAgB,CAChB,KAAM,CAGN,SACF,CAEA,oBAEE,eAAgB,CADhB,YAAa,CAEb,eAAgB,CAChB,kBACF,CAEA,oBAEE,+BAAqD,CAArD,mDAAqD,CADrD,YAEF,CAEA,kCACE,wBAAgD,CAAhD,8CACF,CAEA,yCACE,0BAAmE,CAAnE,qDACF,CAEA,oDACE,0BAAmE,CAAnE,qDACF,CAGA,yBACE,iBAGE,iBAAkB,CAFlB,YAAa,CACb,gBAEF,CAEA,uBACE,gBACF,CAEA,wCAEE,eACF,CAGA,+BAGE,2BAA4B,CAC5B,oBAAqB,CAFrB,mBAAoB,CAKpB,eAAgB,CANhB,eAAgB,CAKhB,sBAAuB,CADvB,kBAGF,CACF,CAGA,MACE,oDAAsD,CACtD,+CAAiD,CACjD,kDAAoD,CACpD,sDAAwD,CACxD,oDACF,CAGA,sBACE,UAAW,CACX,qBACF,CAEA,wBACE,QACF,CAEA,oCACE,YACF,CAEA,mCACE,eACF,CAEA,mBAIE,mBAAoB,CAHpB,wBAAyB,CAEzB,2BAA4B,CAD5B,aAA8C,CAA9C,4CAA8C,CAG9C,cACF,CAGA,yBACE,8CAGE,YAAa,CACb,qBAAsB,CAFtB,WAGF,CAEA,oBAGE,QAAS,CAFT,6BAA8B,CAC9B,YAEF,CAEA,2BACE,aACF,CAEA,8BAGE,QAAS,CAFT,iBAAkB,CAClB,eAEF,CAEA,mBACE,WACF,CAGA,sBAEE,WAAY,CADZ,UAEF,CAEA,kBAEE,WAAY,CADZ,UAEF,CAEA,oBAEE,cAAe,CADf,iBAEF,CAGA,yBAIE,WAAY,CAGZ,gBAAiB,CALjB,WAAY,CAGZ,MAAO,CACP,OAAQ,CAHR,KAAM,CAFN,UAOF,CAEA,+BACE,UAAW,CACX,UACF,CAGA,gCACE,WAAY,CACZ,QAAS,CAET,cAAe,CAEf,WAAY,CAHZ,KAAM,CAEN,yBAEF,CAEA,sCACE,UAAY,CACZ,oCACF,CAEA,uCACE,SAAU,CACV,kCACF,CACF,CAGA,gDACE,8CAEE,WACF,CAEA,2BACE,aACF,CACF,CAGA,qDACE,oBACE,6BACF,CAEA,mBACE,WACF,CACF,CAGA,6BAEE,wBAAqD,CAArD,mDAAqD,CADrD,+BAAqD,CAArD,mDAAqD,CAErD,eACF,CAEA,+CACE,wBAAwD,CAAxD,sDAAwD,CACxD,oBAA2C,CAA3C,yCACF,CAEA,sBAEE,kBAAmB,CAEnB,cAAe,CAHf,YAAa,CAEb,iBAAkB,CAElB,qCACF,CAEA,4BACE,wBAAgD,CAAhD,8CACF,CAEA,8CACE,0BACF,CAEA,oBAEE,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CAGvB,iBACF,CAEA,sCAJE,WAAY,CADZ,UAQF,CAEA,qBAIE,aAA4C,CAA5C,0CAA4C,CAH5C,QAAO,CAEP,cAAe,CADf,eAGF,CAEA,uCACE,aAAoC,CAApC,kCACF,CAEA,sBAGE,aAAuC,CAAvC,qCAAuC,CADvC,WAAY,CADZ,UAGF,CAEA,oBACE,qBACF,CAEA,oBACE,iBACF,CAEA,oBACE,YAAa,CACb,sBAAuB,CAEvB,kBAAmB,CADnB,cAEF,CAEA,uBACE,YAAa,CACb,wBAAyB,CACzB,eACF,CAEA,uBAEE,kBAAmB,CAGnB,wBAAqD,CAArD,mDAAqD,CACrD,+BAAqD,CAArD,mDAAqD,CAErD,aAAuC,CAAvC,qCAAuC,CAPvC,YAAa,CAMb,cAAe,CAJf,sBAAuB,CACvB,WAKF,CAEA,yCACE,wBAAwD,CAAxD,sDAAwD,CACxD,oBAA2C,CAA3C,yCAA2C,CAC3C,aAAuC,CAAvC,qCACF,CAEA,4BACE,eACF,CAGA,WACE,0BAAyC,CAEzC,6BAA8B,CAD9B,aAGF,CAEA,sCAHE,aAKF,CAEA,2BAIE,0BAAqC,CACrC,iBAAkB,CAHlB,cAAe,CADf,eAAiB,CAEjB,aAGF,CAEA,iCACE,0BACF,CAEA,8BACE,oBAAqB,CACrB,kBACF,CAEA,iCACE,mBACF,CAEA,yBACE,wBAAyB,CAGzB,eAAiB,CADjB,aAAc,CADd,UAGF,CAEA,4CAEE,qBAAsB,CACtB,WAAY,CACZ,eACF,CAEA,sBACE,0BAAqC,CACrC,eACF,CAEA,oCACE,0BACF,CAGA,sBAIE,iCAA2C,CAH3C,gBAAiB,CAEjB,oBAAsB,CADtB,iBAAkB,CAGlB,qBACF,CAEA,sBACE,gBAAiB,CAEjB,mBAAqB,CADrB,kBAEF,CAEA,8BACE,6BAA8B,CAG9B,aAAc,CACd,iBAAkB,CAFlB,aAAc,CADd,iBAIF,CAEA,sBAEE,QAAS,CACT,8BAAwC,CAFxC,eAGF,CAEA,wBACE,0BAAqC,CAErC,iBAAkB,CAClB,qBAAsB,CACtB,cAAgB,CAHhB,mBAIF,CAEA,uBACE,0BAAqC,CAErC,iBAAkB,CAClB,eAAgB,CAFhB,YAGF,CAEA,4BACE,wBAA6B,CAC7B,SACF,CAGA,wBAQE,kBAAmB,CAPnB,wBAA6B,CAE7B,WAAY,CADZ,aAA4C,CAA5C,0CAA4C,CAE5C,cAAe,CAGf,YAAa,CAEb,sBAAuB,CAJvB,WAAY,CACZ,uBAIF,CAEA,8BAEE,oBACF,CAEA,wEAJE,aAA0C,CAA1C,wCAMF,CAEA,iCACE,aAAuC,CAAvC,qCAAuC,CACvC,kBACF,CAGA,qBAQE,0BAAqC,CACrC,iBAAkB,CARlB,YAAa,CACb,cAAe,CACf,OAAQ,CAOR,iBAAkB,CAJlB,gBAAiB,CACjB,eAAgB,CAHhB,WAAY,CACZ,UAMF,CAEA,uCACE,0BACF,CAEA,yBAEE,kBAAmB,CACnB,qBAAuB,CACvB,iBAAkB,CAHlB,YAAa,CAKb,cAAe,CACf,eAAgB,CAFhB,eAAgB,CAGhB,iBACF,CAEA,2CACE,0BAAmE,CAAnE,qDACF,CAEA,yBAEE,aAA4C,CAA5C,0CAA4C,CAC5C,aAAc,CAFd,gBAGF,CAEA,0BAGE,iBAAkB,CADlB,WAAY,CAEZ,eAAgB,CAHhB,UAIF,CAEA,8BAEE,WAAY,CACZ,gBAAiB,CAFjB,UAGF,CAEA,yBAIE,eAAgB,CAFhB,eAAgB,CAChB,sBAAuB,CAFvB,kBAIF,CAEA,uBACE,eAAgB,CAChB,WAAY,CACZ,aAAuC,CAAvC,qCAAuC,CACvC,cAAe,CAGf,cAAe,CADf,eAAgB,CAEhB,UAAY,CAHZ,WAAY,CAIZ,uBACF,CAEA,6BAEE,aAAc,CADd,SAEF,CAGA,iBAGE,0BAAqC,CACrC,iBAAkB,CAFlB,UAAW,CAGX,iBAAkB,CAClB,iBAAkB,CALlB,UAMF,CAEA,qBAEE,wBAAuD,CAAvD,qDAAuD,CACvD,iBAAkB,CAFlB,WAAY,CAGZ,yBACF,CAEA,sBAKE,aAAuC,CAAvC,qCAAuC,CADvC,cAAe,CAHf,iBAAkB,CAElB,OAAQ,CADR,SAIF,CAGA,qBACE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CACR,cAAe,CACf,UACF,CAEA,iBAEE,0BAAqC,CACrC,iBAAkB,CAGlB,cAAe,CALf,YAAa,CAGb,eAAgB,CAChB,uBAEF,CAEA,mCACE,0BACF,CAEA,uBACE,0BAAqC,CAErC,8BAAyC,CADzC,0BAEF,CAEA,yCACE,0BACF,CAEA,kBAIE,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CAHvB,gBAAiB,CAIjB,WAAY,CALZ,UAMF,CAEA,sBAIE,iBAAkB,CAFlB,gBAAiB,CADjB,cAAe,CAEf,kBAEF,CAEA,iBAIE,kBAAmB,CAHnB,YAAa,CACb,YAAa,CACb,UAEF,CAEA,iBAQE,kBAAmB,CAJnB,wBAAuD,CAAvD,qDAAuD,CAEvD,iBAAkB,CADlB,UAAY,CAEZ,YAAa,CANb,aAAc,CAEd,WAAY,CAMZ,sBAAuB,CACvB,iBAAkB,CARlB,UASF,CAEA,iBACE,QAAO,CACP,eACF,CAEA,iBAEE,cAAe,CADf,eAAgB,CAEhB,iBAAkB,CAElB,eAAgB,CAChB,sBAAuB,CAFvB,kBAGF,CAEA,iBAEE,aAAuC,CAAvC,qCAAuC,CADvC,cAEF,CAGA,yBACE,qBACE,eACF,CAEA,yBACE,eAAgB,CAChB,eACF,CAEA,yBACE,cACF,CAEA,kBACE,gBACF,CAEA,sBACE,gBACF,CACF,CAGA,2BAEE,aAAc,CADd,eAAiB,CAEjB,eAAmB,CACnB,eACF,CAEA,iBAEE,aAAc,CADd,eAAiB,CAEjB,eAAmB,CACnB,eACF,CAGA,gCAEE,kBAAmB,CAMnB,aAAc,CAPd,YAAa,CAEb,cAAe,CAGf,eAAiB,CACjB,eAAgB,CAHhB,OAAQ,CACR,iBAIF,CC36CA,sBASE,QAAS,CART,YAAa,CAEb,YAAa,CAIb,MAAO,CAHP,eAAgB,CAChB,cAAe,CAGf,OAAQ,CAFR,KAAM,CAJN,UAQF,CAEA,gBAGE,aAAc,CADd,WAAY,CAEZ,iBAAkB,CAHlB,SAIF,CAEA,mBAEE,YAAa,CAEb,iBAAkB,CADlB,eAAgB,CAFhB,yBAIF,CAEA,gCACE,SACF,CAEA,8BACE,SACF,CAEA,yBAEE,qCAAsC,CACtC,yCAA0C,CAG1C,YAAa,CACb,qBAAsB,CACtB,YAAa,CAJb,eAAgB,CAKhB,cAAe,CAEf,OAAQ,CADR,KAAM,CALN,uBAAyB,CAJzB,SAWF,CAEA,gCAEE,OAAQ,CADR,UAAW,CAEX,UACF,CAEA,4BAEE,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAGZ,eACF,CAGA,+CACE,QAAO,CAEP,iBAAkB,CADlB,eAEF,CAGA,uBAgBE,kBAAmB,CAPnB,qCAAsC,CADtC,WAAY,CAKZ,8BAA+B,CAD/B,2BAA4B,CAE5B,8BAAwC,CAJxC,6BAA8B,CAC9B,cAAe,CAIf,YAAa,CAGb,cAAe,CAXf,WAAY,CAUZ,sBAAuB,CAfvB,MAAO,CAiBP,SAAU,CAlBV,iBAAkB,CAElB,OAAQ,CACR,0BAA2B,CAE3B,UAAW,CADX,UAeF,CAGA,cAEE,2CAA4C,CAD5C,YAAa,CAEb,aACF,CAEA,aAME,6BAA8B,CAF9B,cAAe,CAHf,QAAO,CAIP,cAAe,CAFf,YAAa,CADb,iBAAkB,CAKlB,uBACF,CAEA,mBACE,mCACF,CAEA,oBAEE,4CAA6C,CAD7C,0BAEF,CAGA,0BACE,yBACE,WACF,CAEA,oBACE,+BACF,CAEA,mCACE,8BACF,CACF,CAEA,yBACE,yBAOE,gBAAiB,CACjB,wCAAyC,CANzC,QAAS,CAOT,UAAW,CAHX,YAAa,CAHb,MAAO,CAOP,eAAgB,CAThB,cAAe,CAGf,OAAQ,CACR,UAMF,CAEA,gCACE,WAAY,CACZ,UACF,CAEA,oDACE,mBACF,CAEA,uBAIE,yBAA0B,CAF1B,QAAS,CADT,KAAM,CAEN,wCAEF,CACF,CAGA,YACE,YAAa,CACb,kEAEyB,CACzB,mCAAoC,CACpC,2BAA4B,CAC5B,YAAa,CACb,eACF,CAGA,0BACE,YACE,kDAEgB,CAChB,8BACF,CACF,CACA,eACE,wCACF,CAEA,YAIE,kBAAmB,CAEnB,wCAAyC,CACzC,yCAA0C,CAI1C,8BAAyC,CATzC,YAAa,CADb,gBAAiB,CAQjB,2BAA4B,CAN5B,6BAA8B,CAE9B,uCAAwC,CAKxC,iBAAkB,CAFlB,uBAIF,CAEA,2BAEE,kBAAmB,CADnB,YAAa,CAEb,mBACF,CAEA,aAGE,iBACF,CAGA,wBALE,kBAAmB,CADnB,YAUF,CAJA,WAGE,iBACF,CAEA,YACE,gCAAiC,CACjC,iBAAkB,CAClB,eAAiB,CACjB,mBAAqB,CACrB,kBACF,CAEA,UAGE,iCAAkC,CAElC,iBAAkB,CAHlB,wBAAyB,CADzB,eAAiB,CAQjB,eAAiB,CADjB,eAAgB,CAJhB,eAAgB,CAEhB,iBAAkB,CAIlB,wBAAyB,CAHzB,QAIF,CAGA,iBACE,WAAY,CACZ,eAAgB,CAChB,kBACF,CAGA,qCAME,kBAAmB,CALnB,gBAAuB,CACvB,WAAY,CAQZ,iBAAkB,CAPlB,0BAA2B,CAC3B,cAAe,CACf,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CAIvB,uBAAyB,CAHzB,UAIF,CAEA,2CACE,uCAAwC,CACxC,gCACF,CAGA,gDAME,kBAAmB,CALnB,gBAAuB,CACvB,WAAY,CAQZ,iBAAkB,CAPlB,0BAA2B,CAC3B,cAAe,CACf,YAAa,CAGb,eAAiB,CADjB,sBAAuB,CAEvB,uCAAwC,CAExC,kDACF,CAEA,4DACE,kCACF,CAEA,yBAEE,kBAAmB,CAQnB,wBAA6B,CAC7B,WAAY,CANZ,iBAAkB,CAOlB,cAAe,CAXf,YAAa,CAYb,OAAQ,CAJR,WAAY,CANZ,sBAAuB,CAWvB,cAAe,CAPf,iBAAkB,CADlB,qCAAsC,CAEtC,UAOF,CAEA,+BACE,uCAAwC,CACxC,qBACF,CAEA,gCACE,0BACF,CAEA,sCACE,0BACF,CAEA,aAGE,0BAA2B,CAC3B,oBAAqB,CAHrB,cAAe,CACf,eAGF,CAEA,aACE,WAAY,CACZ,cAAiB,CAEjB,kBAAmB,CADnB,aAAgB,CAEhB,6BACF,CAEA,4CACE,qBACF,CAEA,aAGE,sBAAuB,CADvB,WAAY,CADZ,UAGF,CAGA,aAGE,wCAAyC,CAIzC,wCAAyC,CAGzC,YAAa,CACb,qBAAsB,CAVtB,iBAAkB,CAOlB,yCAA0C,CAH1C,eAAgB,CAHhB,iBAAkB,CAIlB,yCAA0C,CAF1C,0BAA2B,CAK3B,wBAGF,CAEA,uBACE,oCACF,CAGA,iBAGE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,eAAgB,CAJhB,YAKF,CAEA,2BAEE,kBAAmB,CADnB,cAEF,CAGA,mBAEE,kBAAmB,CAEnB,kCAAmC,CAGnC,0BAA2B,CAN3B,YAAa,CAIb,iBAAkB,CAKlB,eAAgB,CAPhB,iBAAkB,CAGlB,oBAAqB,CAErB,oCAAsC,CACtC,kBAAmB,CAEnB,UACF,CAEA,yBACE,kCACF,CAEA,4BAEE,+BAAgC,CADhC,6BAEF,CAGA,6BAOE,kBAAmB,CAFnB,YAAa,CAGb,aAAc,CALd,WAAY,CAGZ,sBAAuB,CAFvB,iBAAkB,CAHlB,cAAe,CACf,UAOF,CAGA,wBAEE,SAAU,CAEV,uBAAwB,CAHxB,kEAAwE,CAExE,kBAEF,CAGA,6BAGE,sBAAuB,CACvB,kBAAmB,CAHnB,cAAe,CACf,UAGF,CAEA,uCACE,cACF,CAEA,kCACE,SAAU,CAEV,iBAAkB,CAClB,0BAA2B,CAF3B,iBAGF,CAGA,iBACE,sCAAuC,CAGvC,aAAc,CAFd,eAAgB,CAChB,UAEF,CAEA,uBAEE,kBAAmB,CAEnB,cAAe,CAHf,YAAa,CAEb,iBAAkB,CAElB,oCACF,CAEA,6BACE,6CACF,CAEA,eAGE,iBAAkB,CAElB,aAAc,CAHd,WAAY,CAEZ,eAAgB,CAHhB,UAKF,CAEA,mBAEE,WAAY,CACZ,gBAAiB,CAFjB,UAGF,CAUA,4BAJE,WAAY,CAFZ,gBAAiB,CACjB,eAAgB,CAEhB,+CAaF,CAVA,cAGE,0BAA2B,CAF3B,cAAe,CACf,eAAgB,CAIhB,sBAAuB,CAFvB,kBAMF,CAEA,cAEE,+BAAgC,CADhC,cAAe,CAGf,eAAgB,CAChB,sBAAuB,CAFvB,kBAMF,CAEA,6BAJE,aAAc,CADd,eAAgB,CAEhB,+CAOF,CAGA,8CACE,sBAAuB,CACvB,cACF,CAEA,qJAOE,QAAS,CAHT,SAAU,CACV,iBAAkB,CAClB,OAEF,CAGA,kBACE,cAAe,CAKf,uBAAyB,CACzB,wBAA0B,CAL1B,eAAgB,CAChB,uBAAwB,CACxB,qFAAuF,CAIvF,UACF,CAEA,gDANE,yCAQF,CAEA,oCACE,aACF,CAMA,iEACE,cACF,CAGA,eAEE,qBAAuB,CAIvB,6BAAmD,CAAnD,iDAAmD,CAGnD,+BAA0C,CAD1C,qBAAsB,CAGtB,YAAa,CACb,qBAAsB,CAXtB,kBAAmB,CAenB,yCAA0C,CAN1C,eAAgB,CAHhB,iBAAkB,CAMlB,cAAe,CAEf,OAAQ,CADR,wBAAyB,CAXzB,4CAAgD,CAchD,UACF,CAGA,4CASE,mCAAoC,CAFpC,wBAAuD,CAAvD,qDAAuD,CANvD,UAAW,CAIX,WAAY,CAFZ,MAAO,CAOP,SAAU,CACV,mBAAoB,CATpB,iBAAkB,CAElB,OAAQ,CAIR,0BAA2B,CAF3B,SAMF,CAEA,sBACE,MAEE,MAAO,CADP,SAEF,CACA,QAEE,UAAW,CADX,UAEF,CACA,IAEE,MAAO,CADP,UAEF,CACF,CAGA,yBACE,4CAME,yCAA0C,CAH1C,UAAW,CAFX,QAAS,CACT,KAAM,CAGN,0BAA2B,CAD3B,UAGF,CAEA,4BACE,MACE,SAAU,CACV,KACF,CACA,QACE,UAAY,CACZ,SACF,CACA,IACE,UAAY,CACZ,KACF,CACF,CACF,CAEA,iCACE,wBAAwD,CAAxD,sDAAwD,CACxD,yBAAgD,CAAhD,8CACF,CAEA,oBAEE,uBAAwB,CADxB,WAEF,CAEA,sBAGE,gBAAiB,CACjB,eAAgB,CAFhB,0BAA2B,CAD3B,OAIF,CAEA,uBAEE,YAAa,CACb,qBAAsB,CAFtB,WAAY,CAGZ,eAAgB,CAChB,UACF,CAGA,gBAEE,+BAAqD,CAArD,mDAAqD,CADrD,YAAa,CAEb,aACF,CAEA,eAME,aAAuC,CAAvC,qCAAuC,CAFvC,cAAe,CAHf,QAAO,CAIP,cAAe,CAFf,YAAa,CADb,iBAAkB,CAKlB,uBACF,CAEA,sBAEE,+BAA8D,CAA9D,4DAA8D,CAC9D,eACF,CAEA,wDALE,aAA4C,CAA5C,0CAQF,CAHA,kCACE,wBAAgD,CAAhD,8CAEF,CAGA,oBAEE,kBAAmB,CAGnB,wBAA6B,CAC7B,WAAY,CAFZ,cAAe,CAHf,YAAa,CAQb,WAAY,CANZ,sBAAuB,CAIvB,SAAU,CACV,UAEF,CAEA,WACE,YAAa,CACb,qBAAsB,CAGtB,WAAY,CAFZ,6BAA8B,CAC9B,UAEF,CAEA,gBAIE,qCAAsC,CACtC,iBAAkB,CAJlB,aAAc,CACd,UAAW,CAIX,uBAAyB,CAHzB,UAIF,CAEA,mCACE,uCACF,CAEA,oCACE,SACF,CAEA,oCACE,yCACF,CAGA,qBAME,0BAAoC,CADpC,QAAS,CAGT,YAAa,CALb,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,wBAAyB,CAKzB,UAEF,CAGA,wCAQE,kBAAmB,CAHnB,wCAAyC,CADzC,kCAAmC,CADnC,kCAAmC,CAMnC,8BAAyC,CAFzC,YAAa,CANb,WAAY,CACZ,gBAAiB,CAIjB,qCAIF,CAEA,8CACE,uCAAwC,CACxC,uCACF,CAEA,yBACE,+BAAgC,CAChC,cAAe,CACf,aAAc,CACd,iBACF,CAEA,yBACE,0BAA2B,CAC3B,cAAe,CACf,eAAgB,CAChB,eACF,CAGA,yCAEE,gCAAiC,CADjC,eAAgB,CAEhB,6BACF,CAEA,+CACE,yBACF,CAGA,yBAEE,YACE,+CAEa,CACb,yBACF,CAGA,oBACE,YAAa,CACb,iBACF,CAEA,qBACE,aACF,CAGA,YACE,cAAe,CACf,mBACF,CAEA,UACE,eAAiB,CAEjB,eAAgB,CADhB,QAEF,CAGA,aASE,wCAAyC,CACzC,wCAAyC,CANzC,QAAS,CAQT,YAAa,CACb,qBAAsB,CAFtB,yCAA0C,CAT1C,MAAO,CAMP,eAAgB,CAPhB,cAAe,CAEf,wBAAyB,CAGzB,2BAA4B,CAC5B,UAAW,CAFX,UASF,CAGA,iBACE,QAAO,CAEP,eAAgB,CADhB,YAEF,CAGA,iBAEE,sCAAuC,CADvC,eAEF,CAGA,yBAEE,+BAA0C,CAD1C,uBAEF,CAGA,yBAEE,yCAA0C,CAD1C,iBAEF,CAEA,iEAGE,aAAc,CADd,cAAe,CADf,UAGF,CAEA,wCAEE,6CAA8C,CAD9C,kCAAmC,CAGnC,WAAY,CADZ,iBAEF,CAEA,mDACE,cAAe,CACf,UACF,CAEA,mDACE,cAAe,CACf,eAAgB,CAChB,eACF,CAGA,aACE,QACF,CAEA,iBACE,WAAY,CACZ,eACF,CAEA,YACE,uCACF,CAEA,kBACE,uBAAyB,CACzB,wBAA0B,CAE1B,uBAAwB,CADxB,UAEF,CAGA,eAUE,gBAAiB,CACjB,2BAA4B,CAF5B,4BAAkD,CAAlD,gDAAkD,CANlD,QAAS,CAST,gCAA0C,CAL1C,WAAY,CAHZ,MAAO,CAIP,gBAAiB,CAPjB,cAAe,CAIf,OAAQ,CAHR,QAAS,CAYT,uBAAwB,CARxB,oBAAsB,CAOtB,YAEF,CAEA,sBACE,0BACF,CAEA,oBACE,uBACF,CAGA,yBAME,gBAAiB,CADjB,WAAY,CAHZ,MAAO,CACP,OAAQ,CAFR,KAAM,CAGN,UAGF,CAEA,+BAGE,iBAAkB,CADlB,UAAW,CADX,UAGF,CAGA,wCACE,eAIF,CAEA,iEAJE,eAAgB,CAChB,sBAAuB,CAFvB,kBASF,CAGA,aACE,YACF,CAEA,yBAEE,uBAAwB,CADxB,UAEF,CACF,CAGA,mCACE,4CAGE,wCAAuE,CAAvE,qEACF,CAEA,gBACE,qCACF,CACF,CAGA,0BACE,eAME,gCAA0C,CAL1C,cAAe,CACf,OAAQ,CACR,wBAAyB,CACzB,2BAA4B,CAC5B,UAEF,CAGA,gCACE,cACF,CAGA,oBACE,uBACF,CAEA,sBAEE,eAAgB,CADhB,0BAEF,CACF,CAGA,gBACE,YAAa,CACb,qBAAsB,CAEtB,WAAY,CACZ,cAAe,CAFf,UAGF,CAEA,iCAEE,cACF,CAGA,UAEE,kBAAmB,CAGnB,iBAAkB,CAJlB,YAAa,CAGb,iBAAkB,CADlB,iBAAkB,CAGlB,oBAAqB,CACrB,oCACF,CAEA,gBACE,yCACF,CAEA,oCAEE,2CAA4C,CAC5C,0BAA2B,CAC3B,eACF,CAEA,oBAKE,kBAAmB,CADnB,YAAa,CAGb,aAAc,CALd,WAAY,CAIZ,sBAAuB,CAHvB,iBAAkB,CAFlB,UAOF,CAEA,oBACE,sBAAuB,CACvB,gBACF,CAEA,8BACE,cACF,CAGA,0BACE,aACF,CAEA,oBAEE,kBAAmB,CAGnB,iBAAkB,CAElB,iCAAkC,CADlC,cAAe,CALf,YAAa,CAOb,eAAgB,CAJhB,eAAgB,CADhB,iBAAkB,CAOlB,iBAAkB,CADlB,oCAEF,CAEA,0BACE,yCACF,CAEA,8BAKE,kBAAmB,CADnB,YAAa,CAGb,aAAc,CALd,WAAY,CAIZ,sBAAuB,CAHvB,iBAAkB,CAFlB,UAOF,CAEA,8BACE,sBAAuB,CACvB,gBACF,CAEA,wCACE,cACF,CAEA,eACE,WACF,CAEA,aAIE,kBAAmB,CADnB,YAAa,CADb,WAAY,CAGZ,sBAAuB,CACvB,6BAA+B,CAL/B,UAMF,CAEA,qBACE,qBACF,CAEA,kBAEE,eAAiB,CADjB,iBAEF,CAGA,yBAKE,8BACE,iBACF,CAEA,qBACE,qBACF,CACF,CAGA,uBAEE,gBAAiB,CADjB,iBAEF,CAEA,sBAME,kBAAmB,CALnB,eAAgB,CAChB,WAAY,CAQZ,iBAAkB,CAPlB,cAAe,CAEf,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CAHvB,iBAAkB,CAOlB,+BAAiC,CAHjC,UAIF,CAEA,4BACE,0BACF,CAEA,8CACE,0BACF,CAEA,WAGE,aAA4C,CAA5C,0CAA4C,CAD5C,WAAY,CADZ,UAGF,CAEA,6BACE,aAAoC,CAApC,kCACF,CAEA,gBAYE,kBAAmB,CARnB,wBAAqD,CAArD,mDAAqD,CAMrD,iBAAkB,CAIlB,0BAAwC,CATxC,UAAY,CAMZ,YAAa,CALb,cAAe,CACf,eAAgB,CAEhB,WAAY,CAIZ,sBAAuB,CAZvB,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAMN,UAOF,CAEA,mBAME,qBAAuB,CACvB,iBAAkB,CAClB,+BAAyC,CAGzC,cAAe,CANf,cAAe,CAKf,eAAgB,CAThB,iBAAkB,CAElB,OAAQ,CADR,QAAS,CAET,WAAY,CAKZ,YAGF,CAEA,qCACE,wBAAwD,CAAxD,sDAAwD,CACxD,+BACF,CAEA,0BAGE,kBAAmB,CAEnB,+BAAqD,CAArD,mDAAqD,CAJrD,YAAa,CACb,6BAA8B,CAE9B,iBAEF,CAEA,4CACE,2BAAkD,CAAlD,gDACF,CAEA,6BAIE,aAA4C,CAA5C,0CAA4C,CAF5C,cAAe,CACf,eAAgB,CAFhB,QAIF,CAEA,+CACE,aAAoC,CAApC,kCACF,CAEA,4BAGE,aAAuC,CAAvC,qCAAuC,CADvC,cAAe,CADf,cAGF,CAEA,8CACE,aAAuC,CAAvC,qCACF,CAEA,0BAME,4BAA8B,CAD9B,sBAAwB,CADxB,wBAA0B,CAG1B,iBAAmB,CALnB,kBAAoB,CACpB,0BAKF,CAGA,oCACE,qBAAuB,CACvB,sBAA4B,CAA5B,yBACF,CAEA,2BACE,gBAAiB,CACjB,eACF,CAEA,eACE,iBACF,CAEA,cACE,iBACF,CAEA,+BAIE,aAAuC,CAAvC,qCAAuC,CAFvC,YAAa,CACb,iBAEF,CAEA,mEAEE,aAAuC,CAAvC,qCACF,CAEA,mCAGE,cAAe,CADf,YAEF,CAEA,yBACE,6BAA8B,CAC9B,cAAe,CACf,cACF,CAGA,gBACE,YAAa,CACb,OAAQ,CACR,sBAAuB,CACvB,eACF,CAEA,eAEE,0CAA2C,CAD3C,cAEF,CAEA,2BAA8B,kBAAqB,CACnD,4BAA8B,mBAAuB,CACrD,4BAA8B,mBAAuB,CAErD,kBACE,UAAgB,gCAAmC,CACnD,IAAM,qCAAwC,CAChD,CAGA,qBACE,YAAa,CACb,OAAQ,CACR,sBAAuB,CACvB,kBACF,CAEA,iBAIE,WAAY,CAHZ,cAAe,CACf,UAAY,CACZ,oCAEF,CAEA,uBACE,SAAU,CACV,oBACF,CAGA,0BAGE,4BAA8B,CAD9B,sBAAwB,CAExB,iBAAmB,CAHnB,uBAIF,CAGA,0BAEE,wCAAyC,CADzC,iBAAkB,CAElB,iBACF,CAEA,wBAEE,0BAA2B,CAE3B,aAAc,CAHd,cAAe,CAEf,oBAAqB,CAErB,2BACF,CAEA,8BACE,UAAY,CACZ,yBACF,CAEA,4CACE,yBAAoD,CAApD,kDACF,CAGA,yBACE,mBAEE,eAAgB,CAChB,WAAY,CAFZ,wBAGF,CAEA,sBACE,WAAY,CACZ,UACF,CAEA,WAEE,WAAY,CADZ,UAEF,CAMA,oDACE,eACF,CACF,CAGA,0BACE,2BACE,gBACF,CACF,CAGA,sBAEE,iBAAkB,CADlB,iBAEF,CAEA,mBAEE,kBAAmB,CAInB,2CAA4C,CAD5C,kBAAmB,CAEnB,uBAAwB,CAGxB,cAAe,CATf,YAAa,CAOb,iBAAmB,CACnB,eAAgB,CANhB,OAAQ,CACR,gBAAiB,CAOjB,+BACF,CAEA,yBACE,4CACF,CAEA,oBACE,iCAAkC,CAClC,0BACF,CAEA,iBACE,kBACF,CAEA,qBAME,qCAAsC,CACtC,iBAAkB,CAClB,+BAA0C,CAH1C,cAAe,CAKf,eAAgB,CAThB,iBAAkB,CAElB,OAAQ,CADR,oBAAqB,CAErB,WAAY,CAKZ,WAEF,CAEA,4BAKE,kBAAmB,CAHnB,2CAA4C,CAC5C,YAAa,CACb,6BAA8B,CAH9B,iBAKF,CAEA,+BACE,cAAe,CACf,eAAgB,CAChB,QACF,CAEA,6BAEE,gBAAiB,CACjB,eAAgB,CAFhB,YAGF,CAEA,+BAEE,iBAAmB,CACnB,eAAgB,CAFhB,kBAGF,CAYA,sBAEE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAPf,YAAa,CAQb,cAAe,CACf,eAAgB,CAHhB,iBAAkB,CADlB,gBAAiB,CAKjB,oCACF,CAGA,qCAEE,cAAe,CADf,gBAEF,CAEA,qCACE,kBACF,CAEA,yBACE,sBAEE,cAAe,CADf,eAEF,CACA,uCACE,cACF,CAGA,mCACE,YACF,CAEA,qCACE,cACF,CACF,CAGA,kBAGE,kBAAmB,CADnB,YAAa,CADb,iBAGF,CAEA,eAKE,wBAA8C,CAA9C,4CAA8C,CAK9C,oBAAsB,CAJtB,UAAY,CACZ,eAAiB,CACjB,eAAgB,CAChB,mBAAsB,CAPtB,SAAU,CASV,SACF,CAEA,oCAbE,iBAAkB,CAElB,OAAQ,CACR,0BAiBF,CAPA,qBAME,aAAmC,CAAnC,iCAAmC,CADnC,gBAAkB,CAHlB,SAKF,CAEA,iBACE,aAA2C,CAA3C,yCAA2C,CAC3C,UACF,CAGA,qBAGE,0BAAyC,CACzC,oBAAsB,CAHtB,WAAY,CACZ,YAAa,CAGb,iBACF,CAEA,gBAME,wBAA8C,CAA9C,4CAA8C,CAE9C,WAAY,CAJZ,mBAAqB,CAGrB,UAAY,CAEZ,cAAe,CANf,eAAgB,CAEhB,mBAAqB,CAHrB,kBAAoB,CADpB,UASF,CAEA,cAGE,aAA2C,CAA3C,yCAA2C,CAD3C,gBAAkB,CADlB,QAGF,CAEA,uBAGE,kBAAmB,CAGnB,0BAAyC,CACzC,mBAAqB,CACrB,cAAe,CAPf,YAAa,CACb,sBAAuB,CAEvB,aAAc,CACd,aAIF,CAEA,cAEE,aAAmC,CAAnC,iCAAmC,CADnC,iBAEF,CCxmDA,aAEE,4CAA6C,CAD7C,YAAa,CAEb,oBACF,CAEA,YAIE,eAAgB,CAEhB,WAAoC,CAApC,6BAAoC,CAGpC,iCAAkC,CAFlC,cAAe,CALf,cAAe,CACf,eAAgB,CAFhB,qBAAuB,CAOvB,uBAEF,CAEA,kBACE,uBACF,CAEA,mBAEE,wCAAyC,CADzC,0BAEF,CAGA,gBACE,kBACF,CAGA,gBACE,oBACF,CAEA,cACE,gDAAiD,CACjD,oBAAsB,CAEtB,8BAAwC,CADxC,cAEF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,oBACF,CAEA,mBACE,iBAAkB,CAClB,eAAgB,CAChB,QACF,CAEA,mBAEE,iCAAkC,CADlC,iBAAmB,CAEnB,eACF,CAEA,eACE,YAAa,CAEb,QAAS,CADT,4BAEF,CAEA,kBAGE,kBAAmB,CAEnB,mBAAqB,CAJrB,YAAa,CACb,qBAAsB,CAItB,eAAgB,CAFhB,YAGF,CAEA,mBACE,cAAe,CACf,eAAgB,CAChB,mBACF,CAEA,mBAEE,iCAAkC,CADlC,iBAEF,CAEA,cACE,kDAAqD,CACrD,0BACF,CAEA,cACE,kDAAqD,CACrD,0BACF,CAEA,YACE,gDAAmD,CACnD,wBACF,CAGA,aACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,mBACE,qCAAsC,CAItC,2BAAkC,CAHlC,oBAAsB,CACtB,8BAAwC,CACxC,eAEF,CAEA,6BACE,sCACF,CAEA,2BACE,sCACF,CAEA,+BACE,oCACF,CAEA,qBAEE,kBAAmB,CAGnB,cAAe,CAJf,YAAa,CAEb,6BAA8B,CAC9B,mBAAoB,CAEpB,oCACF,CAEA,2BACE,mDACF,CAEA,mBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,mBAME,kBAAmB,CAFnB,kDAAqD,CADrD,iBAAkB,CAElB,YAAa,CAHb,WAAY,CAKZ,sBAAuB,CACvB,eAAgB,CAPhB,UAQF,CAEA,uBAEE,WAAY,CACZ,kBAAmB,CAFnB,UAGF,CAEA,sBACE,QACF,CAEA,yBACE,kBAAmB,CACnB,eAAgB,CAChB,mBACF,CAEA,yBACE,YAAa,CACb,qBAAsB,CAEtB,eAAgB,CADhB,UAEF,CAEA,cAEE,2CAA4C,CAC5C,kBAAmB,CAFnB,YAAc,CAId,oBAAsB,CADtB,eAEF,CAEA,eAGE,qCAAsC,CADtC,kBAAmB,CADnB,WAAY,CAGZ,yBACF,CAEA,YAEE,iCAAkC,CADlC,gBAEF,CAEA,0BAEE,kBAAmB,CADnB,YAAa,CAEb,UACF,CAEA,oBAEE,aAAc,CADd,YAEF,CAEA,uBACE,0BACF,CAEA,uBACE,0BACF,CAEA,qBACE,wBACF,CAEA,uBACE,iCACF,CAEA,SACE,6BACF,CAEA,kBACE,uBACF,CAGA,+BAGE,yBAA2B,CAD3B,yCAA0C,CAD1C,uBAGF,CAEA,kBACE,GAAO,SAAY,CACnB,GAAK,SAAY,CACnB,CAGA,0BAOE,8CAA+C,CAC/C,mBAAqB,CAHrB,iCAAkC,CAFlC,iBAAmB,CACnB,eAAgB,CAHhB,kBAAmB,CACnB,eAAgB,CAIhB,cAGF,CAGA,2BACE,kBACF,CAEA,8BACE,cAAe,CACf,eAAgB,CAChB,mBAAqB,CACrB,eACF,CAEA,mBACE,YAAa,CACb,cAAe,CACf,SACF,CAEA,iBAEE,kBAAmB,CAEnB,mDAAsD,CAEtD,qBAAuB,CALvB,YAAa,CAMb,gBAAkB,CAJlB,SAAW,CAEX,oBAGF,CAEA,iBAGE,kBAAmB,CAFnB,0BAA2B,CAC3B,YAAa,CAEb,sBACF,CAGA,mBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,sBACE,cAAe,CACf,eAAgB,CAChB,mBAAqB,CACrB,eACF,CAEA,kBAKE,8CAA+C,CAD/C,mBAAqB,CAHrB,YAAa,CACb,QAAS,CACT,cAGF,CAEA,sBACE,mDACF,CAEA,wBACE,iDACF,CAEA,qBACE,QACF,CAEA,kBAIE,aAAc,CAFd,iBAAmB,CADnB,eAAgB,CAIhB,oBAAsB,CAFtB,yBAGF,CAEA,sBAGE,iCAAkC,CAFlC,aAAc,CACd,gBAAkB,CAElB,oBACF,CAEA,gCAEE,wBAAyB,CADzB,gBAAkB,CAElB,iBACF,CAGA,aAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,yDAA4D,CAE5D,eACF,CAEA,aAEE,gDAAiD,CAIjD,2BAAkC,CAHlC,oBAAsB,CACtB,8BAAwC,CAHxC,YAAa,CAIb,cAAe,CAEf,iDACF,CAEA,mBAEE,8BAAyC,CADzC,0BAEF,CAEA,qBACE,sCACF,CAEA,qBACE,sCACF,CAEA,mBACE,oCACF,CAEA,aAME,kBAAmB,CAFnB,kDAAqD,CADrD,iBAAkB,CAOlB,0BAA2B,CAL3B,YAAa,CAIb,aAAc,CAPd,aAAc,CAKd,sBAAuB,CACvB,oBAAqB,CAGrB,uBAAyB,CAVzB,YAWF,CAEA,qBACE,mDAAsD,CACtD,0BACF,CAEA,qBACE,mDAAsD,CACtD,0BACF,CAEA,mBACE,iDAAoD,CACpD,wBACF,CAEA,gBACE,QACF,CAEA,mBACE,gBAAiB,CACjB,eAAgB,CAChB,kBACF,CAEA,gBACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,QAGE,kBAAmB,CAFnB,YAAa,CAGb,iBAAmB,CAFnB,6BAGF,CAEA,cACE,eACF,CAEA,cAGE,kBAAmB,CADnB,YAAa,CADb,eAAgB,CAGhB,SACF,CAGA,4BAEE,cAAe,CADf,aAEF,CAGA,uBACE,cACF,CAEA,mBACE,YAAa,CACb,qBAAsB,CACtB,UACF,CAEA,sBACE,iBAAkB,CAClB,eAAgB,CAChB,kBACF,CAEA,kBACE,qCAAsC,CACtC,oBAAsB,CACtB,8BAAwC,CACxC,cACF,CAEA,oBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,uBACE,kBAAmB,CACnB,eACF,CAEA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,WACE,YAAa,CACb,qBACF,CAEA,iBACE,gBAAkB,CAClB,oBACF,CAEA,iBACE,kBAAmB,CACnB,eACF,CAEA,aAGE,yCAA0C,CAF1C,eAAgB,CAChB,gBAEF,CAEA,YAEE,kBAAmB,CADnB,YAAa,CAGb,iBAAmB,CADnB,SAAW,CAEX,mBACF,CAGA,yBACE,eACE,qBACF,CAMA,yBACE,yBACF,CACF,CAGA,oBAEE,kDAAmD,CACnD,mBAAqB,CAFrB,kBAAmB,CAGnB,cACF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,mBACE,iBAGF,CAEA,gBACE,6CAA8C,CAE9C,WAAY,CACZ,oBAAsB,CAFtB,0BAA2B,CAK3B,cAAe,CADf,iBAAmB,CADnB,kBAAoB,CAGpB,+BACF,CAEA,sBACE,8CACF,CAEA,yBAEE,kBAAmB,CADnB,UAEF,CAEA,qBAOE,kBAAmB,CANnB,gDAAiD,CACjD,qBAAuB,CAGvB,YAAa,CACb,6BAA8B,CAF9B,oBAAqB,CADrB,YAKF,CAEA,uBAEE,kBAAmB,CADnB,YAAa,CAEb,eACF,CAEA,+BACE,0BACF,CAEA,+BACE,0BACF,CAEA,6BACE,wBACF,CAEA,2CACE,kBACF,CAEA,cAEE,iCAAkC,CADlC,iBAEF,CAEA,sBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAEF,CAEA,cACE,gDAAiD,CAGjD,2BAAkC,CAFlC,qBAAuB,CACvB,YAEF,CAEA,sBACE,sCACF,CAEA,sBACE,sCACF,CAEA,oBACE,oCACF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,oBACF,CAEA,mBACE,cAAe,CACf,eAAgB,CAChB,QACF,CAEA,iBACE,oBACF,CAEA,gBACE,YAAa,CAEb,iBAAmB,CADnB,6BAA8B,CAE9B,mBACF,CAEA,sBACE,iCACF,CAEA,sBACE,eACF,CAEA,iBACE,kDAAmD,CACnD,oBAAsB,CAGtB,iCAAkC,CADlC,iBAAmB,CADnB,cAGF,CAEA,sBAME,kBAAmB,CALnB,4CAA6C,CAC7C,qBAAuB,CAKvB,wBAAyB,CAFzB,YAAa,CADb,kBAAmB,CADnB,YAKF,CAEA,0CACE,mBACF,CAEA,2BACE,QACF,CAEA,sCAEE,2CAA4C,CAC5C,wBAAyB,CAFzB,gBAGF,CAEA,4CACE,yCACF,CC3sBA,uBACE,cACF,CAEA,mBAKE,YAEF,CAEA,gCANE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CAEvB,iBAYF,CATA,aAOE,yCAA0C,CAC1C,mBAAqB,CAHrB,iBAIF,CAEA,mBAEE,yBAA0B,CAC1B,eAAiB,CAFjB,aAGF,CAEA,MACE,2BAA4B,CAC5B,kBACF,CAEA,kBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAEF,CAEA,iBACE,+BAAgC,CAChC,+BAAgC,CAChC,mBAAqB,CAErB,8BAAwC,CADxC,YAEF,CAEA,mBAGE,sBAAuB,CAFvB,YAAa,CACb,6BAA8B,CAE9B,mBACF,CAEA,sBACE,gBAAiB,CACjB,eAAgB,CAChB,QAAS,CAET,sBAAuB,CADvB,kBAEF,CAEA,eAOE,kBAAmB,CALnB,eAAgB,CAChB,WAAY,CAMZ,oBAAsB,CARtB,mBAAoB,CAGpB,cAAe,CAEf,YAAa,CAIb,aAAc,CAFd,sBAAuB,CAHvB,cAMF,CAEA,qBACE,mCACF,CAEA,oBACE,yBAA0B,CAC1B,eACF,CAEA,6BACE,oBAAsB,CAEtB,sBAAuB,CADvB,kBAEF,CAEA,8BAEE,oBAAqB,CADrB,eAEF,CAEA,cAIE,oBAAqB,CAErB,cAAe,CAHf,cAIF,CAEA,kBACE,gBAGF,CAGA,yBACE,UACF,CAEA,kBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,eAEE,mBAAqB,CADrB,kBAAmB,CAEnB,eACF,CAEA,iBACE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,mBAKE,UAAW,CAFX,cAAe,CACf,eAAgB,CAFhB,kBAAmB,CADnB,YAKF,CAEA,cAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,yDAA4D,CAE5D,gBAAiB,CACjB,eAAgB,CAChB,mBACF,CAEA,aACE,qBAAuB,CAEvB,qBAAsB,CADtB,iBAAkB,CAGlB,cAAe,CADf,YAAa,CAEb,iBAAkB,CAClB,uBACF,CAEA,gBAEE,cAAe,CADf,gBAIF,CAEA,+BAHE,sBAAuB,CADvB,kBAUF,CANA,eAGE,UAAW,CADX,eAAiB,CADjB,iBAKF,CAEA,uBACE,UAAW,CACX,gBACF,CAEA,mBACE,iBAAkB,CAClB,8BACF,CAEA,sBACE,wBAAyB,CACzB,oBACF,CAEA,4BACE,wBACF,CAEA,2BAEE,kBAAmB,CADnB,UAEF,CAEA,qBAIE,wBAAyB,CAIzB,iBAAkB,CAHlB,UAAY,CACZ,eAAiB,CACjB,mBAAsB,CANtB,iBAAkB,CAElB,WAAa,CADb,SAOF,CAUA,yBACE,iBACE,qBAAsB,CACtB,OACF,CAMA,gCACE,yBACF,CACF,CAGA,wBACE,cACF,CAEA,UACE,kBACF,CAEA,uBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,iCAEF,CAEA,cACE,YAAa,CAEb,QAAS,CADT,wBAAyB,CAEzB,iBACF,CAGA,wBAIE,oBAAqB,CAHrB,YAAa,CACb,QAAS,CACT,kBAEF,CAEA,gBACE,YAAa,CACb,wBAAyB,CACzB,eACF,CAEA,YAGE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,YAAa,CACb,iBACF,CAEA,qBAGE,UAAW,CADX,YAAa,CADb,iBAGF,CAGA,cAEE,kBAAmB,CAEnB,aAAc,CAHd,mBAAoB,CAIpB,gBAAkB,CAClB,gBAAkB,CAHlB,oBAIF,CAEA,kBAGE,WAAY,CAFZ,mBAAqB,CACrB,UAEF,CAGA,eAGE,sCAAuC,CAFvC,YAAa,CACb,kBAEF,CAEA,YAIE,eAAgB,CAKhB,WAAoC,CAApC,6BAAoC,CADpC,yBAA0B,CAF1B,cAAe,CALf,QAAO,CAMP,eAAgB,CALhB,mBAAqB,CACrB,iBAAkB,CAOlB,uBACF,CAEA,mBAEE,yCAA0C,CAD1C,oBAEF,CAGA,yBACE,uBAEE,SAAW,CADX,yBAEF,CAEA,wBAEE,mBAAoB,CADpB,qBAEF,CACF,CAGA,6BACE,6BACF,CAEA,eAEE,YAAa,CACb,wBAAyB,CAFzB,cAGF,CAGA,2BAEE,+BAAgC,CAChC,wBAAyB,CACzB,mBAAqB,CACrB,kBAAmB,CAJnB,YAKF,CAEA,wBAEE,kBAAmB,CADnB,YAAa,CAEb,kBACF,CAEA,sBAIE,kBAAmB,CAGnB,0BAAyC,CADzC,iBAAkB,CAElB,aAAc,CALd,YAAa,CADb,WAAY,CAGZ,sBAAuB,CAIvB,mBAAqB,CARrB,UASF,CAEA,uBAEE,gBAAiB,CACjB,eAAgB,CAFhB,QAGF,CAEA,0BAGE,yBAA0B,CAD1B,gBAAkB,CADlB,QAGF,CAEA,sBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,sBACE,yCAA0C,CAC1C,mBAAqB,CACrB,YACF,CAEA,4BAIE,aAAc,CAFd,eAAiB,CACjB,eAAgB,CAFhB,gBAIF,CAEA,8BACE,gBACF,CAEA,kBACE,YAAa,CACb,6BAA8B,CAC9B,oBACF,CAEA,aACE,yBACF,CAEA,aACE,eACF,CAEA,8BACE,mBACF,CAEA,8BACE,oBACF,CAEA,eACE,eAAgB,CAEhB,QAAS,CADT,SAEF,CAEA,kBAEE,sBAAuB,CADvB,YAAa,CAEb,mBACF,CAEA,yBAEE,aAAc,CADd,WAAY,CAEZ,kBACF,CAEA,wBAGE,yBAA0B,CAD1B,gBAAkB,CAElB,iBAAkB,CAHlB,iBAIF,CC3cA,kBAEE,cAAe,CADf,YAEF,CAEA,eAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,cACE,cAAe,CACf,eAAgB,CAChB,QACF,CAEA,gBACE,YAAa,CACb,QACF,CAEA,gBACE,YAAa,CAIb,cAAe,CADf,QAAS,CAFT,6BAA8B,CAC9B,kBAGF,CAEA,aACE,YAAa,CAEb,QAAO,CADP,OAAQ,CAER,eACF,CAEA,eACE,eAMF,CAEA,8BAJE,gCAAiC,CAFjC,oCAAqC,CACrC,iBAAkB,CAElB,uBAAwB,CAJxB,gBAeF,CARA,eAGE,cAAe,CAFf,UAOF,CAEA,wBACE,uCAAwC,CACxC,iBAAkB,CAClB,2BAA4B,CAC5B,eACF,CAEA,cAEE,uBAAyB,CACzB,gBAAiB,CAFjB,UAGF,CAEA,iBACE,oCAAqC,CAIrC,2BAA4B,CAD5B,eAAgB,CADhB,eAIF,CAEA,kCAHE,2CAA4C,CAJ5C,iBAUF,CAEA,+BACE,kBACF,CAEA,0BACE,gCACF,CAEA,cAEE,kBAAmB,CAEnB,oBAAqB,CAHrB,mBAAoB,CAIpB,iBAAmB,CACnB,eAAgB,CAHhB,gBAIF,CAEA,sBACE,wBAAyB,CACzB,aACF,CAEA,yBACE,wBAAyB,CACzB,aACF,CAEA,wBACE,wBAAyB,CACzB,aACF,CAEA,wBACE,wBAAyB,CACzB,aACF,CAEA,eACE,YAAa,CACb,OACF,CAEA,YAGE,kBAAmB,CAGnB,uCAAwC,CACxC,iBAAkB,CAClB,2BAA4B,CAP5B,YAAa,CACb,6BAA8B,CAE9B,eAAgB,CAChB,iBAIF,CAEA,iBACE,2BACF,CAEA,qBACE,YAAa,CACb,OACF,CAGA,wBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAEF,CAEA,sBAEE,oCAAqC,CACrC,iBAAkB,CAFlB,kBAAmB,CAGnB,eACF,CAEA,gBAEE,yCAA0C,CAC1C,2CAA4C,CAF5C,YAGF,CAEA,mBAEE,cAAe,CACf,eAAgB,CAFhB,QAGF,CAEA,iBACE,YACF,CAEA,uBACE,YAAa,CACb,qBAAsB,CAEtB,OAAQ,CADR,eAEF,CAEA,eACE,YAAa,CACb,QAAS,CACT,aACF,CAEA,eAEE,kBAAmB,CAKnB,wBAAyB,CADzB,oBAAqB,CAErB,aAAc,CAPd,YAAa,CAIb,WAAY,CAFZ,sBAAuB,CACvB,UAKF,CAEA,sBACE,wBAAyB,CACzB,UACF,CAEA,mBAEE,wBAAyB,CADzB,UAEF,CAEA,sBAGE,yCAA0C,CAF1C,eAIF,CAEA,4CAHE,2CAA4C,CAF5C,iBAQF,CAEA,oCACE,kBACF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,OAAQ,CACR,eACF,CAEA,aACE,YAAa,CACb,6BACF,CAEA,eAIE,wCAAyC,CAHzC,eAAgB,CAChB,cAAe,CACf,eAEF,CAMA,yBAHE,eAQF,CALA,WAGE,iBAAa,CAFb,YAAa,CAEb,YAAa,CADb,8BAGF,CAEA,YAEE,2BAA4B,CAD5B,eAEF,CAEA,YACE,uBACF,CAEA,gBACE,YAAa,CACb,OAAQ,CACR,eACF,CAGA,yBACE,wBACE,yBACF,CAEA,gBACE,qBACF,CAEA,aACE,UACF,CAEA,eAEE,sBAAuB,CADvB,qBAAsB,CAEtB,QACF,CAEA,gBACE,UACF,CAEA,wBACE,eACF,CACF,CCxSA,yBACI,YACJ,CAMA,yCAHI,mBAeJ,CAZA,cAEI,kBAAmB,CACnB,kCAAmC,CAQnC,iBAAkB,CAJlB,4EAA8E,CAH9E,uBAAwB,CAHxB,YAAa,CAIb,eAAgB,CAGhB,eAAgB,CAEhB,gBAAiB,CAJjB,qBAMJ,CAEA,6BAII,aAAc,CAHd,YAAa,CAIb,aAAc,CAHd,sBAAuB,CACvB,eAAgB,CAGhB,oBACJ,CAEA,iCAII,kBAAmB,CAFnB,YAAa,CACb,sBAAuB,CAGvB,eAAgB,CADhB,cAAe,CAJf,iBAMJ,CAEA,8BACI,iBACJ,CAEA,4BAKI,wEACY,CAFZ,cAAe,CADf,UAAY,CAFZ,iBAAkB,CAClB,mBAKJ,CAEA,8BASI,wEACY,CACZ,mBAAsB,CANtB,kBAAmB,CADnB,kBAAmB,CADnB,WAAY,CADZ,SAAU,CAIV,iBAAkB,CAClB,uBAAwB,CANxB,UAWJ,CAEA,oCAGI,kDAAoD,CAEpD,mBAAsB,CAItB,4BAAkB,CAAlB,sBAAkB,CAAlB,2BAAkB,CAAlB,qBAAkB,CAClB,UAAW,CARX,qBAAsB,CADtB,UAAW,CAWX,WAAY,CADZ,QAAS,CAPT,SAAU,CAEV,iBAAkB,CAOlB,SACJ,CAEA,0BASI,wEACY,CACZ,mBAAsB,CANtB,kBAAmB,CADnB,kBAAmB,CADnB,WAAY,CADZ,SAAU,CAIV,iBAAkB,CAClB,uBAAwB,CANxB,UAWJ,CAEA,iEAGI,mDAAqD,CACrD,oBAAsB,CAItB,eAAgB,CAFhB,iBAAkB,CAGlB,UAAW,CAPX,UAAW,CASX,UAAW,CADX,QAAS,CAHT,SAAU,CAFV,iBAAkB,CAOlB,UACJ,CAEA,iCACI,oDAAsD,CACtD,oBAAsB,CACtB,uBACJ,CAEA,2BAQI,mCAAoC,CADpC,wBAA2B,CAF3B,kBAAmB,CAEnB,0BAA2B,CAJ3B,qBAAsB,CADtB,WAAY,CADZ,UAQJ,CAEA,yBACI,GAAkD,UAAU,CAAvD,0CAAyD,CAC9D,GAA+C,SAAS,CAAjD,gCAAmD,CAC9D,CAEA,wBACI,GAA+C,SAAS,CAApD,mCAAqD,CACzD,GAAsD,SAAS,CAAzD,6CAA0D,CACpE,CAEA,sBACI,GAEI,UAAY,CADZ,mBAEJ,CACA,GAEI,SAAU,CADV,kBAEJ,CACJ,CAEA,kBACI,GACI,sBACJ,CACA,GACI,uBACJ,CACJ,CAEA,2BACI,GAEI,SAAU,CADV,gCAEJ,CACA,GAEI,SAAU,CADV,gCAEJ,CACJ,CAEA,8BACI,GACI,QAAS,CAET,SAAU,CADV,OAEJ,CACA,IACI,QAAS,CAET,SAAU,CADV,SAEJ,CACA,GAEI,WAAY,CADZ,SAEJ,CACJ,CAEA,8BACI,GAEI,SAAU,CADV,kBAEJ,CACA,GAEI,SAAU,CADV,kBAEJ,CACJ,CAEA,+BACI,GAEI,SAAU,CADV,gCAEJ,CACA,GAEI,SAAU,CADV,gCAEJ,CACJ,CCjNA,aAAa,eAAe,CAAC,eAAe,wBAAwB,CAAC,oBAAoB,CAAC,sCAAsC,UAAU,CAAC,uBAAuB,UAAU,CAAC,oBAAoB,CAAC,oBAAuG,UAAU,CAAsE,8DAA8D,CAAC,wBAAwB,CAAlN,WAAW,CAAC,cAAc,CAAC,eAAe,CAAyK,aAAY,CAA/G,UAAU,CAAlK,iBAAiB,CAAC,WAAW,CAAiE,gCAAgC,CAAC,wBAAwB,CAAzH,SAA0O,CAAC,oDAAoD,UAAU,CAAsB,cAAc,CAAY,8DAA8D,CAAC,wBAAuB,CAAjG,UAAU,CAA9C,oBAAsI,CAAC,yBAAoC,UAAU,CAArB,UAAU,CAAY,UAAU,CAAC,0BAA2E,uBAAsB,CAA9C,cAAc,CAAC,QAAQ,CAAtC,cAAc,CAAxB,SAAwE,CAAC,kBAAwB,OAAO,CAAb,KAAK,CAAS,UAAU,CAAC,qBAAqB,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,sBAA4B,OAAO,CAAb,KAAK,CAAS,UAAU,CAAC,yBAAyB,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,gBAAyB,SAAQ,CAAjB,QAAkB,CAAC,iBAA0B,UAAS,CAAlB,QAAmB,CAAC,oBAA+B,WAAU,CAArB,UAAsB,CAAC,mBAAmB,WAAW,CAAC,SAAS,CAAC,iBAA+C,mBAAkB,CAAhD,cAAc,CAAC,cAAkC,CAAC,mBAA4E,qBAAqB,CAAC,qBAAqM,wBAA+B,CAAC,2BAA2B,CAA7E,iBAAiB,CAA4H,wBAAwB,CAAC,UAAU,CAAY,8DAA8D,CAAC,wBAAuB,CAA1X,cAAc,CAA2Q,UAAU,CAAnT,eAAe,CAAgB,2BAA2B,CAA9E,mBAAmB,CAArC,iBAAiB,CAAgF,WAAgV,CAAC,yBAAmE,qCAAoC,CAA9E,aAAa,CAAC,2BAAiE,CAAC,2BAA0F,wBAAwB,CAAqG,cAAa,CAAvG,+DAA+D,CAAC,yBAAyB,CAAnG,SAAkH,CAAC,6BAA6B,swBAAmxG,+BAAe,8yBAAi0B,iCAA4B,kgBAAgE,kjECCrzM,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,gLAAgI,CAChI,gFACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,4KAA4H,CAC5H,+DACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,6KAA6H,CAC7H,oBACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,yKAAyH,CACzH,yBACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,8KAA8H,CAC9H,wGACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,6KAA6H,CAC7H,qGACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,yKAAyH,CACzH,mJACF,CC7DA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,gLAAgI,CAChI,gFACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,4KAA4H,CAC5H,+DACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,6KAA6H,CAC7H,oBACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,yKAAyH,CACzH,yBACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,8KAA8H,CAC9H,wGACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,6KAA6H,CAC7H,qGACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,yKAAyH,CACzH,mJACF,CC7DA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,gLAAgI,CAChI,gFACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,4KAA4H,CAC5H,+DACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,6KAA6H,CAC7H,oBACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,yKAAyH,CACzH,yBACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,8KAA8H,CAC9H,wGACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,6KAA6H,CAC7H,qGACF,CAEA,WAGE,iBAAkB,CAFlB,iBAAoB,CACpB,iBAAkB,CAElB,eAAgB,CAChB,yKAAyH,CACzH,mJACF,CC9DA,MACI,qBAAsB,CACtB,kBAAsB,CAGtB,iCAAkC,CAClC,+BAAgC,CAChC,0BAA2B,CAC3B,6BAA8B,CAC9B,wBAAyB,CAEzB,kCAAmC,CACnC,4BAA6B,CAE7B,oBAAqB,CACrB,yBAA0B,CAC1B,yBAA0B,CAE1B,qCAAsC,CACtC,+BAAgC,CAGhC,qCAAsC,CACtC,wCAAyC,CAGzC,oBAAwB,CACxB,iCAAkC,CAClC,uBAAwB,CAExB,mBAAoB,CACpB,wBAAyB,CACzB,wBAAyB,CAEzB,oBAAqB,CACrB,yBAA0B,CAC1B,yBAA0B,CAE1B,sBAAuB,CACvB,2BAA4B,CAC5B,2BAA4B,CAE5B,qBAAsB,CACtB,0BAA2B,CAC3B,0BAA2B,CAE3B,wBAAkC,CAClC,6BAAwC,CAExC,sBAAuB,CAEvB,qCAAsC,CACtC,mCAAoC,CAEpC,mBAAoB,CACpB,yBAA0B,CAC1B,0BACJ,CAEA,kBACI,qBAAsB,CAGtB,iCAAkC,CAClC,+BAAgC,CAChC,0BAA2B,CAC3B,6BAA8B,CAC9B,wBAAyB,CAEzB,oBAAqB,CACrB,yBAA0B,CAE1B,kCAAmC,CACnC,4BAA6B,CAE7B,qCAAsC,CACtC,+BAAgC,CAEhC,qCAAsC,CACtC,wCAAyC,CAEzC,gCAAiC,CACjC,0CAA2C,CAG3C,mBAAoB,CACpB,0BAA0C,CAC1C,qBAAyB,CAEzB,oBAAqB,CACrB,2BAA0C,CAC1C,yBAA0B,CAE1B,sBAAuB,CACvB,6BAA4C,CAC5C,wBAA4B,CAE5B,qBAAsB,CACtB,4BAA2C,CAC3C,0BAA2B,CAG3B,uBAAwB,CACxB,iCAAkC,CAClC,uBAAwB,CAExB,mCAAoC,CACpC,mCACJ,CC5GA,mDAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,4FAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,yBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CACd,qBAAoB,CAApB,mDAAoB,EAApB,mDAAoB,EAApB,mDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EACpB,2BAAmB,CAAnB,yBAAmB,CAAnB,WAAmB,CAAnB,eAAmB,CAAnB,SAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,SAAmB,CAAnB,wCAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,6BAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,OAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,cAAmB,CAAnB,mBAAmB,CAAnB,mBAAmB,CAAnB,oBAAmB,CAAnB,gBAAmB,CAAnB,oBAAmB,CAAnB,qBAAmB,CAAnB,mBAAmB,CAAnB,sBAAmB,CAAnB,YAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,4CAAmB,CAAnB,4CAAmB,CAAnB,wCAAmB,CAAnB,8CAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,2BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,gCAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,yCAAmB,CAAnB,wCAAmB,CAAnB,eAAmB,CAAnB,oBAAmB,CAAnB,kCAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,oBAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,sBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,sBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,gBAAmB,CAAnB,uBAAmB,CAAnB,yBAAmB,CAAnB,mBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,+BAAmB,CAAnB,8BAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,qBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,eAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,wBAAmB,CAAnB,oBAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,8BAAmB,CAAnB,8BAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,6BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,4BAAmB,CAAnB,yCAAmB,CAAnB,wCAAmB,CAAnB,mOAAmB,CAAnB,wCAAmB,CAAnB,wCAAmB,CAAnB,wNAAmB,CAAnB,0CAAmB,EAAnB,+CAAmB,CAAnB,sCAAmB,CAAnB,8BAAmB,CAAnB,mBAAmB,CAAnB,uCAAmB,CAAnB,+BAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,yBAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,qCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,kEAAmB,CAAnB,8GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,oHAAmB,CAAnB,+EAAmB,CAAnB,oEAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,4BAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,+CAAmB,CAAnB,yCAAmB,CAAnB,+BAAmB,CAAnB,2CAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,6BAAmB,CAAnB,iCAAmB,CAAnB,0CAAmB,CAAnB,6BAAmB,CAAnB,8CAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,mCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,8BAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,kCAAmB,CAAnB,8BAAmB,CAAnB,0CAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,0CAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,+CAAmB,CAAnB,0CAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,+CAAmB,CAAnB,8DAAmB,CAAnB,0EAAmB,CAAnB,oEAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,yCAAmB,CAAnB,iDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,uDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,iDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,yDAAmB,CAAnB,8CAAmB,CAAnB,wCAAmB,CAAnB,wBAAmB,CAAnB,yDAAmB,CAAnB,0CAAmB,CAAnB,kCAAmB,CAAnB,qBAAmB,CAAnB,kDAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,+CAAmB,CAAnB,+CAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,oBAAmB,CAAnB,oDAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,qBAAmB,CAAnB,sDAAmB,CAAnB,0EAAmB,CAAnB,oEAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,kDAAmB,CAAnB,wCAAmB,CAAnB,wCAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,uDAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,iDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,iDAAmB,CAAnB,qEAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,oCAAmB,CAAnB,yDAAmB,CAAnB,yDAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,qFAAmB,CAAnB,8EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,8EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,2EAAmB,CAAnB,yGAAmB,CAAnB,wEAAmB,CAAnB,wEAAmB,CAAnB,+BAAmB,CAAnB,2DAAmB,CAAnB,kCAAmB,CAAnB,8BAAmB,CAAnB,mBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,6BAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,mDAAmB,CAAnB,8CAAmB,CAAnB,mDAAmB,CAAnB,+CAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,mDAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,0CAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,8GAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,iCAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,mCAAmB,CAAnB,mCAAmB,CAAnB,yBAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,oCAAmB,CAAnB,sCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,sCAAmB,CAAnB,sCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,sCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,sCAAmB,CAAnB,UAAmB,CAAnB,6CAAmB,CAAnB,2DAAmB,CAAnB,qDAAmB,CAAnB,iEAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,wCAAmB,CAAnB,kDAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,gDAAmB,CAAnB,+CAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,kEAAmB,CAAnB,4FAAmB,CAAnB,kEAAmB,CAAnB,kGAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,wEAAmB,CAAnB,+FAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,2EAAmB,CAAnB,kGAAmB,CAAnB,+CAAmB,CAAnB,kGAAmB,CAAnB,wLAAmB,CAAnB,0LAAmB,CAAnB,6IAAmB,CAAnB,qKAAmB,CAAnB,kDAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,qIAAmB,CAAnB,kDAAmB,CAAnB,wEAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,+DAAmB,CAAnB,2DAAmB,CAGnB,KAME,YAAa,CADb,iBAIF,CAEA,uBACI,GACE,SACF,CACA,GACE,SACF,CACF,CASF,KASI,kCAAmC,CACnC,iCAAkC,CAPlC,kCAAmC,CACnC,0BAA2B,CAH3B,4FAA2G,CAC3G,cAAe,CAGf,QAAS,CACT,SAKJ,CAEA,EAAI,qBAAwB,CAE5B,EACI,aAAc,CACd,oBACJ,CAEA,QACI,yBACJ,CAEA,kBACI,qCAAsC,CACtC,UACJ,CACA,sBACI,0BACJ,CAEA,cACI,mBACJ,CAEA,oCACI,cAAe,CACf,eACJ,CAEA,yBAEI,YAAa,CACb,qBAAsB,CAFtB,gBAGJ,CAEA,2BACI,gBACJ,CAEA,WACI,kCAAmC,CACnC,wCAAyC,CACzC,YACJ,CAEA,GAGI,cAAe,CACf,gBAEJ,CAEA,MAPI,iBAAkB,CAClB,eAAgB,CAGhB,aASJ,CANA,GAGI,cAAe,CACf,gBAEJ,CAEA,GACI,cAAe,CACf,gBACJ,CAEA,GACI,cAAe,CACf,gBACJ,CAEA,GACI,cAAe,CACf,gBAAiB,CACjB,YACJ,CAEA,eAEI,kCAAmC,CAInC,kCAAmC,CACnC,kCAAmC,CAGnC,8BAA8C,CAP9C,0BAA2B,CAC3B,2CAAkD,CAClD,cAAe,CAMf,+BAAgC,CAHhC,iBAAkB,CAPlB,iBAAkB,CAWlB,2BAA8B,CAH9B,UAIJ,CAEA,cACI,+BACJ,CAEA,YAEI,cAAe,CACf,gBAAiB,CAFjB,eAAgB,CAGhB,UACJ,CAEA,SACI,gBACJ,CAEA,2BACI,mCACJ,CAEA,kBAGI,oBAAqB,CADrB,eAAgB,CADhB,eAGJ,CAEA,MACI,aAAc,CACd,iBACJ,CAEA,YACI,aAAc,CACd,YACJ,CAEA,WAEI,aAAc,CACd,eAAgB,CAFhB,iBAGJ,CAEA,oBACI,6BAA8B,CAC9B,2CACJ,CAEA,mBACI,sBACJ,CAEA,iBACI,QACJ,CAEA,gBACI,+BAAgC,CAEhC,cAAe,CADf,eAEJ,CAEA,cACI,oBAAqB,CACrB,WAAY,CACZ,iBAAkB,CAClB,UACJ,CAEA,oBACI,YACJ,CAEA,sBACI,kCAAmC,CACnC,QAAS,CACT,cAAe,CACf,MAAO,CACP,iBAAkB,CAClB,OAAQ,CACR,KAAM,CACN,cACJ,CAEA,6BACI,wCAAyC,CACzC,UAAW,CACX,UAAW,CACX,WAAY,CAEZ,QAAS,CACT,iBAAkB,CAClB,cAAe,CAHf,UAIJ,CAEA,oCACI,mCACJ,CAEA,2CACI,0BACJ,CAEA,4BACI,kBACJ,CAEA,mCACI,iBACJ,CAEA,SACI,uCAAwC,CACxC,kCAAmC,CAEnC,aAAc,CACd,WAAY,CAFZ,mBAGJ,CAEA,MAEI,WAAY,CADZ,UAEJ,CAEA,UAMI,kBAAmB,CALnB,gBAAiB,CACjB,iBAAkB,CAGlB,YAAa,CADb,WAAY,CAGZ,sBAAuB,CAJvB,UAKJ,CAEA,KACI,oBAAqB,CACrB,uCAAwC,CACxC,iBAAkB,CAClB,WACJ,CAEA,MACI,uCAAwC,CACxC,4BACJ,CAEA,OACI,wCAAyC,CACzC,6BACJ,CAEA,KACI,sCAAuC,CACvC,2BACJ,CAEA,QACI,yCAA0C,CAC1C,8BACJ,CAGA,iBAEI,kBAAmB,CACnB,oBAAsB,CAFtB,mBAAoB,CAIpB,gBAAkB,CAClB,eAAgB,CAFhB,qBAGJ,CAEA,2BACI,wCAAyC,CACzC,6BACJ,CAEA,yBACI,+CAAgD,CAChD,+BACJ,CAEA,yCACI,cACI,mBACJ,CACJ,CAEA,MAEE,uBAAwB,CACxB,8BAAiC,CACjC,6BAA8B,CAC9B,yBAA0B,CAC1B,yBAA0B,CAC1B,8BAA+B,CAC/B,sBAAuB,CACvB,wBAAyB,CACzB,sBAAuB,CACvB,sBAAuB,CACvB,4BAA+B,CAC/B,4BAA6B,CAC7B,uBAAwB,CACxB,uBAAwB,CACxB,oBAAqB,CACrB,uBAAkC,CAGlC,oBAAqB,CACrB,qBAAsB,CACtB,8BAA+B,CAC/B,sBAAuB,CAGvB,kBAAmB,CACnB,iBAAkB,CAClB,eAAgB,CAChB,iBAAkB,CAClB,eAAgB,CAGhB,+BAA0C,CAC1C,+BAAyC,CACzC,iCAA2C,CAG3C,sBAAuB,CACvB,wBAAyB,CACzB,sBAAuB,CAGvB,cAAe,CACf,cAAe,CACf,eAAgB,CAChB,cAAe,CACf,cACF,CAGA,mCACE,MACE,4BAA6B,CAC7B,8BAA+B,CAC/B,sBAAuB,CACvB,wBAAyB,CACzB,mBAAuB,CACvB,uBACF,CACF,CAKA,wBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAEA,sBACE,4CACF,CA/YA,mDA+YC,CA/YD,8DA+YC,CA/YD,oDA+YC,CA/YD,6EA+YC,CA/YD,mDA+YC,CA/YD,kBA+YC,CA/YD,wEA+YC,CA/YD,sDA+YC,CA/YD,mBA+YC,CA/YD,0DA+YC,CA/YD,2DA+YC,CA/YD,UA+YC,CA/YD,+CA+YC,CA/YD,8DA+YC,CA/YD,4DA+YC,CA/YD,4DA+YC,CA/YD,4DA+YC,CA/YD,4DA+YC,CA/YD,4DA+YC,CA/YD,4DA+YC,CA/YD,uFA+YC,CA/YD,iDA+YC,CA/YD,2CA+YC,CA/YD,wBA+YC,CA/YD,sDA+YC,CA/YD,2CA+YC,CA/YD,wBA+YC,CA/YD,wDA+YC,CA/YD,2CA+YC,CA/YD,wBA+YC,CA/YD,wDA+YC,CA/YD,0CA+YC,CA/YD,wBA+YC,CA/YD,wDA+YC,CA/YD,4CA+YC,CA/YD,wBA+YC,CA/YD,sDA+YC,CA/YD,8DA+YC,CA/YD,kFA+YC,CA/YD,mDA+YC,CA/YD,aA+YC,CA/YD,4CA+YC,CA/YD,+CA+YC,CA/YD,aA+YC,CA/YD,6CA+YC,CA/YD,+CA+YC,CA/YD,aA+YC,CA/YD,6CA+YC,CA/YD,kDA+YC,CA/YD,aA+YC,CA/YD,8CA+YC,CA/YD,+CA+YC,CA/YD,aA+YC,CA/YD,+CA+YC,CA/YD,+CA+YC,CA/YD,aA+YC,CA/YD,4CA+YC,CA/YD,+CA+YC,CA/YD,aA+YC,CA/YD,4CA+YC,CA/YD,+CA+YC,CA/YD,aA+YC,CA/YD,4CA+YC,CA/YD,8CA+YC,CA/YD,aA+YC,CA/YD,6CA+YC,CA/YD,8CA+YC,CA/YD,aA+YC,CA/YD,6CA+YC,CA/YD,4CA+YC,CA/YD,UA+YC,CA/YD,+CA+YC,CA/YD,sDA+YC,CA/YD,wFA+YC,CA/YD,kGA+YC,CA/YD,+CA+YC,CA/YD,kGA+YC,CA/YD,mDA+YC,CA/YD,oBA+YC,CA/YD,uDA+YC,CA/YD,8DA+YC,CA/YD,mDA+YC,CA/YD,kDA+YC,CA/YD,kBA+YC,CA/YD,+HA+YC,CA/YD,wGA+YC,CA/YD,uEA+YC,CA/YD,wFA+YC,CA/YD,+CA+YC,CA/YD,wDA+YC,CA/YD,+DA+YC,CA/YD,sDA+YC,CA/YD,yDA+YC,CA/YD,yCA+YC,CA/YD,0CA+YC,CA/YD,qDA+YC,CA/YD,8CA+YC,CA/YD,gDA+YC,CA/YD,iFA+YC,EA/YD,0FA+YC,EA/YD,mDA+YC,CA/YD,sBA+YC,CA/YD,mCA+YC,EA/YD,kDA+YC,CA/YD,4BA+YC,CA/YD,8BA+YC,CA/YD,wBA+YC,CA/YD,sBA+YC,CA/YD,oBA+YC,CA/YD,uBA+YC,CA/YD,mBA+YC,CA/YD,sBA+YC,CA/YD,oCA+YC,CA/YD,8DA+YC,CA/YD,8DA+YC,CA/YD,gCA+YC,CA/YD,mEA+YC,CA/YD,wGA+YC,CA/YD,2BA+YC,CA/YD,kBA+YC,CA/YD,8BA+YC,CA/YD,mBA+YC,EA/YD,kEA+YC,CA/YD,yCA+YC,CA/YD,oCA+YC,CA/YD,2BA+YC,CA/YD,6BA+YC,CA/YD,8DA+YC,CA/YD,8DA+YC,CA/YD,8DA+YC,CA/YD,0DA+YC,CA/YD,uDA+YC,CA/YD,gCA+YC,CA/YD,mEA+YC,CA/YD,wGA+YC,CA/YD,qBA+YC,CA/YD,+BA+YC,CA/YD,kBA+YC,CA/YD,8BA+YC,CA/YD,mBA+YC,EA/YD,mEA+YC,CA/YD,yBA+YC,CA/YD,yBA+YC,CA/YD,8DA+YC,CA/YD,8DA+YC,CA/YD,gCA+YC,EA/YD,6CA+YC,EA/YD,6GA+YC,CA/YD,oBA+YC,CA/YD,qDA+YC,CA/YD,4CA+YC,CA/YD,oBA+YC,CA/YD,sDA+YC,CA/YD,4CA+YC,CA/YD,oBA+YC,CA/YD,qDA+YC,CA/YD,4CA+YC,CA/YD,oBA+YC,CA/YD,qDA+YC,CA/YD,4CA+YC,CA/YD,oBA+YC,CA/YD,qDA+YC,CA/YD,6CA+YC,CA/YD,oBA+YC,CA/YD,sDA+YC,CA/YD,8CA+YC,CA/YD,oBA+YC,CA/YD,sDA+YC,CA/YD,2CA+YC,CA/YD,oBA+YC,CA/YD,sDA+YC,CA/YD,oCA+YC,CA/YD,wBA+YC,CA/YD,uDA+YC,CA/YD,oCA+YC,CA/YD,wBA+YC,CA/YD,uDA+YC,CA/YD,iDA+YC,CA/YD,iDA+YC,CA/YD,uCA+YC,CA/YD,wBA+YC,CA/YD,sDA+YC,CA/YD,oCA+YC,CA/YD,wBA+YC,CA/YD,qDA+YC,CA/YD,kDA+YC,CA/YD,sCA+YC,CA/YD,wBA+YC,CA/YD,sDA+YC,CA/YD,gDA+YC,CA/YD,wCA+YC,CA/YD,aA+YC,CA/YD,+CA+YC,CA/YD,wCA+YC,CA/YD,aA+YC,CA/YD,8CA+YC,CA/YD,2CA+YC,CA/YD,aA+YC,CA/YD,8CA+YC,CA/YD,wCA+YC,CA/YD,aA+YC,CA/YD,+CA+YC,CA/YD,wCA+YC,CA/YD,aA+YC,CA/YD,+CA+YC,CA/YD,yCA+YC,CA/YD,aA+YC,CA/YD,+CA+YC,CA/YD,yCA+YC,CA/YD,aA+YC,CA/YD,8CA+YC,CA/YD,0CA+YC,CA/YD,aA+YC,CA/YD,+CA+YC,CA/YD,0CA+YC,CA/YD,aA+YC,CA/YD,+CA+YC,CA/YD,uCA+YC,CA/YD,aA+YC,CA/YD,+CA+YC,CA/YD,uCA+YC,CA/YD,aA+YC,CA/YD,+CA+YC,CA/YD,iDA+YC,CA/YD,wBA+YC,CA/YD,sDA+YC,CA/YD,iDA+YC,CA/YD,wBA+YC,CA/YD,qDA+YC,CA/YD,iDA+YC,CA/YD,wBA+YC,CA/YD,qDA+YC,CA/YD,qDA+YC,CA/YD,aA+YC,CA/YD,+CA+YC,CA/YD,qDA+YC,CA/YD,aA+YC,CA/YD,+CA+YC,CA/YD,qDA+YC,CA/YD,aA+YC,CA/YD,+CA+YC,CA/YD,kDA+YC,CA/YD,UA+YC,CA/YD,+CA+YC", "sources": ["views/campaign/Campaigns.css", "ui/Alert.css", "ui/Button.css", "views/ErrorPage.css", "ui/ButtonGroup.css", "ui/Columns.css", "ui/DataTable.css", "ui/Modal.css", "ui/Heading.css", "ui/InfoTable.css", "ui/JsonPreview.css", "ui/Menu.css", "ui/form/TextInput.css", "ui/Pagination.css", "ui/Stack.css", "ui/SearchTable.css", "ui/Sidebar.css", "ui/form/RadioInput.css", "ui/Tabs.css", "ui/Tag.css", "ui/Tile.css", "views/campaign/editor/EmailEditor.css", "ui/form/Select.css", "views/users/Lists.css", "ui/premium-badge.css", "ui/form/UploadField.css", "views/campaign/ImageGalleryModal.css", "ui/Preview.css", "ui/SourceEditor.css", "views/users/RuleBuilder.css", "views/campaign/CampaignPreview.css", "views/automation/Journeys.css", "ui/CodeExample.css", "ui/PreviewImage.css", "views/automation/JourneyEditor.css", "../node_modules/reactflow/dist/style.css", "ui/form/SchemaFields.css", "views/settings/IntegrationModal.css", "views/auth/Auth.css", "views/dashboard/Dashboard.css", "../node_modules/react-big-calendar/lib/sass/reset.scss", "../node_modules/react-big-calendar/lib/css/react-big-calendar.css", "../node_modules/react-big-calendar/lib/sass/styles.scss", "../node_modules/react-big-calendar/lib/sass/toolbar.scss", "../node_modules/react-big-calendar/lib/sass/variables.scss", "../node_modules/react-big-calendar/lib/sass/event.scss", "../node_modules/react-big-calendar/lib/sass/month.scss", "../node_modules/react-big-calendar/lib/sass/agenda.scss", "../node_modules/react-big-calendar/lib/sass/time-column.scss", "../node_modules/react-big-calendar/lib/sass/time-grid.scss", "views/dashboard/CampaignCalendar.css", "ui/ProductTable.css", "views/chat/modernChat.css", "components/insights/ChatInsights.css", "views/chat/ChatPage.css", "ui/InsightCard.css", "views/insights/Insights.css", "ui/form/CheckboxInput.css", "views/products/Products.css", "views/products/ProductDetail.css", "views/products/AddProduct.css", "views/files/FileManager.css", "views/dashboard/MiniChatPage.css", "views/layouts/AppLayout.css", "views/health/SystemHealth.css", "views/competitors/Competitors.css", "views/orders/Orders.css", "ui/Toast.css", "../node_modules/toastr/build/toastr.min.css", "../node_modules/@fontsource/inter/400.css", "../node_modules/@fontsource/inter/500.css", "../node_modules/@fontsource/inter/700.css", "variables.css", "index.css"], "sourcesContent": ["/* Campaign Container Layout */\n.campaigns-container {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n  width: 100%;\n}\n\n/* Campaigns Filter and Search Layout */\n.campaigns-filter {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  padding-bottom: 0.5rem;\n  border-bottom: none;\n}\n\n.campaigns-search {\n  width: 250px;\n  position: relative;\n}\n\n.campaigns-search input {\n  width: 100%;\n  padding: 0.5rem 1rem 0.5rem 2.2rem;\n  border-radius: var(--border-radius);\n  border: 1px solid var(--color-grey);\n  background-color: var(--color-surface);\n  font-size: 0.9rem;\n}\n\n.campaigns-search input:focus {\n  border-color: var(--color-emerald-green);\n  outline: none;\n}\n\n.campaigns-search::before {\n  content: \"🔍\";\n  position: absolute;\n  left: 0.75rem;\n  top: 50%;\n  transform: translateY(-50%);\n  opacity: 0.6;\n  font-size: 0.9rem;\n}\n\n/* Campaign Grid Layout - Simplified */\n.campaigns-grid {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr); /* 4-column layout for desktop */\n  gap: 1rem;\n  width: 100%;\n  padding: 0.25rem 0;\n}\n\n/* Campaign Card Styling - Simplified */\n.campaign-card {\n  display: flex;\n  flex-direction: column;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-divider);\n  border-radius: 8px;\n  overflow: hidden;\n  transition: box-shadow 0.15s ease-in-out;\n  cursor: pointer;\n  height: 100%;\n}\n\n.campaign-card:hover {\n  box-shadow: 0 2px 8px var(--color-shadow-soft);\n}\n\n/* Campaign Card Header */\n.campaign-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem;\n  border-bottom: 1px solid var(--color-divider);\n  background-color: var(--color-background);\n}\n\n.campaign-card-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.campaign-card-icon .placeholder {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n  background-color: var(--color-background);\n  border-radius: var(--border-radius-inner);\n}\n\n/* Campaign Card Content */\n.campaign-card-content {\n  display: flex;\n  flex-direction: column;\n  padding: 0.75rem;\n  flex-grow: 1;\n  gap: 0.5rem;\n}\n\n.campaign-card-title {\n  margin-bottom: 0;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n\n.campaign-card-title h3 {\n  margin: 0;\n  font-size: 1rem;\n  font-weight: 600;\n  color: var(--color-on-background);\n  line-height: 1.3;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.campaign-card-subtitle {\n  font-size: 0.8rem;\n  color: var(--color-primary-soft);\n  margin-top: 0.25rem;\n}\n\n/* Campaign Metrics */\n.campaign-card-metrics {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 0.75rem;\n  margin: 0.25rem 0;\n}\n\n.metric-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n  min-width: 0;\n}\n\n.metric-label {\n  font-size: 0.75rem;\n  color: var(--color-primary-soft);\n  text-transform: uppercase;\n  letter-spacing: 0.02em;\n}\n\n.metric-value {\n  font-size: 0.85rem;\n  font-weight: 500;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.metric-subvalue {\n  font-size: 0.8rem;\n  color: var(--color-primary-soft);\n}\n\n/* Campaign Footer */\n.campaign-card-footer {\n  margin-top: auto;\n  padding-top: 0.5rem;\n  border-top: 1px solid var(--color-divider);\n  font-size: 0.75rem;\n  color: var(--color-primary-soft);\n}\n\n/* Loading and Empty States */\n.loading-state,\n.empty-state {\n  grid-column: 1 / -1;\n  padding: 2rem;\n  text-align: center;\n  background-color: var(--color-background);\n  border-radius: var(--border-radius);\n  color: var(--color-primary-soft);\n}\n\n/* Pagination */\n.campaigns-pagination {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0.75rem 0;\n  font-size: 0.85rem;\n  margin-top: 0.5rem;\n  border-top: 1px solid var(--color-divider);\n}\n\n.pagination-controls {\n  display: flex;\n  gap: 0.5rem;\n}\n\n.pagination-controls button {\n  padding: 0.35rem 0.7rem;\n  border-radius: var(--border-radius-inner);\n  border: 1px solid var(--color-divider);\n  background-color: var(--color-background);\n  cursor: pointer;\n  font-size: 0.8rem;\n}\n\n.pagination-controls button:hover:not(:disabled) {\n  background-color: var(--color-background-soft);\n}\n\n.pagination-controls button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* Responsive adjustments */\n@media (max-width: 1200px) {\n  .campaigns-grid {\n    grid-template-columns: repeat(3, 1fr); /* 3 columns on large screens */\n  }\n}\n\n@media (max-width: 1024px) {\n  .campaigns-grid {\n    grid-template-columns: repeat(2, 1fr); /* 2 columns on medium screens */\n  }\n}\n\n@media (max-width: 768px) {\n  .campaigns-filter {\n    flex-direction: column;\n    align-items: stretch;\n  }\n\n  .campaigns-search {\n    width: 100%;\n  }\n\n  .campaigns-grid {\n    grid-template-columns: 1fr; /* 1 column on mobile */\n  }\n}\n\n@media (max-width: 480px) {\n  .campaigns-pagination {\n    flex-direction: column;\n    gap: 0.75rem;\n  }\n}\n\n/* Campaign status tabs */\n.ui-tabs {\n  position: relative;\n  display: flex;\n  gap: 0.5rem;\n  border-bottom: none;\n}\n\n.ui-tabs::before {\n  content: '';\n  width: 100%;\n  bottom: 0;\n  height: 1px;\n  left: 0;\n  position: absolute;\n  border-bottom: 1px solid var(--color-divider);\n  z-index: 0;\n}\n\n.ui-tabs .tab {\n  background: none;\n  color: var(--color-primary-soft);\n  font-size: 0.9rem;\n  font-weight: 500;\n  padding: 0.75rem 0.5rem;\n  border: 0;\n  border-bottom: 2px solid transparent;\n  cursor: pointer;\n  text-decoration: none;\n  z-index: 10;\n  position: relative;\n}\n\n.ui-tabs .tab:hover {\n  color: var(--color-primary);\n}\n\n.ui-tabs .tab.selected {\n  color: var(--color-primary);\n  border-bottom: 2px solid var(--color-emerald-green);\n}\n\n/* Add Campaign Button */\nbutton.ui-button {\n  background-color: var(--color-emerald-green);\n  color: white;\n  border: none;\n  border-radius: var(--border-radius);\n  padding: 0.5rem 1rem;\n  font-weight: 500;\n  transition: background-color 0.2s;\n}\n\nbutton.ui-button:hover {\n  background-color: var(--color-pine-green);\n}\n\n\n/* Campaign Status Tag */\n.campaign-card-title .ui-tag {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.7rem;\n  font-weight: 500;\n  border-radius: 4px;\n  margin-left: 0.5rem;\n  white-space: nowrap;\n}\n\n.campaign-card-title .ui-tag.plain {\n  background-color: var(--color-grey-soft);\n  color: var(--color-primary-soft);\n}\n\n.campaign-card-title .ui-tag.success {\n  background-color: var(--color-green-soft);\n  color: var(--color-green-hard);\n}\n\n.campaign-card-title .ui-tag.info {\n  background-color: var(--color-blue-soft);\n  color: var(--color-blue-hard);\n}\n\n.campaign-card-title .ui-tag.error {\n  background-color: var(--color-red-soft);\n  color: var(--color-red-hard);\n} ", ".ui-alert {\n    padding: 20px;\n    border-radius: var(--border-radius);\n    margin-bottom: 5px;\n    background: var(--color-grey-soft);\n    color: var(--color-primary);\n}\n\n.ui-alert h4 {\n    margin: 0;\n}\n\n.ui-alert p {\n    margin: 5px 0;\n}\n\n.ui-alert .alert-actions {\n    margin-top: 10px;\n}\n\n.ui-alert.info {\n    background: var(--color-blue-soft);\n    color: var(--color-blue-hard);\n}\n\n.ui-alert.success {\n    background: var(--color-green-soft);\n    color: var(--color-green-hard);\n}\n\n.ui-alert.error {\n    background: var(--color-red-soft);\n    color: var(--color-red-hard);\n}\n\n.ui-alert.warn {\n    background: var(--color-yellow-soft);\n    color: var(--color-yellow-hard);\n}", ".ui-button {\n    display: inline-flex;\n    border: 1px solid transparent;\n    background: transparent;\n    border-radius: var(--border-radius);\n    padding: 10px 20px;\n    margin: 0;\n    font-weight: 500;\n    font-size: 14px;\n    line-height: 20px;\n    box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);\n    flex-shrink: 0;\n    cursor: pointer;\n    transition: .2s;\n    text-decoration: none;\n    position: relative;\n    align-items: center;\n    gap: 5px;\n}\n\n.ui-button .button-icon svg {\n    width: 16px;\n    height: 16px;\n}\n\n.ui-button .button-icon {\n    margin-left: -2px;\n    height: 20px;\n    padding: 2px 0;\n    display: inline-block;\n    flex-shrink: 0;\n}\n\n.ui-button .button-text {\n    display: inline-block;\n    flex-grow: 1;\n}\n\n.ui-button.ui-button-no-children .button-icon {\n    margin: 0;\n}\n\n.ui-button:focus {\n    outline: none;\n}\n\n.ui-button:hover {\n    transform: translateY(-1px);\n    z-index: 2;\n}\n\na.ui-button:hover {\n    text-decoration: none;\n}\n\n.ui-button:disabled {\n    cursor: not-allowed;\n    opacity: 0.5 !important;\n    color: var(--color-grey-hard);\n}\n\n.ui-button:before {\n    display: none;\n}\n\n.ui-button:disabled:hover {\n    transform: none;\n}\n\n.ui-button.is-loading {\n    padding-left: 40px;\n}\n\n.ui-button.is-loading:before {\n    content: '';\n    position: absolute;\n    display: block;\n    width: 12px;\n    height: 12px;\n    border-radius: 50%;\n    left: 15px;\n    top: 12px;\n    border: 1.5px solid var(--color-primary);\n    border-right: 1.5px solid transparent;\n    animation: rotate360 .75s infinite linear;\n}\n\n.ui-button.primary {\n    background: var(--color-primary);\n    color: var(--color-on-primary);\n    border-color: var(--color-primary);\n}\n\n.ui-button.primary:after {\n    content: '';\n    position: absolute;\n    border-radius: var(--border-radius);\n    left: -1px;\n    top: -1px;\n    right: -1px;\n    bottom: -1px;\n}\n\n.ui-button.primary.is-loading:before {\n    border-color: var(--color-on-primary);\n    border-right-color: transparent;\n}\n\n.ui-button.primary:hover:not(:disabled):after {\n    background-color: rgba(255, 255, 255, 0.2);\n}\n\n.ui-button.small {\n    padding: 5px 7px;\n    border-radius: var(--border-radius-inner);\n}\n\n.ui-button.tiny {\n    padding: 2px 3px;\n    border-radius: var(--border-radius-inner);\n}\n\n.ui-button.small.is-loading {\n    padding-left: 30px;\n}\n\n.ui-button.small.is-loading:before {\n    width: 10px;\n    height: 10px;\n    left: 10px;\n    top: 8px;\n}\n\n.ui-button.secondary {\n    background: var(--color-background);\n    border: 1px solid var(--color-grey);\n    color: var(--color-primary);\n}\n\n.ui-button.secondary:hover {\n    background: var(--color-background-soft);\n    border-color: var(--color-grey-hard);\n}\n\n.ui-button.secondary:disabled:hover {\n    background: var(--color-background);\n    border: 1px solid var(--color-grey);\n}\n\n.ui-button.destructive {\n    background: var(--color-red);\n    color: var(--color-white);\n}\n\n.ui-button.destructive:hover {\n    background: var(--color-red-hard);\n}\n\n.ui-button.plain {\n    color: var(--color-primary);\n    background: var(--color-background);\n    box-shadow: none;\n}\n\n.ui-button.plain:hover {\n    color: var(--color-primary);\n    background: var(--color-grey-soft);\n}\n\n@keyframes rotate360 {\n    100% { transform: rotate(360deg) }\n}\n\n@keyframes exist {\n    100% {\n        width: 10px;\n        height: 10px;\n    }\n}", ".error-page {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: 15px;\n}\n\n.error-page > * {\n    max-width: 640px;\n}", ".ui-button-group {\n    display: flex;\n    box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);\n    border-radius: var(--border-radius);\n}\n\n.ui-button-group .ui-button,\n.ui-button-group .ui-select .select-button,\n.ui-button-group .ui-text-input input {\n    box-shadow: none;\n    margin: 0;\n}\n\n.ui-button-group .ui-button:not(:last-child),\n.ui-button-group .ui-select:not(:last-child) .select-button,\n.ui-button-group .ui-text-input:not(:last-child) input {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n}\n\n.ui-button-group .ui-button:not(:first-child),\n.ui-button-group .ui-select:not(:first-child) .select-button,\n.ui-button-group .ui-text-input:not(:first-child) input {\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n    \n}\n\n.ui-button-group .ui-button:not(:first-child) {\n    margin-left: -1px;\n}\n\n.ui-button-group .ui-select:not(:first-child),\n.ui-button-group .ui-text-input:not(:first-child) {\n    margin-left: -1px;\n}", ".ui-columns {\n    --spacing: 20px;\n\n    display: flex;\n    flex-direction: row;\n    flex-wrap: wrap;\n    flex-grow: 1;\n    gap: var(--spacing);\n}\n\n.ui-columns > .ui-column {\n    flex-basis: 0;\n    flex-grow: 1;\n}\n\n.ui-column.fullscreen {\n    display: flex;\n    flex-direction: column;\n}\n\n@media only screen and (max-width: 600px) {\n    .ui-columns {\n        flex-direction: column;\n    }\n}", ".ui-table {\n    width: 100%;\n    display: table;\n    border-radius: var(--border-radius);\n    border: 1px solid var(--color-grey);\n    overflow: hidden;\n}\n\n.ui-table .table-row, .ui-table .table-header {\n    display: table-row;\n}\n\n.ui-table .table-header {\n    background: var(--color-background-soft);\n}\n\n.ui-table .table-row:hover .table-cell {\n    background: var(--color-background-soft);\n}\n\n.ui-table .table-row.table-row-selected .table-cell {\n    background: var(--color-grey-soft);\n}\n\n.ui-table .table-row-interactive {\n    cursor: pointer;\n}\n\n.ui-table .table-cell, .ui-table .table-header-cell {\n    display: table-cell;\n    border-bottom: 1px solid var(--color-grey);\n}\n\n.ui-table .table-header-cell {\n    padding: 10px 5px;\n    white-space: nowrap;\n    vertical-align: center;\n}\n\n.ui-table .table-header-cell .header-cell-content {\n    display: flex;\n    gap: 5px;\n    align-items: center;\n}\n\n.ui-table .table-header-cell .header-sort {\n    display: inline-flex;\n    border-radius: var(--border-radius-inner);\n    padding: 3px;\n    cursor: pointer;\n}\n\n.ui-table .table-header-cell .header-sort:hover {\n    background: var(--color-grey);\n}\n\n.ui-table .table-cell {\n    padding: 15px 5px;\n}\n\n.ui-table .table-cell:first-child, .ui-table .table-header-cell:first-child {\n    padding-left: 15px;\n}\n\n.ui-table .table-cell:last-child, .ui-table .table-header-cell:last-child {\n    padding-right: 15px;\n}\n\n.ui-table .table-row:last-child .table-cell {\n    border-bottom: 0px;\n    padding-right: 15px;\n}\n\n.ui-table .table-cell .cell-content {\n    display: flex;\n    align-items: center;\n    gap: 5px;\n}\n\n.ui-table .table-header-cell {\n    font-weight: 500;\n}\n\n.ui-table .table-cell {\n    vertical-align: middle;\n    overflow-wrap: anywhere;\n}\n\n.ui-table .table-cell .multi-cell {\n    display: grid;\n    grid-template-areas: \"image text\";\n    grid-template-columns: 50px auto;\n    align-items: center;\n    column-gap: 10px;\n}\n\n.ui-table .table-cell .multi-cell.no-image {\n    grid-template-areas: \"text text\";\n}\n\n.ui-table .table-cell .multi-cell .text {\n    grid-area: text;\n}\n\n.ui-table .table-cell .multi-cell .title {\n    font-weight: 500;\n}\n\n.ui-table .table-cell .multi-cell .subtitle {\n    font-size: 14px;\n    color: var(--color-primary-soft);\n    margin-top: 3px;\n}\n\n.ui-table .table-cell .multi-cell object,\n.ui-table .table-cell .multi-cell .placeholder {\n    background-color: var(--color-background-soft);\n    color: var(--color-grey-hard);\n    grid-area: image;\n    width: 100%;\n    height: 40px;\n    object-fit: cover;\n    object-position: 0px 0px;\n    border-radius: var(--border-radius-inner);\n}\n\n.ui-table .table-cell .multi-cell .placeholder {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n\n.ui-table .table-cell .multi-cell .placeholder .icon {\n    width: 24px;\n    height: 24px;\n}\n\n.ui-table .table-cell .loader {\n    background-color: var(--color-grey-soft);\n    height: 18px;\n    border-radius: 7px;\n    width: 100%;\n    background: linear-gradient(100deg,\n    rgba(255, 255, 255, 0) 40%,\n    rgba(255, 255, 255, .8) 50%,\n    rgba(255, 255, 255, 0) 60%) var(--color-grey-soft);\n    background-size: 200% 100%;\n    background-position-x: 180%;\n    animation: 1s loading ease-in-out infinite;\n    opacity: 0.7;\n}\n\n.ui-table .table-row.loading:hover .table-cell {\n    background: transparent;\n    cursor: default;\n}\n\n@keyframes loading {\n    to {\n        background-position-x: -30%;\n    }\n}", ".modal {\n    position: fixed;\n    height: 100vh;\n    width: 100vw;\n    top: 0;\n    left: 0;\n}\n\n.modal-wrapper {\n    position: absolute;\n    z-index: 999;\n    left: 0;\n    top: 0;\n    width: 100%;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    transition: opacity 0.2s ease;\n}\n\n.modal-inner {\n    border-radius: var(--border-radius-outer);\n    background: var(--color-background);\n    width: 100%;\n    max-width: 440px;\n    padding: 30px;\n    box-shadow: 0 20px 40px 0 rgba(0, 0, 0, 0.12);\n    max-height: 90vh;\n    overflow-y: scroll;\n    position: relative;\n}\n\n/* No margins for modals in dashboard-main */\n.dashboard-main .modal-inner {\n    margin: 0;\n}\n\n.modal.regular .modal-inner {\n    max-width: 600px;\n}\n\n.modal.large .modal-inner {\n    max-width: 960px;\n}\n\n.modal.fullscreen .modal-inner {\n    max-height: 100vh;\n    max-width: 100vw;\n    height: 100vh;\n    width: 100vw;\n    border-radius: 0;\n    padding: 0;\n    display: flex;\n    flex-direction: column;\n}\n\n.modal-inner .modal-header {\n    margin-bottom: 20px;\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n}\n\n.modal.fullscreen .modal-header {\n    margin-bottom: 0;\n}\n\n.modal-inner .modal-header > * + * {\n    margin-left: 15px;\n}\n\n.modal.fullscreen .modal-header {\n    padding: 10px 20px;\n    border-bottom: 1px solid var(--color-grey);\n}\n\n.modal.fullscreen .modal-header .modal-fullscreen-actions {\n    display: flex;\n    gap: 10px;\n    align-items: center;\n}\n\n.modal.fullscreen .modal-content {\n    flex-grow: 1;\n    position: relative;\n    overflow: auto;\n}\n\n.modal .modal-close {\n    position: absolute;\n    top: 30px;\n    right: 25px;\n}\n\n.modal .modal-close .button-icon,\n.modal .modal-close .button-icon svg {\n    width: 20px;\n    height: 20px;\n}\n\n.modal-inner .modal-header h3 {\n    margin-top: 0;\n    margin-bottom: 0;\n    flex-grow: 1;\n}\n\n.modal-inner .modal-footer {\n    margin-top: 20px;\n}\n\n.modal-overlay {\n    background: rgba(0,0,0,0.3);\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 999;\n}\n\n.modal .transition-enter {\n    transition: opacity .1s cubic-bezier(.4,0,.2,1), transform .1s ease-out;\n}\n\n.modal .transition-leave {\n    transition: opacity .1s ease-in, transform .1s ease-in;\n}\n\n.modal .transition-leave-from,\n.modal .transition-enter-to {\n    opacity: 1;\n}\n\n.modal .transition-leave-to,\n.modal .transition-enter-from {\n    opacity: 0;\n}\n\n.modal .transition-enter-from-scale {\n    transform: scale(1.05);\n}\n\n/* Add CSS for larger modal size */\n.modal.large .modal-content {\n  width: 800px;\n  max-width: 90vw;\n}\n\n/* Ensure the modal body can scroll for larger forms */\n.modal.large .modal-body {\n  max-height: 70vh;\n  overflow-y: auto;\n}", ".heading {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin: 20px 0;\n}\n\n.heading .heading-text {\n    display: flex;\n    flex-direction: column;\n}\n\n.heading h2, .heading h3, .heading h4 {\n    margin: 0;\n    flex-shrink: 0;\n}\n\n.heading .actions {\n    display: flex;\n    justify-content: flex-end;\n    align-self: start;\n    gap: 10px;\n}\n\n.heading .desc {\n    grid-area: desc;\n    margin: 5px 0 0;\n}\n\n.heading.heading-h2 {\n    margin: 0;\n    padding: 20px 0 15px;\n}\n\n.heading.heading-h3 {\n    margin: 20px 0 10px;\n}\n\n.heading.heading-h3 .actions {\n    margin: -5px 0;\n}\n\n.heading.heading-h4 {\n    margin: 10px 0;\n}\n\n.heading.heading-h4 h4 {\n    padding: 5px 0;\n}\n\n.heading label, .heading input {\n    margin: 0;\n}", ".ui-info-table {\n    border: 1px solid var(--color-grey);\n    border-radius: var(--border-radius);\n    margin-bottom: 20px;\n}\n\n.ui-info-table .info-row {\n    display: block;\n    padding: 15px;\n    display: flex;\n    justify-content: space-between;\n    border-bottom: 1px solid var(--color-grey);\n    gap: 20px;\n}\n\n.ui-info-table .info-label {\n    min-width: 25%;\n}\n\n.ui-info-table .info-value {\n    text-align: right;\n    position: relative;\n}\n\n.ui-info-table .info-value .ui-tag {\n    margin-top: -5px;\n    margin-bottom: -5px;\n}\n\n.ui-info-table .info-row:last-child {\n    border-bottom-width: 0px;\n}\n\n.ui-info-table.horizontal {\n    display: flex;\n    border: 0;\n    column-gap: 20px;\n    margin-bottom: 0;\n    flex-wrap: wrap;\n}\n\n.ui-info-table.horizontal .info-row {\n    border-bottom: 0px;\n    padding: 5px 0;\n    gap: 10px;\n    align-items: center;\n}\n\n.ui-info-table.horizontal .info-row:not(:last-child):after {\n    content: \"\";\n    border-right: 1px solid var(--color-grey-hard);\n    align-self: stretch;\n    padding-left: 10px;\n}\n\n.ui-info-table.horizontal .info-row:last-child {\n    border-right-width: 0px;\n}\n\n.ui-info-table.horizontal .info-row .info-label {\n    min-width: auto;\n}\n\n.ui-info-table.horizontal .info-row .info-value {\n    font-weight: 500;\n}\n\n", ".json-viewer-theme-dark {\n    background: transparent !important;\n}", ".ui-menu {\n    background: var(--color-background);\n    border: 1px solid var(--color-grey);\n    border-radius: var(--border-radius);\n    box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.1) 0px 4px 6px -4px;\n    z-index: 999;\n    min-width: 150px;\n    padding: 3px 0;\n    font-size: 15px;\n    overflow: hidden;\n}\n\n.ui-menu-item {\n    padding: 7px 10px;\n    display: flex;\n    gap: 5px;\n    align-items: center;\n    cursor: pointer;\n}\n\n.ui-menu-item:last-child {\n    border-bottom-width: 0px;\n}\n\n.ui-menu-item:hover {\n    background: var(--color-background-soft);\n}\n\n.ui-menu-item svg {\n    width: 16px;\n    height: 16px;\n    margin-top: -1px;\n    margin-right: 5px;\n}", ".ui-text-input.disabled input,\n.ui-text-input.disabled textarea {\n    cursor: not-allowed;\n    opacity: 0.5 !important;\n}\n\n\n.ui-text-input.disabled input:hover,\n.ui-text-input.disabled textarea:hover {\n    border-color: var(--color-grey);\n}\n\n.ui-text-input-icon-wrapper {\n    position: relative;\n}\n\n.ui-text-input-icon-wrapper input,\n.ui-text-input-icon-wrapper textarea {\n    padding-left: 36px;\n}\n\n.ui-text-input-icon {\n    position: absolute;\n    top: 50%;\n    left: 12px;\n    height: 16px;\n    width: 16px;\n    transform: translate(0, -50%);\n}\n\n.ui-text-input-suffix-wrapper {\n    width: 100%;\n    display: flex;\n}\n\n.ui-text-input-suffix-wrapper input {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n    width: auto;\n    flex-grow: 1;\n}\n\n.ui-text-input-suffix {\n    border: 1px solid var(--color-grey);\n    border-left-width: 0;\n    border-top-right-radius: var(--border-radius);\n    border-bottom-right-radius: var(--border-radius);\n    padding: 12px 15px;\n}", ".ui-pagination {\n    background: var(--color-background);\n    border: 1px solid var(--color-grey);\n    display: inline-flex;\n    border-radius: var(--border-radius);\n    box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);\n    margin-top: 10px;\n    overflow: hidden;\n}\n\n.ui-pagination > * {\n    padding: 10px 15px;\n    border: 0;\n    border-right: 1px solid var(--color-grey);\n}\n\n.ui-pagination .pagination-button {\n    background: var(--color-background);\n    font-size: 16px;\n    line-height: 15px;\n    cursor: pointer;\n    text-decoration: none;\n    color: var(--color-primary);\n    margin: 0;\n}\n\n.ui-pagination .pagination-button:hover {\n    background: var(--color-grey-soft);\n}\n\n.ui-pagination .pagination-button:disabled {\n    color: var(--color-primary-soft);\n    cursor: not-allowed;\n}\n\n.ui-pagination .pagination-button:disabled:hover {\n    background: var(--color-background);\n}\n\n.ui-pagination .pagination-button.selected {\n    background: var(--color-grey-soft);\n    font-weight: 500;\n}\n\n.ui-pagination .prev,\n.ui-pagination .next {\n    display: flex;\n    align-items: center;\n    gap: 5px;\n}\n\n.ui-pagination .prev svg,\n.ui-pagination .next svg {\n    width: 16px;\n}\n\n.ui-pagination .next {\n    border-right-width: 0px;\n}\n\n.ui-pagination .next svg {\n    width: 16px;\n}\n", ".ui-stack {\n    --spacing: 10px;\n    display: flex;\n    flex-direction: row;\n    gap: var(--spacing);\n    margin-bottom: var(--spacing);\n}\n\n.ui-stack.ui-stack-vertical {\n    flex-direction: column;\n}\n", "/* Empty table container - holds both the header table and the empty state */\n.empty-table-container {\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n}\n\n/* Clean empty state container with header row */\n.clean-empty-state-container {\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  border: 1px solid var(--color-divider);\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n/* Table header row styling */\n.table-header-row {\n  display: flex;\n  width: 100%;\n  background: var(--color-surface);\n  border-bottom: 1px solid var(--color-divider);\n  font-weight: 600;\n  color: var(--color-text);\n  padding: 10px 16px;\n}\n\n.table-header-cell {\n  padding: 0 8px;\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n/* Clean empty state that appears below headers */\n.clean-empty-state {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 24px 20px;\n  background: var(--color-surface-elevated);\n  min-height: 120px;\n  width: 100%;\n}\n\n.empty-state-content {\n  max-width: 500px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  margin: 0 auto;\n}\n\n.empty-state-icon {\n  margin-bottom: 12px;\n}\n\n.empty-state-icon img {\n  height: 120px;\n  width: auto;\n  filter: drop-shadow(0 4px 6px var(--color-primary-shadow));\n}\n\n.empty-state-title {\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 8px;\n  color: var(--color-text);\n}\n\n.empty-state-message {\n  color: var(--color-text-subtle);\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n/* Make DataTable's empty message span all columns */\n[data-ui-component=\"DataTable\"] td[colspan=\"1\"][class*=\"emptycell\"] {\n  text-align: center !important;\n  padding: 0 !important;\n}\n\n[data-ui-component=\"DataTable\"] td[colspan=\"1\"][class*=\"emptycell\"] > div {\n  width: 100%;\n}\n\n/* Hacky but necessary - force the colspan to span all columns */\n[data-ui-component=\"DataTable\"] td[colspan=\"1\"][class*=\"emptycell\"] {\n  display: table-cell;\n  width: 100%;\n  position: absolute;\n  left: 0;\n  right: 0;\n}\n\n/* Responsive styles */\n@media (max-width: 768px) {\n  .clean-empty-state {\n    padding: 20px 15px;\n  }\n  \n  .empty-state-icon img {\n     height: 100px;\n  }\n  \n  .empty-state-title {\n    font-size: 16px;\n  }\n  \n  .empty-state-message {\n    font-size: 13px;\n  }\n}\n\n/* Clean empty table styling */\n.clean-empty-table {\n  width: 100%;\n}\n\n.clean-empty-table .data-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.clean-empty-table th {\n  padding: 1rem;\n  text-align: left;\n  border-bottom: 1px solid #e5e7eb;\n  font-weight: 500;\n}\n\n.clean-empty-table .sortable {\n  cursor: pointer;\n}\n\n.clean-empty-table .table-header-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.clean-empty-table .empty-state-cell {\n  padding: 3rem 1rem;\n  text-align: center;\n}\n\n.clean-empty-table .empty-state-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n}\n\n.clean-empty-table .empty-state-icon {\n  width: 80px;\n  height: 80px;\n  margin-bottom: 1rem;\n}\n\n.clean-empty-table .empty-state-icon img {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n}\n\n.clean-empty-table .empty-state-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin: 0;\n  color: #4b5563;\n}\n\n.clean-empty-table .empty-state-message {\n  color: #6b7280;\n  max-width: 500px;\n  margin: 0 auto;\n}\n\n/* New footer styles */\n.search-table-footer {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-top: 1rem;\n}\n\n.search-table-total-count {\n  font-size: 0.875rem;\n  color: #6b7280;\n  padding: 0.5rem 0;\n}\n\n/* Make the footer responsive */\n@media (max-width: 640px) {\n  .search-table-footer {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n} ", "/* ::-webkit-scrollbar {\n    display: none;\n} */\n\n/* Firefox\n* {\n    scrollbar-width: none;\n} */\n\n/* IE/Edge */\n* {\n    /* -ms-overflow-style: none; */\n}\n\n/* Sidebar component styles */\n.sidebar {\n    /* width: 100%; */\n    /* height: calc(100vh - 70px); Make sidebar full viewport height */\n    /* background: var(--color-background);\n    display: flex;\n    flex-direction: column;\n    transition: width 0.3s ease;\n    position: relative;\n    overflow-y: auto; Allow scrolling within sidebar */\n    height: 100vh;\n    display: flex;\n    flex-direction: column;\n}\n\n/* Add new nav container styling */\n.nav-container {\n    flex: 0 1 auto; /* Changed from flex: 1 to not take all available space */\n    overflow-y: auto;\n    margin-bottom: 10px; /* Add space before the footer */\n    padding-bottom: 10px; /* Reduced padding */\n}\n\n/* Collapsed sidebar styles */\n.sidebar.collapsed {\n    width: 100%; /* Will be constrained by the parent container */\n}\n\n.sidebar.collapsed .sidebar-header .logo {\n    width: 100%;\n    display: flex;\n    justify-content: center;\n}\n\n.sidebar.collapsed .sidebar-header .logo img {\n    height: 30px;\n    width: auto;\n}\n\n.sidebar .sidebar-header {\n    display: flex;\n    position: relative;\n    border-bottom: 1px solid var(--color-grey);\n    padding: 15px 20px;\n    gap: 10px;\n    align-items: center;\n}\n\n.sidebar-header .logo {\n    display: flex;\n    align-items: center;\n}\n\n.sidebar-header .logo svg {\n    height: 30px;\n    flex-shrink: 0;\n    fill: var(--color-primary);\n}\n\n/* Navigation styles */\nnav {\n    padding: 20px;\n    width: 100%;\n}\n\nnav.nav-collapsed {\n    padding: 20px 0;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n}\n\nnav a {\n    display: flex;\n    padding: 15px;\n    text-decoration: none;\n    color: var(--color-primary);\n    border-radius: var(--border-radius);\n    font-size: 15px;\n    font-weight: 500;\n    margin-bottom: 5px;\n    align-items: center;\n    gap: 5px;\n}\n\nnav a.nav-link-collapsed {\n    padding: 15px 0;\n    width: 100%;\n    display: flex;\n    justify-content: center;\n    margin-bottom: 10px;\n}\n\nnav a.nav-link-collapsed span {\n    display: none;\n    opacity: 0;\n    visibility: hidden;\n    max-width: 0;\n    overflow: hidden;\n}\n\nnav a.nav-link-collapsed .nav-icon {\n    width: 20px;\n    height: 20px;\n    margin-right: 0;\n}\n\n/* Enhanced hover styles */\nnav a:hover {\n    color: var(--color-primary);\n    /* background: var(--color-grey); */\n    /* text-decoration: none; */\n}\n\n/* nav a.selected {\n    color: var(--color-on-primary);\n    background: var(--color-primary);\n} */\n\nnav a .nav-icon {\n    width: 16px;\n    height: 16px;\n    display: flex;\n    margin-right: 4px;\n}\n\n/* Smokey Budtender Download Section */\n.smokey-download-section {\n    margin: 5px 20px 15px 20px; /* Adjusted margins */\n    border-radius: var(--border-radius);\n    overflow: hidden;\n    border: 1px solid var(--color-grey);\n    transition: all 0.2s ease;\n    flex: 0 0 auto; /* Don't grow or shrink */\n    position: relative; /* Ensure it stays in document flow */\n}\n\n.smokey-download-section:hover {\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    transform: translateY(-1px);\n    cursor: pointer;\n}\n\n.smokey-download-content {\n    display: flex;\n    align-items: center;\n    padding: 12px 15px;\n}\n\n.smokey-icon-wrapper {\n    position: relative;\n}\n\n.smokey-icon {\n    width: 40px;\n    height: 40px;\n    object-fit: contain;\n    margin-right: 12px;\n}\n\n.download-icon-overlay {\n    position: absolute;\n    bottom: -4px;\n    right: 6px;\n    background:  #3EDC81;\n    color: white;\n    border-radius: 50%;\n    width: 20px;\n    height: 20px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-size: 12px;\n    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n.download-icon-overlay svg {\n    width: 12px;\n    height: 12px;\n}\n\n.smokey-text {\n    flex: 1;\n}\n\n.smokey-text h4 {\n    margin: 0 0 4px 0;\n    font-size: 14px;\n    font-weight: 600;\n    color: var(--color-primary);\n}\n\n.smokey-text p {\n    margin: 0;\n    font-size: 12px;\n    color: var(--color-text-soft);\n}\n\n/* Collapsed Smokey Download Section */\n.smokey-download-collapsed {\n    margin: 5px auto 15px auto; /* Adjusted margins */\n    width: 50px;\n    display: flex;\n    justify-content: center;\n}\n\n.smokey-download-content-collapsed {\n    display: flex;\n    justify-content: center;\n    padding: 10px;\n}\n\n.smokey-download-collapsed .smokey-icon {\n    margin-right: 0;\n}\n\n.smokey-download-collapsed .smokey-icon-wrapper {\n    position: relative;\n}\n\n.smokey-download-collapsed .download-icon-overlay {\n    bottom: -2px;\n    right: -2px;\n    width: 16px;\n    height: 16px;\n}\n\n.smokey-download-collapsed .download-icon-overlay svg {\n    width: 10px;\n    height: 10px;\n}\n\n/* Sidebar footer section */\n.sidebar-footer {\n    border-top: 1px solid var(--color-grey);\n    width: 100%;\n    margin-top: auto; /* Push to bottom */\n    position: sticky;\n    bottom: 0;\n    background: var(--color-background); /* Match sidebar background */\n    z-index: 0; /* Ensure it stays on top of scrollable content */\n    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05); /* Subtle shadow for distinction */\n}\n\n/* Sidebar profile section */\n.sidebar-profile {\n    width: 100%;\n}\n\n.sidebar-profile-collapsed {\n    display: flex;\n    justify-content: center;\n    padding: 15px 0;\n}\n\n.sidebar-profile .sidebar-profile-inner {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    width: 100%;\n    overflow: hidden;\n    padding: 15px 20px;\n    height: 50px;\n}\n\n.sidebar-profile .sidebar-profile-inner:hover {\n    background-color: var(--color-background-soft);\n}\n\n.sidebar-profile .profile-image {\n    flex-shrink: 0;\n    margin-right: 8px;\n    width: 40px;\n    height: 40px;\n    background: var(--color-grey);\n    border-radius: 20px;\n    grid-area: image;\n    overflow: hidden;\n}\n\n.sidebar-profile-collapsed .profile-image {\n    margin-right: 0;\n}\n\n.sidebar-profile .profile-image img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n}\n\n.sidebar-profile .profile-name {\n    flex-grow: 1;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    grid-area: name;\n    font-size: 14px;\n    padding-left: 10px;\n}\n\n.sidebar-profile .profile-role {\n    flex-shrink: 0;\n    margin-left: 8px;\n    grid-area: role;\n    font-size: 13px;\n    font-weight: 500;\n    display: flex;\n    padding-left: 10px;\n}\n\n.sidebar-profile .profile-caret {\n    flex-shrink: 0;\n    margin-left: 8px;\n    grid-area: caret;\n}\n\n/* Chat history submenu styles */\n.chat-history-submenu {\n  padding-left: 20px;\n  display: none;\n}\n\n@media (max-width: 767px) {\n  /* .chat-history-submenu {\n    display: block;\n  } */\n}\n\n.chat-history-toggle {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  padding: 10px 15px;\n  color: var(--color-primary-soft);\n  background: none;\n  border: none;\n  font-size: 14px;\n  cursor: pointer;\n}\n\n.chat-history-list {\n  padding-left: 10px;\n}\n\n.chat-history-list a {\n  padding: 8px 15px;\n  font-size: 14px;\n}\n\n/* Trial Countdown Styles */\n.trial-countdown-section {\n  background: linear-gradient(135deg, rgba(70, 60, 255, 0.1), rgba(70, 60, 255, 0.05));\n  border: 1px solid rgba(70, 60, 255, 0.2);\n  border-radius: 8px;\n  padding: 10px;\n  margin: 10px;\n  text-align: center;\n}\n\n.trial-header {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 5px;\n}\n\n.trial-heading {\n  font-size: 12px;\n  font-weight: 600;\n  color: var(--text-color);\n  margin: 0;\n  display: inline;\n}\n\n.trial-countdown-timer {\n  display: flex;\n  justify-content: center;\n  gap: 8px;\n  margin-bottom: 8px;\n}\n\n.time-value {\n  font-size: 14px;\n  font-weight: 600;\n  color: var(--primary-color);\n  background: rgba(70, 60, 255, 0.1);\n  padding: 2px 5px;\n  border-radius: 4px;\n}\n\n.trial-expired {\n  color: var(--danger-color);\n  font-weight: 600;\n  display: block;\n  margin-bottom: 5px;\n}\n\n.upgrade-button {\n  margin-top: 5px !important;\n  font-size: 12px !important;\n  padding: 5px 10px !important;\n  height: auto !important;\n}\n\n.trial-countdown-collapsed {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 10px 0;\n  margin: 10px 0;\n  background: linear-gradient(135deg, rgba(70, 60, 255, 0.1), rgba(70, 60, 255, 0.05));\n  border-radius: 8px;\n  font-size: 12px;\n  font-weight: 600;\n  color: var(--primary-color);\n}\n\n.trial-icon {\n  margin-right: 4px;\n}\n\n/* Trial Expiry Modal Styles */\n.trial-expiry-modal {\n  padding: 10px;\n  text-align: center;\n}\n\n.trial-expiry-modal p {\n  margin-bottom: 15px;\n  line-height: 1.5;\n  color: var(--text-color);\n}\n\n.expiry-countdown {\n  background: rgba(70, 60, 255, 0.05);\n  border-radius: 8px;\n  padding: 15px;\n  margin: 15px 0;\n}\n\n.expiry-countdown p {\n  margin-bottom: 10px;\n  font-weight: 500;\n}\n\n.expiry-modal-actions {\n  display: flex;\n  justify-content: center;\n  gap: 10px;\n  margin-top: 20px;\n}\n\n", ".options-group {\n    padding-bottom: 5px;\n}\n\n.options-group .options {\n    width: 100%;\n    display: flex;\n    gap: 10px;\n    border: 1px solid var(--color-grey);\n    border-radius: var(--border-radius);\n    padding: 5px;\n    box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);\n}\n\n.options-group .options label {\n    margin: 0;\n}\n\n.options-group .option {\n    flex-grow: 1;\n    border-radius: var(--border-radius-inner);\n    padding: 8px 10px;\n    cursor: pointer;\n    text-align: center;\n}\n\n.options-group .label-subtitle {\n    font-weight: 400;\n    font-size: 14px;\n    margin-bottom: 5px;\n    display: block;\n}\n\n.options-group .option:hover:not(.disabled):not(.selected) {\n    background: var(--color-grey-soft);\n    border-color: var(--color-grey-hard);\n}\n\n.options-group .option.selected {\n    background: var(--color-primary);\n    color: var(--color-on-primary);\n    border-color: var(--color-primary);\n}\n\n.options-group .option.disabled {\n    cursor: not-allowed;\n}", ".ui-tabs {\n    position: relative;\n}\n\n.ui-tabs::before {\n    content: '';\n    width: 100%;\n    bottom: 0;\n    height: 1px;\n    left: 0;\n    position: absolute;\n    border-bottom: 1px solid var(--color-grey);\n    z-index: 0;\n}\n\n.ui-tabs .tab {\n    background: none;\n    color: var(--color-primary-soft);\n    font-size: 15px;\n    margin-right: 20px;\n    font-weight: 500;\n    padding: 12px 5px;\n    display: inline-block;\n    border: 0;\n    border-bottom: 3px solid transparent;\n    cursor: pointer;\n    text-decoration: none;\n    z-index: 10;\n    position: relative;\n}\n\n.ui-tabs .tab:hover {\n    color: var(--color-primary);\n    border-bottom: 3px solid var(--color-grey);\n}\n\n.ui-tabs .tab.selected {\n    color: var(--color-primary);\n    border-bottom: 3px solid var(--color-on-background);\n}\n\n.ui-tabs-panels .panel {\n    width: 100%;\n    display: flex;\n}\n", ".ui-tag {\n    padding: 5px 8px;\n    font-size: 15px;\n    line-height: 20px;\n    border-radius: var(--border-radius);\n    display: inline-flex;\n    gap: 5px;\n    align-items: center;\n    background: var(--color-grey-soft);\n    color: var(--color-primary);\n    font-weight: 500;\n    overflow-wrap: break-word;\n}\n\n.ui-tag svg {\n    width: 16px;\n    height: 16px;\n}\n\n.ui-tag.info {\n    background: var(--color-blue-soft);\n    color: var(--color-blue-hard);\n}\n\n.ui-tag.success {\n    background: var(--color-green-soft);\n    color: var(--color-green-hard);\n}\n\n.ui-tag.error {\n    background: var(--color-red-soft);\n    color: var(--color-red-hard);\n}\n\n.ui-tag.warn {\n    background: var(--color-yellow-soft);\n    color: var(--color-yellow-hard);\n}\n\n.ui-tag-group {\n    display: inline-flex;\n    align-items: center;\n    gap: 5px;\n}\n\n.ui-tag.tiny {\n    font-size: 12px;\n    padding: 2px 4px;\n    gap: 2px;\n    border-radius: var(--border-radius-inner);\n}\n\n.ui-tag.large {\n    padding: 10px;\n}\n\n.ui-tag.tiny .icon {\n    width: 12px;\n    height: 12px;\n}", ".ui-tile-grid {\n    display: grid;\n    gap: 10px;\n    margin: 15px 0;\n}\n\n.ui-tile {\n    border: 1px solid var(--color-grey);\n    border-radius: var(--border-radius);\n    padding: 20px;\n    transition: box-shadow ease-in .1s;\n    display: flex;\n    gap: 10px;\n    align-items: center;\n}\n\n.ui-tile.interactive { cursor: pointer; }\n.ui-tile.interactive:hover {\n    box-shadow: 0 3px 10px rgba(0,0,0,0.1);\n}\n\n.ui-tile.selected {\n    border: 1px solid var(--color-primary);\n}\n\n.ui-tile img {\n    border-radius: var(--border-radius);\n    width: 100%;\n    max-width: 40px;\n    flex-shrink: 0;\n}\n\n.ui-tile-text {\n    flex-grow: 1;\n}\n\n.ui-tile h5 {\n    margin: 0 0 5px;\n}\n\n.ui-tile p {\n    font-size: 13px;\n    margin: 0;\n    color: var(--color-primary-soft);\n}\n\n.ui-tile.large {\n    padding: 30px 20px;\n}\n\n.ui-tile.large h5 {\n    font-size: 30px;\n    margin: 0 0 10px;\n}\n\n@media only screen and (max-width: 600px) {\n    .ui-tile-grid {\n        grid-template-columns: 1fr !important;\n    }\n}", ".email-editor {\n    background: var(--color-background);\n    position: absolute;\n    display: flex;\n    left: 0;\n    right: 0;\n    top: 0;\n    bottom: 0;\n    z-index: 1;\n}\n\n.template-editor.inline {\n    position: relative;\n    top: unset;\n    left: unset;\n    right: unset;\n    bottom: unset;\n}\n\n.email-editor-header {\n    display: flex;\n    gap: 20px;\n    border-bottom: 1px solid var(--color-grey);\n    padding: 10px 20px;\n    align-items: center;\n}\n\n.email-editor-header .header-exit {\n    border-right: 1px solid var(--color-grey);\n    height: calc(100% + 20px);\n    margin-top: -10px;\n    font-size: 30px;\n    color: var(--color-grey-hard);\n    padding: 0 20px 0 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.email-editor-header h3 {\n    padding: 0;\n    margin: 0;\n    flex-grow: 1;\n}\n\n.email-editor-footer {\n    background: var(--color-background);\n    position: fixed;\n    bottom: 10px;\n    right: 10px;\n    border-radius: var(--border-radius-outer);\n    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);\n    padding: 10px;\n    display: flex;\n    gap: 20px;\n    align-items: center;\n}\n\n.email-editor-footer .publish-details {\n    padding-left: 5px;\n}\n\n.email-editor-footer .publish-details > * {\n    display: block;\n}\n\n.email-editor-footer .publish-details .publish-label {\n    text-transform: uppercase;\n    font-size: 11px;\n    font-weight: 500;\n}\n\n.email-editor .ui-tabs {\n    padding-left: 20px;\n    padding-right: 20px;\n    margin-top: 0;\n    flex-grow: 0;\n}\n\n.email-editor .ui-tabs-panels {\n    display: flex;\n    flex-grow: 1;\n}\n\n.editor-html {\n    width: 100%;\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: 20px;\n    min-height: 300px;\n    position: relative;\n    overflow: auto;\n    padding: 20px;\n}\n\n.source-editor {    \n    background: var(--color-editor);\n    border-radius: var(--border-radius);\n    border: 1px solid #1a1f2b;\n    overflow: hidden;\n    padding: 0;\n    display: flex;\n    flex-direction: column;\n}\n\n.source-editor .ui-tabs {\n    border-bottom: 0;\n}\n\n.source-editor .ui-tabs .tab {\n    color: var(--color-white);\n}\n\n.source-editor .ui-tabs .tab.selected,\n.source-editor .ui-tabs .tab:hover {\n    color: var(--color-white);\n    border-color: var(--color-white);\n}\n\n.source-editor .ui-tabs-panels .panel {\n    flex-direction: column;\n}\n\n.source-editor .editor-toolbar {\n    display: flex;\n    justify-content: flex-end;\n    gap: 10px;\n    padding: 10px;\n}\n\n.source-preview {\n    display: grid;\n}\n\n.source-preview, .source-preview iframe {\n    border-radius: var(--border-radius);\n}\n\n.source-preview iframe {\n    width: 100%;\n    height: 100%;\n}", ".ui-select {\n    position: relative;\n}\n\n.ui-select svg {\n    width: 1.25rem;\n    height: 1.25rem;\n}\n\n.ui-select .select-button {\n    background: var(--color-background);\n    appearance: none;\n    border: 1px solid transparent;\n    padding: 12px 15px;\n    width: 100%;\n    margin: 0;\n    color: var(--color-primary);\n    font-weight: 400;\n    font-size: 14px;\n    line-height: 20px;\n    cursor: pointer;\n    position: relative;\n    outline-color: var(--color-blue);\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    gap: 5px;\n}\n\n.ui-select.plain .select-button {\n    border-color: var(--color-grey);\n    box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);\n    border-radius: var(--border-radius);\n}\n\n.ui-select.plain .select-button:hover {\n    border-color: var(--color-grey-hard);\n    z-index: 2;\n}\n\n.ui-select.minimal .select-button:hover {\n    background-color: var(--color-background-soft);\n}\n\n.ui-select .select-button.small {\n    padding: 5px 7px;\n    border-radius: var(--border-radius-inner);\n}\n\n.ui-select .select-button.small svg {\n    width: 1rem;\n    height: 1rem;\n}\n\n.ui-select .select-button .select-button-label {\n    flex-grow: 2;\n    display: block;\n    text-align: left;\n}\n\n.ui-select .select-button .select-button-icon {\n    display: inline-flex;\n    justify-content: center;\n    margin-top: 1px;\n}\n\n.ui-select .transition-enter {\n    transition: opacity .05s ease-out;\n}\n\n.ui-select .transition-leave {\n    transition: opacity .05s ease-in;\n}\n\n.ui-select .transition-leave-from,\n.ui-select .transition-enter-to {\n    opacity: 1;\n}\n\n.ui-select .transition-leave-to,\n.ui-select .transition-enter-from {\n    opacity: 0;\n}\n\n.ui-select .select-options-wrapper {\n    z-index: 999;\n}\n\n.ui-select .select-options {\n    background: var(--color-background);\n    border: 1px solid var(--color-grey);\n    box-shadow: 0px 1px 5px rgba(16, 24, 40, 0.1);\n    border-radius: var(--border-radius);\n    list-style: none;\n    padding: 5px;\n    max-height: 275px;\n    overflow: scroll;\n    margin: 0;\n    z-index: 999;\n}\n\n.ui-select .select-option {\n    padding: 12px 10px;\n    list-style: none;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    cursor: pointer;\n    border-radius: var(--border-radius-inner);\n    font-weight: 500;\n    margin: 3px 0;\n    gap: 10px;\n}\n\n.ui-select .select-option:first-child {\n    margin-top: 0;\n}\n\n.ui-select .select-option:last-child {\n    margin-bottom: 0;\n}\n\n.ui-select .select-option span {\n    line-height: 20px;\n}\n\n.ui-select .select-option .option-icon {\n    display: none;\n}\n\n.ui-select .select-option.active {\n    background: var(--color-background-soft);\n}\n\n.ui-select .select-option.selected {\n    background: var(--color-grey-soft);\n}\n\n.ui-select .select-option.selected .option-icon {\n    display: block;\n    height: 18px;\n}\n\n.ui-select .select-option.selected svg {\n    height: 15px;\n    width: 15px;\n}\n\n.ui-select .select-option.disabled {\n    color: var(--color-grey-hard);\n    cursor: not-allowed;\n}\n", ".button-wrapper {\n  position: relative;\n  display: inline-block;\n}\n\n.button-wrapper .premium-badge {\n  position: absolute;\n  top: -10px;\n  right: -10px;\n  z-index: 1;\n} ", ".button-wrapper {\n  position: relative;\n  display: inline-block;\n}\n\n.button-wrapper .premium-badge {\n  position: absolute;\n  top: -8px;\n  right: -30px;\n  z-index: 2;\n  background: #ff7b00;\n  color: white;\n  padding: 4px 12px;\n  font-size: 12px;\n  font-weight: bold;\n  border-radius: 4px;\n  transform: rotate(20deg);\n  box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n  opacity: 0.95;\n} ", ".ui-upload-field {\n    border: 1px solid var(--color-grey);\n    border-radius: var(--border-radius);\n    padding: 12px;\n}\n\n.ui-upload-field.highlighted {\n    border: 1px solid var(--color-grey-hard);\n}\n\n.ui-upload-field span {\n    font-weight: 600;\n}\n\n.ui-upload-field input {\n    display: none;\n}\n\n.ui-upload-field p {\n    margin: 10px 0;\n}\n\n.ui-upload-field .upload-icon {\n    font-size: 24px;\n}", ".image-gallery .images {\n    display: grid;\n    grid-template-columns: repeat(5, 1fr);\n    gap: 20px;\n    place-items: start;\n    padding: 20px 0;\n}\n\n.image-gallery .image {\n    width: 100%;\n    position: relative;\n    display: flex;\n    aspect-ratio: 1;\n    overflow: hidden;\n    border-radius: var(--border-radius-inner);\n    cursor: pointer;\n    \n}\n\n.image-gallery .image:after {\n    content: '';\n    background: rgba(255,255,255,0.2);\n    position: absolute;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    top: 0;\n    border: 3px solid var(--color-primary);\n    border-radius: var(--border-radius-inner);\n    opacity: 0;\n    transition: all ease-in-out 0.25s;\n}\n\n.image-gallery .image:hover:after {\n    opacity: 1;\n}\n\n.image-gallery .image img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n}", ".preview {\n    background: var(--color-white);\n    border-radius: var(--border-radius);\n    overflow: hidden;\n    display: flex;\n    justify-content: center;\n    align-items: stretch;\n    border: 1px solid var(--color-grey);\n    flex-grow: 1;\n    max-height: 600px;\n}\n\n.preview iframe {\n    overflow: auto;\n    min-height: 500px;\n    flex-grow: 1;\n}\n\n.email-frame {\n    display: flex;\n    flex-direction: column;\n    width: 100%;\n    overflow: auto;\n    max-height: 600px;\n}\n\n.email-frame-header {\n    background: var(--color-white);\n    color: var(--color-black);\n    padding: 20px;\n}\n\n.email-frame-header .email-from {\n    font-weight: bold;\n}\n\n.email-frame-header span {\n    display: block;\n}\n\n.email-frame iframe {\n    width: 100%;\n    border-radius: 0;\n    max-height: 520px;\n    overflow: auto;\n}\n\n.email-frame iframe img {\n    max-width: 100%;\n    height: auto;\n}\n\n.phone-frame {\n    margin: 40px;\n    display: flex;\n    max-width: 320px;\n    width: 100%;\n    min-height: 600px;\n    border-radius: 40px;\n    border: 4px solid var(--color-primary);\n    overflow: hidden;\n}\n\n.text-frame {\n    background-color: var(--color-background);\n    justify-content: flex-start;\n    flex-direction: column;\n    gap: 15px;\n}\n\n.text-frame .text-frame-header {\n    width: 100%;\n    height: 80px;\n    background: var(--color-background-soft);\n    border-bottom: 1px solid var(--color-grey-soft);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n\n.text-frame .text-frame-header .text-frame-profile-image {\n    background: var(--color-grey-hard);\n    width: 40px;\n    height: 40px;\n    border-radius: 20px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding-top: 10px;\n    overflow: hidden;\n    border: 2px solid var(--color-grey-hard);\n}\n\n.text-frame .text-frame-header .text-frame-profile-image {\n    font-size: 40px;\n    color: var(--color-background);\n}\n\n.text-frame .text-frame-context {\n    font-size: 12px;\n    text-align: center;\n}\n\n.text-frame .text-bubble {\n    width: calc(100% - 30px);\n    background: var(--color-grey-soft);\n    border-radius: 20px;\n    padding: 15px;\n    margin: 0 15px;\n    font-size: 14px;\n}\n\n.push-frame {\n    background: rgb(2,0,36);\n    background: linear-gradient(45deg, rgba(2,0,36,1) 0%, rgba(9,9,121,1) 35%, rgba(0,212,255,1) 100%);\n    align-items: center;\n    justify-content: center;\n}\n\n.push-notification {\n    width: 100%;\n    background: rgba(0,0,0,.5);\n    color: var(--color-white);\n    border-radius: 20px;\n    padding: 15px;\n    margin: 15px;\n    display: grid;\n    grid-template-columns: 40px auto;\n    grid-template-rows: auto auto;\n    grid-template-areas: \"icon header\" \"icon body\";\n    gap: 10px;\n    font-size: 13px;\n}\n\n.push-notification .notification-icon {\n    grid-area: icon;\n    width: 40px;\n    height: 40px;\n    background: rgba(255,255,255,0.3);\n    border-radius: 10px;\n    align-self: center;\n}\n\n.push-notification .notification-header {\n    grid-area: header;\n    display: flex;\n    gap: 10px;\n}\n\n.push-notification .notification-title {\n    font-weight: 600;\n}\n\n.push-notification .notification-body {\n    grid-area: body;\n}\n\n.webhook-frame {\n    padding: 20px;\n    width: 100%;\n}\n\n.email-content-container {\n    flex: 1;\n    overflow: auto;\n    max-height: 520px; /* Ensure it fits within the parent container */\n    display: flex;\n    flex-direction: column;\n}\n\n/* Add styling specifically for images inside the email to ensure they don't overflow */\n.prose img {\n    max-width: 100%;\n    height: auto;\n}", ".monaco-editor {\n    --vscode-editor-background: var(--color-editor);\n    --vscode-editorStickyScroll-background: var(--color-editor);\n    --vscode-breadcrumb-background: var(--color-editor);\n    --vscode-editorGutter-background: var(--color-editor);\n    --vscode-editorMarkerNavigation-background: var(--color-editor);\n}", ".rule-builder {\n    border: 1px solid var(--color-grey);\n    border-radius: var(--border-radius);\n    padding: 20px;\n    margin-bottom: 20px;\n}\n\n.rule-set {\n    width: 100%;\n    display: flex;\n    flex-direction: column;\n    align-items: flex-start;\n    padding-bottom: 10px;\n    border-radius: var(--border-radius);\n}\n\n.rule-set .rule > .rule-set {\n    border: 1px solid var(--color-grey);\n}\n\n.rule-set .rule-set-header {\n    width: 100%;\n    display: flex;\n    align-items: center;\n    justify-content: flex-start;\n    gap: 5px;\n}\n\n.rule-set .rule-set-rules {\n    display: flex;\n    flex-direction: column;\n    padding: 5px 0;\n    margin-left: 10px;\n}\n\n.rule-set .rule-set-actions {\n    display: flex;\n    gap: 5px;\n    padding: 0 20px;\n    margin-left: 10px;\n}\n\n.rule-set .rule > .rule-set .rule-set-header {\n    padding: 5px 10px 0;\n}\n\n.rule-set .rule > .rule-set .rule-set-rules,\n.rule-set .rule > .rule-set .rule-set-actions {\n    margin-left: 0;\n}\n\n.rule {\n    position: relative;\n    display: flex;\n    justify-content: flex-start;\n    gap: 10px;\n    margin-left: -1px;\n    padding: 5px 20px;\n    border-left: 1px solid var(--color-grey);\n}\n\n.rule::after {\n    content: '';\n    border-bottom: 1px solid var(--color-grey);\n    border-left: 1px solid var(--color-grey);\n    border-bottom-left-radius: var(--border-radius);\n    position: absolute;\n    left: -1px;\n    top: 0;\n    width: 20px;\n    height: 20px;\n}\n\n.rule:last-child {\n    border-left-color: transparent;\n}\n\n.rule-inner {\n    position: relative;\n    display: flex;\n    justify-content: flex-start;\n}\n\n.rule-options {\n    display: flex;\n    align-items: center;\n    justify-content: flex-start;\n    gap: 5px;\n    padding: 5px;\n}\n\n.rule-part input {\n    margin: 0;\n    min-width: 100px;\n}\n\n.rule-inner .ui-button {\n    color: var(--color-primary-soft);\n}\n\n.rule-inner input.small {\n    font-weight: 500;\n    width: 100%;\n}\n\n.rule-form-title {\n    margin: 10px 0 0;\n}\n\n.rule-form-title span {\n    font-weight: 500;\n}\n", ".preview-source-editor {\n    background: var(--color-editor);\n    display: flex;\n    position: relative;\n    align-items: stretch;\n    border-radius: var(--border-radius);\n    overflow: auto;\n    padding: 10px 0;\n    min-height: 400px;\n    flex-grow: 1;\n}\n\n.preview-source-editor section {\n    height: auto !important;\n}\n\n.user-lookup .ui-button-group {\n    width: 100%;\n    margin-bottom: 10px;\n}\n\n.user-lookup .ui-button-group .ui-text-input {\n    flex-grow: 1;\n}", "/* Override the page background */\n:root {\n  --page-background: var(--color-background);\n}\n\n/* Journeys Container Layout */\n.journeys-container {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n  width: 100%;\n}\n\n/* Filter and search area */\n.journeys-filter {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n  width: 100%;\n  border-bottom: 1px solid var(--color-divider);\n  padding-bottom: 0.5rem;\n}\n\n/* Create <PERSON><PERSON> Styling */\n.journeys-header .ui-button {\n  color: var(--color-white);\n  border-radius: 9999px;\n  padding: 0.75rem 1.5rem;\n  font-weight: 500;\n  border: none;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  transition: background-color 0.2s, transform 0.1s;\n}\n\n.journeys-header .ui-button:hover {\n  background-color: var(--color-pine-green);\n  transform: translateY(-1px);\n}\n\n.journeys-header .ui-button svg {\n  width: 16px;\n  height: 16px;\n}\n\n/* Search styling */\n.journeys-search {\n  width: 300px;\n  position: relative;\n}\n\n.journeys-search input {\n  width: 100%;\n  padding: 0.75rem 1rem;\n  border-radius: 8px;\n  font-size: 0.9rem;\n  color: var(--color-on-background);\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-divider);\n}\n\n.journeys-search input::placeholder {\n  color: var(--color-primary-soft);\n}\n\n.journeys-search input:focus {\n  outline: none;\n  border-color: var(--color-emerald-green);\n}\n\n/* Journeys Grid Layout */\n.journeys-grid {\n  display: grid;\n  grid-template-columns: repeat(1, 1fr);\n  gap: 1.25rem;\n  width: 100%;\n}\n\n/* Journey Card Styling */\n.journey-card {\n  display: flex;\n  flex-direction: column;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-divider);\n  border-radius: 12px;\n  overflow: hidden;\n  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n  cursor: pointer;\n}\n\n.journey-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px var(--color-shadow);\n}\n\n/* Journey Card Header */\n.journey-card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem;\n  background-color: var(--color-surface-secondary);\n  border-bottom: 1px solid var(--color-divider);\n}\n\n.journey-card-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.journey-card-icon .placeholder {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n  background-color: var(--color-emerald-green);\n  color: var(--color-white);\n  border-radius: 50%;\n}\n\n.journey-card-icon .placeholder svg {\n  width: 16px;\n  height: 16px;\n}\n\n.journey-card-actions {\n  color: var(--color-primary-soft);\n}\n\n/* Journey Card Content */\n.journey-card-content {\n  padding: 1rem;\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.journey-card-title {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n\n.journey-card-title h3 {\n  margin: 0;\n  font-size: 1.1rem;\n  font-weight: 500;\n  color: var(--color-on-background);\n  line-height: 1.3;\n}\n\n.journey-description {\n  margin-top: 0.5rem;\n  font-size: 0.875rem;\n  color: var(--color-primary-soft);\n  line-height: 1.4;\n}\n\n/* Journey status tag styling */\n.journey-card-title .ui-tag {\n  padding: 0.25rem 0.75rem;\n  font-size: 0.75rem;\n  font-weight: 500;\n  border-radius: 4px;\n  margin-left: 1rem;\n  white-space: nowrap;\n  flex-shrink: 0;\n}\n\n.journey-card-title .ui-tag.plain {\n  background-color: var(--color-grey-soft);\n  color: var(--color-primary-soft);\n}\n\n.journey-card-title .ui-tag.success {\n  background-color: var(--color-green-soft);\n  color: var(--color-green-hard);\n}\n\n/* Journey Metrics */\n.journey-card-metrics {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 1.5rem;\n  padding-top: 0.5rem;\n  margin-top: 0.5rem;\n  border-top: 1px solid var(--color-divider);\n}\n\n.metric-group {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.metric-label {\n  font-size: 0.75rem;\n  color: var(--color-primary-soft);\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.metric-value {\n  font-size: 0.875rem;\n  color: var(--color-on-background);\n  font-weight: 500;\n}\n\n.metric-subvalue {\n  font-size: 0.75rem;\n  color: var(--color-primary-soft);\n  margin-top: 0.25rem;\n}\n\n/* Journey Card Footer */\n.journey-card-footer {\n  margin-top: auto;\n  padding-top: 0.75rem;\n  border-top: 1px solid var(--color-divider);\n  font-size: 0.75rem;\n  color: var(--color-primary-soft);\n}\n\n.updated-at {\n  font-style: italic;\n}\n\n/* Loading and Empty States */\n.loading-state,\n.empty-state {\n  grid-column: 1 / -1;\n  padding: 3rem;\n  text-align: center;\n  background-color: var(--color-surface);\n  border-radius: 12px;\n  color: var(--color-primary-soft);\n  border: 1px solid var(--color-divider);\n}\n\n/* Pagination */\n.journeys-pagination {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 1rem 0;\n  font-size: 0.9rem;\n  border-top: 1px solid var(--color-divider);\n  margin-top: 1rem;\n  color: var(--color-primary-soft);\n}\n\n.pagination-controls {\n  display: flex;\n  gap: 0.75rem;\n}\n\n.pagination-controls button {\n  padding: 0.5rem 1rem;\n  border-radius: 6px;\n  border: 1px solid var(--color-divider);\n  background-color: var(--color-surface);\n  color: var(--color-on-background);\n  cursor: pointer;\n  font-size: 0.85rem;\n  transition: background-color 0.2s;\n}\n\n.pagination-controls button:hover:not(:disabled) {\n  background-color: var(--color-surface-secondary);\n}\n\n.pagination-controls button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* Responsive adjustments */\n@media (min-width: 640px) {\n  .journeys-grid {\n    grid-template-columns: repeat(1, 1fr);\n  }\n}\n\n@media (min-width: 768px) {\n  .journeys-grid {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n@media (min-width: 1024px) {\n  .journeys-grid {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n@media (min-width: 1280px) {\n  .journeys-grid {\n    grid-template-columns: repeat(4, 1fr);\n  }\n} ", ".code-example {\n    position: relative;\n}\n\n.code-example pre {\n    border-radius: var(--border-radius);\n    background-color: var(--color-grey-soft);\n    word-break: keep-all;\n    overflow-wrap: break-word;\n    overflow: hidden;\n    padding: 20px;\n    margin: 0;\n}\n\n.code-example pre code {\n    background-color: transparent;\n}\n\n.code-example .copy-button {\n    position: absolute;\n    right: 10px;\n    top: 10px;\n}\n", ".preview-image {\n    border-radius: var(--border-radius-inner);\n    overflow: hidden;\n    position: relative;\n}\n\n.preview-image iframe {\n    transform-origin: top left;\n    overflow: hidden;\n}\n\n.preview-image::after {\n    content: '';\n    display: block;\n    position: absolute;\n    top: 0;\n    left: 0;\n    bottom: 0;\n    right: 0;\n}\n\n@media only screen and (max-width: 600px) {\n    .preview-image {\n        display: none;\n    }\n}", ".journey {\n    display: flex;\n    align-items: stretch;\n    height: 100%;\n}\n\n.journey-actions {\n    display: flex;\n}\n\n.journey-builder {\n    background-size: 30px 30px;\n    flex-grow: 1;\n}\n\n.journey-options {\n    max-width: 300px;\n    border-color: var(--color-grey);\n    border-style: solid;\n    border-width: 0;\n    border-left-width: 1px;\n    position: relative;\n    overflow: scroll;\n}\n\n.journey-options-edit {\n    padding: 15px 20px;\n}\n\n.editing .journey-options {\n    max-width: none;\n}\n\n.journey-options h4 {\n    padding: 10px 20px;\n    margin: 0;\n}\n\n.journey-options .options-section {\n    padding: 10px 20px;\n}\n\n.journey-options .component {\n    display: grid;\n    grid-template-areas: \"handle title\" \"handle desc\";\n    grid-template-columns: auto 1fr;\n    border-bottom: 1px solid var(--color-grey);\n    padding: 20px;\n    column-gap: 15px;\n    cursor: grab;\n}\n\n.journey-options .component:hover {\n    background-color: var(--color-background-soft);\n}\n\n.journey-options .component:last-child {\n    border-bottom: 0px;\n}\n\n.journey-options .journey-step-header {\n    padding: 15px 20px;\n}\n\n.journey-options .journey-step-header h4 {\n    font-size: 18px;\n    line-height: 22px;\n}\n\n.component-handle {\n    background-color: var(--color-grey);\n    grid-area: handle;\n    align-self: start;\n    border-radius: 4px;\n    width: 36px;\n    height: 36px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.component-handle svg {\n    width: 16px;\n    height: 16px;\n}\n\n.component.entrance .component-handle,\n.journey-step.entrance .step-header-icon,\n.component.exit .component-handle,\n.journey-step.exit .step-header-icon {\n    background-color: var(--color-red-soft);\n    color: var(--color-red-hard);\n}\n\n.component.entrance .component-handle svg, .journey-step.entrance .step-header-icon svg {\n    margin-left: -4px;\n}\n\n.component.delay .component-handle, .journey-step.delay .step-header-icon {\n    background-color: var(--color-yellow-soft);\n    color: var(--color-yellow-hard);\n}\n\n.component.action .component-handle, .journey-step.action .step-header-icon {\n    background-color: var(--color-blue-soft);\n    color: var(--color-blue-hard);\n}\n\n.component.flow .component-handle, .journey-step.flow .step-header-icon {\n    background-color: var(--color-green-soft);\n    color: var(--color-green-hard);\n}\n\n.journey-minimap.entrance,\n.journey-minimap.exit {\n    fill: var(--color-red-soft);\n}\n\n.journey-minimap.entrance.selected,\n.journey-minimap.exit.selected {\n    fill: var(--color-red);\n}\n\n.journey-minimap.delay {\n    fill: var(--color-yellow-soft);\n}\n\n.journey-minimap.delay.selected {\n    fill: var(--color-yellow);\n}\n\n.journey-minimap.action {\n    fill: var(--color-blue-soft);\n}\n\n.journey-minimap.action.selected {\n    fill: var(--color-blue);\n}\n\n.journey-minimap.flow {\n    fill: var(--color-green-soft);\n}\n\n.journey-minimap.flow.selected {\n    fill: var(--color-green);\n}\n\n.journey .react-flow__minimap {\n    background-color: var(--color-background-soft);\n}\n\n.journey .react-flow__minimap-mask {\n    fill: var(--color-background);\n    opacity: 0.6;\n}\n\n.component-title {\n    grid-area: title;\n    font-weight: bold;\n}\n\n.component-desc {\n    font-size: 14px;\n}\n\n.internal-canvas {\n    background: var(--color-background-soft);\n}\n\n.editing .internal-canvas {\n    background: var(--color-grey);\n}\n\n.journey-step {\n    background: var(--color-background);\n    border: 1px solid var(--color-grey);\n    box-shadow: 0px 4px 30px rgba(22, 33, 74, 0.05);\n    border-radius: 8px;\n    min-width: 200px;\n}\n\n.editing .journey-step {\n    opacity: 0.5;\n}\n\n.editing .journey-step.editing {\n    opacity: 1;\n}\n\n.journey-step.entrance.selected,\n.journey-step.exit.selected {\n    border-color: var(--color-red-hard);\n}\n\n.journey-step.flow.selected {\n    border-color: var(--color-green-hard);\n}\n\n.journey-step.action.selected {\n    border-color: var(--color-blue-hard);\n}\n\n.journey-step.delay.selected {\n    border-color: var(--color-yellow-hard);\n}\n\n.journey-step .data-key {\n    background-color: var(--color-background-soft);\n    padding: 7px 10px;\n    border-radius: var(--border-radius-inner);\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    color: var(--color-primary-soft);\n}\n\n.journey-step .data-key svg {\n    height: 16px;\n    margin-right: 5px;\n}\n\n.journey-step .connectors {\n    position: absolute;\n    display: flex;\n    align-items: center;\n    justify-content: space-evenly;\n    bottom: -6px;\n    width: 100%;\n}\n\n.journey-step .connector {\n    position: relative;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 5px;\n}\n\n.journey-step .connector .connector-anchor {\n    content: '';\n    border-radius: 50%;\n    width: 12px;\n    height: 12px;\n    z-index: 0;\n    border: 1px solid var(--color-border);\n    background: var(--color-background);\n}\n\n.journey-step-header {\n    padding: 10px 15px;\n    border-bottom: 1px solid var(--color-grey);\n    display: flex;\n    align-items: center;\n    gap: 10px;\n}\n\n.journey-step-header .step-header-icon {\n    padding: 5px;\n    border-radius: 4px;\n    width: 30px;\n    height: 30px;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n\n.journey-step-header .step-header-title {\n    flex-grow: 1;\n}\n\n.journey-step-header .step-header-options {\n    flex-shrink: 0;\n}\n\n.journey-step-header h4 {\n    padding: 5px 0;\n    margin: 0;\n    font-size: 16px;\n    line-height: 20px;\n}\n\n.journey-step-header .step-header-stats {\n    display: flex;\n    align-items: center;\n    gap: 10px;\n    background: var(--color-background-soft);\n    border-radius: var(--border-radius-inner);\n    padding: 5px 10px;\n}\n\n.journey-step-header .step-header-stats:hover .stat {\n    color: var(--color-primary);\n}\n\n.journey-step-header .step-header-stats .stat {\n    display: flex;\n    align-items: center;\n    gap: 2px;\n    font-weight: 500;\n    color: var(--color-primary-soft);\n}\n\n.journey-step-body {\n    padding: 20px;\n}\n\n.journey-step-body-name {\n    margin-bottom: 10px;\n    font-weight: bold;\n}\n\n.journey-step-edge {\n    background-color: var(--color-background);\n    padding: 10px;\n    border: 1px solid var(--color-grey);\n    border-radius: 8px;\n    pointer-events: all;\n}\n\n.journey .react-flow__handle {\n    width: 10px;\n    height: 10px;\n}\n\n.journey .react-flow__handle-connecting {\n    background: var(--color-red)\n}\n\n.journey .react-flow__handle-valid {\n    background: var(--color-green)\n}\n\n.journey .react-flow__edge-path,\n.journey .react-flow__connection-path {\n    stroke-width: 4;\n}\n\n.journey-step-labelled-sources {\n    padding-bottom: 36px;\n}\n\n.step-handle-label {\n    position: absolute;\n    bottom: 10px;\n    transform: translate(-50%, 0)\n}\n\n.react-flow__attribution {\n    display: none;\n}", "/* this gets exported as style.css and can be used for the default theming */\n/* these are the necessary styles for React Flow, they get used by base.css and style.css */\n.react-flow__container {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  left: 0;\n}\n.react-flow__pane {\n  z-index: 1;\n  cursor: -webkit-grab;\n  cursor: grab;\n}\n.react-flow__pane.selection {\n    cursor: pointer;\n  }\n.react-flow__pane.dragging {\n    cursor: -webkit-grabbing;\n    cursor: grabbing;\n  }\n.react-flow__viewport {\n  transform-origin: 0 0;\n  z-index: 2;\n  pointer-events: none;\n}\n.react-flow__renderer {\n  z-index: 4;\n}\n.react-flow__selection {\n  z-index: 6;\n}\n.react-flow__nodesselection-rect:focus,\n.react-flow__nodesselection-rect:focus-visible {\n  outline: none;\n}\n.react-flow .react-flow__edges {\n  pointer-events: none;\n  overflow: visible;\n}\n.react-flow__edge-path,\n.react-flow__connection-path {\n  stroke: #b1b1b7;\n  stroke-width: 1;\n  fill: none;\n}\n.react-flow__edge {\n  pointer-events: visibleStroke;\n  cursor: pointer;\n}\n.react-flow__edge.animated path {\n    stroke-dasharray: 5;\n    -webkit-animation: dashdraw 0.5s linear infinite;\n            animation: dashdraw 0.5s linear infinite;\n  }\n.react-flow__edge.animated path.react-flow__edge-interaction {\n    stroke-dasharray: none;\n    -webkit-animation: none;\n            animation: none;\n  }\n.react-flow__edge.inactive {\n    pointer-events: none;\n  }\n.react-flow__edge.selected,\n  .react-flow__edge:focus,\n  .react-flow__edge:focus-visible {\n    outline: none;\n  }\n.react-flow__edge.selected .react-flow__edge-path,\n  .react-flow__edge:focus .react-flow__edge-path,\n  .react-flow__edge:focus-visible .react-flow__edge-path {\n    stroke: #555;\n  }\n.react-flow__edge-textwrapper {\n    pointer-events: all;\n  }\n.react-flow__edge-textbg {\n    fill: white;\n  }\n.react-flow__edge .react-flow__edge-text {\n    pointer-events: none;\n    -webkit-user-select: none;\n       -moz-user-select: none;\n            user-select: none;\n  }\n.react-flow__connection {\n  pointer-events: none;\n}\n.react-flow__connection .animated {\n    stroke-dasharray: 5;\n    -webkit-animation: dashdraw 0.5s linear infinite;\n            animation: dashdraw 0.5s linear infinite;\n  }\n.react-flow__connectionline {\n  z-index: 1001;\n}\n.react-flow__nodes {\n  pointer-events: none;\n  transform-origin: 0 0;\n}\n.react-flow__node {\n  position: absolute;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  pointer-events: all;\n  transform-origin: 0 0;\n  box-sizing: border-box;\n  cursor: -webkit-grab;\n  cursor: grab;\n}\n.react-flow__node.dragging {\n    cursor: -webkit-grabbing;\n    cursor: grabbing;\n  }\n.react-flow__nodesselection {\n  z-index: 3;\n  transform-origin: left top;\n  pointer-events: none;\n}\n.react-flow__nodesselection-rect {\n    position: absolute;\n    pointer-events: all;\n    cursor: -webkit-grab;\n    cursor: grab;\n  }\n.react-flow__handle {\n  position: absolute;\n  pointer-events: none;\n  min-width: 5px;\n  min-height: 5px;\n  width: 6px;\n  height: 6px;\n  background: #1a192b;\n  border: 1px solid white;\n  border-radius: 100%;\n}\n.react-flow__handle.connectionindicator {\n    pointer-events: all;\n    cursor: crosshair;\n  }\n.react-flow__handle-bottom {\n    top: auto;\n    left: 50%;\n    bottom: -4px;\n    transform: translate(-50%, 0);\n  }\n.react-flow__handle-top {\n    left: 50%;\n    top: -4px;\n    transform: translate(-50%, 0);\n  }\n.react-flow__handle-left {\n    top: 50%;\n    left: -4px;\n    transform: translate(0, -50%);\n  }\n.react-flow__handle-right {\n    right: -4px;\n    top: 50%;\n    transform: translate(0, -50%);\n  }\n.react-flow__edgeupdater {\n  cursor: move;\n  pointer-events: all;\n}\n.react-flow__panel {\n  position: absolute;\n  z-index: 5;\n  margin: 15px;\n}\n.react-flow__panel.top {\n    top: 0;\n  }\n.react-flow__panel.bottom {\n    bottom: 0;\n  }\n.react-flow__panel.left {\n    left: 0;\n  }\n.react-flow__panel.right {\n    right: 0;\n  }\n.react-flow__panel.center {\n    left: 50%;\n    transform: translateX(-50%);\n  }\n.react-flow__attribution {\n  font-size: 10px;\n  background: rgba(255, 255, 255, 0.5);\n  padding: 2px 3px;\n  margin: 0;\n}\n.react-flow__attribution a {\n    text-decoration: none;\n    color: #999;\n  }\n@-webkit-keyframes dashdraw {\n  from {\n    stroke-dashoffset: 10;\n  }\n}\n@keyframes dashdraw {\n  from {\n    stroke-dashoffset: 10;\n  }\n}\n.react-flow__edgelabel-renderer {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n.react-flow__edge.updating .react-flow__edge-path {\n      stroke: #777;\n    }\n.react-flow__edge-text {\n    font-size: 10px;\n  }\n.react-flow__node.selectable:focus,\n  .react-flow__node.selectable:focus-visible {\n    outline: none;\n  }\n.react-flow__node-default,\n.react-flow__node-input,\n.react-flow__node-output,\n.react-flow__node-group {\n  padding: 10px;\n  border-radius: 3px;\n  width: 150px;\n  font-size: 12px;\n  color: #222;\n  text-align: center;\n  border-width: 1px;\n  border-style: solid;\n  border-color: #1a192b;\n  background-color: white;\n}\n.react-flow__node-default.selectable:hover, .react-flow__node-input.selectable:hover, .react-flow__node-output.selectable:hover, .react-flow__node-group.selectable:hover {\n      box-shadow: 0 1px 4px 1px rgba(0, 0, 0, 0.08);\n    }\n.react-flow__node-default.selectable.selected,\n    .react-flow__node-default.selectable:focus,\n    .react-flow__node-default.selectable:focus-visible,\n    .react-flow__node-input.selectable.selected,\n    .react-flow__node-input.selectable:focus,\n    .react-flow__node-input.selectable:focus-visible,\n    .react-flow__node-output.selectable.selected,\n    .react-flow__node-output.selectable:focus,\n    .react-flow__node-output.selectable:focus-visible,\n    .react-flow__node-group.selectable.selected,\n    .react-flow__node-group.selectable:focus,\n    .react-flow__node-group.selectable:focus-visible {\n      box-shadow: 0 0 0 0.5px #1a192b;\n    }\n.react-flow__node-group {\n  background-color: rgba(240, 240, 240, 0.25);\n}\n.react-flow__nodesselection-rect,\n.react-flow__selection {\n  background: rgba(0, 89, 220, 0.08);\n  border: 1px dotted rgba(0, 89, 220, 0.8);\n}\n.react-flow__nodesselection-rect:focus,\n  .react-flow__nodesselection-rect:focus-visible,\n  .react-flow__selection:focus,\n  .react-flow__selection:focus-visible {\n    outline: none;\n  }\n.react-flow__controls {\n  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.08);\n}\n.react-flow__controls-button {\n    border: none;\n    background: #fefefe;\n    border-bottom: 1px solid #eee;\n    box-sizing: content-box;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 16px;\n    height: 16px;\n    cursor: pointer;\n    -webkit-user-select: none;\n       -moz-user-select: none;\n            user-select: none;\n    padding: 5px;\n  }\n.react-flow__controls-button:hover {\n      background: #f4f4f4;\n    }\n.react-flow__controls-button svg {\n      width: 100%;\n      max-width: 12px;\n      max-height: 12px;\n    }\n.react-flow__controls-button:disabled {\n      pointer-events: none;\n    }\n.react-flow__controls-button:disabled svg {\n        fill-opacity: 0.4;\n      }\n.react-flow__minimap {\n  background-color: #fff;\n}\n.react-flow__resize-control {\n  position: absolute;\n}\n.react-flow__resize-control.left,\n.react-flow__resize-control.right {\n  cursor: ew-resize;\n}\n.react-flow__resize-control.top,\n.react-flow__resize-control.bottom {\n  cursor: ns-resize;\n}\n.react-flow__resize-control.top.left,\n.react-flow__resize-control.bottom.right {\n  cursor: nwse-resize;\n}\n.react-flow__resize-control.bottom.left,\n.react-flow__resize-control.top.right {\n  cursor: nesw-resize;\n}\n/* handle styles */\n.react-flow__resize-control.handle {\n  width: 4px;\n  height: 4px;\n  border: 1px solid #fff;\n  border-radius: 1px;\n  background-color: #3367d9;\n  transform: translate(-50%, -50%);\n}\n.react-flow__resize-control.handle.left {\n  left: 0;\n  top: 50%;\n}\n.react-flow__resize-control.handle.right {\n  left: 100%;\n  top: 50%;\n}\n.react-flow__resize-control.handle.top {\n  left: 50%;\n  top: 0;\n}\n.react-flow__resize-control.handle.bottom {\n  left: 50%;\n  top: 100%;\n}\n.react-flow__resize-control.handle.top.left {\n  left: 0;\n}\n.react-flow__resize-control.handle.bottom.left {\n  left: 0;\n}\n.react-flow__resize-control.handle.top.right {\n  left: 100%;\n}\n.react-flow__resize-control.handle.bottom.right {\n  left: 100%;\n}\n/* line styles */\n.react-flow__resize-control.line {\n  border-color: #3367d9;\n  border-width: 0;\n  border-style: solid;\n}\n.react-flow__resize-control.line.left,\n.react-flow__resize-control.line.right {\n  width: 1px;\n  transform: translate(-50%, 0);\n  top: 0;\n  height: 100%;\n}\n.react-flow__resize-control.line.left {\n  left: 0;\n  border-left-width: 1px;\n}\n.react-flow__resize-control.line.right {\n  left: 100%;\n  border-right-width: 1px;\n}\n.react-flow__resize-control.line.top,\n.react-flow__resize-control.line.bottom {\n  height: 1px;\n  transform: translate(0, -50%);\n  left: 0;\n  width: 100%;\n}\n.react-flow__resize-control.line.top {\n  top: 0;\n  border-top-width: 1px;\n}\n.react-flow__resize-control.line.bottom {\n  border-bottom-width: 1px;\n  top: 100%;\n}\n", ".ui-schema-form .ui-schema-fields > .ui-schema-form .ui-schema-fields {\n    border: 1px solid var(--color-grey);\n    border-radius: var(--border-radius);\n    padding: 10px;\n}\n\n.ui-schema-form > h5 {\n    margin-top: 15px;\n    margin-bottom: 2px;\n}\n\n.ui-schema-form > p {\n    margin: 2px 0 10px;\n    color: var(--color-primary-soft);\n    font-weight: 400;\n    font-size: 14px;\n}", ".provider-tile {\n    display: flex;\n    gap: 10px;\n    align-items: center;\n}\n\n.provider-tile img {\n    width: 100%;\n    max-width: 40px;\n    border-radius: var(--border-radius);\n}\n\n.provider-tile h5 {\n    margin: 0;\n}\n\n.provider-tile p {\n    font-size: 13px;\n    color: var(--color-primary-soft);\n    margin: 0;\n}\n", ".auth {\n    background: var(--color-background-soft);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    min-height: 100vh;\n    flex-direction: column;\n    gap: 15px;\n    padding: 40px 0;\n}\n\n.auth .logo svg {\n    height: 40px;\n}\n\n.auth-step {\n    min-width: 400px;\n    max-width: 600px;\n    background: var(--color-background);\n    border: 1px solid var(--color-grey-soft);\n    border-radius: var(--border-radius-outer);\n    padding: 40px;\n}\n\n.auth.login .auth-step {\n    min-width: 300px;\n    width: 100%;\n    max-width: 350px;\n}\n\n.auth-step h1, .auth-step h2 {\n    margin: 0;\n}\n\n.auth-step .form {\n    padding: 10px 0;\n}\n\n.auth-methods {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n}\n\n.auth.login .ui-button,\n.auth.login .form-submit {\n    width: 100%;\n}\n\n.auth.login form {\n    margin-bottom: 20px;\n    display: flex;\n    flex-direction: column;\n    gap: 15px;\n}\n\n.form-actions {\n    margin-top: 20px;\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n}\n\n.auth-links {\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n    align-items: center;\n}\n\n.auth-step .ui-button {\n    width: 100%;\n}\n\n.auth-step .ui-button[variant=\"plain\"] {\n    color: var(--color-primary);\n}\n\n.divider {\n    margin: 20px 0;\n    text-align: center;\n    position: relative;\n}\n\n.divider::before,\n.divider::after {\n    content: \"\";\n    height: 1px;\n    background: var(--color-grey-soft);\n    position: absolute;\n    top: 50%;\n    width: 40%;\n}\n\n.divider::before {\n    left: 0;\n}\n\n.divider::after {\n    right: 0;\n}\n\n@media only screen and (max-width: 600px) {\n    .auth {\n        padding: 40px 20px;\n    }\n\n    .auth-step {\n        min-width: auto;\n        width: 100%;\n    }\n}\n\n.auth-success {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px 0;\n}\n\n.success-checkmark {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  background: var(--color-green);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transform: scale(0);\n  animation: scaleIn 0.3s ease-out forwards;\n}\n\n.success-checkmark svg {\n  width: 30px;\n  height: 30px;\n  stroke: white;\n  stroke-width: 3;\n  stroke-linecap: round;\n  stroke-linejoin: round;\n  transform: scale(0);\n  animation: checkmark 0.2s ease-out 0.3s forwards;\n}\n\n@keyframes scaleIn {\n  from { transform: scale(0); }\n  to { transform: scale(1); }\n}\n\n@keyframes checkmark {\n  from { transform: scale(0); }\n  to { transform: scale(1); }\n}", "/* Define custom CSS variables that aren't in the original theme */\n:root {\n    /* Additional surface colors for light mode */\n    --color-surface-emphasis: rgba(249, 250, 251, 0.9);\n    --color-surface-brand: linear-gradient(90deg, #f5f7fa, #f8f9fb);\n    --color-surface-elevated: #ffffff;\n    --color-surface-highlight: rgba(245, 247, 250, 0.6);\n    --color-surface-muted: rgba(234, 232, 232, 0.5);\n    --color-surface-overlay: rgba(21, 28, 45, 0.5);\n    \n    /* Text colors */\n    /* --color-text: var(--color-primary); */\n    --color-text-subtle: var(--color-primary-soft);\n    --color-text-muted: #94a3b8;\n    \n    /* Translucent colors */\n    --color-success-translucent: rgba(50, 213, 131, 0.2);\n    --color-warning-translucent: rgba(254, 200, 75, 0.2);\n    --color-text-muted-translucent: rgba(148, 163, 184, 0.2);\n    \n    /* Shadow colors */\n    --color-primary-shadow: rgba(41, 112, 255, 0.3);\n    \n    /* Success and warning colors */\n    --color-success: var(--color-green);\n    --color-success-muted: var(--color-green-soft);\n    --color-warning: var(--color-yellow);\n  }\n  \n  /* Dark mode overrides */\n  [data-theme=\"dark\"] {\n    /* Additional surface colors for dark mode */\n    --color-surface-emphasis: rgba(21, 28, 45, 0.9);\n    --color-surface-brand: linear-gradient(90deg, #1a1f2b, #1e2334);\n    --color-surface-elevated: #1e2538;\n    --color-surface-highlight: rgba(30, 37, 56, 0.8);\n    --color-surface-muted: rgba(43, 50, 69, 0.5);\n    --color-surface-overlay: rgba(15, 23, 42, 0.85);\n    \n    /* Text muted color for dark mode */\n    --color-text-muted: #94a3b8;\n  }\n  \n  .empty-state-icon svg {\n    width: 100%;\n    height: 100%;\n  }\n  \n  /* Dashboard layout adjustments */\n  .dashboard-section {\n    margin-bottom: 2.5rem;\n  }\n  \n  /* Ensure equal-height columns if desired */\n  .dashboard-section .columns {\n    display: flex;\n    gap: 1.5rem;\n  }\n  \n  .dashboard-section .columns > .column {\n    flex: 1;\n  }\n  \n  /* Chart container adjustments */\n  .chart-container {\n    position: relative;\n    height: 300px;\n    width: 100%;\n  }\n  \n  /* Responsive adjustments for small screens */\n  @media only screen and (max-width: 768px) {\n    .dashboard-section .columns {\n      flex-direction: column;\n    }\n    \n    .chart-container {\n      height: 250px;\n    }\n  }\n  .page-content {\n    padding-right: 0px;\n    padding-left: 0px;\n  }\n  \n  /* Top stats (cards) */\n  .top-stats {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));\n    gap: 1rem;\n    margin-bottom: 1.5rem;\n  }\n  \n  .stat-card {\n    background: var(--color-surface-elevated);\n    border-radius: 12px;\n    padding: 16px;\n    box-shadow: var(--shadow-sm);\n    border: 1px solid var(--color-divider);\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n  }\n  \n  .stat-title {\n    font-size: 14px;\n    color: var(--color-text-muted);\n  }\n  \n  .stat-value {\n    font-size: 24px;\n    font-weight: 600;\n    margin-top: 8px;\n  }\n  \n  /* Section headings */\n  .section-heading {\n    font-size: 18px;\n    font-weight: 600;\n    margin-bottom: 1rem;\n    color: var(--color-primary);\n  }\n  \n  /* Campaigns area */\n  .campaigns-section {\n    display: grid;\n    grid-template-columns: 300px 1fr;\n    gap: 2rem;\n  }\n  \n  .calendar {\n    background: var(--color-surface-elevated);\n    border-radius: 12px;\n    padding: 16px;\n    border: 1px solid var(--color-divider);\n    box-shadow: var(--shadow-sm);\n  }\n  \n  .calendar h3 {\n    margin-bottom: 1rem;\n  }\n  \n  .calendar-grid {\n    display: grid;\n    grid-template-columns: repeat(7, 1fr);\n    gap: 4px;\n    text-align: center;\n  }\n  \n  .calendar-day {\n    padding: 8px 0;\n    background: var(--color-surface-muted);\n    border-radius: 4px;\n    color: var(--color-text-subtle);\n  }\n  \n  .selected-day {\n    background: var(--color-primary);\n    color: #fff;\n  }\n  \n  /* Campaign details card */\n  .campaign-details {\n    background: var(--color-surface-elevated);\n    border-radius: 12px;\n    padding: 16px;\n    border: 1px solid var(--color-divider);\n    box-shadow: var(--shadow-sm);\n  }\n  \n  .campaign-title {\n    font-size: 16px;\n    font-weight: 600;\n    margin-bottom: 8px;\n  }\n  \n  .campaign-dates {\n    font-size: 14px;\n    margin-bottom: 16px;\n  }\n  \n  .campaign-performance {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: 1rem;\n    margin-top: 1rem;\n  }\n  \n  .performance-item {\n    display: flex;\n    flex-direction: column;\n    background: var(--color-surface-muted);\n    border-radius: 8px;\n    padding: 12px;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .performance-value {\n    font-size: 18px;\n    font-weight: 600;\n    margin-bottom: 4px;\n  }\n  \n  /* Responsive adjustments */\n  @media (max-width: 768px) {\n    .campaigns-section {\n      grid-template-columns: 1fr;\n    }\n  }\n  \n  /* Dashboard Styles */\n  \n  /* Layout */\n  .dashboard-layout {\n    display: grid;\n    grid-template-columns: 1fr 350px;\n    gap: 24px;\n    position: relative;\n    max-width: 100%;\n  }\n  \n  .dashboard-main {\n    overflow-y: auto;\n    padding: 0;\n    margin: 0;\n    grid-column: 1;\n  }\n  \n  /* Make sure modals within dashboard have no margins */\n  .dashboard-main .modal-inner {\n    margin: 0;\n  }\n  \n  .dashboard-chat-sidebar {\n    width: 350px;\n    display: flex;\n    flex-direction: column;\n    flex-shrink: 0;\n    height: calc(100vh - 70px);\n    position: sticky;\n    top: 70px;\n    overflow: hidden;\n  }\n  \n  .dashboard-chat-sidebar > div {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    overflow: hidden;\n  }\n  \n  .dashboard-chat-container {\n    flex-grow: 1;\n    display: flex;\n    flex-direction: column;\n    overflow: hidden;\n  }\n  \n  /* Customer inquiry bar */\n  .customer-inquiry-bar {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 8px 12px;\n    border-bottom: 1px solid var(--color-divider);\n  }\n  \n  .customer-inquiry-input {\n    flex: 1;\n    padding: 6px 10px;\n    border-radius: 4px;\n    border: 1px solid var(--color-divider);\n    font-size: 0.875rem;\n    background-color: var(--color-surface);\n    color: var(--color-text);\n  }\n  \n  /* Team members avatars */\n  .team-members {\n    display: flex;\n    margin-left: 8px;\n  }\n  \n  .team-member-avatar {\n    width: 32px;\n    height: 32px;\n    border-radius: 50%;\n    overflow: hidden;\n    margin-left: -8px;\n    border: 2px solid var(--color-surface-elevated);\n    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n  }\n  \n  .team-member-avatar img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n  }\n  \n  /* Tab navigation */\n  .dashboard-tabs {\n    display: flex;\n    border-bottom: 1px solid var(--color-divider);\n  }\n  \n  .dashboard-tab {\n    padding: 10px 16px;\n    font-size: 0.875rem;\n    font-weight: 500;\n    color: var(--color-text-muted);\n    cursor: pointer;\n    transition: all 0.2s ease;\n  }\n  \n  .dashboard-tab:hover {\n    color: var(--color-text);\n  }\n  \n  .dashboard-tab.active {\n    color: var(--color-primary);\n    border-bottom: 2px solid var(--color-primary);\n  }\n  \n  /* Alert panels */\n  .alert-panel {\n    margin: 12px;\n    padding: 12px;\n    border-radius: 8px;\n    font-size: 0.875rem;\n  }\n  \n  .alert-panel.urgent {\n    background-color: rgba(239, 68, 68, 0.1);\n    border-left: 3px solid rgb(239, 68, 68);\n  }\n  \n  .alert-panel.info {\n    background-color: rgba(59, 130, 246, 0.1);\n    border-left: 3px solid rgb(59, 130, 246);\n  }\n  \n  .alert-panel.success {\n    background-color: rgba(16, 185, 129, 0.1);\n    border-left: 3px solid rgb(16, 185, 129);\n  }\n  \n  .alert-header {\n    display: flex;\n    align-items: center;\n    font-weight: 600;\n    margin-bottom: 8px;\n  }\n  \n  .alert-header svg {\n    margin-right: 6px;\n  }\n  \n  .alert-description {\n    font-size: 0.75rem;\n    margin-bottom: 8px;\n  }\n  \n  .alert-suggestion {\n    font-size: 0.75rem;\n    font-style: italic;\n  }\n  \n  /* Responsive adjustments */\n  @media (max-width: 1280px) {\n    .dashboard-layout {\n      grid-template-columns: 1fr 300px;\n    }\n  }\n  \n  @media (max-width: 1024px) {\n    .dashboard-layout {\n      grid-template-columns: 1fr;\n    }\n    \n    .dashboard-main {\n      grid-column: 1;\n    }\n    \n    .dashboard-chat-sidebar {\n      display: none;\n    }\n  }\n  \n  /* Welcome Dashboard Styles */\n  .welcome-dashboard {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 24px;\n    border-radius: 16px;\n    overflow: hidden;\n    margin-bottom: 40px;\n    box-shadow: var(--shadow-sm);\n  }\n  \n  .welcome-header {\n    display: flex;\n    align-items: center;\n    margin-bottom: 40px;\n    padding: 20px 20px;\n    background: var(--color-surface-brand);\n    border-bottom: 1px solid var(--color-divider);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-wrap: wrap;\n  }\n  \n  .welcome-title {\n    font-size: 24px;\n    font-weight: 700;\n    margin-bottom: 5px;\n    background: linear-gradient(90deg, var(--color-success), var(--color-primary));\n    -webkit-background-clip: text;\n    -webkit-text-fill-color: transparent;\n  }\n  \n  .welcome-subtitle {\n    font-size: 16px;\n    color: var(--color-text-subtle);\n    margin-bottom: 0;\n  }\n  \n  .mascot-image {\n    height: 80px;\n    margin-bottom: 0;\n    margin-right: 15px;\n    animation: float 3s ease-in-out infinite;\n  }\n  \n  @keyframes float {\n    0% { transform: translateY(0px); }\n    50% { transform: translateY(-10px); }\n    100% { transform: translateY(0px); }\n  }\n  \n  .welcome-header .text-content {\n    display: flex;\n    flex-direction: column;\n    align-items: flex-start;\n    text-align: left;\n  }\n  \n  /* Onboarding Steps */\n  .onboarding-steps {\n    background: var(--color-surface-elevated);\n    border-radius: 12px;\n    padding: 40px;\n    margin-bottom: 40px;\n    box-shadow: var(--shadow-sm);\n    border: 1px solid var(--color-divider);\n    border-bottom: 1px solid var(--color-divider);\n  }\n  \n  .step-counter {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 30px;\n  }\n  \n  .step {\n    width: 36px;\n    height: 36px;\n    border-radius: 50%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-weight: 600;\n    background: var(--color-surface-muted);\n    color: var(--color-text-subtle);\n  }\n  \n  .step.active {\n    background: var(--color-success);\n    color: var(--color-surface);\n  }\n  \n  .step.completed {\n    background: var(--color-primary);\n    color: var(--color-surface);\n  }\n  \n  .connector {\n    height: 2px;\n    width: 80px;\n    background: var(--color-divider);\n    margin: 0 10px;\n  }\n  \n  .steps-content {\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    gap: 20px;\n  }\n  \n  .step-card {\n    padding: 24px;\n    border-radius: 8px;\n    background: var(--color-surface-muted);\n    border: 1px solid var(--color-divider);\n    transition: all 0.3s ease;\n    height: 100%;\n  }\n  \n  .step-card.active {\n    border-left: 4px solid var(--color-success);\n  }\n  \n  .step-card.next {\n    border-left: 4px solid var(--color-primary);\n  }\n  \n  .step-card.completed {\n    border-left: 4px solid var(--color-success-muted);\n    opacity: 0.7;\n  }\n  \n  .step-card h3 {\n    font-size: 18px;\n    font-weight: 600;\n    margin-bottom: 10px;\n    color: var(--color-primary);\n  }\n  \n  .step-card p {\n    color: var(--color-text-subtle);\n    margin-bottom: 20px;\n  }\n  \n  .action-button {\n    background: linear-gradient(90deg, var(--color-primary), var(--color-success));\n    border: none;\n    border-radius: 8px;\n    padding: 10px 16px;\n    font-weight: 600;\n    color: var(--color-surface);\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    cursor: pointer;\n    transition: all 0.2s ease;\n    width: fit-content;\n  }\n  \n  .action-button:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 6px 12px var(--color-primary-shadow);\n  }\n  \n  .action-button svg {\n    margin-right: 8px;\n    width: 20px;\n    height: 20px;\n  }\n  \n  /* AI Agents Section */\n  .agents-section {\n    background: var(--color-surface-elevated);\n    border-radius: 12px;\n    padding: 40px;\n    margin-bottom: 40px;\n    box-shadow: var(--shadow-sm);\n    border: 1px solid var(--color-divider);\n  }\n  \n  .agents-section h2 {\n    text-align: center;\n    font-size: 24px;\n    font-weight: 600;\n    margin-bottom: 10px;\n    color: var(--color-primary);\n  }\n  \n  .agents-description {\n    text-align: center;\n    color: var(--color-text-subtle);\n    margin-bottom: 30px;\n  }\n  \n  .agents-grid {\n    display: grid;\n    grid-template-columns: repeat(3, 1fr);\n    gap: 20px;\n    margin-bottom: 30px;\n  }\n  \n  .agent-card {\n    background: var(--color-surface-elevated);\n    border-radius: 12px;\n    padding: 24px;\n    transition: all 0.3s ease;\n    position: relative;\n    overflow: hidden;\n    border: 1px solid var(--color-divider);\n  }\n  \n  .agent-card.unlocked {\n    border-top: 4px solid var(--color-success);\n  }\n  \n  .agent-card.partial {\n    border-top: 4px solid var(--color-warning);\n  }\n  \n  .agent-card.locked {\n    border-top: 4px solid var(--color-text-muted);\n    opacity: 0.7;\n  }\n  \n  .agent-card:hover {\n    transform: translateY(-5px);\n    background: var(--color-surface-highlight);\n  }\n  \n  .agent-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n  }\n  \n  .agent-icon {\n    width: 40px;\n    height: 40px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 50%;\n    background: var(--color-surface-elevated);\n    color: var(--color-primary);\n  }\n  \n  .agent-icon img {\n    width: 100%;\n    height: 100%;\n    border-radius: 50%;\n    object-fit: cover;\n  }\n  \n  .agent-status {\n    font-size: 12px;\n    font-weight: 600;\n    padding: 4px 8px;\n    border-radius: 4px;\n  }\n  \n  .status-unlocked {\n    background: var(--color-success-translucent);\n    color: var(--color-success);\n  }\n  \n  .status-partial {\n    background: var(--color-warning-translucent);\n    color: var(--color-warning);\n  }\n  \n  .status-locked {\n    background: var(--color-text-muted-translucent);\n    color: var(--color-text-muted);\n  }\n  \n  .agent-card h3 {\n    font-size: 18px;\n    font-weight: 700;\n    margin-bottom: 5px;\n    color: var(--color-primary);\n  }\n  \n  .agent-role {\n    font-size: 14px;\n    color: var(--color-primary);\n    margin-bottom: 10px;\n  }\n  \n  .agent-description {\n    font-size: 14px;\n    color: var(--color-text-subtle);\n    margin-bottom: 15px;\n    line-height: 1.4;\n  }\n  \n  .agent-requirements {\n    display: flex;\n    flex-direction: column;\n    gap: 8px;\n    margin-top: 15px;\n  }\n  \n  .requirement {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 8px 12px;\n    border-radius: 6px;\n    font-size: 13px;\n  }\n  \n  .requirement.met {\n    background: var(--color-success-translucent);\n  }\n  \n  .requirement.unmet {\n    background: var(--color-text-muted-translucent);\n  }\n  \n  .requirement-label {\n    font-weight: 500;\n  }\n  \n  .agent-coming-soon {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: var(--color-surface-overlay);\n    border-radius: 12px;\n    z-index: 1;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .agent-coming-soon span {\n    background: var(--color-surface-muted);\n    color: var(--color-text);\n    font-weight: 600;\n    padding: 8px 16px;\n    border-radius: 4px;\n    transform: rotate(-15deg);\n    letter-spacing: 1px;\n    border: 1px solid var(--color-divider);\n  }\n  \n  .agents-cta {\n    display: flex;\n    justify-content: center;\n    margin-top: 20px;\n  }\n  \n  /* Features Section */\n  .dashboard-features {\n    background: var(--color-surface-elevated);\n    border-radius: 12px;\n    padding: 40px;\n    box-shadow: var(--shadow-sm);\n    border: 1px solid var(--color-divider);\n  }\n  \n  .dashboard-features h2 {\n    font-size: 24px;\n    font-weight: 700;\n    margin-bottom: 30px;\n    color: var(--color-primary);\n    text-align: center;\n  }\n  \n  .features-grid {\n    display: grid;\n    grid-template-columns: repeat(4, 1fr);\n    gap: 20px;\n  }\n  \n  .feature-card {\n    background: var(--color-surface-elevated);\n    border-radius: 12px;\n    padding: 24px;\n    border: 1px solid var(--color-divider);\n    transition: all 0.3s ease;\n    text-align: center;\n  }\n  \n  .feature-card:hover {\n    transform: translateY(-5px);\n    background: var(--color-surface-highlight);\n  }\n  \n  .feature-icon {\n    width: 48px;\n    height: 48px;\n    margin: 0 auto 16px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    color: var(--color-primary);\n  }\n  \n  .feature-card h3 {\n    font-size: 18px;\n    font-weight: 600;\n    margin-bottom: 10px;\n    color: var(--color-primary);\n  }\n  \n  .feature-card p {\n    font-size: 14px;\n    color: var(--color-text-subtle);\n  }\n  \n  /* Responsive adjustments for welcome dashboard */\n  @media (max-width: 1024px) {\n    .features-grid {\n      grid-template-columns: repeat(2, 1fr);\n    }\n  }\n  \n  @media (max-width: 768px) {\n    .steps-content {\n      grid-template-columns: 1fr;\n    }\n    \n    .step-counter {\n      display: none;\n    }\n    \n    .agents-grid,\n    .features-grid {\n      grid-template-columns: 1fr;\n    }\n    \n    .welcome-header {\n      flex-direction: column;\n      padding: 15px;\n    }\n    \n    .mascot-image {\n      margin-right: 0;\n      margin-bottom: 10px;\n    }\n    \n    .welcome-header .text-content {\n      align-items: center;\n      text-align: center;\n    }\n  }\n  \n  /* Responsive adjustments */\n  @media (max-width: 1280px) {\n    .agents-grid {\n      grid-template-columns: repeat(2, 1fr);\n    }\n  }\n  \n  @media (max-width: 768px) {\n    .agents-grid {\n      grid-template-columns: 1fr;\n    }\n    \n    .agent-card {\n      padding: 20px;\n    }\n    \n    .agents-section {\n      padding: 20px;\n    }\n  }\n  \n  /* Tab panels */\n  .tab-panel {\n    flex-grow: 1;\n    display: flex;\n    flex-direction: column;\n    overflow: hidden;\n  }\n  \n  .tab-panel-content {\n    flex-grow: 1;\n    overflow: auto;\n  }\n  \n  /* Insights tab styles */\n  .insights-container {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n  }\n  \n  .insights-list {\n    flex-grow: 1;\n    overflow-y: auto;\n    padding: 0.5rem;\n  }\n  \n  .insights-empty-state {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    text-align: center;\n    padding: 1rem;\n  }\n  \n  .insights-empty-icon {\n    width: 60px;\n    height: 60px;\n    margin-bottom: 1rem;\n  }\n  \n  .insights-empty-icon img {\n    width: 100%;\n    height: 100%;\n  }\n  \n  /* Mini chat fixes */\n  .dashboard-chat-container {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    overflow: hidden;\n  }\n  \n  .mini-chat-container {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    overflow: hidden;\n  }\n  \n  .mini-chat-messages {\n    flex-grow: 1;\n    overflow-y: auto;\n    padding: 1rem;\n  }\n  \n  /* Small spinner for buttons */\n  .spinner-small {\n    width: 16px;\n    height: 16px;\n    border: 2px solid rgba(255, 255, 255, 0.3);\n    border-radius: 50%;\n    border-top-color: white;\n    animation: spin 1s ease-in-out infinite;\n    display: inline-block;\n    vertical-align: middle;\n    margin-right: 0.25rem;\n  }\n  \n  @keyframes spin {\n    to { transform: rotate(360deg); }\n  }\n  \n  /* Chat header and tabs */\n  .chat-header {\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  }\n  \n  .customer-inquiry-bar {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 12px 16px;\n    background-color: #212b3c;\n    border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  }\n  \n  [data-theme=\"dark\"] .customer-inquiry-bar {\n    background-color: #212b3c;\n  }\n  \n  .customer-inquiry-input {\n    flex: 1;\n    padding: 10px 16px;\n    border-radius: 100px;\n    border: none;\n    font-size: 14px;\n    background-color: white;\n    color: #555;\n    height: 40px;\n  }\n  \n  [data-theme=\"dark\"] .customer-inquiry-input {\n    background-color: white;\n    color: #555;\n  }\n  \n  /* Team members avatars */\n  .team-members {\n    display: flex;\n    align-items: center;\n    margin-left: 8px;\n  }\n  \n  .team-member-avatar {\n    width: 32px;\n    height: 32px;\n    border-radius: 50%;\n    overflow: hidden;\n    margin-left: -8px;\n    border: 2px solid #212b3c;\n    position: relative;\n  }\n  \n  .team-member-avatar img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n  }\n  \n  .team-more-members {\n    background-color: #e9eaec;\n    color: #555;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n  }\n  \n  /* Tabs */\n  .dashboard-tabs {\n    display: flex;\n    background-color: #deeeea;\n  }\n  \n  [data-theme=\"dark\"] .dashboard-tabs {\n    background-color: #212b3c;\n  }\n  \n  .dashboard-tab {\n    padding: 16px 24px;\n    font-size: 14px;\n    font-weight: 500;\n    color: var(--color-primary);\n    cursor: pointer;\n    transition: all 0.15s ease;\n    position: relative;\n  }\n  \n  .dashboard-tab:hover {\n    color: rgba(255, 255, 255, 0.9);\n  }\n  \n  .dashboard-tab.active {\n    color: var(--color-primary);\n  }\n  \n  .dashboard-tab.active::after {\n    content: '';\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    height: 3px;\n    background-color: #2ac963;\n  }\n  \n  /* Chat selection dropdown */\n  .chat-selection {\n    padding: 12px 16px;\n    border-bottom: 1px solid var(--color-divider, #e5e7eb);\n  }\n  \n  [data-theme=\"dark\"] .chat-selection {\n    border-bottom-color: var(--color-divider, #374151);\n  }\n  \n  .chat-dropdown {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 10px 16px;\n    border-radius: 4px;\n    cursor: pointer;\n    font-weight: 500;\n    color: var(--color-text, #111827);\n  }\n  \n  [data-theme=\"dark\"] .chat-dropdown {\n    color: var(--color-text, #e5e7eb);\n  }\n  \n  .dropdown-icon {\n    width: 16px;\n    height: 16px;\n    color: var(--color-text-muted, #6b7280);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  \n  .dropdown-menu {\n    position: absolute;\n    top: calc(100% + 4px);\n    left: 0;\n    width: 100%;\n    background-color: white;\n    border: 1px solid var(--color-divider, #e5e7eb);\n    border-radius: 8px;\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n    z-index: 1000;\n  }\n  \n  [data-theme=\"dark\"] .dropdown-menu {\n    background-color: var(--color-surface-elevated, #1e2538);\n    border-color: var(--color-divider, #374151);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);\n  }\n  \n  .dropdown-item {\n    padding: 10px 16px;\n    cursor: pointer;\n    transition: background-color 0.15s ease;\n  }\n  \n  .dropdown-item:hover {\n    background-color: var(--color-surface-highlight, rgba(245, 247, 250, 0.6));\n  }\n  \n  [data-theme=\"dark\"] .dropdown-item:hover {\n    background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));\n  }\n  ", ".rbc-btn {\n  color: inherit;\n  font: inherit;\n  margin: 0;\n}\n\nbutton.rbc-btn {\n  overflow: visible;\n  text-transform: none;\n  appearance: button;\n  cursor: pointer;\n}\n\nbutton[disabled].rbc-btn {\n  cursor: not-allowed;\n}\n\nbutton.rbc-input::-moz-focus-inner {\n  border: 0;\n  padding: 0;\n}\n\n", "@charset \"UTF-8\";\n.rbc-btn {\n  color: inherit;\n  font: inherit;\n  margin: 0;\n}\n\nbutton.rbc-btn {\n  overflow: visible;\n  text-transform: none;\n  -webkit-appearance: button;\n     -moz-appearance: button;\n          appearance: button;\n  cursor: pointer;\n}\n\nbutton[disabled].rbc-btn {\n  cursor: not-allowed;\n}\n\nbutton.rbc-input::-moz-focus-inner {\n  border: 0;\n  padding: 0;\n}\n\n.rbc-calendar {\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  height: 100%;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-align: stretch;\n      -ms-flex-align: stretch;\n          align-items: stretch;\n}\n\n.rbc-m-b-negative-3 {\n  margin-bottom: -3px;\n}\n\n.rbc-h-full {\n  height: 100%;\n}\n\n.rbc-calendar *,\n.rbc-calendar *:before,\n.rbc-calendar *:after {\n  -webkit-box-sizing: inherit;\n          box-sizing: inherit;\n}\n\n.rbc-abs-full, .rbc-row-bg {\n  overflow: hidden;\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n}\n\n.rbc-ellipsis, .rbc-show-more, .rbc-row-segment .rbc-event-content, .rbc-event-label {\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.rbc-rtl {\n  direction: rtl;\n}\n\n.rbc-off-range {\n  color: #999999;\n}\n\n.rbc-off-range-bg {\n  background: #e6e6e6;\n}\n\n.rbc-header {\n  overflow: hidden;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0%;\n          flex: 1 0 0%;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 0 3px;\n  text-align: center;\n  vertical-align: middle;\n  font-weight: bold;\n  font-size: 90%;\n  min-height: 0;\n  border-bottom: 1px solid #ddd;\n}\n.rbc-header + .rbc-header {\n  border-left: 1px solid #ddd;\n}\n.rbc-rtl .rbc-header + .rbc-header {\n  border-left-width: 0;\n  border-right: 1px solid #ddd;\n}\n.rbc-header > a, .rbc-header > a:active, .rbc-header > a:visited {\n  color: inherit;\n  text-decoration: none;\n}\n\n.rbc-button-link {\n  color: inherit;\n  background: none;\n  margin: 0;\n  padding: 0;\n  border: none;\n  cursor: pointer;\n  -webkit-user-select: text;\n     -moz-user-select: text;\n      -ms-user-select: text;\n          user-select: text;\n}\n\n.rbc-row-content {\n  position: relative;\n  -moz-user-select: none;\n   -ms-user-select: none;\n       user-select: none;\n  -webkit-user-select: none;\n  z-index: 4;\n}\n\n.rbc-row-content-scrollable {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  height: 100%;\n}\n.rbc-row-content-scrollable .rbc-row-content-scroll-container {\n  height: 100%;\n  overflow-y: scroll;\n  -ms-overflow-style: none; /* IE and Edge */\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n  scrollbar-width: none; /* Firefox */\n  /* Hide scrollbar for Chrome, Safari and Opera */\n}\n.rbc-row-content-scrollable .rbc-row-content-scroll-container::-webkit-scrollbar {\n  display: none;\n}\n\n.rbc-today {\n  background-color: #eaf6ff;\n}\n\n.rbc-toolbar {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-wrap: wrap;\n      flex-wrap: wrap;\n  -webkit-box-pack: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  -webkit-box-align: center;\n      -ms-flex-align: center;\n          align-items: center;\n  margin-bottom: 10px;\n  font-size: 16px;\n}\n.rbc-toolbar .rbc-toolbar-label {\n  -webkit-box-flex: 1;\n      -ms-flex-positive: 1;\n          flex-grow: 1;\n  padding: 0 10px;\n  text-align: center;\n}\n.rbc-toolbar button {\n  color: #373a3c;\n  display: inline-block;\n  margin: 0;\n  text-align: center;\n  vertical-align: middle;\n  background: none;\n  background-image: none;\n  border: 1px solid #ccc;\n  padding: 0.375rem 1rem;\n  border-radius: 4px;\n  line-height: normal;\n  white-space: nowrap;\n}\n.rbc-toolbar button:active, .rbc-toolbar button.rbc-active {\n  background-image: none;\n  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n          box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n  background-color: #e6e6e6;\n  border-color: #adadad;\n}\n.rbc-toolbar button:active:hover, .rbc-toolbar button:active:focus, .rbc-toolbar button.rbc-active:hover, .rbc-toolbar button.rbc-active:focus {\n  color: #373a3c;\n  background-color: #d4d4d4;\n  border-color: #8c8c8c;\n}\n.rbc-toolbar button:focus {\n  color: #373a3c;\n  background-color: #e6e6e6;\n  border-color: #adadad;\n}\n.rbc-toolbar button:hover {\n  color: #373a3c;\n  cursor: pointer;\n  background-color: #e6e6e6;\n  border-color: #adadad;\n}\n\n.rbc-btn-group {\n  display: inline-block;\n  white-space: nowrap;\n}\n.rbc-btn-group > button:first-child:not(:last-child) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.rbc-btn-group > button:last-child:not(:first-child) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.rbc-rtl .rbc-btn-group > button:first-child:not(:last-child) {\n  border-radius: 4px;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.rbc-rtl .rbc-btn-group > button:last-child:not(:first-child) {\n  border-radius: 4px;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.rbc-btn-group > button:not(:first-child):not(:last-child) {\n  border-radius: 0;\n}\n.rbc-btn-group button + button {\n  margin-left: -1px;\n}\n.rbc-rtl .rbc-btn-group button + button {\n  margin-left: 0;\n  margin-right: -1px;\n}\n.rbc-btn-group + .rbc-btn-group, .rbc-btn-group + button {\n  margin-left: 10px;\n}\n\n@media (max-width: 767px) {\n  .rbc-toolbar {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n        -ms-flex-direction: column;\n            flex-direction: column;\n  }\n}\n.rbc-event, .rbc-day-slot .rbc-background-event {\n  border: none;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  -webkit-box-shadow: none;\n          box-shadow: none;\n  margin: 0;\n  padding: 2px 5px;\n  background-color: #3174ad;\n  border-radius: 5px;\n  color: #fff;\n  cursor: pointer;\n  width: 100%;\n  text-align: left;\n}\n.rbc-slot-selecting .rbc-event, .rbc-slot-selecting .rbc-day-slot .rbc-background-event, .rbc-day-slot .rbc-slot-selecting .rbc-background-event {\n  cursor: inherit;\n  pointer-events: none;\n}\n.rbc-event.rbc-selected, .rbc-day-slot .rbc-selected.rbc-background-event {\n  background-color: #265985;\n}\n.rbc-event:focus, .rbc-day-slot .rbc-background-event:focus {\n  outline: 5px auto #3b99fc;\n}\n\n.rbc-event-label {\n  font-size: 80%;\n}\n\n.rbc-event-overlaps {\n  -webkit-box-shadow: -1px 1px 5px 0px rgba(51, 51, 51, 0.5);\n          box-shadow: -1px 1px 5px 0px rgba(51, 51, 51, 0.5);\n}\n\n.rbc-event-continues-prior {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.rbc-event-continues-after {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.rbc-event-continues-earlier {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.rbc-event-continues-later {\n  border-bottom-left-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.rbc-row {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: row;\n          flex-direction: row;\n}\n\n.rbc-row-segment {\n  padding: 0 1px 1px 1px;\n}\n.rbc-selected-cell {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.rbc-show-more {\n  background-color: rgba(255, 255, 255, 0.3);\n  z-index: 4;\n  font-weight: bold;\n  font-size: 85%;\n  height: auto;\n  line-height: normal;\n  color: #3174ad;\n}\n.rbc-show-more:hover, .rbc-show-more:focus {\n  color: #265985;\n}\n\n.rbc-month-view {\n  position: relative;\n  border: 1px solid #ddd;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0px;\n          flex: 1 0 0;\n  width: 100%;\n  -moz-user-select: none;\n   -ms-user-select: none;\n       user-select: none;\n  -webkit-user-select: none;\n  height: 100%;\n}\n\n.rbc-month-header {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: row;\n          flex-direction: row;\n}\n\n.rbc-month-row {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  position: relative;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0px;\n          flex: 1 0 0;\n  -ms-flex-preferred-size: 0px;\n      flex-basis: 0px;\n  overflow: hidden;\n  height: 100%;\n}\n.rbc-month-row + .rbc-month-row {\n  border-top: 1px solid #ddd;\n}\n\n.rbc-date-cell {\n  -webkit-box-flex: 1;\n      -ms-flex: 1 1 0px;\n          flex: 1 1 0;\n  min-width: 0;\n  padding-right: 5px;\n  text-align: right;\n}\n.rbc-date-cell.rbc-now {\n  font-weight: bold;\n}\n.rbc-date-cell > a, .rbc-date-cell > a:active, .rbc-date-cell > a:visited {\n  color: inherit;\n  text-decoration: none;\n}\n\n.rbc-row-bg {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: row;\n          flex-direction: row;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0px;\n          flex: 1 0 0;\n  overflow: hidden;\n  right: 1px;\n}\n\n.rbc-day-bg {\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0%;\n          flex: 1 0 0%;\n}\n.rbc-day-bg + .rbc-day-bg {\n  border-left: 1px solid #ddd;\n}\n.rbc-rtl .rbc-day-bg + .rbc-day-bg {\n  border-left-width: 0;\n  border-right: 1px solid #ddd;\n}\n\n.rbc-overlay {\n  position: absolute;\n  z-index: 5;\n  border: 1px solid #e5e5e5;\n  background-color: #fff;\n  -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);\n          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);\n  padding: 10px;\n}\n.rbc-overlay > * + * {\n  margin-top: 1px;\n}\n\n.rbc-overlay-header {\n  border-bottom: 1px solid #e5e5e5;\n  margin: -10px -10px 5px -10px;\n  padding: 2px 10px;\n}\n\n.rbc-agenda-view {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0px;\n          flex: 1 0 0;\n  overflow: auto;\n}\n.rbc-agenda-view table.rbc-agenda-table {\n  width: 100%;\n  border: 1px solid #ddd;\n  border-spacing: 0;\n  border-collapse: collapse;\n}\n.rbc-agenda-view table.rbc-agenda-table tbody > tr > td {\n  padding: 5px 10px;\n  vertical-align: top;\n}\n.rbc-agenda-view table.rbc-agenda-table .rbc-agenda-time-cell {\n  padding-left: 15px;\n  padding-right: 15px;\n  text-transform: lowercase;\n}\n.rbc-agenda-view table.rbc-agenda-table tbody > tr > td + td {\n  border-left: 1px solid #ddd;\n}\n.rbc-rtl .rbc-agenda-view table.rbc-agenda-table tbody > tr > td + td {\n  border-left-width: 0;\n  border-right: 1px solid #ddd;\n}\n.rbc-agenda-view table.rbc-agenda-table tbody > tr + tr {\n  border-top: 1px solid #ddd;\n}\n.rbc-agenda-view table.rbc-agenda-table thead > tr > th {\n  padding: 3px 5px;\n  text-align: left;\n  border-bottom: 1px solid #ddd;\n}\n.rbc-rtl .rbc-agenda-view table.rbc-agenda-table thead > tr > th {\n  text-align: right;\n}\n\n.rbc-agenda-time-cell {\n  text-transform: lowercase;\n}\n.rbc-agenda-time-cell .rbc-continues-after:after {\n  content: \" »\";\n}\n.rbc-agenda-time-cell .rbc-continues-prior:before {\n  content: \"« \";\n}\n\n.rbc-agenda-date-cell,\n.rbc-agenda-time-cell {\n  white-space: nowrap;\n}\n\n.rbc-agenda-event-cell {\n  width: 100%;\n}\n\n.rbc-time-column {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  min-height: 100%;\n}\n.rbc-time-column .rbc-timeslot-group {\n  -webkit-box-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n}\n\n.rbc-timeslot-group {\n  border-bottom: 1px solid #ddd;\n  min-height: 40px;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-flow: column nowrap;\n          flex-flow: column nowrap;\n}\n\n.rbc-time-gutter,\n.rbc-header-gutter {\n  -webkit-box-flex: 0;\n      -ms-flex: none;\n          flex: none;\n}\n\n.rbc-label {\n  padding: 0 5px;\n}\n\n.rbc-day-slot {\n  position: relative;\n}\n.rbc-day-slot .rbc-events-container {\n  bottom: 0;\n  left: 0;\n  position: absolute;\n  right: 0;\n  margin-right: 10px;\n  top: 0;\n}\n.rbc-day-slot .rbc-events-container.rbc-rtl {\n  left: 10px;\n  right: 0;\n}\n.rbc-day-slot .rbc-event, .rbc-day-slot .rbc-background-event {\n  border: 1px solid #265985;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  max-height: 100%;\n  min-height: 20px;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-flow: column wrap;\n          flex-flow: column wrap;\n  -webkit-box-align: start;\n      -ms-flex-align: start;\n          align-items: flex-start;\n  overflow: hidden;\n  position: absolute;\n}\n.rbc-day-slot .rbc-background-event {\n  opacity: 0.75;\n}\n.rbc-day-slot .rbc-event-label {\n  -webkit-box-flex: 0;\n      -ms-flex: none;\n          flex: none;\n  padding-right: 5px;\n  width: auto;\n}\n.rbc-day-slot .rbc-event-content {\n  width: 100%;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 1 0px;\n          flex: 1 1 0;\n  word-wrap: break-word;\n  line-height: 1;\n  height: 100%;\n  min-height: 1em;\n}\n.rbc-day-slot .rbc-time-slot {\n  border-top: 1px solid #f7f7f7;\n}\n\n.rbc-time-view-resources .rbc-time-gutter,\n.rbc-time-view-resources .rbc-time-header-gutter {\n  position: sticky;\n  left: 0;\n  background-color: white;\n  border-right: 1px solid #ddd;\n  z-index: 10;\n  margin-right: -1px;\n}\n.rbc-time-view-resources .rbc-time-header {\n  overflow: hidden;\n}\n.rbc-time-view-resources .rbc-time-header-content {\n  min-width: auto;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0px;\n          flex: 1 0 0;\n  -ms-flex-preferred-size: 0px;\n      flex-basis: 0px;\n}\n.rbc-time-view-resources .rbc-time-header-cell-single-day {\n  display: none;\n}\n.rbc-time-view-resources .rbc-day-slot {\n  min-width: 140px;\n}\n.rbc-time-view-resources .rbc-header,\n.rbc-time-view-resources .rbc-day-bg {\n  width: 140px;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 1 0px;\n          flex: 1 1 0;\n  -ms-flex-preferred-size: 0 px;\n      flex-basis: 0 px;\n}\n\n.rbc-time-header-content + .rbc-time-header-content {\n  margin-left: -1px;\n}\n\n.rbc-time-slot {\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0px;\n          flex: 1 0 0;\n}\n.rbc-time-slot.rbc-now {\n  font-weight: bold;\n}\n\n.rbc-day-header {\n  text-align: center;\n}\n\n.rbc-slot-selection {\n  z-index: 10;\n  position: absolute;\n  background-color: rgba(0, 0, 0, 0.5);\n  color: white;\n  font-size: 75%;\n  width: 100%;\n  padding: 3px;\n}\n\n.rbc-slot-selecting {\n  cursor: move;\n}\n\n.rbc-time-view {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n  width: 100%;\n  border: 1px solid #ddd;\n  min-height: 0;\n}\n.rbc-time-view .rbc-time-gutter {\n  white-space: nowrap;\n  text-align: right;\n}\n.rbc-time-view .rbc-allday-cell {\n  -webkit-box-sizing: content-box;\n          box-sizing: content-box;\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n.rbc-time-view .rbc-allday-cell + .rbc-allday-cell {\n  border-left: 1px solid #ddd;\n}\n.rbc-time-view .rbc-allday-events {\n  position: relative;\n  z-index: 4;\n}\n.rbc-time-view .rbc-row {\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  min-height: 20px;\n}\n\n.rbc-time-header {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-flex: 0;\n      -ms-flex: 0 0 auto;\n          flex: 0 0 auto;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: row;\n          flex-direction: row;\n}\n.rbc-time-header.rbc-overflowing {\n  border-right: 1px solid #ddd;\n}\n.rbc-rtl .rbc-time-header.rbc-overflowing {\n  border-right-width: 0;\n  border-left: 1px solid #ddd;\n}\n.rbc-time-header > .rbc-row:first-child {\n  border-bottom: 1px solid #ddd;\n}\n.rbc-time-header > .rbc-row.rbc-row-resource {\n  border-bottom: 1px solid #ddd;\n}\n\n.rbc-time-header-cell-single-day {\n  display: none;\n}\n\n.rbc-time-header-content {\n  -webkit-box-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  min-width: 0;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  border-left: 1px solid #ddd;\n}\n.rbc-rtl .rbc-time-header-content {\n  border-left-width: 0;\n  border-right: 1px solid #ddd;\n}\n.rbc-time-header-content > .rbc-row.rbc-row-resource {\n  border-bottom: 1px solid #ddd;\n  -ms-flex-negative: 0;\n      flex-shrink: 0;\n}\n\n.rbc-time-content {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0%;\n          flex: 1 0 0%;\n  -webkit-box-align: start;\n      -ms-flex-align: start;\n          align-items: flex-start;\n  width: 100%;\n  border-top: 2px solid #ddd;\n  overflow-y: auto;\n  position: relative;\n}\n.rbc-time-content > .rbc-time-gutter {\n  -webkit-box-flex: 0;\n      -ms-flex: none;\n          flex: none;\n}\n.rbc-time-content > * + * > * {\n  border-left: 1px solid #ddd;\n}\n.rbc-rtl .rbc-time-content > * + * > * {\n  border-left-width: 0;\n  border-right: 1px solid #ddd;\n}\n.rbc-time-content > .rbc-day-slot {\n  width: 100%;\n  -moz-user-select: none;\n   -ms-user-select: none;\n       user-select: none;\n  -webkit-user-select: none;\n}\n\n.rbc-current-time-indicator {\n  position: absolute;\n  z-index: 3;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background-color: #74ad31;\n  pointer-events: none;\n}\n\n.rbc-resource-grouping.rbc-time-header-content {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n}\n.rbc-resource-grouping .rbc-row .rbc-header {\n  width: 141px;\n}\n\n/*# sourceMappingURL=react-big-calendar.css.map */", "@import './variables';\n@import './reset';\n\n.rbc-calendar {\n  box-sizing: border-box;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n}\n\n.rbc-m-b-negative-3 {\n  margin-bottom: -3px;\n}\n\n.rbc-h-full {\n  height: 100%;\n}\n\n.rbc-calendar *,\n.rbc-calendar *:before,\n.rbc-calendar *:after {\n  box-sizing: inherit;\n}\n\n.rbc-abs-full {\n  overflow: hidden;\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n}\n\n.rbc-ellipsis {\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.rbc-rtl {\n  direction: rtl;\n}\n\n.rbc-off-range {\n  color: $out-of-range-color;\n}\n\n.rbc-off-range-bg {\n  background: $out-of-range-bg-color;\n}\n\n.rbc-header {\n  overflow: hidden;\n  flex: 1 0 0%;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 0 3px;\n  text-align: center;\n  vertical-align: middle;\n  font-weight: bold;\n  font-size: 90%;\n  min-height: 0;\n  border-bottom: 1px solid $cell-border;\n\n  & + & {\n    border-left: 1px solid $cell-border;\n  }\n\n  .rbc-rtl & + & {\n    border-left-width: 0;\n    border-right: 1px solid $cell-border;\n  }\n\n  & > a {\n    &,\n    &:active,\n    &:visited {\n      color: inherit;\n      text-decoration: none;\n    }\n  }\n}\n\n.rbc-button-link {\n  color: inherit;\n  background: none;\n  margin: 0;\n  padding: 0;\n  border: none;\n  cursor: pointer;\n  user-select: text;\n}\n\n.rbc-row-content {\n  position: relative;\n  user-select: none;\n  -webkit-user-select: none;\n  z-index: 4;\n}\n\n.rbc-row-content-scrollable {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n\n  .rbc-row-content-scroll-container {\n    height: 100%;\n    overflow-y: scroll;\n    -ms-overflow-style: none; /* IE and Edge */\n    scrollbar-width: none; /* Firefox */\n\n    -ms-overflow-style: none; /* IE and Edge */\n    scrollbar-width: none; /* Firefox */\n\n    /* Hide scrollbar for Chrome, Safari and Opera */\n    &::-webkit-scrollbar {\n      display: none;\n    }\n  }\n}\n\n.rbc-today {\n  background-color: $today-highlight-bg;\n}\n\n@import './toolbar';\n@import './event';\n@import './month';\n@import './agenda';\n@import './time-grid';\n", "@import './variables';\n\n$active-background: darken($btn-bg, 10%);\n$active-border: darken($btn-border, 12%);\n\n.rbc-toolbar {\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: center;\n  align-items: center;\n  margin-bottom: 10px;\n  font-size: 16px;\n\n  .rbc-toolbar-label {\n    flex-grow:1;\n    padding: 0 10px;\n    text-align: center;\n  }\n\n  & button {\n    color: $btn-color;\n    display: inline-block;\n    margin: 0;\n    text-align: center;\n    vertical-align: middle;\n    background: none;\n    background-image: none;\n    border: 1px solid $btn-border;\n    padding: .375rem 1rem;\n    border-radius: 4px;\n    line-height: normal;\n    white-space: nowrap;\n\n    &:active,\n    &.rbc-active {\n      background-image: none;\n      box-shadow: inset 0 3px 5px rgba(0,0,0,.125);\n      background-color: $active-background;\n      border-color: $active-border;\n\n      &:hover,\n      &:focus {\n        color: $btn-color;\n        background-color: darken($btn-bg, 17%);\n        border-color: darken($btn-border, 25%);\n      }\n    }\n\n    &:focus {\n      color: $btn-color;\n      background-color: $active-background;\n      border-color: $active-border;\n    }\n\n    &:hover {\n      color: $btn-color;\n      cursor: pointer;\n      background-color: $active-background;\n          border-color: $active-border;\n    }\n  }\n}\n\n.rbc-btn-group {\n  display: inline-block;\n  white-space: nowrap;\n\n  > button:first-child:not(:last-child) {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n\n  > button:last-child:not(:first-child) {\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n  }\n\n  .rbc-rtl & > button:first-child:not(:last-child) {\n    border-radius: 4px;\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n  }\n\n  .rbc-rtl & > button:last-child:not(:first-child) {\n    border-radius: 4px;\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n\n  > button:not(:first-child):not(:last-child) {\n    border-radius: 0;\n  }\n\n  button + button {\n    margin-left: -1px;\n  }\n\n  .rbc-rtl & button + button {\n    margin-left: 0;\n    margin-right: -1px;\n  }\n\n  & + &,\n  & + button {\n    margin-left: 10px;\n  }\n}\n\n@media (max-width: 767px) {\n  .rbc-toolbar {\n    flex-direction: column;\n  }\n}\n", "$out-of-range-color: lighten(#333, 40%) !default;\n$out-of-range-bg-color: lighten(#333, 70%) !default;\n\n$calendar-border: #ddd !default;\n$cell-border: #ddd !default;\n\n$border-color: #ccc !default;\n\n// Each calendar segment is 1/7th.\n$segment-width: 0.14286% !default;\n\n$time-selection-color: white !default;\n$time-selection-bg-color: rgba(0, 0, 0, 0.5) !default;\n$date-selection-bg-color: rgba(0, 0, 0, 0.1) !default;\n\n$event-bg: #3174ad !default;\n$event-border: darken(#3174ad, 10%) !default;\n$event-outline: #3b99fc !default;\n$event-color: #fff !default;\n$event-border-radius: 5px !default;\n$event-padding: 2px 5px !default;\n$event-zindex: 4 !default;\n\n$btn-color: #373a3c !default;\n$btn-bg: #fff !default;\n$btn-border: #ccc !default;\n\n$current-time-color: #74ad31 !default;\n\n$rbc-css-prefix: rbc-i !default;\n\n$today-highlight-bg: #eaf6ff !default;\n", "@import './variables';\n\n.rbc-event {\n  border: none;\n  box-sizing: border-box;\n  box-shadow: none;\n  margin: 0;\n  padding: $event-padding;\n  background-color: $event-bg;\n  border-radius: $event-border-radius;\n  color: $event-color;\n  cursor: pointer;\n  width: 100%;\n  text-align: left;\n\n  .rbc-slot-selecting & {\n    cursor: inherit;\n    pointer-events: none;\n  }\n\n  &.rbc-selected {\n    background-color: darken($event-bg, 10%);\n  }\n\n  &:focus {\n    outline: 5px auto $event-outline;\n  }\n}\n\n.rbc-event-label {\n  @extend .rbc-ellipsis;\n  font-size: 80%;\n}\n\n.rbc-event-overlaps {\n  box-shadow: -1px 1px 5px 0px rgba(51,51,51,.5);\n}\n\n.rbc-event-continues-prior {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.rbc-event-continues-after {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n\n.rbc-event-continues-earlier {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n.rbc-event-continues-later {\n  border-bottom-left-radius: 0;\n  border-bottom-right-radius: 0;\n}\n", "@import './variables';\n\n.rbc-row {\n  display: flex;\n  flex-direction: row;\n}\n\n.rbc-row-segment {\n  padding: 0 1px 1px 1px;\n\n  .rbc-event-content {\n    @extend .rbc-ellipsis;\n  }\n}\n\n.rbc-selected-cell {\n  background-color: $date-selection-bg-color;\n}\n\n.rbc-show-more {\n  @extend .rbc-ellipsis;\n  background-color: rgba(255, 255, 255, 0.3);\n  z-index: $event-zindex;\n  font-weight: bold;\n  font-size: 85%;\n  height: auto;\n  line-height: normal;\n  color: $event-bg;\n  &:hover,\n  &:focus {\n    color: darken($event-bg, 10%);\n  }\n}\n\n.rbc-month-view {\n  position: relative;\n  border: 1px solid $calendar-border;\n  display: flex;\n  flex-direction: column;\n  flex: 1 0 0;\n  width: 100%;\n  user-select: none;\n  -webkit-user-select: none;\n\n  height: 100%; // ie-fix\n}\n\n.rbc-month-header {\n  display: flex;\n  flex-direction: row;\n}\n\n.rbc-month-row {\n  display: flex;\n  position: relative;\n  flex-direction: column;\n  flex: 1 0 0; // postcss will remove the 0px here hence the duplication below\n  flex-basis: 0px;\n  overflow: hidden;\n\n  height: 100%; // ie-fix\n\n  & + & {\n    border-top: 1px solid $cell-border;\n  }\n}\n\n.rbc-date-cell {\n  flex: 1 1 0;\n  min-width: 0;\n  padding-right: 5px;\n  text-align: right;\n\n  &.rbc-now {\n    font-weight: bold;\n  }\n\n  > a {\n    &,\n    &:active,\n    &:visited {\n      color: inherit;\n      text-decoration: none;\n    }\n  }\n}\n\n.rbc-row-bg {\n  @extend .rbc-abs-full;\n  display: flex;\n  flex-direction: row;\n  flex: 1 0 0;\n  overflow: hidden;\n  right: 1px;\n}\n\n.rbc-day-bg {\n  flex: 1 0 0%;\n\n  & + & {\n    border-left: 1px solid $cell-border;\n  }\n\n  .rbc-rtl & + & {\n    border-left-width: 0;\n    border-right: 1px solid $cell-border;\n  }\n}\n\n.rbc-overlay {\n  position: absolute;\n  z-index: $event-zindex + 1;\n  border: 1px solid #e5e5e5;\n  background-color: #fff;\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);\n  padding: 10px;\n\n  > * + * {\n    margin-top: 1px;\n  }\n}\n\n.rbc-overlay-header {\n  border-bottom: 1px solid #e5e5e5;\n  margin: -10px -10px 5px -10px;\n  padding: 2px 10px;\n}\n", "@import './variables';\n\n.rbc-agenda-view {\n  display: flex;\n  flex-direction: column;\n  flex: 1 0 0;\n  overflow: auto;\n\n  table.rbc-agenda-table {\n    width: 100%;\n    border: 1px solid $cell-border;\n    border-spacing: 0;\n    border-collapse: collapse;\n\n    tbody > tr > td {\n      padding: 5px 10px;\n      vertical-align: top;\n    }\n\n    .rbc-agenda-time-cell {\n      padding-left: 15px;\n      padding-right: 15px;\n      text-transform: lowercase;\n    }\n\n    tbody > tr > td + td {\n      border-left: 1px solid $cell-border;\n    }\n\n    .rbc-rtl & {\n      tbody > tr > td + td {\n        border-left-width: 0;\n        border-right: 1px solid $cell-border;\n      }\n    }\n\n    tbody > tr + tr {\n      border-top: 1px solid $cell-border;\n    }\n\n    thead > tr > th {\n      padding: 3px 5px;\n      text-align: left;\n      border-bottom: 1px solid $cell-border;\n\n      .rbc-rtl & {\n        text-align: right;\n      }\n    }\n  }\n}\n\n.rbc-agenda-time-cell {\n  text-transform: lowercase;\n\n  .rbc-continues-after:after {\n    content: ' »'\n  }\n  .rbc-continues-prior:before {\n    content: '« '\n  }\n}\n\n.rbc-agenda-date-cell,\n.rbc-agenda-time-cell {\n  white-space: nowrap;\n}\n\n\n\n.rbc-agenda-event-cell {\n  width: 100%\n}\n", "@import './variables';\n\n.rbc-time-column {\n  display: flex;\n  flex-direction: column;\n  min-height: 100%;\n\n  .rbc-timeslot-group {\n    flex: 1;\n  }\n}\n\n\n.rbc-timeslot-group {\n  border-bottom: 1px solid $cell-border;\n  min-height: 40px;\n  display: flex;\n  flex-flow: column nowrap;\n}\n\n.rbc-time-gutter,\n.rbc-header-gutter {\n  flex: none;\n}\n\n.rbc-label {\n  padding: 0 5px;\n}\n\n.rbc-day-slot {\n  position: relative;\n\n  .rbc-events-container {\n    bottom: 0;\n    left: 0;\n    position: absolute;\n    right: 0;\n    margin-right: 10px;\n    top: 0;\n\n    &.rbc-rtl {\n      left: 10px;\n      right: 0;\n    }\n  }\n\n  .rbc-event {\n    border: 1px solid $event-border;\n    display: flex;\n    max-height: 100%;\n    min-height: 20px;\n    flex-flow: column wrap;\n    align-items: flex-start;\n    overflow: hidden;\n    position: absolute;\n  }\n  \n  .rbc-background-event {\n    @extend .rbc-event;\n    opacity: 0.75;\n  }\n\n  .rbc-event-label {\n    flex: none;\n    padding-right: 5px;\n    width: auto;\n  }\n\n  .rbc-event-content {\n    width: 100%;\n    flex: 1 1 0;\n    word-wrap: break-word;\n    line-height: 1;\n    height: 100%;\n    min-height: 1em;\n  }\n\n  .rbc-time-slot {\n    border-top: 1px solid lighten($cell-border, 10%);\n  }\n}\n\n.rbc-time-view-resources {\n  .rbc-time-gutter,\n  .rbc-time-header-gutter {\n    position: sticky;\n    left: 0;\n    background-color: white;\n    border-right: 1px solid $cell-border;\n    z-index: 10;\n    margin-right: -1px;\n  }\n\n  .rbc-time-header {\n    overflow: hidden;\n  }\n\n  .rbc-time-header-content {\n    min-width: auto;\n    flex: 1 0 0;\n    flex-basis: 0px;\n  }\n\n  .rbc-time-header-cell-single-day {\n    display: none;\n  }\n\n  .rbc-day-slot {\n    min-width: 140px;\n  }\n\n  .rbc-header,\n  .rbc-day-bg {\n    width: 140px;\n    // min-width: 0;\n    flex:  1 1 0;\n    flex-basis: 0 px;\n  }\n}\n\n.rbc-time-header-content + .rbc-time-header-content {\n  margin-left: -1px;\n}\n\n.rbc-time-slot {\n  flex: 1 0 0;\n\n  &.rbc-now {\n    font-weight: bold;\n  }\n}\n\n.rbc-day-header {\n  text-align: center;\n}\n", "@import './variables';\n@import './time-column';\n\n.rbc-slot-selection {\n  z-index: 10;\n  position: absolute;\n  background-color: $time-selection-bg-color;\n  color: $time-selection-color;\n  font-size: 75%;\n  width: 100%;\n  padding: 3px;\n}\n\n.rbc-slot-selecting {\n  cursor: move;\n}\n\n.rbc-time-view {\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n  width: 100%;\n  border: 1px solid $calendar-border;\n  min-height: 0;\n\n  .rbc-time-gutter {\n    white-space: nowrap;\n    text-align: right;\n  }\n\n  .rbc-allday-cell {\n    box-sizing: content-box;\n    width: 100%;\n    height: 100%;\n    position: relative;\n  }\n  .rbc-allday-cell + .rbc-allday-cell {\n    border-left: 1px solid $cell-border;\n  }\n\n  .rbc-allday-events {\n    position: relative;\n    z-index: 4;\n  }\n\n  .rbc-row {\n    box-sizing: border-box;\n    min-height: 20px;\n  }\n}\n\n.rbc-time-header {\n  display: flex;\n  flex: 0 0 auto; // should not shrink below height\n  flex-direction: row;\n\n  &.rbc-overflowing {\n    border-right: 1px solid $cell-border;\n  }\n\n  .rbc-rtl &.rbc-overflowing {\n    border-right-width: 0;\n    border-left: 1px solid $cell-border;\n  }\n\n  > .rbc-row:first-child {\n    border-bottom: 1px solid $cell-border;\n  }\n\n  > .rbc-row.rbc-row-resource {\n    border-bottom: 1px solid $cell-border;\n  }\n\n  // .rbc-gutter-cell {\n  //   flex: none;\n  // }\n\n  // > .rbc-gutter-cell + * {\n  //   width: 100%;\n  // }\n}\n\n.rbc-time-header-cell-single-day {\n  display: none;\n}\n\n.rbc-time-header-content {\n  flex: 1;\n  display: flex;\n  min-width: 0;\n  flex-direction: column;\n  border-left: 1px solid $cell-border;\n\n  .rbc-rtl & {\n    border-left-width: 0;\n    border-right: 1px solid $cell-border;\n  }\n\n  > .rbc-row.rbc-row-resource {\n    border-bottom: 1px solid $cell-border;\n    flex-shrink: 0;\n  }\n}\n\n.rbc-time-content {\n  display: flex;\n  flex: 1 0 0%;\n  align-items: flex-start;\n  width: 100%;\n  border-top: 2px solid $calendar-border;\n  overflow-y: auto;\n  position: relative;\n\n  > .rbc-time-gutter {\n    flex: none;\n  }\n\n  > * + * > * {\n    border-left: 1px solid $cell-border;\n  }\n\n  .rbc-rtl & > * + * > * {\n    border-left-width: 0;\n    border-right: 1px solid $cell-border;\n  }\n\n  > .rbc-day-slot {\n    width: 100%;\n    user-select: none;\n    -webkit-user-select: none;\n  }\n}\n\n.rbc-current-time-indicator {\n  position: absolute;\n  z-index: 3;\n  left: 0;\n  right: 0;\n  height: 1px;\n\n  background-color: $current-time-color;\n  pointer-events: none;\n}\n\n.rbc-resource-grouping {\n  &.rbc-time-header-content {\n    display: flex;\n    flex-direction: column;\n  }\n\n  .rbc-row .rbc-header {\n    width: 141px;\n  }\n}", "/* Campaign Calendar Styles */\n\n/* Import react-big-calendar styles at the component level */\n@import 'react-big-calendar/lib/css/react-big-calendar.css';\n@import '../../variables.css';\n\n.campaign-calendar-widget {\n  margin-bottom: 2rem;\n}\n.rbc-show-more{\n    color: var(--color-brunswick-green, #23504A);\n}\n\n.campaigns-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1.5rem;\n}\n.rbc-overlay{\n    background-color: var(--color-background);\n}\n\n.section-heading {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin: 0;\n}\n\n.add-campaign-btn {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  background-color: var(--color-brunswick-green, #23504A);\n  color: white;\n  border: none;\n  border-radius: 0.375rem;\n  padding: 0.5rem 1rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.add-campaign-btn:hover {\n  background-color: var(--color-dark-jungle-green, #0D211D);\n}\n\n/* Loading and Empty States */\n.loading-campaigns,\n.no-campaigns {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  gap: 1rem;\n  padding: 3rem 0;\n  color: var(--color-text-muted, #6b7280);\n  font-size: 0.875rem;\n  background-color: var(--color-surface-muted, #f9fafb);\n  border-radius: 0.5rem;\n  text-align: center;\n}\n\n.no-campaign-selected {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100%;\n  color: var(--color-text-muted, #6b7280);\n  font-size: 0.875rem;\n  font-style: italic;\n}\n\n/* Campaign Grid Layout */\n.campaigns-calendar-grid {\n  display: grid;\n  grid-template-columns: 1fr 300px;\n  gap: 1.5rem;\n}\n\n/* Improved Calendar Column with reduced height */\n.calendar-column {\n  background-color: var(--color-surface, white);\n  border-radius: 0.5rem;\n  border: 1px solid var(--color-border, #e5e7eb);\n  overflow: hidden;\n  min-height: 500px; /* Match the height used in the component */\n}\n\n/* View Switcher Styles - Updated for Button components */\n.view-switcher {\n  display: flex;\n  gap: 0.5rem;\n}\n\n/* Big Calendar Overrides */\n.rbc-toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.75rem;\n  flex-wrap: wrap;\n  gap: 0.75rem;\n  padding: 0.5rem 1rem;\n}\n\n.rbc-toolbar-label {\n  font-size: 1rem;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.05em;\n}\n\n.toolbar-controls {\n  display: flex;\n  gap: 0.5rem;\n}\n\n/* Calendar Brand Color Overrides */\n.rbc-active {\n  background-color: var(--color-pine-green, #00766D) !important;\n  color: white !important;\n}\n\n.rbc-today {\n  background-color: var(--color-surface-secondary) !important;\n}\n\n.rbc-event {\n  background-color: var(--color-brunswick-green, #23504A) !important;\n  border-color: var(--color-dark-jungle-green, #0D211D) !important;\n}\n\n.rbc-selected {\n  background-color: var(--color-emerald-green, #22AD85) !important;\n}\n\n.rbc-day-slot .rbc-event {\n  border-left: 5px solid var(--color-emerald-green, #22AD85) !important;\n}\n\n.rbc-btn-group button {\n  color: var(--color-on-background);\n}\n\n.rbc-btn-group button.rbc-active {\n  background-color: var(--color-brunswick-green, #23504A) !important;\n  color: white !important;\n  border-color: var(--color-dark-jungle-green, #0D211D) !important;\n}\n\n.rbc-btn-group button:hover {\n  background-color: var(--color-honeydew, #DFF4E9) !important;\n  color: var(--color-dark-jungle-green, #0D211D) !important;\n}\n\n/* Empty states styling */\n.empty-campaigns-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n  padding: 2rem 1rem;\n  text-align: center;\n  height: 100%;\n  color: var(--color-text-muted, #6b7280);\n}\n\n.empty-campaigns-state p {\n  margin: 0;\n  font-size: 0.875rem;\n}\n\n.campaign-details-loading {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 2rem;\n  height: 100%;\n  color: var(--color-text-muted, #6b7280);\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .campaigns-calendar-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .delivery-stats {\n    grid-template-columns: 1fr;\n  }\n  \n  .rbc-toolbar {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n  \n  .rbc-toolbar-label {\n    margin-bottom: 0.5rem;\n  }\n  \n  .view-switcher {\n    width: 100%;\n    margin-bottom: 0.5rem;\n  }\n  \n  .toolbar-controls {\n    margin-bottom: 0.5rem;\n    width: 100%;\n    justify-content: space-between;\n  }\n  \n  .add-campaign-btn {\n    width: 100%;\n    justify-content: center;\n  }\n  \n  .campaign-details-column {\n    height: auto;\n    min-height: 300px;\n  }\n}\n\n/* Campaign Details Column */\n.campaign-details-column {\n  background-color: var(--color-surface, white);\n  border-radius: 0.5rem;\n  border: 1px solid var(--color-border, #e5e7eb);\n  overflow: hidden;\n  min-height: 500px; /* Match calendar height for better alignment */\n  display: flex;\n  flex-direction: column;\n}\n\n.campaign-details {\n  padding: 1.25rem;\n  height: 100%;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n}\n\n.campaign-header {\n  margin-bottom: 1.25rem;\n}\n\n.campaign-title {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin: 0 0 0.75rem 0;\n}\n\n.campaign-title.clickable {\n  cursor: pointer;\n  transition: color 0.2s;\n  display: inline-block;\n}\n\n.campaign-title.clickable:hover {\n  text-decoration: underline;\n  color: var(--color-brunswick-green, #23504A);\n}\n\n.campaign-meta {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  flex-wrap: wrap;\n}\n\n.status-badge {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.25rem 0.5rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 500;\n  background-color: var(--color-surface-muted, #f3f4f6);\n  color: var(--color-text-muted, #6b7280);\n}\n\n.status-badge.active {\n  background-color: var(--color-emerald-green, #22AD85);\n  color: white;\n}\n\n.status-badge.scheduled {\n  background-color: var(--color-honeydew, #DFF4E9);\n  color: var(--color-dark-jungle-green, #0D211D);\n}\n\n.status-badge.draft {\n  background-color: var(--color-surface-muted, #f3f4f6);\n}\n\n.status-badge.completed {\n  background-color: var(--color-brunswick-green, #23504A);\n  color: white;\n}\n\n.channel-badge {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.25rem 0.5rem;\n  border-radius: 9999px;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.channel-badge.email {\n  background-color: rgba(35, 80, 74, 0.15);\n}\n\n.channel-badge.sms {\n  background-color: rgba(0, 118, 109, 0.15);\n}\n\n.channel-badge.push {\n  background-color: rgba(34, 173, 133, 0.15);\n}\n\n.channel-badge.automation {\n  background-color: rgba(13, 33, 29, 0.15);\n}\n\n.campaign-dates {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n  margin-bottom: 1.25rem;\n  padding: 1rem;\n  background-color: var(--color-surface-muted, #f9fafb);\n  border-radius: 0.5rem;\n}\n\n.date-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.date-label {\n  font-size: 0.75rem;\n  color: var(--color-text-muted, #6b7280);\n}\n\n.date-value {\n  font-weight: 500;\n}\n\n.campaign-description {\n  margin-bottom: 1.25rem;\n}\n\n.campaign-description h4 {\n  font-size: 0.875rem;\n  font-weight: 600;\n  margin: 0 0 0.5rem 0;\n}\n\n.campaign-description p {\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: var(--color-text-secondary, #4b5563);\n  margin: 0;\n}\n\n.campaign-tags {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n  margin-bottom: 1.25rem;\n}\n\n.tag {\n  display: inline-flex;\n  padding: 0.25rem 0.5rem;\n  background-color: var(--color-honeydew, #DFF4E9);\n  color: var(--color-brunswick-green, #23504A);\n  border-radius: 0.25rem;\n  font-size: 0.75rem;\n}\n\n.delivery-stats {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 0.75rem;\n  margin-bottom: 1.25rem;\n  background-color: var(--color-surface-muted, #f9fafb);\n  border-radius: 0.5rem;\n  padding: 1rem;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  gap: 0.25rem;\n}\n\n.stat-label {\n  font-size: 0.75rem;\n  color: var(--color-text-muted, #6b7280);\n}\n\n.stat-value {\n  font-weight: 500;\n  font-size: 0.875rem;\n}\n\n.campaign-actions {\n  display: flex;\n  gap: 0.75rem;\n  margin-top: 1.5rem;\n}\n\n.campaign-actions .ui-button {\n  flex: 1;\n}\n\n.action-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.5rem 1rem;\n  border-radius: 0.375rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.action-button.primary {\n  background-color: var(--color-brunswick-green, #23504A);\n  color: white;\n  border: none;\n}\n\n.action-button.primary:hover {\n  background-color: var(--color-dark-jungle-green, #0D211D);\n}\n\n.action-button.secondary {\n  background-color: transparent;\n  color: var(--color-brunswick-green, #23504A);\n  border: 1px solid var(--color-brunswick-green, #23504A);\n}\n\n.action-button.secondary:hover {\n  background-color: var(--color-honeydew, #DFF4E9);\n}\n\n/* Create Campaign Button override */\n.create-campaign-button {\n  display: inline-flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n  gap: 5px !important;\n  background-color: var(--color-brunswick-green, #23504A) !important;\n}\n\n.create-campaign-button:hover {\n  background-color: var(--color-dark-jungle-green, #0D211D) !important;\n}\n\n.create-campaign-button .button-icon,\n.create-campaign-button .button-text {\n  display: inline-flex !important;\n  align-items: center !important;\n} ", "/* Product Table Container */\n.product-table-container {\n  width: 100%;\n  margin: 1rem 0;\n}\n\n/* Product Table Title */\n.product-table-title {\n  text-align: center;\n  font-weight: bold;\n  color: var(--color-brunswick-green, #23504A);\n  font-size: 0.9rem;\n  margin-bottom: 0.5rem;\n  letter-spacing: 0.03em;\n}\n\n/* Categories Filter */\n.product-categories {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.25rem;\n  margin-bottom: 0.75rem;\n  justify-content: center;\n}\n\n.category-button {\n  padding: 0.25rem 0.75rem;\n  font-size: 0.7rem;\n  border-radius: 9999px;\n  border: none;\n  background-color: var(--color-honeydew, #DFF4E9);\n  color: var(--color-dark-jungle-green, #0D211D);\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-weight: 500;\n}\n\n.category-button:hover {\n  background-color: var(--color-emerald-green, #22AD85);\n  color: white;\n}\n\n.category-button-active {\n  background-color: var(--color-brunswick-green, #23504A);\n  color: white;\n}\n\n/* Table Scroll Container */\n.product-table-scroll {\n  overflow-x: auto;\n  overflow-y: auto;\n  max-height: 350px;\n  border-radius: 8px;\n  border: 1px solid var(--color-border, #e5e7eb);\n  margin-bottom: 0.5rem;\n  background-color: white;\n  scrollbar-width: thin;\n  -ms-overflow-style: none;\n}\n\n.product-table-scroll::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n.product-table-scroll::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.05);\n  border-radius: 3px;\n}\n\n.product-table-scroll::-webkit-scrollbar-thumb {\n  background: rgba(0, 0, 0, 0.15);\n  border-radius: 3px;\n}\n\n.product-table-scroll::-webkit-scrollbar-thumb:hover {\n  background: rgba(0, 0, 0, 0.3);\n}\n\n/* Product Table */\n.product-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 0.875rem;\n}\n\n/* Table Headers */\n.product-table thead {\n  position: sticky;\n  top: 0;\n  z-index: 1;\n}\n\n.product-table th {\n  background-color: var(--color-brunswick-green, #23504A);\n  color: white;\n  text-align: left;\n  padding: 0.75rem 1rem;\n  font-weight: 600;\n  white-space: nowrap;\n}\n\n/* Table Cells */\n.product-table td {\n  padding: 0.75rem 1rem;\n  border-bottom: 1px solid var(--color-border, #e5e7eb);\n}\n\n.product-table tr {\n  transition: background-color 0.15s ease;\n  cursor: pointer;\n}\n\n.product-table tr:hover {\n  background-color: var(--color-honeydew, #DFF4E9);\n}\n\n.product-table tr:last-child td {\n  border-bottom: none;\n}\n\n.product-table tr:nth-child(even) {\n  background-color: var(--color-honeydew, #DFF4E9);\n  opacity: 0.7;\n}\n\n/* Product Name and Description */\n.product-name {\n  font-weight: 500;\n}\n\n.product-description {\n  font-size: 0.75rem;\n  color: var(--color-text-muted, #6b7280);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 150px;\n}\n\n/* Price Column */\n.price-column {\n  text-align: right;\n  min-width: 100px;\n}\n.size-column {\n  min-width: 80px;\n}\n.name-column {\n  min-width: 150px;\n}\n\n.product-price {\n  font-weight: 500;\n  color: var(--color-emerald-green, #22AD85);\n  font-variant-numeric: tabular-nums;\n  letter-spacing: 0.01em;\n}\n\n.product-original-price {\n  font-size: 0.75rem;\n  color: var(--color-text-muted, #6b7280);\n  text-decoration: line-through;\n  font-variant-numeric: tabular-nums;\n}\n\n/* Empty State */\n.product-table-empty {\n  text-align: center;\n  padding: 1rem;\n  color: var(--color-text-muted, #6b7280);\n  font-size: 0.875rem;\n}\n\n/* Dark Theme Styles */\n[data-theme=\"dark\"] .product-table-title {\n  color: var(--color-emerald-green, #22AD85);\n}\n\n[data-theme=\"dark\"] .category-button {\n  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));\n  color: var(--color-text-muted, #9ca3af);\n}\n\n[data-theme=\"dark\"] .category-button:hover {\n  background-color: var(--color-emerald-green, #22AD85);\n  color: white;\n}\n\n[data-theme=\"dark\"] .product-table-scroll {\n  background-color: var(--color-surface-elevated, #1e2538);\n  border-color: var(--color-divider, #374151);\n}\n\n[data-theme=\"dark\"] .product-table td {\n  border-bottom-color: var(--color-divider, #374151);\n}\n\n[data-theme=\"dark\"] .product-table tr:nth-child(even) {\n  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.3));\n}\n\n[data-theme=\"dark\"] .product-table tr:hover {\n  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));\n}\n\n/* Mobile Styles */\n@media (max-width: 767px) {\n  .product-table-scroll {\n    max-height: 250px;\n  }\n  \n  .product-table th,\n  .product-table td {\n    padding: 0.5rem 0.75rem;\n    font-size: 0.8rem;\n  }\n  \n  .product-description {\n    max-width: 100px;\n  }\n  \n  .category-button {\n    padding: 0.2rem 0.5rem;\n    font-size: 0.65rem;\n  }\n} ", "/* Modern Chat Component Styles */\n/* Uses existing theme variables from variables.css */\n\n/* Core Layout */\n.chat-app {\n  display: flex;\n  height: 100%;\n  background-color: var(--color-background);\n  color: var(--color-on-background);\n  font-family: var(--font-family, 'Inter', sans-serif);\n  overflow: hidden;\n  position: relative;\n}\n\n/* Sidebar Styles */\n.chat-sidebar {\n  display: flex;\n  flex-direction: column;\n  width: 280px;\n  background-color: var(--color-surface-secondary);\n  border-right: 1px solid var(--color-divider);\n  transition: transform 0.3s ease, width 0.3s ease;\n  position: absolute;\n  height: 100%;\n  z-index: 20;\n  overflow: hidden;\n  box-shadow: 2px 0 10px var(--color-shadow-soft);\n}\n\n@media (max-width: 768px) {\n  .chat-sidebar {\n    /* No longer need position and height here since they're in the main styles */\n    /* Just add any mobile-specific adjustments */\n  }\n\n  .chat-header-mobile {\n    display: none;\n  }\n}\n\n.chat-sidebar.closed {\n  width: 0;\n  transform: translateX(-100%);\n}\n\n.chat-sidebar.open {\n  transform: translateX(0);\n  width: 280px;\n}\n\n.sidebar-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  border-bottom: 1px solid var(--color-divider);\n}\n\n.sidebar-header h2 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n  flex: 1;\n  text-align: center;\n}\n\n.sidebar-close-button {\n  background: transparent;\n  border: none;\n  color: var(--color-primary-soft);\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: color 0.2s;\n  margin-right: 8px;\n}\n\n.sidebar-close-button:hover {\n  color: var(--color-on-background);\n}\n\n.new-chat-button {\n  background: var(--color-emerald-green);\n  color: white;\n  border: none;\n  border-radius: 4px;\n  width: 32px;\n  height: 32px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.new-chat-button:hover {\n  background-color: var(--color-pine-green);\n}\n\n.chat-list {\n  flex: 1;\n  overflow-y: auto;\n  padding: 8px;\n}\n\n.chat-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px;\n  border-radius: 8px;\n  margin-bottom: 4px;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.chat-item:hover {\n  background-color: var(--color-background-soft);\n}\n\n.chat-item.active {\n  background-color: rgba(34, 173, 133, 0.1); /* emerald-green with opacity */\n  color: var(--color-emerald-green);\n}\n\n.chat-item-content {\n  flex: 1;\n  overflow: hidden;\n}\n\n.chat-name {\n  font-weight: 500;\n  margin-bottom: 4px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.chat-time {\n  font-size: 12px;\n  color: var(--color-primary-soft);\n}\n\n.chat-menu-button {\n  background: transparent;\n  border: none;\n  color: var(--color-primary-soft);\n  padding: 4px;\n  font-size: 14px;\n  cursor: pointer;\n  opacity: 0.7;\n  transition: opacity 0.2s;\n}\n\n.chat-menu-button:hover {\n  opacity: 1;\n}\n\n/* Main Chat Area */\n.chat-main {\n  flex: 1;\n  margin-left: 0;\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  position: relative;\n  background-color: var(--color-background);\n  transition: margin-left 0.3s ease;\n}\n\n/* Replace chat-header-mobile with chat-header */\n.chat-header {\n  display: flex;\n  align-items: center;\n  border-bottom: 1px solid var(--color-divider);\n  padding: 12px 16px;\n}\n\n.menu-button {\n  background: transparent;\n  border: none;\n  font-size: 16px;\n  margin-right: 12px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: var(--color-on-background);\n}\n\n.menu-button:hover {\n  color: var(--color-emerald-green);\n}\n\n.chat-title {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.chat-messages {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  display: flex;\n  flex-direction: column;\n}\n\n/* Welcome Screen */\n.welcome-screen {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  text-align: center;\n}\n\n.welcome-hero {\n  margin-bottom: 32px;\n}\n\n.assistant-avatar {\n  height: 80px;\n  margin-bottom: 16px;\n  object-fit: cover;\n}\n\n.welcome-title {\n  font-size: 28px;\n  font-weight: 700;\n  margin: 0 0 8px;\n  color: var(--color-emerald-green);\n}\n\n.welcome-subtitle {\n  font-size: 16px;\n  color: var(--color-primary-soft);\n  margin: 0;\n  max-width: 500px;\n}\n\n.thought-cloud {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 12px;\n  max-width: 100%;\n  margin-top: 24px;\n}\n\n.thought-bubble {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  padding: 12px 16px;\n  border: 1px solid #e2e8f0;\n  border-radius: 16px;\n  background: white;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);\n  cursor: pointer;\n  transition: all 0.2s ease;\n  text-align: left;\n  min-height: 60px;\n  gap: 4px;\n}\n\n.thought-bubble:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);\n  border-color: #3b82f6;\n}\n\n.thought-bubble.loading {\n  opacity: 0.7;\n  cursor: default;\n  animation: pulse 1.5s ease-in-out infinite;\n}\n\n.thought-bubble .bubble-text {\n  font-size: 14px;\n  line-height: 1.4;\n  color: #1f2937;\n  font-weight: 500;\n}\n\n.thought-bubble .bubble-agent {\n  font-size: 11px;\n  color: #6b7280;\n  font-weight: 400;\n  opacity: 0.8;\n  margin-top: 2px;\n}\n\n/* Message Styles */\n.message {\n  display: flex;\n  flex-direction: column;\n  margin-bottom: 24px;\n  max-width: 88%;\n}\n\n.user-message {\n  align-self: flex-end;\n}\n\n.assistant-message {\n  align-self: flex-start;\n}\n\n.message-container {\n  display: flex;\n  margin-bottom: 4px;\n}\n\n.user-message .message-container {\n  flex-direction: row-reverse;\n}\n\n.message-avatar {\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  margin: 0 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.avatar-placeholder {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: var(--color-emerald-green);\n  color: white;\n  font-weight: 600;\n  border-radius: 50%;\n  font-size: 14px;\n}\n\n.user-avatar .avatar-placeholder {\n  background-color: var(--color-yellow);\n}\n\n.message-bubble {\n  padding: 12px 16px;\n  border-radius: 18px;\n  max-width: calc(100% - 60px);\n  overflow-wrap: break-word;\n  overflow: hidden;\n  min-width: fit-content;\n}\n\n.user-message .message-bubble {\n  background-color: var(--color-emerald-green);\n  color: white;\n  border-top-right-radius: 4px;\n  \n}\n\n.assistant-message .message-bubble {\n  background-color: var(--color-surface-secondary);\n  color: var(--color-on-background);\n  border-top-left-radius: 4px;\n}\n.message-bubble img{\n    max-width: 50%;\n}\n\n.message-content-wrapper {\n  font-size: 15px;\n  line-height: 1.5;\n}\n\n.message-content-wrapper p {\n  margin: 0 0 12px;\n}\n\n.message-content-wrapper p:last-child {\n  margin-bottom: 0;\n}\n\n.message-meta {\n  display: flex;\n  align-items: center;\n  justify-content: flex-end;\n  margin-top: 4px;\n  font-size: 11px;\n  color: var(--color-primary-soft);\n}\n\n.user-message .message-meta {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.message-time {\n  margin-left: 4px;\n}\n\n/* Typing Indicator */\n.typing-indicator {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 8px 0;\n  gap: 4px;\n}\n\n.typing-indicator span {\n  width: 8px;\n  height: 8px;\n  background-color: var(--color-primary-soft);\n  border-radius: 50%;\n  animation: typing-dot 1.4s infinite ease-in-out both;\n}\n\n.typing-indicator span:nth-child(1) {\n  animation-delay: -0.32s;\n}\n\n.typing-indicator span:nth-child(2) {\n  animation-delay: -0.16s;\n}\n\n@keyframes typing-dot {\n  0%, 80%, 100% { transform: scale(0.4); opacity: 0.4; }\n  40% { transform: scale(1.0); opacity: 1; }\n}\n\n.typing-cursor {\n  display: inline-block;\n  width: 2px;\n  height: 16px;\n  background-color: var(--color-emerald-green);\n  margin-left: 2px;\n  animation: blink 1s infinite;\n  vertical-align: middle;\n}\n\n@keyframes blink {\n  0%, 100% { opacity: 1; }\n  50% { opacity: 0; }\n}\n\n/* Loading Spinner */\n.loading-spinner {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: 60px;\n  width: 100%;\n}\n\n.spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid rgba(0, 0, 0, 0.1);\n  border-radius: 50%;\n  border-top-color: var(--color-emerald-green);\n  animation: spin 1s ease-in-out infinite;\n}\n\n@keyframes spin {\n  to { transform: rotate(360deg); }\n}\n\n/* Message Input Area */\n.message-input-area {\n  padding: 16px;\n  border-top: 1px solid var(--color-divider);\n  background-color: var(--color-background);\n}\n\n.input-container {\n  display: flex;\n  align-items: center;\n  background-color: var(--color-surface-secondary);\n  border: 1px solid var(--color-divider);\n  border-radius: 24px;\n  padding: 0 8px 0 16px;\n  transition: border-color 0.2s;\n}\n\n.input-container:focus-within {\n  border-color: var(--color-emerald-green);\n}\n\n.message-input {\n  flex: 1;\n  border: none;\n  background: transparent;\n  padding: 12px 0;\n  font-size: 14px;\n  color: var(--color-on-background);\n  outline: none;\n  resize: none;\n  min-height: 24px;\n}\n\n.message-input::placeholder {\n  color: var(--color-primary-soft);\n}\n\n.send-button {\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: var(--color-emerald-green);\n  color: white;\n  border: none;\n  border-radius: 50%;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  margin-left: 8px;\n}\n\n.send-button:hover {\n  background-color: var(--color-pine-green);\n}\n\n.send-button:disabled {\n  background-color: var(--color-grey);\n  cursor: not-allowed;\n}\n\n/* Context Menu */\n.context-menu {\n  position: fixed;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-divider);\n  border-radius: 8px;\n  box-shadow: 0 4px 12px var(--color-shadow);\n  z-index: 100;\n  overflow: hidden;\n  width: 180px;\n  transform-origin: top left;\n  animation: popIn 0.1s forwards;\n}\n\n@keyframes popIn {\n  from { transform: scale(0.95); opacity: 0; }\n  to { transform: scale(1); opacity: 1; }\n}\n\n.context-menu-item {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  padding: 10px 12px;\n  background: none;\n  border: none;\n  text-align: left;\n  font-size: 14px;\n  color: var(--color-on-background);\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.context-menu-item:hover {\n  background-color: var(--color-background-soft);\n}\n\n.context-menu-item.delete:hover {\n  background-color: var(--color-red-soft);\n  color: var(--color-red);\n}\n\n.context-menu-item svg {\n  margin-right: 8px;\n  width: 16px;\n  height: 16px;\n}\n\n/* Message actions (feedback buttons) */\n.message-actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 8px;\n  padding: 0 12px;\n}\n\n.action-button {\n  background: transparent;\n  border: none;\n  color: var(--color-primary-soft);\n  padding: 4px;\n  margin-left: 8px;\n  cursor: pointer;\n  border-radius: 4px;\n  transition: all 0.2s;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.action-button:hover {\n  color: var(--color-on-background);\n  background-color: var(--color-background-soft);\n}\n\n.feedback-button.active {\n  color: var(--color-emerald-green);\n}\n\n.download-button:hover {\n  color: var(--color-emerald-green);\n}\n\n/* Suggestion buttons */\n.suggestion-buttons {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  margin-top: 12px;\n}\n\n.suggestion-button {\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-divider);\n  border-radius: 16px;\n  padding: 6px 12px;\n  font-size: 13px;\n  color: var(--color-on-background);\n  cursor: pointer;\n  transition: all 0.2s;\n}\n\n.suggestion-button:hover {\n  background-color: var(--color-background-soft);\n  border-color: var(--color-emerald-green);\n}\n\n/* Image handling */\n.message-images {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  margin-top: 8px;\n}\n\n.message-image {\n  position: relative;\n  border-radius: 8px;\n  overflow: hidden;\n  max-width: 100%;\n}\n\n.message-image img {\n  max-width: 100%;\n  display: block;\n  border-radius: 8px;\n}\n\n.image-prompt {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  color: white;\n  padding: 8px;\n  font-size: 12px;\n}\n\n/* Rename modal input */\n.rename-input {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid var(--color-divider);\n  border-radius: 4px;\n  margin-bottom: 12px;\n  font-size: 14px;\n  background-color: var(--color-background);\n  color: var(--color-on-background);\n}\n\n/* Mobile-specific styles */\n@media (max-width: 768px) {\n  .chat-sidebar {\n    /* No longer need position and height here since they're in the main styles */\n    /* Just add any mobile-specific adjustments */\n  }\n  .message-bubble img{\n    max-width: 100%;\n}\n\n  .thought-cloud {\n    flex-direction: column;\n    padding: 0 16px;\n  }\n\n  .message {\n    padding: 8px 0;\n  }\n\n  .welcome-title {\n    font-size: 24px;\n  }\n\n  .welcome-subtitle {\n    font-size: 14px;\n  }\n\n  .sidebar-header {\n    padding: 12px;\n  }\n  \n  .sidebar-header h2 {\n    font-size: 16px; /* Slightly smaller title on mobile */\n  }\n  \n  .sidebar-close-button {\n    margin-right: 4px; /* Less space on mobile */\n  }\n}\n\n/* Dark mode adjustments */\n[data-theme=\"dark\"] .spinner {\n  border-color: rgba(255, 255, 255, 0.1);\n  border-top-color: var(--color-emerald-green);\n}\n\n[data-theme=\"dark\"] .message-input::placeholder {\n  color: var(--color-primary-soft);\n}\n\n/* Fix for code blocks and markdown */\n.message-content-wrapper pre {\n  background-color: var(--color-black);\n  border-radius: 6px;\n  padding: 12px;\n  overflow-x: auto;\n  margin: 8px 0;\n}\n\n.message-content-wrapper code {\n  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;\n  font-size: 13px;\n  color: var(--color-white);\n}\n\n.message-content-wrapper a {\n  color: var(--color-emerald-green);\n  text-decoration: none;\n}\n\n.message-content-wrapper a:hover {\n  text-decoration: underline;\n}\n\n/* Add margin when sidebar is open on larger screens */\n@media (min-width: 769px) {\n  .chat-main.with-sidebar {\n    margin-left: 280px;\n  }\n}\n\n/* Chat history modal */\n.chat-history-modal {\n  padding: 8px 0;\n  width: 100%;\n}\n\n.chat-list-modal {\n  margin-top: 16px;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n.chat-list-modal .chat-item {\n  padding: 10px;\n  border-radius: 6px;\n  margin-bottom: 4px;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.chat-list-modal .chat-item:hover {\n  background-color: var(--color-background-soft);\n}\n\n/* When sidebar is closed */\n.chat-main.with-sidebar {\n  margin-left: 280px;\n}\n\n@media (max-width: 768px) {\n  .chat-main.with-sidebar {\n    margin-left: 0;\n  }\n}\n\n/* Chat header adjustment */\n.chat-header {\n  padding: 12px 16px;\n}\n\n/* Integration with mini-chat styles when in ModernChat */\n.chat-app .mini-chat-container {\n  width: 100%;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n}\n\n/* Product Table Responsive Styles */\n.overflow-x-auto {\n  overflow-x: auto;\n  scrollbar-width: thin;\n  -ms-overflow-style: none;\n}\n\n.overflow-x-auto::-webkit-scrollbar {\n  height: 6px;\n}\n\n.overflow-x-auto::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.05);\n  border-radius: 3px;\n}\n\n.overflow-x-auto::-webkit-scrollbar-thumb {\n  background: rgba(0, 0, 0, 0.15);\n  border-radius: 3px;\n}\n\n.overflow-x-auto::-webkit-scrollbar-thumb:hover {\n  background: rgba(0, 0, 0, 0.3);\n}\n\n/* Mobile and small screen optimizations for product table */\n@media (max-width: 639px) {\n  .overflow-x-auto table {\n    font-size: 0.75rem; /* Smaller font on mobile */\n  }\n  \n  .overflow-x-auto th,\n  .overflow-x-auto td {\n    padding: 0.5rem;\n  }\n  \n  /* Ensure the description doesn't make rows too tall */\n  .overflow-x-auto .line-clamp-1 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 1;\n    white-space: nowrap;\n    text-overflow: ellipsis;\n    max-width: 150px;\n  }\n}\n\n/* Empty chats state */\n.empty-chats-container {\n  display: flex;\n  height: 100%;\n  width: 100%;\n  align-items: center;\n  justify-content: center;\n  padding: 1rem;\n}\n\n.empty-chats-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  max-width: 200px;\n}\n\n.empty-chats-message {\n  font-weight: 600;\n  font-size: 1rem;\n  margin-bottom: 0.5rem;\n}\n\n.empty-chats-subtitle {\n  font-size: 0.875rem;\n  margin-bottom: 1.5rem;\n}\n\n/* Add new styles for the message bubble container */\n.message-bubble-container {\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n}\n\n/* Add styles for the message header */\n.message-header {\n  font-size: 14px;\n  margin-bottom: 4px;\n  color: var(--color-primary);\n  font-weight: 500;\n}\n\n.message-header.bot-header {\n  color: var(--color-emerald-green);\n}\n\n.agent-role {\n  font-size: 12px;\n  color: var(--color-primary-soft);\n  margin-left: 8px;\n  font-weight: normal;\n}\n\n/* Mini Chat Styles for Simple Layout */\n.mini-message-header {\n  font-size: 13px;\n  margin-bottom: 4px;\n  color: var(--color-primary);\n  font-weight: 500;\n}\n\n.mini-message-header.bot-header {\n  color: var(--color-emerald-green);\n}\n\n.mini-agent-role {\n  font-size: 11px;\n  color: var(--color-primary-soft);\n  margin-left: 8px;\n  font-weight: normal;\n}\n\n/* Mini chat thought bubble styles */\n.mini-thought-bubble {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  padding: 10px 12px;\n  border: 1px solid #e2e8f0;\n  border-radius: 12px;\n  background: white;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\n  cursor: pointer;\n  transition: all 0.2s ease;\n  text-align: left;\n  min-height: 50px;\n  gap: 3px;\n  font-size: 13px;\n}\n\n.mini-thought-bubble:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border-color: #3b82f6;\n}\n\n.mini-thought-bubble.loading {\n  opacity: 0.7;\n  cursor: default;\n  animation: pulse 1.5s ease-in-out infinite;\n}\n\n.mini-thought-bubble .bubble-text {\n  font-size: 13px;\n  line-height: 1.3;\n  color: #1f2937;\n  font-weight: 500;\n}\n\n.mini-thought-bubble .bubble-agent {\n  font-size: 10px;\n  color: #6b7280;\n  font-weight: 400;\n  opacity: 0.8;\n  margin-top: 1px;\n}\n\n/* Pulse animation for loading states */\n@keyframes pulse {\n  0%, 100% {\n    opacity: 0.7;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n/* Responsive adjustments for agent information */\n@media (max-width: 768px) {\n  .thought-bubble .bubble-text {\n    font-size: 13px;\n  }\n  \n  .thought-bubble .bubble-agent {\n    font-size: 10px;\n  }\n  \n  .mini-thought-bubble .bubble-text {\n    font-size: 12px;\n  }\n  \n  .mini-thought-bubble .bubble-agent {\n    font-size: 9px;\n  }\n}\n\n/* Ensure thought clouds handle the new structure */\n.mini-thought-cloud {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 10px;\n  max-width: 100%;\n  margin-top: 20px;\n}\n\n/* Loading state for thought clouds */\n.thought-cloud .loading,\n.mini-thought-cloud .loading {\n  text-align: center;\n  grid-column: 1 / -1;\n  padding: 20px;\n  color: #6b7280;\n  font-style: italic;\n}\n\n/* Agent selection information */\n.agent-selection-info {\n  font-size: 0.75rem;\n  color: #6b7280;\n  font-weight: normal;\n  margin-left: 8px;\n}\n\n.mini-agent-selection-info {\n  font-size: 0.7rem;\n  color: #6b7280;\n  font-weight: normal;\n  margin-left: 6px;\n}\n\n.mini-agent-role {\n  font-size: 0.7rem;\n  color: #9ca3af;\n  font-weight: normal;\n  margin-left: 4px;\n}\n\n/* Enhanced message header for better agent visibility */\n.message-header.bot-header {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 4px;\n  margin-bottom: 4px;\n  font-size: 0.875rem;\n  font-weight: 600;\n  color: #374151;\n}\n\n.mini-message-header.bot-header {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 3px;\n  margin-bottom: 3px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  color: #374151;\n}\n", ".insights-container {\n  width: 100%;\n  padding: 4px 0;\n  border-bottom: 1px solid #e2e8f0;\n  flex-shrink: 0;\n  transition: all 0.2s ease-out;\n  position: relative;\n  max-height: min-content;\n}\n\n.insights-container.minimized {\n  max-height: 0;\n  padding: 0;\n  border-bottom: none;\n  overflow: visible;\n}\n\n.insights-header {\n  display: flex;\n  justify-content: flex-end;\n  padding: 0 12px;\n  position: absolute;\n  right: 0;\n  top: 0;\n  z-index: 10;\n}\n\n.insights-container.minimized .insights-header {\n  position: fixed;\n  right: 20px;\n  top: 70px;\n  border-radius: 50%;\n  padding: 4px;\n  backdrop-filter: blur(4px);\n}\n\n.toggle-insights-btn {\n  background: transparent;\n  border: none;\n  width: 24px;\n  height: 24px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  color: #718096;\n  font-size: 14px;\n  padding: 0;\n  margin: 0;\n  opacity: 0.5;\n  transition: opacity 0.2s;\n}\n\n.toggle-insights-btn:hover {\n  opacity: 1;\n}\n\n.insights-scroll-area {\n  display: flex;\n  overflow-x: auto;\n  gap: 8px; /* Reduced from 12px */\n  padding: 0 12px;\n  scrollbar-width: thin;\n  -ms-overflow-style: none;\n  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;\n  height: 100%;\n}\n\n.insights-scroll-area::-webkit-scrollbar {\n  height: 4px;\n}\n\n.insights-scroll-area::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.insights-scroll-area::-webkit-scrollbar-thumb {\n  border-radius: 4px;\n}\n\n.insight-card {\n  flex: 0 0 auto;\n  min-width: 240px; /* Reduced from 280px */\n  max-width: 300px; /* Reduced from 350px */\n  border-radius: 10px;\n  padding: 8px 10px; /* Reduced from 12px */\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  border: 1px solid #e2e8f0;\n}\n\n.insight-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 6px; /* Reduced from 10px */\n}\n\n.insight-icon {\n  width: 24px; /* Reduced from 28px */\n  height: 24px; /* Reduced from 28px */\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 8px; /* Reduced from 10px */\n  font-size: 14px; /* Reduced from 16px */\n}\n\n.insight-icon.demographic {\n  background-color: #e6f7ff;\n  color: #0072ff;\n}\n\n.insight-icon.trending {\n  background-color: #fff2e6;\n  color: #ff4500;\n}\n\n.insight-icon.market {\n  background-color: #e6ffe6;\n  color: #00b300;\n}\n\n.insight-icon.performance {\n  background-color: #e6e6ff;\n  color: #4d4dff;\n}\n\n.insight-icon.other {\n  background-color: #f0f0f0;\n  color: #666666;\n}\n\n.insight-header h3 {\n  font-size: 13px; /* Reduced from 14px */\n  font-weight: 600;\n  margin: 0;\n}\n\n.insight-content {\n  font-size: 13px; /* Reduced from 14px */\n  line-height: 1.3; /* Reduced from 1.4 */\n  color: #4a5568;\n  margin: 0 0 10px 0; /* Reduced from 15px */\n  flex-grow: 1;\n}\n\n.insight-footer {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: auto;\n}\n\n.insight-indicators {\n  display: flex;\n  gap: 3px; /* Reduced from 4px */\n}\n\n.indicator {\n  width: 6px; /* Reduced from 8px */\n  height: 2px; /* Reduced from 3px */\n  background-color: #e2e8f0;\n  border-radius: 1px;\n}\n\n.indicator.active {\n  background-color: #3EDC81;\n  width: 12px; /* Reduced from 16px */\n}\n\n.insight-action {\n  padding: 0;\n  background: none;\n  border: none;\n  color: #3EDC81;\n  font-size: 12px;\n  font-weight: 500;\n  cursor: pointer;\n  text-decoration: none;\n}\n\n.insight-action:hover {\n  text-decoration: underline;\n}\n\n", ".chat-page {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  width: 100%;\n  overflow: hidden;\n}\n\n/* New wrapper to ensure chat displays properly */\n.chat-wrapper {\n  flex: 1;\n  overflow: hidden;\n  display: flex;\n  min-height: 0; /* Critical for Firefox */\n  max-height: 100%;\n}\n\n/* Ensure the chat takes up all remaining space */\n.chat-page .chat-app {\n  flex: 1;\n  width: 100%;\n  overflow: hidden;\n  display: flex; /* Ensure chat app contents display properly */\n}\n\n/* Refined compact insights container */\n.insights-container {\n  flex-shrink: 0; /* Prevent insights from shrinking */\n  margin-bottom: 4px; /* Add a small margin for visual separation */\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\n}\n\n/* Make card colors more subtle in light mode */\n@media (prefers-color-scheme: light) {\n  .insight-card {\n    background-color: #fdfdfd;\n  }\n}\n", "/* Main Insight Card Styles */\n.insight-card {\n  background: var(--color-surface-elevated);\n  border: 1px solid var(--color-border);\n  border-radius: 8px;\n  padding: 0;\n  margin-bottom: 16px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  transition: box-shadow 0.2s;\n}\n\n.insight-card:hover {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n/* Agent Header */\n.insight-agent-header {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  border-bottom: 1px solid var(--color-border);\n  background: var(--color-background-subtle);\n}\n\n.insight-agent-icon {\n  font-size: 24px;\n  margin-right: 12px;\n}\n\n.insight-agent-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.insight-agent-name {\n  font-weight: 600;\n  font-size: 14px;\n  color: var(--color-text);\n}\n\n.insight-agent-role {\n  font-size: 12px;\n  color: var(--color-text-subtle);\n}\n\n/* Impact Badge */\n.insight-impact-badge {\n  padding: 4px 12px;\n  border-radius: 16px;\n  font-size: 12px;\n  font-weight: 600;\n  text-transform: uppercase;\n}\n\n.insight-impact-badge.high {\n  background: var(--color-red-soft);\n  color: var(--color-red);\n}\n\n.insight-impact-badge.medium {\n  background: var(--color-yellow-soft);\n  color: var(--color-yellow-dark);\n}\n\n.insight-impact-badge.low {\n  background: var(--color-green-soft);\n  color: var(--color-green);\n}\n\n/* Content Area */\n.insight-content {\n  padding: 16px;\n}\n\n.insight-title {\n  font-size: 16px;\n  font-weight: 600;\n  margin: 0 0 8px 0;\n  color: var(--color-text);\n}\n\n.insight-description {\n  font-size: 14px;\n  line-height: 1.5;\n  color: var(--color-text-secondary);\n  margin: 0 0 16px 0;\n}\n\n/* Actions */\n.insight-actions {\n  margin-top: 16px;\n}\n\n.insight-actions-label {\n  font-size: 12px;\n  font-weight: 600;\n  color: var(--color-text-subtle);\n  margin: 0 0 8px 0;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.insight-action-buttons {\n  display: flex;\n  gap: 8px;\n}\n\n/* Compact Insight Card Styles */\n.compact-insight-card {\n  padding: 12px 14px;\n  border-radius: 6px;\n  margin-bottom: 8px;\n  font-size: 13px;\n  position: relative;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.compact-insight-card:hover {\n  filter: brightness(0.95);\n}\n\n.compact-insight-content {\n  flex: 1;\n  padding-right: 24px; /* Space for the dismiss button */\n  overflow: hidden; /* Ensure content doesn't overflow */\n}\n\n.compact-insight-title {\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  line-height: 1.3;\n  display: block;\n  width: 100%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.compact-insight-description {\n  margin: 4px 0 0 0;\n  font-size: 12px;\n  line-height: 1.4;\n  color: rgba(0, 0, 0, 0.7);\n  display: block;\n  width: 100%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.compact-insight-dismiss {\n  position: absolute;\n  top: 8px;\n  right: 10px;\n  background: none;\n  border: none;\n  font-size: 14px;\n  line-height: 1;\n  padding: 2px;\n  color: rgba(0, 0, 0, 0.5);\n  cursor: pointer;\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.compact-insight-dismiss:hover {\n  background-color: rgba(0, 0, 0, 0.1);\n  color: rgba(0, 0, 0, 0.8);\n}\n\n/* Variants based on impact level */\n.compact-insight-card.success {\n  background-color: var(--color-green-soft, rgba(209, 250, 229, 0.4));\n}\n\n.compact-insight-card.error {\n  background-color: var(--color-red-soft, rgba(254, 226, 226, 0.4));\n}\n\n.compact-insight-card.warn {\n  background-color: var(--color-yellow-soft, rgba(254, 249, 195, 0.4));\n}\n\n/* Dark theme support */\n[data-theme=\"dark\"] .insight-card {\n  background: var(--color-surface-elevated);\n  border-color: var(--color-border);\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n}\n\n[data-theme=\"dark\"] .insight-card:hover {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);\n}\n\n[data-theme=\"dark\"] .insight-agent-header {\n  background: rgba(255, 255, 255, 0.05);\n  border-bottom-color: var(--color-border);\n}\n\n[data-theme=\"dark\"] .insight-impact-badge.high {\n  background: rgba(153, 27, 27, 0.3);\n  color: #ef4444;\n}\n\n[data-theme=\"dark\"] .insight-impact-badge.medium {\n  background: rgba(146, 123, 2, 0.3);\n  color: #f59e0b;\n}\n\n[data-theme=\"dark\"] .insight-impact-badge.low {\n  background: rgba(6, 95, 70, 0.3);\n  color: #10b981;\n}\n\n[data-theme=\"dark\"] .compact-insight-description {\n  color: rgba(255, 255, 255, 0.7);\n}\n\n[data-theme=\"dark\"] .compact-insight-dismiss {\n  color: rgba(255, 255, 255, 0.5);\n}\n\n[data-theme=\"dark\"] .compact-insight-dismiss:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.8);\n}\n\n[data-theme=\"dark\"] .compact-insight-card.success {\n  background-color: rgba(6, 95, 70, 0.3);\n}\n\n[data-theme=\"dark\"] .compact-insight-card.error {\n  background-color: rgba(153, 27, 27, 0.3);\n}\n\n[data-theme=\"dark\"] .compact-insight-card.warn {\n  background-color: rgba(146, 123, 2, 0.3);\n} ", "/* Empty Insights State */\n.empty-insights {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 400px;\n}\n\n.empty-insights-content {\n  text-align: center;\n  max-width: 800px;\n  padding: 0 20px;\n}\n\n.empty-insights-icon {\n  margin-bottom: 20px;\n}\n\n.empty-insights-icon img {\n  width: 120px;\n}\n\n.empty-circle {\n  stroke: var(--color-primary-soft);\n  stroke-width: 2;\n  fill: var(--color-success-translucent);\n}\n\n.empty-insights-title {\n  font-size: 28px;\n  margin-bottom: 12px;\n  color: var(--color-text);\n}\n\n.empty-insights-description {\n  margin-bottom: 32px;\n  color: var(--color-text-secondary);\n  font-size: 16px;\n  line-height: 1.6;\n}\n\n/* System Explanation */\n.system-explanation {\n  background: var(--color-background-subtle);\n  border-radius: 12px;\n  padding: 24px;\n  margin-bottom: 32px;\n  border: 1px solid var(--color-border);\n}\n\n.system-explanation-title {\n  font-size: 18px;\n  font-weight: 600;\n  margin-bottom: 8px;\n  color: var(--color-text);\n}\n\n.system-explanation-description {\n  color: var(--color-text-secondary);\n  margin-bottom: 20px;\n  line-height: 1.6;\n}\n\n/* Agents Grid */\n.agents-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 12px;\n}\n\n.agent-card {\n  display: flex;\n  align-items: center;\n  background: var(--color-surface-elevated);\n  border: 1px solid var(--color-border);\n  border-radius: 8px;\n  padding: 12px;\n  transition: all 0.2s;\n}\n\n.agent-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.agent-icon {\n  font-size: 28px;\n  margin-right: 12px;\n}\n\n.agent-info {\n  flex: 1;\n}\n\n.agent-name {\n  font-weight: 600;\n  font-size: 14px;\n  margin: 0;\n  color: var(--color-text);\n}\n\n.agent-role {\n  font-size: 12px;\n  color: var(--color-text-subtle);\n  margin: 0;\n}\n\n/* Model Selector */\n.model-selector {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  margin-bottom: 24px;\n  width: 100%;\n  max-width: 400px;\n}\n\n.model-selector label {\n  font-weight: 600;\n  color: var(--color-text);\n  white-space: nowrap;\n}\n\n/* Generate Button */\n.generate-insights-button {\n  background: linear-gradient(90deg, var(--color-primary), var(--color-success));\n  border: none;\n  border-radius: 8px;\n  padding: 12px 24px;\n  font-weight: 600;\n  color: var(--color-surface-elevated);\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n\n.generate-insights-button:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 6px 12px var(--color-primary-shadow);\n}\n\n/* Loading State */\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n  gap: 16px;\n}\n\n.loading-text {\n  margin-top: 20px;\n  font-size: 16px;\n  color: var(--color-text-subtle);\n}\n\n.loading-agents {\n  display: flex;\n  gap: 12px;\n  margin-top: 16px;\n}\n\n.loading-agent {\n  font-size: 32px;\n  animation: bounce 1.4s ease-in-out infinite;\n}\n\n.loading-agent:nth-child(1) { animation-delay: 0s; }\n.loading-agent:nth-child(2) { animation-delay: 0.2s; }\n.loading-agent:nth-child(3) { animation-delay: 0.4s; }\n.loading-agent:nth-child(4) { animation-delay: 0.6s; }\n\n@keyframes bounce {\n  0%, 80%, 100% { transform: scale(1) translateY(0); }\n  40% { transform: scale(1.2) translateY(-8px); }\n}\n\n/* Insights Header */\n.insights-header {\n  margin-bottom: 24px;\n}\n\n.insights-header-text {\n  font-size: 16px;\n  color: var(--color-text-secondary);\n}\n\n/* Insights List */\n.insights-list {\n  position: relative;\n}\n\n/* Blur overlay styles */\n.insights-premium-container {\n  position: relative;\n  margin-top: 32px;\n}\n\n.insights-blur {\n  filter: blur(8px);\n  pointer-events: none;\n}\n\n.blurred-insight {\n  opacity: 0.7;\n  margin-bottom: 16px;\n}\n\n.upgrade-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.7);\n  border-radius: 8px;\n  z-index: 10;\n}\n\n.upgrade-content {\n  text-align: center;\n  background: white;\n  padding: 24px;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n  max-width: 90%;\n  width: 400px;\n}\n\n.upgrade-content h3 {\n  font-size: 20px;\n  margin-bottom: 12px;\n  color: var(--text-primary);\n}\n\n.upgrade-content p {\n  margin-bottom: 20px;\n  color: var(--text-secondary);\n}\n\n.upgrade-button {\n  width: 100%;\n}\n\n/* Dark theme support */\n[data-theme=\"dark\"] .system-explanation {\n  background: rgba(255, 255, 255, 0.05);\n  border-color: var(--color-border);\n}\n\n[data-theme=\"dark\"] .agent-card {\n  background: var(--color-surface-elevated);\n}\n\n[data-theme=\"dark\"] .agent-card:hover {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n}\n\n[data-theme=\"dark\"] .upgrade-overlay {\n  background: rgba(0, 0, 0, 0.7);\n}\n\n[data-theme=\"dark\"] .upgrade-content {\n  background: var(--color-surface-elevated);\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .empty-insights {\n    padding: 40px 16px;\n  }\n  \n  .empty-insights-icon img {\n    width: 80px;\n  }\n  \n  .empty-insights-title {\n    font-size: 24px;\n  }\n  \n  .empty-insights-description {\n    font-size: 14px;\n  }\n  \n  .model-selector {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 8px;\n  }\n  \n  .agents-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .system-explanation {\n    padding: 16px;\n  }\n} ", ".checkbox-wrapper {\n  display: inline-flex;\n  align-items: center;\n  margin-bottom: 8px;\n}\n\n.checkbox-label {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  user-select: none;\n  font-size: 14px;\n  color: var(--color-text-primary);\n}\n\n.checkbox-label.disabled {\n  cursor: not-allowed;\n  opacity: 0.6;\n}\n\n.checkbox-input {\n  margin-right: 8px;\n  cursor: pointer;\n  appearance: none;\n  width: 18px;\n  height: 18px;\n  border: 1px solid var(--color-border);\n  border-radius: 3px;\n  background-color: var(--color-background);\n  position: relative;\n  outline: none;\n  transition: border-color 0.2s, background-color 0.2s;\n}\n\n.checkbox-input:checked {\n  background-color: var(--color-checkbox-checked-bg);\n  border-color: var(--color-checkbox-checked-bg);\n}\n\n.checkbox-input:checked::after {\n  content: '';\n  position: absolute;\n  display: block;\n  left: 13px;\n  top: 4px;\n  width: 6px;\n  height: 11px;\n  border: solid white;\n  border-width: 0 1px 1px 0;\n  transform: rotate(45deg);\n}\n\n.checkbox-input:focus {\n  border-color: var(--color-primary);\n  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);\n}\n\n.checkbox-text {\n  font-weight: normal;\n}\n\n.input-error {\n  color: var(--color-destructive);\n  font-size: 12px;\n  margin-top: 4px;\n} ", ".action-group {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.action-divider {\n  width: 1px;\n  height: 24px;\n  background-color: var(--color-divider);\n  margin: 0 0.25rem;\n}\n\n.secondary-actions {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.dropdown-container {\n  position: relative;\n}\n\n.dropdown-button {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n.icon-wrapper {\n  display: inline-flex;\n  transition: transform 0.2s ease;\n}\n\n.icon-wrapper.rotate-icon {\n  transform: rotate(180deg);\n}\n\n.dropdown-button svg {\n  transition: transform 0.2s ease;\n}\n\n.dropdown-button svg.rotate-180 {\n  transform: rotate(180deg);\n}\n\n.dropdown-menu {\n  position: absolute;\n  margin-left: -20px;\n  top: 100%;\n  margin-top: 0.25rem;\n  z-index: 10;\n  min-width: 180px;\n  background-color: var(--color-surface);\n  border: 1px solid var(--color-divider);\n  border-radius: var(--border-radius);\n  box-shadow: 0 4px 6px var(--color-shadow-soft);\n  overflow: hidden;\n}\n\n.dropdown-item {\n  display: block;\n  width: 100%;\n  text-align: left;\n  padding: 0.75rem 1rem;\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: var(--color-primary);\n  font-size: 0.875rem;\n  transition: background-color 0.2s;\n}\n\n.dropdown-item:not(:last-child) {\n  border-bottom: 1px solid var(--color-divider);\n}\n\n.dropdown-item:hover {\n  background-color: var(--color-background-soft);\n}\n\n.modal-content {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.info-message {\n  color: var(--color-primary-soft);\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n.info-message strong {\n  font-weight: 600;\n}\n\n.warning-message {\n  background-color: rgba(var(--color-yellow), 0.1);\n  border: 1px solid var(--color-yellow-soft);\n  color: var(--color-yellow-hard);\n  padding: 0.75rem 1rem;\n  border-radius: var(--border-radius-inner);\n  font-size: 0.875rem;\n}\n\n.sync-progress {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 0.5rem;\n  padding: 1rem 0;\n}\n\n.modal-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 0.5rem;\n  margin-top: 0.5rem;\n}\n\n/* Add responsive styles for action buttons */\n@media (max-width: 767px) {\n  /* Make the dropdown menu positioned better on mobile */\n  .absolute.right-0.mt-2.w-56 {\n    right: 0;\n    left: auto;\n    width: 240px;\n    max-width: calc(100vw - 32px);\n  }\n  \n  /* Make content in dropdown more readable on mobile */\n  .px-3.py-2 {\n    padding-top: 10px;\n    padding-bottom: 10px;\n  }\n  \n  /* Ensure the loading status indicator has proper spacing */\n  .flex.items-center.gap-2.text-blue-600.bg-blue-50 {\n    margin-top: 8px;\n    width: 100%;\n    justify-content: center;\n  }\n}\n\n/* Make tooltip content readable on mobile */\n@media (max-width: 500px) {\n  .absolute.bottom-full.left-1\\/2.transform.-translate-x-1\\/2.-translate-y-2 {\n    max-width: 200px;\n    white-space: normal;\n  }\n} ", ".product-detail-container {\n  display: grid;\n  grid-template-columns: 1fr 2fr;\n  gap: 24px;\n}\n\n.product-image-container {\n  display: flex;\n  flex-direction: column;\n}\n\n.product-image-wrapper {\n  width: 100%;\n  height: 300px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  overflow: hidden;\n  padding: 16px;\n}\n\n.product-image {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n}\n\n.product-info-container {\n  display: flex;\n  flex-direction: column;\n  gap: 24px;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 8px;\n}\n\n.card-header {\n  padding: 16px;\n  border-bottom: 1px solid #e5e7eb;\n  background-color: #f9fafb;\n}\n\n.card-header h3 {\n  margin: 0;\n  color: #374151;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.card-content {\n  padding: 16px;\n}\n\n.product-url {\n  color: #3b82f6;\n  text-decoration: none;\n  word-break: break-all;\n}\n\n.product-url:hover {\n  text-decoration: underline;\n}\n\n/* Edit form styles */\n.edit-form {\n  width: 100%;\n}\n\n.form-section {\n  margin-bottom: 24px;\n  border-radius: 8px;\n}\n\n.form-section h3 {\n  font-size: 1.1rem;\n  font-weight: 600;\n  margin-bottom: 16px;\n  color: #333;\n}\n\n.form-section-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.form-row {\n  display: flex;\n  gap: 16px;\n  width: 100%;\n}\n\n.form-row > * {\n  flex: 1;\n}\n\n.expandable-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  cursor: pointer;\n  padding-bottom: 8px;\n}\n\n.expandable-icon {\n  color: #6b7280;\n}\n\n/* Image upload styles */\n.image-upload-container {\n  border: 1px dashed #d1d5db;\n  border-radius: 8px;\n  padding: 16px;\n  margin-bottom: 16px;\n}\n\n.image-upload-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 16px;\n}\n\n.image-upload-buttons {\n  display: flex;\n  gap: 8px;\n}\n\n.hidden-file-input {\n  display: none;\n}\n\n.image-preview-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-bottom: 16px;\n  height: 200px;\n  border-radius: 4px;\n}\n\n.image-preview {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n}\n\n.image-placeholder {\n  color: #9ca3af;\n}\n\n/* Ensure the modal is wide enough */\n:global(.modal-large) {\n  width: 800px;\n  max-width: 90vw;\n}\n\n/* Modal action buttons */\n.modal-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  margin-top: 24px;\n  padding-top: 16px;\n  position: sticky;\n  bottom: 0;\n}\n\n.checkbox-container {\n  display: flex;\n  gap: 20px;\n  margin-top: 8px;\n}\n\n.delete-confirmation {\n  padding: 16px;\n  text-align: center;\n}\n\n.delete-confirmation p {\n  margin: 8px 0;\n}\n\n.delete-confirmation strong {\n  color: #ef4444;\n}\n\n/* Action footer for page layout */\n.page-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  margin-top: 24px;\n  padding: 16px;\n  background-color: #f9fafb;\n  border: 1px solid #e5e7eb;\n  border-radius: 8px;\n}\n\n/* Make sure modal large size is appropriate */\n:global(.modal.large .modal-inner) {\n  width: 90%;\n  max-width: 900px !important;\n}\n\n/* Responsive styles */\n@media (max-width: 768px) {\n  .product-detail-container {\n    grid-template-columns: 1fr;\n  }\n  \n  .form-row {\n    flex-direction: column;\n    gap: 8px;\n  }\n} \n\n/* Dark mode adjustments */\n@media (prefers-color-scheme: dark) {\n\n  .page-actions {\n    background-color: #1f2937;\n    border-color: #374151;\n  }\n} \n\n/* Add styles for AI badge */\n.ai-badge {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.25rem 0.5rem;\n  margin-left: 0.5rem;\n  background-color: #818cf8; /* Indigo-400 */\n  color: white;\n  font-size: 0.75rem;\n  font-weight: 500;\n  border-radius: 0.375rem;\n}\n\n/* Enhancement status badge */\n.ai-enhancement-status-badge {\n  display: inline-flex;\n  align-items: center;\n  padding: 0.25rem 0.75rem;\n  border-radius: 1rem;\n  color: white;\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n/* Spinner for loading state */\n.spinner {\n  border: 3px solid rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  border-top-color: #6366f1; /* Indigo-500 */\n  width: 1rem;\n  height: 1rem;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Product detail grid layout */\n.product-detail-grid {\n  display: grid;\n  grid-template-columns: 1fr 2fr;\n  gap: 1rem;\n}\n\n@media (max-width: 768px) {\n  .product-detail-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n/* Product image styling */\n.product-image-container {\n  background-color: white;\n  border-radius: 0.5rem;\n  padding: 1rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.product-image {\n  max-width: 100%;\n  max-height: 20rem;\n  object-fit: contain;\n}\n\n/* Action group styling */\n.action-group {\n  display: flex;\n  gap: 0.5rem;\n} ", ".add-product-form {\n  width: 100%;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.card-content {\n  padding: 1.5rem;\n}\n\n.form-fields {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.checkbox-container {\n  display: flex;\n  gap: 20px;\n  margin-top: 8px;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  margin-top: 24px;\n}\n\n.error-message {\n  color: #e53e3e;\n  margin-top: 16px;\n  padding: 12px;\n  background-color: #fff5f5;\n  border-radius: 4px;\n  border-left: 4px solid #e53e3e;\n}\n\n.success-message {\n  color: #38a169;\n  margin-top: 16px;\n  padding: 12px;\n  background-color: #f0fff4;\n  border-radius: 4px;\n  border-left: 4px solid #38a169;\n}\n\n/* Image upload styles */\n.image-upload-container {\n  margin-bottom: 20px;\n  width: 100%;\n  border: 1px solid var(--color-border);\n  border-radius: var(--border-radius-inner);\n  padding: 1rem;\n  background-color: var(--color-background-secondary);\n}\n\n.image-upload-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.image-upload-label {\n  font-weight: 600;\n  color: #374151;\n}\n\n.image-upload-buttons {\n  display: flex;\n  gap: 8px;\n}\n\n.hidden-file-input {\n  display: none;\n}\n\n.upload-button {\n  display: inline-block;\n  padding: 0.5rem 1rem;\n  background-color: var(--color-primary);\n  color: white;\n  font-size: 0.875rem;\n  border-radius: var(--border-radius-inner);\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.upload-button:hover {\n  background-color: var(--color-primary-dark);\n}\n\n.clear-image-button {\n  padding: 0.5rem 1rem;\n  background-color: var(--color-border);\n  color: var(--color-text-primary);\n  font-size: 0.875rem;\n  border: none;\n  border-radius: var(--border-radius-inner);\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.clear-image-button:hover {\n  background-color: var(--color-destructive-light);\n  color: var(--color-destructive);\n}\n\n.image-preview-container {\n  width: 100%;\n  height: 200px;\n  border: 2px dashed #d1d5db;\n  border-radius: 8px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  overflow: hidden;\n  margin-bottom: 12px;\n}\n\n.image-preview {\n  max-width: 100%;\n  max-height: 100%;\n  object-fit: contain;\n}\n\n.image-placeholder {\n  color: #9ca3af;\n  font-size: 14px;\n}\n\n.image-url-input {\n  margin-top: 1rem;\n}\n\n/* New styles for the updated layout */\n.form-section {\n  margin-bottom: 24px;\n  border: 1px solid #e5e7eb;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.form-section h3 {\n  margin: 0;\n  padding: 12px 16px;\n  background-color: #f9fafb;\n  font-size: 16px;\n  font-weight: 600;\n  color: #374151;\n  border-bottom: 1px solid #e5e7eb;\n}\n\n.form-section-content {\n  padding: 16px;\n}\n\n.form-row {\n  display: flex;\n  gap: 16px;\n  margin-bottom: 16px;\n}\n\n.form-row > * {\n  flex: 1;\n}\n\n.expandable-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  cursor: pointer;\n}\n\n.expandable-icon {\n  display: flex;\n  align-items: center;\n  margin-right: 16px;\n  color: #6b7280;\n}\n\n/* Make form layout responsive */\n@media (max-width: 768px) {\n  .form-row {\n    flex-direction: column;\n    gap: 8px;\n  }\n}\n\n/* Dark mode adjustments */\n@media (prefers-color-scheme: dark) {\n  .error-message {\n    background-color: rgba(var(--color-destructive-rgb), 0.2);\n  }\n  \n  .success-message {\n    background-color: rgba(var(--color-success-rgb), 0.2);\n  }\n  \n  .image-upload-container {\n    background-color: rgba(255, 255, 255, 0.05);\n  }\n  \n  .clear-image-button {\n    background-color: rgba(255, 255, 255, 0.1);\n  }\n  \n  .clear-image-button:hover {\n    background-color: rgba(var(--color-destructive-rgb), 0.2);\n  }\n  \n  .image-preview-container {\n    background-color: rgba(0, 0, 0, 0.2);\n    border-color: var(--color-border);\n  }\n} ", "/* FileManager.css */\n.file-list {\n  margin-top: 20px;\n  width: 100%;\n  overflow-x: auto;\n}\n\n.file-table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 0.9rem;\n}\n\n.file-table th {\n  text-align: left;\n  padding: 12px 15px;\n  background-color: var(--color-background-secondary);\n  border-bottom: 1px solid var(--color-border);\n  font-weight: 600;\n}\n\n.file-table td {\n  padding: 12px 15px;\n  border-bottom: 1px solid var(--color-border);\n}\n\n.file-name-cell {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.file-type-icon {\n  width: 20px;\n  height: 20px;\n  margin-right: 6px;\n}\n\n.actions-cell {\n  display: flex;\n  gap: 8px;\n}\n\n.status-badge {\n  display: inline-block;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-size: 0.8rem;\n  font-weight: 500;\n  text-transform: capitalize;\n}\n\n/* Status badge colors */\n.status-pending {\n  background-color: #fef9c3;\n  color: #854d0e;\n}\n\n.status-processing {\n  background-color: #dbeafe;\n  color: #1e40af;\n}\n\n.status-completed {\n  background-color: #dcfce7;\n  color: #166534;\n}\n\n.status-failed {\n  background-color: #fee2e2;\n  color: #b91c1c;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 40px;\n  color: var(--color-text-secondary);\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 60px 20px;\n  text-align: center;\n}\n\n.empty-state-content {\n  max-width: 500px;\n}\n\n.empty-state-icon {\n  margin-bottom: 24px;\n  display: flex;\n  justify-content: center;\n}\n\n.empty-state-icon svg {\n  width: 60px;\n  height: 60px;\n  color: var(--color-text-tertiary);\n}\n\n.empty-state-title {\n  font-size: 1.5rem;\n  margin-bottom: 12px;\n  color: var(--color-text-primary);\n}\n\n.empty-state-description {\n  color: var(--color-text-secondary);\n  margin-bottom: 24px;\n}\n\n.upload-modal-content {\n  padding: 20px 0;\n}\n\n.upload-instructions {\n  margin-bottom: 20px;\n  color: var(--color-text-secondary);\n}\n\n.upload-dropzone {\n  margin-bottom: 20px;\n}\n\n.file-input {\n  display: none;\n}\n\n.dropzone {\n  border: 2px dashed var(--color-border);\n  border-radius: 8px;\n  padding: 40px 20px;\n  text-align: center;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n}\n\n.dropzone:hover {\n  border-color: var(--color-primary);\n  background-color: var(--color-background-hover);\n}\n\n.dropzone svg {\n  width: 40px;\n  height: 40px;\n  margin-bottom: 16px;\n  color: var(--color-text-tertiary);\n  display: block;\n}\n\n.dropzone p {\n  margin-bottom: 8px;\n  color: var(--color-text-primary);\n}\n\n.file-types {\n  display: block;\n  font-size: 0.85rem;\n  color: var(--color-text-tertiary);\n}\n\n.selected-file {\n  margin: 16px 0;\n  padding: 12px 16px;\n  background-color: var(--color-background-secondary);\n  border-radius: 6px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.selected-file span {\n  font-weight: 500;\n}\n\n.modal-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n  margin-top: 20px;\n}\n\n.delete-modal-content {\n  padding: 10px 0;\n}\n\n.delete-file-info {\n  margin: 16px 0;\n  padding: 12px 16px;\n  background-color: var(--color-background-secondary);\n  border-radius: 6px;\n}\n\n/* Add a spinner for processing status */\n.status-spinner {\n  display: inline-block;\n  width: 10px;\n  height: 10px;\n  margin-right: 6px;\n  border: 2px solid rgba(30, 64, 175, 0.3);\n  border-radius: 50%;\n  border-top-color: #1e40af;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Add styling for the polling indicator */\n.polling-indicator {\n  font-size: 0.8rem;\n  color: var(--color-text-tertiary);\n  margin-bottom: 10px;\n  text-align: right;\n  font-style: italic;\n}\n\n/* Add styling for the error details button */\n.error-details-button {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  width: 16px;\n  height: 16px;\n  border-radius: 50%;\n  background: none;\n  border: none;\n  color: inherit;\n  font-size: 12px;\n  margin-left: 6px;\n  cursor: pointer;\n  opacity: 0.8;\n  transition: opacity 0.2s;\n}\n\n.error-details-button:hover {\n  opacity: 1;\n}\n\n/* Add styles for the analysis modal */\n.analysis-modal-content {\n  padding: 20px;\n}\n\n.analysis-modal-content h3 {\n  margin-top: 0;\n  margin-bottom: 15px;\n  font-size: 18px;\n}\n\n.analysis-content {\n  margin-top: 20px;\n}\n\n.analysis-content h4 {\n  margin-top: 0;\n  margin-bottom: 10px;\n  font-size: 16px;\n}\n\n.analysis-json {\n  border: 1px solid #e1e4e8;\n  border-radius: 6px;\n  padding: 15px;\n  overflow: auto;\n  max-height: 400px;\n  font-family: monospace;\n  white-space: pre-wrap;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n/* Enhanced analysis modal styles */\n.analysis-section {\n  margin-bottom: 24px;\n}\n\n.analysis-section h5 {\n  font-size: 16px;\n  margin-bottom: 10px;\n  color: var(--color-text-primary);\n  font-weight: 600;\n}\n\n.analysis-text {\n  white-space: pre-wrap;\n  line-height: 1.5;\n  margin-bottom: 10px;\n  padding: 12px;\n  border-radius: 6px;\n  border: 1px solid #e1e4e8;\n}\n\n.agent-contribution {\n  padding: 12px;\n  border: 1px solid #e1e4e8;\n  border-radius: 6px;\n  margin-bottom: 12px;\n}\n\n.agent-header {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 8px;\n  padding-bottom: 8px;\n  border-bottom: 1px solid #e1e4e8;\n  font-size: 14px;\n}\n\n.relevance-score {\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 12px;\n}\n\n.key-insights {\n  margin-top: 8px;\n}\n\n.insight {\n  white-space: pre-wrap;\n  line-height: 1.5;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.completion-percentage {\n  font-size: 18px;\n  font-weight: 600;\n  color: var(--color-primary);\n} ", "/* Mini Chat Page Styles */\n.mini-chat-container,\n.mini-insights-container {\n  /* display: flex; */\n  /* flex-direction: column; */\n  height: 100vh;\n  background-color: white;\n  border-radius: 0;\n  overflow: hidden;\n  box-shadow: none;\n  position: relative; /* Added for resize handle positioning */\n  /* flex: 1; */\n  /* max-height: 100%; */\n}\n\n[data-theme=\"dark\"] .mini-chat-container,\n[data-theme=\"dark\"] .mini-insights-container {\n  background-color: var(--color-surface-elevated, #1e2538);\n  box-shadow: none;\n}\n\n/* Drag Resize Handle */\n.mini-chat-resize-handle {\n  position: absolute;\n  left: -6px;\n  top: 0;\n  width: 12px;\n  height: 100%;\n  cursor: ew-resize;\n  z-index: 10;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.mini-chat-resize-handle::after {\n  content: \"\";\n  display: block;\n  width: 4px;\n  height: 50px;\n  border-radius: 2px;\n  background-color: var(--color-brunswick-green, #23504A);\n  opacity: 0;\n  transition: opacity 0.2s ease;\n}\n\n/* Drag button that appears on hover */\n.mini-chat-resize-handle::before {\n  content: \"⋮\";\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%) rotate(90deg);\n  width: 24px;\n  height: 24px;\n  background-color: var(--color-brunswick-green, #23504A);\n  color: white;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 14px;\n  opacity: 0;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n  transition: opacity 0.2s ease, transform 0.2s ease;\n}\n\n.mini-chat-resize-handle:hover::before {\n  opacity: 0.8;\n  transform: translateY(-50%) rotate(90deg) scale(1.1);\n}\n\n.mini-chat-resize-handle:active::before {\n  opacity: 1;\n  transform: translateY(-50%) rotate(90deg) scale(1);\n}\n\n.mini-chat-resize-handle:hover::after {\n  opacity: 0.5;\n}\n\n.mini-chat-resize-handle:active::after {\n  opacity: 0.7;\n}\n\n/* Header Styles */\n.mini-chat-header {\n  display: flex;\n  flex-direction: column;\n  padding: 12px 16px;\n  border-bottom: 1px solid var(--color-border, #e5e7eb);\n  background-color: var(--color-surface);\n  flex-shrink: 0;\n  gap: 8px;\n  z-index: 2;\n}\n\n[data-theme=\"dark\"] .mini-chat-header {\n  background-color: var(--color-surface-elevated, #1e2538);\n  border-bottom-color: var(--color-divider, #374151);\n}\n\n.mini-chat-title-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.mini-chat-agents-row {\n  display: flex;\n  align-items: center;\n  padding-top: 4px;\n}\n\n.mini-chat-dropdown {\n  position: relative;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n  width: 100%;\n}\n\n.mini-chat-selected {\n  flex: 1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n[data-theme=\"dark\"] .mini-chat-dropdown {\n  color: var(--color-primary, #e5e7eb);\n}\n\n.mini-chat-dropdown-icon {\n  width: 16px;\n  height: 16px;\n  color: var(--color-text-muted, #6b7280);\n}\n\n.mini-chat-dropdown-content {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  z-index: 1000;\n  background-color: white;\n  border-radius: 6px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  margin-top: 8px;\n  max-height: 300px;\n  overflow-y: auto;\n}\n\n[data-theme=\"dark\"] .mini-chat-dropdown-content {\n  background-color: var(--color-surface-elevated, #1e2538);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);\n}\n\n.mini-chat-dropdown-item {\n  padding: 8px 16px;\n  cursor: pointer;\n  transition: background-color 0.15s ease;\n}\n\n.mini-chat-dropdown-item:hover {\n  background-color: var(--color-bg-hover, #f3f4f6);\n}\n\n[data-theme=\"dark\"] .mini-chat-dropdown-item:hover {\n  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));\n}\n\n.mini-chat-dropdown-divider {\n  height: 1px;\n  background-color: var(--color-border, #e5e7eb);\n  margin: 4px 0;\n}\n\n[data-theme=\"dark\"] .mini-chat-dropdown-divider {\n  background-color: var(--color-divider, #374151);\n}\n\n/* Agents Styles */\n.mini-chat-agents {\n  display: flex;\n  align-items: center;\n}\n\n.mini-chat-agent-avatar {\n  width: 28px;\n  height: 28px;\n  border-radius: 50%;\n  background-color: var(--color-brunswick-green, #23504A);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  font-weight: 500;\n  font-size: 12px;\n  position: relative;\n  border: 2px solid white;\n  box-sizing: content-box;\n}\n\n[data-theme=\"dark\"] .mini-chat-agent-avatar {\n  border-color: var(--color-surface-elevated, #1e2538);\n}\n\n.mini-chat-agent-avatar img {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  object-fit: cover;\n}\n\n.mini-avatar-placeholder {\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.mini-chat-agent-more {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: #1a2540;\n  color: white;\n  font-weight: 600;\n  font-size: 13px;\n  width: 28px;\n  height: 28px;\n  border-radius: 50%;\n  margin-left: -8px;\n  box-sizing: content-box;\n  border: 2px solid white;\n  z-index: 1;\n}\n\n[data-theme=\"dark\"] .mini-chat-agent-more {\n  border-color: var(--color-surface-elevated, #1e2538);\n}\n\n.mini-chat-agent-avatar:hover .mini-agent-tooltip {\n  display: block;\n}\n\n.mini-agent-tooltip {\n  position: absolute;\n  bottom: 100%;\n  left: 50%;\n  transform: translateX(-50%);\n  background-color: #333;\n  color: white;\n  font-size: 12px;\n  padding: 4px 8px;\n  border-radius: 4px;\n  white-space: nowrap;\n  display: none;\n  margin-bottom: 6px;\n  z-index: 10;\n}\n\n.mini-agent-tooltip::after {\n  content: '';\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  transform: translateX(-50%);\n  border-width: 4px;\n  border-style: solid;\n  border-color: #333 transparent transparent transparent;\n}\n\n.mini-agent-name {\n  font-weight: 500;\n}\n\n.mini-agent-role {\n  font-size: 10px;\n  opacity: 0.8;\n}\n\n/* Messages Area */\n.mini-chat-messages {\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  padding: 16px;\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  max-height: calc(100% - 100px);\n}\n\n.mini-chat-welcome {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  padding: 20px;\n  height: 100%;\n}\n\n[data-theme=\"dark\"] .mini-chat-welcome {\n  color: var(--color-text-muted, #9ca3af);\n}\n\n.mini-smokey-image {\n  height: 60px;\n  overflow: hidden;\n  margin-bottom: 16px;\n}\n\n.mini-smokey-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.mini-chat-welcome h3 {\n  font-size: 20px;\n  font-weight: 700;\n  margin: 0 0 8px;\n  color: var(--color-emerald-green);\n}\n\n.mini-chat-welcome p {\n  font-size: 14px;\n  color: var(--color-primary-soft);\n  margin: 0 0 20px;\n  max-width: 280px;\n}\n\n/* Thought cloud styles */\n.mini-thought-cloud {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 10px;\n  max-width: 100%;\n  margin-top: 20px;\n}\n\n.mini-thought-bubble {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  padding: 10px 12px;\n  border: 1px solid #e2e8f0;\n  border-radius: 12px;\n  background: white;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\n  cursor: pointer;\n  transition: all 0.2s ease;\n  text-align: left;\n  min-height: 50px;\n  gap: 3px;\n  font-size: 13px;\n}\n\n.mini-thought-bubble:hover {\n  transform: translateY(-1px);\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border-color: #3b82f6;\n}\n\n.mini-thought-bubble.loading {\n  opacity: 0.7;\n  cursor: default;\n  animation: pulse 1.5s ease-in-out infinite;\n  justify-content: center;\n  align-items: center;\n  text-align: center;\n}\n\n.mini-thought-bubble .bubble-text {\n  font-size: 13px;\n  line-height: 1.3;\n  color: #1f2937;\n  font-weight: 500;\n}\n\n.mini-thought-bubble .bubble-agent {\n  font-size: 10px;\n  color: #6b7280;\n  font-weight: 400;\n  opacity: 0.8;\n  margin-top: 1px;\n}\n\n/* Pulse animation for loading states */\n@keyframes pulse {\n  0%, 100% {\n    opacity: 0.7;\n  }\n  50% {\n    opacity: 0.5;\n  }\n}\n\n/* Loading state for thought clouds */\n.mini-thought-cloud .loading {\n  text-align: center;\n  grid-column: 1 / -1;\n  padding: 15px;\n  color: #6b7280;\n  font-style: italic;\n  font-size: 12px;\n}\n\n/* Responsive adjustments for mobile */\n@media (max-width: 768px) {\n  .mini-thought-cloud {\n    grid-template-columns: 1fr;\n    gap: 8px;\n  }\n  \n  .mini-thought-bubble {\n    padding: 8px 10px;\n    min-height: 45px;\n  }\n  \n  .mini-thought-bubble .bubble-text {\n    font-size: 12px;\n  }\n  \n  .mini-thought-bubble .bubble-agent {\n    font-size: 9px;\n  }\n}\n\n/* Message Styling */\n.mini-chat-message-row {\n  display: flex;\n  margin-bottom: 12px;\n}\n\n.user-message-row {\n  justify-content: flex-end;\n}\n\n.bot-message-row {\n  justify-content: flex-start;\n}\n\n.mini-message-avatar {\n  width: 28px;\n  height: 28px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.bot-avatar {\n  background-color: var(--color-secondary, #8b5cf6);\n  color: white;\n  margin-right: 8px;\n}\n\n.user-avatar {\n  background-color: var(--color-primary, #3b82f6);\n  color: var(--color-surface-elevated, #1e2538);\n  margin-left: 8px;\n}\n\n.mini-message-avatar-placeholder {\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.mini-chat-message-content {\n  max-width: 75%;\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n\n.user-message-row .mini-chat-message-content {\n  align-items: flex-end;\n}\n\n/* Message header styles - WhatsApp style */\n.mini-message-header {\n  font-size: 12px;\n  font-weight: 600;\n  margin-bottom: 2px;\n  padding: 0 3px;\n}\n\n.mini-message-header.bot-header {\n  color: var(--color-brunswick-green, #23504A);\n}\n\n.mini-message-header.user-header {\n  color: var(--color-emerald-green, #22AD85);\n  text-align: right;\n}\n\n.mini-message-title {\n  font-size: 10px;\n  font-weight: normal;\n  opacity: 0.8;\n}\n\n/* Message timestamp - WhatsApp style */\n.mini-message-timestamp {\n  font-size: 11px;\n  opacity: 0.7;\n  display: inline-block;\n  margin-top: 2px;\n  user-select: none;\n  position: absolute;\n  bottom: 2px;\n  right: 12px;\n}\n\n.mini-chat-message {\n  padding: 8px 12px;\n  padding-bottom: 20px;\n  border-radius: 16px;\n  font-size: 14px;\n  line-height: 1.4;\n  word-break: break-word;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n  margin-bottom: 4px;\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  min-width: 100px;\n\n}\n\n.mini-bot-message {\n  background-color: var(--color-brunswick-green, #23504A);\n  color: white;\n  border-top-left-radius: 4px;\n  align-self: flex-start;\n  max-width: 100%;\n}\n\n[data-theme=\"dark\"] .mini-bot-message {\n  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));\n  color: var(--color-primary, #e5e7eb);\n}\n\n/* Typing Indicator */\n.mini-typing-indicator {\n  display: flex;\n  gap: 4px;\n  padding: 12px 16px;\n}\n\n.mini-typing-indicator span {\n  width: 8px;\n  height: 8px;\n  background-color: var(--color-honeydew, #DFF4E9);\n  opacity: 0.7;\n  border-radius: 50%;\n  animation: typing-animation 1.4s infinite ease-in-out;\n}\n\n.mini-typing-indicator span:nth-child(1) {\n  animation-delay: 0s;\n}\n\n.mini-typing-indicator span:nth-child(2) {\n  animation-delay: 0.2s;\n}\n\n.mini-typing-indicator span:nth-child(3) {\n  animation-delay: 0.4s;\n}\n\n@keyframes typing-animation {\n  0%, 60%, 100% {\n    transform: translateY(0);\n  }\n  30% {\n    transform: translateY(-4px);\n  }\n}\n\n[data-theme=\"dark\"] .mini-typing-indicator span {\n  background-color: var(--color-text-muted, #9ca3af);\n}\n\n/* Feedback Buttons */\n.mini-feedback-buttons {\n  display: flex;\n  justify-content: flex-end;\n  gap: 8px;\n  margin-top: 4px;\n  padding-right: 0;\n  opacity: 0.6;\n  transition: opacity 0.2s ease;\n  width: 100%;\n  align-self: flex-start;\n}\n\n.mini-chat-message-content:hover .mini-feedback-buttons {\n  opacity: 1;\n}\n\n.mini-feedback-button {\n  width: 24px;\n  height: 24px;\n  border: none;\n  background-color: rgba(255, 255, 255, 0.1);\n  color: rgba(255, 255, 255, 0.7);\n  cursor: pointer;\n  padding: 0;\n  margin: 0;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: all 0.2s ease;\n}\n\n.mini-feedback-button:hover {\n  color: white;\n  background-color: rgba(255, 255, 255, 0.2);\n}\n\n.mini-feedback-button.feedback-given {\n  color: white;\n  background-color: var(--color-emerald-green, #22AD85);\n}\n\n/* Input Area */\n.mini-message-input-container {\n  padding: 12px 16px;\n  background-color: white;\n  border-top: 1px solid var(--color-border, #e5e7eb);\n  display: flex;\n  gap: 8px;\n  align-items: center;\n  flex-shrink: 0;\n}\n\n[data-theme=\"dark\"] .mini-message-input-container {\n  background-color: var(--color-surface-elevated, #1e2538);\n  border-top-color: var(--color-divider, #374151);\n}\n\n.mini-message-input {\n  flex: 1;\n  border: 1px solid var(--color-border, #e5e7eb);\n  border-radius: 24px;\n  padding: 8px 16px;\n  font-size: 14px;\n  outline: none;\n  background-color: var(--color-bg-secondary, #f3f4f6);\n  color: var(--color-primary, #1e2538);\n}\n\n[data-theme=\"dark\"] .mini-message-input {\n  border-color: var(--color-divider, #374151);\n  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));\n  color: var(--color-secondary, #d8dce7);\n}\n\n.mini-message-input:focus {\n  border-color: var(--color-primary, #3b82f6);\n}\n\n.mini-send-button {\n  background-color: var(--color-brunswick-green, #23504A);\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: background-color 0.2s;\n  flex-shrink: 0;\n}\n\n.mini-send-button:hover {\n  background-color: var(--color-dark-jungle-green, #0D211D);\n}\n\n.mini-send-button:disabled {\n  background-color: var(--color-honeydew, #DFF4E9);\n  cursor: not-allowed;\n  opacity: 0.7;\n}\n\n[data-theme=\"dark\"] .mini-send-button:disabled {\n  background-color: var(--color-divider, #374151);\n}\n\n/* Add any missing styles needed for insights view */\n\n/* Mini Insights Container */\n.mini-insights-container {\n  height: 100%;\n  overflow: hidden;\n}\n\n/* Insights list */\n.insights-container {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  overflow: hidden;\n}\n\n.insights-list {\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n  padding: 0.5rem;\n  max-height: 100%;\n}\n\n/* Empty state for insights */\n.insights-empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 100%;\n  text-align: center;\n  padding: 1rem;\n}\n\n.insights-empty-state h3 {\n  color: var(--color-brunswick-green, #23504A);\n}\n\n.insights-empty-icon {\n  width: 64px;\n  height: 64px;\n  margin-bottom: 1rem;\n}\n\n.insights-empty-icon img {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n}\n\n/* Loading spinner */\n.spinner-small {\n  width: 16px;\n  height: 16px;\n  border: 2px solid rgba(255, 255, 255, 0.3);\n  border-radius: 50%;\n  border-top-color: white;\n  animation: spin 1s ease-in-out infinite;\n  display: inline-block;\n  vertical-align: middle;\n  margin-right: 0.25rem;\n}\n\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Make sure the loading state centers properly */\n.flex.flex-col.items-center.justify-center.h-full.py-8 {\n  height: 100% !important;\n  display: flex !important;\n  align-items: center !important;\n  justify-content: center !important;\n}\n\n/* Product Table Responsive Styles */\n.overflow-x-auto {\n  overflow-x: auto;\n  overflow-y: auto;\n  max-height: 500px;\n  border-radius: 8px;\n  margin: 8px 0;\n  scrollbar-width: thin;\n  -ms-overflow-style: none;\n}\n\n.overflow-x-auto::-webkit-scrollbar {\n  height: 6px;\n  width: 6px;\n}\n\n.overflow-x-auto::-webkit-scrollbar-track {\n  background: rgba(0, 0, 0, 0.05);\n  border-radius: 3px;\n}\n\n.overflow-x-auto::-webkit-scrollbar-thumb {\n  background: rgba(0, 0, 0, 0.15);\n  border-radius: 3px;\n}\n\n.overflow-x-auto::-webkit-scrollbar-thumb:hover {\n  background: rgba(0, 0, 0, 0.3);\n}\n\n/* Responsive table styles */\n.overflow-x-auto table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.overflow-x-auto thead {\n  position: sticky;\n  top: 0;\n  background-color: var(--color-brunswick-green, #23504A);\n  color: white;\n  z-index: 1;\n}\n\n.overflow-x-auto th {\n  padding: 10px;\n  font-weight: 500;\n  text-align: left;\n  white-space: nowrap;\n}\n\n.overflow-x-auto td {\n  padding: 10px;\n  border-bottom: 1px solid var(--color-border, #e5e7eb);\n}\n\n.overflow-x-auto tr:nth-child(even) {\n  background-color: var(--color-honeydew, #DFF4E9);\n}\n\n[data-theme=\"dark\"] .overflow-x-auto thead {\n  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));\n}\n\n[data-theme=\"dark\"] .overflow-x-auto tr:nth-child(even) {\n  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.3));\n}\n\n/* Mobile and small screen optimizations for product table */\n@media (max-width: 639px) {\n  .overflow-x-auto {\n    margin: 4px 0;\n    max-height: 300px; /* Smaller height on mobile */\n    border-radius: 6px;\n  }\n  \n  .overflow-x-auto table {\n    font-size: 0.75rem; /* Smaller font on mobile */\n  }\n  \n  .overflow-x-auto th,\n  .overflow-x-auto td {\n    padding: 8px 6px;\n  }\n  \n  /* Ensure the description doesn't make rows too tall */\n  .overflow-x-auto .line-clamp-1 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 1;\n    white-space: nowrap;\n    text-overflow: ellipsis;\n    max-width: 100px;\n  }\n}\n\n/* Updating agent colors with brand palette */\n:root {\n  --color-agent-0: var(--color-brunswick-green, #23504A);\n  --color-agent-1: var(--color-pine-green, #00766D);\n  --color-agent-2: var(--color-emerald-green, #22AD85);\n  --color-agent-3: var(--color-dark-jungle-green, #0D211D);\n  --color-agent-4: var(--color-brunswick-green, #23504A);\n}\n\n/* Message content wrapper */\n.mini-message-content {\n  width: 100%;\n  word-break: break-word;\n}\n\n.mini-message-content p {\n  margin: 0;\n}\n\n.mini-message-content p:first-child {\n  margin-top: 0;\n}\n\n.mini-message-content p:last-child {\n  margin-bottom: 0;\n}\n\n.mini-user-message {\n  background-color: #E1F5EE;\n  color: var(--color-dark-jungle-green, #0D211D);\n  border-top-right-radius: 4px;\n  align-self: flex-end;\n  max-width: 100%;\n}\n\n/* Mobile and Tablet Responsive Styles */\n@media (max-width: 767px) {\n  .mini-chat-container,\n  .mini-insights-container {\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n  }\n  \n  .mini-chat-messages {\n    max-height: calc(100% - 120px);\n    padding: 12px;\n    gap: 12px;\n  }\n  \n  .mini-chat-message-content {\n    max-width: 85%;\n  }\n  \n  .mini-message-input-container {\n    padding: 10px 12px;\n    position: sticky;\n    bottom: 0;\n  }\n  \n  .mini-smokey-image {\n    height: 80px;\n  }\n  \n  /* Make touch targets larger for mobile */\n  .mini-feedback-button {\n    width: 32px;\n    height: 32px;\n  }\n  \n  .mini-send-button {\n    width: 44px;\n    height: 44px;\n  }\n  \n  .mini-message-input {\n    padding: 10px 16px;\n    font-size: 16px;\n  }\n  \n  /* Improve mobile resize handle */\n  .mini-chat-resize-handle {\n    width: 100%;\n    height: 20px;\n    top: 0;\n    bottom: auto;\n    left: 0;\n    right: 0;\n    cursor: ns-resize;\n  }\n  \n  .mini-chat-resize-handle::after {\n    height: 4px;\n    width: 50px;\n  }\n  \n  /* Vertical drag button */\n  .mini-chat-resize-handle::before {\n    content: \"≡\";\n    left: 50%;\n    top: 0;\n    margin-top: 4px;\n    transform: translate(-50%, 0);\n    rotate: 0deg;\n  }\n  \n  .mini-chat-resize-handle:hover::before {\n    opacity: 0.8;\n    transform: translate(-50%, 0) scale(1.1);\n  }\n  \n  .mini-chat-resize-handle:active::before {\n    opacity: 1;\n    transform: translate(-50%, 0) scale(1);\n  }\n}\n\n/* Tablet specific styles */\n@media (min-width: 768px) and (max-width: 1023px) {\n  .mini-chat-container,\n  .mini-insights-container {\n    height: 100%;\n  }\n  \n  .mini-chat-message-content {\n    max-width: 80%;\n  }\n}\n\n/* Handle different orientations */\n@media (max-width: 767px) and (orientation: landscape) {\n  .mini-chat-messages {\n    max-height: calc(100% - 100px);\n  }\n  \n  .mini-smokey-image {\n    height: 60px;\n  }\n}\n\n/* Insights Notification Styles */\n.mini-insights-notifications {\n  border-bottom: 1px solid var(--color-border, #e5e7eb);\n  background-color: var(--color-surface-muted, #f9fafb);\n  overflow: hidden;\n}\n\n[data-theme=\"dark\"] .mini-insights-notifications {\n  background-color: var(--color-surface-elevated, #2b3245);\n  border-color: var(--color-divider, #374151);\n}\n\n.mini-insights-header {\n  display: flex;\n  align-items: center;\n  padding: 10px 16px;\n  cursor: pointer;\n  transition: background-color 0.15s ease;\n}\n\n.mini-insights-header:hover {\n  background-color: var(--color-bg-hover, #f3f4f6);\n}\n\n[data-theme=\"dark\"] .mini-insights-header:hover {\n  background-color: rgba(255, 255, 255, 0.05);\n}\n\n.mini-insights-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  margin-right: 12px;\n}\n\n.mini-smokey-icon {\n  width: 24px;\n  height: 24px;\n}\n\n.mini-insights-title {\n  flex: 1;\n  font-weight: 500;\n  font-size: 14px;\n  color: var(--color-brunswick-green, #23504A);\n}\n\n[data-theme=\"dark\"] .mini-insights-title {\n  color: var(--color-primary, #6ee7b7);\n}\n\n.mini-insights-toggle {\n  width: 20px;\n  height: 20px;\n  color: var(--color-text-muted, #6b7280);\n}\n\n.mini-insights-list {\n  padding: 8px 16px 16px;\n}\n\n.mini-insights-item {\n  margin-bottom: 8px;\n}\n\n.mini-insights-more {\n  display: flex;\n  justify-content: center;\n  margin-top: 8px;\n  margin-bottom: 12px;\n}\n\n.mini-insights-actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 12px;\n}\n\n.mini-insights-loading {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 8px;\n  background-color: var(--color-surface-muted, #f9fafb);\n  border-bottom: 1px solid var(--color-border, #e5e7eb);\n  font-size: 14px;\n  color: var(--color-text-muted, #6b7280);\n}\n\n[data-theme=\"dark\"] .mini-insights-loading {\n  background-color: var(--color-surface-elevated, #2b3245);\n  border-color: var(--color-divider, #374151);\n  color: var(--color-text-muted, #9ca3af);\n}\n\n.mini-insights-loading span {\n  margin-left: 8px;\n}\n\n/* Enhanced formatting styles */\n.highlight {\n  background-color: rgba(255, 235, 59, 0.2);\n  padding: 0.5rem;\n  border-left: 3px solid #ffc107;\n  margin: 1rem 0;\n}\n\n.mini-chat-message details {\n  margin: 1rem 0;\n}\n\n.mini-chat-message summary {\n  font-weight: bold;\n  cursor: pointer;\n  padding: 0.5rem;\n  background-color: rgba(0, 0, 0, 0.05);\n  border-radius: 4px;\n}\n\n.mini-chat-message summary:hover {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.mini-chat-message .checklist {\n  list-style-type: none;\n  padding-left: 0.5rem;\n}\n\n.mini-chat-message .checklist li {\n  margin-bottom: 0.5rem;\n}\n\n.mini-chat-message table {\n  border-collapse: collapse;\n  width: 100%;\n  margin: 1rem 0;\n  font-size: 0.9rem;\n}\n\n.mini-chat-message th, \n.mini-chat-message td {\n  border: 1px solid #ddd;\n  padding: 8px;\n  text-align: left;\n}\n\n.mini-chat-message th {\n  background-color: rgba(0, 0, 0, 0.05);\n  font-weight: bold;\n}\n\n.mini-chat-message tr:nth-child(even) {\n  background-color: rgba(0, 0, 0, 0.02);\n}\n\n/* Markdown improvements */\n.mini-chat-message h2 {\n  font-size: 1.4rem;\n  margin-top: 1.5rem;\n  margin-bottom: 0.75rem;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.1);\n  padding-bottom: 0.25rem;\n}\n\n.mini-chat-message h3 {\n  font-size: 1.2rem;\n  margin-top: 1.25rem;\n  margin-bottom: 0.5rem;\n}\n\n.mini-chat-message blockquote {\n  border-left: 4px solid #6B7280;\n  padding-left: 1rem;\n  margin-left: 0;\n  color: #4B5563;\n  font-style: italic;\n}\n\n.mini-chat-message hr {\n  margin: 1.5rem 0;\n  border: 0;\n  border-top: 1px solid rgba(0, 0, 0, 0.1);\n}\n\n.mini-chat-message code {\n  background-color: rgba(0, 0, 0, 0.05);\n  padding: 0.2rem 0.4rem;\n  border-radius: 3px;\n  font-family: monospace;\n  font-size: 0.9em;\n}\n\n.mini-chat-message pre {\n  background-color: rgba(0, 0, 0, 0.05);\n  padding: 1rem;\n  border-radius: 4px;\n  overflow-x: auto;\n}\n\n.mini-chat-message pre code {\n  background-color: transparent;\n  padding: 0;\n}\n\n/* File upload styles */\n.mini-attachment-button {\n  background-color: transparent;\n  color: var(--color-brunswick-green, #23504A);\n  border: none;\n  cursor: pointer;\n  padding: 8px;\n  transition: all 0.2s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.mini-attachment-button:hover {\n  color: var(--color-emerald-green, #22AD85);\n  transform: scale(1.1);\n}\n\n[data-theme=\"dark\"] .mini-attachment-button {\n  color: var(--color-emerald-green, #22AD85);\n}\n\n.mini-attachment-button:disabled {\n  color: var(--color-text-muted, #9ca3af);\n  cursor: not-allowed;\n}\n\n/* Attachments preview styles */\n.attachments-preview {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 8px;\n  padding: 8px;\n  width: 100%;\n  max-height: 100px;\n  overflow-y: auto;\n  background-color: rgba(0, 0, 0, 0.02);\n  border-radius: 8px;\n  margin-bottom: 8px;\n}\n\n[data-theme=\"dark\"] .attachments-preview {\n  background-color: rgba(255, 255, 255, 0.05);\n}\n\n.attachment-preview-item {\n  display: flex;\n  align-items: center;\n  background-color: white;\n  border-radius: 4px;\n  padding: 4px 8px;\n  font-size: 12px;\n  max-width: 180px;\n  position: relative;\n}\n\n[data-theme=\"dark\"] .attachment-preview-item {\n  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));\n}\n\n.attachment-preview-icon {\n  margin-right: 6px;\n  color: var(--color-brunswick-green, #23504A);\n  flex-shrink: 0;\n}\n\n.attachment-preview-image {\n  width: 24px;\n  height: 24px;\n  border-radius: 2px;\n  overflow: hidden;\n}\n\n.attachment-preview-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n.attachment-preview-name {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 120px;\n}\n\n.attachment-remove-btn {\n  background: none;\n  border: none;\n  color: var(--color-text-muted, #9ca3af);\n  cursor: pointer;\n  padding: 2px;\n  margin-left: 6px;\n  font-size: 12px;\n  opacity: 0.7;\n  transition: all 0.2s ease;\n}\n\n.attachment-remove-btn:hover {\n  opacity: 1;\n  color: #f87171;\n}\n\n/* Upload progress bar */\n.upload-progress {\n  width: 100%;\n  height: 4px;\n  background-color: rgba(0, 0, 0, 0.05);\n  border-radius: 2px;\n  margin-bottom: 8px;\n  position: relative;\n}\n\n.upload-progress-bar {\n  height: 100%;\n  background-color: var(--color-brunswick-green, #23504A);\n  border-radius: 2px;\n  transition: width 0.3s ease;\n}\n\n.upload-progress-text {\n  position: absolute;\n  top: -18px;\n  right: 0;\n  font-size: 10px;\n  color: var(--color-text-muted, #9ca3af);\n}\n\n/* Message attachments display styles */\n.message-attachments {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  margin-top: 8px;\n  width: 100%;\n}\n\n.attachment-item {\n  display: flex;\n  background-color: rgba(0, 0, 0, 0.03);\n  border-radius: 8px;\n  overflow: hidden;\n  transition: all 0.2s ease;\n  cursor: pointer;\n}\n\n[data-theme=\"dark\"] .attachment-item {\n  background-color: rgba(255, 255, 255, 0.05);\n}\n\n.attachment-item:hover {\n  background-color: rgba(0, 0, 0, 0.05);\n  transform: translateY(-2px);\n  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);\n}\n\n[data-theme=\"dark\"] .attachment-item:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.attachment-image {\n  width: 100%;\n  max-height: 200px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 4px;\n}\n\n.attachment-image img {\n  max-width: 100%;\n  max-height: 192px;\n  object-fit: contain;\n  border-radius: 4px;\n}\n\n.attachment-file {\n  display: flex;\n  padding: 12px;\n  width: 100%;\n  align-items: center;\n}\n\n.attachment-icon {\n  flex-shrink: 0;\n  width: 40px;\n  height: 40px;\n  background-color: var(--color-brunswick-green, #23504A);\n  color: white;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n}\n\n.attachment-info {\n  flex: 1;\n  overflow: hidden;\n}\n\n.attachment-name {\n  font-weight: 500;\n  font-size: 14px;\n  margin-bottom: 4px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.attachment-size {\n  font-size: 12px;\n  color: var(--color-text-muted, #9ca3af);\n}\n\n/* Responsive attachment styles */\n@media (max-width: 767px) {\n  .attachments-preview {\n    max-height: 80px;\n  }\n  \n  .attachment-preview-item {\n    max-width: 140px;\n    padding: 3px 6px;\n  }\n  \n  .attachment-preview-name {\n    max-width: 80px;\n  }\n  \n  .attachment-image {\n    max-height: 160px;\n  }\n  \n  .attachment-image img {\n    max-height: 152px;\n  }\n}\n\n/* Agent selection information for mini chat */\n.mini-agent-selection-info {\n  font-size: 0.7rem;\n  color: #6b7280;\n  font-weight: normal;\n  margin-left: 6px;\n}\n\n.mini-agent-role {\n  font-size: 0.7rem;\n  color: #9ca3af;\n  font-weight: normal;\n  margin-left: 4px;\n}\n\n/* Enhanced message header for better agent visibility */\n.mini-message-header.bot-header {\n  display: flex;\n  align-items: center;\n  flex-wrap: wrap;\n  gap: 3px;\n  margin-bottom: 3px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  color: #374151;\n}\n", "/* .app-layout {\n  display: flex;\n  flex-direction: column;\n  height: 100vh;\n  overflow: hidden; /* Prevent any scrolling at this level *\n  position: fixed; /* Fix the entire layout to viewport *\n  top: 0;\n  left: 225px;\n  right: 0;\n  bottom: 0;\n  z-index: 0; /* Ensure it's below other fixed elements *\n} */\n\n\n/* \n.app-layout-content {\n  display: grid;\n  grid-template-columns: 1fr auto; /* Middle content takes available space, sidebar fixed width *\n  height: 100vh;\n  overflow: hidden;\n  position: relative;\n} */\n\n/* .app-layout-main {\n  position: relative;\n  overflow-y: auto; /* Only this area should scroll *\n  overflow-x: hidden;\n  padding: 0 1rem;\n  height: 100%;\n  width: 100%;\n} */\n\n/* Layout and transition styles */\n.app-layout-container {\n  display: flex;\n  width: 100%;\n  height: 100vh;\n  overflow: hidden; /* Prevent scrolling on the main container */\n  position: fixed; /* Fix the entire layout to viewport */\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n}\n\n.app-layout-nav {\n  width: 12%;\n  height: 100%;\n  flex-shrink: 0;\n  overflow-y: hidden; /* Prevent scrolling in nav */\n}\n\n.app-layout-middle {\n  transition: width 0.3s ease;\n  height: 100vh;\n  overflow-y: auto; /* Allow scrolling in content */\n  overflow-x: hidden;\n}\n\n.app-layout-middle.with-sidebar {\n  width: 66%;\n}\n\n.app-layout-middle.full-width {\n  width: 88%;\n}\n\n.app-layout-chat-sidebar {\n  width: 20%;\n  background-color: var(--color-surface);\n  border-left: 1px solid var(--color-border);\n  overflow: hidden; /* No scrolling in the sidebar container itself */\n  transition: all 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  height: 100vh; /* Match viewport height */\n  position: fixed; /* Fixed position to ensure it stays in place */\n  top: 0;\n  right: 0;\n}\n\n.app-layout-chat-sidebar.closed {\n  width: 32px; /* Just enough for the toggle button */\n  right: 0;\n  z-index: 10; /* Ensure it stays above content when collapsed */\n}\n\n.app-layout-sidebar-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden; /* Prevent scrolling at this level */\n}\n\n/* Content area inside the sidebar should be scrollable */\n.app-layout-sidebar-content .mini-chat-content {\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n\n/* Sidebar toggle button */\n.sidebar-toggle-button {\n  position: absolute;\n  left: 0;\n  top: 50%;\n  transform: translateY(-50%);\n  z-index: 11;\n  width: 24px;\n  height: 48px;\n  border: none;\n  background-color: var(--color-surface);\n  color: var(--color-text-muted);\n  cursor: pointer;\n  border-top-right-radius: 4px;\n  border-bottom-right-radius: 4px;\n  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 16px;\n  padding: 0;\n}\n\n/* Tabs in sidebar */\n.sidebar-tabs {\n  display: flex;\n  border-bottom: 1px solid var(--color-border);\n  flex-shrink: 0;\n}\n\n.sidebar-tab {\n  flex: 1;\n  text-align: center;\n  padding: 10px;\n  cursor: pointer;\n  font-size: 14px;\n  color: var(--color-text-muted);\n  transition: all 0.2s ease;\n}\n\n.sidebar-tab:hover {\n  background-color: var(--color-hover);\n}\n\n.sidebar-tab.active {\n  color: var(--color-primary);\n  border-bottom: 2px solid var(--color-primary);\n}\n\n/* Responsive behavior */\n@media (max-width: 1280px) {\n  .app-layout-chat-sidebar {\n    width: 300px;\n  }\n  \n  .app-layout-content {\n    grid-template-columns: 1fr 300px;\n  }\n  \n  .app-layout-content.sidebar-closed {\n    grid-template-columns: 1fr 32px;\n  }\n}\n\n@media (max-width: 768px) {\n  .app-layout-chat-sidebar {\n    position: fixed;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    width: 100%;\n    height: 350px;\n    border-left: none;\n    border-top: 1px solid var(--color-border);\n    grid-row: 2;\n    overflow: hidden; /* Ensure no scrolling in the container itself */\n  }\n  \n  .app-layout-chat-sidebar.closed {\n    height: 32px;\n    width: 100%;\n  }\n  \n  .app-layout-content.sidebar-closed .app-layout-main {\n    padding-bottom: 32px;\n  }\n  \n  .sidebar-toggle-button {\n    top: 0;\n    left: 50%;\n    transform: translateX(-50%) rotate(90deg);\n    border-radius: 0 0 4px 4px;\n  }\n}\n\n/* Base layout styles */\n.app-layout {\n  display: grid;\n  grid-template-areas:\n    \"header header header\"\n    \"sidebar main minichat\";\n  grid-template-columns: auto 1fr auto;\n  grid-template-rows: auto 1fr;\n  height: 100vh;\n  overflow: hidden;\n}\n\n/* For screens smaller than 1440px, adjust the grid to not reserve space for mini-chat */\n@media (max-width: 1439px) {\n  .app-layout {\n    grid-template-areas:\n      \"header header\"\n      \"sidebar main\";\n    grid-template-columns: auto 1fr;\n  }\n}\n.app-header-bg{\n  background-color: var(--color-background);\n}\n/* Header */\n.app-header {\n  grid-area: header;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: var(--space-sm) var(--space-md);\n  background-color: var(--color-background);\n  border-bottom: 1px solid var(--color-grey);\n  z-index: var(--z-header);\n  height: var(--header-height);\n  position: relative;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);\n}\n\n.header-left, .header-right {\n  display: flex;\n  align-items: center;\n  gap: var(--space-md);\n}\n\n.header-logo {\n  display: flex;\n  align-items: center;\n  margin-right: 1rem;\n}\n\n/* Updated text logo styles */\n.text-logo {\n  display: flex;\n  align-items: center;\n  position: relative;\n}\n\n.brand-name {\n  color: var(--color-emerald-green); /* Using the emerald green from the theme */\n  font-size: 1.25rem;\n  font-weight: bold;\n  letter-spacing: 0.5px;\n  margin-left: 0.25rem;\n}\n\n.beta-tag {\n  font-size: 0.6rem;\n  color: var(--color-white);\n  background-color: var(--color-red);\n  padding: 2px 4px;\n  border-radius: 3px;\n  position: relative;\n  top: -8px;\n  margin-left: 4px;\n  font-weight: bold;\n  text-transform: uppercase;\n}\n\n/* Keep the existing image styles for backwards compatibility */\n.header-logo img {\n  height: 36px;\n  max-width: 180px;\n  object-fit: contain;\n}\n\n/* Modern sidebar toggle */\n.sidebar-toggle-header.modern-toggle {\n  background: transparent;\n  border: none;\n  color: var(--color-primary);\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 34px;\n  height: 34px;\n  border-radius: 6px;\n  transition: all 0.2s ease;\n}\n\n.sidebar-toggle-header.modern-toggle:hover {\n  background-color: var(--color-grey-soft);\n  color: var(--color-emerald-green);\n}\n\n/* Header buttons */\n.sidebar-toggle-header, .mini-chat-toggle-header {\n  background: transparent;\n  border: none;\n  color: var(--color-primary);\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 0.9rem;\n  padding: var(--space-xs) var(--space-sm);\n  border-radius: 4px;\n  transition: background-color var(--transition-fast);\n}\n\n.sidebar-toggle-header:hover, .mini-chat-toggle-header:hover {\n  background-color: var(--color-grey);\n}\n\n.mini-chat-toggle-header {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--space-xs);\n  border-radius: 8px;\n  transition: all var(--transition-fast);\n  position: relative;\n  width: auto;\n  height: 40px;\n  background-color: transparent;\n  border: none;\n  cursor: pointer;\n  gap: 8px;\n  padding: 0 12px;\n}\n\n.mini-chat-toggle-header:hover {\n  background-color: var(--color-grey-soft);\n  transform: scale(1.05);\n}\n\n.mini-chat-toggle-header.active {\n  background-color: rgba(0, 0, 0, 0.05);\n}\n\n.mini-chat-toggle-header.active:hover {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.toggle-text {\n  font-size: 14px;\n  font-weight: 500;\n  color: var(--color-primary);\n  display: inline-block;\n}\n\n.smokey-icon {\n  height: 36px;\n  margin-right: 0px;\n  padding: 2px 0px;\n  object-fit: contain;\n  transition: transform 0.2s ease;\n}\n\n.mini-chat-toggle-header:hover .smokey-icon {\n  transform: scale(1.05);\n}\n\n.header-icon {\n  width: 24px;\n  height: 24px;\n  color: var(--color-red);\n}\n\n/* Sidebar Container */\n.app-sidebar {\n  grid-area: sidebar;\n  position: relative;\n  background-color: var(--color-background);\n  width: var(--sidebar-width);\n  overflow-y: auto; /* Allow scrolling for the content */\n  transition: width var(--transition-normal);\n  border-right: 1px solid var(--color-grey);\n  height: calc(100vh - var(--header-height));\n  z-index: var(--z-sidebar);\n  display: flex;\n  flex-direction: column; /* Enable vertical layout */\n}\n\n.app-sidebar.collapsed {\n  width: var(--sidebar-collapsed-width);\n}\n\n/* Main navigation area - takes available space */\n.app-sidebar nav {\n  padding: 20px;\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.app-sidebar.collapsed nav {\n  padding: 20px 0;\n  align-items: center;\n}\n\n/* Navigation links */\n.app-sidebar nav a {\n  display: flex;\n  align-items: center;\n  padding: 12px 15px;\n  border-radius: var(--border-radius);\n  margin-bottom: 5px;\n  text-decoration: none;\n  color: var(--color-primary);\n  transition: background-color 0.2s ease;\n  white-space: nowrap;\n  overflow: hidden;\n  width: 100%;\n}\n\n.app-sidebar nav a:hover {\n  background-color: var(--color-grey);\n}\n\n.app-sidebar nav a.selected {\n  color: var(--color-on-primary);\n  background: var(--color-primary);\n}\n\n/* Icon styling */\n.app-sidebar nav a .nav-icon {\n  min-width: 24px;\n  width: 24px;\n  height: 24px;\n  margin-right: 12px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  flex-shrink: 0;\n}\n\n/* Text styling */\n.app-sidebar nav a span {\n  transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.2s ease;\n  opacity: 1;\n  visibility: visible;\n  transform: translateX(0);\n}\n\n/* Collapsed state styles */\n.app-sidebar.collapsed nav a {\n  padding: 12px 0;\n  width: 44px;\n  justify-content: center;\n  margin-bottom: 10px;\n}\n\n.app-sidebar.collapsed nav a .nav-icon {\n  margin-right: 0;\n}\n\n.app-sidebar.collapsed nav a span {\n  opacity: 0;\n  visibility: hidden;\n  position: absolute;\n  transform: translateX(10px);\n}\n\n/* Profile section at the bottom */\n.sidebar-profile {\n  border-top: 1px solid var(--color-grey);\n  margin-top: auto; /* Push to the bottom */\n  width: 100%;\n  flex-shrink: 0;\n}\n\n.sidebar-profile-inner {\n  display: flex;\n  align-items: center;\n  padding: 15px 20px;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.sidebar-profile-inner:hover {\n  background-color: var(--color-background-soft);\n}\n\n.profile-image {\n  width: 38px;\n  height: 38px;\n  border-radius: 50%;\n  overflow: hidden;\n  flex-shrink: 0;\n}\n\n.profile-image img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n\n/* Profile text wrapper - may not exist in current HTML structure */\n.profile-info {\n  margin-left: 12px;\n  overflow: hidden;\n  flex-grow: 1;\n  transition: opacity 0.2s ease, visibility 0.2s ease;\n}\n\n.profile-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: var(--color-primary);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  flex-grow: 1;\n  margin-left: 12px;\n  transition: opacity 0.2s ease, visibility 0.2s ease;\n}\n\n.profile-role {\n  font-size: 12px;\n  color: var(--color-primary-soft);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-left: 8px;\n  flex-shrink: 0;\n  transition: opacity 0.2s ease, visibility 0.2s ease;\n}\n\n.profile-caret {\n  margin-left: 8px;\n  flex-shrink: 0;\n  transition: opacity 0.2s ease, visibility 0.2s ease;\n}\n\n/* Collapsed state profile */\n.app-sidebar.collapsed .sidebar-profile-inner {\n  justify-content: center;\n  padding: 15px 0;\n}\n\n.app-sidebar.collapsed .profile-name,\n.app-sidebar.collapsed .profile-role,\n.app-sidebar.collapsed .profile-caret,\n.app-sidebar.collapsed .profile-info {\n  opacity: 0;\n  visibility: hidden;\n  width: 0;\n  margin: 0;\n}\n\n/* Main Content */\n.app-main-content {\n  grid-area: main;\n  overflow-y: auto;\n  padding: var(--space-lg);\n  transition: margin-left var(--transition-normal), margin-right var(--transition-normal);\n  height: calc(100vh - var(--header-height));\n  margin-left: 0 !important;\n  margin-right: 0 !important;\n  width: 100%;\n}\n\n.app-main-content.with-header {\n  height: calc(100vh - var(--header-height));\n}\n\n.app-main-content.sidebar-collapsed {\n  margin-left: 0;\n}\n\n.app-main-content.chat-expanded {\n  margin-right: 0;\n}\n\n.app-main-content.chat-collapsed {\n  margin-right: 0;\n}\n\n/* Mini Chat */\n.app-mini-chat {\n  grid-area: minichat;\n  background-color: white;\n  transition: width 0.2s ease, transform 0.3s ease;\n  height: calc(100vh - var(--header-height));\n  overflow: hidden;\n  border-left: 1px solid var(--color-border, #e5e7eb);\n  position: relative;\n  box-sizing: border-box;\n  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.05);\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n  position: fixed;\n  top: var(--header-height);\n  right: 0;\n  height: calc(100vh - var(--header-height));\n  z-index: 99;\n}\n\n/* Visual cue animation for new users to show the panel is resizable */\n.app-mini-chat.open.show-resize-hint::before {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 50%;\n  height: 50px;\n  width: 3px;\n  background-color: var(--color-brunswick-green, #23504A);\n  transform: translateY(-50%);\n  animation: resizeHint 2s ease-in-out;\n  opacity: 0;\n  pointer-events: none;\n}\n\n@keyframes resizeHint {\n  0%, 100% { \n    opacity: 0;\n    left: 0;\n  }\n  20%, 80% { \n    opacity: 0.7;\n    left: -10px;\n  }\n  50% {\n    opacity: 0.7;\n    left: 0;\n  }\n}\n\n/* Mobile version of the visual cue */\n@media (max-width: 767px) {\n  .app-mini-chat.open.show-resize-hint::before {\n    left: 50%;\n    top: 0;\n    height: 3px;\n    width: 50px;\n    transform: translateX(-50%);\n    animation: resizeHintMobile 2s ease-in-out;\n  }\n  \n  @keyframes resizeHintMobile {\n    0%, 100% { \n      opacity: 0;\n      top: 0;\n    }\n    20%, 80% { \n      opacity: 0.7;\n      top: -10px;\n    }\n    50% {\n      opacity: 0.7;\n      top: 0;\n    }\n  }\n}\n\n[data-theme=\"dark\"] .app-mini-chat {\n  background-color: var(--color-surface-elevated, #1e2538);\n  border-left-color: var(--color-divider, #374151);\n}\n\n.app-mini-chat.open {\n  width: 350px; /* Default width, will be overridden by inline style */\n  transform: translateX(0);\n}\n\n.app-mini-chat.closed {\n  width: 0;\n  transform: translateX(100%);\n  border-left: none;\n  overflow: hidden;\n}\n\n.app-mini-chat-content {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  width: 100%;\n}\n\n/* Mini-chat tabs at top */\n.mini-chat-tabs {\n  display: flex;\n  border-bottom: 1px solid var(--color-border, #e5e7eb);\n  flex-shrink: 0;\n}\n\n.mini-chat-tab {\n  flex: 1;\n  text-align: center;\n  padding: 10px;\n  cursor: pointer;\n  font-size: 14px;\n  color: var(--color-text-muted, #6b7280);\n  transition: all 0.2s ease;\n}\n\n.mini-chat-tab.active {\n  color: var(--color-brunswick-green, #23504A);\n  border-bottom: 2px solid var(--color-brunswick-green, #23504A);\n  font-weight: 500;\n}\n\n.mini-chat-tab:hover:not(.active) {\n  background-color: var(--color-honeydew, #DFF4E9);\n  color: var(--color-brunswick-green, #23504A);\n}\n\n/* Mobile Hamburger Menu */\n.mobile-menu-toggle {\n  display: none;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  background-color: transparent;\n  border: none;\n  padding: 0;\n  width: 40px;\n  height: 40px;\n}\n\n.hamburger {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  width: 20px;\n  height: 16px;\n}\n\n.hamburger span {\n  display: block;\n  height: 2px;\n  width: 100%;\n  background-color: var(--color-primary);\n  border-radius: 2px;\n  transition: all 0.3s ease;\n}\n\n.hamburger.active span:nth-child(1) {\n  transform: translateY(7px) rotate(45deg);\n}\n\n.hamburger.active span:nth-child(2) {\n  opacity: 0;\n}\n\n.hamburger.active span:nth-child(3) {\n  transform: translateY(-7px) rotate(-45deg);\n}\n\n/* Mobile menu overlay */\n.mobile-menu-overlay {\n  position: fixed;\n  top: var(--header-height);\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 20;\n  display: none;\n}\n\n/* Header Location Picker */\n.header-location-switcher.select-button {\n  height: 40px;\n  padding: 5px 12px;\n  border-radius: var(--border-radius);\n  border: 1px solid var(--color-grey);\n  background-color: var(--color-background);\n  transition: all var(--transition-fast);\n  display: flex;\n  align-items: center;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n}\n\n.header-location-switcher.select-button:hover {\n  background-color: var(--color-grey-soft);\n  border-color: var(--color-emerald-green);\n}\n\n.location-switcher-label {\n  color: var(--color-primary-soft);\n  font-size: 12px;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.location-switcher-value {\n  color: var(--color-primary);\n  font-size: 14px;\n  font-weight: 500;\n  line-height: 1.2;\n}\n\n/* Update the dropdown icon to make it more modern */\n.header-location-switcher .dropdown-icon {\n  margin-left: 8px;\n  color: var(--color-emerald-green);\n  transition: transform 0.2s ease;\n}\n\n.header-location-switcher:hover .dropdown-icon {\n  transform: translateY(2px);\n}\n\n/* Mobile styles (767px and below) */\n@media (max-width: 767px) {\n  /* Layout changes for mobile */\n  .app-layout {\n    grid-template-areas:\n      \"header header\"\n      \"main main\";\n    grid-template-columns: 1fr;\n  }\n  \n  /* Mobile menu button */\n  .mobile-menu-toggle {\n    display: flex;\n    margin-right: 10px;\n  }\n  \n  .mobile-menu-overlay {\n    display: block;\n  }\n  \n  /* Smaller app title for mobile */\n  .brand-name {\n    font-size: 1rem;\n    letter-spacing: 0.3px;\n  }\n  \n  .beta-tag {\n    font-size: 0.5rem;\n    top: -6px;\n    padding: 1px 3px;\n  }\n  \n  /* Sidebar for mobile - hidden by default */\n  .app-sidebar {\n    position: fixed;\n    left: 0;\n    top: var(--header-height);\n    bottom: 0;\n    z-index: 50; /* Higher than mini-chat */\n    transform: translateX(-100%);\n    width: 100%;\n    max-width: 300px;\n    background-color: var(--color-background);\n    border-right: 1px solid var(--color-grey);\n    height: calc(100vh - var(--header-height));\n    display: flex;\n    flex-direction: column;\n  }\n  \n  /* Mobile sidebar content */\n  .app-sidebar nav {\n    flex: 1;\n    padding: 15px;\n    overflow-y: auto;\n  }\n  \n  /* Keep profile at bottom */\n  .sidebar-profile {\n    margin-top: auto;\n    border-top: 1px solid var(--color-grey);\n  }\n  \n  /* Show sidebar when menu is open */\n  .app-sidebar.mobile-open {\n    transform: translateX(0);\n    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.15);\n  }\n  \n  /* Mobile sidebar location picker */\n  .sidebar-location-picker {\n    padding: 12px 20px;\n    border-bottom: 1px solid var(--color-grey);\n  }\n  \n  .sidebar-location-picker .header-location-switcher.select-button {\n    width: 100%;\n    max-width: 100%;\n    margin-left: 0;\n  }\n  \n  .mobile-location-switcher.select-button {\n    border: 1px solid var(--color-grey);\n    background-color: var(--color-background-soft);\n    padding: 10px 15px;\n    height: auto;\n  }\n  \n  .mobile-location-switcher .location-switcher-label {\n    font-size: 13px;\n    opacity: 0.8;\n  }\n  \n  .mobile-location-switcher .location-switcher-value {\n    font-size: 16px;\n    font-weight: 500;\n    line-height: 1.3;\n  }\n  \n  /* Header adjustments */\n  .header-left {\n    flex: 1;\n  }\n  \n  .header-logo img {\n    height: 30px;\n    margin-left: 5px;\n  }\n  \n  .app-header {\n    padding: var(--space-xs) var(--space-sm);\n  }\n  \n  .app-main-content {\n    margin-left: 0 !important;\n    margin-right: 0 !important;\n    width: 100%;\n    padding: var(--space-md);\n  }\n  \n  /* Mini chat on mobile */\n  .app-mini-chat {\n    position: fixed;\n    top: auto;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    width: 100% !important; /* Override inline style on mobile */\n    height: 50vh; /* Half the screen height on mobile */\n    max-height: 500px;\n    border-top: 1px solid var(--color-border, #e5e7eb);\n    border-left: none;\n    border-radius: 16px 16px 0 0;\n    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);\n    z-index: 1000;\n    transform: translateY(0);\n  }\n  \n  .app-mini-chat.closed {\n    transform: translateY(100%);\n  }\n  \n  .app-mini-chat.open {\n    transform: translateY(0);\n  }\n\n  /* Move the resize handle to the top edge on mobile */\n  .mini-chat-resize-handle {\n    top: 0;\n    left: 0;\n    right: 0;\n    width: 100%;\n    height: 20px;\n    cursor: ns-resize;\n  }\n\n  .mini-chat-resize-handle::after {\n    width: 50px;\n    height: 4px;\n    border-radius: 2px;\n  }\n\n  /* Responsive header styles */\n  .header-location-switcher.select-button {\n    max-width: 150px;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n  \n  .location-switcher-value {\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  }\n\n  /* Hide toggle text on mobile */\n  .toggle-text {\n    display: none;\n  }\n  \n  .mini-chat-toggle-header {\n    width: 40px;\n    padding: var(--space-xs);\n  }\n}\n\n/* Dark mode styles */\n@media (prefers-color-scheme: dark) {\n  .app-sidebar,\n  .app-mini-chat, \n  .mini-chat-tabs {\n    background-color: var(--color-background-dark, var(--color-background));\n  }\n\n  .hamburger span {\n    background-color: var(--color-primary);\n  }\n}\n\n/* Media query for screens smaller than 1440px - Overlay mini-chat */\n@media (max-width: 1439px) {\n  .app-mini-chat {\n    position: fixed;\n    right: 0;\n    top: var(--header-height);\n    width: var(--minichat-width);\n    z-index: 45; /* Higher than main content but lower than mobile sidebar */\n    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);\n  }\n  \n  /* Remove right margin adjustment from main content when mini-chat is open */\n  .app-main-content.chat-expanded {\n    margin-right: 0;\n  }\n  \n  /* When mini-chat is open, let it overlay the main content */\n  .app-mini-chat.open {\n    transform: translateX(0);\n  }\n  \n  .app-mini-chat.closed {\n    transform: translateX(100%);\n    box-shadow: none;\n  }\n}\n\n/* New custom sidebar styles */\n.custom-sidebar {\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  height: 100%;\n  padding: 10px 0;\n}\n\n.primary-links,\n.standalone-links {\n  padding: 0 12px;\n}\n\n/* Nav link styling */\n.nav-link {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  margin-bottom: 4px;\n  border-radius: 8px;\n  text-decoration: none;\n  transition: background-color 0.2s ease;\n}\n\n.nav-link:hover {\n  background-color: var(--color-hover-light);\n}\n\n.nav-link.active,\n.nav-link.selected {\n  background-color: var(--color-primary-light);\n  color: var(--color-primary);\n  font-weight: 500;\n}\n\n.nav-link .nav-icon {\n  width: 20px;\n  height: 20px;\n  margin-right: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.nav-link.collapsed {\n  justify-content: center;\n  padding: 12px 8px;\n}\n\n.nav-link.collapsed .nav-icon {\n  margin-right: 0;\n}\n\n/* Collapsible menu section styling */\n.collapsible-menu-section {\n  margin: 10px 0;\n}\n\n.collapsible-header {\n  display: flex;\n  align-items: center;\n  padding: 12px 16px;\n  margin: 4px 12px;\n  border-radius: 8px;\n  cursor: pointer;\n  color: var(--color-text-secondary);\n  font-weight: 500;\n  transition: background-color 0.2s ease;\n  position: relative;\n}\n\n.collapsible-header:hover {\n  background-color: var(--color-hover-light);\n}\n\n.collapsible-header .nav-icon {\n  width: 20px;\n  height: 20px;\n  margin-right: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n\n.collapsible-header.collapsed {\n  justify-content: center;\n  padding: 12px 8px;\n}\n\n.collapsible-header.collapsed .nav-icon {\n  margin-right: 0;\n}\n\n.section-title {\n  flex-grow: 1;\n}\n\n.toggle-icon {\n  width: 16px;\n  height: 16px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  transition: transform 0.2s ease;\n}\n\n.collapsible-content {\n  padding: 0 12px 0 24px;\n}\n\n.nav-link.sublink {\n  padding: 10px 16px;\n  font-size: 0.95em;\n}\n\n/* Mobile styles */\n@media (max-width: 767px) {\n  .collapsible-header {\n    padding: 14px 16px;\n  }\n  \n  .nav-link {\n    padding: 14px 16px;\n  }\n  \n  .collapsible-content {\n    padding: 0 12px 0 20px;\n  }\n}\n\n/* Insights Notification Styles */\n.insights-notification {\n  position: relative;\n  margin-right: 8px;\n}\n\n.insights-bell-button {\n  background: none;\n  border: none;\n  cursor: pointer;\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 36px;\n  height: 36px;\n  border-radius: 50%;\n  transition: background-color 0.2s;\n}\n\n.insights-bell-button:hover {\n  background-color: rgba(0, 0, 0, 0.05);\n}\n\n[data-theme=\"dark\"] .insights-bell-button:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n.bell-icon {\n  width: 22px;\n  height: 22px;\n  color: var(--color-brunswick-green, #23504A);\n}\n\n[data-theme=\"dark\"] .bell-icon {\n  color: var(--color-primary, #d1d5db);\n}\n\n.insights-count {\n  position: absolute;\n  top: 0;\n  right: 0;\n  background-color: var(--color-emerald-green, #22AD85);\n  color: white;\n  font-size: 11px;\n  font-weight: 600;\n  width: 18px;\n  height: 18px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\n}\n\n.insights-dropdown {\n  position: absolute;\n  top: 100%;\n  right: 0;\n  width: 360px;\n  max-width: 90vw;\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  overflow: hidden;\n  margin-top: 8px;\n}\n\n[data-theme=\"dark\"] .insights-dropdown {\n  background-color: var(--color-surface-elevated, #1e2538);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);\n}\n\n.insights-dropdown-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 16px;\n  border-bottom: 1px solid var(--color-border, #e5e7eb);\n}\n\n[data-theme=\"dark\"] .insights-dropdown-header {\n  border-bottom-color: var(--color-divider, #374151);\n}\n\n.insights-dropdown-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: var(--color-brunswick-green, #23504A);\n}\n\n[data-theme=\"dark\"] .insights-dropdown-header h3 {\n  color: var(--color-primary, #e5e7eb);\n}\n\n.insights-dropdown-subtitle {\n  margin: 2px 0 0 0;\n  font-size: 12px;\n  color: var(--color-text-muted, #6b7280);\n}\n\n[data-theme=\"dark\"] .insights-dropdown-subtitle {\n  color: var(--color-text-muted, #9ca3af);\n}\n\n.generate-insights-button {\n  /* Keep these styles to adjust size */\n  margin: 0 !important;\n  padding: 4px 10px !important;\n  font-size: 12px !important;\n  display: flex !important;\n  align-items: center !important;\n  gap: 6px !important;\n}\n\n/* Ensure the Button component's styling is preserved */\n.generate-insights-button.btn-small {\n  height: auto !important;\n  min-height: unset !important;\n}\n\n.insights-dropdown-content {\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.insights-list {\n  padding: 10px 16px;\n}\n\n.insight-item {\n  margin-bottom: 8px;\n}\n\n.insights-loading,\n.no-insights {\n  padding: 20px;\n  text-align: center;\n  color: var(--color-text-muted, #6b7280);\n}\n\n[data-theme=\"dark\"] .insights-loading,\n[data-theme=\"dark\"] .no-insights {\n  color: var(--color-text-muted, #9ca3af);\n}\n\n.insights-loading p,\n.no-insights p {\n  margin: 6px 0;\n  font-size: 13px;\n}\n\n.no-insights-description {\n  color: var(--color-text-muted);\n  font-size: 14px;\n  margin-top: 8px;\n}\n\n/* Loading agents animation */\n.loading-agents {\n  display: flex;\n  gap: 8px;\n  justify-content: center;\n  margin-top: 12px;\n}\n\n.loading-agent {\n  font-size: 24px;\n  animation: bounce 1.4s ease-in-out infinite;\n}\n\n.loading-agent:nth-child(1) { animation-delay: 0s; }\n.loading-agent:nth-child(2) { animation-delay: 0.2s; }\n.loading-agent:nth-child(3) { animation-delay: 0.4s; }\n\n@keyframes bounce {\n  0%, 80%, 100% { transform: scale(1) translateY(0); }\n  40% { transform: scale(1.1) translateY(-6px); }\n}\n\n/* Mini agents preview */\n.mini-agents-preview {\n  display: flex;\n  gap: 6px;\n  justify-content: center;\n  margin: 12px 0 16px 0;\n}\n\n.mini-agent-icon {\n  font-size: 20px;\n  opacity: 0.8;\n  transition: opacity 0.2s, transform 0.2s;\n  cursor: help;\n}\n\n.mini-agent-icon:hover {\n  opacity: 1;\n  transform: scale(1.1);\n}\n\n/* Dropdown generate button */\n.dropdown-generate-button {\n  margin: 0 auto !important;\n  display: flex !important;\n  align-items: center !important;\n  gap: 6px !important;\n}\n\n/* Insights dropdown footer styling */\n.insights-dropdown-footer {\n  padding: 10px 15px;\n  border-top: 1px solid var(--color-border);\n  text-align: center;\n}\n\n.view-all-insights-link {\n  font-size: 14px;\n  color: var(--color-primary);\n  text-decoration: none;\n  display: block;\n  transition: opacity 0.2s ease;\n}\n\n.view-all-insights-link:hover {\n  opacity: 0.8;\n  text-decoration: underline;\n}\n\n[data-theme=\"dark\"] .insights-dropdown-footer {\n  border-top: 1px solid var(--color-border-dark, #333);\n}\n\n/* Mobile styles for insights dropdown */\n@media (max-width: 767px) {\n  .insights-dropdown {\n    width: calc(100vw - 24px);\n    max-width: 360px;\n    right: -12px; /* Adjust position to align properly */\n  }\n\n  .insights-bell-button {\n    height: 42px;\n    width: 42px;\n  }\n\n  .bell-icon {\n    width: 24px;\n    height: 24px;\n  }\n\n  .compact-insight-title {\n    max-width: 260px;\n  }\n\n  .compact-insight-description {\n    max-width: 260px;\n  }\n}\n\n/* Ensure the dropdown has a good max height on smaller screens */\n@media (max-height: 700px) {\n  .insights-dropdown-content {\n    max-height: 300px;\n  }\n}\n\n/* Processing indicator in header */\n.processing-indicator {\n  position: relative;\n  margin-right: 12px;\n}\n\n.processing-button {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 6px 12px;\n  border-radius: 16px;\n  background-color: var(--color-surface-hover);\n  color: var(--color-text);\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.processing-button:hover {\n  background-color: var(--color-surface-active);\n}\n\n.processing-spinner {\n  animation: spin 1s linear infinite;\n  color: var(--color-primary);\n}\n\n.processing-text {\n  white-space: nowrap;\n}\n\n.processing-dropdown {\n  position: absolute;\n  top: calc(100% + 8px);\n  right: 0;\n  width: 300px;\n  max-width: 90vw;\n  background-color: var(--color-surface);\n  border-radius: 8px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n  z-index: 100;\n  overflow: hidden;\n}\n\n.processing-dropdown-header {\n  padding: 12px 16px;\n  border-bottom: 1px solid var(--color-border);\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.processing-dropdown-header h3 {\n  font-size: 1rem;\n  font-weight: 600;\n  margin: 0;\n}\n\n.processing-dropdown-content {\n  padding: 16px;\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.processing-dropdown-content p {\n  margin-bottom: 12px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Schedule Call Button styles */\n.schedule-call-button {\n  display: flex;\n  align-items: center;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  padding: 6px 12px;\n  margin-right: 12px;\n  cursor: pointer;\n  font-size: 14px;\n  font-weight: 500;\n  transition: background-color 0.2s ease;\n}\n\n\n.schedule-call-button .calendar-icon {\n  margin-right: 6px;\n  font-size: 16px;\n}\n\n.schedule-call-button .schedule-text {\n  white-space: nowrap;\n}\n\n@media (max-width: 560px) {\n  .schedule-call-button {\n    padding: 4px 8px;\n    font-size: 12px;\n  }\n  .schedule-call-button .button-icon svg {\n    margin-right: 0px;\n  }\n  \n  \n  .schedule-call-button .button-text {\n    display: none;\n  }\n  \n  .schedule-call-button .calendar-icon {\n    margin-right: 0;\n  }\n}\n\n/* Premium feature badge */\n.nav-link-wrapper {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.premium-badge {\n  position: absolute;\n  right: 5px;\n  top: 50%;\n  transform: translateY(-50%);\n  background-color: var(--color-accent, #f59e0b);\n  color: white;\n  font-size: 0.6rem;\n  font-weight: 600;\n  padding: 0.1rem 0.3rem;\n  border-radius: 0.25rem;\n  z-index: 1;\n}\n\n.premium-badge-small {\n  position: absolute;\n  right: 2px;\n  top: 50%;\n  transform: translateY(-50%);\n  font-size: 0.75rem;\n  color: var(--color-accent, #f59e0b);\n}\n\n.premium-feature {\n  color: var(--color-text-secondary, #6b7280);\n  opacity: 0.8;\n}\n\n/* Upgrade CTA */\n.upgrade-cta-section {\n  margin: 1rem;\n  padding: 1rem;\n  background-color: rgba(245, 158, 11, 0.1);\n  border-radius: 0.75rem;\n  text-align: center;\n}\n\n.upgrade-button {\n  width: 100%;\n  padding: 0.5rem 1rem;\n  font-weight: 600;\n  border-radius: 0.5rem;\n  margin-bottom: 0.5rem;\n  background-color: var(--color-accent, #f59e0b);\n  color: white;\n  border: none;\n  cursor: pointer;\n}\n\n.upgrade-text {\n  margin: 0;\n  font-size: 0.75rem;\n  color: var(--color-text-secondary, #6b7280);\n}\n\n.upgrade-cta-collapsed {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin: 1rem 0;\n  padding: 0.5rem;\n  background-color: rgba(245, 158, 11, 0.1);\n  border-radius: 0.5rem;\n  cursor: pointer;\n}\n\n.upgrade-icon {\n  font-size: 1.25rem;\n  color: var(--color-accent, #f59e0b);\n} ", "/* System Health Page Styles */\n\n/* Tabs */\n.health-tabs {\n  display: flex;\n  border-bottom: 1px solid var(--color-divider);\n  margin-bottom: 1.5rem;\n}\n\n.health-tab {\n  padding: 0.75rem 1.5rem;\n  font-size: 1rem;\n  font-weight: 500;\n  background: none;\n  border: none;\n  border-bottom: 3px solid transparent;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  color: var(--color-text-secondary);\n}\n\n.health-tab:hover {\n  color: var(--color-text);\n}\n\n.health-tab.active {\n  color: var(--color-primary);\n  border-bottom-color: var(--color-primary);\n}\n\n/* Content section */\n.health-content {\n  margin-bottom: 2rem;\n}\n\n/* Summary Cards */\n.health-summary {\n  margin-bottom: 1.5rem;\n}\n\n.summary-card {\n  background-color: var(--color-background-primary);\n  border-radius: 0.75rem;\n  padding: 1.5rem;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.summary-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.75rem;\n}\n\n.summary-header h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin: 0;\n}\n\n.summary-timestamp {\n  font-size: 0.875rem;\n  color: var(--color-text-secondary);\n  font-weight: 500;\n}\n\n.summary-stats {\n  display: flex;\n  justify-content: space-around;\n  gap: 1rem;\n}\n\n.health-stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 1rem;\n  border-radius: 0.5rem;\n  min-width: 120px;\n}\n\n.health-stat-value {\n  font-size: 2rem;\n  font-weight: 700;\n  margin-bottom: 0.5rem;\n}\n\n.health-stat-label {\n  font-size: 0.875rem;\n  color: var(--color-text-secondary);\n}\n\n.stat-healthy {\n  background-color: rgba(var(--color-success-rgb), 0.1);\n  color: var(--color-success);\n}\n\n.stat-warning {\n  background-color: rgba(var(--color-warning-rgb), 0.1);\n  color: var(--color-warning);\n}\n\n.stat-error {\n  background-color: rgba(var(--color-error-rgb), 0.1);\n  color: var(--color-error);\n}\n\n/* Agent health cards */\n.agents-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.health-agent-card {\n  background-color: var(--color-surface);\n  border-radius: 0.75rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  overflow: hidden;\n  border-left: 4px solid transparent;\n}\n\n.health-agent-card.available {\n  border-left-color: var(--color-success);\n}\n\n.health-agent-card.partial {\n  border-left-color: var(--color-warning);\n}\n\n.health-agent-card.unavailable {\n  border-left-color: var(--color-error);\n}\n\n.health-agent-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 1rem 1.5rem;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n\n.health-agent-header:hover {\n  background-color: rgba(var(--color-primary-rgb), 0.05);\n}\n\n.health-agent-info {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.health-agent-icon {\n  width: 3rem;\n  height: 3rem;\n  border-radius: 50%;\n  background-color: rgba(var(--color-primary-rgb), 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n}\n\n.health-agent-icon img {\n  width: 100%;\n  height: 100%;\n  object-fit: contain;\n}\n\n.health-agent-details {\n  flex: 1;\n}\n\n.health-agent-details h3 {\n  font-size: 1.125rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n}\n\n.health-agent-status-bar {\n  display: flex;\n  flex-direction: column;\n  width: 100%;\n  max-width: 250px;\n}\n\n.progress-bar {\n  height: 0.5rem;\n  background-color: var(--color-surface-muted);\n  border-radius: 1rem;\n  overflow: hidden;\n  margin-bottom: 0.25rem;\n}\n\n.progress-fill {\n  height: 100%;\n  border-radius: 1rem;\n  background-color: var(--color-primary);\n  transition: width 0.3s ease;\n}\n\n.percentage {\n  font-size: 0.75rem;\n  color: var(--color-text-secondary);\n}\n\n.health-agent-status-icon {\n  display: flex;\n  align-items: center;\n  gap: 0.75rem;\n}\n\n.health-status-icon {\n  width: 1.5rem;\n  height: 1.5rem;\n}\n\n.health-status-healthy {\n  color: var(--color-success);\n}\n\n.health-status-warning {\n  color: var(--color-warning);\n}\n\n.health-status-error {\n  color: var(--color-error);\n}\n\n.health-status-unknown {\n  color: var(--color-text-secondary);\n}\n\n.chevron {\n  transition: transform 0.3s ease;\n}\n\n.chevron.expanded {\n  transform: rotate(90deg);\n}\n\n/* Agent expanded details */\n.health-agent-details-expanded {\n  padding: 0 1.5rem 1.5rem;\n  border-top: 1px solid var(--color-divider);\n  animation: fadeIn 0.3s ease;\n}\n\n@keyframes fadeIn {\n  from { opacity: 0; }\n  to { opacity: 1; }\n}\n\n/* Agent description */\n.health-agent-description {\n  margin-bottom: 1rem;\n  margin-top: 1rem;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  color: var(--color-text-secondary);\n  padding: 0.75rem;\n  background-color: var(--color-surface-elevated);\n  border-radius: 0.5rem;\n}\n\n/* Agent capabilities */\n.health-agent-capabilities {\n  margin-bottom: 1rem;\n}\n\n.health-agent-capabilities h4 {\n  font-size: 1rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  margin-top: 1rem;\n}\n\n.capabilities-list {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 0.5rem;\n}\n\n.capability-item {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  background-color: rgba(var(--color-primary-rgb), 0.05);\n  padding: 0.5rem 0.75rem;\n  border-radius: 0.375rem;\n  font-size: 0.75rem;\n}\n\n.capability-icon {\n  color: var(--color-primary);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n/* Requirements list */\n.requirements-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.requirements-list h4 {\n  font-size: 1rem;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  margin-top: 1rem;\n}\n\n.requirement-item {\n  display: flex;\n  gap: 1rem;\n  padding: 0.75rem;\n  border-radius: 0.5rem;\n  background-color: var(--color-surface-elevated);\n}\n\n.requirement-item.met {\n  background-color: rgba(var(--color-success-rgb), 0.05);\n}\n\n.requirement-item.unmet {\n  background-color: rgba(var(--color-error-rgb), 0.05);\n}\n\n.requirement-details {\n  flex: 1;\n}\n\n.requirement-name {\n  font-weight: 500;\n  font-size: 0.875rem;\n  text-transform: capitalize;\n  display: block;\n  margin-bottom: 0.25rem;\n}\n\n.requirement-progress {\n  display: block;\n  font-size: 0.75rem;\n  color: var(--color-text-secondary);\n  margin-bottom: 0.25rem;\n}\n\n.missing-columns, .missing-types {\n  font-size: 0.75rem;\n  color: var(--color-error);\n  margin-top: 0.25rem;\n}\n\n/* System grid */\n.system-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n  gap: 1.5rem;\n  margin-top: 1rem;\n}\n\n.system-card {\n  display: flex;\n  background-color: var(--color-background-primary);\n  border-radius: 0.75rem;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  padding: 1.5rem;\n  border-left: 4px solid transparent;\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.system-card:hover {\n  transform: translateY(-3px);\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n\n.system-card.healthy {\n  border-left-color: var(--color-success);\n}\n\n.system-card.warning {\n  border-left-color: var(--color-warning);\n}\n\n.system-card.error {\n  border-left-color: var(--color-error);\n}\n\n.system-icon {\n  width: 3.5rem;\n  height: 3.5rem;\n  border-radius: 50%;\n  background-color: rgba(var(--color-primary-rgb), 0.1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 1.25rem;\n  flex-shrink: 0;\n  color: var(--color-primary);\n  transition: all 0.3s ease;\n}\n\n.system-icon-healthy {\n  background-color: rgba(var(--color-success-rgb), 0.15);\n  color: var(--color-success);\n}\n\n.system-icon-warning {\n  background-color: rgba(var(--color-warning-rgb), 0.15);\n  color: var(--color-warning);\n}\n\n.system-icon-error {\n  background-color: rgba(var(--color-error-rgb), 0.15);\n  color: var(--color-error);\n}\n\n.system-details {\n  flex: 1;\n}\n\n.system-details h3 {\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n}\n\n.system-metrics {\n  display: flex;\n  flex-direction: column;\n  gap: 0.75rem;\n}\n\n.metric {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-size: 0.875rem;\n}\n\n.metric-label {\n  font-weight: 500;\n}\n\n.metric-value {\n  font-weight: 600;\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n}\n\n/* Status icon size adjustment */\n.metric .health-status-icon {\n  width: 1.25rem;\n  height: 1.25rem;\n}\n\n/* Data sources */\n.data-health-container {\n  padding: 1rem 0;\n}\n\n.data-sources-list {\n  display: flex;\n  flex-direction: column;\n  gap: 1.5rem;\n}\n\n.data-sources-list h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin-bottom: 1rem;\n}\n\n.data-source-card {\n  background-color: var(--color-surface);\n  border-radius: 0.75rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  padding: 1.5rem;\n}\n\n.data-source-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.data-source-header h4 {\n  font-size: 1.125rem;\n  font-weight: 600;\n}\n\n.data-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n\n.data-stat {\n  display: flex;\n  flex-direction: column;\n}\n\n.data-stat-label {\n  font-size: 0.75rem;\n  margin-bottom: 0.25rem;\n}\n\n.data-stat-value {\n  font-size: 1.125rem;\n  font-weight: 600;\n}\n\n.data-issues {\n  margin-top: 1rem;\n  padding-top: 1rem;\n  border-top: 1px solid var(--color-divider);\n}\n\n.issue-item {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  font-size: 0.875rem;\n  margin-bottom: 0.5rem;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .summary-stats {\n    flex-direction: column;\n  }\n  \n  .system-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .data-stats {\n    grid-template-columns: 1fr;\n  }\n}\n\n/* Vector Data Health Styles */\n.vector-data-health {\n  margin-bottom: 2rem;\n  background-color: var(--color-background-secondary);\n  border-radius: 0.5rem;\n  padding: 1.5rem;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.section-header h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  margin: 0;\n}\n\n.refresh-button {\n  background-color: var(--color-primary-lighter);\n  color: var(--color-primary);\n  border: none;\n  border-radius: 0.25rem;\n  padding: 0.5rem 1rem;\n  font-size: 0.875rem;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.refresh-button:hover {\n  background-color: var(--color-primary-lightest);\n}\n\n.refresh-button:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.health-summary-card {\n  background-color: var(--color-background-primary);\n  border-radius: 0.375rem;\n  padding: 1rem;\n  margin-bottom: 1.5rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.health-overall-status {\n  display: flex;\n  align-items: center;\n  font-weight: 600;\n}\n\n.health-overall-status.healthy {\n  color: var(--color-success);\n}\n\n.health-overall-status.warning {\n  color: var(--color-warning);\n}\n\n.health-overall-status.error {\n  color: var(--color-error);\n}\n\n.health-overall-status .health-status-icon {\n  margin-right: 0.5rem;\n}\n\n.last-checked {\n  font-size: 0.875rem;\n  color: var(--color-text-secondary);\n}\n\n.vector-services-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n  gap: 1rem;\n}\n\n.service-card {\n  background-color: var(--color-background-primary);\n  border-radius: 0.375rem;\n  padding: 1rem;\n  border-left: 4px solid transparent;\n}\n\n.service-card.healthy {\n  border-left-color: var(--color-success);\n}\n\n.service-card.warning {\n  border-left-color: var(--color-warning);\n}\n\n.service-card.error {\n  border-left-color: var(--color-error);\n}\n\n.service-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.75rem;\n}\n\n.service-header h4 {\n  font-size: 1rem;\n  font-weight: 600;\n  margin: 0;\n}\n\n.service-details {\n  margin-bottom: 0.75rem;\n}\n\n.service-metric {\n  display: flex;\n  justify-content: space-between;\n  font-size: 0.875rem;\n  margin-bottom: 0.5rem;\n}\n\n.service-metric-label {\n  color: var(--color-text-secondary);\n}\n\n.service-metric-value {\n  font-weight: 500;\n}\n\n.service-message {\n  background-color: var(--color-background-secondary);\n  border-radius: 0.25rem;\n  padding: 0.75rem;\n  font-size: 0.875rem;\n  color: var(--color-text-secondary);\n}\n\n.health-error-message {\n  background-color: var(--color-error-lightest);\n  border-radius: 0.375rem;\n  padding: 1rem;\n  margin-bottom: 1rem;\n  display: flex;\n  align-items: center;\n  color: var(--color-error);\n}\n\n.health-error-message .health-status-icon {\n  margin-right: 0.75rem;\n}\n\n.health-error-message span {\n  flex: 1;\n}\n\n.health-error-message .refresh-button {\n  margin-left: 1rem;\n  background-color: var(--color-error-lighter);\n  color: var(--color-error);\n}\n\n.health-error-message .refresh-button:hover {\n  background-color: var(--color-error-light);\n} ", "/* Competitors page styles */\n.competitors-container {\n  padding: 1rem 0;\n}\n\n.loading-container {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n  text-align: center;\n}\n\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 3rem 1rem;\n  text-align: center;\n  background-color: var(--surface-secondary);\n  border-radius: 0.5rem;\n}\n\n.empty-state .hint {\n  margin: 1rem 0;\n  color: var(--primary-soft);\n  font-size: 0.9rem;\n}\n\n.hint {\n  color: var(--text-secondary);\n  margin-bottom: 16px;\n}\n\n.competitors-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 1rem;\n}\n\n.competitor-card {\n  background-color: var(--surface);\n  border: 1px solid var(--divider);\n  border-radius: 0.5rem;\n  padding: 1rem;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n}\n\n.competitor-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 0.5rem;\n}\n\n.competitor-header h3 {\n  font-size: 1.1rem;\n  font-weight: 500;\n  margin: 0;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.remove-button {\n  color: var(--danger);\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 0.25rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 0.25rem;\n  flex-shrink: 0;\n}\n\n.remove-button:hover {\n  background-color: var(--danger-soft);\n}\n\n.competitor-details {\n  color: var(--primary-soft);\n  font-size: 0.9rem;\n}\n\n.competitor-details .address {\n  margin-bottom: 0.25rem;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.competitor-details .distance {\n  font-weight: 500;\n  color: var(--primary);\n}\n\n.website-link {\n  display: inline-flex;\n  align-items: center;\n  margin-top: 8px;\n  color: var(--primary);\n  text-decoration: none;\n  font-size: 13px;\n}\n\n.website-link svg {\n  margin-right: 4px;\n  width: 14px;\n  height: 14px;\n}\n\n/* Search modal */\n.competitor-search-modal {\n  width: 100%;\n}\n\n.search-container {\n  display: flex;\n  flex-direction: column;\n  gap: 1rem;\n}\n\n.map-container {\n  margin-bottom: 1rem;\n  border-radius: 0.5rem;\n  overflow: hidden;\n}\n\n.search-controls {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n\n.search-results h3 {\n  margin-top: 0;\n  margin-bottom: 1rem;\n  font-size: 1rem;\n  font-weight: 500;\n  color: #555;\n}\n\n.results-list {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\n  gap: 1rem;\n  max-height: 300px;\n  overflow-y: auto;\n  padding-right: 0.5rem;\n}\n\n.result-item {\n  background-color: white;\n  border-radius: 8px;\n  border: 1px solid #eee;\n  padding: 1rem;\n  cursor: pointer;\n  position: relative;\n  transition: all 0.2s ease;\n}\n\n.result-item h4 {\n  margin: 0 0 0.5rem 0;\n  font-size: 1rem;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.result-item p {\n  margin: 0 0 0.25rem 0;\n  font-size: 0.9rem;\n  color: #555;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n}\n\n.result-item .distance {\n  color: #777;\n  font-size: 0.85rem;\n}\n\n.result-item:hover {\n  border-color: #ccc;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n}\n\n.result-item.selected {\n  background-color: #e8f4ff;\n  border-color: #4a90e2;\n}\n\n.result-item.selected:hover {\n  background-color: #d8edff;\n}\n\n.result-item.already-added {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.already-added-badge {\n  position: absolute;\n  top: 0.5rem;\n  right: 0.5rem;\n  background-color: #4caf50;\n  color: white;\n  font-size: 0.7rem;\n  padding: 0.2rem 0.5rem;\n  border-radius: 4px;\n}\n\n/* For text truncation in sync modal */\n.truncate {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* Mobile responsiveness */\n@media (max-width: 768px) {\n  .search-controls {\n    flex-direction: column;\n    gap: 8px;\n  }\n  \n  .competitors-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .results-list {\n    grid-template-columns: 1fr;\n  }\n}\n\n/* Form styles for manual entry */\n.manual-entry-container {\n  padding: 1rem 0;\n}\n\n.form-row {\n  margin-bottom: 1rem;\n}\n\n.form-row.three-column {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr;\n  gap: 1rem;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: 1rem;\n  margin-top: 1.5rem;\n}\n\n/* Search tab styles */\n.search-input-container {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n  align-items: flex-end;\n}\n\n.search-actions {\n  display: flex;\n  justify-content: flex-end;\n  margin-top: 1rem;\n}\n\n.no-results {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  padding: 2rem;\n  text-align: center;\n}\n\n.search-instructions {\n  text-align: center;\n  padding: 1rem;\n  color: #666;\n}\n\n/* Website link in competitor card */\n.website-link {\n  display: inline-flex;\n  align-items: center;\n  text-decoration: none;\n  color: #4a90e2;\n  font-size: 0.85rem;\n  margin-top: 0.5rem;\n}\n\n.website-link svg {\n  margin-right: 0.25rem;\n  width: 14px;\n  height: 14px;\n}\n\n/* Tab styling */\n.tab-container {\n  display: flex;\n  margin-bottom: 1rem;\n  border-bottom: 1px solid var(--divider);\n}\n\n.tab-button {\n  flex: 1;\n  padding: 0.75rem 1rem;\n  text-align: center;\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-weight: 500;\n  color: var(--primary-soft);\n  border-bottom: 2px solid transparent;\n  transition: all 0.2s ease;\n}\n\n.tab-button.active {\n  color: var(--primary);\n  border-bottom-color: var(--primary-accent);\n}\n\n/* Mobile responsiveness for the new components */\n@media (max-width: 768px) {\n  .form-row.three-column {\n    grid-template-columns: 1fr;\n    gap: 0.5rem;\n  }\n  \n  .search-input-container {\n    flex-direction: column;\n    align-items: stretch;\n  }\n}\n\n/* Database result specific styles */\n.result-item.database-result {\n  border-left: 3px solid #3EDC81;\n}\n\n.product-count {\n  margin-top: 4px;\n  display: flex;\n  justify-content: flex-end;\n}\n\n/* Market analysis styles */\n.market-analysis-container {\n  padding: 1rem;\n  background-color: var(--surface);\n  border: 1px solid #3EDC81;\n  border-radius: 0.5rem;\n  margin-bottom: 1rem;\n}\n\n.market-analysis-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 1rem;\n}\n\n.market-analysis-icon {\n  width: 2rem;\n  height: 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  background-color: rgba(62, 220, 129, 0.1);\n  color: #3EDC81;\n  margin-right: 0.75rem;\n}\n\n.market-analysis-title {\n  margin: 0;\n  font-size: 1.1rem;\n  font-weight: 500;\n}\n\n.market-analysis-subtitle {\n  margin: 0;\n  font-size: 0.85rem;\n  color: var(--primary-soft);\n}\n\n.market-analysis-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n\n.market-analysis-card {\n  background-color: var(--surface-secondary);\n  border-radius: 0.5rem;\n  padding: 1rem;\n}\n\n.market-analysis-card-title {\n  margin: 0 0 0.5rem 0;\n  font-size: 0.9rem;\n  font-weight: 500;\n  color: #3EDC81;\n}\n\n.market-analysis-card-content {\n  font-size: 0.85rem;\n}\n\n.price-comparison {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 0.25rem;\n}\n\n.price-label {\n  color: var(--primary-soft);\n}\n\n.price-value {\n  font-weight: 500;\n}\n\n.price-value .change-positive {\n  color: var(--danger);\n}\n\n.price-value .change-negative {\n  color: var(--success);\n}\n\n.insights-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n\n.insights-list li {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 0.5rem;\n}\n\n.insights-list li::before {\n  content: \"✓\";\n  color: #3EDC81;\n  margin-right: 0.5rem;\n}\n\n.market-analysis-footer {\n  text-align: center;\n  font-size: 0.85rem;\n  color: var(--primary-soft);\n  font-style: italic;\n} ", ".orders-container {\n  padding: 20px;\n  max-width: 100%;\n}\n\n.orders-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.orders-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin: 0;\n}\n\n.orders-actions {\n  display: flex;\n  gap: 10px;\n}\n\n.orders-filters {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20px;\n  gap: 16px;\n  flex-wrap: wrap;\n}\n\n.search-form {\n  display: flex;\n  gap: 8px;\n  flex: 1;\n  min-width: 300px;\n}\n\n.status-filter {\n  min-width: 200px;\n  padding: 8px 12px;\n  border: 1px solid var(--border-color);\n  border-radius: 4px;\n  background-color: var(--input-bg);\n  color: var(--text-color);\n}\n\n.status-select {\n  width: 100%;\n  padding: 8px 12px;\n  margin-top: 8px;\n  border: 1px solid var(--border-color);\n  border-radius: 4px;\n  background-color: var(--input-bg);\n  color: var(--text-color);\n}\n\n.orders-table-container {\n  background-color: var(--card-background);\n  border-radius: 8px;\n  box-shadow: var(--shadow-sm);\n  overflow: hidden;\n}\n\n.orders-table {\n  width: 100%;\n  border-collapse: separate;\n  border-spacing: 0;\n}\n\n.orders-table th {\n  background-color: var(--secondary-bg);\n  padding: 12px 16px;\n  text-align: left;\n  font-weight: 600;\n  color: var(--text-secondary);\n  border-bottom: 1px solid var(--border-color);\n}\n\n.orders-table td {\n  padding: 12px 16px;\n  border-bottom: 1px solid var(--border-color);\n}\n\n.orders-table tr:last-child td {\n  border-bottom: none;\n}\n\n.orders-table tr:hover td {\n  background-color: var(--hover-bg);\n}\n\n.order-status {\n  display: inline-flex;\n  align-items: center;\n  padding: 4px 12px;\n  border-radius: 9999px;\n  font-size: 0.875rem;\n  font-weight: 500;\n}\n\n.order-status.pending {\n  background-color: #fef3c7;\n  color: #92400e;\n}\n\n.order-status.processing {\n  background-color: #dbeafe;\n  color: #1e40af;\n}\n\n.order-status.completed {\n  background-color: #d1fae5;\n  color: #065f46;\n}\n\n.order-status.cancelled {\n  background-color: #fee2e2;\n  color: #b91c1c;\n}\n\n.order-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.pagination {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 20px;\n  padding: 10px 16px;\n  background-color: var(--card-background);\n  border-radius: 8px;\n  box-shadow: var(--shadow-sm);\n}\n\n.pagination-info {\n  color: var(--text-secondary);\n}\n\n.pagination-controls {\n  display: flex;\n  gap: 8px;\n}\n\n/* Order detail page styling */\n.order-detail-container {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 24px;\n}\n\n.order-detail-section {\n  margin-bottom: 24px;\n  border: 1px solid var(--border-color);\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.section-header {\n  padding: 16px;\n  background-color: var(--section-header-bg);\n  border-bottom: 1px solid var(--border-color);\n}\n\n.section-header h3 {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.section-content {\n  padding: 16px;\n}\n\n.order-status-timeline {\n  display: flex;\n  flex-direction: column;\n  margin-top: 16px;\n  gap: 8px;\n}\n\n.timeline-item {\n  display: flex;\n  gap: 16px;\n  padding: 8px 0;\n}\n\n.timeline-icon {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 24px;\n  height: 24px;\n  border-radius: 9999px;\n  background-color: #e5e7eb;\n  color: #4b5563;\n}\n\n.timeline-icon.active {\n  background-color: #3b82f6;\n  color: white;\n}\n\n.order-items-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.order-items-table th {\n  text-align: left;\n  padding: 12px 16px;\n  background-color: var(--section-header-bg);\n  border-bottom: 1px solid var(--border-color);\n}\n\n.order-items-table td {\n  padding: 12px 16px;\n  border-bottom: 1px solid var(--border-color);\n}\n\n.order-items-table tr:last-child td {\n  border-bottom: none;\n}\n\n.order-summary {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  margin-top: 16px;\n}\n\n.summary-row {\n  display: flex;\n  justify-content: space-between;\n}\n\n.summary-total {\n  font-weight: 600;\n  margin-top: 8px;\n  padding-top: 8px;\n  border-top: 1px solid var(--border-color);\n}\n\n.address-info {\n  margin-top: 16px;\n}\n\n.info-grid {\n  display: grid;\n  grid-template-columns: auto 1fr;\n  gap: 8px 16px;\n  margin-top: 16px;\n}\n\n.info-label {\n  font-weight: 500;\n  color: var(--text-secondary);\n}\n\n.info-value {\n  color: var(--text-color);\n}\n\n.status-actions {\n  display: flex;\n  gap: 8px;\n  margin-top: 16px;\n}\n\n/* Responsive styling */\n@media (max-width: 768px) {\n  .order-detail-container {\n    grid-template-columns: 1fr;\n  }\n  \n  .orders-filters {\n    flex-direction: column;\n  }\n  \n  .search-form {\n    width: 100%;\n  }\n  \n  .orders-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n  }\n  \n  .orders-actions {\n    width: 100%;\n  }\n  \n  .orders-table-container {\n    overflow-x: auto;\n  }\n} ", ".ui-toast-wrapper { }\n\n.ui-toast-wrapper.active {\n    z-index: 9999;\n}\n\n.ui-toast-wrapper.active > * {\n    pointer-events: auto;\n}\n\n.ui-toast-bar {\n    display: flex;\n    align-items: center;\n    background: var(--color-background);\n    color: var(--color-text);\n    line-height: 1.3;\n    will-change: transform;\n    box-shadow: 0 3px 10px var(--color-shadow), 0 3px 3px var(--color-shadow-soft);\n    max-width: 350px;\n    pointer-events: auto;\n    padding: 8px 10px;\n    border-radius: 8px;\n}\n\n.ui-toast-bar .toast-message {\n    display: flex;\n    justify-content: center;\n    margin: 4px 10px;\n    color: inherit;\n    flex: 1 1 auto;\n    white-space: pre-line;\n}\n\n.ui-toast-bar .indicator-wrapper {\n    position: relative;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    min-width: 20px;\n    min-height: 20px;\n}\n\n.ui-toast-bar .status-wrapper {\n    position: absolute;\n}\n\n.ui-toast-bar .icon-wrapper {\n    position: relative;\n    transform: scale(0.6);\n    opacity: 0.4;\n    min-width: 20px;\n    animation: toastEnter 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n        forwards;\n}\n\n.ui-toast-bar .checkmark-icon {\n    width: 20px;\n    opacity: 0;\n    height: 20px;\n    border-radius: 10px;\n    background: #61d345;\n    position: relative;\n    transform: rotate(45deg);\n\n    animation: circleAnimation 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n        forwards;\n    animation-delay: 100ms;\n}\n\n.ui-toast-bar .checkmark-icon:after {\n    content: '';\n    box-sizing: border-box;\n    animation: checkmarkAnimation 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: #fff;\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n}\n\n.ui-toast-bar .error-icon {\n    width: 20px;\n    opacity: 0;\n    height: 20px;\n    border-radius: 10px;\n    background: #ff4b4b;\n    position: relative;\n    transform: rotate(45deg);\n\n    animation: circleAnimation 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n        forwards;\n    animation-delay: 100ms;\n}\n\n.ui-toast-bar .error-icon:after,\n.ui-toast-bar .error-icon:before {\n    content: '';\n    animation: firstLineAnimation 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: #fff;\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n}\n\n.ui-toast-bar .error-icon:before {\n    animation: secondLineAnimation 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n}\n\n.ui-toast-bar .loader-icon {\n    width: 12px;\n    height: 12px;\n    box-sizing: border-box;\n    border: 2px solid;\n    border-radius: 100%;\n    border-color: #e0e0e0;\n    border-right-color: #616161;\n    animation: rorate 1s linear infinite;\n}\n\n@keyframes toastBarEnter {\n    0% { transform: translate3d(0,-200%,0) scale(.6); opacity:.5; }\n    100% { transform: translate3d(0,0,0) scale(1); opacity:1; }\n}\n\n@keyframes toastBarExit {\n    0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n    100% {transform: translate3d(0,-150%,-1px) scale(.6); opacity:0;}\n}\n\n@keyframes toastEnter {\n    from {\n        transform: scale(0.6);\n        opacity: 0.4;\n    }\n    to {\n        transform: scale(1);\n        opacity: 1;\n    }\n}\n\n@keyframes rotate {\n    from {\n        transform: rotate(0deg);\n    }\n    to {\n        transform: rotate(360deg);\n    }\n}\n\n@keyframes circleAnimation {\n    from {\n        transform: scale(0) rotate(45deg);\n        opacity: 0;\n    }\n    to {\n        transform: scale(1) rotate(45deg);\n        opacity: 1;\n    }\n}\n\n@keyframes checkmarkAnimation {\n    0% {\n        height: 0;\n        width: 0;\n        opacity: 0;\n    }\n    40% {\n        height: 0;\n        width: 6px;\n        opacity: 1;\n    }\n    100% {\n        opacity: 1;\n        height: 10px;\n    }\n}\n\n@keyframes firstLineAnimation {\n    from {\n        transform: scale(0);\n        opacity: 0;\n    }\n    to {\n        transform: scale(1);\n        opacity: 1;\n    }\n}\n\n@keyframes secondLineAnimation {\n    from {\n        transform: scale(0) rotate(90deg);\n        opacity: 0;\n    }\n    to {\n        transform: scale(1) rotate(90deg);\n        opacity: 1;\n    }\n}", ".toast-title{font-weight:700}.toast-message{-ms-word-wrap:break-word;word-wrap:break-word}.toast-message a,.toast-message label{color:#FFF}.toast-message a:hover{color:#CCC;text-decoration:none}.toast-close-button{position:relative;right:-.3em;top:-.3em;float:right;font-size:20px;font-weight:700;color:#FFF;-webkit-text-shadow:0 1px 0 #fff;text-shadow:0 1px 0 #fff;opacity:.8;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=80);filter:alpha(opacity=80);line-height:1}.toast-close-button:focus,.toast-close-button:hover{color:#000;text-decoration:none;cursor:pointer;opacity:.4;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=40);filter:alpha(opacity=40)}.rtl .toast-close-button{left:-.3em;float:left;right:.3em}button.toast-close-button{padding:0;cursor:pointer;background:0 0;border:0;-webkit-appearance:none}.toast-top-center{top:0;right:0;width:100%}.toast-bottom-center{bottom:0;right:0;width:100%}.toast-top-full-width{top:0;right:0;width:100%}.toast-bottom-full-width{bottom:0;right:0;width:100%}.toast-top-left{top:12px;left:12px}.toast-top-right{top:12px;right:12px}.toast-bottom-right{right:12px;bottom:12px}.toast-bottom-left{bottom:12px;left:12px}#toast-container{position:fixed;z-index:999999;pointer-events:none}#toast-container *{-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box}#toast-container>div{position:relative;pointer-events:auto;overflow:hidden;margin:0 0 6px;padding:15px 15px 15px 50px;width:300px;-moz-border-radius:3px;-webkit-border-radius:3px;border-radius:3px;background-position:15px center;background-repeat:no-repeat;-moz-box-shadow:0 0 12px #999;-webkit-box-shadow:0 0 12px #999;box-shadow:0 0 12px #999;color:#FFF;opacity:.8;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=80);filter:alpha(opacity=80)}#toast-container>div.rtl{direction:rtl;padding:15px 50px 15px 15px;background-position:right 15px center}#toast-container>div:hover{-moz-box-shadow:0 0 12px #000;-webkit-box-shadow:0 0 12px #000;box-shadow:0 0 12px #000;opacity:1;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=100);filter:alpha(opacity=100);cursor:pointer}#toast-container>.toast-info{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVEhLtZa9SgNBEMc9sUxxRcoUKSzSWIhXpFMhhYWFhaBg4yPYiWCXZxBLERsLRS3EQkEfwCKdjWJAwSKCgoKCcudv4O5YLrt7EzgXhiU3/4+b2ckmwVjJSpKkQ6wAi4gwhT+z3wRBcEz0yjSseUTrcRyfsHsXmD0AmbHOC9Ii8VImnuXBPglHpQ5wwSVM7sNnTG7Za4JwDdCjxyAiH3nyA2mtaTJufiDZ5dCaqlItILh1NHatfN5skvjx9Z38m69CgzuXmZgVrPIGE763Jx9qKsRozWYw6xOHdER+nn2KkO+Bb+UV5CBN6WC6QtBgbRVozrahAbmm6HtUsgtPC19tFdxXZYBOfkbmFJ1VaHA1VAHjd0pp70oTZzvR+EVrx2Ygfdsq6eu55BHYR8hlcki+n+kERUFG8BrA0BwjeAv2M8WLQBtcy+SD6fNsmnB3AlBLrgTtVW1c2QN4bVWLATaIS60J2Du5y1TiJgjSBvFVZgTmwCU+dAZFoPxGEEs8nyHC9Bwe2GvEJv2WXZb0vjdyFT4Cxk3e/kIqlOGoVLwwPevpYHT+00T+hWwXDf4AJAOUqWcDhbwAAAAASUVORK5CYII=)!important}#toast-container>.toast-error{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAHOSURBVEhLrZa/SgNBEMZzh0WKCClSCKaIYOED+AAKeQQLG8HWztLCImBrYadgIdY+gIKNYkBFSwu7CAoqCgkkoGBI/E28PdbLZmeDLgzZzcx83/zZ2SSXC1j9fr+I1Hq93g2yxH4iwM1vkoBWAdxCmpzTxfkN2RcyZNaHFIkSo10+8kgxkXIURV5HGxTmFuc75B2RfQkpxHG8aAgaAFa0tAHqYFfQ7Iwe2yhODk8+J4C7yAoRTWI3w/4klGRgR4lO7Rpn9+gvMyWp+uxFh8+H+ARlgN1nJuJuQAYvNkEnwGFck18Er4q3egEc/oO+mhLdKgRyhdNFiacC0rlOCbhNVz4H9FnAYgDBvU3QIioZlJFLJtsoHYRDfiZoUyIxqCtRpVlANq0EU4dApjrtgezPFad5S19Wgjkc0hNVnuF4HjVA6C7QrSIbylB+oZe3aHgBsqlNqKYH48jXyJKMuAbiyVJ8KzaB3eRc0pg9VwQ4niFryI68qiOi3AbjwdsfnAtk0bCjTLJKr6mrD9g8iq/S/B81hguOMlQTnVyG40wAcjnmgsCNESDrjme7wfftP4P7SP4N3CJZdvzoNyGq2c/HWOXJGsvVg+RA/k2MC/wN6I2YA2Pt8GkAAAAASUVORK5CYII=)!important}#toast-container>.toast-success{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADsSURBVEhLY2AYBfQMgf///3P8+/evAIgvA/FsIF+BavYDDWMBGroaSMMBiE8VC7AZDrIFaMFnii3AZTjUgsUUWUDA8OdAH6iQbQEhw4HyGsPEcKBXBIC4ARhex4G4BsjmweU1soIFaGg/WtoFZRIZdEvIMhxkCCjXIVsATV6gFGACs4Rsw0EGgIIH3QJYJgHSARQZDrWAB+jawzgs+Q2UO49D7jnRSRGoEFRILcdmEMWGI0cm0JJ2QpYA1RDvcmzJEWhABhD/pqrL0S0CWuABKgnRki9lLseS7g2AlqwHWQSKH4oKLrILpRGhEQCw2LiRUIa4lwAAAABJRU5ErkJggg==)!important}#toast-container>.toast-warning{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGYSURBVEhL5ZSvTsNQFMbXZGICMYGYmJhAQIJAICYQPAACiSDB8AiICQQJT4CqQEwgJvYASAQCiZiYmJhAIBATCARJy+9rTsldd8sKu1M0+dLb057v6/lbq/2rK0mS/TRNj9cWNAKPYIJII7gIxCcQ51cvqID+GIEX8ASG4B1bK5gIZFeQfoJdEXOfgX4QAQg7kH2A65yQ87lyxb27sggkAzAuFhbbg1K2kgCkB1bVwyIR9m2L7PRPIhDUIXgGtyKw575yz3lTNs6X4JXnjV+LKM/m3MydnTbtOKIjtz6VhCBq4vSm3ncdrD2lk0VgUXSVKjVDJXJzijW1RQdsU7F77He8u68koNZTz8Oz5yGa6J3H3lZ0xYgXBK2QymlWWA+RWnYhskLBv2vmE+hBMCtbA7KX5drWyRT/2JsqZ2IvfB9Y4bWDNMFbJRFmC9E74SoS0CqulwjkC0+5bpcV1CZ8NMej4pjy0U+doDQsGyo1hzVJttIjhQ7GnBtRFN1UarUlH8F3xict+HY07rEzoUGPlWcjRFRr4/gChZgc3ZL2d8oAAAAASUVORK5CYII=)!important}#toast-container.toast-bottom-center>div,#toast-container.toast-top-center>div{width:300px;margin-left:auto;margin-right:auto}#toast-container.toast-bottom-full-width>div,#toast-container.toast-top-full-width>div{width:96%;margin-left:auto;margin-right:auto}.toast{background-color:#030303}.toast-success{background-color:#51A351}.toast-error{background-color:#BD362F}.toast-info{background-color:#2F96B4}.toast-warning{background-color:#F89406}.toast-progress{position:absolute;left:0;bottom:0;height:4px;background-color:#000;opacity:.4;-ms-filter:progid:DXImageTransform.Microsoft.Alpha(Opacity=40);filter:alpha(opacity=40)}@media all and (max-width:240px){#toast-container>div{padding:8px 8px 8px 50px;width:11em}#toast-container>div.rtl{padding:8px 50px 8px 8px}#toast-container .toast-close-button{right:-.2em;top:-.2em}#toast-container .rtl .toast-close-button{left:-.2em;right:.2em}}@media all and (min-width:241px) and (max-width:480px){#toast-container>div{padding:8px 8px 8px 50px;width:18em}#toast-container>div.rtl{padding:8px 50px 8px 8px}#toast-container .toast-close-button{right:-.2em;top:-.2em}#toast-container .rtl .toast-close-button{left:-.2em;right:.2em}}@media all and (min-width:481px) and (max-width:768px){#toast-container>div{padding:15px 15px 15px 50px;width:25em}#toast-container>div.rtl{padding:15px 50px 15px 15px}}", "/* inter-cyrillic-ext-400-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url('./files/inter-cyrillic-ext-400-normal.woff2') format('woff2'), url('./files/inter-all-400-normal.woff') format('woff');\n  unicode-range: U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;\n}\n/* inter-cyrillic-400-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url('./files/inter-cyrillic-400-normal.woff2') format('woff2'), url('./files/inter-all-400-normal.woff') format('woff');\n  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;\n}\n/* inter-greek-ext-400-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url('./files/inter-greek-ext-400-normal.woff2') format('woff2'), url('./files/inter-all-400-normal.woff') format('woff');\n  unicode-range: U+1F00-1FFF;\n}\n/* inter-greek-400-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url('./files/inter-greek-400-normal.woff2') format('woff2'), url('./files/inter-all-400-normal.woff') format('woff');\n  unicode-range: U+0370-03FF;\n}\n/* inter-vietnamese-400-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url('./files/inter-vietnamese-400-normal.woff2') format('woff2'), url('./files/inter-all-400-normal.woff') format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB;\n}\n/* inter-latin-ext-400-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url('./files/inter-latin-ext-400-normal.woff2') format('woff2'), url('./files/inter-all-400-normal.woff') format('woff');\n  unicode-range: U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n/* inter-latin-400-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 400;\n  src: url('./files/inter-latin-400-normal.woff2') format('woff2'), url('./files/inter-all-400-normal.woff') format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}\n", "/* inter-cyrillic-ext-500-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url('./files/inter-cyrillic-ext-500-normal.woff2') format('woff2'), url('./files/inter-all-500-normal.woff') format('woff');\n  unicode-range: U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;\n}\n/* inter-cyrillic-500-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url('./files/inter-cyrillic-500-normal.woff2') format('woff2'), url('./files/inter-all-500-normal.woff') format('woff');\n  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;\n}\n/* inter-greek-ext-500-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url('./files/inter-greek-ext-500-normal.woff2') format('woff2'), url('./files/inter-all-500-normal.woff') format('woff');\n  unicode-range: U+1F00-1FFF;\n}\n/* inter-greek-500-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url('./files/inter-greek-500-normal.woff2') format('woff2'), url('./files/inter-all-500-normal.woff') format('woff');\n  unicode-range: U+0370-03FF;\n}\n/* inter-vietnamese-500-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url('./files/inter-vietnamese-500-normal.woff2') format('woff2'), url('./files/inter-all-500-normal.woff') format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB;\n}\n/* inter-latin-ext-500-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url('./files/inter-latin-ext-500-normal.woff2') format('woff2'), url('./files/inter-all-500-normal.woff') format('woff');\n  unicode-range: U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n/* inter-latin-500-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 500;\n  src: url('./files/inter-latin-500-normal.woff2') format('woff2'), url('./files/inter-all-500-normal.woff') format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}\n", "/* inter-cyrillic-ext-700-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url('./files/inter-cyrillic-ext-700-normal.woff2') format('woff2'), url('./files/inter-all-700-normal.woff') format('woff');\n  unicode-range: U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;\n}\n/* inter-cyrillic-700-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url('./files/inter-cyrillic-700-normal.woff2') format('woff2'), url('./files/inter-all-700-normal.woff') format('woff');\n  unicode-range: U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;\n}\n/* inter-greek-ext-700-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url('./files/inter-greek-ext-700-normal.woff2') format('woff2'), url('./files/inter-all-700-normal.woff') format('woff');\n  unicode-range: U+1F00-1FFF;\n}\n/* inter-greek-700-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url('./files/inter-greek-700-normal.woff2') format('woff2'), url('./files/inter-all-700-normal.woff') format('woff');\n  unicode-range: U+0370-03FF;\n}\n/* inter-vietnamese-700-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url('./files/inter-vietnamese-700-normal.woff2') format('woff2'), url('./files/inter-all-700-normal.woff') format('woff');\n  unicode-range: U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+1EA0-1EF9,U+20AB;\n}\n/* inter-latin-ext-700-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url('./files/inter-latin-ext-700-normal.woff2') format('woff2'), url('./files/inter-all-700-normal.woff') format('woff');\n  unicode-range: U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;\n}\n/* inter-latin-700-normal*/\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-display: swap;\n  font-weight: 700;\n  src: url('./files/inter-latin-700-normal.woff2') format('woff2'), url('./files/inter-all-700-normal.woff') format('woff');\n  unicode-range: U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;\n}\n", ":root {\n    --color-black: #151c2d;\n    --color-white: #ffffff;\n\n    /* Brand guide colors */\n    --color-dark-jungle-green: #0D211D;\n    --color-brunswick-green: #23504A;\n    --color-pine-green: #00766D;\n    --color-emerald-green: #22AD85;\n    --color-honeydew: #DFF4E9;\n\n    --color-primary: var(--color-black);\n    --color-primary-soft: #6b707b;\n\n    --color-grey: #eae8e8;\n    --color-grey-soft: #F5F5F7;\n    --color-grey-hard: #cecfd2;\n\n    --color-background: var(--color-white);\n    --color-background-soft: #F9FAFB;\n    --color-on-background: var(--color-black);\n\n    --color-on-primary: var(--color-white);\n    --color-on-background: var(--color-black);\n    \n    /* Surface & divider colors for light mode */\n    --color-surface: #ffffff;\n    --color-surface-secondary: #F9FAFB;\n    --color-divider: #eae8e8;\n\n    --color-red: #D92D20;\n    --color-red-soft: #FECDCA;\n    --color-red-hard: #B42419;\n    \n    --color-blue: #2970FF;\n    --color-blue-soft: #D1E0FF;\n    --color-blue-hard: #004EBB;\n\n    --color-yellow: #FEC84B;\n    --color-yellow-soft: #FEF0C7;\n    --color-yellow-hard: #F79009;\n    \n    --color-green: #32D583;\n    --color-green-soft: #D1FADF;\n    --color-green-hard: #039855;\n\n    --color-shadow: rgba(0, 0, 0, 0.1);\n    --color-shadow-soft: rgba(0, 0, 0, 0.05);\n\n    --color-editor: #0d121e;\n\n    --color-border: var(--color-grey-hard);\n    --color-checkbox-checked-bg: #151c2d;\n\n    --border-radius: 8px;\n    --border-radius-inner: 6px;\n    --border-radius-outer: 14px;\n}\n\n[data-theme=\"dark\"] {\n    --color-black: #121721;\n\n    /* Brand guide colors for dark mode - slightly adjusted for better visibility */\n    --color-dark-jungle-green: #132E29;\n    --color-brunswick-green: #2A5C56;\n    --color-pine-green: #0B8379;\n    --color-emerald-green: #29BE96;\n    --color-honeydew: #E5F7EF;\n\n    --color-grey: #2b3245;\n    --color-grey-soft: #252b3a;\n\n    --color-primary: var(--color-white);\n    --color-primary-soft: #919496;\n\n    --color-background: var(--color-black);\n    --color-background-soft: #1a1f2b;\n\n    --color-on-primary: var(--color-black);\n    --color-on-background: var(--color-white);\n\n    --color-shadow: var(--color-grey);\n    --color-shadow-soft: var(--color-grey-soft);\n    \n    /* Dark mode color variants */\n    --color-red: #FF6B6B;\n    --color-red-soft: rgba(255, 107, 107, 0.2);\n    --color-red-hard: #FF3333;\n    \n    --color-blue: #5C9DFF;\n    --color-blue-soft: rgba(92, 157, 255, 0.2);\n    --color-blue-hard: #2E7BFF;\n\n    --color-yellow: #FFCC29;\n    --color-yellow-soft: rgba(255, 204, 41, 0.2);\n    --color-yellow-hard: #FFBB00;\n    \n    --color-green: #4ADE80;\n    --color-green-soft: rgba(74, 222, 128, 0.2);\n    --color-green-hard: #22C55E;\n    \n    /* Surface & divider colors for dark mode */\n    --color-surface: #1e2538;\n    --color-surface-secondary: #2a334a;\n    --color-divider: #323d5d;\n\n    --color-border: var(--color-divider);\n    --color-checkbox-checked-bg: #151c2d;\n}\n", "@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* Apply 85% zoom to entire application */\nhtml {\n  /* transform: scale(0.85);\n  transform-origin: 0 0; \n  width: 117.65%; /* 100% / 0.85 to compensate for the scale *\n  height: 11.65%; */\n  overflow-x: hidden;\n  height: 100vh;\n  /* margin: 0; */\n\n}\n\n@keyframes overlayShow {\n    from {\n      opacity: 0;\n    }\n    to {\n      opacity: 1;\n    }\n  }\n  \n  @layer utilities {\n    .animate-overlay-show {\n      animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);\n    }\n  }\n  \n\nbody {\n    font-family: 'Inter', 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n    font-size: 15px;\n    background: var(--color-background);\n    color: var(--color-primary);\n    margin: 0;\n    padding: 0;\n    /* overflow: hidden; Prevent scrolling at body level */\n    /* position: relative; */\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n}\n\n* { box-sizing: border-box; }\n\na {\n    color: inherit;\n    text-decoration: none;\n}\n\na:hover {\n    text-decoration: underline;\n}\n\n.rbc-off-range-bg {\n    background-color: var(--color-primary);\n    opacity: 0.5;\n}\n.rbc-off-range button{\n    color: var(--color-primary);\n}\n\n.page-content {\n    padding: 0 40px 40px;\n}\n\n.page-content.no-horizontal-padding {\n    padding-left: 0;\n    padding-right: 0;\n}\n\n.page-content.fullscreen {\n    min-height: 100vh;\n    display: flex;\n    flex-direction: column;\n}\n\n.page-content .page-banner {\n    padding-top: 20px;\n}\n\n.container {\n    border: 1px solid var(--color-grey);\n    border-radius: var(--border-radius-outer);\n    padding: 20px;\n}\n\nh1 {\n    font-style: normal;\n    font-weight: 700;\n    font-size: 36px;\n    line-height: 44px;\n    margin: 20px 0;\n}\n\nh2 {\n    font-style: normal;\n    font-weight: 700;\n    font-size: 30px;\n    line-height: 42px;\n    margin: 20px 0;\n}\n\nh3 {\n    font-size: 22px;\n    line-height: 24px;\n}\n\nh4 {\n    font-size: 18px;\n    line-height: 22px;\n}\n\nh5 {\n    font-size: 14px;\n    line-height: 18px;\n    margin: 5px 0;\n}\n\ninput, textarea {\n    position: relative;\n    background: var(--color-background);\n    color: var(--color-primary);\n    font-family: 'Inter', 'Helvetica Neue', sans-serif;\n    font-size: 16px;\n    border: 1px solid var(--color-grey);\n    border-radius: var(--border-radius);\n    padding: 12px 15px;\n    width: 100%;\n    box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);\n    outline-color: var(--color-blue);\n    transition: border-color 100ms;\n}\n\n::placeholder {\n    color: var(--color-primary-soft);\n}\n\ninput.small {\n    padding: 5px 7px;\n    font-size: 14px;\n    line-height: 20px;\n    width: auto;\n}\n\ntextarea {\n    min-height: 100px;\n}\n\ninput:hover, textarea:hover {\n    border-color: var(--color-grey-hard);\n}\n\nform .form-submit {\n    margin-top: 20px;\n    margin-bottom: 0;\n    display: inline-block;\n}\n\nlabel {\n    display: block;\n    margin: 10px 0 5px;\n}\n\nform .label {\n    display: block;\n    margin: 5px 0;\n}\n\nlabel > span {\n    margin-bottom: 3px;\n    display: block;\n    font-weight: 500;\n}\n\nlabel.invalid input {\n    border-color: var(--color-red);\n    box-shadow: 0px 1px 2px var(--color-red-light);\n}\n\nlabel.invalid span {\n    color: var(--color-red);\n}\n\nlabel.hide-label {\n    margin: 0;\n}\n\n.label-subtitle {\n    color: var(--color-primary-soft);\n    font-weight: 400;\n    font-size: 14px;\n}\n\nlabel .switch {\n    display: inline-block;\n    height: 26px;\n    position: relative;\n    width: 46px;\n}\n\nlabel .switch input {\n    display: none;\n}\n\nlabel .switch .slider {\n    background-color: var(--color-grey);\n    bottom: 0;\n    cursor: pointer;\n    left: 0;\n    position: absolute;\n    right: 0;\n    top: 0;\n    transition: .4s;\n}\n\nlabel .switch .slider:before {\n    background-color: var(--color-background);\n    bottom: 4px;\n    content: \"\";\n    height: 18px;\n    width: 18px;\n    left: 4px;\n    position: absolute;\n    transition: .4s;\n}\n\nlabel .switch input:checked + .slider {\n    background-color: var(--color-green);\n}\n\nlabel .switch input:checked + .slider:before {\n    transform: translateX(20px);\n}\n\nlabel .switch .slider.round {\n    border-radius: 26px;\n}\n\nlabel .switch .slider.round:before {\n    border-radius: 50%;\n}\n\nfieldset {\n    border: 1px solid var(--color-grey-soft);\n    border-radius: var(--border-radius);\n    padding: 0 10px 10px;\n    margin: 15px 0;\n    min-width: 0;\n}\n\n.icon {\n    width: 16px;\n    height: 16px;\n}\n\n.icon-box {\n    align-self: start;\n    border-radius: 4px;\n    width: 36px;\n    height: 36px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\ncode {\n    word-wrap: break-word;\n    background-color: var(--color-grey-soft);\n    border-radius: 2px;\n    padding: 2px;\n}\n\n.blue {\n    background-color: var(--color-blue-soft);\n    color: var(--color-blue-hard);\n}\n\n.green {\n    background-color: var(--color-green-soft);\n    color: var(--color-green-hard);\n}\n\n.red {\n    background-color: var(--color-red-soft);\n    color: var(--color-red-hard);\n}\n\n.yellow {\n    background-color: var(--color-yellow-soft);\n    color: var(--color-yellow-hard);\n}\n\n/* Custom class for requirement tags */\n.requirement-tag {\n    display: inline-flex;\n    align-items: center;\n    border-radius: 0.25rem;\n    padding: 0.125rem 0.5rem;\n    font-size: 0.75rem;\n    font-weight: 500;\n}\n\n.requirement-tag.completed {\n    background-color: var(--color-green-soft);\n    color: var(--color-green-hard);\n}\n\n.requirement-tag.pending {\n    background-color: var(--color-surface-secondary);\n    color: var(--color-primary-soft);\n}\n\n@media only screen and (max-width: 600px) {\n    .page-content {\n        padding: 0 20px 20px;\n    }\n}\n\n:root {\n  /* Existing theme variables */\n  --primary-color: #f37335;\n  --primary-color-rgb: 243, 115, 53; /* RGB values for the primary color */\n  --primary-color-hover: #e56224;\n  --secondary-color: #fdc830;\n  --background-primary: #fff;\n  --background-secondary: #f8f9fa;\n  --text-primary: #212529;\n  --text-secondary: #6c757d;\n  --border-color: #dee2e6;\n  --danger-color: #dc3545;\n  --danger-color-rgb: 220, 53, 69; /* RGB values for the danger color */\n  --danger-color-hover: #c82333;\n  --success-color: #28a745;\n  --warning-color: #ffc107;\n  --info-color: #17a2b8;\n  --hover-color: rgba(0, 0, 0, 0.05);\n  \n  /* Layout specific */\n  --header-height: 60px;\n  --sidebar-width: 240px;\n  --sidebar-collapsed-width: 60px;\n  --minichat-width: 350px;\n  \n  /* Spacing */\n  --space-xs: 0.25rem;\n  --space-sm: 0.5rem;\n  --space-md: 1rem;\n  --space-lg: 1.5rem;\n  --space-xl: 2rem;\n  \n  /* Shadows */\n  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);\n  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);\n  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);\n  \n  /* Animations */\n  --transition-fast: 0.2s;\n  --transition-normal: 0.3s;\n  --transition-slow: 0.5s;\n  \n  /* Z-index layers */\n  --z-header: 100;\n  --z-sidebar: 90;\n  --z-minichat: 80;\n  --z-modal: 1000;\n  --z-toast: 1100;\n}\n\n/* Dark mode variables (if needed) */\n@media (prefers-color-scheme: dark) {\n  :root {\n    --background-primary: #121212;\n    --background-secondary: #1e1e1e;\n    --text-primary: #e0e0e0;\n    --text-secondary: #a0a0a0;\n    --border-color: #333333;\n    --hover-color: rgba(255, 255, 255, 0.05);\n  }\n}\n\n/* Global styles */\n\n/* Animations */\n@keyframes slideInRight {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n.animate-slideInRight {\n  animation: slideInRight 0.5s ease-out forwards;\n}"], "names": [], "sourceRoot": ""}