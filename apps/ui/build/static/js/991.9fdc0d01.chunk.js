"use strict";(self.webpackChunk_bakedBot_ui=self.webpackChunk_bakedBot_ui||[]).push([[991],{19991:(e,t,n)=>{n.r(t),n.d(t,{default:()=>c});n(49884);var s=n(90722),o=n(55897),l=n.n(o),a=n(65043),r=n(56188),u=n(70579);function i(e){let{id:t,mjml:n,onChange:o,setAssetState:r,fonts:i=[]}=e;const[c,d]=(0,a.useState)(void 0),[m,f]=(0,a.useState)(!1),p=(0,a.useCallback)(((e,t)=>{const n=e.Canvas.getDocument();if(!n)return;((e,t)=>{const n=e.head.querySelectorAll(`[${t}]`);Array.from(n).forEach((e=>e.remove()))})(n,"data-silex-gstatic");let s="";for(const l of t)s+=`\n            <link href="${o=l.url}" rel="stylesheet" type="text/css" data-silex-font>\n            <style type="text/css" data-silex-font>\n                @import url(${o});\n            </style>\n        `;var o;n.head.insertAdjacentHTML("beforeend",s)}),[]),v=(0,a.useCallback)(((e,t)=>{const n=e.StyleManager,s=n.getProperty("typography","font-family"),o=[...(null===s||void 0===s?void 0:s.getOptions()).filter((e=>!e.custom)),...t.map((e=>({id:e.value,label:e.name,custom:!0})))];null===s||void 0===s||s.setOptions(o),n.render(),p(e,t)}),[p]);return(0,a.useEffect)((()=>{if(!c){const e=s.Ay.init({fromElement:!1,container:`#${t}`,storageManager:!1,autorender:!1,plugins:[l()],pluginsOpts:{[l()]:{fonts:i.reduce(((e,t)=>{let{name:n,url:s}=t;return{...e,[n]:s}}),{})}},height:"100%",assetManager:{custom:{open(e){r({...e,event:"open"})},close(e){r({...e,event:"close"})}}}});d(e),e.on("load",(()=>{var t;null===(t=e.Panels.getButton("views","open-blocks"))||void 0===t||t.set("active",!0),f(!0)})),e.render(),e.setComponents(null!==n&&void 0!==n?n:"<mjml><mj-body></mj-body></mjml>"),e.on("update",(()=>{o(e.getHtml(),e.runCommand("mjml-code-to-html").html,e)}))}}),[c,i,t,n,o,r]),(0,a.useEffect)((()=>{c&&v(c,i)}),[m,i,c,v]),(0,u.jsx)("div",{id:t})}function c(e){let{template:t,setTemplate:n,resources:s}=e;const[o,l]=(0,a.useState)(!1),[c,d]=(0,a.useState)(),m=s.map((e=>e.value));function f(){l(!1),null===c||void 0===c||c.close()}return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(i,{id:`grapes-editor-${t.id}`,mjml:t.data.mjml,fonts:m,onChange:function(e,s){n({...t,data:{...t.data,mjml:e,html:s}})},setAssetState:function(e){l("open"===e.event),d(e)}}),(0,u.jsx)(r.A,{open:o,onClose:f,onInsert:function(e){null===c||void 0===c||c.select({src:e.url}),f()}})]})}}}]);
//# sourceMappingURL=991.9fdc0d01.chunk.js.map