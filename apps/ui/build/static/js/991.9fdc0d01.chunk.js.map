{"version": 3, "file": "static/js/991.9fdc0d01.chunk.js", "mappings": "uNAuBA,SAASA,EAAWC,GAME,IAND,GACnBC,EAAE,KACFC,EAAI,SACJC,EAAQ,cACRC,EAAa,MACbC,EAAQ,IACSL,EACjB,MAAOM,EAAQC,IAAaC,EAAAA,EAAAA,eAA6BC,IAClDC,EAAQC,IAAaH,EAAAA,EAAAA,WAAS,GAiB/BI,GAAaC,EAAAA,EAAAA,cAAY,CAACP,EAAgBD,KAC9C,MAAMS,EAAMR,EAAOS,OAAOC,cAC1B,IAAKF,EAAK,OAjBMG,EAACH,EAAeI,KAChC,MAAMC,EAAML,EAAIM,KAAKC,iBAAiB,IAAIH,MAC1CI,MAAMC,KAAKJ,GAAKK,SAASC,GAAOA,EAAGC,UAAS,EAgB5CT,CAAUH,EAJc,sBAKxB,IAAIa,EAAO,GACX,IAAK,MAAMC,KAAQvB,EACjBsB,GAfK,6BADYE,EAgBGD,EAAKC,4IAZDA,sCAJPA,MAkBnBf,EAAIM,KAAKU,mBAAmB,YAAaH,EAAK,GAC7C,IAEGI,GAAelB,EAAAA,EAAAA,cACnB,CAACP,EAAgBD,KACf,MAAM2B,EAAe1B,EAAO2B,aACtBC,EAAeF,EAAaG,YAChC,aACA,eAGIC,EAAa,KADS,OAAZF,QAAY,IAAZA,OAAY,EAAZA,EAAcG,cAEjBC,QAAQC,IAAiBA,EAAOC,YACxCnC,EAAMoC,KAAKb,IAAI,CAChB3B,GAAI2B,EAAKc,MACTC,MAAOf,EAAKgB,KACZJ,QAAQ,OAGA,OAAZN,QAAY,IAAZA,GAAAA,EAAcW,WAAWT,GACzBJ,EAAac,SAEblC,EAAWN,EAAQD,EAAM,GAE3B,CAACO,IAoDH,OAjDAmC,EAAAA,EAAAA,YAAU,KACR,IAAKzC,EAAQ,CACX,MAAMA,EAAS0C,EAAAA,GAASC,KAAK,CAC3BC,aAAa,EACbC,UAAW,IAAIlD,IACfmD,gBAAgB,EAChBC,YAAY,EACZC,QAAS,CAACC,KACVC,YAAa,CACX,CAACD,KAAsB,CACrBlD,MAAOA,EAAMoD,QACX,CAACC,EAAGC,KAAA,IAAE,KAAEf,EAAI,IAAEf,GAAK8B,EAAA,MAAM,IAAKD,EAAK,CAACd,GAAOf,EAAK,GAChD,CAAC,KAIP+B,OAAQ,OACRC,aAAc,CACZrB,OAAQ,CACNsB,IAAAA,CAAKC,GACH3D,EAAc,IAAK2D,EAAOC,MAAO,QACnC,EACAC,KAAAA,CAAMF,GACJ3D,EAAc,IAAK2D,EAAOC,MAAO,SACnC,MAINzD,EAAUD,GACVA,EAAO4D,GAAG,QAAQ,KAAO,IAADC,EACyB,QAA/CA,EAAA7D,EAAO8D,OAAOC,UAAU,QAAS,sBAAc,IAAAF,GAA/CA,EAAiDG,IAAI,UAAU,GAC/D3D,GAAU,EAAK,IAEjBL,EAAOwC,SACPxC,EAAOiE,cAAkB,OAAJrE,QAAI,IAAJA,EAAAA,EAAQ,oCAC7BI,EAAO4D,GAAG,UAAU,KAClB/D,EACEG,EAAOkE,UACPlE,EAAOmE,WAAW,qBAAqB9C,KACvCrB,EACD,GAEL,IACC,CAACA,EAAQD,EAAOJ,EAAIC,EAAMC,EAAUC,KAEvC2C,EAAAA,EAAAA,YAAU,KACJzC,GAAQyB,EAAazB,EAAQD,EAAM,GACtC,CAACK,EAAQL,EAAOC,EAAQyB,KAEpB2C,EAAAA,EAAAA,KAAA,OAAKzE,GAAIA,GAClB,CAQe,SAAS0E,EAAYC,GAIb,IAJc,SACnCC,EAAQ,YACRC,EAAW,UACXC,GACkBH,EAClB,MAAOI,EAAYC,IAAiBzE,EAAAA,EAAAA,WAAS,IACtCqD,EAAcqB,IAAmB1E,EAAAA,EAAAA,YAGlCH,EAAQ0E,EAAUtC,KAAK0C,GAAaA,EAASzC,QAUnD,SAAS0C,IACPH,GAAc,GACF,OAAZpB,QAAY,IAAZA,GAAAA,EAAcI,OAChB,CAOA,OACEoB,EAAAA,EAAAA,MAAAC,EAAAA,SAAA,CAAAC,SAAA,EACEb,EAAAA,EAAAA,KAAC3E,EAAW,CACVE,GAAI,iBAAiB4E,EAAS5E,KAC9BC,KAAM2E,EAASW,KAAKtF,KACpBG,MAAOA,EACPF,SAzBN,SAA2BD,EAAcyB,GACvCmD,EAAY,IAAKD,EAAUW,KAAM,IAAKX,EAASW,KAAMtF,OAAMyB,SAC7D,EAwBMvB,cAZN,SAA0BqF,GACxBR,EAA8B,SAAhBQ,EAAMzB,OACpBkB,EAAgBO,EAClB,KAWIf,EAAAA,EAAAA,KAACgB,EAAAA,EAAiB,CAChB5B,KAAMkB,EACNW,QAASP,EACTQ,SA3BN,SAA2BC,GACb,OAAZhC,QAAY,IAAZA,GAAAA,EAAciC,OAAO,CAAEC,IAAKF,EAAMhE,MAClCuD,GACF,MA4BF,C", "sources": ["views/campaign/editor/VisualEditor.tsx"], "sourcesContent": ["import \"grapesjs/dist/css/grapes.min.css\";\nimport \"./VisualEditor.css\";\nimport grapesJ<PERSON>, { Editor } from \"grapesjs\";\nimport grapesJSMJML from \"grapesjs-mjml\";\nimport { useEffect, useState, useCallback } from \"react\";\nimport { Font, Image, Resource, Template } from \"../../../types\";\nimport ImageGalleryModal from \"../ImageGalleryModal\";\n\ninterface GrapesAssetManagerProps {\n  event: \"open\" | \"close\";\n  open: boolean;\n  select: (asset: any) => void;\n  close: () => void;\n}\n\ninterface GrapesReactProps {\n  id: HTMLElement[\"id\"];\n  mjml?: string;\n  onChange: (mjml: string, html: string, editor: Editor) => void;\n  setAssetState: (props: GrapesAssetManagerProps) => void;\n  fonts?: Font[];\n}\n\nfunction GrapesReact({\n  id,\n  mjml,\n  onChange,\n  setAssetState,\n  fonts = [],\n}: GrapesReactProps) {\n  const [editor, setEditor] = useState<Editor | undefined>(undefined);\n  const [loaded, setLoaded] = useState(false);\n\n  const removeAll = (doc: Document, attr: string) => {\n    const all = doc.head.querySelectorAll(`[${attr}]`);\n    Array.from(all).forEach((el) => el.remove());\n  };\n\n  const getFontHtml = (url: string) => {\n    return `\n            <link href=\"${url}\" rel=\"stylesheet\" type=\"text/css\" data-silex-font>\n            <style type=\"text/css\" data-silex-font>\n                @import url(${url});\n            </style>\n        `;\n  };\n\n  const GOOGLE_FONTS_ATTR = \"data-silex-gstatic\";\n  const updateHead = useCallback((editor: Editor, fonts: Font[]) => {\n    const doc = editor.Canvas.getDocument();\n    if (!doc) return;\n    removeAll(doc, GOOGLE_FONTS_ATTR);\n    let html = \"\";\n    for (const font of fonts) {\n      html += getFontHtml(font.url);\n    }\n    doc.head.insertAdjacentHTML(\"beforeend\", html);\n  }, []);\n\n  const updateFontUi = useCallback(\n    (editor: Editor, fonts: Font[]) => {\n      const styleManager = editor.StyleManager;\n      const fontProperty = styleManager.getProperty(\n        \"typography\",\n        \"font-family\"\n      ) as any;\n      const options = fontProperty?.getOptions();\n      const newOptions = [\n        ...options.filter((option: any) => !option.custom),\n        ...fonts.map((font) => ({\n          id: font.value,\n          label: font.name,\n          custom: true,\n        })),\n      ];\n      fontProperty?.setOptions(newOptions);\n      styleManager.render();\n\n      updateHead(editor, fonts);\n    },\n    [updateHead]\n  );\n\n  useEffect(() => {\n    if (!editor) {\n      const editor = grapesJS.init({\n        fromElement: false,\n        container: `#${id}`,\n        storageManager: false,\n        autorender: false,\n        plugins: [grapesJSMJML],\n        pluginsOpts: {\n          [grapesJSMJML as any]: {\n            fonts: fonts.reduce(\n              (acc, { name, url }) => ({ ...acc, [name]: url }),\n              {}\n            ),\n          },\n        },\n        height: \"100%\",\n        assetManager: {\n          custom: {\n            open(props) {\n              setAssetState({ ...props, event: \"open\" });\n            },\n            close(props) {\n              setAssetState({ ...props, event: \"close\" });\n            },\n          },\n        },\n      });\n      setEditor(editor);\n      editor.on(\"load\", () => {\n        editor.Panels.getButton(\"views\", \"open-blocks\")?.set(\"active\", true);\n        setLoaded(true);\n      });\n      editor.render();\n      editor.setComponents(mjml ?? \"<mjml><mj-body></mj-body></mjml>\");\n      editor.on(\"update\", () => {\n        onChange(\n          editor.getHtml(),\n          editor.runCommand(\"mjml-code-to-html\").html,\n          editor\n        );\n      });\n    }\n  }, [editor, fonts, id, mjml, onChange, setAssetState]);\n\n  useEffect(() => {\n    if (editor) updateFontUi(editor, fonts);\n  }, [loaded, fonts, editor, updateFontUi]);\n\n  return <div id={id} />;\n}\n\ninterface VisualEditorProps {\n  template: Template;\n  setTemplate: (template: Template) => void;\n  resources: Resource[];\n}\n\nexport default function VisualEditor({\n  template,\n  setTemplate,\n  resources,\n}: VisualEditorProps) {\n  const [showImages, setShowImages] = useState(false);\n  const [assetManager, setAssetManager] = useState<\n    GrapesAssetManagerProps | undefined\n  >();\n  const fonts = resources.map((resource) => resource.value as Font);\n  function handleSetTemplate(mjml: string, html: string) {\n    setTemplate({ ...template, data: { ...template.data, mjml, html } });\n  }\n\n  function handleImageInsert(image: Image) {\n    assetManager?.select({ src: image.url });\n    handleHideImages();\n  }\n\n  function handleHideImages() {\n    setShowImages(false);\n    assetManager?.close();\n  }\n\n  function handleAssetState(state: GrapesAssetManagerProps) {\n    setShowImages(state.event === \"open\");\n    setAssetManager(state);\n  }\n\n  return (\n    <>\n      <GrapesReact\n        id={`grapes-editor-${template.id}`}\n        mjml={template.data.mjml}\n        fonts={fonts}\n        onChange={handleSetTemplate}\n        setAssetState={handleAssetState}\n      />\n      <ImageGalleryModal\n        open={showImages}\n        onClose={handleHideImages}\n        onInsert={handleImageInsert}\n      />\n    </>\n  );\n}\n"], "names": ["GrapesReact", "_ref", "id", "mjml", "onChange", "setAssetState", "fonts", "editor", "setEditor", "useState", "undefined", "loaded", "setLoaded", "updateHead", "useCallback", "doc", "<PERSON><PERSON>", "getDocument", "removeAll", "attr", "all", "head", "querySelectorAll", "Array", "from", "for<PERSON>ach", "el", "remove", "html", "font", "url", "insertAdjacentHTML", "updateFontUi", "styleManager", "StyleManager", "fontProperty", "getProperty", "newOptions", "getOptions", "filter", "option", "custom", "map", "value", "label", "name", "setOptions", "render", "useEffect", "grapesJS", "init", "fromElement", "container", "storageManager", "autorender", "plugins", "grapesJSMJML", "pluginsOpts", "reduce", "acc", "_ref2", "height", "assetManager", "open", "props", "event", "close", "on", "_editor$Panels$getBut", "Panels", "getButton", "set", "setComponents", "getHtml", "runCommand", "_jsx", "VisualEditor", "_ref3", "template", "setTemplate", "resources", "showImages", "setShowImages", "setAssetManager", "resource", "handleHideImages", "_jsxs", "_Fragment", "children", "data", "state", "ImageGalleryModal", "onClose", "onInsert", "image", "select", "src"], "sourceRoot": ""}