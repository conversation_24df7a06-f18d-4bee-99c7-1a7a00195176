server {
    listen 3000;
    root /usr/share/nginx/html;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://api:3001;
        proxy_read_timeout 600s;
        proxy_connect_timeout 60s;
        proxy_send_timeout 600s;
    }
    
    location ~ ^\/(uploads|unsubscribe|\.well-known|(?:c$|c\/)|(?:o$|o\/)) { 
        rewrite ^\/(.*)$ /api/$1 break;
        proxy_pass http://api:3001;
        proxy_read_timeout 600s;
        proxy_connect_timeout 60s;
        proxy_send_timeout 600s;
    }

    client_max_body_size 64M;
}