MallocNanoZone=0
USER=dashon
COMMAND_MODE=unix2003
__CFBundleIdentifier=com.todesktop.230313mzl4w4u92
PATH=/Users/<USER>/.pyenv/versions/3.10.4/bin:/opt/anaconda3/bin:/opt/anaconda3/condabin:/Users/<USER>/.sdkman/candidates/java/current/bin:/Users/<USER>/.docker/bin:/Users/<USER>/.nvm/versions/node/v20.10.0/bin:/Users/<USER>/.pyenv/versions/3.10.4/bin:/opt/homebrew/bin:/opt/homebrew/sbin:/Library/Frameworks/Python.framework/Versions/3.9/bin:/Library/Frameworks/Python.framework/Versions/3.12/bin:/Library/Frameworks/Python.framework/Versions/3.10/bin:/usr/local/bin:/System/Cryptexes/App/usr/bin:/usr/bin:/bin:/usr/sbin:/sbin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/local/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/bin:/var/run/com.apple.security.cryptexd/codex.system/bootstrap/usr/appleinternal/bin:/Library/Apple/usr/bin:/usr/local/share/dotnet:~/.dotnet/tools:/Library/Frameworks/Mono.framework/Versions/Current/Commands:/Users/<USER>/.pyenv/versions/3.10.4/bin:/Users/<USER>/Development/Applications/venice/cli:/Users/<USER>/Development/Applications/marketing_auto/apps/ui/cli:/Users/<USER>/Development/Applications/venice/cli:/Users/<USER>/Development/Applications/venice/cli:/Users/<USER>/Development/Applications/venice/cli:/Users/<USER>/Development/Applications/venice/cli:/Users/<USER>/Library/Android/sdk/platform-tools
LOGNAME=dashon
SSH_AUTH_SOCK=/private/tmp/com.apple.launchd.5XoakeAuwN/Listeners
HOME=/Users/<USER>
SHELL=/bin/zsh
TMPDIR=/var/folders/rq/3lwh01ms1079csv85r9xl9zm0000gn/T/
__CF_USER_TEXT_ENCODING=0x1F5:0x0:0x0
XPC_SERVICE_NAME=0
XPC_FLAGS=0x0
ORIGINAL_XDG_CURRENT_DESKTOP=undefined
CURSOR_TRACE_ID=f8498bbdde1c4b5e8a662a427c127b28
TERM_PROGRAM=vscode
TERM_PROGRAM_VERSION=0.44.11
LANG=en_US.UTF-8
COLORTERM=truecolor
GIT_ASKPASS=/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass.sh
VSCODE_GIT_ASKPASS_NODE=/Applications/Cursor.app/Contents/Frameworks/Cursor Helper (Plugin).app/Contents/MacOS/Cursor Helper (Plugin)
VSCODE_GIT_ASKPASS_EXTRA_ARGS=
VSCODE_GIT_ASKPASS_MAIN=/Applications/Cursor.app/Contents/Resources/app/extensions/git/dist/askpass-main.js
VSCODE_GIT_IPC_HANDLE=/var/folders/rq/3lwh01ms1079csv85r9xl9zm0000gn/T/vscode-git-fe01350d56.sock
APP_SECRET=Ck7R453k4j5k435ndjymbBoA6zqih
UI_PORT=3000
NODE_ENV=development
DB_CLIENT=mysql2
DB_HOST=mysql
DB_USERNAME=root
DB_PASSWORD=parcelvoypassword
DB_PORT=3306
DB_DATABASE=parcelvoy
QUEUE_DRIVER=redis
REDIS_HOST=redis
REDIS_PORT=6379
STORAGE_DRIVER=firebase
STORAGE_BASE_URL=https://firebasestorage.googleapis.com/v0/b/bakedbot-agents.appspot.com/o
AUTH_DRIVER=firebase
AUTH_BASIC_EMAIL=<EMAIL>
AUTH_BASIC_PASSWORD=password
AUTH_BASIC_NAME=Login
AUTH_FIREBASE_NAME=Firebase
FIREBASE_PRIVATE_KEY_ID=388fe788893976999313ebbfd09fce82c895b898
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDdXvSpXhRch9S/G/H92qIfCPG61RFGjocK/4EFp4hg/iBsE1Z+2TSyrZ8/qv6e6E9VOXVSKl9iNtNmKh+AzVtZarz68ecydzBR6T2hhMprMuAyXj5VtHKHPRHfIWA104nI5hKBbpuPMxH6s2JSTvQ6aFg4D341qx0QVCf0OVlhuJhp4PpCqzHdSliXeQVVCfUYUJ6MaGtvwrUvaQdr6mu2fzwIQjKkgW1DwtIFE92kCOlYkGekLgYxhvswvS40MK+Df1nHWMqIROw2GfCJaGe2v2WohcSO8mxNE2H9VHHpLi0/EcE0GsDn9TvGmVIhXWnW4zs63mqxQVMIHvhZNGD1AgMBAAECggEAGJm70LKOyrWhk9dULiTEzXQeAOg6sj+WjEdWnD1A755uFfSUMSaML6sRvm6POzOP1WW8PvRMg0qadYkK1TJ9C1PMSorlaXtLWF72JWPvZrhRp+dHQBj8fUDxIn3ABltKvZOrzMxw5xoCznxu2bzCcVc1Lo2yNd6dLTpc8kwJPc2j3UNSF713JnucUZcDY0ArSO6SPYsFPN9agVh0zs0xsX9Fi28akxRLxiVFlfWrdeWzamnhJzapLGSBXW5TFfn1e/roKNI0oohtzaM3aJW4x0zkU07s2QZhv5vnY1TqHof2sWN3shUQE0fYbrtx/cgVDkFRAesRpJtr/U+cojulhQKBgQDz+4I9+Yd5tRx+xIeUnauCfsOw669usiqoE6JFwBEUupfFIBwEWiKKoZQbgYQzuLRtHrgMwOcpJSUDj0Jd5OYB6fFk/Wec70DQf6plEs5XAGK3qov41UqwQtw6IWOPWzqYl7fZXQjAlTOrcrNi7Ihc+9ZsfnhltnojJ/MRSpez3wKBgQDoRlPKjfq853DIu99xsPO2XbrLGy1gtkMLXI9RrFdNHhtPTX/9WtNX8yrSGUruF0wkxJK8xzu+CzvocrSeLLzCjTP3ZkcszGdTbHGL9l5nAXRrVwm2owKkLFLRsoXxF1DuoFDDea8wJpwsg/z3tGzny5sfQdOZOFL1gBWegyAlqwKBgGOKb169mWosXA3UPMSDd1/sPAbIgXCWPBUKvZ/bdpyQbC8+pG+aHpGUzopSUw+PAf2rFMgPQ8BjqTBEmH0aUETjn1rmkAlVC4qFK43FGG4ieCQa7rR8wNYc0SDeUnJAdC2haJ7D+DgqJ0SBrXJkslXNYXQuPQyyYC5LtD2Pw0jdAoGBAN+c3QwS9cIo+f52SB4W4fm+cJcaWexSa2cju+JADWfzxQzqqn5Jk0+HjcDokqd2jFqVWEMfd1d1Tz5wN4xTu4F9GPtjXsA1JIA+1W2OSMHXt7GjAHnwoxlUhoLU4HgDH2orsrUNj+fGM/HzO3VpILNi47sKz2AhxlQM9EM/L5KzAoGAXzc9iS7jDWCGqFAKpurqKKQqUz1HfJmcndJZVbvF9q8yj2pTB13uGvrkCcC/w97AdDbnOydTlS6C9WO6YUlFHOPevlFpw1ZpQ8sZPUQI8FCGpRxYLLJjCEiSkma1ciUTsskR4AHcuQgKU7HPchcgjQ2ryF3HaOdHF/p0zZTbNe0=-----END PRIVATE KEY-----"
FIREBASE_AUTH_DOMAIN=bakedbot-agents.firebaseapp.com
FIREBASE_PROJECT_ID=bakedbot-agents
FIREBASE_STORAGE_BUCKET=bakedbot-agents.appspot.com
FIREBASE_MESSAGING_SENDER_ID=************
FIREBASE_APP_ID=1:************:web:f92bba3616f4e96cfb5313
FIREBASE_MEASUREMENT_ID=G-XNRELY75L4
BASE_URL=https://cannabis-marketing-chatbot-224bde0578da.herokuapp.com/api/v1
TWILIO_AUTH_TOKEN=a5445b06c4d5a8b90a7fb05eda604e73
TWILIO_ACCOUNT_SID=**********************************
TWILIO_PHONE_NUMBER=+***********
SENDGRID_API_KEY=*********************************************************************
OPENAI_API_KEY=************************************************************************************************************************************
PINECONE_API_KEY=pcsk_48ooNQ_P4WkPtNoehtQB8VeYhj8qJ3AbwNjbCuhcYPYKyUM8XMH1QJQNJdWVsDcHtaU3r7
REACT_APP_AWS_ACCESS_KEY_ID=********************
REACT_APP_AWS_SECRET_ACCESS_KEY=4326TN+FWUA4dZ/WoM0r9L7aV9Xyxt9hv6DL/Wra
REACT_APP_AWS_REGION=eu-north-1
SUPABASE_PASSWORD=Lf6LBAK$pA76dRw
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.2NqUNM8NGxU3n5gAVf4FTOBCqKcRlFh0xjBvIrf5psk
SUPABASE_URL=https://nixatetkmouteapspmth.supabase.co
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
DIRECT_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL=true
VSCODE_L10N_BUNDLE_LOCATION=
VSCODE_INJECTION=1
ZDOTDIR=/Users/<USER>
USER_ZDOTDIR=/Users/<USER>
PWD=/Users/<USER>/Development/Applications/marketing_auto/apps/ui
TERM=xterm-256color
SHLVL=1
OLDPWD=/Users/<USER>/Development/Applications/marketing_auto/apps/ui
HOMEBREW_PREFIX=/opt/homebrew
HOMEBREW_CELLAR=/opt/homebrew/Cellar
HOMEBREW_REPOSITORY=/opt/homebrew
INFOPATH=/opt/homebrew/share/info:
ZSH=/Users/<USER>/.oh-my-zsh
PAGER=less
LESS=-R
LSCOLORS=Gxfxcxdxbxegedabagacad
LS_COLORS=di=1;36:ln=35:so=32:pi=33:ex=31:bd=34;46:cd=34;43:su=30;41:sg=30;46:tw=30;42:ow=30;43
NVM_DIR=/Users/<USER>/.nvm
NVM_CD_FLAGS=-q
NVM_BIN=/Users/<USER>/.nvm/versions/node/v20.10.0/bin
NVM_INC=/Users/<USER>/.nvm/versions/node/v20.10.0/include/node
ANDROID_HOME=/Users/<USER>/Library/Android/sdk
SDKMAN_DIR=/Users/<USER>/.sdkman
SDKMAN_CANDIDATES_API=https://api.sdkman.io/2
SDKMAN_PLATFORM=darwinarm64
SDKMAN_CANDIDATES_DIR=/Users/<USER>/.sdkman/candidates
JAVA_HOME=/Users/<USER>/.sdkman/candidates/java/current
MAPBOX_ACCESS_TOKEN=pk.eyJ1IjoidHJhdmVsZGF5IiwiYSI6ImNsczg5MWM0azIybzQybWw4Y203MDI1YjIifQ.oecsQnk8AKVDG84swkbyEw
CONDA_EXE=/opt/anaconda3/bin/conda
_CE_M=
_CE_CONDA=
CONDA_PYTHON_EXE=/opt/anaconda3/bin/python
CONDA_SHLVL=1
CONDA_PREFIX=/opt/anaconda3
CONDA_DEFAULT_ENV=base
CONDA_PROMPT_MODIFIER=(base) 
GSETTINGS_SCHEMA_DIR_CONDA_BACKUP=
GSETTINGS_SCHEMA_DIR=/opt/anaconda3/share/glib-2.0/schemas
_=/Users/<USER>/.pyenv/versions/3.10.4/bin/python
