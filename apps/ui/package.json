{"name": "@bakedBot/ui", "version": "0.1.0", "dependencies": {"@amraneze/react-instagram-login": "^4.0.0", "@aws-sdk/client-sagemaker-runtime": "^3.716.0", "@firebase/auth": "^1.8.1", "@fontsource/inter": "^4.5.14", "@googlemaps/js-api-loader": "^1.16.8", "@greatsumini/react-facebook-login": "^3.4.0", "@headlessui/react": "1.7.18", "@heroicons/react": "^2.0.11", "@hookform/resolvers": "^4.1.3", "@monaco-editor/react": "^4.4.6", "@popperjs/core": "^2.11.6", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-tooltip": "^1.1.6", "@rehookify/datepicker": "^3.2.0", "@stripe/react-stripe-js": "^3.5.0", "@stripe/stripe-js": "^6.1.0", "@supabase/supabase-js": "^2.49.1", "@textea/json-viewer": "2.16.2", "@types/google.maps": "^3.58.1", "@types/node": "^16.11.41", "@types/react": "^18.0.14", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.0.5", "@types/uuid": "^9.0.0", "axios": "^0.27.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^2.30.0", "date-fns-tz": "^1.3.8", "dayjs": "^1.11.13", "firebase": "^11.0.2", "framer-motion": "^11.16.4", "fuse.js": "^7.1.0", "grapesjs": "^0.21.1", "grapesjs-mjml": "^1.0.4", "html2canvas": "^1.4.1", "i18next": "^23.10.1", "i18next-http-backend": "^2.5.0", "libphonenumber-js": "^1.12.6", "lodash": "^4.17.21", "monaco-editor": "^0.41.0", "postal-codes-js": "^2.5.2", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-big-calendar": "^1.18.0", "react-charts": "^3.0.0-beta.57", "react-circular-progressbar": "^2.1.0", "react-countdown": "^2.3.6", "react-dom": "^18.2.0", "react-hook-form": "7.51.1", "react-hot-toast": "^2.4.0", "react-i18next": "^14.1.0", "react-icons": "^5.4.0", "react-linkedin-login-oauth2": "^2.0.1", "react-markdown": "^9.0.1", "react-mentions": "^4.4.10", "react-popper": "^2.3.0", "react-router-dom": "^6.4.2", "react-share": "^5.2.2", "react-toastify": "^11.0.2", "reactflow": "11.10.1", "recharts": "^2.14.1", "rehype-raw": "^7.0.0", "rrule": "2.7.2", "socket.io-client": "^4.8.1", "styled-components": "^6.1.13", "sweetalert2": "^11.14.5", "tailwind-merge": "^2.2.1", "toastr": "^2.1.4", "usetiful-sdk": "^0.2.0", "uuid": "^9.0.0", "web-vitals": "^2.1.4", "zod": "^3.23.8"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-app-rewired eject", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "package:build": "rimraf lib/ && npm run package:build:esm && npm run package:build:cjs", "package:build:esm": "tsc --project ./tsconfig.lib-module.json && copyfiles -u 1 'src/**/*.css' lib/esm/ && copyfiles -u 2 'src/assets/*' lib/esm/assets/", "package:build:cjs": "tsc --project ./tsconfig.lib.json && copyfiles -u 1 'src/**/*.css' lib/cjs/ && copyfiles -u 2 'src/assets/*' lib/cjs/assets/", "package:publish": "npm run package:build && npm version $npm_config_tag --no-git-tag-version && npm pack && npm publish --tag=latest --access public"}, "types": "./lib/cjs/mod.d.ts", "main": "./lib/cjs/mod.js", "module": "./lib/esm/mod.js", "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "7.21.11", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/lodash": "^4.17.16", "@types/react-big-calendar": "^1.16.1", "@types/react-mentions": "^4.4.1", "@types/toastr": "^2.1.43", "@typescript-eslint/eslint-plugin": "^5.43.0", "autoprefixer": "^10.4.20", "copyfiles": "2.4.1", "eslint": "^8.28.0", "eslint-config-standard-with-typescript": "^23.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-n": "^15.5.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.31.11", "http-proxy-middleware": "^2.0.6", "postcss": "^8.4.49", "react-app-rewired": "^2.2.1", "react-scripts": "5.0.1", "rimraf": "5.0.1", "source-map-explorer": "2.5.3", "tailwindcss": "^3.4.17", "typescript": "^4.9.3"}, "files": ["lib"]}