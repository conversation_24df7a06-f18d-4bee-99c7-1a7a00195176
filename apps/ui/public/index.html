<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="ie=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="theme-color" content="#000000" />
        <link rel="icon" href="%PUBLIC_URL%/favicon.ico" type="image/x-icon">
        <link rel="icon" href="%PUBLIC_URL%/favicon-32x32.png" type="image/png" sizes="32x32">
        <link href="%PUBLIC_URL%/safari.svg" rel="mask-icon" color="#151c2d">
        <meta property="og:title" content="BakedBot" />
        <script async src="https://js.stripe.com/v3/pricing-table.js"></script>

        <meta property="og:description" content="Revolutionize your dispensary with BakedBot AI — the only cannabis marketing platform powered by autonomous agents that deliver AI-driven product recommendations, automate marketing, and boost sales while ensuring full compliance." />
        <meta property="og:url" content="https://bakedBot.ai" />
        <meta property="og:image" content="https://bakedBot.ai/og-image.jpg" />
        <script src="https://www.google.com/recaptcha/api.js"></script>
        <script src="%PUBLIC_URL%/config.js"></script>
        <!--
            manifest.json provides metadata used when your web app is installed on a
            user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
        -->
        <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
        <!--
            Notice the use of %PUBLIC_URL% in the tags above.
            It will be replaced with the URL of the `public` folder during the build.
            Only files inside the `public` folder can be referenced from the HTML.

            Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
            work correctly both with client-side routing and a non-root public URL.
            Learn how to configure a non-root public URL by running `npm run build`.
        -->
        <title>BakedBot</title>
    </head>
    <body>
        <noscript>You need to enable JavaScript to run this app.</noscript>
        <div id="root"></div>
        <!--
            This HTML file is a template.
            If you open it directly in the browser, you will see an empty page.

            You can add webfonts, meta tags, or analytics to this file.
            The build step will place the bundled scripts into the <body> tag.

            To begin the development, run `npm start` or `yarn start`.
            To create a production bundle, use `npm run build` or `yarn build`.
        -->
    </body>
</html>
