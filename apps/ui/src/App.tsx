import { useMemo } from "react";
import { RouterProvider } from "react-router-dom";
import { PreferencesProvider } from "./ui/PreferencesContext";
import { RouterProps, createRouter } from "./views/router";
import { Toaster } from "./ui/Toast";
import Dashboard from "./views/dashboard/Dashboard";
import SystemHealth from "./views/health/SystemHealth";

import "toastr/build/toastr.min.css";
import "@fontsource/inter/400.css";
import "@fontsource/inter/500.css";
import "@fontsource/inter/700.css";
import "./variables.css";
import "./index.css";
import { ChatProvider } from "./views/chat/ChatContext";

export default function App(props: RouterProps) {
  const router = useMemo(() => createRouter(props), [props]);

  return (
    <ChatProvider>
      <PreferencesProvider>
        <RouterProvider router={router} />
        <Toaster />
      </PreferencesProvider>
    </ChatProvider>
  );
}
