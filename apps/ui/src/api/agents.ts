import { AxiosInstance } from "axios";

// Agent data status interfaces
export interface AgentDataStatus {
  available: boolean;
  current_records?: number;
  min_records?: number;
  missing_columns?: string[];
  current_documents?: number;
  min_documents?: number;
  missing_types?: string[];
}

// Agent availability interfaces
export interface AgentAvailabilityData {
  agentId: string;
  name: string;
  isAvailable: boolean;
  missingRequirements: string[];
  partiallyAvailable: boolean;
  availabilityPercentage: number;
  dataStatus: Record<string, AgentDataStatus>;
  capabilities?: string[];
  description?: string;
}

export interface AgentAvailabilityResponse {
  summary: {
    total: number;
    available: number;
    partial: number;
    unavailable: number;
  };
  available: AgentAvailabilityData[];
  partial: AgentAvailabilityData[];
  unavailable: AgentAvailabilityData[];
  locationId: number;
}

export default function (client: AxiosInstance) {
  return {
    /**
     * Get detailed agent availability for a location
     * This shows which agents are available, partially available, or unavailable
     * along with detailed information about data requirements
     * @param locationId The location ID to check availability for
     */
    getAvailability: async (
      locationId: number
    ): Promise<AgentAvailabilityResponse> => {
      return client
        .get(`/locations/${locationId}/agents/availability`)
        .then((r) => r.data);
    },

    /**
     * Get agent definitions (all possible agents regardless of availability)
     */
    getDefinitions: async () => {
      return client.get("/agents/definitions").then((r) => r.data.agents);
    },

    /**
     * Get detailed requirements for a specific agent
     * @param agentId The agent ID to get requirements for
     */
    getRequirements: async (agentId: string) => {
      return client.get(`/agents/${agentId}/requirements`).then((r) => r.data);
    },
  };
}
