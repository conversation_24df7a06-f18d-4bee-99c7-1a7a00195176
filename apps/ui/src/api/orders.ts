import { Order, SearchParams, SearchResult } from "../types";
import api from "./index";

/**
 * Search orders with pagination and filtering
 * @param locationId Location ID
 * @param params Search parameters
 * @returns Paginated search results
 */
export async function search(
  locationId: number,
  params: SearchParams
): Promise<SearchResult<Order>> {
  return api.get(`/locations/${locationId}/orders`, params);
}

/**
 * Get a specific order by ID
 * @param locationId Location ID
 * @param orderId Order ID
 * @returns Order details
 */
export async function getById(
  locationId: number,
  orderId: number
): Promise<Order> {
  return api.get(`/locations/${locationId}/orders/${orderId}`);
}

/**
 * Update order status
 * @param locationId Location ID
 * @param orderId Order ID
 * @param data Status data
 * @returns Updated order
 */
export async function updateStatus(
  locationId: number,
  orderId: number,
  data: { status: Order["status"] }
): Promise<Order> {
  return api.put(`/locations/${locationId}/orders/${orderId}/status`, data);
}

/**
 * Cancel an order
 * @param locationId Location ID
 * @param orderId Order ID
 * @returns Updated order
 */
export async function cancel(
  locationId: number,
  orderId: number
): Promise<Order> {
  return api.post(`/locations/${locationId}/orders/${orderId}/cancel`);
}

/**
 * Export orders to CSV
 * @param locationId Location ID
 * @param params Export parameters
 * @returns CSV file URL
 */
export async function exportOrders(
  locationId: number,
  params: Record<string, any>
): Promise<{ url: string }> {
  return api.get(`/locations/${locationId}/orders/export`, params);
}

export default {
  search,
  getById,
  updateStatus,
  cancel,
  exportOrders,
};
