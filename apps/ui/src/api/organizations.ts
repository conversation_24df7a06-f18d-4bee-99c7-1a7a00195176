import { Organization } from "../types";
import { ApiClient } from "./client";

const client = new ApiClient();

export interface OrganizationUpdateParams {
  username?: string;
  domain?: string;
  tracking_deeplink_mirror_url?: string;
  sender_email?: string;
}

export interface VerifySenderParams {
  email: string;
}

export interface VerifySenderResponse {
  status: string;
  message: string;
}

export default {
  get: async (): Promise<Organization> => {
    return await client.get<Organization>("/organizations");
  },

  update: async (
    id: string | number,
    params: OrganizationUpdateParams
  ): Promise<Organization> => {
    return await client.put<Organization>(`/organizations/${id}`, params);
  },

  delete: async (): Promise<boolean> => {
    return await client.delete<boolean>("/organizations");
  },

  verifySender: async (
    params: VerifySenderParams
  ): Promise<VerifySenderResponse> => {
    return await client.post<VerifySenderResponse>(
      "/organizations/verify-sender",
      params
    );
  },
};
