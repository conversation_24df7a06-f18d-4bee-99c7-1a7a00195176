export interface EnhanceOptions {
  limit?: number;
  skip?: number;
  meta_sku?: string;
  async?: boolean;
}

export interface ProductsAPI {
  enhance(
    locationId: number,
    options?: EnhanceOptions
  ): Promise<{
    message: string;
    enhanced_count?: number;
    vectorized_count?: number;
    failed_vectorization?: number;
    status?: string;
    count?: number;
    async?: boolean;
  }>;

  // Add other product API methods as needed
}

// Implementation would be elsewhere in the codebase
