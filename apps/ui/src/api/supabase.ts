import client from "./client";

// Types for Supabase integration
interface DocumentMetadata {
  documentId?: string;
  name: string;
  size: number;
  type: string;
  url?: string;
}

interface SupabaseUploadResult {
  success: boolean;
  tableName?: string;
  recordCount?: number;
  error?: string;
}

export default {
  uploadData: async (
    locationId: number | string,
    data: any
  ): Promise<SupabaseUploadResult> => {
    // Make sure we're formatting document metadata correctly if present
    if (
      data.data_sources?.documents &&
      Array.isArray(data.data_sources.documents)
    ) {
      // Format document metadata appropriately for backend processing
      data.data_sources.documents = data.data_sources.documents.map(
        (doc: DocumentMetadata) => ({
          documentId:
            doc.documentId ||
            `doc_${doc.name.replace(/\s+/g, "_")}_${Date.now()}`,
          name: doc.name,
          size: doc.size,
          type: doc.type,
          url: doc.url || "",
        })
      );
    }

    const response = await client.post(
      `/locations/${locationId}/supabase/upload`,
      data
    );
    return response as SupabaseUploadResult;
  },

  getStatus: async (locationId: number | string) => {
    const response = await client.get(
      `/locations/${locationId}/supabase/status`
    );
    return response;
  },

  getTableInfo: async (locationId: number | string, tableName: string) => {
    const response = await client.get(
      `/locations/${locationId}/supabase/tables/${tableName}`
    );
    return response;
  },
};
