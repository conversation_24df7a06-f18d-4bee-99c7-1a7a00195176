.agent-questions-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.agent-questions-container h3 {
  margin-bottom: 16px;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.agent-questions-container.compact {
  padding: 12px;
}

.agent-questions-container.compact h3 {
  font-size: 1.1rem;
  margin-bottom: 12px;
}

.questions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.questions-grid.compact-grid {
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 12px;
}

.question-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.question-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.question-text {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  line-height: 1.4;
  margin-bottom: 8px;
}

.question-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.question-agent {
  font-size: 11px;
  color: #3b82f6;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.question-category {
  font-size: 10px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
  text-transform: capitalize;
}

.agent-questions-loading {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.agent-questions-loading .loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

.agent-questions-error {
  text-align: center;
  padding: 20px;
  color: #ef4444;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
}

.agent-questions-error small {
  display: block;
  margin-top: 8px;
  color: #7f1d1d;
}

.no-questions {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
}

.no-questions small {
  display: block;
  margin-top: 8px;
  color: #9ca3af;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .questions-grid {
    grid-template-columns: 1fr;
  }
  
  .question-card {
    padding: 12px;
  }
  
  .question-text {
    font-size: 13px;
  }
  
  .question-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
} 