import React from "react";
import { useAgentBasedQuestions } from "../hooks/useAgentBasedQuestions";

interface AgentBasedQuestionsDemoProps {
  onQuestionClick?: (question: string) => void;
  compact?: boolean;
}

export const AgentBasedQuestionsDemo: React.FC<
  AgentBasedQuestionsDemoProps
> = ({ onQuestionClick, compact = false }) => {
  const { questions, loading, error } = useAgentBasedQuestions();

  if (error) {
    return (
      <div className="agent-questions-error">
        <p>Failed to load personalized questions</p>
        <small>{error}</small>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="agent-questions-loading">
        <div className="loading-spinner"></div>
        <p>Loading personalized questions based on your data...</p>
      </div>
    );
  }

  return (
    <div className={`agent-questions-container ${compact ? "compact" : ""}`}>
      <h3>Smart Questions Based on Your Data</h3>
      <div className={`questions-grid ${compact ? "compact-grid" : ""}`}>
        {questions.map((question, index) => (
          <button
            key={index}
            className="question-card"
            onClick={() => onQuestionClick?.(question.prompt)}
            title={question.description}
          >
            <div className="question-text">{question.prompt}</div>
            <div className="question-meta">
              <span className="question-agent">{question.agent}</span>
              <span className="question-category">{question.category}</span>
            </div>
          </button>
        ))}
      </div>

      {questions.length === 0 && (
        <div className="no-questions">
          <p>No personalized questions available at the moment.</p>
          <small>
            This usually means you haven't connected any data sources yet.
          </small>
        </div>
      )}
    </div>
  );
};

export default AgentBasedQuestionsDemo;
