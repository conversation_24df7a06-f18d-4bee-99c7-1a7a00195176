import React, { useRef } from "react";
import Button from "../ui/Button";

interface FileUploadProps {
  onUpload: (file: File) => void;
  accept?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({ onUpload, accept }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleClick = () => {
    fileInputRef.current?.click();
  };

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onUpload(file);
      // Reset input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  return (
    <div className="space-y-4">
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleChange}
        accept={accept}
        className="hidden"
      />
      <Button onClick={handleClick}>Choose File</Button>
      <p className="text-xs text-gray-500">
        Supported formats: {accept?.split(",").join(", ") || "All files"}
      </p>
    </div>
  );
};

export default FileUpload;
