import { motion } from "framer-motion";
import * as Tooltip from "@radix-ui/react-tooltip";
import { useEffect } from "react";

export interface Agent {
  id: string;
  name: string;
  role: string;
  description: string;
  examples: string[];
  requirements: {
    required: string[];
    optional: string[];
  };
  icon: string;
  unlockCondition: (
    completedSteps: Set<string>,
    connectedData: Set<string>
  ) => "locked" | "partial" | "unlocked";
  disabled?: boolean;
}

interface AgentCardProps {
  agent: Agent;
  completedSteps: Set<string>;
  connectedData: Set<string>;
  recentlyUnlockedAgent: string | null;
  setRecentlyUnlockedAgent: (id: string | null) => void;
}

// Helper function to get user-friendly requirement names
const getRequirementLabel = (req: string): string => {
  const labels: Record<string, string> = {
    "company-info": "Business Information",
    pos: "POS System",
    competitors: "Competitor Data",
    "business-documents": "Business Documents",
    "social-media": "Social Media",
    "data-sources": "Data Sources",
    "government-database": "Government Database",
    "lab-data": "Lab Data",
  };
  return labels[req] || req;
};

// Helper function to get what each agent actually needs for full unlock
const getAgentFullUnlockRequirements = (agentId: string): string[] => {
  const agentRequirements: Record<string, string[]> = {
    smokey: ["company-info", "pos"],
    craig: ["pos", "company-info", "social-media"], // needs social-media for full unlock
    pops: ["pos", "company-info", "business-documents"], // needs business-documents for full unlock
    ezal: ["competitors", "company-info"],
    "money-mike": ["business-documents", "company-info"],
    "mrs-parker": ["pos", "company-info", "social-media"],
    deebo: ["company-info"], // partial unlock, would need government-database & lab-data for full
    "day-day": [],
    "big-worm": [],
  };
  return agentRequirements[agentId] || [];
};

// Helper function to get status-specific tooltip content
const getTooltipContent = (
  agent: Agent,
  status: string,
  completedSteps: Set<string>,
  connectedData: Set<string>
) => {
  // Get what this agent actually needs for full unlock (not just required fields)
  const fullUnlockRequirements = getAgentFullUnlockRequirements(agent.id);
  const missingForFullUnlock = fullUnlockRequirements.filter(
    (req) => !connectedData.has(req) && !completedSteps.has(req)
  );

  // For locked status, use the actual required fields
  const requiredForBasic = agent.requirements.required.filter(
    (req) => !connectedData.has(req) && !completedSteps.has(req)
  );

  switch (status) {
    case "unlocked":
      return {
        statusText: "✅ Fully Unlocked",
        statusColor: "text-green-hard",
        description: `${agent.name} is ready to help with full capabilities.`,
        capabilities: [
          "Answer complex questions about your business",
          "Provide personalized recommendations",
          "Access all connected data sources",
        ],
        nextSteps: null,
      };

    case "partial":
      return {
        statusText: "⚠️ Partially Unlocked",
        statusColor: "text-yellow-hard",
        description: `${agent.name} has basic functionality but needs more data for full capabilities.`,
        capabilities: [
          "Answer general questions",
          "Provide basic recommendations",
          "Limited data access",
        ],
        nextSteps:
          missingForFullUnlock.length > 0
            ? `Connect ${missingForFullUnlock
                .map(getRequirementLabel)
                .join(", ")} to unlock full potential`
            : "Partially unlocked - some advanced features may still be limited",
      };

    case "locked":
    default:
      return {
        statusText: "🔒 Locked",
        statusColor: "text-primary-soft",
        description: `${agent.name} needs data connections to start helping.`,
        capabilities: [],
        nextSteps: `Complete: ${requiredForBasic
          .map(getRequirementLabel)
          .join(", ")}`,
      };
  }
};

export default function AgentCard({
  agent,
  completedSteps,
  connectedData,
  recentlyUnlockedAgent,
  setRecentlyUnlockedAgent,
}: AgentCardProps) {
  const status = agent.disabled
    ? "locked"
    : agent.unlockCondition(completedSteps, connectedData);
  const isRecentlyUnlocked = recentlyUnlockedAgent === agent.id;

  const tooltipContent = getTooltipContent(
    agent,
    status,
    completedSteps,
    connectedData
  );

  useEffect(() => {
    if (status === "unlocked" && !agent.disabled) {
      setRecentlyUnlockedAgent(agent.id);
      setTimeout(() => {
        setRecentlyUnlockedAgent(null);
      }, 3000);
    }
  }, [status, agent.id, setRecentlyUnlockedAgent, agent.disabled]);

  return (
    <Tooltip.Provider delayDuration={300}>
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <motion.div
            key={agent.id}
            layout
            initial={{ opacity: 0, y: 20 }}
            animate={{
              opacity: agent.disabled ? 0.7 : 1,
              y: 0,
              scale: isRecentlyUnlocked ? 1.05 : 1,
            }}
            className={`relative p-4 rounded-lg border overflow-hidden ${
              status === "locked"
                ? "bg-surface border-divider"
                : status === "partial"
                ? "bg-yellow-soft border-yellow-hard"
                : "bg-green-soft border-green-hard"
            } ${agent.disabled ? "cursor-not-allowed" : ""}`}
          >
            {status === "partial" && (
              <div
                className="absolute top-0 left-0 right-0 h-1 bg-yellow-hard rounded-none"
                style={{
                  width: `${
                    (agent.requirements.required.filter((req) =>
                      connectedData.has(req)
                    ).length /
                      agent.requirements.required.length) *
                    100
                  }%`,
                }}
              />
            )}

            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-primary">{agent.name}</h3>
              <motion.div
                animate={
                  isRecentlyUnlocked
                    ? {
                        scale: [1, 1.2, 1],
                        rotate: [0, 10, -10, 0],
                      }
                    : {}
                }
                transition={{ duration: 0.5, repeat: 3 }}
              >
                {status === "locked" ? (
                  <svg
                    className="w-5 h-5 text-primary-soft"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                  </svg>
                ) : status === "partial" ? (
                  <svg
                    className="w-5 h-5 text-yellow-hard"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    />
                  </svg>
                ) : (
                  <svg
                    className="w-5 h-5 text-green-hard"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                )}
              </motion.div>
            </div>
            <p className="text-sm text-primary-soft">{agent.description}</p>
            <div className="mt-2">
              <h4 className="text-xs font-semibold text-primary-soft">
                Requirements:
              </h4>
              <div className="flex flex-wrap gap-1 mt-1">
                {agent.requirements.required.map((req) => (
                  <span
                    key={req}
                    className={`requirement-tag ${
                      connectedData.has(req) ? "completed" : "pending"
                    }`}
                  >
                    {connectedData.has(req) && (
                      <svg
                        className="w-3 h-3 mr-1"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                    {getRequirementLabel(req)}
                  </span>
                ))}
              </div>
            </div>

            {isRecentlyUnlocked && (
              <motion.div
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.5 }}
                className="absolute top-0 right-0 bg-green-hard text-white px-3 py-1 rounded-bl-lg text-xs font-bold shadow-md"
              >
                {status === "partial" ? "Partially Unlocked!" : "Unlocked!"}
              </motion.div>
            )}
          </motion.div>
        </Tooltip.Trigger>
        <Tooltip.Portal>
          <Tooltip.Content
            className="bg-surface-secondary text-primary p-4 rounded-lg shadow-lg max-w-sm z-50"
            sideOffset={5}
          >
            <div className="space-y-3">
              {/* Status Header */}
              <div className="border-b border-divider pb-2">
                <div className="flex items-center justify-between">
                  <p className="font-medium text-primary">{agent.role}</p>
                  <span
                    className={`text-xs font-medium ${tooltipContent.statusColor}`}
                  >
                    {tooltipContent.statusText}
                  </span>
                </div>
                <p className="text-xs text-primary-soft mt-1">
                  {tooltipContent.description}
                </p>
              </div>

              {/* Current Capabilities */}
              {tooltipContent.capabilities.length > 0 && (
                <div>
                  <p className="text-xs font-medium text-primary mb-1">
                    Current Capabilities:
                  </p>
                  <ul className="text-xs space-y-1">
                    {tooltipContent.capabilities.map((capability, index) => (
                      <li
                        key={index}
                        className="text-primary-soft flex items-start"
                      >
                        <span className="text-green-hard mr-1">•</span>
                        {capability}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Example Queries */}
              <div>
                <p className="text-xs font-medium text-primary mb-1">
                  Example Questions:
                </p>
                <ul className="text-xs space-y-1">
                  {agent.examples.slice(0, 2).map((example, index) => (
                    <li
                      key={index}
                      className="text-primary-soft flex items-start"
                    >
                      <span className="text-blue-400 mr-1">•</span>"{example}"
                    </li>
                  ))}
                </ul>
              </div>

              {/* Next Steps */}
              {tooltipContent.nextSteps && (
                <div className="border-t border-divider pt-2">
                  <p className="text-xs font-medium text-yellow-hard mb-1">
                    To Unlock:
                  </p>
                  <p className="text-xs text-primary-soft">
                    {tooltipContent.nextSteps}
                  </p>
                </div>
              )}

              {/* Requirements Status */}
              <div>
                <p className="text-xs font-medium text-primary mb-1">
                  Data Connections:
                </p>
                <div className="flex flex-wrap gap-1">
                  {agent.requirements.required.map((req) => (
                    <span
                      key={req}
                      className={`text-xs px-2 py-0.5 rounded-full ${
                        connectedData.has(req) || completedSteps.has(req)
                          ? "bg-green-soft text-green-hard border border-green-hard"
                          : "bg-surface text-primary-soft border border-divider"
                      }`}
                    >
                      {getRequirementLabel(req)}
                    </span>
                  ))}
                </div>
              </div>
            </div>
            <Tooltip.Arrow className="fill-surface-secondary" />
          </Tooltip.Content>
        </Tooltip.Portal>
      </Tooltip.Root>
    </Tooltip.Provider>
  );
}
