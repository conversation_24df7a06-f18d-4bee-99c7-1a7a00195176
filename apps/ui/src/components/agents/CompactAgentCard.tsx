import React from "react";
import * as Tooltip from "@radix-ui/react-tooltip";
import { Agent } from "./AgentCard";

interface CompactAgentCardProps {
  agent: Agent;
  completedSteps: Set<string>;
  connectedData: Set<string>;
  recentlyUnlockedAgent: string | null;
  setRecentlyUnlockedAgent: (id: string | null) => void;
}

// Helper function to get user-friendly requirement names
const getRequirementLabel = (req: string): string => {
  const labels: Record<string, string> = {
    "company-info": "Business Information",
    pos: "POS Data",
    competitors: "Competitor Data",
    "business-documents": "Business Documents",
    "social-media": "Social Media Accounts",
    "data-sources": "Data Sources",
    "government-database": "Government Database",
    "lab-data": "Lab Data",
  };
  return labels[req] || req;
};

// Helper function to get what each agent actually needs for full unlock
const getAgentFullUnlockRequirements = (agentId: string): string[] => {
  const agentRequirements: Record<string, string[]> = {
    smokey: ["company-info", "pos"],
    craig: ["pos", "company-info", "social-media"], // needs social-media for full unlock
    pops: ["pos", "company-info", "business-documents"], // needs business-documents for full unlock
    ezal: ["competitors", "company-info"],
    "money-mike": ["business-documents", "company-info"],
    "mrs-parker": ["pos", "company-info", "social-media"],
    deebo: ["company-info"], // partial unlock, would need government-database & lab-data for full
    "day-day": [],
    "big-worm": [],
  };
  return agentRequirements[agentId] || [];
};

// Helper function to get status-specific tooltip content
const getTooltipContent = (
  agent: Agent,
  status: string,
  completedSteps: Set<string>,
  connectedData: Set<string>
) => {
  // Get what this agent actually needs for full unlock (not just required fields)
  const fullUnlockRequirements = getAgentFullUnlockRequirements(agent.id);
  const missingForFullUnlock = fullUnlockRequirements.filter(
    (req) => !connectedData.has(req) && !completedSteps.has(req)
  );

  // For locked status, use the actual required fields
  const requiredForBasic = agent.requirements.required.filter(
    (req) => !connectedData.has(req) && !completedSteps.has(req)
  );

  switch (status) {
    case "unlocked":
      return {
        statusText: "✅ Fully Unlocked",
        statusColor: "text-[#3EDC81]",
        description: `${agent.name} is ready to help with full capabilities.`,
        capabilities: [
          "Answer complex questions about your business",
          "Provide personalized recommendations",
          "Access all connected data sources",
        ],
        nextSteps: null,
      };

    case "partial":
      return {
        statusText: "⚠️ Partially Unlocked",
        statusColor: "text-[#FFD700]",
        description: `${agent.name} has basic functionality but needs more data for full capabilities.`,
        capabilities: [
          "Answer general questions",
          "Provide basic recommendations",
          "Limited data access",
        ],
        nextSteps:
          missingForFullUnlock.length > 0
            ? `Add ${missingForFullUnlock
                .map(getRequirementLabel)
                .join(", ")} to unlock full potential`
            : "Partially unlocked - some advanced features may still be limited",
      };

    case "locked":
    default:
      return {
        statusText: "🔒 Locked",
        statusColor: "text-gray-400",
        description: `${agent.name} needs data connections to start helping.`,
        capabilities: [],
        nextSteps: `Add: ${requiredForBasic
          .map(getRequirementLabel)
          .join(", ")}`,
      };
  }
};

const CompactAgentCard: React.FC<CompactAgentCardProps> = ({
  agent,
  completedSteps,
  connectedData,
  recentlyUnlockedAgent,
  setRecentlyUnlockedAgent,
}) => {
  const status = agent.disabled
    ? "locked"
    : agent.unlockCondition(completedSteps, connectedData);
  const isRecentlyUnlocked = recentlyUnlockedAgent === agent.id;

  // Calculate progress percentage for partial status
  const progressPercentage =
    status === "partial"
      ? (agent.requirements.required.filter((req) => connectedData.has(req))
          .length /
          agent.requirements.required.length) *
        100
      : 0;

  const tooltipContent = getTooltipContent(
    agent,
    status,
    completedSteps,
    connectedData
  );

  return (
    <Tooltip.Provider delayDuration={300}>
      <Tooltip.Root>
        <Tooltip.Trigger asChild>
          <div
            className={`relative flex items-center p-3 rounded-lg border overflow-hidden ${
              status === "locked"
                ? "bg-surface  border-[#313642]"
                : status === "partial"
                ? "bg-[#35332E] border-[#FFD700]/20"
                : "bg-[#2E3A2F] border-[#3EDC81]/20"
            } ${agent.disabled ? "cursor-not-allowed opacity-70" : ""}`}
          >
            {status === "partial" && (
              <div
                className="absolute top-0 left-0 h-1 bg-[#FFD700]"
                style={{ width: `${progressPercentage}%` }}
              />
            )}

            <div className="flex-shrink-0 mr-3">
              {status === "locked" ? (
                <div className="w-8 h-8 bg-surface rounded-full flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                  </svg>
                </div>
              ) : (
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center  ${
                    status === "partial"
                      ? "bg-[#FFD700] text-white"
                      : "bg-[#3EDC81] text-white"
                  }`}
                >
                  <span className="font-bold">{agent.name.charAt(0)}</span>
                </div>
              )}
            </div>

            <div className="flex-1 min-w-0">
              <h3
                className={`text-sm font-medium ${
                  status === "locked" ? "" : "text-white"
                } truncate`}
              >
                {agent.name}
              </h3>
              <p className="text-xs text-gray-400 truncate">
                {agent.description}
              </p>
            </div>

            <div className="flex-shrink-0 ml-2">
              {status === "locked" ? (
                <svg
                  className="w-5 h-5 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  />
                </svg>
              ) : status === "partial" ? (
                <svg
                  className="w-5 h-5 text-[#FFD700]"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                  />
                </svg>
              ) : (
                <svg
                  className="w-5 h-5 text-[#3EDC81]"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              )}
            </div>

            {isRecentlyUnlocked && (
              <div className="absolute top-0 right-0 bg-[#3EDC81] text-[#1A1E2A] px-2 text-xs font-medium rounded-bl-lg">
                New!
              </div>
            )}
          </div>
        </Tooltip.Trigger>
        <Tooltip.Portal>
          <Tooltip.Content
            className="bg-[#2D3341] text-white p-3 rounded-lg shadow-lg max-w-sm z-50"
            sideOffset={5}
          >
            <div className="space-y-3">
              {/* Status Header */}
              <div className="border-b border-gray-600 pb-2">
                <div className="flex items-center justify-between">
                  <p className="font-medium text-white">{agent.role}</p>
                  <span
                    className={`text-xs font-medium ${tooltipContent.statusColor} fit-content`}
                  >
                    {tooltipContent.statusText}
                  </span>
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  {tooltipContent.description}
                </p>
              </div>

              {/* Current Capabilities */}
              {tooltipContent.capabilities.length > 0 && (
                <div>
                  <p className="text-xs font-medium text-gray-300 mb-1">
                    Current Capabilities:
                  </p>
                  <ul className="text-xs space-y-1">
                    {tooltipContent.capabilities.map((capability, index) => (
                      <li
                        key={index}
                        className="text-gray-400 flex items-start"
                      >
                        <span className="text-[#3EDC81] mr-1">•</span>
                        {capability}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Example Queries */}
              <div>
                <p className="text-xs font-medium text-gray-300 mb-1">
                  Example Questions:
                </p>
                <ul className="text-xs space-y-1">
                  {agent.examples.slice(0, 2).map((example, index) => (
                    <li key={index} className="text-gray-400 flex items-start">
                      <span className="text-blue-400 mr-1">•</span>"{example}"
                    </li>
                  ))}
                </ul>
              </div>

              {/* Next Steps */}
              {tooltipContent.nextSteps && (
                <div className="border-t border-gray-600 pt-2">
                  <p className="text-xs font-medium text-[#FFD700] mb-1">
                    To Unlock:
                  </p>
                  <p className="text-xs text-gray-300">
                    {tooltipContent.nextSteps}
                  </p>
                </div>
              )}

              {/* Requirements Status */}
              <div>
                <p className="text-xs font-medium text-gray-300 mb-1">
                  Data Connections:
                </p>
                <div className="flex flex-wrap gap-1">
                  {agent.requirements.required.map((req) => (
                    <span
                      key={req}
                      className={`text-xs px-2 py-0.5 rounded-full ${
                        connectedData.has(req) || completedSteps.has(req)
                          ? "bg-[#3EDC81]/20 text-[#3EDC81]"
                          : "bg-[#313642] text-gray-400"
                      }`}
                    >
                      {getRequirementLabel(req)}
                    </span>
                  ))}
                </div>
              </div>
            </div>
            <Tooltip.Arrow className="fill-[#2D3341]" />
          </Tooltip.Content>
        </Tooltip.Portal>
      </Tooltip.Root>
    </Tooltip.Provider>
  );
};

export default CompactAgentCard;
