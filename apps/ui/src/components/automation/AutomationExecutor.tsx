import { useCallback } from "react";
import { PlanStep } from "../../types/automation";
import { useAutomationExecution } from "../../hooks/useAutomationExecution";
import AutomationStepList from "./AutomationStepList";
import ExecutionSummary from "./ExecutionSummary";
import Button from "../../ui/Button";
import { useTranslation } from "react-i18next";

interface AutomationExecutorProps {
  steps: PlanStep[];
  onComplete?: () => void;
  onError?: (error: Error) => void;
}

export default function AutomationExecutor({
  steps,
  onComplete,
  onError,
}: AutomationExecutorProps) {
  const { t } = useTranslation();
  const {
    currentStepIndex,
    stepStatuses,
    stepErrors,
    createdResources,
    executeStep,
    retryStep,
    executeAll,
  } = useAutomationExecution({
    steps,
    onComplete,
    onError,
  });

  const handleRegenerate = useCallback(
    async (index: number) => {
      // Here you would implement the regeneration logic
      // For now, we'll just retry the step
      await retryStep(index);
    },
    [retryStep]
  );

  const isExecuting = currentStepIndex !== null;
  const hasErrors = Object.values(stepStatuses).includes("error");
  const isComplete =
    Object.keys(stepStatuses).length === steps.length &&
    !hasErrors &&
    Object.values(stepStatuses).every((status) => status === "completed");

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <AutomationStepList
            steps={steps}
            stepStatuses={stepStatuses}
            stepErrors={stepErrors}
            onRetry={retryStep}
            onRegenerate={handleRegenerate}
          />

          <div className="mt-6 flex justify-end space-x-4">
            {!isExecuting && !isComplete && (
              <Button
                variant="primary"
                onClick={executeAll}
                disabled={steps.length === 0}
              >
                {t("execute_all")}
              </Button>
            )}
            {!isExecuting && hasErrors && (
              <Button
                variant="secondary"
                onClick={() => {
                  const errorIndex = Object.entries(stepStatuses).find(
                    ([_, status]) => status === "error"
                  )?.[0];
                  if (errorIndex) {
                    retryStep(Number(errorIndex));
                  }
                }}
              >
                {t("retry_failed")}
              </Button>
            )}
          </div>
        </div>

        <div>
          <ExecutionSummary
            steps={steps}
            stepStatuses={stepStatuses}
            createdResources={createdResources}
          />
        </div>
      </div>

      {isComplete && (
        <div className="text-sm text-green-600 dark:text-green-400">
          {t("execution_complete")}
        </div>
      )}
    </div>
  );
}
