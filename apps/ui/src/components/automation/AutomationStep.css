.automation-step {
  background: var(--color-background);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: var(--shadow-sm);
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.step-name {
  font-weight: 500;
  color: var(--color-text);
}

.step-actions {
  display: flex;
  gap: 0.5rem;
}

.step-error {
  margin-top: 0.5rem;
  color: var(--color-red-hard);
  font-size: 0.875rem;
}

.status-icon {
  width: 20px;
  height: 20px;
}

.status-icon--success {
  color: rgb(34, 197, 94);
}

.status-icon--error {
  color: rgb(239, 68, 68);
}

.status-icon--progress {
  color: rgb(59, 130, 246);
  animation: spin 1s linear infinite;
}

.status-icon--queued {
  color: rgb(107, 114, 128);
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
} 