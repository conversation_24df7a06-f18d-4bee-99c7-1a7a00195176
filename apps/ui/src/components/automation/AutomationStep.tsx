import { useState } from "react";
import { motion } from "framer-motion";
import { useTranslation } from "react-i18next";
import { PlanStep } from "../../types/automation";
import Button from "../../ui/Button";
import {
  CheckIcon,
  ExclamationIcon,
  ClockIcon,
  RestartIcon,
} from "../../ui/icons";
import StepRegeneration, { RegenerationOptions } from "./StepRegeneration";
import "./AutomationStep.css";

export type StepStatus = "completed" | "in-progress" | "queued" | "error";
export interface StepError {
  message: string;
  code?: string;
}

interface AutomationStepProps {
  step: PlanStep & {
    name?: string;
    description?: string;
  };
  itemKey: string;
  status: StepStatus;
  error?: StepError;
  onRetry?: () => Promise<void>;
  onRegenerate?: (
    itemKey: string,
    options: RegenerationOptions
  ) => Promise<void>;
}

export default function AutomationStep({
  step,
  itemKey,
  status,
  error,
  onRetry,
  onRegenerate,
}: AutomationStepProps) {
  const { t } = useTranslation();
  const [showRegeneration, setShowRegeneration] = useState(false);

  const handleRetry = async () => {
    if (onRetry) {
      await onRetry();
    }
  };

  const handleRegenerate = () => {
    setShowRegeneration(true);
  };

  const getStatusIcon = () => {
    switch (status) {
      case "completed":
        return <CheckIcon className="status-icon status-icon--success" />;
      case "error":
        return <ExclamationIcon className="status-icon status-icon--error" />;
      case "in-progress":
        return <RestartIcon className="status-icon status-icon--progress" />;
      case "queued":
      default:
        return <ClockIcon className="status-icon status-icon--queued" />;
    }
  };

  const getErrorMessage = () => {
    if (!error) return null;
    return error.message || t("unknown_error");
  };

  return (
    <motion.div
      className="automation-step"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.2 }}
    >
      <div className="step-header">
        <div className="step-status">
          {getStatusIcon()}
          <span className="step-name">{step.name || t("unnamed_step")}</span>
        </div>
        {status === "error" && (
          <div className="step-actions">
            {onRetry && (
              <Button variant="secondary" size="small" onClick={handleRetry}>
                {t("retry")}
              </Button>
            )}
            {onRegenerate && (
              <Button
                variant="secondary"
                size="small"
                onClick={handleRegenerate}
              >
                {t("regenerate")}
              </Button>
            )}
          </div>
        )}
      </div>
      {error && <div className="step-error">{getErrorMessage()}</div>}
      {showRegeneration && onRegenerate && (
        <StepRegeneration
          step={step}
          itemKey={itemKey}
          onRegenerate={onRegenerate}
          onCancel={() => setShowRegeneration(false)}
        />
      )}
    </motion.div>
  );
}
