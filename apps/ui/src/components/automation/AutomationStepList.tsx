import { PlanStep } from "../../types/automation";
import AutomationStep, { StepStatus, StepError } from "./AutomationStep";

interface AutomationStepListProps {
  steps: PlanStep[];
  stepStatuses: Record<number, StepStatus>;
  stepErrors: Record<number, StepError | undefined>;
  onRetry?: (index: number) => Promise<void>;
  onRegenerate?: (index: number) => Promise<void>;
}

export default function AutomationStepList({
  steps,
  stepStatuses,
  stepErrors,
  onRetry,
  onRegenerate,
}: AutomationStepListProps) {
  return (
    <div className="space-y-4">
      {steps.map((step, index) => (
        <AutomationStep
          key={index}
          step={step}
          itemKey={`step-${index}`}
          status={stepStatuses[index] || "queued"}
          error={stepErrors[index]}
          onRetry={onRetry ? () => onRetry(index) : undefined}
          onRegenerate={onRegenerate ? () => onRegenerate(index) : undefined}
        />
      ))}
    </div>
  );
}
