import { PlanStep, StepStatus } from "../../types/automation";
import { useTranslation } from "react-i18next";

interface ExecutionSummaryProps {
  steps: PlanStep[];
  stepStatuses: Record<number, StepStatus>;
  createdResources: Record<number, { id: number; type: string }>;
}

export default function ExecutionSummary({
  steps,
  stepStatuses,
  createdResources,
}: ExecutionSummaryProps) {
  const { t } = useTranslation();

  const totalSteps = steps.length;
  const completedSteps = Object.values(stepStatuses).filter(
    (status) => status === "completed"
  ).length;
  const failedSteps = Object.values(stepStatuses).filter(
    (status) => status === "error"
  ).length;

  const resourcesByType = Object.values(createdResources).reduce(
    (acc, { type }) => ({
      ...acc,
      [type]: (acc[type] || 0) + 1,
    }),
    {} as Record<string, number>
  );

  return (
    <div className="bg-surface rounded-lg shadow p-6">
      <h3 className="text-lg font-medium text-gray-900  mb-4">
        {t("execution_summary")}
      </h3>

      <div className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-sm text-blue-600">{t("total_steps")}</div>
            <div className="text-xl font-semibold text-blue-700 dark:text-blue-300">
              {totalSteps}
            </div>
          </div>
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div className="text-sm text-green-600 dark:text-green-400">
              {t("completed_steps")}
            </div>
            <div className="text-xl font-semibold text-green-700 dark:text-green-300">
              {completedSteps}
            </div>
          </div>
          <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
            <div className="text-sm text-red-600 dark:text-red-400">
              {t("failed_steps")}
            </div>
            <div className="text-xl font-semibold text-red-700 dark:text-red-300">
              {failedSteps}
            </div>
          </div>
        </div>

        {Object.keys(resourcesByType).length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-2">
              {t("created_resources")}
            </h4>
            <div className="space-y-2">
              {Object.entries(resourcesByType).map(([type, count]) => (
                <div
                  key={type}
                  className="flex justify-between items-center text-sm"
                >
                  <span className="">{t(type)}</span>
                  <span className="font-medium ">{count}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
