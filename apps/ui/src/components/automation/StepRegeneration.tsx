import { useState } from "react";
import { PlanStep } from "../../types/automation";
import Button from "../../ui/Button";
import { useTranslation } from "react-i18next";

interface StepRegenerationProps {
  step: PlanStep;
  itemKey: string;
  onRegenerate: (
    itemKey: string,
    options: RegenerationOptions
  ) => Promise<void>;
  onCancel: () => void;
}

export interface RegenerationOptions {
  keepConfig?: boolean;
  modifyPrompt?: string;
}

export default function StepRegeneration({
  step,
  itemKey,
  onRegenerate,
  onCancel,
}: StepRegenerationProps) {
  const { t } = useTranslation();
  const [keepConfig, setKeepConfig] = useState(true);
  const [modifyPrompt, setModifyPrompt] = useState("");
  const [isRegenerating, setIsRegenerating] = useState(false);

  const handleRegenerate = async () => {
    try {
      setIsRegenerating(true);
      await onRegenerate(itemKey, {
        keepConfig,
        modifyPrompt: modifyPrompt.trim() || undefined,
      });
      onCancel();
    } catch (error) {
      console.error("Error regenerating step:", error);
    } finally {
      setIsRegenerating(false);
    }
  };

  return (
    <div className="bg-surface rounded-lg shadow p-6">
      <h3 className="text-lg font-medium  mb-4">{t("regenerate_step")}</h3>

      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="keepConfig"
            checked={keepConfig}
            onChange={(e) => setKeepConfig(e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="keepConfig" className="ml-2 block text-sm">
            {t("keep_existing_config")}
          </label>
        </div>

        <div>
          <label
            htmlFor="modifyPrompt"
            className="block text-sm font-medium mb-1"
          >
            {t("modify_prompt")}
          </label>
          <textarea
            id="modifyPrompt"
            value={modifyPrompt}
            onChange={(e) => setModifyPrompt(e.target.value)}
            placeholder={t("modify_prompt_placeholder")}
            className="w-full h-24 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 bg-surface"
          />
        </div>

        <div className="flex justify-end space-x-4">
          <Button
            variant="secondary"
            onClick={onCancel}
            disabled={isRegenerating}
          >
            {t("cancel")}
          </Button>
          <Button
            variant="primary"
            onClick={handleRegenerate}
            disabled={isRegenerating}
            isLoading={isRegenerating}
          >
            {t("regenerate")}
          </Button>
        </div>
      </div>
    </div>
  );
}
