.insights-container {
  width: 100%;
  padding: 2px 0;
  border-bottom: 1px solid #e2e8f0;
  flex-shrink: 0;
  transition: all 0.2s ease-out;
  position: relative;
  max-height: 100px;
}

.insights-container.minimized {
  max-height: 0;
  padding: 0;
  border-bottom: none;
  overflow: visible;
}

.insights-header {
  display: flex;
  justify-content: flex-end;
  padding: 0 12px;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 10;
}

.insights-container.minimized .insights-header {
  position: fixed;
  right: 20px;
  top: 70px;
  border-radius: 50%;
  padding: 4px;
  backdrop-filter: blur(4px);
}

.toggle-insights-btn {
  background: transparent;
  border: none;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #718096;
  font-size: 14px;
  padding: 0;
  margin: 0;
  opacity: 0.5;
  transition: opacity 0.2s;
}

.toggle-insights-btn:hover {
  opacity: 1;
}

.insights-scroll-area {
  display: flex;
  overflow-x: auto;
  gap: 6px;
  padding: 0 8px;
  scrollbar-width: thin;
  -ms-overflow-style: none;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
  height: 100%;
}

.insights-scroll-area::-webkit-scrollbar {
  height: 4px;
}

.insights-scroll-area::-webkit-scrollbar-track {
  background: transparent;
}

.insights-scroll-area::-webkit-scrollbar-thumb {
  border-radius: 4px;
}

.insight-card {
  flex: 0 0 auto;
  min-width: 280px;
  max-width: 320px;
  height: 80px;
  border-radius: 8px;
  padding: 8px 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid #e2e8f0;
  flex-grow: 1;
}

.insight-header {
  display: flex;
  align-items: center;
  padding: 4px 4px 0px 4px;
}

.insight-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;
  font-size: 12px;
}

.insight-icon.demographic {
  background-color: #e6f7ff;
  color: #0072ff;
}

.insight-icon.trending {
  background-color: #fff2e6;
  color: #ff4500;
}

.insight-icon.market {
  background-color: #e6ffe6;
  color: #00b300;
}

.insight-icon.performance {
  background-color: #e6e6ff;
  color: #4d4dff;
}

.insight-icon.other {
  background-color: #f0f0f0;
  color: #666666;
}

.insight-header h3 {
  font-size: 11px;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.insight-content {
  font-size: 11px;
  line-height: 1.1;
  color: #4a5568;
  margin: 0 0 0px 0;
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.insight-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding:0px 4px 2px 4px;
}

.insight-indicators {
  display: flex;
  gap: 2px;
}

.indicator {
  width: 5px;
  height: 2px;
  background-color: #e2e8f0;
  border-radius: 1px;
}

.indicator.active {
  background-color: #3EDC81;
  width: 10px;
}

.insight-action {
  padding: 0;
  background: none;
  border: none;
  color: #3EDC81;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  text-decoration: none;
}

.insight-action:hover {
  text-decoration: underline;
}

/* Loading, Empty, and Error States */
.insights-loading,
.insights-error,
.insights-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px;
  text-align: center;
  min-height: 60px;
}

.insights-loading {
  color: #718096;
}

.insights-error {
  color: #e53e3e;
}

.insights-empty {
  color: #a0aec0;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #3EDC81;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon,
.empty-icon {
  font-size: 20px;
  margin-bottom: 8px;
  opacity: 0.7;
}

.insights-loading p,
.insights-error p,
.insights-empty p {
  margin: 0 0 4px 0;
  font-size: 12px;
  font-weight: 500;
}

.insights-error small,
.insights-empty small {
  font-size: 11px;
  opacity: 0.7;
  margin: 0;
}

