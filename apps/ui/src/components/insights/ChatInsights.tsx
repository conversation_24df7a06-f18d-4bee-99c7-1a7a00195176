import React, { useState } from "react";
import "./ChatInsights.css";
import { Button } from "../../ui";

export interface InsightData {
  title: string;
  content: string;
  type: "demographics" | "trending" | "market" | "performance" | "other";
  action?: string;
}

interface ChatInsightsProps {
  insights: InsightData[];
  isLoading?: boolean;
  error?: string;
}

const ChatInsights: React.FC<ChatInsightsProps> = ({
  insights,
  isLoading = false,
  error,
}) => {
  const [isMinimized, setIsMinimized] = useState(false);

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="insights-loading">
          <div className="loading-spinner"></div>
          <p>Analyzing your data to generate insights...</p>
        </div>
      );
    }

    if (error) {
      return (
        <div className="insights-error">
          <div className="error-icon">⚠️</div>
          <p>Unable to load insights at this time.</p>
          <small>{error}</small>
        </div>
      );
    }

    if (!insights || insights.length === 0) {
      return (
        <div className="insights-empty">
          <div className="empty-icon">📊</div>
          <p>No insights available yet.</p>
          <small>
            We need more data to generate meaningful insights. Check back after
            some activity.
          </small>
        </div>
      );
    }

    return (
      <div className="insights-scroll-area">
        {insights.map((insight, index) => (
          <div key={index} className={`insight-card ${insight.type}`}>
            <div className="insight-header">
              {insight.type === "demographics" && (
                <div className="insight-icon demographic">👥</div>
              )}
              {insight.type === "trending" && (
                <div className="insight-icon trending">🔥</div>
              )}
              {insight.type === "market" && (
                <div className="insight-icon market">📊</div>
              )}
              {insight.type === "performance" && (
                <div className="insight-icon performance">📈</div>
              )}
              {insight.type === "other" && (
                <div className="insight-icon other">💡</div>
              )}
              <h3>{insight.title}</h3>
            </div>
            <p className="insight-content">{insight.content}</p>
            <div className="insight-footer">
              <div className="insight-indicators">
                <span className="indicator active"></span>
                <span className="indicator"></span>
                <span className="indicator"></span>
              </div>
              {insight.action && (
                <button className="insight-action">{insight.action}</button>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={`insights-container ${isMinimized ? "minimized" : ""}`}>
      <div className="insights-header">
        <Button
          className="toggle-insights-btn"
          onClick={() => setIsMinimized(!isMinimized)}
          aria-label={isMinimized ? "Expand insights" : "Minimize insights"}
        >
          {isMinimized ? "↓" : "↑"}
        </Button>
      </div>

      {!isMinimized && renderContent()}
    </div>
  );
};

export default ChatInsights;
