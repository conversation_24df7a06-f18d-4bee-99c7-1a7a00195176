import { useTranslation } from "react-i18next";
import Button from "../../ui/Button";
import { getCurrentLocation } from "../../utils/location";
import { useState, useRef, useEffect } from "react";
import documentsApi from "../../api/documents";
import toastr from "toastr";

interface DocumentsStepProps {
  onStepComplete: () => void;
  isCompleted?: boolean;
  onBack?: () => void;
  documents: File[];
  setDocuments: (files: File[]) => void;
}

interface UploadedDocument {
  id: number;
  name: string;
  type: string;
  status: "pending" | "processing" | "completed" | "failed";
}

export default function DocumentsStep({
  onStepComplete,
  documents,
  setDocuments,
  isCompleted,
  onBack,
}: DocumentsStepProps) {
  const { t } = useTranslation();
  const locationId = getCurrentLocation();
  const [isUploading, setIsUploading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load existing documents when component mounts
  // useEffect(() => {
  //   const loadDocuments = async () => {
  //     if (!locationId) return;

  //     try {
  //       setIsLoading(true);
  //       const docs = await documentsApi.list(locationId.toString());
  //       setDocuments(
  //         docs.map((doc) => ({
  //           id: doc.id,
  //           name: doc.name,
  //           type: doc.type,
  //           status: doc.status,
  //         }))
  //       );
  //     } catch (error) {
  //       console.error("Error loading documents:", error);
  //       toastr.error("Failed to load documents");
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   loadDocuments();
  // }, [locationId]);

  // const handleDrag = (e: React.DragEvent) => {
  //   e.preventDefault();
  //   e.stopPropagation();

  //   if (e.type === "dragenter" || e.type === "dragover") {
  //     setDragActive(true);
  //   } else if (e.type === "dragleave") {
  //     setDragActive(false);
  //   }
  // };

  // const handleDrop = (e: React.DragEvent) => {
  //   e.preventDefault();
  //   e.stopPropagation();
  //   setDragActive(false);

  //   if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
  //     handleFiles(e.dataTransfer.files);
  //   }
  // };

  // const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //   if (e.target.files && e.target.files.length > 0) {
  //     handleFiles(e.target.files);
  //   }
  // };

  const handleManualUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files as FileList;
    // if (!locationId) {
    //   toastr.error("No location selected");
    //   return;
    // }
    for (const file of files) {
      if (file) {
        setDocuments([...documents, file]);
      }
    }
  };

  function handleUpload() {
    document.getElementById("fileUpload")?.click();
  }

  // Handle File Drop
  function handleDrop(event: React.DragEvent<HTMLDivElement>) {
    event.preventDefault();
    const file = event.dataTransfer.files?.[0];

    // if (!locationId) {
    //   toastr.error("No location selected");
    //   return;
    // }

    if (file) {
      setDocuments([...documents, file]);
    }
  }

  // Prevent default drag behaviors
  function handleDragOver(event: React.DragEvent<HTMLDivElement>) {
    event.preventDefault();
  }

  // const uploadDocument = async (file: File) => {
  //   if (!locationId) {
  //     toastr.error("No location selected");
  //     return;
  //   }

  //   setIsUploading(true);
  //   try {
  //     const response = await documentsApi.upload(locationId.toString(), file);

  //     setDocuments((prev) => [
  //       ...prev,
  //       {
  //         id: response.document.id,
  //         name: response.document.name,
  //         type: response.document.type,
  //         status: "processing",
  //       },
  //     ]);

  //   } catch (error) {
  //     console.error("Error uploading document:", error);
  //     toastr.error(`Failed to upload "${file.name}"`);
  //   } finally {
  //     setIsUploading(false);
  //   }
  // };

  // const handleSelectFilesClick = () => {
  //   fileInputRef.current?.click();
  // };

  // Add function to get document download URL
  const getDocumentDownloadUrl = (documentId: number) => {
    return documentsApi.getDownloadUrl(
      locationId?.toString() || "",
      documentId
    );
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4 text-primary">
        {t("onboarding.steps.documents.title")}
      </h2>
      <p className="text-sm text-primary-soft mb-4">
        {t(
          "onboarding.steps.documents.description",
          "Upload your business documents to help us understand your business better. These could include licenses, permits, or other relevant documentation."
        )}
      </p>

      <div className="space-y-6">
        <div
          className={`border-2 ${
            dragActive ? "border-primary" : "border-dashed "
          } rounded-lg p-8 text-center`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p className="mt-4 text-sm text-primary-soft">
            {t(
              "onboarding.steps.documents.upload_instructions",
              "Drag and drop your files here, or click to select files"
            )}
          </p>
          <p className="mt-2 text-xs text-primary-soft">
            {t(
              "onboarding.steps.documents.supported_formats",
              "PDF, Word, and Excel files up to 10MB"
            )}
          </p>
          <Button variant="secondary" onClick={handleUpload} className="mt-4">
            {t("onboarding.steps.documents.select_files", "Select Files")}
          </Button>
          <input
            id="fileUpload"
            type="file"
            multiple
            onChange={handleManualUpload}
            className="hidden"
            accept=".pdf,.doc,.docx,.xls,.xlsx"
          />
        </div>

        {isLoading ? (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-sm text-primary-soft">
              {t("loading", "Loading...")}
            </p>
          </div>
        ) : documents.length > 0 ? (
          <div className="mt-6">
            <h3 className="text-sm font-medium text-primary-soft mb-2">
              {t(
                "onboarding.steps.documents.uploaded_documents",
                "Uploaded Documents:"
              )}
            </h3>
            <div className="my-5 max-h-[200px] overflow-y-auto">
              {documents.length > 0 &&
                documents.map((file, index) => (
                  <p className="my-3 flex gap-2 items-center" key={index}>
                    <svg
                      className="w-4 h-4 mr-2 text-gray-500"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    {file.name}
                  </p>
                ))}
            </div>
          </div>
        ) : null}
      </div>
      <div className="flex justify-between mt-4">
        <Button
          variant="secondary"
          onClick={handleBack}
          className="inline-flex items-center"
        >
          {t("onboarding.navigation.back")}
        </Button>
        <Button onClick={onStepComplete} disabled={isUploading}>
          {documents.length > 0
            ? t("onboarding.steps.continue", "Continue")
            : "Skip, I'll add later"}
        </Button>
      </div>
    </div>
  );
}
