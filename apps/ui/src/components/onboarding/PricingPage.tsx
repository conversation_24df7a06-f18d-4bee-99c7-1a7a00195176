/* eslint-disable @typescript-eslint/ban-ts-comment */
import { useEffect, useState, useContext } from "react";
import { AdminContext } from "../../contexts";

// Add Window interface extension for Stripe
declare global {
  interface Window {
    Stripe?: any;
  }
}

interface PricingPageProps {
  organizationId?: number;
  customerEmail?: string;
}

const StripePricingTable = ({
  organizationId: initialOrgId,
  customerEmail = "",
}: PricingPageProps) => {
  // Get admin from context
  const admin = useContext(AdminContext);

  const [organizationId, setOrganizationId] = useState<number | null>(
    initialOrgId || admin?.organization_id || null
  );

  useEffect(() => {
    // Try to get organizationId from props or admin context
    if (initialOrgId) {
      console.log("Using organizationId from props:", initialOrgId);
      setOrganizationId(initialOrgId);
    } else if (admin?.organization_id) {
      setOrganizationId(admin.organization_id);
      console.log(
        "Using organization_id from admin context:",
        admin.organization_id
      );
    } else {
      // For testing/development, you can set a default value
      // Remove this in production
      console.warn("No organization ID provided, using default value");
      setOrganizationId(1); // Replace with appropriate default or remove
    }
  }, [initialOrgId, admin]);

  // Function to handle subscription completion
  const handleSubscriptionCompletion = (event: any) => {
    console.log("Subscription completed!", event);

    fetch(process.env.REACT_APP_SUBSCRIPTION_COMPLETED_URL as string, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        customer_id: event.customerId,
        subscription_id: event.subscriptionId,
        organization_id: organizationId,
      }),
    })
      .then((response) => response.json())
      .then((data) => console.log("Server response:", data))
      .catch((error) => console.error("Error sending data:", error));
  };

  // Load the Stripe Pricing Table script and initialize it with metadata
  useEffect(() => {
    // Remove any existing pricing table scripts to avoid duplicates
    const existingScript = document.getElementById(
      "stripe-pricing-table-script"
    );
    if (existingScript) {
      existingScript.remove();
    }

    // Create and configure the script element
    const script = document.createElement("script");
    script.id = "stripe-pricing-table-script";
    script.src = "https://js.stripe.com/v3/pricing-table.js";
    script.async = true;

    // Listen for the script to load
    script.onload = () => {
      // After script loads, we need to customize the pricing table behavior
      // to include metadata in the checkout session
      if (window.Stripe && organizationId) {
        // Override the default pricing table behavior to include metadata
        const originalFetch = window.fetch;
        window.fetch = function (input, init) {
          // Check if this is a request to create a checkout session
          if (
            typeof input === "string" &&
            input.includes("/checkout/sessions")
          ) {
            try {
              // Parse the request body to add metadata
              const body = init?.body ? JSON.parse(init.body.toString()) : {};

              // Add organization ID to metadata
              if (!body.metadata) body.metadata = {};
              body.metadata.organizationId = organizationId.toString();

              console.log(
                "Adding organization ID to Stripe session:",
                organizationId
              );

              // Replace the body in the request
              const newInit = {
                ...init,
                body: JSON.stringify(body),
              };

              return originalFetch(input, newInit);
            } catch (error) {
              console.error("Error modifying checkout session request:", error);
            }
          }

          // For all other requests, proceed normally
          return originalFetch(input, init);
        };
      }
    };

    document.head.appendChild(script);

    // Cleanup function
    return () => {
      // Restore original fetch if we modified it
      if (window.fetch !== globalThis.fetch) {
        window.fetch = globalThis.fetch;
      }
      // Remove the script when the component unmounts
      if (document.head.contains(script)) {
        document.head.removeChild(script);
      }
    };
  }, [organizationId]);

  // If organization ID isn't available yet, show loading
  if (!organizationId) {
    return <div>Loading pricing information...</div>;
  }
  return (
    <div>
      <h2 className="text-center text-2xl font-bold">Choose Your Plan</h2>
      <div className="mt-20">
        {/* @ts-ignore */}
        <stripe-pricing-table
          customer-email={customerEmail}
          pricing-table-id={`${process.env.REACT_APP_STRIPE_PRICING_TABLE_ID}`}
          publishable-key={process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY}
          client-reference-id={organizationId ? organizationId.toString() : ""}
        />
      </div>

      {/* Enterprise Tier Section */}
      <div className="mt-12 pt-8 border-t border-gray-200">
        <div className="max-w-4xl mx-auto p-6 bg-gray-50 rounded-lg shadow-md">
          <h3 className="text-2xl font-semibold text-center text-gray-800 mb-4">
            Enterprise Tier – Custom Pricing
          </h3>
          <p className="text-center text-gray-600 mb-6">
            For large-scale operators and multi-location dispensaries.
          </p>
          <ul className="space-y-2 text-gray-700 list-disc list-inside mb-6 pl-4 md:columns-2">
            <li>Unlimited Smokey AI Budtender recommendations</li>
            <li>Smokey Chat: BakedGPT with short & long-term memory</li>
            <li>Full Smokey Growth Suite + Custom BI Dashboards</li>
            <li>
              Wholesale messaging rates (SMS from $0.0045, Email from $0.0003)
            </li>
            <li>White-label UX: Custom domains, themes, and portals</li>
            <li>Deep segmentation + advanced campaign scheduling</li>
            <li>Full API access (JSON/GraphQL) + white-label portal</li>
            <li>Dedicated CSM + Slack support</li>
          </ul>
          <p className="text-center font-medium text-gray-800 mb-4">
            Talk to us to get started with a tailored plan.
          </p>
          <div className="text-center">
            <a
              href="javascript:void(0)"
              onClick={() =>
                window.open("https://calendly.com/baked-martez", "_blank")
              }
              className="inline-block px-6 py-2.5 bg-green-600 text-white font-semibold rounded-md shadow hover:bg-green-700 transition duration-300"
            >
              Talk to Sales
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StripePricingTable;
