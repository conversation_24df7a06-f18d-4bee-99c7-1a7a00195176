import React from "react";
import { Button } from "../../ui";

interface ScheduleCallModalProps {
  businessName?: string;
  onClose: () => void;
  onScheduleCall: () => void;
  onSkip: () => void;
}

const ScheduleCallModal: React.FC<ScheduleCallModalProps> = ({
  businessName = "your business",
  onClose,
  onScheduleCall,
  onSkip,
}) => {
  const handleScheduleClick = () => {
    // Open Calendly or similar service in a new tab
    window.open("https://calendly.com/baked-martez", "_blank");
    // Also call onScheduleCall to navigate the user to the dashboard
    onScheduleCall();
  };

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-surface w-full max-w-md rounded-lg shadow-xl overflow-hidden">
        <div className="relative">
          {/* Gradient header */}
          <div className="bg-gradient-to-r from-[#3EDC81] to-[#5BB5A2] p-4">
            <h3 className="text-[#1A1E2A] font-semibold text-lg">
              Complete your onboarding
            </h3>
            <p className="text-[#1A1E2A]/80 text-sm mt-1">
              Get the most out of Smokey's AI with a personalized setup call
            </p>
          </div>

          {/* Close button */}
          <button
            className="absolute top-3 right-3 text-[#1A1E2A]/80 hover:text-[#1A1E2A]"
            onClick={onClose}
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        <div className="p-5">
          <div className="mb-4">
            <div className="flex items-start mb-3">
              <div className="bg-[#3EDC81]/20 p-2 rounded-full mr-3 mt-0.5">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M5 12L10 17L20 7"
                    stroke="#3EDC81"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-primary text-sm">
                  Quick initial setup complete!
                </h4>
                <p className="text-primary-soft text-xs">
                  Basic profile for {businessName} created
                </p>
              </div>
            </div>

            <div className="flex items-start mb-3">
              <div className="bg-[#3EDC81]/20 p-2 rounded-full mr-3 mt-0.5">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                    stroke="#3EDC81"
                    strokeWidth="2"
                  />
                  <path
                    d="M12 8V16"
                    stroke="#3EDC81"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                  <path
                    d="M8 12H16"
                    stroke="#3EDC81"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-primary text-sm">
                  Next: Upload your product data
                </h4>
                <p className="text-primary-soft text-xs">
                  Connect POS & improve AI recommendations by 70%
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <div className="bg-[#3EDC81]/20 p-2 rounded-full mr-3 mt-0.5">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M20 10C20 14.4183 16.4183 18 12 18C7.58172 18 4 14.4183 4 10C4 5.58172 7.58172 2 12 2C16.4183 2 20 5.58172 20 10Z"
                    stroke="#3EDC81"
                    strokeWidth="2"
                  />
                  <path
                    d="M12 18V22"
                    stroke="#3EDC81"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                  <path
                    d="M7 22H17"
                    stroke="#3EDC81"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </div>
              <div>
                <h4 className="font-medium text-primary text-sm">
                  Quick 15-min guided setup
                </h4>
                <p className="text-primary-soft text-xs">
                  Our team will help you get more from Smokey's AI
                </p>
              </div>
            </div>
          </div>

          <div className="pt-2 flex flex-col sm:flex-row gap-3">
            <Button
              type="button"
              variant="primary"
              className="flex-1"
              onClick={handleScheduleClick}
            >
              Schedule 15-min setup call
            </Button>

            <Button
              type="button"
              variant="ghost"
              className="flex-1"
              onClick={onSkip}
            >
              Skip for now
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScheduleCallModal;
