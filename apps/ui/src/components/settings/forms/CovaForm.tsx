import { useState } from "react";
import { Location } from "../../../types";

interface CovaFormProps {
  location: Location;
  onChange: (provider: { type: string; config: any }) => void;
}

export default function CovaForm({ location, onChange }: CovaFormProps) {
  const [apiKey, setApiKey] = useState("");
  const [storeId, setStoreId] = useState("");
  const [environment, setEnvironment] = useState("production");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onChange({
      type: "cova",
      config: {
        api_key: apiKey,
        store_id: storeId,
        environment,
      },
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="apiKey" className="block text-sm font-medium">
          API Key
        </label>
        <input
          type="text"
          id="apiKey"
          value={apiKey}
          onChange={(e) => setApiKey(e.target.value)}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
          required
        />
      </div>

      <div>
        <label htmlFor="storeId" className="block text-sm font-medium">
          Store ID
        </label>
        <input
          type="text"
          id="storeId"
          value={storeId}
          onChange={(e) => setStoreId(e.target.value)}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
          required
        />
      </div>

      <div>
        <label htmlFor="environment" className="block text-sm font-medium">
          Environment
        </label>
        <select
          id="environment"
          value={environment}
          onChange={(e) => setEnvironment(e.target.value)}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring-primary sm:text-sm"
        >
          <option value="production">Production</option>
          <option value="sandbox">Sandbox</option>
        </select>
      </div>

      <button
        type="submit"
        className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
      >
        Connect
      </button>
    </form>
  );
}
