import { useState } from "react";
import { Location } from "../../../types";
import Button from "../../../ui/Button";

interface CsvUploadFormProps {
  location: Location;
  onChange: (provider: { type: string; config: any }) => void;
  onUploadStart: () => void;
  onUploadComplete: (response: { status: string }) => void;
  onError: (error: Error) => void;
}

export default function CsvUploadForm({
  location,
  onChange,
  onUploadStart,
  onUploadComplete,
  onError,
}: CsvUploadFormProps) {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0]);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!file) return;

    try {
      setUploading(true);
      onUploadStart();

      const formData = new FormData();
      formData.append("file", file);
      formData.append("location_id", location.id?.toString() || "");

      const response = await fetch("/api/upload/csv", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Upload failed");
      }

      const data = await response.json();
      onUploadComplete(data);
      onChange({
        type: "csv",
        config: {
          file_name: file.name,
          upload_id: data.upload_id,
        },
      });
    } catch (error) {
      onError(error instanceof Error ? error : new Error("Unknown error"));
    } finally {
      setUploading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label htmlFor="csvFile" className="block text-sm font-medium">
          CSV File
        </label>
        <input
          type="file"
          id="csvFile"
          accept=".csv"
          onChange={handleFileChange}
          className="mt-1 block w-full text-sm text-gray-500
            file:mr-4 file:py-2 file:px-4
            file:rounded-full file:border-0
            file:text-sm file:font-semibold
            file:bg-primary file:text-white
            hover:file:bg-primary/90"
          required
        />
        <p className="mt-1 text-sm text-gray-500">
          Upload a CSV file with your product data
        </p>
      </div>

      <Button type="submit" disabled={!file || uploading} className="w-full">
        {uploading ? "Uploading..." : "Upload"}
      </Button>
    </form>
  );
}
