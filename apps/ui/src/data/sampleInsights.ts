import { InsightData } from "../components/insights/ChatInsights";

export const sampleInsights: InsightData[] = [
  {
    title: "Customer Demographics",
    content: "Primary customers: Adults 30-40, 74% male",
    type: "demographics",
    action: "Next insight",
  },
  {
    title: "Trending Products",
    content: "Vapes and edibles are trending in Tilton this mnh",
    type: "trending",
    action: "Next insight",
  },
  {
    title: "Market Opportunity",
    content: "Female 25-34 segment underserved - potential 22% revenue growth",
    type: "market",
    action: "Next insight",
  },
  {
    title: "Performance",
    content: "Sales up 17% week-over-week, driven by new CBD products",
    type: "performance",
    action: "Next insight",
  },
  {
    title: "Competitor Activity",
    content: "Green Leaf running 30% off promo on all concentrates this week",
    type: "other",
    action: "Next insight",
  },
];

export default sampleInsights;
