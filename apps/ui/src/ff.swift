import UIKit

class KeyboardViewController: UIInputViewController {
    private var refineButton: UIButton!
    private var activityIndicator: UIActivityIndicatorView!
    private var suggestionsStack: UIStackView!
    private var permissionBanner: UIView?
    private var titleLabel: UILabel!
    private let openAIManager = OpenAIManager()  // Local instance instead of shared
    private var lastProcessedText: String?
    private var lastRewrittenText: String?
    
    
    // Hardcoded Prompt
    private let hardcodedPrompt = """
    You are a helpful assistant designed to process text input and rewrite it casually for improved clarity, context, and readability. Here are your guidelines:

    1. Correct transcription errors: Input may include typos, unclear phrasing, or mistakes. Infer the user's intended meaning.
    2. Preserve meaning and tone: Rewrite the text casually and naturally without altering the core message or tone.
    3. Enhance readability: Fix grammar, spelling, and awkward phrasing while keeping the text concise and easy to understand.
    4. Suggestions (optional): If applicable, provide suggestions for improving the text further.

    Respond in JSON format:
    {
        "updatedText": "<Your rewritten text here>",
        "suggestions": ["<Optional improvement suggestions>"]
    }
    """

    private let rephrasePrompt = """
    The user didn't like the following rephrase of their text. Here's the original text:
    "%@"

    And here's the rephrase they disliked:
    "%@"

    Analyze why the user might have disliked it and suggest an improved version.
    """

    // Add properties to track original selection
    private var originalText: String?
    private var originalContext: (before: String, after: String)?
    
    // Simplified replacement tracking
    private struct ReplacementTarget {
        let text: String
        let prefix: String
        let suffix: String
        let originalRange: NSRange?
        let documentHash: Int
        
        var isFullDocument: Bool {
            return prefix.isEmpty && suffix.isEmpty
        }
    }
    
    private struct SelectionState {
        let selectedText: String
        let utf16Range: NSRange
        let documentContext: String
        let documentHash: Int
        
        init?(proxy: UITextDocumentProxy) {
            guard let selected = proxy.selectedText else { return nil }
            
            self.selectedText = selected
            let beforeInput = proxy.documentContextBeforeInput ?? ""
            let afterInput = proxy.documentContextAfterInput ?? ""
            self.documentContext = beforeInput + afterInput
            
            // Calculate UTF-16 positions
            let beforeLength = beforeInput.utf16.count
            let selectedLength = selected.utf16.count
            self.utf16Range = NSRange(location: beforeLength - selectedLength, length: selectedLength)
            self.documentHash = documentContext.hashValue
        }
    }
    
    private var currentSelection: SelectionState?
    private var replacementTarget: ReplacementTarget?
    private let contextLength = 20 // Characters to capture for context

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        checkPermissions()
        print("Keyboard loaded successfully.")
    }

    private func checkPermissions() {
        if !hasFullAccess {
            showPermissionBanner()
        }
    }

    override var hasFullAccess: Bool {
        return super.hasFullAccess
    }

    private func showPermissionBanner() {
        let banner = UIView()
        banner.backgroundColor = UIColor.systemYellow.withAlphaComponent(0.9)
        banner.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(banner)
        
        let label = UILabel()
        label.text = "Enable Full Access in Settings for all features"
        label.textColor = .darkText
        label.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        label.numberOfLines = 2
        label.textAlignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false
        banner.addSubview(label)
        
        let settingsButton = UIButton(type: .system)
        settingsButton.setTitle("Open Settings", for: .normal)
        settingsButton.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .semibold)
        settingsButton.translatesAutoresizingMaskIntoConstraints = false
        settingsButton.addTarget(self, action: #selector(openSettings), for: .touchUpInside)
        banner.addSubview(settingsButton)
        
        NSLayoutConstraint.activate([
            banner.topAnchor.constraint(equalTo: view.topAnchor),
            banner.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            banner.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            banner.heightAnchor.constraint(equalToConstant: 44),
            
            label.centerYAnchor.constraint(equalTo: banner.centerYAnchor),
            label.leadingAnchor.constraint(equalTo: banner.leadingAnchor, constant: 8),
            
            settingsButton.centerYAnchor.constraint(equalTo: banner.centerYAnchor),
            settingsButton.trailingAnchor.constraint(equalTo: banner.trailingAnchor, constant: -8),
            settingsButton.leadingAnchor.constraint(equalTo: label.trailingAnchor, constant: 8)
        ])
        
        permissionBanner = banner
        
        // Adjust other UI elements
        let bannerHeight: CGFloat = 44
        NSLayoutConstraint.activate([
            refineButton.topAnchor.constraint(equalTo: banner.bottomAnchor, constant: 12)
        ])
    }

    @objc private func openSettings() {
        guard let url = URL(string: UIApplication.openSettingsURLString) else { return }
        extensionContext?.open(url, completionHandler: nil)
    }

    private func setupUI() {
        view.backgroundColor = UIColor(white: 0.95, alpha: 1.0)  // Light gray background
        
        // Add Title Label
        titleLabel = UILabel()
        titleLabel.text = "QuBoard"
        titleLabel.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        titleLabel.textColor = UIColor.systemBlue
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(titleLabel)
        
        // Add Refine Button with improved styling
        refineButton = UIButton(type: .system)
        refineButton.setTitle("ReWrite now!", for: .normal)
        refineButton.setTitleColor(.white, for: .normal)
        refineButton.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        refineButton.backgroundColor = UIColor.systemBlue
        refineButton.layer.cornerRadius = 20  // More rounded corners
        refineButton.layer.shadowColor = UIColor.black.cgColor
        refineButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        refineButton.layer.shadowRadius = 4
        refineButton.layer.shadowOpacity = 0.1
        refineButton.translatesAutoresizingMaskIntoConstraints = false
        refineButton.addTarget(self, action: #selector(refineAllText), for: .touchUpInside)
        view.addSubview(refineButton)

        // Add Activity Indicator with improved styling
        activityIndicator = UIActivityIndicatorView(style: .medium)
        activityIndicator.color = UIColor.systemBlue
        activityIndicator.translatesAutoresizingMaskIntoConstraints = false
        activityIndicator.hidesWhenStopped = true
        view.addSubview(activityIndicator)

        // Add Suggestions Stack with improved styling
        suggestionsStack = UIStackView()
        suggestionsStack.axis = .vertical
        suggestionsStack.spacing = 10  // Increased spacing
        suggestionsStack.alignment = .fill
        suggestionsStack.distribution = .fill
        suggestionsStack.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(suggestionsStack)

        // Updated Constraints
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: view.topAnchor, constant: 12),
            titleLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            titleLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            
            refineButton.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16),
            refineButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            refineButton.widthAnchor.constraint(equalToConstant: 180),  // Wider button
            refineButton.heightAnchor.constraint(equalToConstant: 44),  // Taller button

            activityIndicator.topAnchor.constraint(equalTo: refineButton.bottomAnchor, constant: 12),
            activityIndicator.centerXAnchor.constraint(equalTo: view.centerXAnchor),

            suggestionsStack.topAnchor.constraint(equalTo: activityIndicator.bottomAnchor, constant: 12),
            suggestionsStack.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),  // Increased padding
            suggestionsStack.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16)
        ])
    }

    @objc private func refineAllText() {
        guard hasFullAccess else {
            showPermissionBanner()
            addSuggestionLabel("Full Access is required to process text. Please enable it in Settings.")
            return
        }

        if let target = captureContext() {
            replacementTarget = target
            processText(target.text)
            return
        }

        // Fallback to clipboard or full context as before
        if let clipboardText = UIPasteboard.general.string, !clipboardText.isEmpty {
            processText(clipboardText)
            return
        }

        let beforeText = textDocumentProxy.documentContextBeforeInput ?? ""
        let afterText = textDocumentProxy.documentContextAfterInput ?? ""
        let fullText = beforeText + afterText

        if fullText.isEmpty {
            promptUserToCopyText()
            return
        }

        processText(fullText)
    }

    private func promptUserToCopyText() {
        clearSuggestions()
        addSuggestionLabel("Please select and copy the text you want to process, then tap 'Process Text' again.")
    }

    private func processText(_ text: String) {
        guard hasFullAccess else {
            showPermissionBanner()
            return
        }

        activityIndicator.startAnimating()
        clearSuggestions()
        lastProcessedText = text

        let prompt = "\(hardcodedPrompt) Input: \(text)"
        print("Processing text... \(text)")

        openAIManager.generateText(for: prompt) { [weak self] result in
            DispatchQueue.main.async {
                self?.activityIndicator.stopAnimating()
                switch result {
                case .success(let response):
                    self?.handleLLMResponse(response, originalText: text)
                case .failure(let error):
                    print("Error processing text: \(error)")
                    self?.handleError(error)
                }
            }
        }
    }

    private func handleError(_ error: Error) {
        clearSuggestions()
        addSuggestionLabel("Error: \(error.localizedDescription)")
        addSuggestionLabel("Please try again or check your internet connection.")
    }

    private func handleLLMResponse(_ response: String, originalText: String) {
        guard let data = response.data(using: .utf8),
              let llmResponse = try? JSONDecoder().decode(LLMResponse.self, from: data) else {
            print("Failed to decode LLM response.")
            addSuggestionLabel("Failed to process response.")
            return
        }

        lastRewrittenText = llmResponse.updatedText
        showPreviewController(originalText: originalText, refinedText: llmResponse.updatedText, suggestions: llmResponse.suggestions)
        
        // Store suggestions for later use
        if !llmResponse.suggestions.isEmpty {
            // We'll show these in the preview controller if needed
            print("Received suggestions: \(llmResponse.suggestions)")
        }
    }

    private func showPreviewController(originalText: String, refinedText: String, suggestions: [String]) {
        let previewVC = TextPreviewViewController(
            originalText: originalText,
            refinedText: refinedText,
            suggestions: suggestions,
            onInsert: { [weak self] text in
                self?.replaceText(with: text)
                self?.dismissPreviewController()
            },
            onRephrase: { [weak self] in
                self?.dismissPreviewController()
                self?.handleRephrase()
            },
            onDismiss: { [weak self] in
                self?.dismissPreviewController()
            }
        )
        
        // Add preview controller
        addChild(previewVC)
        previewVC.view.frame = view.bounds
        previewVC.view.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(previewVC.view)
        
        // Setup constraints
        NSLayoutConstraint.activate([
            previewVC.view.topAnchor.constraint(equalTo: view.topAnchor),
            previewVC.view.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            previewVC.view.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            previewVC.view.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        previewVC.didMove(toParent: self)
    }
    
    private func dismissPreviewController() {
        children.forEach { child in
            child.willMove(toParent: nil)
            child.view.removeFromSuperview()
            child.removeFromParent()
        }
    }

    private func handleRephrase() {
        guard let originalText = lastProcessedText,
              let previousRephrase = lastRewrittenText else {
            return
        }

        let prompt = String(format: rephrasePrompt, originalText, previousRephrase)
        activityIndicator.startAnimating()
        
        openAIManager.generateText(for: prompt) { [weak self] result in
            DispatchQueue.main.async {
                self?.activityIndicator.stopAnimating()
                switch result {
                case .success(let response):
                    self?.handleLLMResponse(response, originalText: originalText)
                case .failure(let error):
                    print("Error processing rephrase: \(error)")
                    self?.handleError(error)
                }
            }
        }
    }

    private func replaceText(with refinedText: String) {
        guard !textDocumentProxy.isSecureTextEntry! else {
            print("🔒 Blocked: Attempted replacement in secure field")
            addSuggestionLabel("Text replacement disabled in secure fields")
            return
        }
        
        guard let target = replacementTarget else {
            print("📝 Simple Insert: '\(refinedText)'")
            textDocumentProxy.insertText(refinedText)
            return
        }
        
        print("🎯 Starting replacement:")
        print("  • Original text: '\(target.text)'")
        print("  • Refined text: '\(refinedText)'")
        print("  • Context: prefix='\(target.prefix)', suffix='\(target.suffix)'")
        
        // Handle full document replacement
        if target.isFullDocument {
            print("📄 Full document replacement")
            replaceFullDocument(with: refinedText)
            return
        }
        
        let fullText = (textDocumentProxy.documentContextBeforeInput ?? "") +
                      (textDocumentProxy.documentContextAfterInput ?? "")
        
        print("📄 Full context: '\(fullText)'")
        
        // 1. Try to reselect original text if possible
        if let originalRange = target.originalRange,
           fullText.hashValue == target.documentHash {
            print("✅ Using original selection range")
            replaceUsingNSRange(originalRange, text: refinedText)
            return
        }
        
        // 2. Try context-aware replacement
        if let match = fullText.range(of: buildSearchPattern(target: target), options: .regularExpression) {
            print("✅ Context match found at position: \(fullText.distance(from: fullText.startIndex, to: match.lowerBound))")
            replaceUsingNSRange(NSRange(match, in: fullText), text: refinedText)
            return
        }
        
        // 3. Fallback to document scan with word boundaries
        if let range = fullText.range(of: "\\b\(target.text.regexSafe)\\b", options: .regularExpression) {
            print("⚠️ Using fallback word boundary match")
            replaceUsingNSRange(NSRange(range, in: fullText), text: refinedText)
            return
        }
        
        // 4. Final fallback - direct insertion
        print("⚠️ No match found, using direct insertion")
        textDocumentProxy.insertText(refinedText)
        
        replacementTarget = nil
        currentSelection = nil
    }
    
    private func replaceFullDocument(with text: String) {
        // Move to start of document
        while textDocumentProxy.documentContextBeforeInput?.isEmpty == false {
            textDocumentProxy.adjustTextPosition(byCharacterOffset: -1)
        }
        
        // Delete all content
        while textDocumentProxy.hasText {
            textDocumentProxy.deleteBackward()
        }
        
        // Insert new text
        textDocumentProxy.insertText(text)
        
        // Verify replacement
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            let currentText = (self.textDocumentProxy.documentContextBeforeInput ?? "") +
                            (self.textDocumentProxy.documentContextAfterInput ?? "")
            if currentText != text {
                print("❌ Full document replacement verification failed")
                // Retry once
                self.textDocumentProxy.deleteBackward()
                self.textDocumentProxy.insertText(text)
            } else {
                print("✅ Full document replacement verified")
            }
        }
    }

    private func replaceUsingNSRange(_ nsRange: NSRange, text: String) {
        print("🔄 Replacing range: location=\(nsRange.location), length=\(nsRange.length)")
        
        guard let proxy = textDocumentProxy as? UITextInput else {
            print("❌ Failed to get UITextInput proxy")
            textDocumentProxy.insertText(text)
            return
        }
        
        // Convert to UTF-16 positions
        guard let startPos = proxy.position(from: proxy.beginningOfDocument, offset: nsRange.location),
              let endPos = proxy.position(from: startPos, offset: nsRange.length),
              let range = proxy.textRange(from: startPos, to: endPos) else {
            print("❌ Failed to create text range")
            textDocumentProxy.insertText(text)
            return
        }
        
        // Store current state for verification
        let beforeText = textDocumentProxy.documentContextBeforeInput ?? ""
        let afterText = textDocumentProxy.documentContextAfterInput ?? ""
        let originalText = beforeText + afterText
        
        // Perform replacement
        proxy.selectedTextRange = range
        proxy.deleteBackward()
        proxy.insertText(text)
        
        // Verify replacement
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            print("🔍 Verifying replacement at UTF-16 position \(nsRange.location)")
            if !self.verifyReplacement(expected: text, at: nsRange.location, originalText: originalText) {
                print("❌ Verification failed - attempting recovery")
                self.handleReplacementFailure(originalText: originalText, newText: text)
            } else {
                print("✅ Replacement verified successfully")
            }
        }
    }

    private func verifyReplacement(expected: String, at utf16Position: Int, originalText: String) -> Bool {
        let currentContext = textDocumentProxy.documentContextBeforeInput ?? ""
        
        // Convert position to UTF-16
        let utf16Context = currentContext.utf16
        guard utf16Position <= utf16Context.count else {
            print("❌ Verification failed: UTF-16 position \(utf16Position) exceeds context length \(utf16Context.count)")
            return false
        }
        
        // Extract the actual text at the position
        let startIndex = String.Index(utf16Offset: max(0, utf16Position), in: currentContext)
        let endIndex = String.Index(utf16Offset: min(utf16Position + expected.utf16.count, currentContext.utf16.count), in: currentContext)
        let actual = String(currentContext[startIndex..<endIndex])
        
        print("🔍 Verification:")
        print("  • Expected: '\(expected)'")
        print("  • Actual: '\(actual)'")
        
        return actual == expected
    }
    
    private func handleReplacementFailure(originalText: String, newText: String) {
        // First, try to restore original text
        while textDocumentProxy.hasText {
            textDocumentProxy.deleteBackward()
        }
        
        // Then try replacement strategies in order
        if let target = replacementTarget {
            // 1. Try context pattern
            if let match = originalText.range(of: buildSearchPattern(target: target), options: .regularExpression) {
                replaceUsingNSRange(NSRange(match, in: originalText), text: newText)
                return
            }
            
            // 2. Try word boundary match
            if let range = originalText.range(of: "\\b\(target.text.regexSafe)\\b", options: .regularExpression) {
                replaceUsingNSRange(NSRange(range, in: originalText), text: newText)
                return
            }
        }
        
        // Final fallback: insert at current position
        textDocumentProxy.insertText(newText)
    }

    private func captureContext() -> ReplacementTarget? {
        // Try to capture current selection state
        if let selection = SelectionState(proxy: textDocumentProxy) {
            currentSelection = selection
            let maxContext = 20
            let before = textDocumentProxy.documentContextBeforeInput ?? ""
            let after = textDocumentProxy.documentContextAfterInput ?? ""
            
            return ReplacementTarget(
                text: selection.selectedText,
                prefix: before.count > maxContext ? String(before.suffix(maxContext)) : before,
                suffix: after.count > maxContext ? String(after.prefix(maxContext)) : after,
                originalRange: selection.utf16Range,
                documentHash: selection.documentHash
            )
        }
        
        // Handle full document replacement when no text is selected
        let beforeText = textDocumentProxy.documentContextBeforeInput ?? ""
        let afterText = textDocumentProxy.documentContextAfterInput ?? ""
        let fullText = beforeText + afterText
        
        if !fullText.isEmpty {
            return ReplacementTarget(
                text: fullText,
                prefix: "",
                suffix: "",
                originalRange: NSRange(location: 0, length: fullText.utf16.count),
                documentHash: fullText.hashValue
            )
        }
        
        return nil
    }

    private func clearSuggestions() {
        suggestionsStack.arrangedSubviews.forEach { $0.removeFromSuperview() }
    }

    private func addSuggestionLabel(_ text: String) {
        let label = UILabel()
        label.text = text
        label.textColor = .darkGray
        label.font = UIFont.systemFont(ofSize: 13)  // Slightly larger font
        label.numberOfLines = 0
        label.textAlignment = .center
        label.layer.cornerRadius = 8
        label.layer.masksToBounds = true
        label.layoutMargins = UIEdgeInsets(top: 8, left: 8, bottom: 8, right: 8)  // Add padding
        suggestionsStack.addArrangedSubview(label)
    }

    private func addSuggestionButton(_ suggestion: String) {
        let button = UIButton(type: .system)
        button.setTitle(suggestion, for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)  // Larger font
        button.backgroundColor = UIColor.systemBlue.withAlphaComponent(0.8)
        button.layer.cornerRadius = 12
        button.layer.shadowColor = UIColor.black.cgColor
        button.layer.shadowOffset = CGSize(width: 0, height: 1)
        button.layer.shadowRadius = 2
        button.layer.shadowOpacity = 0.1
        button.contentEdgeInsets = UIEdgeInsets(top: 10, left: 16, bottom: 10, right: 16)  // Increased padding
        button.addTarget(self, action: #selector(applySuggestion(_:)), for: .touchUpInside)
        suggestionsStack.addArrangedSubview(button)
    }

    @objc private func applySuggestion(_ sender: UIButton) {
        guard let suggestion = sender.title(for: .normal) else { return }
        print("Applying suggestion: \(suggestion)")

        // Update the text with the selected suggestion
        let prompt = "\(hardcodedPrompt) Input: \(suggestion)"
        activityIndicator.startAnimating()
        clearSuggestions()

        openAIManager.generateText(for: prompt) { [weak self] result in
            DispatchQueue.main.async {
                self?.activityIndicator.stopAnimating()
                switch result {
                case .success(let response):
                    self?.handleLLMResponse(response, originalText: self?.lastProcessedText ?? "")
                case .failure(let error):
                    print("Error applying suggestion: \(error)")
                    self?.addSuggestionLabel("Error applying suggestion.")
                }
            }
        }
    }

    private func replaceTextInDocument(from start: Int, to end: Int, with text: String) {
        guard let proxy = textDocumentProxy as? UITextInput else { return }
        
        // Convert to UTF-16 positions
        guard let startPos = proxy.position(from: proxy.beginningOfDocument, offset: start),
              let endPos = proxy.position(from: startPos, offset: end - start),
              let range = proxy.textRange(from: startPos, to: endPos) else {
            textDocumentProxy.insertText(text)
            return
        }
        
        proxy.selectedTextRange = range
        proxy.deleteBackward()
        proxy.insertText(text)
    }

    private func buildSearchPattern(target: ReplacementTarget) -> String {
        let prefixContext = NSRegularExpression.escapedPattern(for: target.prefix)
        let suffixContext = NSRegularExpression.escapedPattern(for: target.suffix)
        
        let pattern = """
        (?<=\(prefixContext))   # Look behind for prefix context
        \\b                     # Word boundary
        \(target.text.regexSafe)
        \\b                     # Word boundary
        (?=\(suffixContext))    # Look ahead for suffix context
        """
        
        print("🔍 Generated pattern: '\(pattern)'")
        return pattern
    }
}

// JSON Response Model
struct LLMResponse: Codable {
    let updatedText: String
    let suggestions: [String]
}

class TextPreviewViewController: UIViewController {
    private let originalText: String
    private let refinedText: String
    private let suggestions: [String]
    private let onInsert: (String) -> Void
    private let onRephrase: () -> Void
    private let onDismiss: () -> Void
    
    private var textView: UITextView!
    private var actionButtonsStack: UIStackView!
    private var navigationBar: UINavigationBar!
    private var suggestionsCollectionView: UICollectionView!
    
    init(originalText: String, refinedText: String, suggestions: [String], onInsert: @escaping (String) -> Void, onRephrase: @escaping () -> Void, onDismiss: @escaping () -> Void) {
        self.originalText = originalText
        self.refinedText = refinedText
        self.suggestions = suggestions
        self.onInsert = onInsert
        self.onRephrase = onRephrase
        self.onDismiss = onDismiss
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    private func setupUI() {
        view.backgroundColor = UIColor(white: 0.95, alpha: 1.0)
        
        // Setup Navigation Bar
        navigationBar = UINavigationBar()
        navigationBar.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(navigationBar)
        
        let navigationItem = UINavigationItem(title: "Preview")
        let closeButton = UIBarButtonItem(
            image: UIImage(systemName: "xmark.circle.fill"),
            style: .plain,
            target: self,
            action: #selector(dismissPreview)
        )
        navigationItem.leftBarButtonItem = closeButton
        navigationBar.items = [navigationItem]
        
        // Setup comparison view
        let comparisonStack = UIStackView()
        comparisonStack.axis = .vertical
        comparisonStack.spacing = 16
        comparisonStack.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(comparisonStack)
        
        let originalLabel = createLabel(text: "Original:", color: .gray)
        let originalTextView = createTextView(text: originalText)
        let refinedLabel = createLabel(text: "Refined:", color: .systemBlue)
        let refinedTextView = createTextView(text: refinedText)
        
        comparisonStack.addArrangedSubview(originalLabel)
        comparisonStack.addArrangedSubview(originalTextView)
        comparisonStack.addArrangedSubview(refinedLabel)
        comparisonStack.addArrangedSubview(refinedTextView)
        
        // Setup Suggestions Collection View if there are suggestions
        if !suggestions.isEmpty {
            let suggestionsLabel = createLabel(text: "Suggestions:", color: .systemGreen)
            comparisonStack.addArrangedSubview(suggestionsLabel)
            
            let layout = UICollectionViewFlowLayout()
            layout.scrollDirection = .horizontal
            layout.estimatedItemSize = UICollectionViewFlowLayout.automaticSize
            layout.minimumInteritemSpacing = 8
            layout.minimumLineSpacing = 8
            
            suggestionsCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
            suggestionsCollectionView.backgroundColor = .clear
            suggestionsCollectionView.translatesAutoresizingMaskIntoConstraints = false
            suggestionsCollectionView.delegate = self
            suggestionsCollectionView.dataSource = self
            suggestionsCollectionView.register(SuggestionCell.self, forCellWithReuseIdentifier: "SuggestionCell")
            suggestionsCollectionView.showsHorizontalScrollIndicator = false
            comparisonStack.addArrangedSubview(suggestionsCollectionView)
            
            suggestionsCollectionView.heightAnchor.constraint(equalToConstant: 44).isActive = true
        }
        
        // Setup Action Buttons
        actionButtonsStack = UIStackView()
        actionButtonsStack.axis = .horizontal
        actionButtonsStack.spacing = 12
        actionButtonsStack.distribution = .fillEqually
        actionButtonsStack.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(actionButtonsStack)
        
        let insertButton = createActionButton(
            title: "Insert",
            backgroundColor: UIColor(red: 0.2, green: 0.8, blue: 0.7, alpha: 1.0),
            action: #selector(insertText)
        )
        
        let rephraseButton = createActionButton(
            title: "Rephrase",
            backgroundColor: .systemBlue,
            action: #selector(rephraseText)
        )
        
        actionButtonsStack.addArrangedSubview(insertButton)
        actionButtonsStack.addArrangedSubview(rephraseButton)
        
        // Layout constraints
        NSLayoutConstraint.activate([
            navigationBar.topAnchor.constraint(equalTo: view.topAnchor),
            navigationBar.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            navigationBar.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            
            comparisonStack.topAnchor.constraint(equalTo: navigationBar.bottomAnchor, constant: 16),
            comparisonStack.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            comparisonStack.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16),
            
            actionButtonsStack.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -16),
            actionButtonsStack.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 16),
            actionButtonsStack.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -16),
            actionButtonsStack.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    private func createLabel(text: String, color: UIColor) -> UILabel {
        let label = UILabel()
        label.text = text
        label.textColor = color
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        return label
    }
    
    private func createTextView(text: String) -> UITextView {
        let textView = UITextView()
        textView.text = text
        textView.font = UIFont.systemFont(ofSize: 16)
        textView.backgroundColor = .white
        textView.layer.cornerRadius = 8
        textView.layer.masksToBounds = true
        textView.isEditable = false
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 8, bottom: 8, right: 8)
        textView.heightAnchor.constraint(greaterThanOrEqualToConstant: 80).isActive = true
        return textView
    }
    
    private func createActionButton(title: String, backgroundColor: UIColor, action: Selector) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        button.backgroundColor = backgroundColor
        button.layer.cornerRadius = 22
        button.addTarget(self, action: action, for: .touchUpInside)
        return button
    }
    
    @objc private func dismissPreview() {
        onDismiss()
    }
    
    @objc private func insertText() {
        onInsert(refinedText)
    }
    
    @objc private func rephraseText() {
        onRephrase()
    }
}

// MARK: - Collection View
extension TextPreviewViewController: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return suggestions.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "SuggestionCell", for: indexPath) as! SuggestionCell
        cell.configure(with: suggestions[indexPath.item])
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let suggestion = suggestions[indexPath.item]
        onInsert(suggestion)
    }
}

// MARK: - Suggestion Cell
class SuggestionCell: UICollectionViewCell {
    private let label = UILabel()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .systemBlue
        layer.cornerRadius = 16
        
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(label)
        
        NSLayoutConstraint.activate([
            label.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            label.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            label.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 12),
            label.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -12)
        ])
    }
    
    func configure(with text: String) {
        label.text = text
    }
}

