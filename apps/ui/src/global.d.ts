declare module "@amraneze/react-instagram-login" {
  import React from "react";

  interface InstagramLoginProps {
    clientId: string;
    buttonText?: string;
    onSuccess: (response: any) => void;
    onFailure?: (error: any) => void;
    scope?: string;
    cssClass?: string;
    children?: React.ReactNode;
    width: number;
  }

  export const InstagramLogin: React.FC<InstagramLoginProps>;
}

interface Window {
  handleSubscriptionCompletion?: (event: any) => void;
}
