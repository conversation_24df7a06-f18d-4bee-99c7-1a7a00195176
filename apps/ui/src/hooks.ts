import { useCallback, useEffect, useMemo, useRef, useState } from "react";

export function useResolver<T>(
  resolver: () => Promise<T>,
  deps: React.DependencyList = []
) {
  const [value, setValue] = useState<null | T>(null);
  const [loading, setLoading] = useState(true);
  const mountedRef = useRef(true);

  const reload = useCallback(async () => {
    setLoading(true);
    try {
      const result = await resolver();
      setValue(result);
    } catch (err) {
      console.error("useResolver error:", err);
    } finally {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps); // Use the deps parameter instead of depending on resolver function

  // Run on mount and when deps change
  useEffect(() => {
    mountedRef.current = true;
    reload();

    // Cleanup function
    return () => {
      mountedRef.current = false;
    };
  }, [reload]);

  return useMemo(() => [value, setValue, reload, loading] as const, [value, reload, loading]);
}

export function useDebounceControl<T>(
  value: T,
  onChange: (value: T) => void,
  ms = 400
) {
  const changeRef = useRef(onChange);
  changeRef.current = onChange;
  const valueRef = useRef(value);
  valueRef.current = value;
  const timeoutId = useRef<ReturnType<typeof setTimeout>>();
  const synced = useRef(true);
  const [temp, setTemp] = useState<T>(value);
  useEffect(() => {
    clearTimeout(timeoutId.current);
    if (valueRef.current !== temp) {
      timeoutId.current = setTimeout(() => {
        changeRef.current(temp);
        synced.current = false;
      }, ms);
    }
  }, [temp, ms]);
  useEffect(() => {
    if (!synced.current) {
      setTemp(value);
      synced.current = true;
    }
  }, [value]);
  return [temp, setTemp] as const;
}
