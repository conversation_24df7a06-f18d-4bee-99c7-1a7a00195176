import { useState, useEffect, useContext } from "react";
import { LocationContext } from "../contexts";
import api from "../api";

interface AgentQuestionTemplate {
  [agentId: string]: {
    name: string;
    role: string;
    capabilities: string[];
    questions: {
      basic: string[];
      withPosData: string[];
      withCustomerData: string[];
      withProductData: string[];
      withCompetitorData: string[];
      withCampaignData: string[];
    };
  };
}

const AGENT_QUESTION_TEMPLATES: AgentQuestionTemplate = {
  "1": {
    // SMOKEY - AI Budtender & Customer Experience
    name: "SMOKEY",
    role: "AI Budtender & Customer Experience",
    capabilities: [
      "product_recommendations",
      "customer_service",
      "inventory_aware",
    ],
    questions: {
      basic: [
        "What products do you recommend for beginners?",
        "Tell me about your available strains",
        "What's the difference between indica and sativa?",
      ],
      withPosData: [
        "What are your most popular products this month?",
        "Which strains are trending with customers?",
        "Show me your best-selling edibles",
      ],
      withCustomerData: [
        "What products do returning customers prefer?",
        "How do customer preferences vary by age group?",
        "What do VIP customers typically purchase?",
      ],
      withProductData: [
        "What high-THC products do you have in stock?",
        "Show me your CBD-dominant products",
        "What's your strongest concentrate available?",
      ],
      withCompetitorData: [],
      withCampaignData: [],
    },
  },
  "2": {
    // CRAIG - Marketing Automation
    name: "CRAIG",
    role: "Marketing Automation",
    capabilities: [
      "campaign_creation",
      "performance_analysis",
      "email_marketing",
    ],
    questions: {
      basic: [
        "Help me create a new customer welcome campaign",
        "What are some good marketing campaign ideas?",
        "How can I improve customer engagement?",
      ],
      withPosData: [
        "Create a campaign for our top-selling products",
        "Design a promotion based on recent sales trends",
        "What products should I feature in this week's newsletter?",
      ],
      withCustomerData: [
        "Help me segment customers for targeted campaigns",
        "Create a loyalty program email for VIP customers",
        "Design a re-engagement campaign for inactive customers",
      ],
      withProductData: [
        "Create a campaign highlighting our new concentrates",
        "Design a promotion for slow-moving inventory",
        "Help me market our premium product line",
      ],
      withCompetitorData: [],
      withCampaignData: [
        "Analyze the performance of our recent campaigns",
        "What's our best-performing email template?",
        "How can I improve our campaign open rates?",
      ],
    },
  },
  "3": {
    // POPS - Business Intelligence & Strategy
    name: "POPS",
    role: "Business Intelligence & Strategy",
    capabilities: [
      "sales_analysis",
      "trend_identification",
      "performance_tracking",
    ],
    questions: {
      basic: [
        "Give me an overview of business performance",
        "What are some areas for improvement?",
        "Help me understand my key metrics",
      ],
      withPosData: [
        "What are our sales trends over the last quarter?",
        "Which days of the week are most profitable?",
        "How is our revenue performing compared to last month?",
        "What time of day do we see peak sales?",
        "Which budtenders are top performers?",
      ],
      withCustomerData: [
        "How is our customer retention rate?",
        "What's our average customer lifetime value?",
        "Which customer segments are most valuable?",
      ],
      withProductData: [
        "Which product categories drive the most revenue?",
        "What are our highest-margin products?",
        "Which products have the best inventory turnover?",
      ],
      withCompetitorData: [],
      withCampaignData: [
        "How effective are our marketing campaigns?",
        "What's our customer acquisition cost?",
        "Which campaigns drive the most sales?",
      ],
    },
  },
  "4": {
    // EZAL - Market Intelligence
    name: "EZAL",
    role: "Market Intelligence",
    capabilities: [
      "competitor_analysis",
      "market_trends",
      "price_optimization",
    ],
    questions: {
      basic: [
        "Help me understand my market position",
        "What are current industry trends?",
        "How can I stay competitive?",
      ],
      withPosData: [
        "How do our prices compare to market rates?",
        "Which products give us a competitive advantage?",
        "What pricing strategies should we consider?",
      ],
      withCustomerData: [],
      withProductData: [
        "How does our product selection compare to competitors?",
        "What products are missing from our catalog?",
        "Which categories should we expand?",
      ],
      withCompetitorData: [
        "How do our prices compare to nearby dispensaries?",
        "What products are competitors featuring?",
        "Which competitors are our biggest threats?",
        "Analyze the competitive landscape in our area",
      ],
      withCampaignData: [],
    },
  },
  "5": {
    // MONEY MIKE - Financial Analytics
    name: "MONEY MIKE",
    role: "Financial Analytics",
    capabilities: [
      "margin_analysis",
      "revenue_forecasting",
      "cost_optimization",
    ],
    questions: {
      basic: [
        "How can I improve profitability?",
        "What are my biggest financial opportunities?",
        "Help me understand my cost structure",
      ],
      withPosData: [
        "What are our profit margins by category?",
        "Which products are most profitable?",
        "How much revenue did we generate last month?",
        "What's our average transaction value?",
        "Analyze our daily revenue trends",
      ],
      withCustomerData: [
        "What's our customer acquisition cost?",
        "How much does each customer segment contribute to profit?",
        "What's our customer lifetime value?",
      ],
      withProductData: [
        "Which products have the highest profit margins?",
        "What's our inventory turnover rate?",
        "Which categories are underperforming financially?",
      ],
      withCompetitorData: [],
      withCampaignData: [
        "What's the ROI on our marketing campaigns?",
        "How much revenue do our promotions generate?",
        "Which marketing channels are most cost-effective?",
      ],
    },
  },
  "6": {
    // MRS. PARKER - Customer Relations
    name: "MRS. PARKER",
    role: "Customer Relations",
    capabilities: [
      "vip_management",
      "loyalty_optimization",
      "customer_analysis",
    ],
    questions: {
      basic: [
        "How can I improve customer satisfaction?",
        "What loyalty program strategies work best?",
        "Help me identify VIP customers",
      ],
      withPosData: [
        "Who are our highest-value customers?",
        "What do our top customers typically purchase?",
        "How often do customers make repeat purchases?",
      ],
      withCustomerData: [
        "Analyze our customer demographics",
        "Which customers are at risk of churning?",
        "How can I personalize the customer experience?",
        "What are our customer satisfaction trends?",
        "Help me segment customers for better service",
      ],
      withProductData: [
        "What products do loyal customers prefer?",
        "Which products create customer loyalty?",
        "What should I recommend to VIP customers?",
      ],
      withCompetitorData: [],
      withCampaignData: [
        "How effective are our loyalty campaigns?",
        "Which promotions drive customer retention?",
        "What messaging resonates with our best customers?",
      ],
    },
  },
  "7": {
    // DEEBO - Security, Compliance & Quality Assurance
    name: "DEEBO",
    role: "Security, Compliance & Quality Assurance",
    capabilities: [
      "compliance_monitoring",
      "security_protocols",
      "quality_control",
    ],
    questions: {
      basic: [
        "Are we compliant with all regulations?",
        "What security measures should we implement?",
        "Help me understand compliance requirements",
      ],
      withPosData: [],
      withCustomerData: [],
      withProductData: [
        "Are all our products properly tested?",
        "Which products need compliance review?",
        "Help me verify product quality standards",
      ],
      withCompetitorData: [],
      withCampaignData: [],
    },
  },
};

export interface GeneratedQuestion {
  prompt: string;
  agent: string;
  category: string;
  description?: string;
}

export function useAgentBasedQuestions() {
  const [location] = useContext(LocationContext);
  const [questions, setQuestions] = useState<GeneratedQuestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const generateQuestions = async () => {
      if (!location?.id) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Get agent availability data
        const agentAvailability = await api.agents.getAvailability(location.id);

        // Generate questions based on available agents and their data
        const generatedQuestions: GeneratedQuestion[] = [];

        // Process available agents (full functionality)
        agentAvailability.available.forEach((agent: any) => {
          const template = AGENT_QUESTION_TEMPLATES[agent.agentId];
          if (!template) return;

          // Always include basic questions
          const basicQuestions = template.questions.basic.map((q) => ({
            prompt: q,
            agent: template.name,
            category: "basic",
            description: `Ask ${template.name} (${template.role})`,
          }));
          generatedQuestions.push(...basicQuestions);

          // Add data-specific questions based on what data is available
          Object.entries(agent.dataStatus).forEach(
            ([dataType, status]: any) => {
              if (!status.available) return;

              let questionKey: keyof typeof template.questions | null = null;

              // Map data types to question categories
              switch (dataType) {
                case "pos_data":
                  questionKey = "withPosData";
                  break;
                case "customer_data":
                  questionKey = "withCustomerData";
                  break;
                case "product_data":
                  questionKey = "withProductData";
                  break;
                case "competitor_data":
                  questionKey = "withCompetitorData";
                  break;
                case "campaign_data":
                  questionKey = "withCampaignData";
                  break;
              }

              if (questionKey && template.questions[questionKey]) {
                const dataQuestions = template.questions[questionKey].map(
                  (q) => ({
                    prompt: q,
                    agent: template.name,
                    category: dataType,
                    description: `Ask ${template.name} about ${dataType.replace(
                      "_",
                      " "
                    )}`,
                  })
                );
                generatedQuestions.push(...dataQuestions);
              }
            }
          );
        });

        // Process partially available agents (limited functionality)
        agentAvailability.partial.forEach((agent: any) => {
          const template = AGENT_QUESTION_TEMPLATES[agent.agentId];
          if (!template) return;

          // Include basic questions for partially available agents
          // Users will get helpful error messages if they try questions that need missing data
          const basicQuestions = template.questions.basic
            .slice(0, 2) // Show up to 2 questions to give users visibility into capabilities
            .map((q) => ({
              prompt: q,
              agent: `${template.name} (Limited)`,
              category: "basic",
              description: `Ask ${template.name} (${template.role}) - Limited data available`,
            }));
          generatedQuestions.push(...basicQuestions);
        });

        // Shuffle and limit to 8-10 questions for better UX
        const shuffledQuestions = generatedQuestions
          .sort(() => 0.5 - Math.random())
          .slice(0, 8);

        setQuestions(shuffledQuestions);
      } catch (err) {
        console.error("Error generating agent-based questions:", err);
        setError("Failed to generate contextual questions");

        // Fallback to basic questions
        setQuestions([
          {
            prompt: "What can you help me with?",
            agent: "Assistant",
            category: "basic",
          },
          {
            prompt: "Show me an overview of my business",
            agent: "Assistant",
            category: "basic",
          },
          {
            prompt: "What are some ways to improve my dispensary?",
            agent: "Assistant",
            category: "basic",
          },
        ]);
      } finally {
        setLoading(false);
      }
    };

    generateQuestions();
  }, [location?.id]);

  return { questions, loading, error };
}
