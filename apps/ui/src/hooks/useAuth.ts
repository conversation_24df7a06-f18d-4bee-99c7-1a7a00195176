import { useContext, useEffect, useState } from "react";
import { onAuthStateChanged, User, signOut } from "@firebase/auth";
import { useNavigate } from "react-router-dom";
import { auth } from "../config/firebase";
import useValidateSubscription from "./useValidateSubscription";
import { exit } from "process";
import { loadUsetifulScript, setUsetifulTags } from "usetiful-sdk";
import { LocationContext } from "../contexts";

const useAuth = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [displayName, setDisplayName] = useState<string | null>(null);
  const [photoURL, setPhotoURL] = useState<string | null>(null);
  const isAuthenticated = !!user;
  const [location] = useContext(LocationContext);

  const navigate = useNavigate();

  // Initialize Usetiful script
  useEffect(() => {
    loadUsetifulScript("8782067d3e62f50c8722ad8ee9085cce");
  }, []);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (currentUser: User | null) => {
      setUser(currentUser);
      setDisplayName(currentUser?.displayName || null);
      setPhotoURL(currentUser?.photoURL || null);
      // Set Usetiful user tags when auth state changes
      if (currentUser) {
        setUsetifulTags({
          userId: currentUser.uid,
          locationId: location.id || null,
          firstName: currentUser.displayName?.split(" ")[0] || "",
          lastName:
            currentUser.displayName?.split(" ").slice(1).join(" ") || "",
          email: currentUser.email || "",
        });
      }

      if (!currentUser && !window.location.pathname.includes("/widget")) {
        navigate("/login");
        setIsLoading(false);
        exit();
      }
    });

    return () => unsubscribe();
  }, [navigate, location.id]);

  const Logout = async () => {
    console.log("Logging out");
    await signOut(auth);
  };

  return {
    isAuthenticated,
    isLoading,
    user,
    displayName,
    photoURL,
    Logout,
  };
};

export default useAuth;
