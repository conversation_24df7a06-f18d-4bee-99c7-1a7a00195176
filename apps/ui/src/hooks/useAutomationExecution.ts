import { useState, useCallback } from "react";
import { PlanStep } from "../types/automation";
import { StepStatus, StepError } from "../components/automation/AutomationStep";

interface ExecutionState {
  currentStepIndex: number | null;
  stepStatuses: Record<number, StepStatus>;
  stepErrors: Record<number, StepError | undefined>;
  createdResources: Record<number, { id: number; type: string }>;
}

interface UseAutomationExecutionProps {
  steps: PlanStep[];
  onComplete?: () => void;
  onError?: (error: Error) => void;
}

export function useAutomationExecution({
  steps,
  onComplete,
  onError,
}: UseAutomationExecutionProps) {
  const [executionState, setExecutionState] = useState<ExecutionState>({
    currentStepIndex: null,
    stepStatuses: {},
    stepErrors: {},
    createdResources: {},
  });

  const executeStep = useCallback(
    async (index: number) => {
      try {
        setExecutionState((prev) => ({
          ...prev,
          currentStepIndex: index,
          stepStatuses: {
            ...prev.stepStatuses,
            [index]: "in-progress",
          },
          stepErrors: {
            ...prev.stepErrors,
            [index]: undefined,
          },
        }));

        const step = steps[index];
        // Here you would implement the actual step execution logic
        // For now, we'll just simulate success after a delay
        await new Promise((resolve) => setTimeout(resolve, 1000));

        setExecutionState((prev) => ({
          ...prev,
          stepStatuses: {
            ...prev.stepStatuses,
            [index]: "completed",
          },
          createdResources: {
            ...prev.createdResources,
            [index]: { id: Math.floor(Math.random() * 1000), type: step.type },
          },
        }));

        if (index === steps.length - 1) {
          onComplete?.();
        }
      } catch (error) {
        setExecutionState((prev) => ({
          ...prev,
          stepStatuses: {
            ...prev.stepStatuses,
            [index]: "error",
          },
          stepErrors: {
            ...prev.stepErrors,
            [index]: {
              message:
                error instanceof Error
                  ? error.message
                  : "Unknown error occurred",
            },
          },
        }));
        onError?.(
          error instanceof Error ? error : new Error("Unknown error occurred")
        );
      }
    },
    [steps, onComplete, onError]
  );

  const retryStep = useCallback(
    async (index: number) => {
      await executeStep(index);
    },
    [executeStep]
  );

  const executeAll = useCallback(async () => {
    for (let i = 0; i < steps.length; i++) {
      await executeStep(i);
      if (executionState.stepStatuses[i] === "error") {
        break;
      }
    }
  }, [steps, executeStep, executionState.stepStatuses]);

  return {
    currentStepIndex: executionState.currentStepIndex,
    stepStatuses: executionState.stepStatuses,
    stepErrors: executionState.stepErrors,
    createdResources: executionState.createdResources,
    executeStep,
    retryStep,
    executeAll,
  };
}
