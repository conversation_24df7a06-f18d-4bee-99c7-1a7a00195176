import { useState, useCallback } from "react";
import {
  AutomationPlan,
  PlanStep,
  ExecutionState,
  ExecutionContext,
  StepResource,
  StepError,
  ListStep,
  CampaignStep,
  TemplateStep,
  JourneyStep,
} from "../types/automation";
import api from "../api";

interface UseAutomationExecutorProps {
  plan: AutomationPlan | null;
  context: ExecutionContext;
}

export function useAutomationExecutor({
  plan,
  context,
}: UseAutomationExecutorProps) {
  const [executionState, setExecutionState] = useState<ExecutionState>({
    currentStep: null,
    completedSteps: [],
    progress: 0,
    error: null,
    stepErrors: new Map(),
    createdResources: new Map(),
  });

  const getResourceIdFromMap = useCallback(
    (reference: string | number): number | null => {
      if (typeof reference !== "string" || !reference.includes("steps.")) {
        return typeof reference === "number" ? reference : null;
      }

      const match = reference.match(/steps\.(\d+)\.(\w+)\.id/);
      if (!match) return null;

      const [, stepIndex, resourceType] = match;
      const resource = executionState.createdResources.get(Number(stepIndex));

      if (!resource) return null;
      return resource[resourceType]?.id ?? resource.id;
    },
    [executionState.createdResources]
  );

  const executeStep = useCallback(
    async (step: PlanStep, index: number) => {
      try {
        setExecutionState((prev) => ({
          ...prev,
          currentStep: index,
          stepErrors: new Map(prev.stepErrors).set(index, {
            message: "",
            details: null,
          }),
        }));

        // Pre-execution validation
        const validateStep = (
          step: PlanStep
        ): step is ListStep | CampaignStep | TemplateStep | JourneyStep => {
          if (!step.type || !step.config) return false;
          switch (step.type) {
            case "list":
              return (
                "list" in step.config && typeof step.config.list === "object"
              );
            case "campaign":
              return (
                "campaign" in step.config &&
                typeof step.config.campaign === "object"
              );
            case "template":
              return (
                "template" in step.config &&
                typeof step.config.template === "object"
              );
            case "journey":
              return (
                "journey" in step.config &&
                typeof step.config.journey === "object"
              );
            default:
              return false;
          }
        };

        if (!validateStep(step)) {
          throw new Error(`Invalid step configuration at index ${index}`);
        }

        let resourceData: StepResource;

        // Type guard for the switch statement
        const stepType = step.type;
        if (!["list", "campaign", "template", "journey"].includes(stepType)) {
          throw new Error(`Unknown step type: ${stepType}`);
        }

        switch (stepType) {
          case "list": {
            const listConfig: any = {
              name: step.config.list.name,
              type: step.config.list.type || "dynamic",
              rule: step.config.list.rule,
              is_visible: step.config.list.is_visible ?? true,
              tags: step.config.list.tags || [],
            };

            const list = await api.lists.create(context.locationId, listConfig);
            if (!list?.id)
              throw new Error("Failed to create list - no ID returned");

            resourceData = { id: list.id, type: "list", list };
            break;
          }

          case "campaign": {
            const campaignListIds = await Promise.all(
              (step.config.campaign.list_ids || []).map(async (listId) => {
                const resolvedId = getResourceIdFromMap(listId);
                if (!resolvedId)
                  throw new Error(`Failed to resolve list ID: ${listId}`);
                return resolvedId;
              })
            );

            if (!campaignListIds.length) {
              throw new Error("Campaign must have at least one list");
            }

            const campaignConfig = {
              name: step.config.campaign.name,
              type: step.config.campaign.type || "blast",
              channel: step.config.campaign.channel,
              subscription_id: step.config.campaign.subscription_id,
              provider_id: step.config.campaign.provider_id,
              list_ids: campaignListIds,
              exclusion_list_ids:
                step.config.campaign.exclusion_list_ids?.filter(
                  (id) => id !== null
                ) || [],
              tags: step.config.campaign.tags || [],
              send_in_user_timezone:
                step.config.campaign.send_in_user_timezone === true,
              send_at: step.config.campaign.send_at || "",
            };

            const campaign = await api.campaigns.create(
              context.locationId,
              campaignConfig
            );
            if (!campaign?.id)
              throw new Error("Failed to create campaign - no ID returned");

            resourceData = { id: campaign.id, type: "campaign", campaign };
            break;
          }

          case "template": {
            const campaignId = getResourceIdFromMap(
              step.config.template.campaign_ref ||
                step.config.template.campaign_id ||
                ""
            );
            if (!campaignId) {
              throw new Error("Failed to resolve campaign ID for template");
            }

            const templateConfig = {
              type: step.config.template.type,
              locale: step.config.template.locale || "en",
              data: step.config.template.data,
              campaign_id: campaignId,
            };

            const template = await api.templates.create(
              context.locationId,
              templateConfig
            );
            if (!template?.id)
              throw new Error("Failed to create template - no ID returned");

            resourceData = { id: template.id, type: "template", template };
            break;
          }

          case "journey": {
            const journeyConfig = {
              ...step.config.journey,
              published: false,
            };

            const journey = await api.automations.create_with_steps(
              context.locationId,
              journeyConfig
            );
            if (!journey?.id)
              throw new Error("Failed to create journey - no ID returned");

            resourceData = { id: journey.id, type: "journey", journey };
            break;
          }

          default:
            throw new Error(`Unknown step type: ${stepType}`);
        }

        setExecutionState((prev) => ({
          ...prev,
          completedSteps: [...prev.completedSteps, index],
          createdResources: new Map(prev.createdResources).set(
            index,
            resourceData
          ),
          progress: plan
            ? ((index + 1) / Object.keys(plan.items).length) * 100
            : 0,
        }));

        return resourceData;
      } catch (err) {
        const error = err as Error;
        const stepError: StepError = {
          message: error.message || "Step execution failed",
          details: error,
        };

        setExecutionState((prev) => ({
          ...prev,
          error: stepError.message,
          stepErrors: new Map(prev.stepErrors).set(index, stepError),
        }));

        throw error;
      }
    },
    [context.locationId, getResourceIdFromMap, plan]
  );

  const executeSteps = useCallback(async () => {
    if (!plan) return;

    setExecutionState((prev) => ({
      ...prev,
      currentStep: null,
      completedSteps: [],
      progress: 0,
      error: null,
      stepErrors: new Map(),
      createdResources: new Map(),
    }));

    try {
      for (let i = 0; i < Object.keys(plan.items).length; i++) {
        const [itemKey, item] = Object.entries(plan.items)[i];
        await executeStep(item, i);
      }

      // Send success feedback
      await api.insights.sendFeedback(context.locationId, context.insightId, {
        success: true,
        steps: Object.entries(plan.items).map(([_, item]) => ({
          step: item,
          success: true,
        })),
      });
    } catch (err) {
      const error = err as Error;
      console.error("Error executing steps:", error);

      // Send error feedback
      await api.insights.sendFeedback(context.locationId, context.insightId, {
        success: false,
        error: error.message,
        steps: Object.entries(plan.items).map(([_, item], index) => ({
          step: item,
          success: executionState.completedSteps.includes(index),
          error: executionState.stepErrors.get(index)?.message,
        })),
      });

      throw error;
    } finally {
      setExecutionState((prev) => ({ ...prev, currentStep: null }));
    }
  }, [
    context.locationId,
    context.insightId,
    executeStep,
    plan,
    executionState.completedSteps,
    executionState.stepErrors,
  ]);

  const retryStep = useCallback(
    async (index: number) => {
      if (!plan) return;
      const [_, item] = Object.entries(plan.items)[index];
      await executeStep(item, index);
    },
    [executeStep, plan]
  );

  return {
    executionState,
    executeStep,
    executeSteps,
    retryStep,
    getResourceIdFromMap,
  };
}
