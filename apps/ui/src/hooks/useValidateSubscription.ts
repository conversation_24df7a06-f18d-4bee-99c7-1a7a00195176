import { useContext, useEffect, useState } from "react";
import api from "../api";
import toastr from "toastr";
import { AdminContext, OrganizationContext } from "../contexts";

// Define subscription package types
export interface SubscriptionPackage {
  name: string;
  product_id: string;
}

// Define subscription response interface
interface SubscriptionResponse {
  status: string;
  product_id?: string;
  trial_end?: number | string | Date | null;
  [key: string]: any;
}

// Map of price_ids to package details
const SUBSCRIPTION_PACKAGES: Record<string, SubscriptionPackage> = {
  prod_S9EfHg24zkSmfn: {
    name: "Starter",
    product_id: "prod_S9EfHg24zkSmfn",
  },
  prod_S9Eq7rrTdnxctS: {
    name: "Bloom",
    product_id: "prod_S9Eq7rrTdnxctS",
  },
  prod_S9Emu6MWkKEvC3: {
    name: "Sprout",
    product_id: "prod_S9Emu6MWkKEvC3",
  },
};

const useValidateSubscription = () => {
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [subscriptionPackage, setSubscriptionPackage] =
    useState<SubscriptionPackage | null>(null);
  const [subscriptionData, setSubscriptionData] = useState<any>(null);
  const [isPro, setIsPro] = useState(false);
  const [isTrialActive, setIsTrialActive] = useState(false);
  const [trialEndDate, setTrialEndDate] = useState<Date | null>(null);
  const [isArtificialTrial, setIsArtificialTrial] = useState(false);
  const admin = useContext(AdminContext);
  const [organization] = useContext(OrganizationContext);

  useEffect(() => {
    const checkArtificialTrial = () => {
      if (!organization) return;

      // Check if organization has an artificial trial end date
      if (organization.artificial_trial_end) {
        const artificialTrialEnd = new Date(organization.artificial_trial_end);
        const now = new Date();

        // If the artificial trial end date is in the future
        if (artificialTrialEnd > now) {
          setIsArtificialTrial(true);
          setIsTrialActive(true);
          setTrialEndDate(artificialTrialEnd);
          setIsPro(true);
          return true;
        }
      }
      return false;
    };

    const fetchStripeSubscription = async () => {
      if (!admin) return;

      try {
        // First check if artificial trial is active
        if (checkArtificialTrial()) {
          setIsLoading(false);
          return;
        }

        const activeStates = ["trailing", "active", "past_due"];
        const response: SubscriptionResponse =
          await api.subscriptions.getStripeSubscription(admin.organization_id);
        console.log("response_stripe", response);

        if (response && activeStates.includes(response.status)) {
          if (!response.product_id) {
            setIsSubscribed(false);
            setSubscriptionPackage(null);
            setSubscriptionData(null);
            setIsPro(false);
            return;
          }
          const selectedPackage = SUBSCRIPTION_PACKAGES[response.product_id];
          setIsSubscribed(true);
          setSubscriptionData(response);

          // Determine the package based on price_id
          if (selectedPackage) {
            setSubscriptionPackage(selectedPackage);
          } else {
            setSubscriptionPackage({
              name: "Custom Plan",
              product_id: response.product_id || "unknown",
            });
          }
          if (selectedPackage.name != "Starter") {
            setIsPro(true);
          }

          // Check if this is a trial subscription
          if (response.trial_end) {
            setIsTrialActive(true);
            setIsArtificialTrial(false);
            // Convert trial_end to number if it's a string
            const trialEndTimestamp =
              typeof response.trial_end === "string"
                ? parseInt(response.trial_end, 10)
                : response.trial_end;

            if (trialEndTimestamp && typeof trialEndTimestamp === "number") {
              setTrialEndDate(new Date(trialEndTimestamp * 1000));
            }
          } else {
            setIsTrialActive(false);
            setIsArtificialTrial(false);
            setTrialEndDate(null);
          }
        } else {
          setIsSubscribed(false);
          setSubscriptionPackage(null);
          setSubscriptionData(null);

          // If we have trial information but no active subscription
          if (response && response.trial_end) {
            setIsTrialActive(true);
            setIsArtificialTrial(false);
            // Convert trial_end to number if it's a string
            const trialEndTimestamp =
              typeof response.trial_end === "string"
                ? parseInt(response.trial_end, 10)
                : response.trial_end;

            if (trialEndTimestamp && typeof trialEndTimestamp === "number") {
              setTrialEndDate(new Date(trialEndTimestamp * 1000));
            }
          } else {
            setIsTrialActive(false);
            setIsArtificialTrial(false);
            setTrialEndDate(null);
          }
        }
        setIsLoading(false);
      } catch (err) {
        console.log(err, "err");
        setIsLoading(false);
        setSubscriptionPackage(null);
        setSubscriptionData(null);
        setIsTrialActive(false);
        setIsArtificialTrial(false);
        setTrialEndDate(null);
        // toastr.error(err as string);
      }
    };

    fetchStripeSubscription();
  }, [admin, organization]);

  return {
    isSubscribed,
    isLoading,
    subscriptionPackage,
    subscriptionData,
    isPro,
    isTrialActive,
    trialEndDate,
    isArtificialTrial,
  };
};

export default useValidateSubscription;
