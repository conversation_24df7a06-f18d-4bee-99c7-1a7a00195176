@tailwind base;
@tailwind components;
@tailwind utilities;

/* Apply 85% zoom to entire application */
html {
  /* transform: scale(0.85);
  transform-origin: 0 0; 
  width: 117.65%; /* 100% / 0.85 to compensate for the scale *
  height: 11.65%; */
  overflow-x: hidden;
  height: 100vh;
  /* margin: 0; */

}

@keyframes overlayShow {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @layer utilities {
    .animate-overlay-show {
      animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
    }
  }
  

body {
    font-family: 'Inter', 'Helvetica Neue', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    font-size: 13px;
    background: var(--color-background);
    color: var(--color-primary);
    margin: 0;
    padding: 0;
    /* overflow: hidden; Prevent scrolling at body level */
    /* position: relative; */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

* { box-sizing: border-box; }

a {
    color: inherit;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

.rbc-off-range-bg {
    background-color: var(--color-primary);
    opacity: 0.5;
}
.rbc-off-range button{
    color: var(--color-primary);
}

.page-content {
    padding: 0 40px 40px;
}

.page-content.no-horizontal-padding {
    padding-left: 0;
    padding-right: 0;
}

.page-content.fullscreen {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.page-content .page-banner {
    padding-top: 20px;
}

.container {
    border: 1px solid var(--color-grey);
    border-radius: var(--border-radius-outer);
    padding: 20px;
}

h1 {
    font-style: normal;
    font-weight: 700;
    font-size: 32px;
    line-height: 44px;
    margin: 20px 0;
}

h2 {
    font-style: normal;
    font-weight: 700;
    font-size: 28px;
    line-height: 42px;
    margin: 20px 0;
}

h3 {
    font-size: 20px;
    line-height: 24px;
}

h4 {
    font-size: 16px;
    line-height: 22px;
}

h5 {
    font-size: 13px;
    line-height: 18px;
    margin: 5px 0;
}

input, textarea {
    position: relative;
    background: var(--color-background);
    color: var(--color-primary);
    font-family: 'Inter', 'Helvetica Neue', sans-serif;
    font-size: 13px;
    border: 1px solid var(--color-grey);
    border-radius: var(--border-radius);
    padding: 12px 15px;
    width: 100%;
    box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
    outline-color: var(--color-blue);
    transition: border-color 100ms;
}

::placeholder {
    color: var(--color-primary-soft);
}

input.small {
    padding: 5px 7px;
    font-size: 12px;
    line-height: 20px;
    width: auto;
}

textarea {
    min-height: 100px;
}

input:hover, textarea:hover {
    border-color: var(--color-grey-hard);
}

form .form-submit {
    margin-top: 20px;
    margin-bottom: 0;
    display: inline-block;
}

label {
    display: block;
    margin: 10px 0 5px;
}

form .label {
    display: block;
    margin: 5px 0;
}

label > span {
    margin-bottom: 3px;
    display: block;
    font-weight: 500;
}

label.invalid input {
    border-color: var(--color-red);
    box-shadow: 0px 1px 2px var(--color-red-light);
}

label.invalid span {
    color: var(--color-red);
}

label.hide-label {
    margin: 0;
}

.label-subtitle {
    color: var(--color-primary-soft);
    font-weight: 400;
    font-size: 12px;
}

label .switch {
    display: inline-block;
    height: 26px;
    position: relative;
    width: 46px;
}

label .switch input {
    display: none;
}

label .switch .slider {
    background-color: var(--color-grey);
    bottom: 0;
    cursor: pointer;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: .4s;
}

label .switch .slider:before {
    background-color: var(--color-background);
    bottom: 4px;
    content: "";
    height: 18px;
    width: 18px;
    left: 4px;
    position: absolute;
    transition: .4s;
}

label .switch input:checked + .slider {
    background-color: var(--color-green);
}

label .switch input:checked + .slider:before {
    transform: translateX(20px);
}

label .switch .slider.round {
    border-radius: 26px;
}

label .switch .slider.round:before {
    border-radius: 50%;
}

fieldset {
    border: 1px solid var(--color-grey-soft);
    border-radius: var(--border-radius);
    padding: 0 10px 10px;
    margin: 15px 0;
    min-width: 0;
}

.icon {
    width: 16px;
    height: 16px;
}

.icon-box {
    align-self: start;
    border-radius: 4px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

code {
    word-wrap: break-word;
    background-color: var(--color-grey-soft);
    border-radius: 2px;
    padding: 2px;
}

.blue {
    background-color: var(--color-blue-soft);
    color: var(--color-blue-hard);
}

.green {
    background-color: var(--color-green-soft);
    color: var(--color-green-hard);
}

.red {
    background-color: var(--color-red-soft);
    color: var(--color-red-hard);
}

.yellow {
    background-color: var(--color-yellow-soft);
    color: var(--color-yellow-hard);
}
.black {
    color: var(--color-black);
}
.fit-content {
    width: fit-content;
}

/* Custom class for requirement tags */
.requirement-tag {
    display: inline-flex;
    align-items: center;
    border-radius: 0.25rem;
    padding: 0.125rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.requirement-tag.completed {
    background-color: var(--color-green-soft);
    color: var(--color-green-hard);
}

.requirement-tag.pending {
    background-color: var(--color-surface-secondary);
    color: var(--color-primary-soft);
}

@media only screen and (max-width: 600px) {
    .page-content {
        padding: 0 20px 20px;
    }
}

:root {
  /* Existing theme variables */
  --primary-color: #f37335;
  --primary-color-rgb: 243, 115, 53; /* RGB values for the primary color */
  --primary-color-hover: #e56224;
  --secondary-color: #fdc830;
  --background-primary: #fff;
  --background-secondary: #f8f9fa;
  --text-primary: #212529;
  --text-secondary: #6c757d;
  --border-color: #dee2e6;
  --danger-color: #dc3545;
  --danger-color-rgb: 220, 53, 69; /* RGB values for the danger color */
  --danger-color-hover: #c82333;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --hover-color: rgba(0, 0, 0, 0.05);
  
  /* Layout specific */
  --header-height: 60px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 60px;
  --minichat-width: 350px;
  
  /* Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Animations */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  
  /* Z-index layers */
  --z-header: 100;
  --z-sidebar: 90;
  --z-minichat: 80;
  --z-modal: 1000;
  --z-toast: 1100;
}

/* Dark mode variables (if needed) */
@media (prefers-color-scheme: dark) {
  :root {
    --background-primary: #121212;
    --background-secondary: #1e1e1e;
    --text-primary: #e0e0e0;
    --text-secondary: #a0a0a0;
    --border-color: #333333;
    --hover-color: rgba(255, 255, 255, 0.05);
  }
}

/* Global styles */

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease-out forwards;
}