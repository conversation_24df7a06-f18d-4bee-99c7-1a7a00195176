import api from "../api";

export interface SocialAuthResponse {
  success: boolean;
  profileUrl?: string;
  error?: string;
}

const OAUTH_WINDOW_FEATURES =
  "width=600,height=600,menubar=no,toolbar=no,location=no,status=no";

export class SocialAuthService {
  private static async handleOAuthFlow(
    authUrl: string
  ): Promise<SocialAuthResponse> {
    return new Promise((resolve) => {
      const popup = window.open(authUrl, "Social Auth", OAUTH_WINDOW_FEATURES);

      const messageHandler = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;

        if (event.data?.type === "OAUTH_COMPLETE") {
          window.removeEventListener("message", messageHandler);
          if (popup) popup.close();

          resolve({
            success: event.data.success,
            profileUrl: event.data.profileUrl,
            error: event.data.error,
          });
        }
      };

      window.addEventListener("message", messageHandler);

      // Cleanup if window is closed manually
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          window.removeEventListener("message", messageHandler);
          resolve({ success: false, error: "Window closed by user" });
        }
      }, 1000);
    });
  }

  static async connectFacebook(): Promise<SocialAuthResponse> {
    try {
      return await this.handleOAuthFlow(`${api}/auth/facebook/connect`);
    } catch (error) {
      return { success: false, error: "Failed to connect to Facebook" };
    }
  }

  static async connectTwitter(): Promise<SocialAuthResponse> {
    try {
      return await this.handleOAuthFlow(`${api.baseUrl}/auth/twitter/connect`);
    } catch (error) {
      return { success: false, error: "Failed to connect to Twitter" };
    }
  }

  static async connectInstagram(): Promise<SocialAuthResponse> {
    try {
      return await this.handleOAuthFlow(
        `${api.baseUrl}/auth/instagram/connect`
      );
    } catch (error) {
      return { success: false, error: "Failed to connect to Instagram" };
    }
  }

  static async connectLinkedIn(): Promise<SocialAuthResponse> {
    try {
      return await this.handleOAuthFlow(`${api.baseUrl}/auth/linkedin/connect`);
    } catch (error) {
      return { success: false, error: "Failed to connect to LinkedIn" };
    }
  }
}
