const { createProxyMiddleware } = require("http-proxy-middleware");

module.exports = function (app) {
  // Enhanced proxy configuration with better error handling and timeouts
  const proxyOptions = {
    target: process.env.REACT_APP_PROXY_URL || "http://api:3001",
    changeOrigin: true,
    secure: false,
    timeout: 180000, // 180 second timeout (3 minutes)
    proxyTimeout: 180000, // 180 second proxy timeout
    // Add retry logic for connection failures
    onError: (err, req, res) => {
      console.error(`Proxy error for ${req.url}:`, err.message);

      // If the response hasn't been sent yet, send a 504 Gateway Timeout
      if (!res.headersSent) {
        res.writeHead(504, {
          'Content-Type': 'application/json',
        });
        res.end(JSON.stringify({
          error: 'Gateway Timeout',
          message: 'The API server is temporarily unavailable. Please try again.',
          code: 504
        }));
      }
    },
    onProxyReq: (proxyReq, req, res) => {
      // Add timeout to the proxy request
      proxyReq.setTimeout(180000, () => {
        console.error(`Proxy request timeout for ${req.url}`);
        proxyReq.destroy();
      });
    },
    // Add logging for debugging
    logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'warn',
  };

  app.use("/api", createProxyMiddleware(proxyOptions));

  app.use(
    "/unsubscribe",
    createProxyMiddleware({
      ...proxyOptions,
      pathRewrite: {
        "^/unsubscribe": "/api/unsubscribe",
      },
    })
  );
};
