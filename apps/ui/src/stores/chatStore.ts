import create from "zustand";
import { devtools } from "zustand/middleware";
import {
  chatService,
  type Chat,
  type Message,
  type Agent,
  type MessageResponse,
} from "../api/chat";

interface ChatState {
  chats: Chat[];
  currentChat: Chat | null;
  messages: { [chatId: string]: Message[] };
  selectedAgent: Agent | null;
  loading: boolean;
  error: string | null;

  // Actions
  fetchChats: () => Promise<void>;
  selectChat: (chat: Chat) => void;
  sendMessage: (content: string) => Promise<void>;
  renameChat: (chatId: string, name: string) => Promise<void>;
  generateSummary: (chatId: string) => Promise<void>;
  generateInsights: (chatId: string) => Promise<void>;
  selectAgent: (agent: Agent) => void;
  fetchAgents: () => Promise<void>;
  startNewChat: () => void;
}

const useChatStore = create<ChatState>()(
  devtools((set, get) => ({
    chats: [],
    currentChat: null,
    messages: {},
    selectedAgent: null,
    loading: false,
    error: null,

    fetchChats: async () => {
      set({ loading: true, error: null });
      try {
        const chats = await chatService.getChats();
        set({ chats });
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to fetch chats";
        set({ error: errorMessage });
        if (errorMessage.includes("Location ID not found")) {
          console.error(
            "Location ID is missing. Please ensure you're properly logged in."
          );
        }
      } finally {
        set({ loading: false });
      }
    },

    startNewChat: () => {
      set({ currentChat: null });
    },

    selectChat: (chat: Chat) => {
      set({ currentChat: chat });
    },

    sendMessage: async (content) => {
      const { currentChat, selectedAgent } = get();
      if (!selectedAgent) return;

      // Optimistic update
      const tempMessage: Message = {
        id: Date.now().toString(),
        content,
        role: "user",
        timestamp: new Date().toISOString(),
      };

      // If we have a current chat, add message to it
      if (currentChat) {
        set((state) => ({
          messages: {
            ...state.messages,
            [currentChat.chat_id]: [
              ...(state.messages[currentChat.chat_id] || []),
              tempMessage,
            ],
          },
        }));
      }

      try {
        // Get location ID from URL
        const locationId = window.location.pathname.split("/")[2];

        const response = await chatService.sendMessage({
          message: content,
          agentId: selectedAgent.id,
          chatId: currentChat?.chat_id,
          locationId, // Add the required locationId parameter
        });

        // Ensure response.chat exists when creating a new chat
        if (!currentChat && response.chat) {
          const newChat = response.chat;
          const responseMessage: Message = {
            id: response.id,
            content: response.content,
            role: response.role,
            timestamp: response.timestamp,
            agent: response.agent,
            chat: response.chat,
          };

          set(
            (state) =>
              ({
                chats: [newChat, ...state.chats],
                currentChat: newChat,
                messages: {
                  ...state.messages,
                  [newChat.chat_id]: [tempMessage, responseMessage],
                },
              } as Partial<ChatState>)
          );
        } else if (currentChat) {
          const responseMessage: Message = {
            id: response.id,
            content: response.content,
            role: response.role,
            timestamp: response.timestamp,
            agent: response.agent,
            chat: response.chat,
          };

          set((state) => ({
            messages: {
              ...state.messages,
              [currentChat.chat_id]: [
                ...(state.messages[currentChat.chat_id] || []).filter(
                  (m) => m.id !== tempMessage.id
                ),
                tempMessage,
                responseMessage,
              ],
            },
          }));
        }
      } catch (error) {
        // Revert optimistic update on error
        if (currentChat) {
          set((state) => ({
            messages: {
              ...state.messages,
              [currentChat.chat_id]: (
                state.messages[currentChat.chat_id] || []
              ).filter((m) => m.id !== tempMessage.id),
            },
            error: (error as Error).message,
          }));
        }
      }
    },

    renameChat: async (chatId: string, name: string) => {
      set({ loading: true, error: null });
      try {
        await chatService.renameChat(chatId, name);
        set((state) => ({
          chats: state.chats.map((chat) =>
            chat.chat_id === chatId ? { ...chat, name } : chat
          ),
          currentChat:
            state.currentChat?.chat_id === chatId
              ? { ...state.currentChat, name }
              : state.currentChat,
        }));
      } catch (error) {
        set({ error: (error as Error).message });
      } finally {
        set({ loading: false });
      }
    },

    generateSummary: async (chatId: string) => {
      set({ loading: true, error: null });
      try {
        const summary = await chatService.getChatSummary(chatId);
        set((state) => ({
          chats: state.chats.map((chat) =>
            chat.chat_id === chatId ? { ...chat, summary } : chat
          ),
          currentChat:
            state.currentChat?.chat_id === chatId
              ? { ...state.currentChat, summary }
              : state.currentChat,
        }));
      } catch (error) {
        set({ error: (error as Error).message });
      } finally {
        set({ loading: false });
      }
    },

    generateInsights: async (chatId: string) => {
      set({ loading: true, error: null });
      try {
        const insights = await chatService.generateInsights(chatId);
        set((state) => ({
          chats: state.chats.map((chat) =>
            chat.chat_id === chatId ? { ...chat, insights } : chat
          ),
          currentChat:
            state.currentChat?.chat_id === chatId
              ? { ...state.currentChat, insights }
              : state.currentChat,
        }));
      } catch (error) {
        set({ error: (error as Error).message });
      } finally {
        set({ loading: false });
      }
    },

    selectAgent: (agent: Agent) => {
      set({ selectedAgent: agent });
    },

    fetchAgents: async () => {
      set({ loading: true, error: null });
      try {
        const agents = await chatService.getAvailableAgents();
        set({
          selectedAgent:
            agents.find((a) => a.id === "smokey") || agents[0] || null,
        });
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to fetch agents";
        set({ error: errorMessage });
        if (errorMessage.includes("Location ID not found")) {
          console.error(
            "Location ID is missing. Please ensure you're properly logged in."
          );
        }
      } finally {
        set({ loading: false });
      }
    },
  }))
);

export default useChatStore;
