import { Rule } from "./rules";

export type StepType = "list" | "campaign" | "template" | "journey";
export type StepStatus =
  | "completed"
  | "in-progress"
  | "pending"
  | "queued"
  | "error";

interface BaseStep {
  type: StepType;
  config: Record<string, any>;
}

export interface ListStep extends BaseStep {
  type: "list";
  config: {
    list: {
      name: string;
      type: "dynamic" | "static";
      description?: string;
      rule?: Rule;
      is_visible?: boolean;
      tags?: string[];
      user_ids?: number[];
    };
  };
}

export interface CampaignStep extends BaseStep {
  type: "campaign";
  config: {
    campaign: {
      type: "blast" | "trigger";
      name: string;
      channel: "email" | "text";
      subscription_id: number;
      provider_id: number;
      list_refs?: string[];
      exclusion_list_refs?: string[];
      list_ids?: number[];
      exclusion_list_ids?: number[];
      send_in_user_timezone?: boolean;
      send_at?: string;
      tags?: string[];
    };
  };
}

export interface TemplateStep extends BaseStep {
  type: "template";
  config: {
    template: {
      type: "email" | "text";
      name: string;
      locale: string;
      campaign_ref?: string; // Must be a key from the items dictionary
      campaign_id?: number; // Existing campaign ID
      data: {
        editor: "code";
        name: string;
        subject: string;
        html: string;
        text: string;
        from: {
          name: string;
          address: string;
        };
        reply_to: string;
        preheader?: string;
      };
    };
  };
}

export interface JourneyStep extends BaseStep {
  type: "journey";
  config: {
    journey: {
      name: string;
      steps: Record<
        string,
        {
          type: string;
          x: number;
          y: number;
          data: Record<string, any>;
        }
      >;
      published?: boolean;
    };
  };
}

export type PlanStep = ListStep | CampaignStep | TemplateStep | JourneyStep;

export interface AutomationPlan {
  name: string;
  description: string;
  items: Record<string, PlanStep>;
}

export interface StepResource {
  id: number;
  type: StepType;
  [key: string]: any;
}

export interface StepError {
  message: string;
  details?: any;
}

export interface ExecutionState {
  currentStep: number | null;
  completedSteps: number[];
  progress: number;
  error: string | null;
  stepErrors: Map<number, StepError>;
  createdResources: Map<number, StepResource>;
}

export interface ExecutionContext {
  locationId: number;
  insightId: number;
  model: string;
  channelConfig: {
    email?: { subscription_id: number; provider_id: number };
    text?: { subscription_id: number; provider_id: number };
  };
}
