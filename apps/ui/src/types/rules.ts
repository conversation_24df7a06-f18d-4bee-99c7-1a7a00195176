export type RuleType =
  | "wrapper"
  | "string"
  | "number"
  | "date"
  | "boolean"
  | "array";
export type RuleGroup = "parent" | "pos" | "user" | "event";
export type Operator =
  | "and"
  | "or"
  | "="
  | ">"
  | "<"
  | ">="
  | "<="
  | "contains";

export interface Rule {
  uuid: string;
  root_uuid?: string;
  parent_uuid?: string;
  type: RuleType;
  group: RuleGroup;
  path: string;
  operator: Operator;
  value?: string | number | boolean;
  children?: Rule[];
}
