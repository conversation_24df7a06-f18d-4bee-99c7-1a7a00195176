import { t } from "i18next";
import { CircularProgressbar, buildStyles } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";
import Heading from "./Heading";

export const AIScoreCard = ({
  score,
  factors,
}: {
  score: number;
  factors: any[];
}) => (
  <div className="bg-surface p-8 rounded-lg shadow-sm">
    <Heading title={t("ai_score")} size="h4" className="mb-6" />
    <div className="ui-columns" style={{ alignItems: "center" }}>
      <div
        className="ui-column"
        style={{ maxWidth: "200px", display: "flex", justifyContent: "center" }}
      >
        <CircularProgressbar
          value={score}
          text={`${score}`}
          styles={buildStyles({
            pathColor:
              score < 40 ? "#f43f5e" : score < 70 ? "#f59e0b" : "#22c55e",
            textColor: "currentColor",
            trailColor: "#e5e7eb",
          })}
        />
      </div>
      <div className="ui-column">
        <div className="space-y-4">
          {factors.map((factor: any) => (
            <div
              key={factor.name}
              className="flex items-center justify-between"
              style={{ marginBottom: "12px" }}
            >
              <span className="text-sm" style={{ flex: 1 }}>
                {factor.name}
              </span>
              <span
                className={`text-sm ${
                  factor.impact === "positive"
                    ? "text-green-600"
                    : "text-red-600"
                }`}
                style={{
                  marginLeft: "16px",
                  minWidth: "40px",
                  textAlign: "right",
                }}
              >
                {factor.impact === "positive" ? "+" : "-"}
                {factor.weight}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);
