import "./Heading.css";

interface HeadingProps {
  title: React.ReactNode;
  size?: "h2" | "h3" | "h4";
  actions?: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
}

export default function Heading({
  title,
  actions,
  children,
  size = "h2",
  className,
}: HeadingProps) {
  const HeadingTitle = `${size}` as keyof JSX.IntrinsicElements;
  return (
    <div
      className={`xs:flex xs:flex-col sm:flex sm:flex-row heading heading-${size} ${className}`}
    >
      <div className="heading-text">
        <HeadingTitle>{title}</HeadingTitle>
        {children && <div className="desc">{children}</div>}
      </div>
      {actions && <div className="xs:mt-2 actions">{actions}</div>}
    </div>
  );
}
