/* Main Insight Card Styles */
.insight-card {
  background: var(--color-surface-elevated);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.insight-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Agent Header */
.insight-agent-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-background-subtle);
}

.insight-agent-icon {
  font-size: 24px;
  margin-right: 12px;
}

.insight-agent-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.insight-agent-name {
  font-weight: 600;
  font-size: 14px;
  color: var(--color-text);
}

.insight-agent-role {
  font-size: 12px;
  color: var(--color-text-subtle);
}

/* Impact Badge */
.insight-impact-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.insight-impact-badge.high {
  background: var(--color-red-soft);
  color: var(--color-red);
}

.insight-impact-badge.medium {
  background: var(--color-yellow-soft);
  color: var(--color-yellow-dark);
}

.insight-impact-badge.low {
  background: var(--color-green-soft);
  color: var(--color-green);
}

/* Content Area */
.insight-card .insight-content {
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-height: 0;
}

.insight-card .insight-description {
  font-size: 14px;
  line-height: 1.5;
  color: var(--color-text-secondary);
  margin: 0 0 16px 0;
}

.insight-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--color-text);
}

/* Actions */
.insight-actions {
  margin-top: auto;
  padding-top: 12px;
  border-top: 1px solid var(--color-border);
}

.insight-actions-label {
  font-size: 13px;
  font-weight: 600;
  color: var(--color-text-subtle);
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.insight-action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* Compact Insight Card Styles */
.compact-insight-card {
  padding: 12px 14px;
  border-radius: 6px;
  margin-bottom: 5px;
  font-size: 13px;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  cursor: pointer;
  transition: background-color 0.2s;
}

.compact-insight-card:hover {
  filter: brightness(0.95);
}

.compact-insight-content {
  flex: 1;
  padding-right: 24px; /* Space for the dismiss button */
  overflow: hidden; /* Ensure content doesn't overflow */
}

.compact-insight-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.3;
  display: block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.compact-insight-description {
  margin: 4px 0 0 0;
  font-size: 12px;
  line-height: 1.4;
  color: rgba(0, 0, 0, 0.7);
  display: block;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.compact-insight-dismiss {
  position: absolute;
  top: 8px;
  right: 10px;
  background: none;
  border: none;
  font-size: 14px;
  line-height: 1;
  padding: 2px;
  color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.compact-insight-dismiss:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.8);
}

/* Variants based on impact level */
.compact-insight-card.success {
  background-color: var(--color-green-soft, rgba(209, 250, 229, 0.4));
}

.compact-insight-card.error {
  background-color: var(--color-red-soft, rgba(254, 226, 226, 0.4));
}

.compact-insight-card.warn {
  background-color: var(--color-yellow-soft, rgba(254, 249, 195, 0.4));
}

/* Dark theme support */
[data-theme="dark"] .insight-card {
  background: var(--color-surface-elevated);
  border-color: var(--color-border);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .insight-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .insight-agent-header {
  background: rgba(255, 255, 255, 0.05);
  border-bottom-color: var(--color-border);
}

[data-theme="dark"] .insight-impact-badge.high {
  background: rgba(153, 27, 27, 0.3);
  color: #ef4444;
}

[data-theme="dark"] .insight-impact-badge.medium {
  background: rgba(146, 123, 2, 0.3);
  color: #f59e0b;
}

[data-theme="dark"] .insight-impact-badge.low {
  background: rgba(6, 95, 70, 0.3);
  color: #10b981;
}

[data-theme="dark"] .compact-insight-description {
  color: rgba(255, 255, 255, 0.7);
}

[data-theme="dark"] .compact-insight-dismiss {
  color: rgba(255, 255, 255, 0.5);
}

[data-theme="dark"] .compact-insight-dismiss:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

[data-theme="dark"] .compact-insight-card.success {
  background-color: rgba(6, 95, 70, 0.3);
}

[data-theme="dark"] .compact-insight-card.error {
  background-color: rgba(153, 27, 27, 0.3);
}

[data-theme="dark"] .compact-insight-card.warn {
  background-color: rgba(146, 123, 2, 0.3);
} 