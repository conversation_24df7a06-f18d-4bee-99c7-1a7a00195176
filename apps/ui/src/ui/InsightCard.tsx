import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, But<PERSON>, Mo<PERSON> } from "../ui";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import AutomationCreator from "./AutomationCreator";
import api from "../api";
import { Insight, InsightStatus } from "../types";
import "./InsightCard.css";
import { AgentInfo, getAgentForInsight } from "../utils/agents";

// interface AgentInfo {
//   icon: string;
//   name: string;
//   role: string;
// }

interface InsightCardProps {
  insight: Insight;
  onRefresh?: () => void;
  compact?: boolean;
  // agents?: Record<string, AgentInfo>; // No longer needed - using shared utils
}

// Helper function to determine if an insight is actionable for automation
// This now uses server-side pattern matching instead of just checking type
const useInsightActionability = (insight: Insight) => {
  const [isActionable, setIsActionable] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkActionability = async () => {
      try {
        setLoading(true);
        const result = await api.insights.checkActionability(
          insight.location_id,
          insight.title,
          insight.description,
          insight.type,
          insight.actions
        );
        setIsActionable(result.isActionable);
      } catch (error) {
        console.error("Error checking insight actionability:", error);
        // Fallback to the old logic if API fails
        setIsActionable(insight.type === "automation" || insight.type === "campaign");
      } finally {
        setLoading(false);
      }
    };

    checkActionability();
  }, [insight.location_id, insight.title, insight.description, insight.type, insight.actions]);

  return { isActionable, loading };
};

// Helper function to get alternative actions for non-actionable insights
const getAlternativeAction = (insight: Insight, navigate: any): { label: string; action: () => void } | null => {
  const { title, description, type } = insight;

  // For non-actionable types or general insights, provide alternative actions
  if (type === "general" || type === "business" ||
      type === "compliance" || type === "market" || type === "product" || type === "financial" || type === "customer") {

    // Return different actions based on insight type and content
    if (type === "market" || title.toLowerCase().includes("market")) {
      return {
        label: "View Market Analysis",
        action: () => {
          // Navigate to market analysis or open detailed view
          navigate("/market-analysis");
        }
      };
    }

    if (type === "product" || title.toLowerCase().includes("product")) {
      return {
        label: "Explore Products",
        action: () => {
          // Navigate to products page
          navigate("/products");
        }
      };
    }

    if (type === "customer" || title.toLowerCase().includes("customer")) {
      return {
        label: "View Customer Data",
        action: () => {
          // Navigate to customer analytics
          navigate("/customers");
        }
      };
    }

    if (type === "financial" || title.toLowerCase().includes("financial") || title.toLowerCase().includes("revenue")) {
      return {
        label: "View Financial Reports",
        action: () => {
          // Navigate to financial dashboard
          navigate("/reports");
        }
      };
    }

    if (title.toLowerCase().includes("analysis") || title.toLowerCase().includes("research")) {
      return {
        label: "Learn More",
        action: () => {
          // Open a help or documentation page
          window.open("/help/insights", "_blank");
        }
      };
    }

    // Default alternative action for general insights
    return {
      label: "View Details",
      action: () => {
        // Could open a modal with more detailed information
        console.log("Viewing detailed insight information:", insight);
      }
    };
  }

  return null;
};

export default function InsightCard({
  insight,
  onRefresh,
  compact = false,
}: InsightCardProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [showAutomationCreator, setShowAutomationCreator] = useState(false);
  const { isActionable, loading: actionabilityLoading } = useInsightActionability(insight);

  const getVariant = () => {
    if (insight.impact === "low") return "success";
    return insight.impact === "high" ? "error" : "warn";
  };

  const handleCreateAutomation = () => {
    setShowAutomationCreator(true);
  };

  const handleStatusUpdate = async (status: InsightStatus) => {
    await api.insights.updateStatus(insight.location_id, insight.id, status);
    onRefresh?.();
  };

  // Determine if this insight is actionable and get alternative action if not
  // Use the server-side actionability check result
  const alternativeAction = (isActionable === false) ? getAlternativeAction(insight, navigate) : null;

  const truncatedDescription =
    compact && insight.description
      ? insight.description.length > 90
        ? `${insight.description.substring(0, 90)}`
        : insight.description
      : insight.description;

  const agent = getAgentForInsight(
    insight.title,
    insight.description,
    insight.agent_id,
    insight.agent_name
  );

  if (compact) {
    return (
      <div className={`compact-insight-card ${getVariant()}`}>
        <div
          className="compact-insight-content"
          onClick={isActionable ? handleCreateAutomation : (alternativeAction?.action || (() => {}))}
        >
          <h4 className="compact-insight-title">{insight.title}</h4>
          <p className="compact-insight-description">{truncatedDescription}</p>
        </div>
        <button
          className="compact-insight-dismiss"
          onClick={(e) => {
            e.stopPropagation();
            handleStatusUpdate("dismissed");
          }}
          aria-label={t("dismiss")}
        >
          ✕
        </button>
      </div>
    );
  }

  return (
    <>
      <div className="insight-card">
        {agent && (
          <div className="insight-agent-header">
            <span className="insight-agent-icon">{agent.icon}</span>
            <div className="insight-agent-info">
              <span className="insight-agent-name">{agent.name}</span>
              <span className="insight-agent-role">{agent.role}</span>
            </div>
            <span className={`insight-impact-badge ${insight.impact}`}>
              {t(`impact_${insight.impact}`, insight.impact)}
            </span>
          </div>
        )}

        <div className="insight-content">
          <h3 className="insight-title">{insight.title}</h3>
          <p className="insight-description">{insight.description}</p>

          {actionabilityLoading ? (
            <div className="insight-actions">
              <p className="insight-actions-label">
                {t("checking_actionability", "Checking actionability...")}
              </p>
            </div>
          ) : isActionable === true ? (
            <div className="insight-actions">
              <p className="insight-actions-label">
                {t("recommended_actions", "Recommended Actions:")}
              </p>
              <div className="insight-action-buttons">
                <Button
                  onClick={handleCreateAutomation}
                  variant="primary"
                  size="small"
                >
                  {t("create_automation", "Create Automation")}
                </Button>
                <Button
                  onClick={() => handleStatusUpdate("dismissed")}
                  variant="secondary"
                  size="small"
                >
                  {t("dismiss", "Dismiss")}
                </Button>
              </div>
            </div>
          ) : (
            <div className="insight-actions">
              <p className="insight-actions-label">
                {t("suggested_actions", "Suggested Actions:")}
              </p>
              <div className="insight-action-buttons">
                {alternativeAction && (
                  <Button
                    onClick={alternativeAction.action}
                    variant="primary"
                    size="small"
                  >
                    {alternativeAction.label}
                  </Button>
                )}
                <Button
                  onClick={() => handleStatusUpdate("dismissed")}
                  variant="secondary"
                  size="small"
                >
                  {t("dismiss", "Dismiss")}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {showAutomationCreator && (
        <Modal
          open={showAutomationCreator}
          onClose={() => setShowAutomationCreator(false)}
          title={insight.title}
        >
          <AutomationCreator
            insight={insight}
            onClose={() => setShowAutomationCreator(false)}
          />
        </Modal>
      )}
    </>
  );
}
