/* Product Table Container */
.product-table-container {
  width: 100%;
  margin: 1rem 0;
}

/* Product Table Title */
.product-table-title {
  text-align: center;
  font-weight: bold;
  color: var(--color-brunswick-green, #23504A);
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  letter-spacing: 0.03em;
}

/* Categories Filter */
.product-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  margin-bottom: 0.75rem;
  justify-content: center;
}

.category-button {
  padding: 0.25rem 0.75rem;
  font-size: 0.7rem;
  border-radius: 9999px;
  border: none;
  background-color: var(--color-honeydew, #DFF4E9);
  color: var(--color-dark-jungle-green, #0D211D);
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.category-button:hover {
  background-color: var(--color-emerald-green, #22AD85);
  color: white;
}

.category-button-active {
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
}

/* Table Scroll Container */
.product-table-scroll {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 350px;
  border-radius: 8px;
  border: 1px solid var(--color-border, #e5e7eb);
  margin-bottom: 0.5rem;
  background-color: white;
  scrollbar-width: thin;
  -ms-overflow-style: none;
}

.product-table-scroll::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.product-table-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.product-table-scroll::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 3px;
}

.product-table-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Product Table */
.product-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

/* Table Headers */
.product-table thead {
  position: sticky;
  top: 0;
  z-index: 1;
}

.product-table th {
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  text-align: left;
  padding: 0.75rem 1rem;
  font-weight: 600;
  white-space: nowrap;
}

/* Table Cells */
.product-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--color-border, #e5e7eb);
}

.product-table tr {
  transition: background-color 0.15s ease;
  cursor: pointer;
}

.product-table tr:hover {
  background-color: var(--color-honeydew, #DFF4E9);
}

.product-table tr:last-child td {
  border-bottom: none;
}

.product-table tr:nth-child(even) {
  background-color: var(--color-honeydew, #DFF4E9);
  opacity: 0.7;
}

/* Product Name and Description */
.product-name {
  font-weight: 500;
}

.product-description {
  font-size: 0.75rem;
  color: var(--color-text-muted, #6b7280);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

/* Price Column */
.price-column {
  text-align: right;
  min-width: 100px;
}
.size-column {
  min-width: 80px;
}
.name-column {
  min-width: 150px;
}

.product-price {
  font-weight: 500;
  color: var(--color-emerald-green, #22AD85);
  font-variant-numeric: tabular-nums;
  letter-spacing: 0.01em;
}

.product-original-price {
  font-size: 0.75rem;
  color: var(--color-text-muted, #6b7280);
  text-decoration: line-through;
  font-variant-numeric: tabular-nums;
}

/* Empty State */
.product-table-empty {
  text-align: center;
  padding: 1rem;
  color: var(--color-text-muted, #6b7280);
  font-size: 0.875rem;
}

/* Dark Theme Styles */
[data-theme="dark"] .product-table-title {
  color: var(--color-emerald-green, #22AD85);
}

[data-theme="dark"] .category-button {
  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));
  color: var(--color-text-muted, #9ca3af);
}

[data-theme="dark"] .category-button:hover {
  background-color: var(--color-emerald-green, #22AD85);
  color: white;
}

[data-theme="dark"] .product-table-scroll {
  background-color: var(--color-surface-elevated, #1e2538);
  border-color: var(--color-divider, #374151);
}

[data-theme="dark"] .product-table td {
  border-bottom-color: var(--color-divider, #374151);
}

[data-theme="dark"] .product-table tr:nth-child(even) {
  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.3));
}

[data-theme="dark"] .product-table tr:hover {
  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));
}

/* Mobile Styles */
@media (max-width: 767px) {
  .product-table-scroll {
    max-height: 250px;
  }
  
  .product-table th,
  .product-table td {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .product-description {
    max-width: 100px;
  }
  
  .category-button {
    padding: 0.2rem 0.5rem;
    font-size: 0.65rem;
  }
} 