import React, {
  useState,
  ReactNode,
  use<PERSON><PERSON>back,
  useMemo,
  useEffect,
} from "react";
import { useSearchParams } from "react-router-dom";
import { useDebounceControl, useResolver } from "../hooks";
import { SearchParams, SearchResult } from "../types";
import { prune } from "../utils";
import { TagPicker } from "../views/settings/TagPicker";
import {
  DataTable,
  DataTableProps,
  HeaderCell,
  DataTableCol,
} from "./DataTable";
import TextInput from "./form/TextInput";
import CheckboxInput from "./form/CheckboxInput";
import Heading from "./Heading";
import { SearchIcon } from "./icons";
import Pagination from "./Pagination";
import Stack from "./Stack";
import { useTranslation } from "react-i18next";
import SmokeyIcon from "../assets/smokey_icon.png";
import "./SearchTable.css";

// Extend the SearchResult type locally to include total count
interface SearchResultWithTotal<T> extends SearchResult<T> {
  total?: number;
}

// Selection-related interfaces
export interface SelectionState<T> {
  selectedItems: T[];
  isAllSelected: boolean;
  isPartiallySelected: boolean;
}

export interface BulkAction<T> {
  key: string;
  label: ReactNode;
  icon?: ReactNode;
  variant?: "primary" | "secondary" | "destructive";
  disabled?: (selectedItems: T[]) => boolean;
  onClick: (selectedItems: T[]) => void | Promise<void>;
}

export interface SearchTableProps<T extends Record<string, any>>
  extends Omit<DataTableProps<T>, "items"> {
  title?: ReactNode;
  description?: ReactNode;
  actions?: ReactNode;
  results: SearchResultWithTotal<T> | null;
  params: SearchParams;
  setParams: (params: SearchParams) => void;
  enableSearch?: boolean;
  searchPlaceholder?: string;
  emptyStateMessage?: string;
  tagEntity?:
    | "journeys"
    | "lists"
    | "users"
    | "campaigns"
    | "pos_data"
    | "products"; // anything else we want to tag?
  onRowClick?: (item: T) => void;
  // Selection-related props
  enableSelection?: boolean;
  bulkActions?: BulkAction<T>[];
  selectionKey?: keyof T; // Key to use for identifying items (defaults to 'id')
  onSelectionChange?: (selectedItems: T[]) => void;
}

const DEFAULT_ITEMS_PER_PAGE = 25;
const DEFAULT_PAGE = 0;

const toTableParams = (searchParams: URLSearchParams): SearchParams => {
  const page = searchParams.get("page");
  return {
    cursor: searchParams.get("cursor") ?? undefined,
    page: page === "prev" || page === "next" ? page : undefined,
    limit: parseInt(searchParams.get("limit") ?? "25", 10),
    q: searchParams.get("q") ?? undefined,
    sort: searchParams.get("sort") ?? undefined,
    direction: searchParams.get("direction") ?? undefined,
    tag: searchParams.getAll("tag"),
    filter: (() => {
      const filterValue = searchParams.get("filter");
      return filterValue ? JSON.parse(filterValue) : undefined;
    })(),
  };
};

const fromTableParams = (params: SearchParams): Record<string, string> => {
  const { tag, filter, ...rest } = params;
  return prune({
    ...rest,
    ...(tag?.length ? { tag } : {}),
    ...(filter ? { filter: JSON.stringify(filter) } : {}),
  });
};

export const useTableSearchParams = () => {
  const [searchParams, setSearchParams] = useSearchParams();

  const params = useMemo(() => toTableParams(searchParams), [searchParams]);

  const setParams = useCallback(
    (params: SearchParams) => {
      const searchParams = fromTableParams(params);
      setSearchParams(searchParams, { replace: true });
    },
    [setSearchParams]
  );

  return [params, setParams] as const;
};

/**
 * local state
 */
export function useSearchTableState<T>(
  loader: (params: SearchParams) => Promise<SearchResult<T> | null>,
  initialParams?: Partial<SearchParams>
) {
  const [params, setParams] = useTableSearchParams();
  const [results, , reload] = useResolver(
    () => loader({ ...initialParams, ...params }),
    [JSON.stringify(params), JSON.stringify(initialParams)]
  );

  return {
    results: results as SearchResultWithTotal<T> | null,
    params,
    setParams,
    reload,
  };
}

export interface SearchTableQueryState<T> {
  results: SearchResultWithTotal<T> | null;
  params: SearchParams;
  reload: () => Promise<void>;
  setParams: (params: SearchParams) => void;
}

/**
 * global query string state
 */
export function useSearchTableQueryState<T>(
  loader: (params: SearchParams) => Promise<SearchResult<T> | null>
): SearchTableQueryState<T> {
  const [params, setParams] = useTableSearchParams();
  const [results, setResults] = useState<SearchResultWithTotal<T> | null>(null);

  const paramsString = JSON.stringify(params);
  const reload = useCallback(async () => {
    try {
      const result = await loader(params);
      setResults(result as SearchResultWithTotal<T>);
    } catch (err) {
      console.error(err);
    }
  }, [loader, params]);

  useEffect(() => {
    reload();
  }, [reload]);

  return {
    results,
    params,
    reload,
    setParams,
  };
}

export function useSelectionState<T>(
  items: T[] = [],
  selectionKey: keyof T = "id" as keyof T
): [
  SelectionState<T>,
  {
    toggleItem: (item: T) => void;
    toggleAll: () => void;
    clearSelection: () => void;
    setSelectedItems: (items: T[]) => void;
  }
] {
  const [selectedItems, setSelectedItems] = useState<T[]>([]);

  const isAllSelected =
    items.length > 0 && selectedItems.length === items.length;
  const isPartiallySelected =
    selectedItems.length > 0 && selectedItems.length < items.length;

  const toggleItem = useCallback(
    (item: T) => {
      setSelectedItems((prev) => {
        const isSelected = prev.some(
          (selected) => selected[selectionKey] === item[selectionKey]
        );
        if (isSelected) {
          return prev.filter(
            (selected) => selected[selectionKey] !== item[selectionKey]
          );
        } else {
          return [...prev, item];
        }
      });
    },
    [selectionKey]
  );

  const toggleAll = useCallback(() => {
    if (isAllSelected) {
      setSelectedItems([]);
    } else {
      setSelectedItems([...items]);
    }
  }, [items, isAllSelected]);

  const clearSelection = useCallback(() => {
    setSelectedItems([]);
  }, []);

  const setSelectedItemsWithCallback = useCallback(
    (items: T[]) => setSelectedItems(items),
    []
  );

  return [
    { selectedItems, isAllSelected, isPartiallySelected },
    {
      toggleItem,
      toggleAll,
      clearSelection,
      setSelectedItems: setSelectedItemsWithCallback,
    },
  ];
}

export function SearchTable<T extends Record<string, any>>({
  actions,
  description,
  enableSearch,
  params,
  results,
  searchPlaceholder,
  setParams,
  tagEntity,
  title,
  emptyStateMessage,
  columns,
  onRowClick,
  enableSelection = false,
  bulkActions = [],
  selectionKey = "id" as keyof T,
  onSelectionChange,
  ...rest
}: SearchTableProps<T>) {
  const { t } = useTranslation();
  const [search, setSearch] = useDebounceControl(params.q ?? "", (q) =>
    setParams({ ...params, q })
  );

  const columnSort = params.sort
    ? { sort: params.sort, direction: params.direction ?? "asc" }
    : undefined;

  // Selection state
  const items = results?.results || [];
  const [selectionState, selectionActions] = useSelectionState(
    items,
    selectionKey
  );
  const { selectedItems, isAllSelected, isPartiallySelected } = selectionState;
  const { toggleItem, toggleAll, clearSelection } = selectionActions;

  // Notify parent of selection changes
  React.useEffect(() => {
    onSelectionChange?.(selectedItems);
  }, [selectedItems, onSelectionChange]);

  // Clear selection when search/filter parameters change (not just data reload)
  const searchFilterParams = useMemo(
    () =>
      JSON.stringify({ q: params.q, tag: params.tag, filter: params.filter }),
    [params.q, params.tag, params.filter]
  );

  React.useEffect(() => {
    clearSelection();
  }, [searchFilterParams, clearSelection]);

  const filters: ReactNode[] = [];

  if (enableSearch) {
    filters.push(
      <TextInput
        key="search"
        name="search"
        value={search}
        placeholder={searchPlaceholder ?? t("search")}
        onChange={setSearch}
        hideLabel={true}
        icon={<SearchIcon />}
      />
    );
  }

  if (tagEntity) {
    filters.push(
      <TagPicker
        key="tags"
        entity={tagEntity}
        value={params.tag ?? []}
        onChange={(tag) => setParams({ ...params, tag })}
        placeholder="Filter By Tag..."
      />
    );
  }

  // Enhanced columns with selection checkbox if enabled
  const enhancedColumns = useMemo(() => {
    if (!enableSelection) return columns;

    const selectionColumn: DataTableCol<T> = {
      key: "__selection__",
      title: (
        <div
          onClick={(e) => e.stopPropagation()}
          className="flex items-center justify-center"
        >
          {/* <div className="selection-column-checkbox">
            <CheckboxInput
              label=""
              checked={isAllSelected}
              onChange={toggleAll}
            />
          </div> */}
        </div>
      ),
      sortable: false,
      cell: ({ item }: { item: T }) => (
        <div
          onClick={(e) => e.stopPropagation()}
          className="flex items-center justify-center"
        >
          <div className="selection-column-checkbox">
            <CheckboxInput
              label=""
              checked={selectedItems.some(
                (selected) => selected[selectionKey] === item[selectionKey]
              )}
              onChange={() => toggleItem(item)}
            />
          </div>
        </div>
      ),
    };

    return [selectionColumn, ...columns];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    enableSelection,
    columns,
    isAllSelected,
    isPartiallySelected,
    toggleAll,
    selectedItems,
    selectionKey,
    toggleItem,
  ]);

  // Bulk actions component
  const BulkActionsBar = () => {
    if (
      !enableSelection ||
      selectedItems.length === 0 ||
      bulkActions.length === 0
    ) {
      return null;
    }

    return (
      <div className="bulk-actions-bar">
        <div className="bulk-actions-info">
          {t("items_selected")} {selectedItems.length}
        </div>
        <div className="bulk-actions-buttons">
          {bulkActions.map((action) => (
            <button
              key={action.key}
              className={`bulk-action-btn ${action.variant || "secondary"}`}
              disabled={action.disabled?.(selectedItems)}
              onClick={() => action.onClick(selectedItems)}
            >
              {action.icon && (
                <span className="bulk-action-icon">{action.icon}</span>
              )}
              {action.label}
            </button>
          ))}
          {/* <button
            className="bulk-action-btn secondary"
            onClick={clearSelection}
          >
            {t("clear_selection")}
          </button> */}
        </div>
      </div>
    );
  };

  return (
    <>
      {
        // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
        (title || actions || description) && (
          <Heading size="h3" title={title} actions={actions}>
            {description}
          </Heading>
        )
      }
      {filters.length > 0 && <Stack>{filters}</Stack>}

      <BulkActionsBar />

      {!results ? (
        // Loading state
        <DataTable
          columns={enhancedColumns}
          {...rest}
          items={[]}
          isLoading={true}
          columnSort={columnSort}
          onColumnSort={(onSort) => {
            const { sort, direction, ...prevParams } = params;
            setParams({ ...prevParams, ...onSort });
          }}
        />
      ) : !results.results || results.results.length === 0 ? (
        // Custom empty table with headers and clean empty state
        <div className="clean-empty-table">
          <table className="data-table">
            <thead>
              <tr>
                {enhancedColumns.map((col, index) => (
                  <th key={index} className={col.sortable ? "sortable" : ""}>
                    <div className="table-header-content">
                      {col.title || col.key}
                      {col.sortable && (
                        <button
                          className="sort-button"
                          onClick={() => {
                            const sortKey =
                              (col as DataTableCol<T>).sortKey || col.key;
                            const direction =
                              columnSort?.sort === sortKey &&
                              columnSort?.direction === "asc"
                                ? "desc"
                                : "asc";
                            setParams({
                              ...params,
                              sort: sortKey,
                              direction,
                            });
                          }}
                        >
                          {/* Sort icon here if needed */}
                        </button>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              <tr>
                <td
                  colSpan={enhancedColumns.length}
                  className="empty-state-cell"
                >
                  <div className="empty-state-content">
                    <div className="empty-state-icon">
                      <img src={SmokeyIcon} alt="Smokey" />
                    </div>
                    <h3 className="empty-state-title">
                      {t("no_data_found", "No Data Found")}
                    </h3>
                    <p className="empty-state-message">
                      {emptyStateMessage ||
                        t(
                          "empty_table_message",
                          "There are no items to display. Try adjusting your filters or search criteria."
                        )}
                    </p>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      ) : (
        // Normal table with data
        <DataTable
          columns={enhancedColumns}
          {...rest}
          items={results.results}
          isLoading={false}
          columnSort={columnSort}
          onColumnSort={(onSort) => {
            const { sort, direction, ...prevParams } = params;
            setParams({ ...prevParams, ...onSort });
          }}
          emptyMessage={null} // Override default empty message
          onRowClick={onRowClick}
        />
      )}

      <div className="search-table-footer">
        {results && results.results && results.results.length > 0 && (
          <>
            {results.total !== undefined && (
              <div className="search-table-total-count">
                {t("showing_of_total_items", {
                  count: results.results.length,
                  total: results.total,
                })}
              </div>
            )}
            <Pagination
              nextCursor={results.nextCursor}
              prevCursor={results.prevCursor}
              onPrev={(cursor) =>
                setParams({ ...params, cursor, page: "prev" })
              }
              onNext={(cursor) =>
                setParams({ ...params, cursor, page: "next" })
              }
            />
          </>
        )}
      </div>
    </>
  );
}
