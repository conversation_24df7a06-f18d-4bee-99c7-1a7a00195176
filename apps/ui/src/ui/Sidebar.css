/* ::-webkit-scrollbar {
    display: none;
} */

/* Firefox
* {
    scrollbar-width: none;
} */

/* IE/Edge */
* {
    /* -ms-overflow-style: none; */
}

/* Sidebar component styles */
.sidebar {
    /* width: 100%; */
    /* height: calc(100vh - 70px); Make sidebar full viewport height */
    /* background: var(--color-background);
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease;
    position: relative;
    overflow-y: auto; Allow scrolling within sidebar */
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Add new nav container styling */
.nav-container {
    flex: 0 1 auto; /* Changed from flex: 1 to not take all available space */
    overflow-y: auto;
    margin-bottom: 10px; /* Add space before the footer */
    padding-bottom: 10px; /* Reduced padding */
}

/* Collapsed sidebar styles */
.sidebar.collapsed {
    width: 100%; /* Will be constrained by the parent container */
}

.sidebar.collapsed .sidebar-header .logo {
    width: 100%;
    display: flex;
    justify-content: center;
}

.sidebar.collapsed .sidebar-header .logo img {
    height: 30px;
    width: auto;
}

.sidebar .sidebar-header {
    display: flex;
    position: relative;
    border-bottom: 1px solid var(--color-grey);
    padding: 15px 20px;
    gap: 10px;
    align-items: center;
}

.sidebar-header .logo {
    display: flex;
    align-items: center;
}

.sidebar-header .logo svg {
    height: 30px;
    flex-shrink: 0;
    fill: var(--color-primary);
}

/* Navigation styles */
nav {
    padding: 20px;
    width: 100%;
}

nav.nav-collapsed {
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

nav a {
    display: flex;
    padding: 15px;
    text-decoration: none;
    color: var(--color-primary);
    border-radius: var(--border-radius);
    font-size: 13px;
    font-weight: 500;
    margin-bottom: 5px;
    align-items: center;
    gap: 5px;
}

nav a.nav-link-collapsed {
    padding: 15px 0;
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
}

nav a.nav-link-collapsed span {
    display: none;
    opacity: 0;
    visibility: hidden;
    max-width: 0;
    overflow: hidden;
}

nav a.nav-link-collapsed .nav-icon {
    width: 20px;
    height: 20px;
    margin-right: 0;
}

/* Enhanced hover styles */
nav a:hover {
    color: var(--color-primary);
    /* background: var(--color-grey); */
    /* text-decoration: none; */
}

/* nav a.selected {
    color: var(--color-on-primary);
    background: var(--color-primary);
} */

nav a .nav-icon {
    width: 16px;
    height: 16px;
    display: flex;
    margin-right: 4px;
}

/* Smokey Budtender Download Section */
.smokey-download-section {
    margin: 5px 20px 15px 20px; /* Adjusted margins */
    border-radius: var(--border-radius);
    overflow: hidden;
    border: 1px solid var(--color-grey);
    transition: all 0.2s ease;
    flex: 0 0 auto; /* Don't grow or shrink */
    position: relative; /* Ensure it stays in document flow */
}

.smokey-download-section:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
    cursor: pointer;
}

.smokey-download-content {
    display: flex;
    align-items: center;
    padding: 12px 15px;
}

.smokey-icon-wrapper {
    position: relative;
}

.smokey-icon {
    width: 40px;
    height: 40px;
    object-fit: contain;
    margin-right: 12px;
}

.download-icon-overlay {
    position: absolute;
    bottom: -4px;
    right: 6px;
    background:  #3EDC81;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.download-icon-overlay svg {
    width: 12px;
    height: 12px;
}

.smokey-text {
    flex: 1;
}

.smokey-text h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--color-primary);
}

.smokey-text p {
    margin: 0;
    font-size: 12px;
    color: var(--color-text-soft);
}

/* Collapsed Smokey Download Section */
.smokey-download-collapsed {
    margin: 5px auto 15px auto; /* Adjusted margins */
    width: 50px;
    display: flex;
    justify-content: center;
}

.smokey-download-content-collapsed {
    display: flex;
    justify-content: center;
    padding: 10px;
}

.smokey-download-collapsed .smokey-icon {
    margin-right: 0;
}

.smokey-download-collapsed .smokey-icon-wrapper {
    position: relative;
}

.smokey-download-collapsed .download-icon-overlay {
    bottom: -2px;
    right: -2px;
    width: 16px;
    height: 16px;
}

.smokey-download-collapsed .download-icon-overlay svg {
    width: 10px;
    height: 10px;
}

/* Sidebar footer section */
.sidebar-footer {
    border-top: 1px solid var(--color-grey);
    width: 100%;
    margin-top: auto; /* Push to bottom */
    position: sticky;
    bottom: 0;
    background: var(--color-background); /* Match sidebar background */
    z-index: 0; /* Ensure it stays on top of scrollable content */
    box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.05); /* Subtle shadow for distinction */
}

/* Sidebar profile section */
.sidebar-profile {
    width: 100%;
}

.sidebar-profile-collapsed {
    display: flex;
    justify-content: center;
    padding: 15px 0;
}

.sidebar-profile .sidebar-profile-inner {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    overflow: hidden;
    padding: 15px 20px;
    height: 50px;
}

.sidebar-profile .sidebar-profile-inner:hover {
    background-color: var(--color-background-soft);
}

.sidebar-profile .profile-image {
    flex-shrink: 0;
    margin-right: 8px;
    width: 40px;
    height: 40px;
    background: var(--color-grey);
    border-radius: 20px;
    grid-area: image;
    overflow: hidden;
}

.sidebar-profile-collapsed .profile-image {
    margin-right: 0;
}

.sidebar-profile .profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.sidebar-profile .profile-name {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    grid-area: name;
    font-size: 14px;
    padding-left: 10px;
}

.sidebar-profile .profile-role {
    flex-shrink: 0;
    margin-left: 8px;
    grid-area: role;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    padding-left: 10px;
}

.sidebar-profile .profile-caret {
    flex-shrink: 0;
    margin-left: 8px;
    grid-area: caret;
}

/* Chat history submenu styles */
.chat-history-submenu {
  padding-left: 20px;
  display: none;
}

@media (max-width: 767px) {
  /* .chat-history-submenu {
    display: block;
  } */
}

.chat-history-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 10px 15px;
  color: var(--color-primary-soft);
  background: none;
  border: none;
  font-size: 14px;
  cursor: pointer;
}

.chat-history-list {
  padding-left: 10px;
}

.chat-history-list a {
  padding: 8px 15px;
  font-size: 14px;
}

/* Trial Countdown Styles */
.trial-countdown-section {
  background: linear-gradient(135deg, rgba(70, 60, 255, 0.1), rgba(70, 60, 255, 0.05));
  border: 1px solid rgba(70, 60, 255, 0.2);
  border-radius: 8px;
  padding: 10px;
  margin: 10px;
  text-align: center;
}

.trial-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
}

.trial-heading {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-color);
  margin: 0;
  display: inline;
}

.trial-countdown-timer {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.time-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
  background: rgba(70, 60, 255, 0.1);
  padding: 2px 5px;
  border-radius: 4px;
}

.trial-expired {
  color: var(--danger-color);
  font-weight: 600;
  display: block;
  margin-bottom: 5px;
}

.upgrade-button {
  margin-top: 5px !important;
  font-size: 12px !important;
  padding: 5px 10px !important;
  height: auto !important;
}

.trial-countdown-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  margin: 10px 0;
  background: linear-gradient(135deg, rgba(70, 60, 255, 0.1), rgba(70, 60, 255, 0.05));
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  color: var(--primary-color);
}

.trial-icon {
  margin-right: 4px;
}

/* Trial Expiry Modal Styles */
.trial-expiry-modal {
  padding: 10px;
  text-align: center;
}

.trial-expiry-modal p {
  margin-bottom: 15px;
  line-height: 1.5;
  color: var(--text-color);
}

.expiry-countdown {
  background: rgba(70, 60, 255, 0.05);
  border-radius: 8px;
  padding: 15px;
  margin: 15px 0;
}

.expiry-countdown p {
  margin-bottom: 10px;
  font-weight: 500;
}

.expiry-modal-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}

