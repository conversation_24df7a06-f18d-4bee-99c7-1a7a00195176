import "./Sidebar.css";
import NavLink from "./NavLink";
import logoPng from "../assets/BakedBot_banner_logo_beta.png";
import smokeyIcon from "../assets/smokey_icon.png";
import { Link, NavLinkProps, useNavigate } from "react-router-dom";
import {
  PropsWithChildren,
  ReactNode,
  useContext,
  useState,
  useEffect,
} from "react";
import Button from "./Button";
import { ChevronDownIcon, MenuIcon, PosIcon, ChatIcon } from "./icons";
import { FiActivity } from "react-icons/fi";
import clsx from "clsx";
import Menu, { MenuItem } from "./Menu";
import {
  AdminContext,
  OrganizationContext,
  LocationContext,
} from "../contexts";
import { PreferencesContext } from "./PreferencesContext";
import api from "../api";
import { snakeToTitle } from "../utils";
import Modal from "./Modal";
import RadioInput from "./form/RadioInput";
import { useTranslation } from "react-i18next";
import useValidateSubscription from "../hooks/useValidateSubscription";
import Countdown from "react-countdown";

export interface SidebarLink extends NavLinkProps {
  key: string;
  icon: ReactNode;
  premiumFeature?: boolean;
}

interface SidebarProps {
  links?: SidebarLink[];
  prepend?: ReactNode;
  append?: ReactNode;
  collapsed?: boolean;
  customContent?: ReactNode;
}

// Countdown renderer props interface
interface CountdownRendererProps {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  completed: boolean;
}

// Local HeartPulseIcon component
const HeartPulseIcon = () => <FiActivity className="icon" />;

// Renderer for countdown timer
const countdownRenderer = ({
  days,
  hours,
  minutes,
  seconds,
  completed,
}: CountdownRendererProps) => {
  if (completed) {
    return <span className="trial-expired">Trial expired</span>;
  } else {
    return (
      <div className="trial-countdown">
        <div className="trial-countdown-timer">
          <span className="time-value">{days}d</span>
          <span className="time-value">{hours}h</span>
          <span className="time-value">{minutes}m</span>
          <span className="time-value">{seconds}s</span>
        </div>
      </div>
    );
  }
};

export default function Sidebar({
  children,
  links,
  prepend,
  append,
  collapsed = false,
  customContent,
}: PropsWithChildren<SidebarProps>) {
  const { t, i18n } = useTranslation();
  const profile = useContext(AdminContext);
  const [location] = useContext(LocationContext);
  const [organization] = useContext(OrganizationContext);
  const navigate = useNavigate();
  const [preferences, setPreferences] = useContext(PreferencesContext);
  const [isOpen, setIsOpen] = useState(false);
  const [isLanguageOpen, setIsLanguageOpen] = useState(false);
  const [isTrialExpiryModalOpen, setIsTrialExpiryModalOpen] = useState(false);
  const {
    isSubscribed,
    isPro,
    isTrialActive,
    trialEndDate,
    isArtificialTrial,
  } = useValidateSubscription();
  const hasSubscription = isSubscribed;

  // Check if trial is about to expire
  useEffect(() => {
    if (isTrialActive && trialEndDate) {
      const now = new Date();
      const twoDaysInMs = 2 * 24 * 60 * 60 * 1000;
      const timeToExpiry = trialEndDate.getTime() - now.getTime();

      // If less than 2 days remaining and the modal hasn't been shown yet
      if (timeToExpiry < twoDaysInMs && timeToExpiry > 0) {
        // Check if we've already shown the modal in the last 24 hours
        const lastShown = localStorage.getItem("trialExpiryModalLastShown");
        if (
          !lastShown ||
          Date.now() - parseInt(lastShown, 10) > 24 * 60 * 60 * 1000
        ) {
          setIsTrialExpiryModalOpen(true);
          localStorage.setItem(
            "trialExpiryModalLastShown",
            Date.now().toString()
          );
        }
      }
    }
  }, [isTrialActive, trialEndDate]);

  const handleDownloadBudtender = () => {
    const link = document.createElement("a");
    link.href = "https://beta.bakedbot.ai/downloads/bakedbot-chatbot.zip";
    link.download = "bakedbot-chatbot.zip";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleUpgradeClick = () => {
    navigate("/upgrade");
  };

  return (
    <div>
      <section
        className={clsx("sidebar", { "is-open": isOpen, collapsed: collapsed })}
      >
        {prepend}
        <div className="nav-container">
          {customContent ? (
            customContent
          ) : (
            <nav className={collapsed ? "nav-collapsed" : ""}>
              {links?.map(({ key, premiumFeature, icon, ...props }) => (
                <div key={key} className="nav-link-wrapper">
                  <NavLink
                    {...props}
                    key={key}
                    icon={icon}
                    collapsed={collapsed}
                    onClick={() => {
                      if (
                        premiumFeature &&
                        !hasSubscription &&
                        !isArtificialTrial
                      ) {
                        navigate("/upgrade");
                      } else {
                        setIsOpen(true);
                      }
                    }}
                    className={clsx(props.className, {
                      "nav-link-collapsed": collapsed,
                      "premium-feature": premiumFeature && !isArtificialTrial,
                    })}
                  />
                  {premiumFeature && !isArtificialTrial && !collapsed && (
                    <span
                      className="premium-badge"
                      title={t("upgrade_required")}
                    >
                      PRO
                    </span>
                  )}
                  {premiumFeature && !isArtificialTrial && collapsed && (
                    <span
                      className="premium-badge-small"
                      title={t("upgrade_required")}
                    >
                      ⭐
                    </span>
                  )}
                </div>
              ))}
            </nav>
          )}
        </div>
        {append}

        {/* Trial Countdown Timer - only show when user has an active trial */}
        {isTrialActive && trialEndDate && !collapsed && (
          <div className="trial-countdown-section">
            <div className="trial-header">
              <span className="trial-icon">⏱</span>
              <h4 className="trial-heading">
                {isArtificialTrial ? "Free Trial" : "Trial"} Remaining:
              </h4>
            </div>
            <Countdown date={trialEndDate} renderer={countdownRenderer} />
            {isArtificialTrial && (
              <Button
                className="upgrade-button"
                onClick={handleUpgradeClick}
                variant="primary"
                size="small"
              >
                {t("upgrade_now")}
              </Button>
            )}
          </div>
        )}

        {/* Collapsed version of trial countdown */}
        {isTrialActive && trialEndDate && collapsed && (
          <div className="trial-countdown-collapsed">
            <div className="trial-icon">⏱</div>
            <Countdown
              date={trialEndDate}
              renderer={({
                days,
                completed,
              }: {
                days: number;
                completed: boolean;
              }) => {
                if (completed) return <span>0d</span>;
                return <span>{days}d</span>;
              }}
            />
          </div>
        )}

        {/* Smokey Budtender Download Section - Now positioned after nav links */}
        {!collapsed ? (
          <div className="smokey-download-section">
            <div
              className="smokey-download-content"
              onClick={handleDownloadBudtender}
            >
              <div className="smokey-icon-wrapper">
                <img
                  src={smokeyIcon}
                  alt="Smokey Budtender"
                  className="smokey-icon"
                />
                <div className="download-icon-overlay">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M12 16l-5-5h3V4h4v7h3l-5 5zm5 4H7v-2h10v2z" />
                  </svg>
                </div>
              </div>
              <div className="smokey-text">
                <h4>Download Now</h4>
                <p>Get our AI-powered budtender for your business</p>
              </div>
            </div>
          </div>
        ) : (
          <div
            className="smokey-download-section smokey-download-collapsed"
            onClick={handleDownloadBudtender}
          >
            <div className="smokey-download-content-collapsed">
              <div className="smokey-icon-wrapper">
                <img
                  src={smokeyIcon}
                  alt="Smokey Budtender"
                  className="smokey-icon"
                />
                <div className="download-icon-overlay">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M12 16l-5-5h3V4h4v7h3l-5 5zm5 4H7v-2h10v2z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="sidebar-footer">
          {profile && !collapsed && (
            <div className="sidebar-profile">
              <Menu
                button={
                  <div className="sidebar-profile-inner">
                    <div className="profile-image">
                      <img
                        src={profile.image_url}
                        referrerPolicy="no-referrer"
                      />
                    </div>
                    <span className="profile-name">
                      {profile.first_name
                        ? `${profile.first_name} ${profile.last_name || ""}`
                        : "User"}
                    </span>
                    <div className="profile-role">
                      {snakeToTitle(location.role ?? organization.username)}
                    </div>
                    <div className="profile-caret">
                      <ChevronDownIcon />
                    </div>
                  </div>
                }
              >
                <MenuItem onClick={() => navigate("/organization/settings")}>
                  {t("settings")}
                </MenuItem>
                <MenuItem onClick={() => setIsLanguageOpen(true)}>
                  {t("language")}
                </MenuItem>
                <MenuItem
                  onClick={() =>
                    setPreferences({
                      ...preferences,
                      mode: preferences.mode === "dark" ? "light" : "dark",
                    })
                  }
                >
                  {preferences.mode === "dark"
                    ? t("light_mode")
                    : t("dark_mode")}
                </MenuItem>
                <MenuItem onClick={async () => await api.auth.logout()}>
                  {t("sign_out")}
                </MenuItem>
              </Menu>
            </div>
          )}
          {profile && collapsed && (
            <div className="sidebar-profile sidebar-profile-collapsed">
              <div className="profile-image">
                <img src={profile.image_url} referrerPolicy="no-referrer" />
              </div>
            </div>
          )}
        </div>
      </section>
      <main
        className={clsx({
          "is-open": isOpen,
          "with-collapsed-sidebar": collapsed,
        })}
      >
        {children}
      </main>

      {/* Language Selection Modal */}
      <Modal
        open={isLanguageOpen}
        onClose={() => setIsLanguageOpen(false)}
        title={"Language"}
      >
        <RadioInput
          label={t("language")}
          options={[
            { label: "English", key: "en" },
            { label: "Espańol", key: "es" },
          ]}
          value={i18n.language}
          onChange={(value) => {
            setPreferences({ ...preferences, lang: value });
          }}
        />
      </Modal>

      {/* Trial Expiry Modal */}
      <Modal
        open={isTrialExpiryModalOpen}
        onClose={() => setIsTrialExpiryModalOpen(false)}
        title={
          isArtificialTrial
            ? "Your Free Trial Is About To Expire"
            : "Your Trial Is About To Expire"
        }
      >
        <div className="trial-expiry-modal">
          <p>
            {isArtificialTrial
              ? "Your free trial period is ending soon. To continue using all premium features, please upgrade your subscription."
              : "Your trial period is ending soon. To continue using all features without interruption, please upgrade your subscription."}
          </p>

          {trialEndDate && (
            <div className="expiry-countdown">
              <p>Time remaining:</p>
              <Countdown date={trialEndDate} renderer={countdownRenderer} />
            </div>
          )}

          <div className="expiry-modal-actions">
            <Button
              variant="primary"
              onClick={() => {
                handleUpgradeClick();
                setIsTrialExpiryModalOpen(false);
              }}
            >
              Upgrade Now
            </Button>
            <Button
              variant="secondary"
              onClick={() => setIsTrialExpiryModalOpen(false)}
            >
              Remind Me Later
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}
