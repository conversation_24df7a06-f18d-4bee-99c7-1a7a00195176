.table-renderer {
  margin: 1rem 0;
  width: 100%;
}

.table-title {
  margin-bottom: 0.75rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--color-brunswick-green, #23504A);
}

.table-container {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 350px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: var(--color-background, #ffffff);
  border: 1px solid var(--color-border, #e5e7eb);
  margin-bottom: 1rem;
  scrollbar-width: thin;
  -ms-overflow-style: none;
}

.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.table-renderer table {
  width: 100%;
  border-collapse: collapse;
  min-width: 100%;
}

.table-renderer th {
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  text-align: left;
  padding: 0.75rem 1rem;
  font-weight: 600;
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
  position: relative;
  border-bottom: 2px solid var(--color-border, #e5e7eb);
  position: sticky;
  top: 0;
  z-index: 2;
}

.table-renderer th:hover {
  background-color: var(--color-dark-jungle-green, #0D211D);
}

.table-renderer th.sorted-asc,
.table-renderer th.sorted-desc {
  background-color: var(--color-pine-green, #00766D);
}

.table-renderer td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--color-border, #e5e7eb);
}

/* Align numeric columns to the right */
.table-renderer td[data-type="number"],
.table-renderer td[data-type="currency"] {
  text-align: right;
}

/* Highlight revenue/price columns */
.table-renderer td[data-type="currency"] {
  color: var(--color-emerald-green, #22AD85);
  font-weight: 500;
}

.table-renderer tr:last-child td {
  border-bottom: none;
}

.table-renderer tr:nth-child(even) {
  background-color: var(--color-honeydew, #DFF4E9);
}

.table-renderer tr:hover {
  background-color: var(--color-honeydew, #DFF4E9);
  opacity: 0.9;
}

.sort-indicator {
  margin-left: 0.25rem;
  font-weight: bold;
}

/* Empty state */
.table-renderer .empty-state {
  padding: 2rem;
  text-align: center;
  color: var(--color-text-muted, #6b7280);
}

/* Dark theme styles */
[data-theme="dark"] .table-title {
  color: var(--color-emerald-green, #22AD85);
}

[data-theme="dark"] .table-container {
  background-color: var(--color-surface-elevated, #1e2538);
  border-color: var(--color-divider, #374151);
}

[data-theme="dark"] .table-renderer th {
  background-color: var(--color-brunswick-green, #23504A);
  border-bottom-color: var(--color-divider, #374151);
}

[data-theme="dark"] .table-renderer th:hover {
  background-color: var(--color-dark-jungle-green, #0D211D);
}

[data-theme="dark"] .table-renderer td {
  border-bottom-color: var(--color-divider, #374151);
}

[data-theme="dark"] .table-renderer tr:nth-child(even) {
  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.3));
}

[data-theme="dark"] .table-renderer tr:hover {
  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));
}

@media (max-width: 768px) {
  .table-container {
    max-height: 250px;
  }
  
  .table-renderer th,
  .table-renderer td {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }
  
  /* Stack header and data on small screens */
  .table-renderer th {
    position: sticky;
    top: 0;
  }
  
  .table-renderer th .sort-indicator {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
  }
} 