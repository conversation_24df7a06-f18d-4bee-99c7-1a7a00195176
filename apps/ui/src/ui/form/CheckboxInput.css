.checkbox-wrapper {
  display: inline-flex;
  align-items: center;
  margin-bottom: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
  font-size: 14px;
  color: var(--color-text-primary);
}

.checkbox-label.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.checkbox-input {
  margin-right: 8px;
  cursor: pointer;
  appearance: none;
  width: 18px;
  height: 18px;
  border: 1px solid var(--color-border);
  border-radius: 3px;
  background-color: var(--color-background);
  position: relative;
  outline: none;
  transition: border-color 0.2s, background-color 0.2s;
}

.checkbox-input:checked {
  background-color: var(--color-checkbox-checked-bg);
  border-color: var(--color-checkbox-checked-bg);
}

.checkbox-input:checked::after {
  content: '';
  position: absolute;
  display: block;
  left: 13px;
  top: 4px;
  width: 6px;
  height: 11px;
  border: solid white;
  border-width: 0 1px 1px 0;
  transform: rotate(45deg);
}

.checkbox-input:focus {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
}

.checkbox-text {
  font-weight: normal;
}

.input-error {
  color: var(--color-destructive);
  font-size: 12px;
  margin-top: 4px;
} 