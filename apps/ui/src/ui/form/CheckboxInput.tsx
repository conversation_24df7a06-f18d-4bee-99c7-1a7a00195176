import { ChangeEvent, useState } from "react";
import { ControlledInputProps } from "../../types";
import "./CheckboxInput.css";

interface CheckboxInputProps
  extends Omit<ControlledInputProps<boolean>, "value"> {
  label: string;
  checked?: boolean;
  value?: boolean;
}

export default function CheckboxInput({
  label,
  checked = false,
  value,
  onChange,
  disabled = false,
  required = false,
  error,
}: CheckboxInputProps) {
  const isCheckedProp =
    checked !== undefined ? checked : value !== undefined ? value : false;
  const [isChecked, setIsChecked] = useState(isCheckedProp);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.checked;
    setIsChecked(newValue);
    onChange(newValue);
  };

  return (
    <div className="checkbox-wrapper">
      <label className={`checkbox-label ${disabled ? "disabled" : ""}`}>
        <input
          type="checkbox"
          checked={isChecked}
          onChange={handleChange}
          disabled={disabled}
          required={required}
          className="checkbox-input"
        />
        <span className="checkbox-text">{label}</span>
      </label>
      {error && <div className="input-error">{error}</div>}
    </div>
  );
}
