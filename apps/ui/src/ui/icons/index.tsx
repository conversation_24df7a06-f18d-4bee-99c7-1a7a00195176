import React from "react";
import { SVGProps } from "react";
import {
  CubeTransparentIcon,
  DocumentDuplicateIcon,
  CreditCardIcon,
  ReceiptPercentIcon,
  QuestionMarkCircleIcon,
} from "@heroicons/react/24/outline";
import {
  FiBox,
  FiFileText,
  FiActivity,
  FiChevronDown,
  FiChevronUp,
} from "react-icons/fi";
import {
  LightBulbIcon as LightBulb,
  SparklesIcon as Sparkles,
  UsersIcon as Users,
  UserCircleIcon as UserCircle,
  UserPlusIcon as UserPlus,
  XCircleIcon as XCircle,
  XMarkIcon as XMark,
} from "@heroicons/react/24/outline";

interface IconProps extends SVGProps<SVGSVGElement> {
  className?: string;
}

export const XIcon = ({ className = "icon", ...props }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className}
    {...props}
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M6 18L18 6M6 6l12 12"
    />
  </svg>
);

export const BellIcon = ({ className = "icon", ...props }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={className}
    {...props}
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0"
    />
  </svg>
);

export const LightBulbIcon = (props: IconProps) => <LightBulb {...props} />;
export const SparklesIcon = (props: IconProps) => <Sparkles {...props} />;
export const UsersIcon = (props: IconProps) => <Users {...props} />;
export const UserCircleIcon = (props: IconProps) => <UserCircle {...props} />;
export const UserPlusIcon = (props: IconProps) => <UserPlus {...props} />;
export const XCircleIcon = (props: IconProps) => <XCircle {...props} />;
export const XMarkIcon = (props: IconProps) => <XMark {...props} />;

export const DocumentIcon = (props: IconProps) => <FiFileText {...props} />;
export const CubeIcon = (props: IconProps) => <FiBox {...props} />;
