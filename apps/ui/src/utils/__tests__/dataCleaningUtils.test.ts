import { 
  cleanField, 
  cleanFormData, 
  cleanTemplateData, 
  isEmpty, 
  getDisplayValue,
  validateRequiredFields 
} from "../dataCleaningUtils";

describe("dataCleaningUtils", () => {
  describe("isEmpty", () => {
    it("should identify empty values correctly", () => {
      expect(isEmpty(null)).toBe(true);
      expect(isEmpty(undefined)).toBe(true);
      expect(isEmpty("")).toBe(true);
      expect(isEmpty("   ")).toBe(true);
      expect(isEmpty("NULL")).toBe(true);
      expect(isEmpty("null")).toBe(true);
      expect(isEmpty("undefined")).toBe(true);
      
      expect(isEmpty("test")).toBe(false);
      expect(isEmpty(0)).toBe(false);
      expect(isEmpty(false)).toBe(false);
    });
  });

  describe("cleanField", () => {
    it("should use default values for empty fields", () => {
      expect(cleanField("full_name", "")).toBe("Unknown User");
      expect(cleanField("first_name", null)).toBe("Unknown");
      expect(cleanField("last_name", undefined)).toBe("User");
      expect(cleanField("email", "")).toBe("");
      expect(cleanField("phone", "NULL")).toBe("");
    });

    it("should transform valid values", () => {
      expect(cleanField("first_name", "  John  ")).toBe("John");
      expect(cleanField("email", "  <EMAIL>  ")).toBe("<EMAIL>");
      expect(cleanField("country", "us")).toBe("US");
    });

    it("should validate email addresses", () => {
      expect(cleanField("email", "invalid-email")).toBe("");
      expect(cleanField("email", "<EMAIL>")).toBe("<EMAIL>");
    });
  });

  describe("cleanFormData", () => {
    it("should clean all fields in form data", () => {
      const dirtyData = {
        full_name: "",
        email: "  <EMAIL>  ",
        phone: "",
        address: null,
      };

      const cleanedData = cleanFormData(dirtyData);

      expect(cleanedData.full_name).toBe("Unknown User");
      expect(cleanedData.email).toBe("<EMAIL>");
      expect(cleanedData.phone).toBe("");
      expect(cleanedData.address).toBe("Unknown Address");
    });
  });

  describe("cleanTemplateData", () => {
    it("should clean template data with proper defaults", () => {
      const dirtyTemplate = {
        from: {
          name: "",
          address: null,
        },
        subject: "",
        reply_to: "",
        body: "",
      };

      const cleanedTemplate = cleanTemplateData(dirtyTemplate);

      expect(cleanedTemplate.from.name).toBe("Unknown Sender");
      expect(cleanedTemplate.from.address).toBe("<EMAIL>");
      expect(cleanedTemplate.subject).toBe("No Subject");
      expect(cleanedTemplate.reply_to).toBe("<EMAIL>");
      expect(cleanedTemplate.body).toBe("No content provided");
    });

    it("should preserve valid template data", () => {
      const validTemplate = {
        from: {
          name: "John Doe",
          address: "<EMAIL>",
        },
        subject: "Test Subject",
        reply_to: "<EMAIL>",
        body: "Test content",
      };

      const cleanedTemplate = cleanTemplateData(validTemplate);

      expect(cleanedTemplate.from.name).toBe("John Doe");
      expect(cleanedTemplate.from.address).toBe("<EMAIL>");
      expect(cleanedTemplate.subject).toBe("Test Subject");
      expect(cleanedTemplate.reply_to).toBe("<EMAIL>");
      expect(cleanedTemplate.body).toBe("Test content");
    });
  });

  describe("getDisplayValue", () => {
    it("should return display values for empty fields", () => {
      expect(getDisplayValue("full_name", "")).toBe("Unknown User");
      expect(getDisplayValue("email", null)).toBe("");
      expect(getDisplayValue("unknown_field", "")).toBe("Unknown");
    });

    it("should return actual values for non-empty fields", () => {
      expect(getDisplayValue("full_name", "John Doe")).toBe("John Doe");
      expect(getDisplayValue("email", "<EMAIL>")).toBe("<EMAIL>");
    });
  });

  describe("validateRequiredFields", () => {
    it("should return errors for missing required fields", () => {
      const data = {
        full_name: "",
        first_name: "",
        email: "<EMAIL>",
      };

      const errors = validateRequiredFields(data);

      expect(errors).toContain("full_name is required");
      expect(errors).toContain("first_name is required");
      expect(errors).not.toContain("email is required");
    });

    it("should return empty array for valid data", () => {
      const data = {
        full_name: "John Doe",
        first_name: "John",
        last_name: "Doe",
        email: "<EMAIL>",
      };

      const errors = validateRequiredFields(data);

      expect(errors).toEqual([]);
    });
  });

  describe("real-world scenarios", () => {
    it("should handle typical form submission data", () => {
      const formData = {
        full_name: "",
        email: "  <EMAIL>  ",
        phone: "",
        address: null,
        city: "  New York  ",
        state: "ny",
        zip_code: "",
      };

      const cleanedData = cleanFormData(formData);

      expect(cleanedData.full_name).toBe("Unknown User");
      expect(cleanedData.email).toBe("<EMAIL>");
      expect(cleanedData.phone).toBe("");
      expect(cleanedData.address).toBe("Unknown Address");
      expect(cleanedData.city).toBe("New York");
      expect(cleanedData.state).toBe("NY");
      expect(cleanedData.zip_code).toBe("00000");
    });

    it("should handle template data from automation creator", () => {
      const templateData = {
        from: {
          name: "",
          address: "",
        },
        subject: null,
        reply_to: undefined,
        html: "",
        text: "",
      };

      const cleanedTemplate = cleanTemplateData(templateData);

      expect(cleanedTemplate.from.name).toBe("Unknown Sender");
      expect(cleanedTemplate.from.address).toBe("<EMAIL>");
      expect(cleanedTemplate.subject).toBe("No Subject");
      expect(cleanedTemplate.reply_to).toBe("<EMAIL>");
      expect(cleanedTemplate.body).toBe("No content provided");
    });
  });
});
