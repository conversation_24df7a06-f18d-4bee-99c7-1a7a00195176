import agentsData from "../api/agents.json";

export interface AgentInfo {
  icon: string;
  name: string;
  role: string;
}

// Transform agents.json data into the format used by components
export const AGENTS: Record<string, AgentInfo> = Object.values(
  agentsData.agents
)
  .filter((agent) => !(agent as any).disabled) // Only include enabled agents
  .reduce((acc, agent) => {
    acc[agent.name] = {
      icon: agent.icon,
      name: agent.name,
      role: agent.role.replace(" & ", " & "), // Keep full role name
    };
    return acc;
  }, {} as Record<string, AgentInfo>);

// Helper function to get agent by name with fallback
export const getAgentByName = (name: string): AgentInfo | null => {
  return AGENTS[name] || null;
};

// Helper function to get all available agents as array
export const getAllAgents = (): AgentInfo[] => {
  return Object.values(AGENTS);
};

// Helper function to determine agent from insight content
// NOTE: This function is now primarily used as a fallback when agent_id/agent_name
// are not available in the insight data. The backend now provides proper agent attribution.
export const getAgentForInsight = (
  title: string,
  description: string,
  agentId?: string,
  agentName?: string
): AgentInfo | null => {
  // If we have real agent data from the backend, use it
  if (agentId && agentName && AGENTS[agentName]) {
    return AGENTS[agentName];
  }

  // If we have agent ID but no name, try to find by ID
  if (agentId) {
    const agentByKey = Object.entries(AGENTS).find(
      ([key, agent]) =>
        agent.name.toUpperCase().replace(/\s/g, "_") ===
          agentId.toUpperCase() || key === agentId
    );
    if (agentByKey) {
      return agentByKey[1];
    }
  }

  // Fallback to content-based guessing (legacy behavior)
  const titleLower = title.toLowerCase();
  const descLower = description.toLowerCase();

  if (
    titleLower.includes("product") ||
    descLower.includes("inventory") ||
    descLower.includes("strain")
  ) {
    return AGENTS.SMOKEY;
  } else if (
    titleLower.includes("campaign") ||
    titleLower.includes("marketing") ||
    descLower.includes("email")
  ) {
    return AGENTS.CRAIG;
  } else if (
    titleLower.includes("sales") ||
    titleLower.includes("revenue") ||
    titleLower.includes("performance")
  ) {
    return AGENTS.POPS;
  } else if (
    titleLower.includes("market") ||
    titleLower.includes("competitor") ||
    descLower.includes("pricing")
  ) {
    return AGENTS.EZAL;
  } else if (
    titleLower.includes("profit") ||
    titleLower.includes("margin") ||
    titleLower.includes("financial")
  ) {
    return AGENTS["MONEY MIKE"];
  } else if (
    titleLower.includes("customer") ||
    titleLower.includes("vip") ||
    titleLower.includes("loyalty")
  ) {
    return AGENTS["MRS. PARKER"];
  } else if (
    titleLower.includes("compliance") ||
    titleLower.includes("security") ||
    titleLower.includes("quality")
  ) {
    return AGENTS.DEEBO;
  }

  // Default to SMOKEY if no specific match
  return AGENTS.SMOKEY || null;
};
