/**
 * Frontend data cleaning utilities for handling empty fields
 */

export interface FieldDefault {
  field: string;
  defaultValue: any;
  required?: boolean;
  validator?: (value: any) => boolean;
  transformer?: (value: any) => any;
}

/**
 * Common field defaults for frontend forms
 */
export const FRONTEND_FIELD_DEFAULTS: FieldDefault[] = [
  {
    field: "full_name",
    defaultValue: "Unknown User",
    required: true,
    transformer: (value: any) => String(value || "").trim(),
  },
  {
    field: "first_name",
    defaultValue: "Unknown",
    required: true,
    transformer: (value: any) => String(value || "").trim(),
  },
  {
    field: "last_name",
    defaultValue: "User",
    required: true,
    transformer: (value: any) => String(value || "").trim(),
  },
  {
    field: "email",
    defaultValue: "",
    required: false,
    validator: (value: any) => {
      if (!value) return true;
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(value);
    },
    transformer: (value: any) => String(value || "").trim().toLowerCase(),
  },
  {
    field: "phone",
    defaultValue: "",
    required: false,
    transformer: (value: any) => String(value || "").trim(),
  },
  {
    field: "address",
    defaultValue: "Unknown Address",
    required: false,
    transformer: (value: any) => String(value || "").trim(),
  },
  {
    field: "city",
    defaultValue: "Unknown City",
    required: false,
    transformer: (value: any) => String(value || "").trim(),
  },
  {
    field: "state",
    defaultValue: "Unknown State",
    required: false,
    transformer: (value: any) => String(value || "").trim().toUpperCase(),
  },
  {
    field: "zip_code",
    defaultValue: "00000",
    required: false,
    transformer: (value: any) => String(value || "").trim(),
  },
  {
    field: "country",
    defaultValue: "US",
    required: false,
    transformer: (value: any) => String(value || "").trim().toUpperCase(),
  },
];

/**
 * Check if a value is considered empty
 */
export const isEmpty = (value: any): boolean => {
  return (
    value === null ||
    value === undefined ||
    value === "" ||
    value === "NULL" ||
    value === "null" ||
    value === "undefined" ||
    (typeof value === "string" && value.trim() === "")
  );
};

/**
 * Clean a single field value with defaults
 */
export const cleanField = (fieldName: string, value: any, defaults: FieldDefault[] = FRONTEND_FIELD_DEFAULTS): any => {
  const fieldDefault = defaults.find(d => d.field === fieldName);
  
  if (!fieldDefault) {
    // Basic cleaning for unknown fields
    if (isEmpty(value)) {
      return "";
    }
    return typeof value === "string" ? value.trim() : value;
  }

  // Check if value is empty
  if (isEmpty(value)) {
    return fieldDefault.defaultValue;
  }

  // Apply transformer if provided
  let cleanedValue = value;
  if (fieldDefault.transformer) {
    try {
      cleanedValue = fieldDefault.transformer(value);
    } catch (error) {
      console.warn(`Error transforming field '${fieldName}':`, error);
      return fieldDefault.defaultValue;
    }
  }

  // Validate if validator provided
  if (fieldDefault.validator && !fieldDefault.validator(cleanedValue)) {
    console.warn(`Validation failed for field '${fieldName}', using default value`);
    return fieldDefault.defaultValue;
  }

  return cleanedValue;
};

/**
 * Clean an entire object/form data
 */
export const cleanFormData = (data: Record<string, any>, defaults: FieldDefault[] = FRONTEND_FIELD_DEFAULTS): Record<string, any> => {
  const cleaned: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(data)) {
    cleaned[key] = cleanField(key, value, defaults);
  }
  
  return cleaned;
};

/**
 * Clean template data specifically for email templates
 */
export const cleanTemplateData = (templateData: any): any => {
  if (!templateData) return templateData;

  const cleaned = { ...templateData };

  // Handle 'from' field for email templates
  if (cleaned.from) {
    if (isEmpty(cleaned.from.name)) {
      cleaned.from.name = "Unknown Sender";
    }
    if (isEmpty(cleaned.from.address)) {
      cleaned.from.address = "<EMAIL>";
    }
  }

  // Handle 'reply_to' field
  if (isEmpty(cleaned.reply_to)) {
    cleaned.reply_to = cleaned.from?.address || "<EMAIL>";
  }

  // Handle subject
  if (isEmpty(cleaned.subject)) {
    cleaned.subject = "No Subject";
  }

  // Handle body/content
  if (isEmpty(cleaned.body) && isEmpty(cleaned.content)) {
    cleaned.body = "No content provided";
  }

  return cleaned;
};

/**
 * Validate required fields and return validation errors
 */
export const validateRequiredFields = (data: Record<string, any>, defaults: FieldDefault[] = FRONTEND_FIELD_DEFAULTS): string[] => {
  const errors: string[] = [];
  
  const requiredFields = defaults.filter(d => d.required);
  
  for (const fieldDefault of requiredFields) {
    const value = data[fieldDefault.field];
    
    if (isEmpty(value)) {
      errors.push(`${fieldDefault.field} is required`);
    } else if (fieldDefault.validator && !fieldDefault.validator(value)) {
      errors.push(`${fieldDefault.field} is invalid`);
    }
  }
  
  return errors;
};

/**
 * Create a form data cleaner hook for React components
 */
export const useFormDataCleaner = (defaults: FieldDefault[] = FRONTEND_FIELD_DEFAULTS) => {
  return {
    cleanField: (fieldName: string, value: any) => cleanField(fieldName, value, defaults),
    cleanFormData: (data: Record<string, any>) => cleanFormData(data, defaults),
    validateRequiredFields: (data: Record<string, any>) => validateRequiredFields(data, defaults),
    isEmpty,
  };
};

/**
 * Helper to get display value for a field (shows default if empty)
 */
export const getDisplayValue = (fieldName: string, value: any, defaults: FieldDefault[] = FRONTEND_FIELD_DEFAULTS): string => {
  if (isEmpty(value)) {
    const fieldDefault = defaults.find(d => d.field === fieldName);
    if (fieldDefault) {
      return String(fieldDefault.defaultValue);
    }
    return "Unknown";
  }

  return String(value);
};

/**
 * Helper to check if a field has been set to a default value
 */
export const isDefaultValue = (fieldName: string, value: any, defaults: FieldDefault[] = FRONTEND_FIELD_DEFAULTS): boolean => {
  const fieldDefault = defaults.find(d => d.field === fieldName);
  return fieldDefault ? value === fieldDefault.defaultValue : false;
};
