/**
 * Notification interface
 */
interface NotificationOptions {
  title: string;
  message?: string;
  type?: "info" | "success" | "warning" | "error";
  duration?: number;
}

/**
 * Global event name for toast notifications
 */
const NOTIFICATION_EVENT = "app:notification";

/**
 * Send a notification to the user
 * This uses a custom event to communicate with the NotificationProvider
 * @param options Notification options
 */
export function notify(options: NotificationOptions): void {
  const event = new CustomEvent(NOTIFICATION_EVENT, {
    detail: {
      ...options,
      type: options.type || "info",
      duration: options.duration || 5000,
    },
  });

  window.dispatchEvent(event);
}
