:root {
    --color-black: #151c2d;
    --color-white: #ffffff;

    /* Brand guide colors */
    --color-dark-jungle-green: #0D211D;
    --color-brunswick-green: #23504A;
    --color-pine-green: #00766D;
    --color-emerald-green: #22AD85;
    --color-honeydew: #DFF4E9;

    --color-primary: var(--color-black);
    --color-primary-soft: #6b707b;

    --color-grey: #eae8e8;
    --color-grey-soft: #F5F5F7;
    --color-grey-hard: #cecfd2;

    --color-background: var(--color-white);
    --color-background-soft: #F9FAFB;
    --color-on-background: var(--color-black);

    --color-on-primary: var(--color-white);
    --color-on-background: var(--color-black);
    
    /* Surface & divider colors for light mode */
    --color-surface: #ffffff;
    --color-surface-secondary: #F9FAFB;
    --color-divider: #eae8e8;

    --color-red: #D92D20;
    --color-red-soft: #FECDCA;
    --color-red-hard: #B42419;
    
    --color-blue: #2970FF;
    --color-blue-soft: #D1E0FF;
    --color-blue-hard: #004EBB;

    --color-yellow: #FEC84B;
    --color-yellow-soft: #FEF0C7;
    --color-yellow-hard: #F79009;
    
    --color-green: #32D583;
    --color-green-soft: #D1FADF;
    --color-green-hard: #039855;

    --color-shadow: rgba(0, 0, 0, 0.1);
    --color-shadow-soft: rgba(0, 0, 0, 0.05);

    --color-editor: #0d121e;

    --color-border: var(--color-grey-hard);
    --color-checkbox-checked-bg: #151c2d;

    --border-radius: 8px;
    --border-radius-inner: 6px;
    --border-radius-outer: 14px;
}

[data-theme="dark"] {
    --color-black: #121721;

    /* Brand guide colors for dark mode - slightly adjusted for better visibility */
    --color-dark-jungle-green: #132E29;
    --color-brunswick-green: #2A5C56;
    --color-pine-green: #0B8379;
    --color-emerald-green: #29BE96;
    --color-honeydew: #E5F7EF;

    --color-grey: #2b3245;
    --color-grey-soft: #252b3a;

    --color-primary: var(--color-white);
    --color-primary-soft: #919496;

    --color-background: var(--color-black);
    --color-background-soft: #1a1f2b;

    --color-on-primary: var(--color-black);
    --color-on-background: var(--color-white);

    --color-shadow: var(--color-grey);
    --color-shadow-soft: var(--color-grey-soft);
    
    /* Dark mode color variants */
    --color-red: #FF6B6B;
    --color-red-soft: rgba(255, 107, 107, 0.2);
    --color-red-hard: #FF3333;
    
    --color-blue: #5C9DFF;
    --color-blue-soft: rgba(92, 157, 255, 0.2);
    --color-blue-hard: #2E7BFF;

    --color-yellow: #FFCC29;
    --color-yellow-soft: rgba(255, 204, 41, 0.2);
    --color-yellow-hard: #FFBB00;
    
    --color-green: #4ADE80;
    --color-green-soft: rgba(74, 222, 128, 0.2);
    --color-green-hard: #22C55E;
    
    /* Surface & divider colors for dark mode */
    --color-surface: #1e2538;
    --color-surface-secondary: #2a334a;
    --color-divider: #323d5d;

    --color-border: var(--color-divider);
    --color-checkbox-checked-bg: #151c2d;
}
