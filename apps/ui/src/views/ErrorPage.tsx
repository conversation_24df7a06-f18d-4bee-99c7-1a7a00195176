import {
  isRouteErrorResponse,
  Navigate,
  useNavigate,
  useRouteError,
} from "react-router-dom";
import Alert, { AlertProps } from "../ui/Alert";
import Button from "../ui/Button";
import "./ErrorPage.css";
import api from "../api";
import { useEffect } from "react";
import { AdminContext } from "../contexts";
import { LoaderContextProvider } from "../mod";
import PricingPage from "../components/onboarding/PricingPage";
const ErrorAlert = (props: AlertProps) => {
  return (
    <section className="error-page">
      <Alert {...props} />
    </section>
  );
};

export default function ErrorPage({ status = 500 }: { status?: number }) {
  const error = useRouteError() as any;
  const navigate = useNavigate();

  console.error(error);

  let message = "";
  if (isRouteErrorResponse(error)) {
    status = error.status;
    message = error.data + "";
  }
  if (error?.response) {
    status = error.response.status;
    message = error.response.data + "";
  }

  if (status === 401) {
    // in case the data router didn't catch this already
    return <Navigate to="/login" />;
  }

  if (status === 402) {
    return (
      <LoaderContextProvider context={AdminContext}>
        <PricingPage />
      </LoaderContextProvider>
    );
  }

  if (status === 403) {
    // Clear recent locations and redirect to organization locations page
    api.auth.clearRecentLocations();
    // Use relative path for redirect
    window.location.href = "/organization/locations";
    return null; // Return null while the redirect happens
  }

  if (status === 404) {
    return (
      <ErrorAlert
        variant="plain"
        title="Looks Like You're Lost!"
        actions={
          <div style={{ display: "flex", gap: "10px" }}>
            <Button onClick={() => navigate("/")}>Go Back</Button>
            <Button onClick={() => api.auth.logout()}>Sign Out</Button>
          </div>
        }
      >
        The page or resource you are looking for does not exist or has been
        moved.
      </ErrorAlert>
    );
  }

  return (
    <ErrorAlert variant="error" title={`Error [${status.toString()}]`}>
      {message}
    </ErrorAlert>
  );
}

export function AccessDenied() {
  const navigate = useNavigate();
  const handleLogout = async () => {
    await api.auth.logout();
  };
  useEffect(() => {
    api.auth.clearRecentLocations();
  }, []);

  return (
    <ErrorAlert
      variant="warn"
      title="Access Denied"
      actions={
        <div style={{ display: "flex", gap: "10px" }}>
          <Button onClick={() => navigate("/organization/locations")}>
            Go to My Locations
          </Button>
          <Button onClick={handleLogout}>Logout</Button>
        </div>
      }
    >
      Additional permission is required in order to access this section. Please
      reach out to your administrator.
    </ErrorAlert>
  );
}

export function ComingSoonPage() {
  return (
    <ErrorAlert
      variant="plain"
      title="Coming Soon!"
      actions={<Button onClick={() => window.history.back()}>Go Back</Button>}
    >
      This feature is currently under development. Check back later for updates!
    </ErrorAlert>
  );
}
