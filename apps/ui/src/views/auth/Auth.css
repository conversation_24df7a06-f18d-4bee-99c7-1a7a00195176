.auth {
    background: var(--color-background-soft);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    flex-direction: column;
    gap: 15px;
    padding: 40px 0;
}

.auth .logo svg {
    height: 40px;
}

.auth-step {
    min-width: 400px;
    max-width: 600px;
    background: var(--color-background);
    border: 1px solid var(--color-grey-soft);
    border-radius: var(--border-radius-outer);
    padding: 40px;
}

.auth.login .auth-step {
    min-width: 300px;
    width: 100%;
    max-width: 350px;
}

.auth-step h1, .auth-step h2 {
    margin: 0;
}

.auth-step .form {
    padding: 10px 0;
}

.auth-methods {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.auth.login .ui-button,
.auth.login .form-submit {
    width: 100%;
}

.auth.login form {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-actions {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.auth-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
    align-items: center;
}

.auth-step .ui-button {
    width: 100%;
}

.auth-step .ui-button[variant="plain"] {
    color: var(--color-primary);
}

.divider {
    margin: 20px 0;
    text-align: center;
    position: relative;
}

.divider::before,
.divider::after {
    content: "";
    height: 1px;
    background: var(--color-grey-soft);
    position: absolute;
    top: 50%;
    width: 40%;
}

.divider::before {
    left: 0;
}

.divider::after {
    right: 0;
}

@media only screen and (max-width: 600px) {
    .auth {
        padding: 40px 20px;
    }

    .auth-step {
        min-width: auto;
        width: 100%;
    }
}

.auth-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

.success-checkmark {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--color-green);
  display: flex;
  align-items: center;
  justify-content: center;
  transform: scale(0);
  animation: scaleIn 0.3s ease-out forwards;
}

.success-checkmark svg {
  width: 30px;
  height: 30px;
  stroke: white;
  stroke-width: 3;
  stroke-linecap: round;
  stroke-linejoin: round;
  transform: scale(0);
  animation: checkmark 0.2s ease-out 0.3s forwards;
}

@keyframes scaleIn {
  from { transform: scale(0); }
  to { transform: scale(1); }
}

@keyframes checkmark {
  from { transform: scale(0); }
  to { transform: scale(1); }
}