import { useSearchPara<PERSON>, useNavigate } from "react-router-dom";
import logoPng from "../../assets/BakedBot_banner_logo_beta.png";
import { env } from "../../config/env";
import Button from "../../ui/Button";
import "./Auth.css";
import { useEffect, useState } from "react";
import { Alert } from "../../ui";
import { useTranslation } from "react-i18next";
import {
  getAuth,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  sendPasswordResetEmail,
  AuthError as FirebaseAuthError,
} from "@firebase/auth";
import TextInput from "../../ui/form/TextInput";
import api from "../../api";
import { CheckIcon } from "../../ui/icons";

// Helper function to get user-friendly error messages for Firebase Auth errors
const getFirebaseErrorMessage = (
  error: any,
  t: (key: string) => string,
  isRegister: boolean
): string => {
  // Check if it's a Firebase Auth error
  if (error?.code) {
    switch (error.code) {
      case "auth/email-already-in-use":
        return t("auth_error_email_already_in_use");
      case "auth/weak-password":
        return t("auth_error_weak_password");
      case "auth/invalid-email":
        return t("auth_error_invalid_email");
      case "auth/user-disabled":
        return t("auth_error_user_disabled");
      case "auth/user-not-found":
        return t("auth_error_user_not_found");
      case "auth/wrong-password":
        return t("auth_error_wrong_password");
      case "auth/too-many-requests":
        return t("auth_error_too_many_requests");
      case "auth/operation-not-allowed":
        return t("auth_error_operation_not_allowed");
      case "auth/invalid-credential":
        return t("auth_error_invalid_credential");
      case "auth/requires-recent-login":
        return t("auth_error_requires_recent_login");
      case "auth/popup-closed-by-user":
        return t("auth_error_popup_closed");
      case "auth/popup-blocked":
        return t("auth_error_popup_blocked");
      case "auth/cancelled-popup-request":
        return t("auth_error_popup_cancelled");
      default:
        // For unknown Firebase errors, fall back to generic message
        break;
    }
  }

  // Check if the error message contains EMAIL_EXISTS (from Google Identity Toolkit)
  if (
    error?.message?.includes("EMAIL_EXISTS") ||
    error?.error?.message === "EMAIL_EXISTS"
  ) {
    return t("auth_error_email_already_in_use");
  }

  // Fallback to generic error messages
  return isRegister ? t("registration_error") : t("login_error");
};

export default function Login() {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const [message, setMessage] = useState<string>();
  const [errorType, setErrorType] = useState<"error" | "info">("info");
  const [isRegister, setIsRegister] = useState<boolean>(false);
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [confirmPassword, setConfirmPassword] = useState<string>("");
  const [showForgotPassword, setShowForgotPassword] = useState<boolean>(false);
  const navigate = useNavigate();
  const auth = getAuth();
  const googleProvider = new GoogleAuthProvider();

  // Check URL parameters on component mount
  useEffect(() => {
    const action = searchParams.get("action");
    const emailParam = searchParams.get("email");

    if (action === "register") {
      setIsRegister(true);
    }

    if (emailParam) {
      setEmail(emailParam);
    }
  }, [searchParams]);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);

  const handleEmailAuth = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage(undefined); // Clear previous messages
    setErrorType("info");

    try {
      if (isRegister) {
        if (password !== confirmPassword) {
          setMessage(t("passwords_do_not_match"));
          setErrorType("error");
          return;
        }

        const userCredential = await createUserWithEmailAndPassword(
          auth,
          email,
          password
        );
        const idToken = await userCredential.user.getIdToken();
        console.log("Firebase token details:", {
          uid: userCredential.user.uid,
          email: userCredential.user.email,
          tokenLength: idToken.length,
          tokenFirstChars: idToken.substring(0, 20) + "...",
          emailVerified: userCredential.user.emailVerified,
          providerData: userCredential.user.providerData,
        });
        await api.auth.firebaseAuth(idToken);
        setIsSuccess(true);
        setTimeout(() => {
          navigate(searchParams.get("r") ?? "/");
        }, 1000);
      } else {
        const userCredential = await signInWithEmailAndPassword(
          auth,
          email,
          password
        );
        const idToken = await userCredential.user.getIdToken();
        console.log("Firebase token details:", {
          uid: userCredential.user.uid,
          email: userCredential.user.email,
          tokenLength: idToken.length,
          tokenFirstChars: idToken.substring(0, 20) + "...",
          emailVerified: userCredential.user.emailVerified,
          providerData: userCredential.user.providerData,
        });
        await api.auth.firebaseAuth(idToken);
        setIsSuccess(true);
        setTimeout(() => {
          navigate(searchParams.get("r") ?? "/");
        }, 1000);
      }
    } catch (error) {
      console.error("Email auth error:", error);
      const errorMessage = getFirebaseErrorMessage(error, t, isRegister);
      setMessage(errorMessage);
      setErrorType("error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleAuth = async () => {
    setIsLoading(true);
    setMessage(undefined); // Clear previous messages
    setErrorType("info");

    try {
      const result = await signInWithPopup(auth, googleProvider);
      const idToken = await result.user.getIdToken();
      console.log("Google Sign-in Firebase token details:", {
        uid: result.user.uid,
        email: result.user.email,
        tokenLength: idToken.length,
        tokenFirstChars: idToken.substring(0, 20) + "...",
        emailVerified: result.user.emailVerified,
        providerData: result.user.providerData,
      });
      await api.auth.firebaseAuth(idToken);
      setIsSuccess(true);
      setTimeout(() => {
        navigate(searchParams.get("r") ?? "/");
      }, 1000);
    } catch (error) {
      console.error("Google auth error:", error);
      const errorMessage = getFirebaseErrorMessage(error, t, false);
      setMessage(errorMessage);
      setErrorType("error");
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage(undefined); // Clear previous messages
    setErrorType("info");

    try {
      await sendPasswordResetEmail(auth, email);
      setMessage(t("password_reset_email_sent"));
      setErrorType("info");
      setShowForgotPassword(false);
    } catch (error) {
      console.error("Password reset error:", error);
      const errorMessage = getFirebaseErrorMessage(error, t, false);
      setMessage(errorMessage);
      setErrorType("error");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="auth login">
      <div className="logo">
        <img src={logoPng} alt="Logo" />
      </div>
      <div className="auth-step">
        {isSuccess ? (
          <div className="auth-success">
            <div className="success-checkmark">
              <CheckIcon />
            </div>
            <h2>{t("login_successful")}</h2>
          </div>
        ) : (
          <>
            <h2 style={{ textAlign: "center" }}>
              {showForgotPassword
                ? t("forgot_password")
                : isRegister
                ? t("register")
                : t("login")}
            </h2>
            {message && (
              <Alert
                variant={errorType}
                title={isRegister ? t("registration") : t("login")}
              >
                {message}
              </Alert>
            )}

            {showForgotPassword ? (
              <form onSubmit={handleForgotPassword}>
                <TextInput
                  name="email"
                  value={email}
                  onChange={(e) => setEmail(e)}
                  placeholder={t("email")}
                  required
                />
                <div className="form-actions">
                  <Button type="submit" disabled={isLoading}>
                    {t("send_reset_link")}
                  </Button>
                  <Button
                    variant="plain"
                    onClick={() => setShowForgotPassword(false)}
                    disabled={isLoading}
                  >
                    {t("back_to_login")}
                  </Button>
                </div>
              </form>
            ) : (
              <form onSubmit={handleEmailAuth}>
                <TextInput
                  name="email"
                  value={email}
                  onChange={(e) => setEmail(e)}
                  placeholder={t("email")}
                  required
                />
                <TextInput
                  name="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e)}
                  placeholder={t("password")}
                  required
                />
                {isRegister && (
                  <TextInput
                    name="confirmPassword"
                    label={t("confirm_password")}
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e)}
                    placeholder={t("confirm_password")}
                    required
                  />
                )}
                <div className="form-actions">
                  <Button type="submit" disabled={isLoading}>
                    {isRegister ? t("register") : t("login")}
                  </Button>
                </div>
              </form>
            )}

            {!showForgotPassword && (
              <>
                <div className="auth-links">
                  <Button
                    variant="plain"
                    onClick={() => setIsRegister(!isRegister)}
                  >
                    {isRegister
                      ? t("have_account_login")
                      : t("no_account_register")}
                  </Button>
                  {!isRegister && (
                    <Button
                      variant="plain"
                      onClick={() => setShowForgotPassword(true)}
                    >
                      {t("forgot_password_link")}
                    </Button>
                  )}
                </div>
                <div className="divider">{t("or")}</div>
                <Button onClick={handleGoogleAuth} disabled={isLoading}>
                  {t("login_with_google")}
                </Button>
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
}
