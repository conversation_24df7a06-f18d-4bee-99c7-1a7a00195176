import { <PERSON><PERSON>utton } from "../../ui/Button";
import { useTranslation } from "react-i18next";
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import api from "../../api";
import { FormEvent } from "react";

export default function Onboarding() {
  const { t } = useTranslation();
  const [inviteCode, setInviteCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [checkingLocations, setCheckingLocations] = useState(true);
  const [hasLocations, setHasLocations] = useState(false);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  // Check if user has any locations
  useEffect(() => {
    async function checkLocations() {
      try {
        const response = await api.locations.all();
        setHasLocations(response && response.length > 0);
        setCheckingLocations(false);
      } catch (error) {
        console.error("Error checking locations:", error);
        setCheckingLocations(false);
      }
    }

    checkLocations();
  }, []);

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError("");

    // If no invite code provided, skip validation and proceed
    if (!inviteCode.trim()) {
      navigate("/onboarding/path-selection");
      return;
    }

    setIsLoading(true);
    try {
      // Validate the invite code with the API
      const response = await api.invites.validateCode(inviteCode.trim());

      if (response.valid) {
        // Store the validated invite code in session storage
        // We'll use this later when creating the first location
        sessionStorage.setItem("onboardingInviteCode", inviteCode.trim());

        // Navigate to the next step - this will either be the path selection
        // or directly to location creation depending on the flow
        navigate("/onboarding/path-selection");
      } else {
        // Use the specific error message from the API response
        setError(response.error || "Invalid invite code. Please try again.");
      }
    } catch (error) {
      setError("Server error. Please try again.");
      console.error("Error validating invite code:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle skip button click
  const handleSkip = () => {
    navigate("/onboarding/path-selection");
  };

  // If we're still checking for locations, show loading
  if (checkingLocations) {
    return (
      <div className="auth-step flex flex-col items-center justify-center max-w-2xl mx-auto text-center py-12 px-6">
        <p>Checking account status...</p>
      </div>
    );
  }

  // If user already has locations, skip the invite code check
  if (hasLocations) {
    // Just redirect to the path selection
    navigate("/onboarding/path-selection");
    return null;
  }

  return (
    <div className="auth-step flex flex-col items-center justify-center max-w-2xl mx-auto text-center py-12 px-6">
      <div className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold mb-4 leading-tight">
          Don't have your data? No problem—we'll predict it.
        </h1>
        <p className="text-lg md:text-xl text-gray-500 mb-8">
          Smokey's AI predicts what sells in your area, analyzes competitors,
          and builds custom marketing in minutes—even without your POS data.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="w-full max-w-md mb-6">
        <div className="mb-4">
          <label
            htmlFor="inviteCode"
            className="block text-left text-sm font-medium text-gray-700 mb-1"
          >
            Enter your invite code{" "}
            <span className="text-gray-500 font-normal">(optional)</span>
          </label>
          <input
            type="text"
            id="inviteCode"
            value={inviteCode}
            onChange={(e) => setInviteCode(e.target.value)}
            placeholder="e.g. MCBA-2025"
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
          />
          {error && (
            <p className="text-red-500 text-sm mt-1 text-left">{error}</p>
          )}
          <p className="text-gray-500 text-xs mt-1 text-left">
            Have an invite code? Enter it above. Otherwise, you can skip this
            step.
          </p>
        </div>
        <button
          type="submit"
          disabled={isLoading}
          className="w-full px-8 py-3 text-lg font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 text-center inline-flex items-center justify-center bg-primary text-white mb-3"
        >
          {isLoading
            ? inviteCode.trim()
              ? "Validating..."
              : "Continuing..."
            : inviteCode.trim()
            ? "Validate Code & Continue"
            : "Continue Without Code"}
        </button>
      </form>

      {/* Alternative skip option */}
      {inviteCode.trim() && (
        <div className="text-center">
          <button
            onClick={handleSkip}
            className="text-gray-500 hover:text-gray-700 underline"
          >
            Skip and continue without invite code
          </button>
        </div>
      )}

      <p className="text-sm text-gray-400 mt-4">
        <a
          href="https://www.benzinga.com/general/biotech/24/10/41186253/budtenders-letting-you-down-meet-the-weed-bot-that-actually-knows-what-you-want"
          target="_blank"
          rel="noopener noreferrer"
        >
          As seen in Benzinga
        </a>
      </p>
    </div>
  );
}
