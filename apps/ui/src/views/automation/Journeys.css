/* Override the page background */
:root {
  --page-background: var(--color-background);
}

/* Journeys Container Layout */
.journeys-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
}

/* Filter and search area */
.journeys-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  width: 100%;
  border-bottom: 1px solid var(--color-divider);
  padding-bottom: 0.5rem;
}

/* Create <PERSON><PERSON> Styling */
.journeys-header .ui-button {
  color: var(--color-white);
  border-radius: 9999px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: background-color 0.2s, transform 0.1s;
}

.journeys-header .ui-button:hover {
  background-color: var(--color-pine-green);
  transform: translateY(-1px);
}

.journeys-header .ui-button svg {
  width: 16px;
  height: 16px;
}

/* Search styling */
.journeys-search {
  width: 300px;
  position: relative;
}

.journeys-search input {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.9rem;
  color: var(--color-on-background);
  background-color: var(--color-surface);
  border: 1px solid var(--color-divider);
}

.journeys-search input::placeholder {
  color: var(--color-primary-soft);
}

.journeys-search input:focus {
  outline: none;
  border-color: var(--color-emerald-green);
}

/* Journeys Grid Layout */
.journeys-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: 1.25rem;
  width: 100%;
}

/* Journey Card Styling */
.journey-card {
  display: flex;
  flex-direction: column;
  background-color: var(--color-surface);
  border: 1px solid var(--color-divider);
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  cursor: pointer;
}

.journey-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--color-shadow);
}

/* Journey Card Header */
.journey-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: var(--color-surface-secondary);
  border-bottom: 1px solid var(--color-divider);
}

.journey-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.journey-card-icon .placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--color-emerald-green);
  color: var(--color-white);
  border-radius: 50%;
}

.journey-card-icon .placeholder svg {
  width: 16px;
  height: 16px;
}

.journey-card-actions {
  color: var(--color-primary-soft);
}

/* Journey Card Content */
.journey-card-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.journey-card-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.journey-card-title h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--color-on-background);
  line-height: 1.3;
}

.journey-description {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: var(--color-primary-soft);
  line-height: 1.4;
}

/* Journey status tag styling */
.journey-card-title .ui-tag {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 4px;
  margin-left: 1rem;
  white-space: nowrap;
  flex-shrink: 0;
}

.journey-card-title .ui-tag.plain {
  background-color: var(--color-grey-soft);
  color: var(--color-primary-soft);
}

.journey-card-title .ui-tag.success {
  background-color: var(--color-green-soft);
  color: var(--color-green-hard);
}

/* Journey Metrics */
.journey-card-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding-top: 0.5rem;
  margin-top: 0.5rem;
  border-top: 1px solid var(--color-divider);
}

.metric-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.metric-label {
  font-size: 0.75rem;
  color: var(--color-primary-soft);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  font-size: 0.875rem;
  color: var(--color-on-background);
  font-weight: 500;
}

.metric-subvalue {
  font-size: 0.75rem;
  color: var(--color-primary-soft);
  margin-top: 0.25rem;
}

/* Journey Card Footer */
.journey-card-footer {
  margin-top: auto;
  padding-top: 0.75rem;
  border-top: 1px solid var(--color-divider);
  font-size: 0.75rem;
  color: var(--color-primary-soft);
}

.updated-at {
  font-style: italic;
}

/* Loading and Empty States */
.loading-state,
.empty-state {
  grid-column: 1 / -1;
  padding: 3rem;
  text-align: center;
  background-color: var(--color-surface);
  border-radius: 12px;
  color: var(--color-primary-soft);
  border: 1px solid var(--color-divider);
}

/* Pagination */
.journeys-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  font-size: 0.9rem;
  border-top: 1px solid var(--color-divider);
  margin-top: 1rem;
  color: var(--color-primary-soft);
}

.pagination-controls {
  display: flex;
  gap: 0.75rem;
}

.pagination-controls button {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: 1px solid var(--color-divider);
  background-color: var(--color-surface);
  color: var(--color-on-background);
  cursor: pointer;
  font-size: 0.85rem;
  transition: background-color 0.2s;
}

.pagination-controls button:hover:not(:disabled) {
  background-color: var(--color-surface-secondary);
}

.pagination-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (min-width: 640px) {
  .journeys-grid {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media (min-width: 768px) {
  .journeys-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .journeys-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .journeys-grid {
    grid-template-columns: repeat(4, 1fr);
  }
} 