/* Campaign Container Layout */
.campaigns-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

/* Campaigns Filter and Search Layout */
.campaigns-filter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: none;
}

.campaigns-search {
  width: 250px;
  position: relative;
}

.campaigns-search input {
  width: 100%;
  padding: 0.5rem 1rem 0.5rem 2.2rem;
  border-radius: var(--border-radius);
  border: 1px solid var(--color-grey);
  background-color: var(--color-surface);
  font-size: 0.9rem;
}

.campaigns-search input:focus {
  border-color: var(--color-emerald-green);
  outline: none;
}

.campaigns-search::before {
  content: "🔍";
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0.6;
  font-size: 0.9rem;
}

/* Campaign Grid Layout - Simplified */
.campaigns-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 4-column layout for desktop */
  gap: 1rem;
  width: 100%;
  padding: 0.25rem 0;
}

/* Campaign Card Styling - Simplified */
.campaign-card {
  display: flex;
  flex-direction: column;
  background-color: var(--color-surface);
  border: 1px solid var(--color-divider);
  border-radius: 8px;
  overflow: hidden;
  transition: box-shadow 0.15s ease-in-out;
  cursor: pointer;
  height: 100%;
}

.campaign-card:hover {
  box-shadow: 0 2px 8px var(--color-shadow-soft);
}

/* Campaign Card Header */
.campaign-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  border-bottom: 1px solid var(--color-divider);
  background-color: var(--color-background);
}

.campaign-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.campaign-card-icon .placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: var(--color-background);
  border-radius: var(--border-radius-inner);
}

/* Campaign Card Content */
.campaign-card-content {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  flex-grow: 1;
  gap: 0.5rem;
}

.campaign-card-title {
  margin-bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.campaign-card-title h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--color-on-background);
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.campaign-card-subtitle {
  font-size: 0.8rem;
  color: var(--color-primary-soft);
  margin-top: 0.25rem;
}

/* Campaign Metrics */
.campaign-card-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin: 0.25rem 0;
}

.metric-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  min-width: 0;
}

.metric-label {
  font-size: 0.75rem;
  color: var(--color-primary-soft);
  text-transform: uppercase;
  letter-spacing: 0.02em;
}

.metric-value {
  font-size: 0.85rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.metric-subvalue {
  font-size: 0.8rem;
  color: var(--color-primary-soft);
}

/* Campaign Footer */
.campaign-card-footer {
  margin-top: auto;
  padding-top: 0.5rem;
  border-top: 1px solid var(--color-divider);
  font-size: 0.75rem;
  color: var(--color-primary-soft);
}

/* Loading and Empty States */
.loading-state,
.empty-state {
  grid-column: 1 / -1;
  padding: 2rem;
  text-align: center;
  background-color: var(--color-background);
  border-radius: var(--border-radius);
  color: var(--color-primary-soft);
}

/* Pagination */
.campaigns-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  font-size: 0.85rem;
  margin-top: 0.5rem;
  border-top: 1px solid var(--color-divider);
}

.pagination-controls {
  display: flex;
  gap: 0.5rem;
}

.pagination-controls button {
  padding: 0.35rem 0.7rem;
  border-radius: var(--border-radius-inner);
  border: 1px solid var(--color-divider);
  background-color: var(--color-background);
  cursor: pointer;
  font-size: 0.8rem;
}

.pagination-controls button:hover:not(:disabled) {
  background-color: var(--color-background-soft);
}

.pagination-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .campaigns-grid {
    grid-template-columns: repeat(3, 1fr); /* 3 columns on large screens */
  }
}

@media (max-width: 1024px) {
  .campaigns-grid {
    grid-template-columns: repeat(2, 1fr); /* 2 columns on medium screens */
  }
}

@media (max-width: 768px) {
  .campaigns-filter {
    flex-direction: column;
    align-items: stretch;
  }

  .campaigns-search {
    width: 100%;
  }

  .campaigns-grid {
    grid-template-columns: 1fr; /* 1 column on mobile */
  }
}

@media (max-width: 480px) {
  .campaigns-pagination {
    flex-direction: column;
    gap: 0.75rem;
  }
}

/* Campaign status tabs */
.ui-tabs {
  position: relative;
  display: flex;
  gap: 0.5rem;
  border-bottom: none;
}

.ui-tabs::before {
  content: '';
  width: 100%;
  bottom: 0;
  height: 1px;
  left: 0;
  position: absolute;
  border-bottom: 1px solid var(--color-divider);
  z-index: 0;
}

.ui-tabs .tab {
  background: none;
  color: var(--color-primary-soft);
  font-size: 0.9rem;
  font-weight: 500;
  padding: 0.75rem 0.5rem;
  border: 0;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  text-decoration: none;
  z-index: 10;
  position: relative;
}

.ui-tabs .tab:hover {
  color: var(--color-primary);
}

.ui-tabs .tab.selected {
  color: var(--color-primary);
  border-bottom: 2px solid var(--color-emerald-green);
}

/* Add Campaign Button */
button.ui-button {
  background-color: var(--color-emerald-green);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

button.ui-button:hover {
  background-color: var(--color-pine-green);
}


/* Campaign Status Tag */
.campaign-card-title .ui-tag {
  padding: 0.25rem 0.5rem;
  font-size: 0.7rem;
  font-weight: 500;
  border-radius: 4px;
  margin-left: 0.5rem;
  white-space: nowrap;
}

.campaign-card-title .ui-tag.plain {
  background-color: var(--color-grey-soft);
  color: var(--color-primary-soft);
}

.campaign-card-title .ui-tag.success {
  background-color: var(--color-green-soft);
  color: var(--color-green-hard);
}

.campaign-card-title .ui-tag.info {
  background-color: var(--color-blue-soft);
  color: var(--color-blue-hard);
}

.campaign-card-title .ui-tag.error {
  background-color: var(--color-red-soft);
  color: var(--color-red-hard);
} 