.chat-page {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  margin-top: -1.0rem;
  overflow: hidden;
}

/* New wrapper to ensure chat displays properly */
.chat-wrapper {
  flex: 1;
  overflow: hidden;
  display: flex;
  min-height: 0; /* Critical for Firefox */
  max-height: 100%;
}

/* Ensure the chat takes up all remaining space */
.chat-page .chat-app {
  flex: 1;
  width: 100%;
  overflow: hidden;
  display: flex; /* Ensure chat app contents display properly */
}

/* Refined compact insights container */
.insights-container {
  flex-shrink: 0; /* Prevent insights from shrinking */
  margin-bottom: 4px; /* Add a small margin for visual separation */
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

/* Make card colors more subtle in light mode */
@media (prefers-color-scheme: light) {
  .insight-card {
    background-color: #fdfdfd;
  }
}
