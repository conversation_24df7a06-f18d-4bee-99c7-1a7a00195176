import React, { useEffect, useState } from "react";
import ModernChat from "./ModernChat_new";
import ChatInsights from "../../components/insights/ChatInsights";
import { InsightData } from "../../components/insights/ChatInsights";
import api from "../../api";
import "./ChatPage.css";

const ChatPage = () => {
  const [insights, setInsights] = useState<InsightData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | undefined>();

  useEffect(() => {
    // Debug log to verify component mounting
    console.log("ChatPage mounted, both insights and chat should be visible");

    // Fetch real insights
    fetchChatInsights();
  }, []);

  const fetchChatInsights = async () => {
    setIsLoading(true);
    setError(undefined);

    try {
      // Get location ID from URL or context (you may need to adjust this based on your routing)
      const locationId = window.location.pathname.split("/")[2] || "1"; // Extract from URL like /admin/locations/1/chat

      const realInsights = await api.getChatInsights(locationId);
      setInsights(realInsights || []);
      console.log("Successfully loaded insights:", realInsights);
    } catch (error: any) {
      console.error("Error fetching insights:", error);

      if (error.response?.status === 401) {
        setError("Authentication required to load insights");
      } else if (error.response?.status === 403) {
        setError("You don't have permission to view insights");
      } else if (error.response?.status) {
        setError(`Failed to load insights (${error.response.status})`);
      } else {
        setError("Network error while loading insights");
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="chat-page">
      <ChatInsights insights={insights} isLoading={isLoading} error={error} />
      <div className="chat-wrapper">
        <ModernChat />
      </div>
    </div>
  );
};

export default ChatPage;
