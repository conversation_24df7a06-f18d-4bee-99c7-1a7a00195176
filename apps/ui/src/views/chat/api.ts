import axios from "axios";
import SmokeyIcon from "../../assets/smokey_icon.png";
import agent<PERSON><PERSON> from "../../api/agents.json";
// Add the Chat interface definition
export interface Chat {
  chat_id: string;
  name: string;
  created_at: string;
  agents?: Array<{
    agent_id: string;
    name: string;
    avatar?: string;
    role?: string;
  }>;
  insights?: Array<{
    id: string;
    title: string;
    description: string;
    created_at: string;
    type: "summary" | "insight" | "action_item";
  }>;
  summary?: string;
}

export const BASE_URL =
  "https://cannabis-marketing-chatbot-224bde0578da.herokuapp.com/api/v1";

export const getChats = async (
  token: string,
  locationId: string
): Promise<Chat[]> => {
  try {
    // Fetch real chats from API if available
    const fetchResponse = await fetch(`${BASE_URL}/user/chats`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!fetchResponse.ok) {
      throw new Error(`Failed to fetch chats: ${fetchResponse.status}`);
    }

    const response = await fetchResponse.json();
    const chats = Array.isArray(response.chats) ? response.chats : [];

    // Skip adding Smokey agent if there are no chats
    if (chats.length === 0) {
      return [];
    }

    // Define Smokey agent that will be added to all chats

    // Add Smokey agent to each chat
    const updatedChats = chats.map((chat: any, index: number) => {
      // Create agents array if it doesn't exist
      const agents = chat.agents || [];
      const agentList = agentJson.agents;
      // Check if Smokey is already in the list
      const smokeyExists = agents.some(
        (agent: any) => agent.agent_id === "smokey"
      );
      const randomAgentKey = String(
        Math.floor(Math.random() * 9) + 1
      ) as keyof typeof agentList;
      const randomAgent = agentList[randomAgentKey];
      // Only add Smokey if not already present
      if (!smokeyExists) {
        agents.push(randomAgent);
        if (index % 3 === 0) {
          agents.push(randomAgent);
        }

        if (index % 2 === 0) {
          const randomAgentKey2 = String(
            Math.floor(Math.random() * 9) + 1
          ) as keyof typeof agentList;
          const randomAgent2 = agentList[randomAgentKey2];
          agents.push(randomAgent2);
          const randomAgentKey3 = String(
            Math.floor(Math.random() * 9) + 1
          ) as keyof typeof agentList;
          const randomAgent3 = agentList[randomAgentKey3];
          agents.push(randomAgent3);
        }
      }

      return {
        ...chat,
        agents,
        insights: chat.insights || [],
        summary: chat.summary || "",
      };
    });

    return updatedChats;
  } catch (error) {
    console.error("Error fetching chats:", error);
    return [];
  }
};

export const getChatMessages = async (chatId: string, token?: string) => {
  const response = await axios.get(
    `${BASE_URL}/chat/messages?chat_id=${chatId}`,
    token ? { headers: { Authorization: `Bearer ${token}` } } : undefined
  );
  return response.data.messages;
};

export const sendMessage = async (
  message: string,
  voiceType: string,
  chatId: string | null,
  token?: string,
  onStreamUpdate?: (data: any) => void
) => {
  // Try WebSocket connection first
  try {
    console.log("Attempting WebSocket connection for chat");
    return await sendMessageViaWebSocket(
      message,
      voiceType,
      chatId,
      token,
      onStreamUpdate
    );
  } catch (error) {
    console.warn("WebSocket connection failed, falling back to HTTP", error);
    // Fallback to HTTP if WebSocket fails
    return sendMessageViaHttp(message, voiceType, chatId, token);
  }
};

/**
 * Send a message via HTTP POST request (fallback method)
 */
const sendMessageViaHttp = async (
  message: string,
  voiceType: string,
  chatId: string | null,
  token?: string
) => {
  const response = await axios.post(
    `${BASE_URL}/chat`,
    { message, voice_type: voiceType, chat_id: chatId },
    token ? { headers: { Authorization: `Bearer ${token}` } } : undefined
  );
  return response.data;
};

/**
 * Convert HTTP URL to WebSocket URL
 */
const getWebSocketBaseUrl = () => {
  return BASE_URL.replace(/^http:/, "ws:").replace(/^https:/, "wss:");
};

/**
 * Send a message via WebSocket connection with reconnection support
 */
const sendMessageViaWebSocket = (
  message: string,
  voiceType: string,
  chatId: string | null,
  token?: string,
  onStreamUpdate?: (data: any) => void
): Promise<any> => {
  return new Promise((resolve, reject) => {
    // Create WebSocket URL with query parameters
    const WS_BASE_URL = getWebSocketBaseUrl();
    const WS_CHAT_ENDPOINT = "/ws/chat";

    // Create query parameters object
    const queryParams = new URLSearchParams();

    // Add token if available
    if (token) {
      queryParams.append("token", token);
      console.log("Using token for WebSocket authentication");
    }

    // Add chat_id if available
    if (chatId) {
      queryParams.append("chat_id", chatId);
      console.log(`Continuing conversation with chat ID: ${chatId}`);
    } else {
      console.log("Starting new conversation (no chat ID provided)");
    }

    // Construct the WebSocket URL with query parameters
    const wsUrl = `${WS_BASE_URL}${WS_CHAT_ENDPOINT}?${queryParams.toString()}`;

    console.log(`Connecting to WebSocket: ${wsUrl}`);

    // Variables to store the complete response
    const responseData: any = {
      type: "ai",
      content: "",
      message_id: Date.now().toString(),
      data: {
        suggested_next_questions: [],
        products: [],
        images: [],
      },
    };

    // Reconnection parameters
    let reconnectAttempt = 0;
    const maxReconnectAttempts = 2;

    // Track whether we've received a chat_id yet
    let chatIdReceived = false;

    // Connection timeout reference
    let connectionTimeout: NodeJS.Timeout | null = null;

    // Function to create and set up the WebSocket
    const setupWebSocket = () => {
      try {
        // Clear any existing timeout
        if (connectionTimeout) {
          clearTimeout(connectionTimeout);
        }

        // Set a new timeout
        connectionTimeout = setTimeout(() => {
          console.error(
            `WebSocket connection timeout (attempt ${reconnectAttempt + 1}/${
              maxReconnectAttempts + 1
            })`
          );

          if (socket && socket.readyState !== WebSocket.CLOSED) {
            socket.close();
          }

          if (reconnectAttempt < maxReconnectAttempts) {
            console.log(
              `Attempting to reconnect (${
                reconnectAttempt + 1
              }/${maxReconnectAttempts})...`
            );
            reconnectAttempt++;
            setupWebSocket();
          } else {
            reject(new Error("WebSocket connection timeout after max retries"));
          }
        }, 5000);

        // Create WebSocket connection
        console.log(`Connecting to: ${wsUrl}`);
        const socket = new WebSocket(wsUrl);

        // Handle socket events
        socket.onopen = () => {
          console.log(
            `WebSocket connection established successfully (attempt ${
              reconnectAttempt + 1
            }/${maxReconnectAttempts + 1})`
          );
          if (connectionTimeout) {
            clearTimeout(connectionTimeout);
            connectionTimeout = null;
          }

          // Send the message as JSON
          const payload = {
            message: message,
            voice_type: voiceType,
          };

          console.log(
            "Sending message via WebSocket:",
            message.substring(0, 50) + (message.length > 50 ? "..." : "")
          );
          socket.send(JSON.stringify(payload));
        };

        socket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);

            console.log(
              "WebSocket raw response:",
              JSON.stringify(data).substring(0, 200) + "..."
            );
            console.log(
              "WebSocket response type:",
              data.type || "unknown type"
            );

            // Special handling for different message formats
            if (data.type === "chat_response") {
              console.log("Received chat_response type message");

              // In this format, the actual message content is nested inside a "message" object
              if (data.message && typeof data.message === "object") {
                console.log("Processing nested message structure");

                // Extract data from the nested message object
                const messageObj = data.message;

                // Log the message structure for debugging
                console.log(
                  "Message object structure:",
                  Object.keys(messageObj).join(", ")
                );

                // Update response data with the correct fields from the nested structure
                responseData.content = messageObj.content || "";
                responseData.chat_id =
                  messageObj.chat_id || responseData.chat_id;
                responseData.message_id =
                  messageObj.message_id || responseData.message_id;

                // Handle nested data structure if it exists
                if (messageObj.data) {
                  console.log(
                    "Message contains data:",
                    Object.keys(messageObj.data).join(", ")
                  );

                  // Update suggested questions and other data properties
                  if (messageObj.data.suggested_next_questions) {
                    responseData.data.suggested_next_questions =
                      messageObj.data.suggested_next_questions;
                  }

                  if (messageObj.data.products) {
                    responseData.data.products = messageObj.data.products;
                  }

                  if (messageObj.data.images) {
                    responseData.data.images = messageObj.data.images;
                  }
                }

                // Ensure content is always a string before resolving
                if (typeof responseData.content !== "string") {
                  console.warn(
                    "Content is not a string, converting to empty string"
                  );
                  responseData.content = "";
                }

                // Call the stream update callback with final response
                if (onStreamUpdate && typeof onStreamUpdate === "function") {
                  onStreamUpdate({ ...responseData });
                }

                // Close the connection and resolve
                if (connectionTimeout) {
                  clearTimeout(connectionTimeout);
                  connectionTimeout = null;
                }
                socket.close();
                resolve(responseData);
                return;
              } else {
                // Legacy format where message might be a string
                responseData.content =
                  typeof data.message === "string" ? data.message : "";
              }
            } else {
              // Handle other message types (status updates, streaming content, etc.)

              // Store chatId if received
              if (data.chat_id && !chatIdReceived) {
                responseData.chat_id = data.chat_id;
                chatIdReceived = true;
              }

              // Check for message_id in response
              if (data.message_id && !responseData.message_id) {
                responseData.message_id = data.message_id;
              }

              // Handle status updates and partial content
              if (data.type === "status") {
                // Status update - contains a message with status info
                if (data.message && typeof data.message === "string") {
                  // Just update the status, don't add to content
                  console.log("Status update:", data.message);
                }
              } else if (
                data.content ||
                (data.message && typeof data.message === "string")
              ) {
                // Streaming content or partial response
                const newContent =
                  data.content ||
                  (typeof data.message === "string" ? data.message : "") ||
                  "";
                if (typeof newContent === "string") {
                  responseData.content += newContent;
                }
              }

              // Add any suggested questions received
              if (data.data?.suggested_next_questions) {
                responseData.data.suggested_next_questions =
                  data.data.suggested_next_questions;
              }

              // Add any products received
              if (data.data?.products) {
                responseData.data.products = data.data.products;
              }

              // Add any images received
              if (data.data?.images) {
                responseData.data.images = data.data.images;
              }

              // Call the stream update callback if provided for incremental updates
              if (onStreamUpdate && typeof onStreamUpdate === "function") {
                // Create a safe copy with string content
                const safeResponseData = {
                  ...responseData,
                  content:
                    typeof responseData.content === "string"
                      ? responseData.content
                      : "",
                };
                onStreamUpdate(safeResponseData);
              }

              // Check for final/complete message types
              if (data.type === "final" || data.type === "complete") {
                // Final message - close and resolve

                // Ensure content is always a string before resolving
                if (typeof responseData.content !== "string") {
                  console.warn(
                    "Content is not a string in final message, converting to empty string"
                  );
                  responseData.content = "";
                }

                if (connectionTimeout) {
                  clearTimeout(connectionTimeout);
                  connectionTimeout = null;
                }
                socket.close();
                resolve(responseData);
              }
            }
          } catch (err) {
            console.error("Error parsing WebSocket message:", err);
          }
        };

        socket.onerror = (error) => {
          console.error(
            `WebSocket error (attempt ${reconnectAttempt + 1}/${
              maxReconnectAttempts + 1
            }):`,
            error
          );

          if (reconnectAttempt < maxReconnectAttempts) {
            console.log(
              `Attempting to reconnect after error (${
                reconnectAttempt + 1
              }/${maxReconnectAttempts})...`
            );
            if (connectionTimeout) {
              clearTimeout(connectionTimeout);
              connectionTimeout = null;
            }
            reconnectAttempt++;
            setTimeout(setupWebSocket, 1000); // Wait 1 second before reconnecting
          } else {
            if (connectionTimeout) {
              clearTimeout(connectionTimeout);
              connectionTimeout = null;
            }
            reject(error);
          }
        };

        socket.onclose = (event) => {
          console.log(
            `WebSocket connection closed (attempt ${reconnectAttempt + 1}/${
              maxReconnectAttempts + 1
            }):`,
            event.code,
            event.reason
          );

          if (connectionTimeout) {
            clearTimeout(connectionTimeout);
            connectionTimeout = null;
          }

          // If we have some data already, resolve with it
          if (responseData.content || responseData.chat_id) {
            // Ensure content is always a string before resolving from onclose handler
            if (typeof responseData.content !== "string") {
              console.warn(
                "Content is not a string in onclose handler, converting to empty string"
              );
              responseData.content = "";
            }

            resolve(responseData);
          } else if (reconnectAttempt < maxReconnectAttempts) {
            console.log(
              `Attempting to reconnect after close (${
                reconnectAttempt + 1
              }/${maxReconnectAttempts})...`
            );
            reconnectAttempt++;
            setTimeout(setupWebSocket, 1000); // Wait 1 second before reconnecting
          } else {
            reject(
              new Error(`WebSocket closed: ${event.code} ${event.reason}`)
            );
          }
        };

        return socket;
      } catch (initError: any) {
        console.error(
          `Error initializing WebSocket (attempt ${reconnectAttempt + 1}/${
            maxReconnectAttempts + 1
          }):`,
          initError
        );

        if (connectionTimeout) {
          clearTimeout(connectionTimeout);
          connectionTimeout = null;
        }

        if (reconnectAttempt < maxReconnectAttempts) {
          console.log(
            `Attempting to reconnect after init error (${
              reconnectAttempt + 1
            }/${maxReconnectAttempts})...`
          );
          reconnectAttempt++;
          setTimeout(setupWebSocket, 1000); // Wait 1 second before reconnecting
        } else {
          reject(initError);
        }
      }
    };

    // Start the initial connection attempt
    setupWebSocket();
  });
};

export const renameChat = async (
  chatId: string,
  newName: string,
  token?: string
) => {
  const response = await axios.put(
    `${BASE_URL}/chat/rename?chat_id=${chatId}&new_name=${newName}`,
    {},
    token ? { headers: { Authorization: `Bearer ${token}` } } : undefined
  );
  return response.data;
};

export const deleteChat = async (chatId: string, token?: string) => {
  const response = await axios.delete(`${BASE_URL}/chat/${chatId}`, {
    headers: token ? { Authorization: `Bearer ${token}` } : undefined,
  });
  return response.data;
};

export const recordFeedback = async (
  token: string,
  message_id: string,
  feedback_type: string
) => {
  const response = await axios.post(
    `${BASE_URL}/feedback`,
    { message_id, feedback_type },
    { headers: { Authorization: `Bearer ${token}` } }
  );
  return response.data;
};

export const retryMessage = async (message_id: string, token?: string) => {
  const response = await axios.post(
    `${BASE_URL}/retry`,
    { message_id },
    token ? { headers: { Authorization: `Bearer ${token}` } } : undefined
  );
  return response.data;
};

export const checkout = async (
  token: string,
  checkoutData: {
    name: string;
    contact_info: { email?: string; phone?: string };
    cart: Record<string, { quantity: number }>;
  }
) => {
  const response = await axios.post(`${BASE_URL}/checkout`, checkoutData, {
    headers: { Authorization: `Bearer ${token}` },
  });
  return response.data;
};
