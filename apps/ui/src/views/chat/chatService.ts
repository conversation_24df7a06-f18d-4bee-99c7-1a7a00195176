import axios from "axios";

// API URL
const API_URL = process.env.REACT_APP_API_URL || "/api";

// Type definitions
export interface Chat {
  chat_id: string;
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
  location_id: string;
  agent_ids: string[];
  status: string;
  metadata: Record<string, any>;
  agents?: Agent[];
}

export interface Agent {
  id: string;
  name: string;
  role: string;
  description: string;
  icon: string;
  capabilities: string[];
  disabled: boolean;
  metadata: Record<string, any>;
}

export interface ChatMessage {
  id?: string;
  message_id: string;
  role: "human" | "ai" | "user" | "assistant";
  content: string;
  timestamp: string;
  chat_id: string;
  agent_id?: string;
  feedback?: "like" | "dislike";
  data?: any;
  metadata?: Record<string, any>;
}

// Get all chats for the current location
export const getChats = async (token: string): Promise<Chat[]> => {
  try {
    const response = await axios.get(`${API_URL}/chats`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching chats:", error);
    throw error;
  }
};

// Get messages for a specific chat
export const getChatMessages = async (
  chatId: string,
  token: string
): Promise<ChatMessage[]> => {
  try {
    const response = await axios.get(`${API_URL}/chats/${chatId}/messages`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching chat messages:", error);
    throw error;
  }
};

// Send a new message
export const sendMessage = async (
  messageData: {
    message: string;
    agent_id: string;
    chat_id: string | null;
  },
  token: string
) => {
  try {
    const response = await axios.post(`${API_URL}/messages`, messageData, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error sending message:", error);
    throw error;
  }
};

// Rename a chat
export const renameChat = async (
  chatId: string,
  newName: string,
  token: string
) => {
  try {
    const response = await axios.put(
      `${API_URL}/chats/${chatId}`,
      { name: newName },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error renaming chat:", error);
    throw error;
  }
};

// Delete a chat
export const deleteChat = async (chatId: string, token: string) => {
  try {
    await axios.delete(`${API_URL}/chats/${chatId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return true;
  } catch (error) {
    console.error("Error deleting chat:", error);
    throw error;
  }
};

// Archive a chat
export const archiveChat = async (chatId: string, token: string) => {
  try {
    await axios.post(
      `${API_URL}/chats/${chatId}/archive`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return true;
  } catch (error) {
    console.error("Error archiving chat:", error);
    throw error;
  }
};

// Record feedback for a message
export const recordFeedback = async (
  messageId: string,
  feedbackType: "like" | "dislike",
  token: string
) => {
  try {
    await axios.post(
      `${API_URL}/feedback`,
      {
        message_id: messageId,
        feedback_type: feedbackType,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );
    return true;
  } catch (error) {
    console.error("Error recording feedback:", error);
    throw error;
  }
};

// Get available agents
export const getAvailableAgents = async (token: string): Promise<Agent[]> => {
  try {
    const response = await axios.get(`${API_URL}/agents`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error("Error fetching available agents:", error);
    throw error;
  }
};

// Update chat metadata
export const updateChatMetadata = async (
  chatId: string,
  metadata: Record<string, any>,
  token: string
) => {
  try {
    await axios.put(`${API_URL}/chats/${chatId}/metadata`, metadata, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });
    return true;
  } catch (error) {
    console.error("Error updating chat metadata:", error);
    throw error;
  }
};
