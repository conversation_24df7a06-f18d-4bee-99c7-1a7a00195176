.chat-page-container {
    /* Agent color variables and UI colors */
  /* Agent colors */
  --color-agent-0: var(--color-brunswick-green, #23504A);
  --color-agent-1: var(--color-pine-green, #00766D);
  --color-agent-2: var(--color-emerald-green, #22AD85);
  --color-agent-3: var(--color-dark-jungle-green, #0D211D);
  --color-agent-4: var(--color-honeydew, #DFF4E9);
  
  /* Chat-specific UI colors - these should override or extend global variables */
  /* Primary and secondary colors are set in variables.css */
  --color-primary-light: var(--color-emerald-green, #22AD85);
  --color-primary-dark: var(--color-dark-jungle-green, #0D211D);
  --color-secondary-light: var(--color-pine-green, #00766D);
  --color-secondary-dark: var(--color-brunswick-green, #23504A);
  
  /* Text colors - using variables.css values */
  --color-text: var(--color-on-background);
  --color-text-secondary: var(--color-primary-soft);
  
  /* App-specific colors */
  --color-sidebar-bg: var(--color-background-soft);
  --color-header-bg: var(--color-background);
  --color-hover: rgba(0, 0, 0, 0.03);
  --color-active: rgba(0, 0, 0, 0.05);
  --color-text-primary: var(--color-primary);
  --color-text-tertiary: var(--color-primary-soft);
  --color-accent: var(--color-emerald-green, #22AD85);
  
  /* Use variables.css colors for consistency */
  --color-grey-light: var(--color-grey);
  --color-hover-light: var(--color-grey-soft);
  
  /* Border radius */
  --border-radius-sm: 6px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  
  /* Shadows and transitions */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --color-primary-blue: var(--color-blue);
  
  /* Chat message colors */
  --color-user-message-bg: #E1F5EE;
  --color-user-message-text: var(--color-dark-jungle-green, #0D211D);
  --color-bot-message-bg: var(--color-brunswick-green, #23504A);
  --color-bot-message-text: #FFFFFF;

/* Chat message container styles */
.chat-message-row {
  display: flex;
  margin: 16px 0;
  position: relative;
  width: 100%;
}

.user-message-row {
  justify-content: flex-end;
}

.bot-message-row {
  justify-content: flex-start;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4px;
  flex-shrink: 0;
}

.user-avatar {
  background-color: var(--color-emerald-green, #22AD85);
  color: white;
  margin-left: 12px;
}

.bot-avatar {
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  margin-right: 12px;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.message-avatar-placeholder {
  font-weight: bold;
  font-size: 14px;
}

/* Chat message styles */
.chat-message {
  padding: 8px 12px;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.chat-message:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.bb-sm-user-message {
  background-color: var(--color-user-message-bg);
  color: var(--color-user-message-text);
  border-top-right-radius: 4px;
}

.bb-sm-bot-message {
  background-color: var(--color-bot-message-bg);
  color: var(--color-bot-message-text);
  border-top-left-radius: 4px;
}

/* Message Header - WhatsApp style */
.message-header {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 2px;
  padding: 0 3px;
}

.message-header.bot-header {
  color: var(--color-brunswick-green, #23504A);
}

.message-header.user-header {
  color: var(--color-emerald-green, #22AD85);
  text-align: right;
}

.message-title {
  font-size: 10px;
  font-weight: normal;
  opacity: 0.8;
}

/* Message Timestamp - WhatsApp style */
.chat-message-timestamp {
  font-size: 11px;
  opacity: 0.7;
  margin-left: 6px;
  align-self: flex-end;
  display: inline-block;
  float: right;
  margin-top: 2px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background-color: var(--color-honeydew, #DFF4E9);
  opacity: 0.7;
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {
  0%, 80%, 100% { 
    transform: scale(0);
  }
  40% { 
    transform: scale(1);
  }
}

/* Add these styles for images within chat messages */
.chat-message img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 8px 0;
  max-width: 500px;
  min-height: 200px;
  background: linear-gradient(110deg, #2c615a 8%, #326b63 18%, #2c615a 33%);
  background-size: 200% 100%;
  animation: shimmer 1.5s linear infinite;
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Once image is loaded, remove placeholder styles */
.chat-message img.loaded {
  min-height: unset;
  background: none;
  animation: none;
  max-width: 500px;
}

.loading-spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid var(--color-grey);
  border-top-color: var(--color-brunswick-green, #23504A);
  border-radius: 50%;
  animation: spinner 0.8s linear infinite;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

.image-container {
  display: inline-block;
  position: relative;
}

.download-button {
  cursor: pointer;
  transition: all 0.2s ease;
}

.download-button:hover {
  transform: scale(1.1);
}

.group:hover .download-button {
  display: flex;
}

.feedback-buttons button {
  opacity: 0.7;
  transition: all 0.2s ease;
  color: var(--color-bot-message-text);
}

.feedback-buttons button:hover {
  opacity: 1;
  background-color: rgba(255, 255, 255, 0.1);
}

.feedback-buttons button.feedback-given {
  opacity: 1;
  color: var(--color-emerald-green, #22AD85);
}

.bb-sm-message-container {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

/* WhatsApp style message headers */
.message-header {
  font-size: 13px;
  font-weight: 600;
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0 4px;
}

.message-header.bot-header {
  color: var(--color-brunswick-green, #23504A);
}

.message-header.user-header {
  color: var(--color-emerald-green, #22AD85);
  justify-content: flex-end;
}

.message-title {
  font-size: 11px;
  font-weight: normal;
  opacity: 0.8;
  font-style: italic;
}

.bb-sm-bot-container {
  align-items: flex-start;
}

.bb-sm-feedback-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 6px;
  width: 100%;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.chat-message-content:hover .bb-sm-feedback-buttons {
  opacity: 1;
}

.bb-sm-left-buttons,
.bb-sm-right-buttons {
  display: flex;
  gap: 8px;
}

.bb-sm-right-buttons {
  margin-left: auto;
}

.bb-sm-feedback-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: var(--color-grey-light);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-text-secondary);
  padding: 0;
  margin: 0;
}

.bb-sm-feedback-button:hover {
  background: var(--color-grey);
  color: var(--primary-color);
  transform: scale(1.1);
}

.bb-sm-feedback-button.bb-sm-feedback-given {
  background: var(--primary-color) !important;
  color: white;
}

.bb-sm-feedback-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

/* Add these CSS rules for shimmer effect */
.shimmer-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  animation: shimmer 1.5s infinite linear;
  overflow: hidden;
}

.shimmer {
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 20%,
    rgba(255, 255, 255, 0.5) 50%,
    rgba(255, 255, 255, 0.2) 80%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Hide scrollbar but allow scrolling */
.no-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Special message styles */
.chat-message.bb-sm-special-message {
  width: 100%;
  max-width: 600px;
}

/* Product item styles */
.product-item {
  transition: transform 0.2s, box-shadow 0.2s;
}

.product-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

/* Loading indicator */
.bb-sm-loading-animation {
  display: flex;
  align-items: center;
  margin: 8px 0;
}

.bb-sm-loading-icon {
  animation: spin 1.5s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.bb-sm-loading-text {
  flex: 1;
}

.bb-sm-loading-dots {
  display: flex;
  gap: 4px;
}

.bb-sm-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--color-text-light);
  opacity: 0.5;
  animation: dot-pulse 1.5s infinite ease-in-out;
}

.bb-sm-dot:nth-child(2) {
  animation-delay: 0.5s;
}

.bb-sm-dot:nth-child(3) {
  animation-delay: 1s;
}

@keyframes dot-pulse {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Feedback buttons */
.bb-sm-feedback-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
  gap: 8px;
}

.bb-sm-feedback-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--color-grey-light);
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.bb-sm-feedback-button:hover {
  background: var(--color-grey);
}

.bb-sm-feedback-given {
  background: var(--color-primary) !important;
  color: white;
}

/* Line clamp utility classes */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Reset prose styles in markdown */
.bb-sm-prose-invert {
  color: inherit;
  line-height: 1.5;
  font-size: 1rem;
}

.bb-sm-prose-invert p {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.bb-sm-prose-invert p:first-child {
  margin-top: 0;
}

.bb-sm-prose-invert p:last-child {
  margin-bottom: 0;
}

.bb-sm-prose-invert a {
  color: var(--color-primary);
  text-decoration: underline;
}

/* SVG Graph Renderer styles */
.svg-graph-container {
  margin: 10px 0;
  padding: 10px;
  border-radius: 8px;
  background-color: var(--color-grey-light);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  overflow: auto;
}

.svg-graph-title {
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--color-text);
  font-size: 1rem;
}

.svg-graph-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 150px;
}

.svg-graph-content svg {
  max-width: 100%;
  height: auto;
}

.svg-graph-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 150px;
  width: 100%;
  background-color: var(--color-grey-light);
  border-radius: 8px;
}

.svg-graph-error {
  padding: 15px;
  background-color: rgba(255, 0, 0, 0.1);
  border-left: 3px solid rgba(255, 0, 0, 0.5);
  color: var(--color-text);
  margin: 10px 0;
  border-radius: 3px;
}

.svg-graph-empty {
  padding: 15px;
  background-color: var(--color-grey-light);
  color: var(--color-text-light);
  margin: 10px 0;
  border-radius: 3px;
  text-align: center;
}

/* SVG Graph Renderer styles - add these to enhance the interactive features */
.svg-graph-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.svg-graph-controls {
  display: flex;
  gap: 5px;
}

.svg-graph-reset-btn {
  background-color: var(--color-grey);
  color: var(--color-text);
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.svg-graph-reset-btn:hover {
  background-color: var(--color-primary);
  color: white;
}

.svg-graph-help-text {
  text-align: center;
  color: var(--color-text-light);
  margin-top: 5px;
  font-size: 0.75rem;
}

/* Make the SVG container larger for better interaction */
.svg-graph-content {
  min-height: 200px;
  border: 1px solid var(--color-grey-light);
  border-radius: 4px;
  padding: 10px;
  transition: all 0.2s;
}

.svg-graph-content:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Add style for interactive elements */
.svg-graph-content svg [data-clickable="true"],
.svg-graph-content svg rect,
.svg-graph-content svg circle,
.svg-graph-content svg path {
  transition: opacity 0.2s, fill 0.2s;
}

/* Add a tooltip style for future use */
.svg-graph-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
  z-index: 100;
  max-width: 200px;
  word-wrap: break-word;
}

/* New styles for 3-column layout */

/* Chat agents display */
.chat-agents {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin: 5px 0;
}

.chat-agent {
  display: flex;
  align-items: center;
  background-color: var(--color-grey-lighter);
  border-radius: 12px;
  padding: 2px 8px 2px 2px;
  font-size: 0.75rem;
}

.agent-avatar {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 5px;
  background-color: var(--color-primary);
  color: white;
  font-size: 0.7rem;
  font-weight: bold;
}

.agent-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.agent-info {
  display: flex;
  flex-direction: column;
}

.agent-name {
  font-weight: 500;
  line-height: 1;
}

.agent-role {
  font-size: 0.65rem;
  color: var(--color-text-light);
  line-height: 1;
}

.chat-agents-placeholder {
  font-size: 0.75rem;
  color: var(--color-text-light);
  margin: 5px 0;
}

.chat-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.chat-date {
  font-size: 0.7rem;
  color: var(--color-primary-soft);
  margin-top: 4px;
}

/* Insights panel styles */
.insights-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.insights-header {
  margin-bottom: 16px;
}

.insights-header h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--color-text);
}

.tabs {
  display: flex;
  gap: 8px;
  border-bottom: 1px solid var(--color-grey-light);
  margin-bottom: 16px;
}

.tab {
  padding: 8px 12px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  color: var(--color-text-light);
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab:hover {
  color: var(--color-text);
}

.tab.active {
  color: var(--color-brunswick-green, #23504A);
  border-bottom: 2px solid var(--color-brunswick-green, #23504A);
}

.insights-content {
  flex: 1;
  overflow-y: auto;
}

.insight-card {
  background-color: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.insight-card h4 {
  font-size: 0.95rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.insight-card p {
  font-size: 0.85rem;
  margin: 0;
}

.no-insights {
  color: var(--color-text-secondary);
  font-style: italic;
  text-align: center;
  padding: 20px;
  background-color: var(--color-background-secondary);
  border-radius: 8px;
  margin: 16px 0;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .insights-panel {
    padding: 12px;
  }
  
  .insight-card {
    padding: 10px;
  }
}

/* Mobile insights panel (slide in from right) */
@media (max-width: 600px) {
  .mobile-insights-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 100%;
    height: 100vh;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  }

  .mobile-insights-panel.active {
    transform: translateX(0);
  }
}

/* Chat message adjustments */
.bb-sm-user-message,
.bb-sm-bot-message {
  max-width: 85%;
}

/* New styles for agent stacking */
.chat-agents-container {
  display: flex;
  align-items: center;
  margin: 5px 0;
  position: relative;
  height: 28px; /* Fixed height to prevent layout shifts */
}

.chat-agents-stacked {
  display: flex;
  align-items: center;
  position: relative;
}

.chat-agent-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid var(--color-background);
  position: relative;
  transition: all 0.2s ease;
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chat-agent-avatar img,
.chat-agent-avatar .avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-primary);
  color: var(--color-text-secondary);
  font-size: 0.7rem;
  font-weight: bold;
}

.chat-agent-tooltip-wrapper:hover .chat-agent-avatar {
  transform: translateY(-3px) scale(1.15);
  z-index: 100 !important;
  
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
}

.chat-agent-more {
  background-color: var(--color-grey);
}

.chat-agent-more .avatar-placeholder {
  background-color: var(--color-grey);
  font-size: 0.65rem;
}

/* Improved agent avatar tooltip styles */
.chat-agent-tooltip-wrapper {
  position: relative;
  display: inline-block;
}

/* Chat agent more styling */
.chat-agent-more {
  background-color: var(--color-grey);
}

.chat-agent-more .avatar-placeholder {
  background-color: var(--color-grey);
  font-size: 0.65rem;
}

/* Agent count styling */
.agent-count {
  font-size: 0.7rem;
  color: var(--color-text-secondary);
  margin-left: 5px;
}

/* Modern chat sidebar styling */
.chat-sidebar {
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background-secondary);
}

.chat-list {
  overflow-y: auto;
  flex: 1;
}

/* Chat item stylings with hover effects */
.chat-item {
  padding: 12px 16px;
  border-radius: 8px;
  margin: 4px 8px;
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s ease;
}

.chat-item:hover {
  background-color: var(--color-hover-light);
  color: var(--color-text);
}

.chat-item.active {
  background-color: var(--color-primary-light);
}

.chat-item-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* Updated chat-name container to include the ellipsis menu */
.chat-name-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-name {
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

/* Updated agents container to include the insights indicator */
.chat-agents-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
}

.chat-date {
  font-size: 12px;
  color: var(--color-primary-soft);
}

/* Chat item controls (ellipsis menu and insight indicator) */
.chat-item-menu-btn {
  background: none;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  padding: 0;
  transition: all 0.2s ease;
  opacity: 0;
}

.chat-item:hover .chat-item-menu-btn {
  opacity: 1;
}

.chat-item-menu-btn:hover {
  background-color: var(--color-hover-light);
  color: var(--color-text);
}

/* Insights indicator styling */
.chat-insight-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--color-primary);
  background-color: rgba(66, 133, 244, 0.08);
  padding: 2px 6px;
  border-radius: 12px;
  transition: background-color 0.2s ease;
}

.chat-insight-indicator:hover {
  background-color: rgba(66, 133, 244, 0.15);
}

.insight-count {
  font-weight: 600;
}

/* Context menu styling */
.context-menu {
  position: fixed;
  background-color: var(--color-background);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid var(--color-divider);
  min-width: 160px;
  z-index: 1100;
  overflow: hidden;
  animation: fadeIn 0.15s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.context-menu-item {
  padding: 10px 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: var(--color-text);
  font-size: 14px;
}

.context-menu-item:hover {
  background-color: var(--color-hover-light);
}

.context-menu-item.delete {
  color: var(--color-error);
}

.context-menu-item.delete:hover {
  background-color: var(--color-error-light);
}

.context-menu-item svg {
  width: 16px;
  height: 16px;
  opacity: 0.8;
}

/* Right collapsible panel */
.insights-sidebar {
  position: relative;
  transition: width 0.3s ease, min-width 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
  box-shadow: -1px 0 3px rgba(0, 0, 0, 0.05);
}

.insights-sidebar.collapsed {
  width: 60px !important;
  min-width: 60px !important;
}

.insights-sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  height: 60px;
}

.insights-sidebar-header-title {
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.insights-sidebar-header-title h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.insights-sidebar-header-datetime {
  font-size: 12px;
  color: var(--color-text-secondary);
}

/* Agents in sidebar */
.insights-sidebar-agents {
  padding: 12px 16px;
}

.insights-agents-container {
  display: flex;
  align-items: center;
}

.insights-agents-stacked {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.panel-collapse-btn {
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}

.panel-collapse-btn:hover {
  background-color: var(--color-hover-light);
  color: var(--color-text);
}

.insights-tabs {
  padding: 8px 0;
}

/* Horizontal tabs for expanded state */
.insights-tabs.horizontal {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  padding: 12px 16px;
  gap: 16px;
}

/* Vertical tabs for collapsed state */
.insights-tabs.vertical {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
}

/* Base tab styling */
.insights-tab {
  position: relative;
  display: flex;
  align-items: center;
  border: none;
  background: none;
  cursor: pointer;
  color: var(--color-text-secondary);
  font-weight: 500;
  text-align: left;
  transition: all 0.2s ease;
}

/* Horizontal tab styling */
.insights-tabs.horizontal .insights-tab {
  padding: 8px 4px;
  gap: 8px;
  border-radius: 0;
  border-bottom: 2px solid transparent;
}

.insights-tabs.horizontal .insights-tab:hover {
  background-color: transparent;
  border-bottom-color: var(--color-grey-light);
}

/* Vertical tab styling */
.insights-tabs.vertical .insights-tab {
  padding: 16px 0;
  justify-content: center;
  align-items: center;
}

.insights-tab:hover {
  background-color: var(--color-hover-light);
  color: var(--color-text);
}

/* Active state for horizontal tabs */
.insights-tabs.horizontal .insights-tab.active {
  color: var(--color-primary);
  background-color: transparent;
  border-bottom: 2px solid var(--color-primary);
  position: relative;
}

/* Active state for vertical tabs */
.insights-tabs.vertical .insights-tab.active {
  color: var(--color-primary);
  border-left: 3px solid var(--color-primary-blue);
  padding-left: 0;
}

/* Icon styling */
.insights-tab-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.insights-tab-icon svg {
  width: 20px;
  height: 20px;
}

.insights-tab.active .insights-tab-icon svg {
  color: var(--color-primary);
}

/* Make icons larger in collapsed state */
.insights-tabs.vertical .insights-tab-icon svg {
  width: 24px;
  height: 24px;
}

.insights-content {
  padding: 20px 16px;
  overflow-y: auto;
  flex: 1;
}

.insight-card {
  background-color: var(--color-background);
  padding: 16px;
  margin-bottom: 12px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.insight-card h4 {
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 8px 0;
}

.insight-card p {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin: 0;
}

/* Chat header styling */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
}

.chat-title {
  display: flex;
  flex-direction: column;
}

.chat-title h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.chat-actions {
  display: flex;
  gap: 8px;
}

/* Message container styling */
.message-input-container {
  display: flex;
  padding: 12px 20px;
  gap: 12px;
  background-color: var(--color-background);
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid var(--color-divider);
  outline: none;
  transition: border-color 0.2s ease;
}

.message-input:focus {
  border-color: var(--color-primary);
}

/* Loading and typing indicators */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 12px;
}

.typing-indicator span {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: white;
  opacity: 0.7;
  border-radius: 50%;
  animation: bounce 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-animation {
  0%, 80%, 100% { 
    transform: scale(0.6);
    opacity: 0.6;
  }
  40% { 
    transform: scale(1);
    opacity: 1;
  }
}

.loading-spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid var(--color-grey);
  border-top-color: var(--color-brunswick-green, #23504A);
  border-radius: 50%;
  animation: spinner 0.8s linear infinite;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

/* Chat message styling */
.chat-message {
  padding: 16px;
  border-radius: 8px;
  max-width: 100%;
}

.bb-sm-user-message {
  background-color: var(--color-user-message-bg);
  align-self: flex-end;
}

.bb-sm-bot-message {
  background-color: var(--color-bot-message-bg);
  align-self: flex-start;
}

.bb-sm-special-message {
  max-width: 100%;
}

/* Chat test buttons */
.chat-test-buttons {
  display: flex;
  gap: 8px;
}

/* Ensure the active tab in the collapsed state has the correct styling */
.insights-sidebar.collapsed .insights-tab.active::before {
  width: 3px;
  left: 0;
  top: 0;
  bottom: 0;
  height: auto;
  right: auto;
}

/* Active tab with blue accent in collapsed state */
.insights-sidebar.collapsed .insights-tab.active {
  border-left: 3px solid var(--color-primary-blue);
  padding-left: 0;
}

/* Make the icons larger in collapsed state */
.insights-sidebar.collapsed .insights-tab-icon svg {
  width: 24px;
  height: 24px;
}

/* Make the page content fill the available height */
.page-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16px;
  overflow: hidden;
}
.new-chat-icon .icon {
    width: 24px;
    height: 24px;
}
.page-content > div {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Chat container styling */
.chat-container {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 80px);
  width: 100%;
}

.chat-body {
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow-y: auto;
  flex: 1;
  height: calc(100vh - 180px);
}

.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Chat layout styling */
.chat-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.chat-column-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

.insights-sidebar-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.insights-tabs {
  padding: 8px 0;
}

/* Agent styling in sidebar */
.insights-agents-stacked {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.chat-agent-tooltip-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.chat-agent-tooltip-wrapper:hover .agent-tooltip {
  display: block;
  opacity: 1;
  visibility: visible;
  animation: tooltipFadeIn 0.2s ease forwards;
}

.chat-agent-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  border: 2px solid var(--color-background);
  overflow: hidden;
  cursor: pointer;
}

.chat-agent-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  text-transform: uppercase;
}

/* Tooltip animation */
@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}

.agent-tooltip {
  display: none;
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: #4285F4;
  color: white;
  border: none;
  border-radius: var(--border-radius-md);
  padding: 12px 16px;
  min-width: 180px;
  z-index: 100;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  margin-left: 8px;
  font-size: 14px;
  pointer-events: none;
  transition: all 0.2s ease;
}

.agent-tooltip:before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #4285F4;
}

.agent-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
  color: white;
}

.agent-role {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.chat-agents-placeholder {
  font-size: 14px;
  color: var(--color-text-secondary);
  padding: 8px 16px;
}

/* Chat message content wrapper for proper alignment */
.chat-message-content {
  display: flex;
  flex-direction: column;
  max-width: 80%;
  align-items: flex-end;
}

.bot-message-row .chat-message-content {
  align-items: flex-start;
}

/* Feedback buttons - positioned on a new line */
.bb-sm-feedback-buttons {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  gap: 8px;
  width: 100%;
}

/* Add styles for disabled sidebar */
.insights-sidebar.disabled {
  position: relative;
  border-left: 1px dashed var(--color-border);
}

.insights-sidebar.disabled::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.1);
  z-index: 10;
  pointer-events: none;
}

.dark .insights-sidebar.disabled::before {
  background-color: rgba(30, 30, 30, 0.1);
}

.insights-sidebar.disabled .insights-tab {
  cursor: not-allowed;
  opacity: 0.6;
}

.insights-sidebar.disabled .insights-content {
  position: relative;
}

.insights-sidebar.disabled .no-insights {
  text-align: center;
  padding: 20px;
  color: var(--color-text-secondary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  max-width: 85%;
  margin: 0 auto;
}

.insights-sidebar.disabled .no-insights p {
  margin-bottom: 16px;
  line-height: 1.5;
}

.insights-sidebar.disabled .no-insights p:first-child {
  font-size: 16px;
  font-weight: 500;
}

.insights-sidebar.disabled .no-insights p.text-sm {
  font-size: 14px;
}

.insights-sidebar.disabled .insights-sidebar-header-title h3 {
  opacity: 0.7;
}

.insights-sidebar.disabled .panel-collapse-btn {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Add styles for insight counter badge */
.insight-counter {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  border-radius: 10px;
  background-color: var(--color-primary);
  color: white;
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
}

.insights-tab-badge {
  position: absolute;
  left: 5px;
  top: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 18px;
  height: 18px;
  padding: 0 5px;
  border-radius: 9px;
  background-color: var(--color-primary);
  color: white;
  font-size: 11px;
  font-weight: 600;
  z-index: 1;
}

/* Text styles for instructions in disabled sidebar */
.text-muted {
  color: var(--color-text-secondary);
  opacity: 0.8;
}

/* Style for the tab badge in the collapsed sidebar */
.insights-sidebar.collapsed .insights-tab-badge {
  left: 4px;
  top: 5px;
}

/* Adjust position of tab icons in collapsed state when badge is present */
.insights-sidebar.collapsed .insights-tab .insights-tab-icon {
  margin-left: 6px;
}

/* Position counter properly when expanded */
.insights-tabs.horizontal .insights-tab .insight-counter {
  margin-left: 8px;
}

/* Welcome Screen */
.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 3rem 1rem 1rem;
  height: 100%;
  width: 100%;
  margin: 0 auto;
  color: var(--color-text);
  position: relative;
}

/* Add the welcome background styling */
.welcome-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(56, 142, 60, 0.08) 0%, rgba(56, 142, 60, 0.03) 50%, transparent 70%);
  z-index: 0;
  pointer-events: none;
}

.smokey-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 3rem;
  max-width: 600px;
  position: relative;
  z-index: 1;
}

.smokey-image img {
  width: 140px;
  height: auto;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.smokey-container h2 {
  font-size: 2rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
  color: var(--color-brunswick-green, #23504A);
  text-shadow: none;
  letter-spacing: -0.02em;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.greeting-subtitle {
  font-size: 1.1rem;
  color: var(--color-text-secondary, #666);
  margin: 0 0 1.5rem;
  max-width: 500px;
  line-height: 1.4;
  font-weight: 400;
  opacity: 0.9;
}

.smokey-container p {
  font-size: 1.2rem;
  max-width: 500px;
  color: var(--color-text-secondary);
}

.welcome-subtitle {
  font-size: 1rem !important;
  font-style: italic;
  margin-top: 0.75rem;
  opacity: 0.8;
  max-width: 650px !important;
  line-height: 1.5;
}

/* Remove old welcome-options styles */
.welcome-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.25rem;
  width: 100%;
  max-width: 1000px;
}

.welcome-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: white;
  padding: 1.25rem;
  border-radius: 0.75rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 110px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--color-grey-light);
}

.welcome-option:hover {
  border-color: var(--color-brunswick-green, #23504A);
  background-color: var(--color-honeydew, #DFF4E9);
}

.welcome-option-icon {
  font-size: 2rem;
  margin-bottom: 0.75rem;
}

.welcome-option-title {
  font-weight: 600;
  font-size: 1rem;
  color: #388e3c;
}

/* Replace with new thought cloud styles */
.thought-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  justify-content: center;
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding: 32px 16px;
}

.thought-bubble {
  background-color: transparent;
  padding: 12px 20px;
  border-radius: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  line-height: 1.4;
  color: var(--color-primary, #4285f4);
  border: 1px solid var(--color-primary, #4285f4);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 52px;
  height: auto;
  box-shadow: none;
  flex: 1;
  min-width: 240px;
  max-width: none;
  width: calc(50% - 8px); /* Set width to approximately half the container minus gap */
  position: relative;
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
  margin-bottom: 8px;
}

.thought-bubble:hover {
  background-color: rgba(66, 133, 244, 0.08);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

/* Remove old positioning and animation styles */
.thought-bubble-1,
.thought-bubble-2,
.thought-bubble-3,
.thought-bubble-4,
.thought-bubble-5 {
  position: static;
  top: auto;
  left: auto;
  right: auto;
  bottom: auto;
  transform: none;
  animation: none;
}

/* Remove speech bubble elements */
.thought-bubble::before,
.thought-bubble::after {
  display: none;
}

/* Dark mode adjustments */
.dark .thought-bubble {
  background-color: transparent;
  color: #5e97f6; /* Lighter blue for dark mode */
  border-color: #5e97f6;
}

.dark .thought-bubble:hover {
  background-color: rgba(94, 151, 246, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .thought-cloud {
    gap: 12px;
    padding: 16px;
  }
  
  .thought-bubble {
    font-size: 0.85rem;
    padding: 10px 16px;
    min-height: 48px;
    width: 100%;
    min-width: auto;
    max-width: 450px;
  }
}

@media (max-width: 480px) {
  .thought-cloud {
    flex-direction: column;
    align-items: center;
  }
  
  .thought-bubble {
    width: 100%;
    max-width: 320px;
  }
}

/* Generated Images Styles */
.generated-images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 12px;
  justify-content: center;
}

.generated-image-item {
  display: flex;
  flex-direction: column;
  max-width: 300px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background-color: var(--color-background);
}

.generated-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 8px 8px 0 0;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.generated-image.loaded {
  opacity: 1;
}

.image-prompt {
  padding: 8px 12px;
  font-size: 0.85rem;
  color: var(--color-text-secondary);
  background-color: var(--color-background);
  border-top: 1px solid var(--color-border);
  text-align: center;
  font-style: italic;
}

/* Suggested Questions Styles */
.suggested-questions {
  margin-top: 16px;
  width: 100%;
}

.suggested-questions-header {
  font-size: 0.8rem;
  color: var(--color-text-light);
  margin-bottom: 8px;
}

.suggested-questions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggested-question-button {
  padding: 6px 12px;
  background-color: var(--color-background-secondary);
  border: 1px solid var(--color-border);
  border-radius: 16px;
  font-size: 0.85rem;
  color: var(--color-text);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.suggested-question-button:hover {
  background-color: var(--color-background-hover);
  border-color: var(--color-border-hover);
}

/* Typing cursor for streaming messages */
.typing-cursor {
  display: inline-block;
  width: 8px;
  height: 16px;
  background-color: var(--color-primary);
  animation: cursor-blink 1s infinite;
  margin-left: 2px;
  vertical-align: middle;
}

@keyframes cursor-blink {
  0%, 49% {
    opacity: 1;
  }
  50%, 100% {
    opacity: 0;
  }
}

/* Send button styles */
.send-button {
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.send-button:hover {
  background-color: var(--color-dark-jungle-green, #0D211D);
}

.send-button:disabled {
  background-color: var(--color-honeydew, #DFF4E9);
  cursor: not-allowed;
  opacity: 0.7;
}

[data-theme="dark"] .send-button:disabled {
  background-color: var(--color-divider, #374151);
}

/* Modern Chat Interface Styles */

/* Core layout */
.chat-app {
  display: flex;
  height: 100vh;
  width: 100%;
  background-color: var(--color-background);
}

/* Sidebar */
.chat-sidebar {
  display: flex;
  flex-direction: column;
  width: 280px;
  height: 100%;
  background-color: var(--color-surface);
  border-right: 1px solid var(--color-border);
  transition: transform 0.3s ease, width 0.3s ease;
  position: relative;
  z-index: 100;
}

.chat-sidebar.closed {
  width: 0;
  transform: translateX(-100%);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
}

.sidebar-header h2 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--color-text);
}

.new-chat-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  color: var(--color-text);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.new-chat-button:hover {
  background-color: var(--color-hover);
}

.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.chat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 4px;
  cursor: pointer;
  position: relative;
  transition: background-color 0.2s ease;
}

.chat-item:hover {
  background-color: var(--color-hover);
}

.chat-item.active {
  background-color: var(--color-active);
}

.chat-item-content {
  flex: 1;
  overflow: hidden;
}

.chat-name {
  font-size: 0.95rem;
  font-weight: 400;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-time {
  font-size: 0.75rem;
  color: var(--color-text-light);
}

.chat-menu-button {
  background: none;
  border: none;
  color: var(--color-text-light);
  cursor: pointer;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.chat-menu-button:hover {
  background-color: var(--color-hover);
}

.sidebar-toggle {
  position: absolute;
  top: 50%;
  right: -12px;
  transform: translateY(-50%);
  width: 24px;
  height: 48px;
  background-color: var(--color-primary);
  border: none;
  border-radius: 0 8px 8px 0;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 101;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

/* Main chat area */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.chat-header-mobile {
  display: none;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
}

.chat-title {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
  flex: 1;
  text-align: center;
}

.mobile-menu-button {
  background: none;
  border: none;
  color: var(--color-text);
  cursor: pointer;
}

/* Chat messages area */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 90%;
  animation: fadeIn 0.3s ease;
}

.user-message {
  align-self: flex-end;
}

.assistant-message {
  align-self: flex-start;
}

.message-container {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.user-message .message-container {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-surface);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-placeholder {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text);
}

.user-avatar {
  background-color: var(--color-primary-light);
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
  min-width: 60px;
  max-width: calc(100% - 50px);
  min-width: 100px;
}
.message-bubble img{
    max-width: 50%;
}

.user-message .message-bubble {
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  border-top-right-radius: 4px;
}

.assistant-message .message-bubble {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-top-left-radius: 4px;
}

.message-content-wrapper {
  word-break: break-word;
  line-height: 1.5;
}

.message-content-wrapper p {
  margin: 0 0 8px;
}

.message-content-wrapper p:last-child {
  margin-bottom: 0;
}

.message-meta {
  display: flex;
  justify-content: flex-end;
  margin-top: 4px;
  font-size: 0.75rem;
}

.message-time {
  color: rgba(255, 255, 255, 0.7);
}

.assistant-message .message-time {
  color: var(--color-text-light);
}

.message-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 4px;
  padding-left: 40px;
}

.action-button {
  background: none;
  border: none;
  color: var(--color-text-light);
  cursor: pointer;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: var(--color-hover);
  color: var(--color-text);
}

.feedback-button.active {
  color: var(--color-primary);
}

.download-button:hover {
  color: var(--color-primary);
}

/* Message input area */
.message-input-area {
  padding: 16px;
  border-top: 1px solid var(--color-border);
}

.input-container {
  display: flex;
  align-items: center;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 24px;
  padding: 0 16px;
  transition: border-color 0.2s ease;
}

.input-container:focus-within {
  border-color: var(--color-primary);
}

.message-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 12px 0;
  font-size: 1rem;
  outline: none;
  color: var(--color-text);
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-button:hover {
  transform: scale(1.05);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 8px;
}

.typing-indicator span {
  display: block;
  width: 8px;
  height: 8px;
  background-color: var(--color-text-light);
  border-radius: 50%;
  animation: typingAnimation 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-bubble {
  min-width: 60px;
  min-height: 20px;
}

/* Loading spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--color-border);
  border-radius: 50%;
  border-top-color: var(--color-primary);
  animation: spin 1s infinite linear;
}

/* Welcome screen */
.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 40px;
  height: 100%;
  padding: 20px;
  text-align: center;
}

.welcome-hero {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 600px;
}

.assistant-avatar {
  height: 100px;
  margin-bottom: 24px;
  object-fit: cover;
}

.welcome-title {
  font-size: 2rem;
  font-weight: 600;
  margin: 0 0 12px;
  color: var(--color-text);
}

.welcome-subtitle {
  font-size: 1.1rem;
  color: var(--color-text-light);
  margin: 0;
}

.suggestion-bubbles {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
  max-width: 800px;
}

.suggestion-bubble {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 20px;
  padding: 12px 20px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-text);
  text-align: left;
}

.suggestion-bubble:hover {
  background-color: var(--color-hover);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.suggestion-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.suggestion-button {
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-text);
}

.suggestion-button:hover {
  background-color: var(--color-hover);
}

/* Message images */
.message-images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-top: 12px;
}

.message-image {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.message-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.message-image img.loaded {
  opacity: 1;
}

.image-prompt {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8px;
  font-size: 0.8rem;
}

/* Context menu */
.context-menu {
  position: fixed;
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  min-width: 180px;
  z-index: 1000;
  overflow: hidden;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  color: var(--color-text);
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.context-menu-item:hover {
  background-color: var(--color-hover);
}

.context-menu-item.delete {
  color: var(--color-error);
}

.context-menu-item.delete:hover {
  background-color: rgba(var(--color-error-rgb), 0.1);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes typingAnimation {
  0%, 80%, 100% { transform: scale(0.6); }
  40% { transform: scale(1); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive styles */
@media (max-width: 768px) {
  .chat-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    bottom: 0;
    z-index: 1000;
    width: 280px;
  }
  
  .chat-header-mobile {
    display: flex;
  }
  
  .message {
    max-width: 95%;
  }
  
  .welcome-title {
    font-size: 1.5rem;
  }
  
  .welcome-subtitle {
    font-size: 1rem;
  }
  .message-bubble{
    max-width: 100%;
  }
  .suggestion-bubbles {
    flex-direction: column;
    align-items: stretch;
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .user-message .message-bubble {
    background-color: var(--color-brunswick-green, #23504A);
  }
  
  .assistant-message .message-bubble {
    background-color: var(--color-surface-dark, #2a2a2a);
    border-color: var(--color-border-dark, #444);
  }
}
}
