/* Modern Chat Component Styles */
/* Uses existing theme variables from variables.css */

/* Core Layout */
.chat-app {
  display: flex;
  height: 100%;
  background-color: var(--color-background);
  color: var(--color-on-background);
  font-family: var(--font-family, 'Inter', sans-serif);
  overflow: hidden;
  position: relative;
}

/* Sidebar Styles */
.chat-sidebar {
  display: flex;
  flex-direction: column;
  width: 280px;
  background-color: var(--color-surface-secondary);
  border-right: 1px solid var(--color-divider);
  transition: transform 0.3s ease, width 0.3s ease;
  position: absolute;
  height: 100%;
  z-index: 20;
  overflow: hidden;
  box-shadow: 2px 0 10px var(--color-shadow-soft);
}

@media (max-width: 768px) {
  .chat-sidebar {
    /* No longer need position and height here since they're in the main styles */
    /* Just add any mobile-specific adjustments */
  }

  .chat-header-mobile {
    display: none;
  }
}

.chat-sidebar.closed {
  width: 0;
  transform: translateX(-100%);
}

.chat-sidebar.open {
  transform: translateX(0);
  width: 280px;
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--color-divider);
}

.sidebar-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
  text-align: center;
}

.sidebar-close-button {
  background: transparent;
  border: none;
  color: var(--color-primary-soft);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: color 0.2s;
  margin-right: 8px;
}

.sidebar-close-button:hover {
  color: var(--color-on-background);
}

.new-chat-button {
  background: var(--color-emerald-green);
  color: white;
  border: none;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.new-chat-button:hover {
  background-color: var(--color-pine-green);
}

.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.chat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chat-item:hover {
  background-color: var(--color-background-soft);
}

.chat-item.active {
  background-color: rgba(34, 173, 133, 0.1); /* emerald-green with opacity */
  color: var(--color-emerald-green);
}

.chat-item-content {
  flex: 1;
  overflow: hidden;
}

.chat-name {
  font-weight: 500;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-time {
  font-size: 12px;
  color: var(--color-primary-soft);
}

.chat-menu-button {
  background: transparent;
  border: none;
  color: var(--color-primary-soft);
  padding: 4px;
  font-size: 14px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.chat-menu-button:hover {
  opacity: 1;
}

/* Main Chat Area */
.chat-main {
  flex: 1;
  margin-left: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  background-color: var(--color-background);
  transition: margin-left 0.3s ease;
}

/* Replace chat-header-mobile with chat-header */
.chat-header {
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--color-divider);
  padding: 12px 16px;
}

.menu-button {
  background: transparent;
  border: none;
  font-size: 16px;
  margin-right: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-on-background);
}

.menu-button:hover {
  color: var(--color-emerald-green);
}

.chat-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Welcome Screen */
.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.welcome-hero {
  margin-bottom: 32px;
}

.assistant-avatar {
  height: 80px;
  margin-bottom: 16px;
  object-fit: cover;
}

.welcome-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 8px;
  color: var(--color-emerald-green);
}

.welcome-subtitle {
  font-size: 16px;
  color: var(--color-primary-soft);
  margin: 0;
  max-width: 500px;
}

.thought-cloud {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  max-width: 100%;
  margin-top: 24px;
}

.thought-bubble {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  min-height: 60px;
  gap: 4px;
}

.thought-bubble:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  border-color: #3b82f6;
}

.thought-bubble.loading {
  opacity: 0.7;
  cursor: default;
  animation: pulse 1.5s ease-in-out infinite;
}

.thought-bubble .bubble-text {
  font-size: 14px;
  line-height: 1.4;
  color: #1f2937;
  font-weight: 500;
}

.thought-bubble .bubble-agent {
  font-size: 11px;
  color: #6b7280;
  font-weight: 400;
  opacity: 0.8;
  margin-top: 2px;
}

/* Message Styles */
.message {
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  max-width: 88%;
}

.user-message {
  align-self: flex-end;
}

.assistant-message {
  align-self: flex-start;
}

.message-container {
  display: flex;
  margin-bottom: 4px;
}

.user-message .message-container {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-emerald-green);
  color: white;
  font-weight: 600;
  border-radius: 50%;
  font-size: 14px;
}

.user-avatar .avatar-placeholder {
  background-color: var(--color-yellow);
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  max-width: calc(100% - 60px);
  overflow-wrap: break-word;
  overflow: hidden;
  min-width: fit-content;
}

.user-message .message-bubble {
  background-color: var(--color-emerald-green);
  color: white;
  border-top-right-radius: 4px;
  
}

.assistant-message .message-bubble {
  background-color: var(--color-surface-secondary);
  color: var(--color-on-background);
  border-top-left-radius: 4px;
}
.message-bubble img{
    max-width: 50%;
}

.message-content-wrapper {
  font-size: 15px;
  line-height: 1.5;
}

.message-content-wrapper p {
  margin: 0 0 12px;
}

.message-content-wrapper p:last-child {
  margin-bottom: 0;
}

.message-meta {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 4px;
  font-size: 11px;
  color: var(--color-primary-soft);
}

.user-message .message-meta {
  color: rgba(255, 255, 255, 0.7);
}

.message-time {
  margin-left: 4px;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 0;
  gap: 4px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background-color: var(--color-primary-soft);
  border-radius: 50%;
  animation: typing-dot 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing-dot {
  0%, 80%, 100% { transform: scale(0.4); opacity: 0.4; }
  40% { transform: scale(1.0); opacity: 1; }
}

.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 16px;
  background-color: var(--color-emerald-green);
  margin-left: 2px;
  animation: blink 1s infinite;
  vertical-align: middle;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 100%;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--color-emerald-green);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Message Input Area */
.message-input-area {
  padding: 16px;
  border-top: 1px solid var(--color-divider);
  background-color: var(--color-background);
}

.input-container {
  display: flex;
  align-items: center;
  background-color: var(--color-surface-secondary);
  border: 1px solid var(--color-divider);
  border-radius: 24px;
  padding: 0 8px 0 16px;
  transition: border-color 0.2s;
}

.input-container:focus-within {
  border-color: var(--color-emerald-green);
}

.message-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 12px 0;
  font-size: 14px;
  color: var(--color-on-background);
  outline: none;
  resize: none;
  min-height: 24px;
}

.message-input::placeholder {
  color: var(--color-primary-soft);
}

.send-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-emerald-green);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-left: 8px;
}

.send-button:hover {
  background-color: var(--color-pine-green);
}

.send-button:disabled {
  background-color: var(--color-grey);
  cursor: not-allowed;
}

/* Context Menu */
.context-menu {
  position: fixed;
  background-color: var(--color-surface);
  border: 1px solid var(--color-divider);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--color-shadow);
  z-index: 100;
  overflow: hidden;
  width: 180px;
  transform-origin: top left;
  animation: popIn 0.1s forwards;
}

@keyframes popIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.context-menu-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 12px;
  background: none;
  border: none;
  text-align: left;
  font-size: 14px;
  color: var(--color-on-background);
  cursor: pointer;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: var(--color-background-soft);
}

.context-menu-item.delete:hover {
  background-color: var(--color-red-soft);
  color: var(--color-red);
}

.context-menu-item svg {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

/* Message actions (feedback buttons) */
.message-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  padding: 0 12px;
}

.action-button {
  background: transparent;
  border: none;
  color: var(--color-primary-soft);
  padding: 4px;
  margin-left: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  color: var(--color-on-background);
  background-color: var(--color-background-soft);
}

.feedback-button.active {
  color: var(--color-emerald-green);
}

.download-button:hover {
  color: var(--color-emerald-green);
}

/* Suggestion buttons */
.suggestion-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 12px;
}

.suggestion-button {
  background-color: var(--color-surface);
  border: 1px solid var(--color-divider);
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 13px;
  color: var(--color-on-background);
  cursor: pointer;
  transition: all 0.2s;
}

.suggestion-button:hover {
  background-color: var(--color-background-soft);
  border-color: var(--color-emerald-green);
}

/* Image handling */
.message-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.message-image {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  max-width: 100%;
}

.message-image img {
  max-width: 100%;
  display: block;
  border-radius: 8px;
}

.image-prompt {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 8px;
  font-size: 12px;
}

/* Rename modal input */
.rename-input {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--color-divider);
  border-radius: 4px;
  margin-bottom: 12px;
  font-size: 14px;
  background-color: var(--color-background);
  color: var(--color-on-background);
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .chat-sidebar {
    /* No longer need position and height here since they're in the main styles */
    /* Just add any mobile-specific adjustments */
  }
  .message-bubble img{
    max-width: 100%;
}

  .thought-cloud {
    flex-direction: column;
    padding: 0 16px;
  }

  .message {
    padding: 8px 0;
  }

  .welcome-title {
    font-size: 24px;
  }

  .welcome-subtitle {
    font-size: 14px;
  }

  .sidebar-header {
    padding: 12px;
  }
  
  .sidebar-header h2 {
    font-size: 16px; /* Slightly smaller title on mobile */
  }
  
  .sidebar-close-button {
    margin-right: 4px; /* Less space on mobile */
  }
}

/* Dark mode adjustments */
[data-theme="dark"] .spinner {
  border-color: rgba(255, 255, 255, 0.1);
  border-top-color: var(--color-emerald-green);
}

[data-theme="dark"] .message-input::placeholder {
  color: var(--color-primary-soft);
}

/* Fix for code blocks and markdown */
.message-content-wrapper pre {
  background-color: var(--color-black);
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  margin: 8px 0;
}

.message-content-wrapper code {
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  color: var(--color-white);
}

.message-content-wrapper a {
  color: var(--color-emerald-green);
  text-decoration: none;
}

.message-content-wrapper a:hover {
  text-decoration: underline;
}

/* Add margin when sidebar is open on larger screens */
@media (min-width: 769px) {
  .chat-main.with-sidebar {
    margin-left: 280px;
  }
}

/* Chat history modal */
.chat-history-modal {
  padding: 8px 0;
  width: 100%;
}

.chat-list-modal {
  margin-top: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.chat-list-modal .chat-item {
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chat-list-modal .chat-item:hover {
  background-color: var(--color-background-soft);
}

/* When sidebar is closed */
.chat-main.with-sidebar {
  margin-left: 280px;
}

@media (max-width: 768px) {
  .chat-main.with-sidebar {
    margin-left: 0;
  }
}

/* Chat header adjustment */
.chat-header {
  padding: 12px 16px;
}

/* Integration with mini-chat styles when in ModernChat */
.chat-app .mini-chat-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Product Table Responsive Styles */
.overflow-x-auto {
  overflow-x: auto;
  scrollbar-width: thin;
  -ms-overflow-style: none;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Mobile and small screen optimizations for product table */
@media (max-width: 639px) {
  .overflow-x-auto table {
    font-size: 0.75rem; /* Smaller font on mobile */
  }
  
  .overflow-x-auto th,
  .overflow-x-auto td {
    padding: 0.5rem;
  }
  
  /* Ensure the description doesn't make rows too tall */
  .overflow-x-auto .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 150px;
  }
}

/* Empty chats state */
.empty-chats-container {
  display: flex;
  height: 100%;
  width: 100%;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.empty-chats-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 200px;
}

.empty-chats-message {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

.empty-chats-subtitle {
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

/* Add new styles for the message bubble container */
.message-bubble-container {
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* Add styles for the message header */
.message-header {
  font-size: 14px;
  margin-bottom: 4px;
  color: var(--color-primary);
  font-weight: 500;
}

.message-header.bot-header {
  color: var(--color-emerald-green);
}

.agent-role {
  font-size: 12px;
  color: var(--color-primary-soft);
  margin-left: 8px;
  font-weight: normal;
}

/* Mini Chat Styles for Simple Layout */
.mini-message-header {
  font-size: 13px;
  margin-bottom: 4px;
  color: var(--color-primary);
  font-weight: 500;
}

.mini-message-header.bot-header {
  color: var(--color-emerald-green);
}

.mini-agent-role {
  font-size: 11px;
  color: var(--color-primary-soft);
  margin-left: 8px;
  font-weight: normal;
}

/* Mini chat thought bubble styles */
.mini-thought-bubble {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  min-height: 50px;
  gap: 3px;
  font-size: 13px;
}

.mini-thought-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.mini-thought-bubble.loading {
  opacity: 0.7;
  cursor: default;
  animation: pulse 1.5s ease-in-out infinite;
}

.mini-thought-bubble .bubble-text {
  font-size: 13px;
  line-height: 1.3;
  color: #1f2937;
  font-weight: 500;
}

.mini-thought-bubble .bubble-agent {
  font-size: 10px;
  color: #6b7280;
  font-weight: 400;
  opacity: 0.8;
  margin-top: 1px;
}

/* Pulse animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 0.5;
  }
}

/* Responsive adjustments for agent information */
@media (max-width: 768px) {
  .thought-bubble .bubble-text {
    font-size: 13px;
  }
  
  .thought-bubble .bubble-agent {
    font-size: 10px;
  }
  
  .mini-thought-bubble .bubble-text {
    font-size: 12px;
  }
  
  .mini-thought-bubble .bubble-agent {
    font-size: 9px;
  }
}

/* Ensure thought clouds handle the new structure */
.mini-thought-cloud {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  max-width: 100%;
  margin-top: 20px;
}

/* Loading state for thought clouds */
.thought-cloud .loading,
.mini-thought-cloud .loading {
  text-align: center;
  grid-column: 1 / -1;
  padding: 20px;
  color: #6b7280;
  font-style: italic;
}

/* Agent selection information */
.agent-selection-info {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: normal;
  margin-left: 8px;
}

.mini-agent-selection-info {
  font-size: 0.7rem;
  color: #6b7280;
  font-weight: normal;
  margin-left: 6px;
}

.mini-agent-role {
  font-size: 0.7rem;
  color: #9ca3af;
  font-weight: normal;
  margin-left: 4px;
}

/* Enhanced message header for better agent visibility */
.message-header.bot-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 4px;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.mini-message-header.bot-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 3px;
  margin-bottom: 3px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #374151;
}
