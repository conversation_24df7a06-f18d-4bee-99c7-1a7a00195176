/* Competitors page styles */
.competitors-container {
  padding: 1rem 0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  background-color: var(--surface-secondary);
  border-radius: 0.5rem;
}

.empty-state .hint {
  margin: 1rem 0;
  color: var(--primary-soft);
  font-size: 0.9rem;
}

.hint {
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.competitors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
}

.competitor-card {
  background-color: var(--surface);
  border: 1px solid var(--divider);
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.competitor-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.competitor-header h3 {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.remove-button {
  color: var(--danger);
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.25rem;
  flex-shrink: 0;
}

.remove-button:hover {
  background-color: var(--danger-soft);
}

.competitor-details {
  color: var(--primary-soft);
  font-size: 0.9rem;
}

.competitor-details .address {
  margin-bottom: 0.25rem;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.competitor-details .distance {
  font-weight: 500;
  color: var(--primary);
}

.website-link {
  display: inline-flex;
  align-items: center;
  margin-top: 8px;
  color: var(--primary);
  text-decoration: none;
  font-size: 13px;
}

.website-link svg {
  margin-right: 4px;
  width: 14px;
  height: 14px;
}

/* Search modal */
.competitor-search-modal {
  width: 100%;
}

.search-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.map-container {
  margin-bottom: 1rem;
  border-radius: 0.5rem;
  overflow: hidden;
}

.search-controls {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.search-results h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1rem;
  font-weight: 500;
  color: #555;
}

.results-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.result-item {
  background-color: white;
  border-radius: 8px;
  border: 1px solid #eee;
  padding: 1rem;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
}

.result-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.result-item p {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  color: #555;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.result-item .distance {
  color: #777;
  font-size: 0.85rem;
}

.result-item:hover {
  border-color: #ccc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.result-item.selected {
  background-color: #e8f4ff;
  border-color: #4a90e2;
}

.result-item.selected:hover {
  background-color: #d8edff;
}

.result-item.already-added {
  opacity: 0.6;
  cursor: not-allowed;
}

.already-added-badge {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background-color: #4caf50;
  color: white;
  font-size: 0.7rem;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
}

/* For text truncation in sync modal */
.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .search-controls {
    flex-direction: column;
    gap: 8px;
  }
  
  .competitors-grid {
    grid-template-columns: 1fr;
  }
  
  .results-list {
    grid-template-columns: 1fr;
  }
}

/* Form styles for manual entry */
.manual-entry-container {
  padding: 1rem 0;
}

.form-row {
  margin-bottom: 1rem;
}

.form-row.three-column {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

/* Search tab styles */
.search-input-container {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: flex-end;
}

.search-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 2rem;
  text-align: center;
}

.search-instructions {
  text-align: center;
  padding: 1rem;
  color: #666;
}

/* Website link in competitor card */
.website-link {
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  color: #4a90e2;
  font-size: 0.85rem;
  margin-top: 0.5rem;
}

.website-link svg {
  margin-right: 0.25rem;
  width: 14px;
  height: 14px;
}

/* Tab styling */
.tab-container {
  display: flex;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--divider);
}

.tab-button {
  flex: 1;
  padding: 0.75rem 1rem;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;
  color: var(--primary-soft);
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button.active {
  color: var(--primary);
  border-bottom-color: var(--primary-accent);
}

/* Mobile responsiveness for the new components */
@media (max-width: 768px) {
  .form-row.three-column {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .search-input-container {
    flex-direction: column;
    align-items: stretch;
  }
}

/* Database result specific styles */
.result-item.database-result {
  border-left: 3px solid #3EDC81;
}

.product-count {
  margin-top: 4px;
  display: flex;
  justify-content: flex-end;
}

/* Market analysis styles */
.market-analysis-container {
  padding: 1rem;
  background-color: var(--surface);
  border: 1px solid #3EDC81;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.market-analysis-header {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.market-analysis-icon {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgba(62, 220, 129, 0.1);
  color: #3EDC81;
  margin-right: 0.75rem;
}

.market-analysis-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.market-analysis-subtitle {
  margin: 0;
  font-size: 0.85rem;
  color: var(--primary-soft);
}

.market-analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.market-analysis-card {
  background-color: var(--surface-secondary);
  border-radius: 0.5rem;
  padding: 1rem;
}

.market-analysis-card-title {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 500;
  color: #3EDC81;
}

.market-analysis-card-content {
  font-size: 0.85rem;
}

.price-comparison {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.25rem;
}

.price-label {
  color: var(--primary-soft);
}

.price-value {
  font-weight: 500;
}

.price-value .change-positive {
  color: var(--danger);
}

.price-value .change-negative {
  color: var(--success);
}

.insights-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.insights-list li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

.insights-list li::before {
  content: "✓";
  color: #3EDC81;
  margin-right: 0.5rem;
}

.market-analysis-footer {
  text-align: center;
  font-size: 0.85rem;
  color: var(--primary-soft);
  font-style: italic;
} 