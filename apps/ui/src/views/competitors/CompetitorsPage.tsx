import React, { useState, useEffect, useContext, useCallback } from "react";
import { useTranslation } from "react-i18next";
import PageContent from "../../ui/PageContent";
import { <PERSON><PERSON>, <PERSON>ert } from "../../ui";
import Spinner from "../../ui/Spinner";
import { LocationContext } from "../../contexts";
import {
  PlusIcon,
  TrashIcon,
  SearchIcon,
  GoogleIcon,
  RefreshIcon,
} from "../../ui/icons";
import api from "../../api";
import "./Competitors.css";
import { Competitor, Location } from "../../types";
import TextInput from "../../ui/form/TextInput";
import {
  searchRetailersSupabase,
  RetailerResult,
  searchNearbyRetailersSupabase,
} from "../../services/firestore";
import { debounce } from "lodash";
import { toast } from "react-hot-toast";
import { GeocodeResult, miscService } from "../../api/misc";
import Modal from "../../ui/Modal";
import CheckboxInput from "../../ui/form/CheckboxInput";
import MarketAnalysisInsights from "../../components/shared/MarketAnalysisInsights";
// Debounced search effect using useRef and useEffect
  import { useRef } from "react";

// CompetitorSearch result interface
interface CompetitorSearchResult {
  place_id: string;
  name: string;
  address: string;
  location: {
    lat: number;
    lng: number;
  };
  distance: number;
  isFromOurDatabase?: boolean;
  productCount?: number;
}

const CompetitorsPage: React.FC = () => {
  const { t } = useTranslation();
  const [location] = useContext(LocationContext);
  const [competitors, setCompetitors] = useState<Competitor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [searchResults, setSearchResults] = useState<CompetitorSearchResult[]>(
    []
  );
  const [selectedCompetitors, setSelectedCompetitors] = useState<Set<string>>(
    new Set()
  );
  const [isAdding, setIsAdding] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("search");
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [isSearching, setIsSearching] = useState(false);
  const [showAIInsights, setShowAIInsights] = useState(false);
  // State for sync functionality
  const [isSyncModalOpen, setIsSyncModalOpen] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const [competitorsToSync, setCompetitorsToSync] = useState<Set<string>>(
    new Set()
  );
  const [selectAllCompetitors, setSelectAllCompetitors] = useState(false);

  // Add pagination state
  const [competitorPage, setCompetitorPage] = useState<number>(1);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [hasMoreCompetitors, setHasMoreCompetitors] = useState<boolean>(true);

  // Add market snapshot state using the new data structure
  const [marketSnapshot, setMarketSnapshot] = useState<any | null>(null);
  const [isMarketAnalysisLoading, setIsMarketAnalysisLoading] = useState(false);
  const [marketAnalysisError, setMarketAnalysisError] = useState<string | null>(
    null
  );

  // Fetch competitors on component mount
  const fetchCompetitors = useCallback(async () => {
    setLoading(true);
    try {
      const response = await api.locations.competitors.get(location.id);
      console.log("API response:", response);

      if (response.data && Array.isArray(response.data)) {
        setCompetitors(response.data);
      } else if (
        response.data &&
        response.data.competitors &&
        Array.isArray(response.data.competitors)
      ) {
        setCompetitors(response.data.competitors);
      } else {
        setCompetitors([]);
      }
      setError("");
    } catch (err) {
      console.error("Error fetching competitors:", err);
      setError(t("competitors.failed_to_load_competitors"));
    } finally {
      setLoading(false);
    }
  }, [location.id, t]);

  useEffect(() => {
    fetchCompetitors();
  }, [fetchCompetitors]);

  // Direct API search function
  const searchCompetitorsByName = useCallback(async () => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      setSelectedCompetitors(new Set()); // Clear selections when clearing search
      return;
    }

    setIsSearching(true);
    setSelectedCompetitors(new Set()); // Clear previous selections when starting new search
    console.log("Searching for:", searchQuery);

    try {
      // Use the retailer search function
      const retailers = await searchRetailersSupabase(searchQuery, true);
      console.log("Search results:", retailers);

      if (retailers && retailers.length > 0) {
        const businessLocation = {
          lat: location.latitude || 0,
          lng: location.longitude || 0,
        };

        // Convert to our format
        const dbCompetitors: CompetitorSearchResult[] = retailers.map(
          (retailer: RetailerResult) => {
            const distance =
              retailer.latitude && retailer.longitude
                ? calculateDistance(
                    businessLocation.lat,
                    businessLocation.lng,
                    retailer.latitude,
                    retailer.longitude
                  )
                : 0;

            return {
              place_id: retailer.id,
              name: retailer.dispensary_name,
              address: formatAddress(retailer),
              location: {
                lat: retailer.latitude || businessLocation.lat,
                lng: retailer.longitude || businessLocation.lng,
              },
              distance: typeof distance === "number" ? distance : 0,
              isFromOurDatabase: true,
              productCount: retailer.productCount || 0,
            };
          }
        );

        console.log("Converted results:", dbCompetitors);
        setSearchResults(dbCompetitors);
      } else {
        console.log("No results found");
        setSearchResults([]);
      }
    } catch (error) {
      console.error("Error searching retailers:", error);
      setError("Failed to search for competitors");
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, location.latitude, location.longitude]);

  // Search for nearby competitors using direct API call
  const searchNearbyCompetitors = useCallback(
    async (page: number = 1) => {
      // Avoid duplicate calls if already loading
      if (isLoadingMore) {
        console.log("Already loading competitors, skipping duplicate request");
        return;
      }

      setIsSearching(true);
      setIsLoadingMore(true);

      // Clear selections when starting a new search (page 1)
      if (page === 1) {
        setSelectedCompetitors(new Set());
      }

      try {
        const businessLocation = {
          lat: location.latitude || 0,
          lng: location.longitude || 0,
        };

        // Call the API to search nearby with pagination
        const nearbyRetailers = await searchNearbyRetailersSupabase(
          businessLocation.lat,
          businessLocation.lng,
          30, // 30km radius
          page, // Current page
          5 // Results per page
        );

        console.log("Nearby retailers:", nearbyRetailers);

        if (nearbyRetailers && nearbyRetailers.length > 0) {
          // Convert to our format
          const dbCompetitors: CompetitorSearchResult[] = nearbyRetailers.map(
            (retailer: RetailerResult) => ({
              place_id: retailer.id,
              name: retailer.dispensary_name,
              address: formatAddress(retailer),
              location: {
                lat: retailer.latitude,
                lng: retailer.longitude,
              },
              distance: retailer.distance || 0,
              isFromOurDatabase: true,
              productCount: retailer.productCount || 0,
            })
          );

          // If not page 1, append results instead of replacing
          if (page > 1) {
            // Filter out any retailers that already exist in the results
            const existingIds = new Set(searchResults.map((r) => r.place_id));
            const newResults = dbCompetitors.filter(
              (c) => !existingIds.has(c.place_id)
            );

            if (newResults.length > 0) {
              setSearchResults([...searchResults, ...newResults]);
              setCompetitorPage(page + 1);
              setHasMoreCompetitors(true);
              toast.success(`Found ${newResults.length} more competitors!`);
            } else {
              setHasMoreCompetitors(false);
              toast.success("No additional competitors found in this area");
            }
          } else {
            setSearchResults(dbCompetitors);
            setCompetitorPage(2); // Next page will be 2
            setHasMoreCompetitors(true);
          }
        } else {
          if (page === 1) {
            setSearchResults([]);
            toast.error("No competitors found within 30km");
          } else {
            toast.success("No more competitors found");
          }
          setHasMoreCompetitors(false);
        }
      } catch (error) {
        console.error("Error finding nearby retailers:", error);
        setError("Failed to find nearby competitors");
        setSearchResults([]);
        setHasMoreCompetitors(false);
      } finally {
        setIsSearching(false);
        setIsLoadingMore(false);
      }
    },
    [isLoadingMore, location.latitude, location.longitude, searchResults]
  );

  // Add a function to load more nearby competitors
  const loadMoreNearbyCompetitors = () => {
    searchNearbyCompetitors(competitorPage);
  };

  // Format address helper
  const formatAddress = (retailer: RetailerResult): string => {
    if (retailer.physical_address) {
      return `${retailer.physical_address}, ${retailer.city}, ${
        retailer.state
      } ${retailer.zip_code || ""}`;
    }
    return `${retailer.city}, ${retailer.state} ${retailer.zip_code || ""}`;
  };

  // Calculate distance (haversine formula)
  const calculateDistance = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ): number => {
    const R = 6371; // Radius of the earth in km
    const dLat = deg2rad(lat2 - lat1);
    const dLon = deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(deg2rad(lat1)) *
        Math.cos(deg2rad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in km
  };

  const deg2rad = (deg: number): number => {
    return deg * (Math.PI / 180);
  };

  // Trigger scrape for selected competitors
  const triggerCompetitorScrape = async () => {
    if (competitorsToSync.size === 0 && !selectAllCompetitors) {
      toast.error("Please select at least one competitor to sync");
      return;
    }

    setIsSyncing(true);

    try {
      // Get retailer_ids either from selected competitors or all competitors
      let retailerIds: string[] = [];

      if (selectAllCompetitors) {
        // Use all competitors with place_id
        retailerIds = competitors
          .filter((comp) => comp.place_id)
          .map((comp) => comp.place_id);
      } else {
        // Use selected competitors
        retailerIds = Array.from(competitorsToSync);
      }

      if (retailerIds.length === 0) {
        toast.error("No valid competitor IDs to sync.");
        setIsSyncing(false);
        return;
      }

      // Call the API to trigger scrape
      const response = await api.locations.competitors.triggerScrape(
        location.id,
        {
          retailer_ids: retailerIds,
        }
      );

      // Handle the response data directly
      const responseData = response.data || response;

      if (responseData && responseData.success) {
        const estimatedTime = responseData.estimatedScrapeTime || 5;

        toast.success(
          <div>
            <p>
              <strong>Sync process initiated!</strong>
            </p>
            <p className="mt-2">
              We're fetching fresh data for {retailerIds.length} competitors.
              This will take approximately {estimatedTime} minutes to complete.
            </p>
            <p className="mt-2">
              The updated competitor data will be available once the scraping
              process is complete.
            </p>
          </div>,
          { duration: 8000 }
        );

        setIsSyncModalOpen(false);
        setCompetitorsToSync(new Set());
        setSelectAllCompetitors(false);
      } else {
        toast.error("Failed to initiate scrape process");
      }
    } catch (error) {
      console.error("Error triggering competitor scrape:", error);
      toast.error("Failed to trigger scrape. Please try again.");
    } finally {
      setIsSyncing(false);
    }
  };


  
  const debouncedSearchRef = useRef<ReturnType<typeof debounce>>();

  useEffect(() => {
    debouncedSearchRef.current = debounce(() => {

      if (searchQuery) {
        searchCompetitorsByName();
      }
    }, 300);
    return () => {
      debouncedSearchRef.current && debouncedSearchRef.current.cancel();
    };
    // Only run when searchCompetitorsByName or searchQuery changes
  }, [searchCompetitorsByName, searchQuery]);

  useEffect(() => {
    if (debouncedSearchRef.current) {
      debouncedSearchRef.current();
    }
  }, [searchQuery]);


  // Toggle competitor selection in search results
  const toggleCompetitorSelection = (competitorId: string) => {
    const newSelection = new Set(selectedCompetitors);

    if (newSelection.has(competitorId)) {
      newSelection.delete(competitorId);
    } else {
      newSelection.add(competitorId);
    }

    setSelectedCompetitors(newSelection);
  };

  // Toggle competitor selection for sync
  const toggleCompetitorForSync = (competitorId: string) => {
    const newSelection = new Set(competitorsToSync);

    if (newSelection.has(competitorId)) {
      newSelection.delete(competitorId);
    } else {
      newSelection.add(competitorId);
    }

    setCompetitorsToSync(newSelection);

    // Update select all status
    if (newSelection.size === 0) {
      setSelectAllCompetitors(false);
    } else if (
      newSelection.size ===
      competitors.filter((c) => c.isFromOurDatabase).length
    ) {
      setSelectAllCompetitors(true);
    }
  };

  // Handle select all competitors
  const handleSelectAllCompetitors = (checked: boolean) => {
    setSelectAllCompetitors(checked);

    if (checked) {
      // Select all competitors with place_id
      const allIds = competitors
        .filter((comp) => comp.place_id)
        .map((comp) => comp.place_id);
      setCompetitorsToSync(new Set(allIds));
    } else {
      // Deselect all
      setCompetitorsToSync(new Set());
    }
  };

  // Add selected competitors
  const addSelectedCompetitors = async () => {
    if (selectedCompetitors.size === 0) return;

    setIsAdding(true);
    const selected = searchResults.filter((c) =>
      selectedCompetitors.has(c.place_id)
    );

    // Check if we have valid competitors to add
    if (selected.length === 0) {
      console.warn("No valid competitors found in current search results for selected IDs");
      setSelectedCompetitors(new Set()); // Clear invalid selections
      setIsAdding(false);
      return;
    }

    try {
      // Convert to the format expected by the API
      const competitorsToAdd = selected.map((competitor) => ({
        place_id: competitor.place_id,
        name: competitor.name,
        address: competitor.address,
        location: {
          lat: competitor.location.lat,
          lng: competitor.location.lng,
        },
        distance: competitor.distance,
        isFromOurDatabase: competitor.isFromOurDatabase || false,
        productCount: competitor.productCount || 0,
      }));

      // Send to API
      await api.locations.competitors.add(
        location.id,
        competitorsToAdd as Competitor[]
      );
      await fetchCompetitors();
      setSelectedCompetitors(new Set());
    } catch (err) {
      console.error("Error adding competitors:", err);
      setError(t("competitors.failed_to_add_competitors"));
    } finally {
      setIsAdding(false);
    }
  };





  // Remove a competitor
  const removeCompetitor = async (placeId: string) => {
    if (!confirm(t("competitors.confirm_remove_competitor"))) return;

    try {
      await api.locations.competitors.remove(location.id, placeId);
      await fetchCompetitors();
    } catch (err) {
      console.error("Error removing competitor:", err);
      setError(t("competitors.failed_to_remove_competitor"));
    }
  };

  // Update handleTabChange to clear search query when changing tabs
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    // Clear search results and selections when changing tabs
    if (tab !== "search") {
      setSearchResults([]);
      setSearchQuery("");
      setSelectedCompetitors(new Set());
    } else {
      // Also clear selections when switching to search tab to start fresh
      setSelectedCompetitors(new Set());
    }
  };

  // Render distance to competitor
  const renderDistance = (distance: number | undefined | null) => {
    if (
      distance === undefined ||
      distance === null ||
      typeof distance !== "number" ||
      isNaN(distance)
    ) {
      return "Unknown distance";
    }
    return `${distance.toFixed(1)} km`;
  };

  // Add function to fetch market analysis using the new API
  const fetchMarketAnalysis = useCallback(async () => {
    if (competitors.length === 0) return;

    setIsMarketAnalysisLoading(true);
    setMarketAnalysisError(null);

    try {
      console.log("Fetching market analysis for competitors page");

      // Convert competitors to the format expected by the new API
      const competitorData = competitors.map((competitor) => ({
        place_id: competitor.place_id,
        name: competitor.name,
        address: competitor.address,
        location: competitor.location,
        distance: competitor.distance,
      }));

      const data = await api.marketAnalysis.getAnalysis(
        competitorData,
        location.retailer_id || location.id?.toString()
      );

      console.log(`Received market snapshot:`, data);
      setMarketSnapshot(data);
    } catch (error) {
      console.error("Error fetching market analysis:", error);
      setMarketAnalysisError("Unable to fetch market analysis");
      setMarketSnapshot(null);
    } finally {
      setIsMarketAnalysisLoading(false);
    }
  }, [competitors, location.retailer_id, location.id]);

  // Fetch market analysis when competitors change or insights are shown
  useEffect(() => {
    if (showAIInsights && competitors.length > 0) {
      fetchMarketAnalysis();
    }
  }, [showAIInsights, competitors.length, fetchMarketAnalysis]);

  return (
    <>
      <PageContent
        title={t("competitors.title")}
        actions={
          <div className="flex space-x-2">
            {competitors.length > 0 && (
              <>
                <Button
                  variant="secondary"
                  onClick={() => setShowAIInsights(!showAIInsights)}
                >
                  {showAIInsights
                    ? "Hide Market Analysis"
                    : "Show Market Analysis"}
                </Button>

                <Button
                  variant="secondary"
                  icon={<RefreshIcon />}
                  onClick={() => setIsSyncModalOpen(true)}
                >
                  Sync with Marketplace
                </Button>
              </>
            )}
          </div>
        }
      >
        {error && <Alert variant="error" title={error} />}

        {/* Market Analysis Section (conditionally shown) */}
        {showAIInsights && competitors.length > 0 && (
          <MarketAnalysisInsights
            competitors={competitors}
            businessName={location.name}
            marketSnapshot={marketSnapshot}
            isLoading={isMarketAnalysisLoading}
            error={marketAnalysisError}
            isOnboarding={false}
            locationId={location.id}
          />
        )}

        {loading ? (
          <div className="loading-container">
            <Spinner size="medium" />
            <p>{t("competitors.loading_competitors")}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Left Column - Search and Add */}
            <div className="bg-surface border border-divider rounded-lg p-4 shadow-sm">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-base font-medium">Find Competitors</h3>
              </div>

              {/* Tab Navigation */}
              <div className="tab-container mb-4">
                <button
                  className={`tab-button ${
                    activeTab === "search" ? "active" : ""
                  }`}
                  onClick={() => handleTabChange("search")}
                >
                  Search
                </button>
                <button
                  className={`tab-button ${
                    activeTab === "nearby" ? "active" : ""
                  }`}
                  onClick={() => handleTabChange("nearby")}
                >
                  Nearby
                </button>
              </div>

              {/* Search Tab */}
              {activeTab === "search" && (
                <div>
                  <div className="search-input-container mb-4">
                    <div className="relative flex-grow">
                      <TextInput
                        name="search"
                        value={searchQuery}
                        onChange={(value) => setSearchQuery(value)}
                        placeholder={t("search")}
                      />
                      {isSearching && (
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                          <Spinner size="small" />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Search Results */}
                  {searchResults.length > 0 && (
                    <div className="mt-4">
                      <div className="text-sm font-medium mb-2">
                        {searchResults.length}{" "}
                        {t("competitors.competitors_found")}
                      </div>
                      <div className="results-list max-h-[400px] overflow-y-auto">
                        {searchResults
                          .sort((a, b) => {
                            // Prioritize database entries with product data
                            if (a.isFromOurDatabase && b.isFromOurDatabase) {
                              const aCount = a.productCount || 0;
                              const bCount = b.productCount || 0;
                              if (aCount !== bCount) return bCount - aCount;
                            }

                            // Then database entries over Google results
                            if (a.isFromOurDatabase && !b.isFromOurDatabase)
                              return -1;
                            if (!a.isFromOurDatabase && b.isFromOurDatabase)
                              return 1;

                            // Default to distance sort
                            return a.distance - b.distance;
                          })
                          .map((result) => {
                            const isAlreadyAdded = competitors.some(
                              (c) => c.place_id === result.place_id
                            );

                            return (
                              <div
                                key={result.place_id}
                                className={`result-item ${
                                  isAlreadyAdded
                                    ? "already-added"
                                    : selectedCompetitors.has(result.place_id)
                                    ? "selected"
                                    : ""
                                } ${
                                  result.isFromOurDatabase
                                    ? "database-result"
                                    : ""
                                }`}
                                onClick={() => {
                                  if (!isAlreadyAdded) {
                                    toggleCompetitorSelection(result.place_id);
                                  }
                                }}
                              >
                                <h4 className="flex items-center truncate">
                                  {result.name}
                                  {result.isFromOurDatabase && (
                                    <span className="ml-2 text-[#3EDC81]">
                                      ★
                                    </span>
                                  )}
                                  {!result.isFromOurDatabase && (
                                    <div className="ml-2">
                                      <span className="ml-2">
                                        <GoogleIcon />
                                      </span>
                                    </div>
                                  )}
                                </h4>
                                <p className="truncate">{result.address}</p>
                                <p className="distance">
                                  {renderDistance(result.distance)}
                                </p>

                                {result.isFromOurDatabase &&
                                  (result.productCount || 0) > 0 && (
                                    <div className="mt-1">
                                      <span className="text-xs text-[#3EDC81] rounded-full bg-[#3EDC81]/10 px-2 py-0.5">
                                        {result.productCount} products
                                      </span>
                                    </div>
                                  )}

                                {result.isFromOurDatabase &&
                                  (!result.productCount ||
                                    result.productCount === 0) && (
                                    <div className="mt-1">
                                      <span className="text-xs text-orange-500 rounded-full bg-orange-100 px-2 py-0.5">
                                        No products
                                      </span>
                                    </div>
                                  )}

                                {isAlreadyAdded && (
                                  <div className="already-added-badge">
                                    {t("competitors.already_added")}
                                  </div>
                                )}
                              </div>
                            );
                          })}
                      </div>

                      {/* Action Button */}
                      <div className="flex justify-end mt-4">
                        <Button
                          variant="primary"
                          onClick={addSelectedCompetitors}
                          disabled={selectedCompetitors.size === 0 || isAdding}
                        >
                          {isAdding ? (
                            <>
                              <Spinner size="small" />
                              <span className="ml-2">
                                {t("competitors.adding")}
                              </span>
                            </>
                          ) : (
                            `Add ${selectedCompetitors.size} selected`
                          )}
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* No Results Message */}
                  {searchQuery &&
                    searchResults.length === 0 &&
                    !isSearching && (
                      <div className="mt-4 p-6 bg-surface-secondary rounded-lg text-center">
                        <p className="text-primary-soft">
                          {t(
                            "competitors.no_results_found",
                            "No results found"
                          )}
                        </p>
                      </div>
                    )}

                  {/* Empty State */}
                  {!searchQuery && searchResults.length === 0 && (
                    <div className="mt-4 p-4 text-center text-primary-soft">
                      <p>
                        {t(
                          "competitors.search_for_competitors_instruction",
                          "Search for competitors by name or location"
                        )}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Nearby Tab */}
              {activeTab === "nearby" && (
                <div>
                  <div className="flex justify-center mb-4">
                    <Button
                      icon={<SearchIcon />}
                      onClick={() => searchNearbyCompetitors(1)}
                      disabled={isSearching}
                      variant="primary"
                    >
                      {isSearching ? (
                        <>
                          <Spinner size="small" />
                          <span className="ml-2">{t("searching")}</span>
                        </>
                      ) : (
                        t(
                          "competitors.search_nearby",
                          "Find Competitors Nearby"
                        )
                      )}
                    </Button>
                  </div>

                  {/* Similar results area as in search tab */}
                  {searchResults.length > 0 && (
                    <div>
                      <div className="text-sm font-medium mb-2">
                        {searchResults.length}{" "}
                        {t("competitors.competitors_found")}
                      </div>

                      <div className="results-list max-h-[400px] overflow-y-auto">
                        {searchResults
                          .sort((a, b) => {
                            // Prioritize database entries
                            if (a.isFromOurDatabase && !b.isFromOurDatabase)
                              return -1;
                            if (!a.isFromOurDatabase && b.isFromOurDatabase)
                              return 1;

                            // Default to distance sort
                            return a.distance - b.distance;
                          })
                          .map((result) => {
                            const isAlreadyAdded = competitors.some(
                              (c) => c.place_id === result.place_id
                            );

                            return (
                              <div
                                key={result.place_id}
                                className={`result-item ${
                                  isAlreadyAdded
                                    ? "already-added"
                                    : selectedCompetitors.has(result.place_id)
                                    ? "selected"
                                    : ""
                                } ${
                                  result.isFromOurDatabase
                                    ? "database-result"
                                    : ""
                                }`}
                                onClick={() => {
                                  if (!isAlreadyAdded) {
                                    toggleCompetitorSelection(result.place_id);
                                  }
                                }}
                              >
                                <h4 className="flex items-center truncate">
                                  {result.name}
                                  {result.isFromOurDatabase && (
                                    <span className="ml-2 text-[#3EDC81]">
                                      ★
                                    </span>
                                  )}
                                  {!result.isFromOurDatabase && (
                                    <div className="ml-2">
                                      <span className="ml-2">
                                        <GoogleIcon />
                                      </span>
                                    </div>
                                  )}
                                </h4>
                                <p className="truncate">{result.address}</p>
                                <p className="distance">
                                  {renderDistance(result.distance)}
                                </p>

                                {result.isFromOurDatabase &&
                                  (result.productCount || 0) > 0 && (
                                    <div className="mt-1">
                                      <span className="text-xs text-[#3EDC81] rounded-full bg-[#3EDC81]/10 px-2 py-0.5">
                                        {result.productCount} products
                                      </span>
                                    </div>
                                  )}

                                {result.isFromOurDatabase &&
                                  (!result.productCount ||
                                    result.productCount === 0) && (
                                    <div className="mt-1">
                                      <span className="text-xs text-orange-500 rounded-full bg-orange-100 px-2 py-0.5">
                                        No products
                                      </span>
                                    </div>
                                  )}

                                {isAlreadyAdded && (
                                  <div className="already-added-badge">
                                    {t("competitors.already_added")}
                                  </div>
                                )}
                              </div>
                            );
                          })}
                      </div>

                      {/* Load more button */}
                      {hasMoreCompetitors && (
                        <div className="flex justify-center mt-4">
                          <Button
                            variant="secondary"
                            onClick={loadMoreNearbyCompetitors}
                            disabled={isLoadingMore}
                          >
                            {isLoadingMore ? (
                              <>
                                <Spinner size="small" />
                                <span className="ml-2">
                                  {t("competitors.loading_more")}
                                </span>
                              </>
                            ) : (
                              t("competitors.load_more", "Load More")
                            )}
                          </Button>
                        </div>
                      )}

                      {/* Action Button */}
                      <div className="flex justify-end mt-4">
                        <Button
                          variant="primary"
                          onClick={addSelectedCompetitors}
                          disabled={selectedCompetitors.size === 0 || isAdding}
                        >
                          {isAdding ? (
                            <>
                              <Spinner size="small" />
                              <span className="ml-2">
                                {t("competitors.adding")}
                              </span>
                            </>
                          ) : (
                            `Add ${selectedCompetitors.size} selected`
                          )}
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Empty State for Nearby */}
                  {searchResults.length === 0 && !isSearching && (
                    <div className="mt-4 p-4 text-center text-primary-soft">
                      <p>
                        {t(
                          "competitors.search_nearby_instructions",
                          "Click the button to find competitors near your location"
                        )}
                      </p>
                    </div>
                  )}
                </div>
              )}


            </div>

            {/* Right Column - Competitor List */}
            <div className="bg-surface border border-divider rounded-lg p-4 shadow-sm">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-base font-medium">Your Competitors</h3>
              </div>

              {competitors.length === 0 ? (
                <div className="empty-state">
                  <p>{t("competitors.no_competitors_yet")}</p>
                  <p className="hint">
                    {t("competitors.add_competitors_hint")}
                  </p>
                </div>
              ) : (
                <div className="competitors-list max-h-[600px] overflow-y-auto">
                  {competitors.map((competitor) => (
                    <div
                      key={competitor.place_id}
                      className="competitor-card mb-3"
                    >
                      <div className="competitor-header">
                        <h3 className="truncate max-w-[200px] overflow-hidden">
                          {competitor.name}
                          {competitor.isFromOurDatabase && (
                            <span className="ml-2 text-[#3EDC81]">★</span>
                          )}
                        </h3>
                        <button
                          className="remove-button"
                          onClick={() => removeCompetitor(competitor.place_id)}
                          title={t("competitors.remove_competitor")}
                        >
                          <TrashIcon />
                        </button>
                      </div>
                      <div className="competitor-details">
                        <p className="address truncate">{competitor.address}</p>
                        <div className="flex justify-between items-center mt-1">
                          {competitor.distance > 0 && (
                            <p className="distance">
                              {renderDistance(competitor.distance)}
                            </p>
                          )}
                          <div className="ml-auto">
                            {(competitor.productCount || 0) > 0 && (
                              <span className="text-sm text-[#3EDC81] rounded-full bg-[#3EDC81]/10 px-2 py-1">
                                {competitor.productCount} products
                              </span>
                            )}
                            {competitor.isFromOurDatabase &&
                              (!competitor.productCount ||
                                competitor.productCount === 0) && (
                                <span className="text-sm text-orange-500 rounded-full bg-orange-100 px-2 py-1">
                                  No products
                                </span>
                              )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}
      </PageContent>

      {/* Sync Modal */}
      <Modal
        title="Sync Competitors with Marketplace"
        open={isSyncModalOpen}
        onClose={() => !isSyncing && setIsSyncModalOpen(false)}
      >
        <div className="modal-content p-2">
          <div className="info-message mb-6 text-gray-600">
            <p>
              This will trigger a fresh data scrape for your selected
              competitors from the marketplace. The process runs in the
              background and may take 5+ minutes to complete.
            </p>
          </div>

          <div className="mb-6">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="selectAll"
                checked={selectAllCompetitors}
                onChange={(e) => handleSelectAllCompetitors(e.target.checked)}
                disabled={isSyncing}
                className="w-5 h-5 mr-3 rounded border-gray-300"
              />
              <label htmlFor="selectAll" className="text-base">
                Select all competitors
              </label>
            </div>
          </div>

          {!selectAllCompetitors && (
            <div className="competitors-selection max-h-[300px] overflow-y-auto border border-gray-200 rounded-md mb-6">
              {competitors.length === 0 ? (
                <p className="text-center p-4 text-gray-500">
                  No competitors available
                </p>
              ) : (
                <div>
                  {competitors.map((comp) => (
                    <div
                      key={comp.place_id}
                      className="p-4 border-b border-gray-100 cursor-pointer flex items-center justify-between"
                      onClick={() => toggleCompetitorForSync(comp.place_id)}
                    >
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          checked={competitorsToSync.has(comp.place_id)}
                          onChange={() => {}}
                          className="w-5 h-5 mr-3"
                        />
                        <span className="text-sm truncate max-w-[150px] block overflow-hidden">
                          {comp.name}
                        </span>
                      </div>
                      <div>
                        {(comp.productCount || 0) > 0 ? (
                          <span className="text-[#3EDC81]">
                            {comp.productCount} products
                          </span>
                        ) : (
                          <span className="text-orange-500">No products</span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {isSyncing && (
            <div className="flex items-center justify-center mb-6">
              <Spinner size="medium" />
              <span className="ml-2">Starting sync process...</span>
            </div>
          )}

          <div className="flex justify-end space-x-2">
            <Button
              variant="secondary"
              onClick={() => setIsSyncModalOpen(false)}
              disabled={isSyncing}
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={triggerCompetitorScrape}
              disabled={
                isSyncing ||
                (competitorsToSync.size === 0 && !selectAllCompetitors)
              }
              icon={<RefreshIcon />}
            >
              {isSyncing ? "Starting..." : "Sync Competitors"}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default CompetitorsPage;
