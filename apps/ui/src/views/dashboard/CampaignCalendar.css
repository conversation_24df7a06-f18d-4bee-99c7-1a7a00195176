/* Campaign Calendar Styles */

/* Import react-big-calendar styles at the component level */
@import 'react-big-calendar/lib/css/react-big-calendar.css';
@import '../../variables.css';

.campaign-calendar-widget {
  margin-bottom: 2rem;
}
.rbc-show-more{
    color: var(--color-brunswick-green, #23504A);
}

.campaigns-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}
.rbc-overlay{
    background-color: var(--color-background);
}

.section-heading {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

.add-campaign-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-campaign-btn:hover {
  background-color: var(--color-dark-jungle-green, #0D211D);
}

/* Loading and Empty States */
.loading-campaigns,
.no-campaigns {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  padding: 3rem 0;
  color: var(--color-text-muted, #6b7280);
  font-size: 0.875rem;
  background-color: var(--color-surface-muted, #f9fafb);
  border-radius: 0.5rem;
  text-align: center;
}

.no-campaign-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--color-text-muted, #6b7280);
  font-size: 0.875rem;
  font-style: italic;
}

/* Campaign Grid Layout */
.campaigns-calendar-grid {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 1.5rem;
}

/* Improved Calendar Column with reduced height */
.calendar-column {
  background-color: var(--color-surface, white);
  border-radius: 0.5rem;
  border: 1px solid var(--color-border, #e5e7eb);
  overflow: hidden;
  min-height: 500px; /* Match the height used in the component */
}

/* View Switcher Styles - Updated for Button components */
.view-switcher {
  display: flex;
  gap: 0.5rem;
}

/* Big Calendar Overrides */
.rbc-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  flex-wrap: wrap;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
}

.rbc-toolbar-label {
  font-size: 1rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.toolbar-controls {
  display: flex;
  gap: 0.5rem;
}

/* Calendar Brand Color Overrides */
.rbc-active {
  background-color: var(--color-pine-green, #00766D) !important;
  color: white !important;
}

.rbc-today {
  background-color: var(--color-surface-secondary) !important;
}

.rbc-event {
  background-color: var(--color-brunswick-green, #23504A) !important;
  border-color: var(--color-dark-jungle-green, #0D211D) !important;
}

.rbc-selected {
  background-color: var(--color-emerald-green, #22AD85) !important;
}

.rbc-day-slot .rbc-event {
  border-left: 5px solid var(--color-emerald-green, #22AD85) !important;
}

.rbc-btn-group button {
  color: var(--color-on-background);
}

.rbc-btn-group button.rbc-active {
  background-color: var(--color-brunswick-green, #23504A) !important;
  color: white !important;
  border-color: var(--color-dark-jungle-green, #0D211D) !important;
}

.rbc-btn-group button:hover {
  background-color: var(--color-honeydew, #DFF4E9) !important;
  color: var(--color-dark-jungle-green, #0D211D) !important;
}

/* Empty states styling */
.empty-campaigns-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 2rem 1rem;
  text-align: center;
  height: 100%;
  color: var(--color-text-muted, #6b7280);
}

.empty-campaigns-state p {
  margin: 0;
  font-size: 0.875rem;
}

.campaign-details-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  height: 100%;
  color: var(--color-text-muted, #6b7280);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .campaigns-calendar-grid {
    grid-template-columns: 1fr;
  }
  
  .delivery-stats {
    grid-template-columns: 1fr;
  }
  
  .rbc-toolbar {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .rbc-toolbar-label {
    margin-bottom: 0.5rem;
  }
  
  .view-switcher {
    width: 100%;
    margin-bottom: 0.5rem;
  }
  
  .toolbar-controls {
    margin-bottom: 0.5rem;
    width: 100%;
    justify-content: space-between;
  }
  
  .add-campaign-btn {
    width: 100%;
    justify-content: center;
  }
  
  .campaign-details-column {
    height: auto;
    min-height: 300px;
  }
}

/* Campaign Details Column */
.campaign-details-column {
  background-color: var(--color-surface, white);
  border-radius: 0.5rem;
  border: 1px solid var(--color-border, #e5e7eb);
  overflow: hidden;
  min-height: 500px; /* Match calendar height for better alignment */
  display: flex;
  flex-direction: column;
}

.campaign-details {
  padding: 1.25rem;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.campaign-header {
  margin-bottom: 1.25rem;
}

.campaign-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
}

.campaign-title.clickable {
  cursor: pointer;
  transition: color 0.2s;
  display: inline-block;
}

.campaign-title.clickable:hover {
  text-decoration: underline;
  color: var(--color-brunswick-green, #23504A);
}

.campaign-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: var(--color-surface-muted, #f3f4f6);
  color: var(--color-text-muted, #6b7280);
}

.status-badge.active {
  background-color: var(--color-emerald-green, #22AD85);
  color: white;
}

.status-badge.scheduled {
  background-color: var(--color-honeydew, #DFF4E9);
  color: var(--color-dark-jungle-green, #0D211D);
}

.status-badge.draft {
  background-color: var(--color-surface-muted, #f3f4f6);
}

.status-badge.completed {
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
}

.channel-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.channel-badge.email {
  background-color: rgba(35, 80, 74, 0.15);
}

.channel-badge.sms {
  background-color: rgba(0, 118, 109, 0.15);
}

.channel-badge.push {
  background-color: rgba(34, 173, 133, 0.15);
}

.channel-badge.automation {
  background-color: rgba(13, 33, 29, 0.15);
}

.campaign-dates {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 1.25rem;
  padding: 1rem;
  background-color: var(--color-surface-muted, #f9fafb);
  border-radius: 0.5rem;
}

.date-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.date-label {
  font-size: 0.75rem;
  color: var(--color-text-muted, #6b7280);
}

.date-value {
  font-weight: 500;
}

.campaign-description {
  margin-bottom: 1.25rem;
}

.campaign-description h4 {
  font-size: 0.875rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.campaign-description p {
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--color-text-secondary, #4b5563);
  margin: 0;
}

.campaign-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.25rem;
}

.tag {
  display: inline-flex;
  padding: 0.25rem 0.5rem;
  background-color: var(--color-honeydew, #DFF4E9);
  color: var(--color-brunswick-green, #23504A);
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.delivery-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  margin-bottom: 1.25rem;
  background-color: var(--color-surface-muted, #f9fafb);
  border-radius: 0.5rem;
  padding: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--color-text-muted, #6b7280);
}

.stat-value {
  font-weight: 500;
  font-size: 0.875rem;
}

.campaign-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.campaign-actions .ui-button {
  flex: 1;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-button.primary {
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  border: none;
}

.action-button.primary:hover {
  background-color: var(--color-dark-jungle-green, #0D211D);
}

.action-button.secondary {
  background-color: transparent;
  color: var(--color-brunswick-green, #23504A);
  border: 1px solid var(--color-brunswick-green, #23504A);
}

.action-button.secondary:hover {
  background-color: var(--color-honeydew, #DFF4E9);
}

/* Create Campaign Button override */
.create-campaign-button {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 5px !important;
  background-color: var(--color-brunswick-green, #23504A) !important;
}

.create-campaign-button:hover {
  background-color: var(--color-dark-jungle-green, #0D211D) !important;
}

.create-campaign-button .button-icon,
.create-campaign-button .button-text {
  display: inline-flex !important;
  align-items: center !important;
} 