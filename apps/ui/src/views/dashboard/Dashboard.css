/* Define custom CSS variables that aren't in the original theme */
:root {
    /* Additional surface colors for light mode */
    --color-surface-emphasis: rgba(249, 250, 251, 0.9);
    --color-surface-brand: linear-gradient(90deg, #f5f7fa, #f8f9fb);
    --color-surface-elevated: #ffffff;
    --color-surface-highlight: rgba(245, 247, 250, 0.6);
    --color-surface-muted: rgba(234, 232, 232, 0.5);
    --color-surface-overlay: rgba(21, 28, 45, 0.5);
    
    /* Text colors */
    /* --color-text: var(--color-primary); */
    --color-text-subtle: var(--color-primary-soft);
    --color-text-muted: #94a3b8;
    
    /* Translucent colors */
    --color-success-translucent: rgba(50, 213, 131, 0.2);
    --color-warning-translucent: rgba(254, 200, 75, 0.2);
    --color-text-muted-translucent: rgba(148, 163, 184, 0.2);
    
    /* Shadow colors */
    --color-primary-shadow: rgba(41, 112, 255, 0.3);
    
    /* Success and warning colors */
    --color-success: var(--color-green);
    --color-success-muted: var(--color-green-soft);
    --color-warning: var(--color-yellow);
  }
  
  /* Dark mode overrides */
  [data-theme="dark"] {
    /* Additional surface colors for dark mode */
    --color-surface-emphasis: rgba(21, 28, 45, 0.9);
    --color-surface-brand: linear-gradient(90deg, #1a1f2b, #1e2334);
    --color-surface-elevated: #1e2538;
    --color-surface-highlight: rgba(30, 37, 56, 0.8);
    --color-surface-muted: rgba(43, 50, 69, 0.5);
    --color-surface-overlay: rgba(15, 23, 42, 0.85);
    
    /* Text muted color for dark mode */
    --color-text-muted: #94a3b8;
  }
  
  .empty-state-icon svg {
    width: 100%;
    height: 100%;
  }
  
  /* Dashboard layout adjustments */
  .dashboard-section {
    margin-bottom: 2.5rem;
  }
  
  /* Ensure equal-height columns if desired */
  .dashboard-section .columns {
    display: flex;
    gap: 1.5rem;
  }
  
  .dashboard-section .columns > .column {
    flex: 1;
  }
  
  /* Chart container adjustments */
  .chart-container {
    position: relative;
    height: 300px;
    width: 100%;
  }
  
  /* Responsive adjustments for small screens */
  @media only screen and (max-width: 768px) {
    .dashboard-section .columns {
      flex-direction: column;
    }
    
    .chart-container {
      height: 250px;
    }
  }
  .page-content {
    padding-right: 0px;
    padding-left: 0px;
  }
  
  /* Top stats (cards) */
  .top-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .stat-card {
    background: var(--color-surface-elevated);
    border-radius: 12px;
    padding: 16px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-divider);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  .stat-title {
    font-size: 14px;
    color: var(--color-text-muted);
  }
  
  .stat-value {
    font-size: 24px;
    font-weight: 600;
    margin-top: 8px;
  }
  
  /* Section headings */
  .section-heading {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--color-primary);
  }
  
  /* Campaigns area */
  .campaigns-section {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
  }
  
  .calendar {
    background: var(--color-surface-elevated);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid var(--color-divider);
    box-shadow: var(--shadow-sm);
  }
  
  .calendar h3 {
    margin-bottom: 1rem;
  }
  
  .calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
    text-align: center;
  }
  
  .calendar-day {
    padding: 8px 0;
    background: var(--color-surface-muted);
    border-radius: 4px;
    color: var(--color-text-subtle);
  }
  
  .selected-day {
    background: var(--color-primary);
    color: #fff;
  }
  
  /* Campaign details card */
  .campaign-details {
    background: var(--color-surface-elevated);
    border-radius: 12px;
    padding: 16px;
    border: 1px solid var(--color-divider);
    box-shadow: var(--shadow-sm);
  }
  
  .campaign-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
  }
  
  .campaign-dates {
    font-size: 14px;
    margin-bottom: 16px;
  }
  
  .campaign-performance {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1rem;
  }
  
  .performance-item {
    display: flex;
    flex-direction: column;
    background: var(--color-surface-muted);
    border-radius: 8px;
    padding: 12px;
    align-items: center;
    justify-content: center;
  }
  
  .performance-value {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
  }
  
  /* Responsive adjustments */
  @media (max-width: 768px) {
    .campaigns-section {
      grid-template-columns: 1fr;
    }
  }
  
  /* Dashboard Styles */
  
  /* Layout */
  .dashboard-layout {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 24px;
    position: relative;
    max-width: 100%;
  }
  
  .dashboard-main {
    overflow-y: auto;
    padding: 0;
    margin: 0;
    grid-column: 1;
  }
  
  /* Make sure modals within dashboard have no margins */
  .dashboard-main .modal-inner {
    margin: 0;
  }
  
  .dashboard-chat-sidebar {
    width: 350px;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    height: calc(100vh - 70px);
    position: sticky;
    top: 70px;
    overflow: hidden;
  }
  
  .dashboard-chat-sidebar > div {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }
  
  .dashboard-chat-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  /* Customer inquiry bar */
  .customer-inquiry-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-bottom: 1px solid var(--color-divider);
  }
  
  .customer-inquiry-input {
    flex: 1;
    padding: 6px 10px;
    border-radius: 4px;
    border: 1px solid var(--color-divider);
    font-size: 0.875rem;
    background-color: var(--color-surface);
    color: var(--color-text);
  }
  
  /* Team members avatars */
  .team-members {
    display: flex;
    margin-left: 8px;
  }
  
  .team-member-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-left: -8px;
    border: 2px solid var(--color-surface-elevated);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .team-member-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  /* Tab navigation */
  .dashboard-tabs {
    display: flex;
    border-bottom: 1px solid var(--color-divider);
  }
  
  .dashboard-tab {
    padding: 10px 16px;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color-text-muted);
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .dashboard-tab:hover {
    color: var(--color-text);
  }
  
  .dashboard-tab.active {
    color: var(--color-primary);
    border-bottom: 2px solid var(--color-primary);
  }
  
  /* Alert panels */
  .alert-panel {
    margin: 12px;
    padding: 12px;
    border-radius: 8px;
    font-size: 0.875rem;
  }
  
  .alert-panel.urgent {
    background-color: rgba(239, 68, 68, 0.1);
    border-left: 3px solid rgb(239, 68, 68);
  }
  
  .alert-panel.info {
    background-color: rgba(59, 130, 246, 0.1);
    border-left: 3px solid rgb(59, 130, 246);
  }
  
  .alert-panel.success {
    background-color: rgba(16, 185, 129, 0.1);
    border-left: 3px solid rgb(16, 185, 129);
  }
  
  .alert-header {
    display: flex;
    align-items: center;
    font-weight: 600;
    margin-bottom: 8px;
  }
  
  .alert-header svg {
    margin-right: 6px;
  }
  
  .alert-description {
    font-size: 0.75rem;
    margin-bottom: 8px;
  }
  
  .alert-suggestion {
    font-size: 0.75rem;
    font-style: italic;
  }
  
  /* Responsive adjustments */
  @media (max-width: 1280px) {
    .dashboard-layout {
      grid-template-columns: 1fr 300px;
    }
  }
  
  @media (max-width: 1024px) {
    .dashboard-layout {
      grid-template-columns: 1fr;
    }
    
    .dashboard-main {
      grid-column: 1;
    }
    
    .dashboard-chat-sidebar {
      display: none;
    }
  }
  
  /* Welcome Dashboard Styles */
  .welcome-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
  }
  
  .welcome-header {
    display: flex;
    align-items: center;
    padding: 20px 20px;
    background: var(--color-surface-brand);
    border-bottom: 1px solid var(--color-divider);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .welcome-title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
    background: linear-gradient(90deg, var(--color-success), var(--color-primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  .welcome-subtitle {
    font-size: 16px;
    color: var(--color-text-subtle);
    margin-bottom: 0;
  }
  
  .mascot-image {
    height: 80px;
    margin-bottom: 0;
    margin-right: 15px;
    animation: float 3s ease-in-out infinite;
  }
  
  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
  }
  
  .welcome-header .text-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
  
  /* Onboarding Steps */
  .onboarding-steps {
    background: var(--color-surface-elevated);
    border-radius: 12px;
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-divider);
    border-bottom: 1px solid var(--color-divider);
  }
  
  .step-counter {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30px;
  }
  
  .step {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    background: var(--color-surface-muted);
    color: var(--color-text-subtle);
  }
  
  .step.active {
    background: var(--color-success);
    color: var(--color-surface);
  }
  
  .step.completed {
    background: var(--color-primary);
    color: var(--color-surface);
  }
  
  .connector {
    height: 2px;
    width: 80px;
    background: var(--color-divider);
    margin: 0 10px;
  }
  
  .steps-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
  
  .step-card {
    padding: 24px;
    border-radius: 8px;
    background: var(--color-surface-muted);
    border: 1px solid var(--color-divider);
    transition: all 0.3s ease;
    height: 100%;
  }
  
  .step-card.active {
    border-left: 4px solid var(--color-success);
  }
  
  .step-card.next {
    border-left: 4px solid var(--color-primary);
  }
  
  .step-card.completed {
    border-left: 4px solid var(--color-success-muted);
    opacity: 0.7;
  }
  
  .step-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--color-primary);
  }
  
  .step-card p {
    color: var(--color-text-subtle);
    margin-bottom: 20px;
  }
  
  .action-button {
    background: linear-gradient(90deg, var(--color-primary), var(--color-success));
    border: none;
    border-radius: 8px;
    padding: 10px 16px;
    font-weight: 600;
    color: var(--color-surface);
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    width: fit-content;
  }
  
  .action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px var(--color-primary-shadow);
  }
  
  .action-button svg {
    margin-right: 8px;
    width: 20px;
    height: 20px;
  }
  
  /* AI Agents Section */
  .agents-section {
    background: var(--color-surface-elevated);
    border-radius: 12px;
    padding: 40px;
    margin-bottom: 40px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-divider);
  }
  
  .agents-section h2 {
    text-align: center;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--color-primary);
  }
  
  .agents-description {
    text-align: center;
    color: var(--color-text-subtle);
    margin-bottom: 30px;
  }
  
  .agents-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
  }
  
  .agent-card {
    background: var(--color-surface-elevated);
    border-radius: 12px;
    padding: 24px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid var(--color-divider);
  }
  
  .agent-card.unlocked {
    border-top: 4px solid var(--color-success);
  }
  
  .agent-card.partial {
    border-top: 4px solid var(--color-warning);
  }
  
  .agent-card.locked {
    border-top: 4px solid var(--color-text-muted);
    opacity: 0.7;
  }
  
  .agent-card:hover {
    transform: translateY(-5px);
    background: var(--color-surface-highlight);
  }
  
  .agent-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }
  
  .agent-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: var(--color-surface-elevated);
    color: var(--color-primary);
  }
  
  .agent-icon img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
  }
  
  .agent-status {
    font-size: 12px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
  }
  
  .status-unlocked {
    background: var(--color-success-translucent);
    color: var(--color-success);
  }
  
  .status-partial {
    background: var(--color-warning-translucent);
    color: var(--color-warning);
  }
  
  .status-locked {
    background: var(--color-text-muted-translucent);
    color: var(--color-text-muted);
  }
  
  .agent-card h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 5px;
    color: var(--color-primary);
  }
  
  .agent-role {
    font-size: 14px;
    color: var(--color-primary);
    margin-bottom: 10px;
  }
  
  .agent-description {
    font-size: 14px;
    color: var(--color-text-subtle);
    margin-bottom: 15px;
    line-height: 1.4;
  }
  
  .agent-requirements {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 15px;
  }
  
  .requirement {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
  }
  
  .requirement.met {
    background: var(--color-success-translucent);
  }
  
  .requirement.unmet {
    background: var(--color-text-muted-translucent);
  }
  
  .requirement-label {
    font-weight: 500;
  }
  
  .agent-coming-soon {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--color-surface-overlay);
    border-radius: 12px;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .agent-coming-soon span {
    background: var(--color-surface-muted);
    color: var(--color-text);
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 4px;
    transform: rotate(-15deg);
    letter-spacing: 1px;
    border: 1px solid var(--color-divider);
  }
  
  .agents-cta {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
  
  /* Features Section */
  .dashboard-features {
    background: var(--color-surface-elevated);
    border-radius: 12px;
    padding: 40px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-divider);
  }
  
  .dashboard-features h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 30px;
    color: var(--color-primary);
    text-align: center;
  }
  
  .features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }
  
  .feature-card {
    background: var(--color-surface-elevated);
    border-radius: 12px;
    padding: 24px;
    border: 1px solid var(--color-divider);
    transition: all 0.3s ease;
    text-align: center;
  }
  
  .feature-card:hover {
    transform: translateY(-5px);
    background: var(--color-surface-highlight);
  }
  
  .feature-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-primary);
  }
  
  .feature-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--color-primary);
  }
  
  .feature-card p {
    font-size: 14px;
    color: var(--color-text-subtle);
  }
  
  /* Responsive adjustments for welcome dashboard */
  @media (max-width: 1024px) {
    .features-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .steps-content {
      grid-template-columns: 1fr;
    }
    
    .step-counter {
      display: none;
    }
    
    .agents-grid,
    .features-grid {
      grid-template-columns: 1fr;
    }
    
    .welcome-header {
      flex-direction: column;
      padding: 15px;
    }
    
    .mascot-image {
      margin-right: 0;
      margin-bottom: 10px;
    }
    
    .welcome-header .text-content {
      align-items: center;
      text-align: center;
    }
  }
  
  /* Responsive adjustments */
  @media (max-width: 1280px) {
    .agents-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .agents-grid {
      grid-template-columns: 1fr;
    }
    
    .agent-card {
      padding: 20px;
    }
    
    .agents-section {
      padding: 20px;
    }
  }
  
  /* Tab panels */
  .tab-panel {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .tab-panel-content {
    flex-grow: 1;
    overflow: auto;
  }
  
  /* Insights tab styles */
  .insights-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .insights-list {
    flex-grow: 1;
    overflow-y: auto;
    padding: 0.5rem;
  }
  
  .insights-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 1rem;
  }
  
  .insights-empty-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
  }
  
  .insights-empty-icon img {
    width: 100%;
    height: 100%;
  }
  
  /* Mini chat fixes */
  .dashboard-chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }
  
  .mini-chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }
  
  .mini-chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 1rem;
  }
  
  /* Small spinner for buttons */
  .spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    display: inline-block;
    vertical-align: middle;
    margin-right: 0.25rem;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  /* Chat header and tabs */
  .chat-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .customer-inquiry-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #212b3c;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  [data-theme="dark"] .customer-inquiry-bar {
    background-color: #212b3c;
  }
  
  .customer-inquiry-input {
    flex: 1;
    padding: 10px 16px;
    border-radius: 100px;
    border: none;
    font-size: 14px;
    background-color: white;
    color: #555;
    height: 40px;
  }
  
  [data-theme="dark"] .customer-inquiry-input {
    background-color: white;
    color: #555;
  }
  
  /* Team members avatars */
  .team-members {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }
  
  .team-member-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-left: -8px;
    border: 2px solid #212b3c;
    position: relative;
  }
  
  .team-member-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .team-more-members {
    background-color: #e9eaec;
    color: #555;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  
  /* Tabs */
  .dashboard-tabs {
    display: flex;
    background-color: #deeeea;
  }
  
  [data-theme="dark"] .dashboard-tabs {
    background-color: #212b3c;
  }
  
  .dashboard-tab {
    padding: 16px 24px;
    font-size: 14px;
    font-weight: 500;
    color: var(--color-primary);
    cursor: pointer;
    transition: all 0.15s ease;
    position: relative;
  }
  
  .dashboard-tab:hover {
    color: rgba(255, 255, 255, 0.9);
  }
  
  .dashboard-tab.active {
    color: var(--color-primary);
  }
  
  .dashboard-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background-color: #2ac963;
  }
  
  /* Chat selection dropdown */
  .chat-selection {
    padding: 12px 16px;
    border-bottom: 1px solid var(--color-divider, #e5e7eb);
  }
  
  [data-theme="dark"] .chat-selection {
    border-bottom-color: var(--color-divider, #374151);
  }
  
  .chat-dropdown {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    color: var(--color-text, #111827);
  }
  
  [data-theme="dark"] .chat-dropdown {
    color: var(--color-text, #e5e7eb);
  }
  
  .dropdown-icon {
    width: 16px;
    height: 16px;
    color: var(--color-text-muted, #6b7280);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .dropdown-menu {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    width: 100%;
    background-color: white;
    border: 1px solid var(--color-divider, #e5e7eb);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }
  
  [data-theme="dark"] .dropdown-menu {
    background-color: var(--color-surface-elevated, #1e2538);
    border-color: var(--color-divider, #374151);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  }
  
  .dropdown-item {
    padding: 10px 16px;
    cursor: pointer;
    transition: background-color 0.15s ease;
  }
  
  .dropdown-item:hover {
    background-color: var(--color-surface-highlight, rgba(245, 247, 250, 0.6));
  }
  
  [data-theme="dark"] .dropdown-item:hover {
    background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));
  }
  
  /* Enhanced Agents Section Styling */
  .agents-section.enhanced {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid rgba(59, 130, 246, 0.1);
  }
  
  .agents-header {
    margin-bottom: 2rem;
  }
  
  .agents-title-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .agents-title-section h2 {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #3b82f6, #9333ea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
  }
  
  .agents-stats {
    display: flex;
    gap: 1.5rem;
    align-items: center;
  }
  
  .agent-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 70px;
  }
  
  [data-theme="dark"] .agent-stat {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.2);
  }
  
  .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-primary);
    line-height: 1;
  }
  
  .stat-label {
    font-size: 0.75rem;
    color: var(--color-text-muted);
    margin-top: 0.25rem;
    font-weight: 500;
  }
  
  .agents-description.enhanced {
    font-size: 1.1rem;
    color: var(--color-text-subtle);
    line-height: 1.6;
    margin: 0;
  }
  
  /* Progress Overview Cards */
  .agents-progress-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .progress-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  [data-theme="dark"] .progress-card {
    background: rgba(30, 41, 59, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.2);
  }
  
  .progress-icon {
    font-size: 2rem;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #3b82f6, #9333ea);
    border-radius: 12px;
    flex-shrink: 0;
  }
  
  .progress-content {
    flex: 1;
  }
  
  .progress-content h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text);
  }
  
  .progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(148, 163, 184, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
  }
  
  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #3b82f6);
    border-radius: 4px;
    transition: width 0.8s ease-in-out;
  }
  
  .progress-fill.agent-progress {
    background: linear-gradient(90deg, #8b5cf6, #ec4899);
  }
  
  .progress-text {
    font-size: 0.875rem;
    color: var(--color-text-muted);
    font-weight: 500;
  }
  
  /* Enhanced Agent Cards */
  .agents-grid.enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;
  }
  
  /* New Agent Card Styles */
  .agent-card-new {
    background-color: var(--color-surface-elevated);
    border: 1px solid var(--color-divider);
    border-radius: 16px;
    padding: 24px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  
  .agent-card-new:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  .agent-card-new.unlocked {
    border-left: 5px solid var(--color-success);
  }
  
  .agent-card-new.partial {
    border-left: 5px solid var(--color-warning);
  }
  
  .agent-card-new.locked {
    border-left: 5px solid var(--color-danger);
  }
  
  .agent-main-info {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .agent-header-new {
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .agent-icon-new {
    width: 60px;
    height: 60px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--color-surface-muted);
  }
  
  .agent-icon-new img,
  .agent-icon-new svg {
    width: 36px;
    height: 36px;
  }
  
  .agent-title-group {
    display: flex;
    flex-direction: column;
  }
  
  .agent-name-new {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--color-text-emphasis);
    margin: 0;
  }
  
  .agent-role-new {
    font-size: 0.875rem;
    color: var(--color-text-muted);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .agent-status-new {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .status-badge-new {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 6px;
  }
  
  .status-badge-new::before {
    content: "";
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
  
  .status-badge-new.unlocked {
    background-color: var(--color-success-translucent);
    color: var(--color-success);
  }
  .status-badge-new.unlocked::before {
    background-color: var(--color-success);
  }
  
  .status-badge-new.partial {
    background-color: var(--color-warning-translucent);
    color: var(--color-warning);
  }
  .status-badge-new.partial::before {
    background-color: var(--color-warning);
  }
  
  .status-badge-new.locked {
    background-color: var(--color-text-muted-translucent);
    color: var(--color-text-muted);
  }
  .status-badge-new.locked::before {
    background-color: var(--color-text-muted);
  }
  
  .progress-circle-new {
    position: relative;
    width: 40px;
    height: 40px;
  }
  
  .circular-chart-new {
    display: block;
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
  }
  
  .circle-bg-new {
    fill: none;
    stroke: var(--color-surface-muted);
    stroke-width: 3;
  }
  
  .circle-new {
    fill: none;
    stroke-width: 3;
    stroke-linecap: round;
    transition: stroke-dasharray 0.3s ease;
  }
  
  .circle-new.unlocked {
    stroke: var(--color-success);
  }
  .circle-new.partial {
    stroke: var(--color-warning);
  }
  .circle-new.locked {
    stroke: var(--color-text-muted);
  }
  
  .progress-text-new {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.75rem;
    font-weight: 700;
    color: var(--color-text-emphasis);
  }
  
  .agent-description-new {
    font-size: 0.9rem;
    color: var(--color-text-subtle);
    line-height: 1.5;
  }
  
  .agent-requirements-new {
    background-color: var(--color-surface-muted);
    border-radius: 12px;
    padding: 20px;
  }
  
  [data-theme="dark"] .agent-requirements-new {
    background-color: var(--color-surface-highlight);
  }
  
  .requirements-title-new {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 16px 0;
    color: var(--color-text-emphasis);
  }
  
  .requirements-list-new {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .requirement-new {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
  }
  
  .requirement-icon-new {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
  }
  
  .requirement-new.met .requirement-icon-new {
    background-color: var(--color-success-translucent);
    color: var(--color-success);
  }
  
  .requirement-new.unmet .requirement-icon-new {
    background-color: rgba(255, 87, 87, 0.2);
    color: #ff5757;
  }
  
  .requirement-label-new {
    color: var(--color-text-subtle);
  }
  
  .requirement-new.met .requirement-label-new {
    color: var(--color-text-emphasis);
  }
  
  @media (max-width: 768px) {
    .agent-card-new {
      grid-template-columns: 1fr;
    }
  }
  
  .agent-card {
    display: none;
  }
  
  /* Features Section */
  .dashboard-features {
    background: var(--color-surface-elevated);
    border-radius: 12px;
    padding: 40px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--color-divider);
  }
  
  .dashboard-features h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 30px;
    color: var(--color-primary);
    text-align: center;
  }
  
  .features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }
  
  .feature-card {
    background: var(--color-surface-elevated);
    border-radius: 12px;
    padding: 24px;
    border: 1px solid var(--color-divider);
    transition: all 0.3s ease;
    text-align: center;
  }
  
  .feature-card:hover {
    transform: translateY(-5px);
    background: var(--color-surface-highlight);
  }
  
  .feature-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-primary);
  }
  
  .feature-card h3 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--color-primary);
  }
  
  .feature-card p {
    font-size: 14px;
    color: var(--color-text-subtle);
  }
  
  /* Responsive adjustments for welcome dashboard */
  @media (max-width: 1024px) {
    .features-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .steps-content {
      grid-template-columns: 1fr;
    }
    
    .step-counter {
      display: none;
    }
    
    .agents-grid,
    .features-grid {
      grid-template-columns: 1fr;
    }
    
    .welcome-header {
      flex-direction: column;
      padding: 15px;
    }
    
    .mascot-image {
      margin-right: 0;
      margin-bottom: 10px;
    }
    
    .welcome-header .text-content {
      align-items: center;
      text-align: center;
    }
  }
  
  /* Responsive adjustments */
  @media (max-width: 1280px) {
    .agents-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .agents-grid {
      grid-template-columns: 1fr;
    }
    
    .agent-card {
      padding: 20px;
    }
    
    .agents-section {
      padding: 20px;
    }
  }
  
  /* Tab panels */
  .tab-panel {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  .tab-panel-content {
    flex-grow: 1;
    overflow: auto;
  }
  
  /* Insights tab styles */
  .insights-container {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  .insights-list {
    flex-grow: 1;
    overflow-y: auto;
    padding: 0.5rem;
  }
  
  .insights-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 1rem;
  }
  
  .insights-empty-icon {
    width: 60px;
    height: 60px;
    margin-bottom: 1rem;
  }
  
  .insights-empty-icon img {
    width: 100%;
    height: 100%;
  }
  
  /* Mini chat fixes */
  .dashboard-chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }
  
  .mini-chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }
  
  .mini-chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 1rem;
  }
  
  /* Small spinner for buttons */
  .spinner-small {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    display: inline-block;
    vertical-align: middle;
    margin-right: 0.25rem;
  }
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
  
  /* Chat header and tabs */
  .chat-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .customer-inquiry-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #212b3c;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  [data-theme="dark"] .customer-inquiry-bar {
    background-color: #212b3c;
  }
  
  .customer-inquiry-input {
    flex: 1;
    padding: 10px 16px;
    border-radius: 100px;
    border: none;
    font-size: 14px;
    background-color: white;
    color: #555;
    height: 40px;
  }
  
  [data-theme="dark"] .customer-inquiry-input {
    background-color: white;
    color: #555;
  }
  
  /* Team members avatars */
  .team-members {
    display: flex;
    align-items: center;
    margin-left: 8px;
  }
  
  .team-member-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    margin-left: -8px;
    border: 2px solid #212b3c;
    position: relative;
  }
  
  .team-member-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .team-more-members {
    background-color: #e9eaec;
    color: #555;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  
  /* Tabs */
  .dashboard-tabs {
    display: flex;
    background-color: #deeeea;
  }
  
  [data-theme="dark"] .dashboard-tabs {
    background-color: #212b3c;
  }
  
  .dashboard-tab {
    padding: 16px 24px;
    font-size: 14px;
    font-weight: 500;
    color: var(--color-primary);
    cursor: pointer;
    transition: all 0.15s ease;
    position: relative;
  }
  
  .dashboard-tab:hover {
    color: rgba(255, 255, 255, 0.9);
  }
  
  .dashboard-tab.active {
    color: var(--color-primary);
  }
  
  .dashboard-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background-color: #2ac963;
  }
  
  /* Chat selection dropdown */
  .chat-selection {
    padding: 12px 16px;
    border-bottom: 1px solid var(--color-divider, #e5e7eb);
  }
  
  [data-theme="dark"] .chat-selection {
    border-bottom-color: var(--color-divider, #374151);
  }
  
  .chat-dropdown {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    color: var(--color-text, #111827);
  }
  
  [data-theme="dark"] .chat-dropdown {
    color: var(--color-text, #e5e7eb);
  }
  
  .dropdown-icon {
    width: 16px;
    height: 16px;
    color: var(--color-text-muted, #6b7280);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .dropdown-menu {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    width: 100%;
    background-color: white;
    border: 1px solid var(--color-divider, #e5e7eb);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
  }
  
  [data-theme="dark"] .dropdown-menu {
    background-color: var(--color-surface-elevated, #1e2538);
    border-color: var(--color-divider, #374151);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
  }
  
  .dropdown-item {
    padding: 10px 16px;
    cursor: pointer;
    transition: background-color 0.15s ease;
  }
  
  .dropdown-item:hover {
    background-color: var(--color-surface-highlight, rgba(245, 247, 250, 0.6));
  }
  
  [data-theme="dark"] .dropdown-item:hover {
    background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));
  }
  
  /* Enhanced Agents Section Styling */
  .agents-section.enhanced {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 51, 234, 0.05) 100%);
    border-radius: 20px;
    padding: 2rem;
    margin: 2rem 0;
    border: 1px solid rgba(59, 130, 246, 0.1);
  }
  
  .agents-header {
    margin-bottom: 2rem;
  }
  
  .agents-title-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .agents-title-section h2 {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #3b82f6, #9333ea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
  }
  
  .agents-stats {
    display: flex;
    gap: 1.5rem;
    align-items: center;
  }
  
  .agent-stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 70px;
  }
  
  [data-theme="dark"] .agent-stat {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.2);
  }
  
  .stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-primary);
    line-height: 1;
  }
  
  .stat-label {
    font-size: 0.75rem;
    color: var(--color-text-muted);
    margin-top: 0.25rem;
    font-weight: 500;
  }
  
  .agents-description.enhanced {
    font-size: 1.1rem;
    color: var(--color-text-subtle);
    line-height: 1.6;
    margin: 0;
  }
  
  /* Progress Overview Cards */
  .agents-progress-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .progress-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  [data-theme="dark"] .progress-card {
    background: rgba(30, 41, 59, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.2);
  }
  
  .progress-icon {
    font-size: 2rem;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #3b82f6, #9333ea);
    border-radius: 12px;
    flex-shrink: 0;
  }
  
  .progress-content {
    flex: 1;
  }
  
  .progress-content h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text);
  }
  
  .progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(148, 163, 184, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
  }
  
  .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #3b82f6);
    border-radius: 4px;
    transition: width 0.8s ease-in-out;
  }
  
  .progress-fill.agent-progress {
    background: linear-gradient(90deg, #8b5cf6, #ec4899);
  }
  
  .progress-text {
    font-size: 0.875rem;
    color: var(--color-text-muted);
    font-weight: 500;
  }
  
  /* Enhanced Agent Cards */
  .agents-grid.enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .agent-card.enhanced {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  
  [data-theme="dark"] .agent-card.enhanced {
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(148, 163, 184, 0.2);
  }
  
  .agent-card.enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #e5e7eb, #e5e7eb);
    transition: background 0.3s ease;
  }
  
  .agent-card.enhanced.unlocked::before {
    background: linear-gradient(90deg, #10b981, #059669);
  }
  
  .agent-card.enhanced.partial::before {
    background: linear-gradient(90deg, #f59e0b, #d97706);
  }
  
  .agent-card.enhanced.locked::before {
    background: linear-gradient(90deg, #ef4444, #dc2626);
  }
  
  .agent-card.enhanced:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
  
  .agent-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }
  
  .agent-icon-wrapper {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
  
  .agent-icon.enhanced {
    width: 3rem;
    height: 3rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
    border: 2px solid rgba(59, 130, 246, 0.2);
    overflow: hidden;
  }
  
  .agent-icon.enhanced img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .agent-status-badge {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }
  
  .agent-status-badge.unlocked {
    background: rgba(16, 185, 129, 0.1);
    color: #059669;
    border: 1px solid rgba(16, 185, 129, 0.2);
  }
  
  .agent-status-badge.partial {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border: 1px solid rgba(245, 158, 11, 0.2);
  }
  
  .agent-status-badge.locked {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
  }
  
  .status-icon {
    font-size: 0.875rem;
  }
  
  .status-text {
    font-size: 0.75rem;
  }
  
  /* Circular Progress Ring */
  .agent-progress-ring {
    width: 3rem;
    height: 3rem;
  }
  
  .circular-chart {
    display: block;
    margin: 0 auto;
    max-width: 100%;
    max-height: 100%;
  }
  
  .circle-bg {
    fill: none;
    stroke: rgba(148, 163, 184, 0.2);
    stroke-width: 2.8;
  }
  
  .circle {
    fill: none;
    stroke-width: 2.8;
    stroke-linecap: round;
    animation: progress 1s ease-in-out forwards;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
  }
  
  .circle.unlocked {
    stroke: #10b981;
  }
  
  .circle.partial {
    stroke: #f59e0b;
  }
  
  .circle.locked {
    stroke: #ef4444;
  }
  
  .percentage {
    fill: var(--color-text);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    font-size: 0.5rem;
    font-weight: 600;
    text-anchor: middle;
  }
  
  @keyframes progress {
    0% {
      stroke-dasharray: 0 100;
    }
  }
  
  /* Agent Info */
  .agent-info {
    margin-bottom: 1rem;
  }
  
  .agent-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--color-text);
    margin: 0 0 0.25rem 0;
  }
  
  .agent-role.enhanced {
    font-size: 0.875rem;
    color: var(--color-primary);
    font-weight: 600;
    margin: 0 0 0.5rem 0;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }
  
  .agent-description.enhanced {
    font-size: 0.875rem;
    color: var(--color-text-muted);
    line-height: 1.5;
    margin: 0;
  }
  
  /* Enhanced Requirements */
  .agent-requirements.enhanced {
    margin: 1rem 0;
    padding: 1rem;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(226, 232, 240, 0.5);
  }
  
  [data-theme="dark"] .agent-requirements.enhanced {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(51, 65, 85, 0.5);
  }
  
  .requirements-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--color-text);
    margin: 0 0 0.75rem 0;
  }
  
  .requirements-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .requirement.enhanced {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.2s ease;
  }
  
  .requirement.enhanced.met {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
  }
  
  .requirement.enhanced.unmet {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
  }
  
  .requirement-icon {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 0.75rem;
    font-weight: 700;
  }
  
  .requirement.enhanced.met .requirement-icon {
    background: #10b981;
    color: white;
  }
  
  .requirement.enhanced.unmet .requirement-icon {
    background: #ef4444;
    color: white;
  }
  
  .requirement-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.125rem;
  }
  
  .requirement-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color-text);
  }
  
  .requirement-status-text {
    font-size: 0.75rem;
    color: var(--color-text-muted);
    font-weight: 500;
  }
  
  /* Agent Actions */
  .agent-actions {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(226, 232, 240, 0.5);
  }
  
  [data-theme="dark"] .agent-actions {
    border-top: 1px solid rgba(51, 65, 85, 0.5);
  }
  
  .agent-action-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    border: none;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    text-transform: capitalize;
  }
  
  .agent-action-btn.ready {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
  }
  
  .agent-action-btn.ready:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
  }
  
  .agent-action-btn.partial {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
  }
  
  .agent-action-btn.partial:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-1px);
  }
  
  .agent-action-btn.locked {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
  }
  
  .agent-action-btn.locked:hover {
    background: linear-gradient(135deg, #4b5563, #374151);
    transform: translateY(-1px);
  }
  
  .action-icon {
    font-size: 1rem;
  }
  
  /* Coming Soon */
  .agent-coming-soon.enhanced {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(236, 72, 153, 0.1));
    border: 1px solid rgba(139, 92, 246, 0.2);
    border-radius: 12px;
    color: #8b5cf6;
    font-weight: 600;
    margin-top: 1rem;
  }
  
  .coming-soon-icon {
    font-size: 1.125rem;
  }
  
  /* Enhanced CTA Section */
  .agents-cta.enhanced {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    border: 1px solid rgba(59, 130, 246, 0.2);
    backdrop-filter: blur(10px);
  }
  
  .cta-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--color-text);
    margin: 0 0 0.5rem 0;
  }
  
  .cta-content p {
    font-size: 1rem;
    color: var(--color-text-muted);
    margin: 0 0 1.5rem 0;
    line-height: 1.6;
  }
  
  .cta-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .primary-cta-button,
  .secondary-cta-button {
    padding: 0.875rem 1.5rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: none;
    text-decoration: none;
    min-width: 140px;
    justify-content: center;
  }
  
  .primary-cta-button {
    background: linear-gradient(135deg, #3b82f6, #9333ea);
    color: white;
  }
  
  .primary-cta-button:hover {
    background: linear-gradient(135deg, #2563eb, #7c3aed);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
  }
  
  .secondary-cta-button {
    background: rgba(255, 255, 255, 0.8);
    color: var(--color-primary);
    border: 1px solid rgba(59, 130, 246, 0.2);
  }
  
  [data-theme="dark"] .secondary-cta-button {
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.2);
  }
  
  .secondary-cta-button:hover {
    background: rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.2);
  }
  
  .cta-icon {
    font-size: 1rem;
  }
  
  /* Responsive Design */
  @media (max-width: 768px) {
    .agents-title-section {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    
    .agents-stats {
      flex-wrap: wrap;
      gap: 1rem;
    }
    
    .agents-grid.enhanced {
      grid-template-columns: 1fr;
    }
    
    .agents-progress-overview {
      grid-template-columns: 1fr;
    }
    
    .cta-actions {
      flex-direction: column;
      align-items: center;
    }
    
    .primary-cta-button,
    .secondary-cta-button {
      width: 100%;
      max-width: 280px;
    }
  }
  
  @media (max-width: 480px) {
    .agents-section.enhanced {
      padding: 1rem;
      margin: 1rem 0;
    }
    
    .agent-card.enhanced {
      padding: 1rem;
    }
    
    .agent-card-header {
      flex-direction: column;
      gap: 1rem;
      align-items: flex-start;
    }
  }
  