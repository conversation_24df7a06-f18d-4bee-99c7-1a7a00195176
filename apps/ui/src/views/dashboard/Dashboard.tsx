import {
  useContext,
  useState,
  useCallback,
  useMemo,
  useEffect,
  useRef,
} from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import PageContent from "../../ui/PageContent";
import { LocationContext } from "../../contexts";
import { PreferencesContext } from "../../ui/PreferencesContext";
import { SingleSelect } from "../../ui/form/SingleSelect";
import { localStorageGetJson, localStorageSetJson } from "../../utils";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  LineChart,
  Line,
  ResponsiveContainer,
  ReferenceLine,
} from "recharts";
import {
  PosIcon,
  UsersIcon,
  InsightsIcon,
  CampaignsIcon,
  SegmentationIcon,
  ChartBarIcon,
  DashboardIcon,
  ChevronDownIcon,
  DocumentIcon,
  CubeIcon,
} from "../../ui/icons";
import api from "../../api";
import "./Dashboard.css";
import MiniChatPage from "./MiniChatPage";
import CampaignCalendar from "./CampaignCalendar";
import smokey_icon from "../../assets/smokey_icon.png";
import { Insight } from "../../types";
import InsightCard from "../../ui/InsightCard";
import Spinner from "../../ui/Spinner";
import { Button } from "../../ui";
import useAuth from "../../hooks/useAuth";
import Modal from "../../ui/Modal";
import { SpinnerIcon } from "../../ui/icons";

/**
 * Example data from the screenshot:
 * - Revenue: $1.2M
 * - Active Customers: +2450 (+15% from last month)
 * - Sales: $15.2M
 * - Two charts: "Sales & Revenue" and "Demand & Supply"
 * - Campaign section with a simple calendar and a "Social Media Campaign"
 */

interface Metric {
  date: Date;
  value: number;
}

// Formatting utilities
const formatCurrency = (value: number | null | undefined) => {
  if (value === null || value === undefined) return "No data";
  if (value >= 1000000) {
    return `$${(value / 1000000).toFixed(1)}M`;
  }
  if (value >= 1000) {
    return `$${(value / 1000).toFixed(1)}K`;
  }
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const formatNumber = (
  value: number | null | undefined,
  decimals: number = 0
) => {
  if (value === null || value === undefined) return "No data";
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  }
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  }
  return new Intl.NumberFormat("en-US", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
};

const formatPercentage = (value: number | null | undefined) => {
  if (value === null || value === undefined) return "No data";
  return new Intl.NumberFormat("en-US", {
    style: "percent",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value / 100);
};

const ProductQualityCard = ({ quality, title, icon: Icon }: any) => {
  const score = quality?.score || 0;
  const getQualityColor = (s: number) => {
    if (s > 80) return "bg-emerald-500";
    if (s > 50) return "bg-yellow-500";
    return "bg-red-500";
  };
  return (
    <div className="bg-surface shadow p-6 rounded-lg flex flex-col justify-between">
      <div>
        <div className="flex items-center">
          <div className="mr-4">{Icon && <Icon className="w-6 h-6" />}</div>
          <div>
            <h3 className="text-sm mb-1">{title}</h3>
            <div className="text-xl font-bold">{score.toFixed(0)}%</div>
          </div>
        </div>
      </div>
      <div className="mt-4">
        <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
          <div
            className={`h-2.5 rounded-full ${getQualityColor(score)}`}
            style={{ width: `${score}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
};

// Chat type interface
interface Chat {
  chat_id: string;
  name: string;
  created_at: string;
  summary?: string;
  insights?: any[];
  agents?: Array<{
    agent_id: string;
    name: string;
    role?: string;
    avatar?: string;
  }>;
}

export default function Dashboard() {
  const [location] = useContext(LocationContext);
  const [preferences] = useContext(PreferencesContext);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { locationId } = useParams();
  const { user } = useAuth();
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [activeTab, setActiveTab] = useState("chat");
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Chat state
  const [chats, setChats] = useState<Chat[]>([]);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);

  // Insights state
  const [insights, setInsights] = useState<Insight[]>([]);
  const [insightsLoading, setInsightsLoading] = useState(false);
  const [generatingInsights, setGeneratingInsights] = useState(false);
  const [selectedModel, setSelectedModel] = useState("gpt-4.1-mini");

  // Timeframe state management
  const [timeframe, setTimeframe] = useState<String>(() => {
    return localStorageGetJson<String>("dashboard_timeframe") || "7d";
  });

  const handleTimeframeChange = (value: String) => {
    setTimeframe(value);
    localStorageSetJson("dashboard_timeframe", value);
  };

  const [dashboardData, setDashboardData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [showActionsModal, setShowActionsModal] = useState<boolean>(false);
  const [selectedInsight, setSelectedInsight] = useState<any>(null);
  const [agentStatus, setAgentStatus] = useState<any>({
    hasPosData: false,
    hasUserData: false,
    hasSocialMedia: false,
    hasBusinessDocs: false,
    hasCompetitors: false,
    hasLabData: false,
    hasGovernmentData: false,
  });

  // Add agent data state
  const [agentData, setAgentData] = useState<any>(null);
  const [agentsLoading, setAgentsLoading] = useState(true);

  // Note: Onboarding status polling is now handled by AppLayout.tsx to avoid duplicate polling
  // These state variables are kept for the OnboardingProcessing modal component
  const [onboardingProcessing, setOnboardingProcessing] = useState(false);
  const [processingDetails, setProcessingDetails] = useState<any>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        const data = await api.dashboard.get(location.id, { timeframe });
        setDashboardData(data);

        // Extract agent status information
        if (data) {
          setAgentStatus({
            hasPosData: data.hasPosData || false,
            hasUserData: data.hasUserData || false,
            hasSocialMedia: data.hasSocialMedia || false,
            hasBusinessDocs: data.hasBusinessDocs || false,
            hasCompetitors: data.hasCompetitors || false,
            hasLabData: data.hasLabData || false,
            hasGovernmentData: data.hasGovernmentData || false,
          });
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [location.id, timeframe]);

  // Fetch agent data
  useEffect(() => {
    const fetchAgentData = async () => {
      if (!location?.id) return;
      setAgentsLoading(true);
      try {
        const response = await api.agents.getAvailability(location.id);
        setAgentData(response);
      } catch (error) {
        console.error("Error fetching agent data:", error);
      } finally {
        setAgentsLoading(false);
      }
    };

    fetchAgentData();
  }, [location.id]);

  // Load chats
  useEffect(() => {
    const loadChats = async () => {
      try {
        if (!user || !location?.id) return;
        const response = await api.getChats(location.id.toString());
        if (response) {
          setChats(response);
        }
      } catch (error) {
        console.error("Error loading chats:", error);
      }
    };

    loadChats();
  }, [user, location?.id]);

  // Function to load insights
  const loadInsights = useCallback(async () => {
    if (!location?.id) return;
    setInsightsLoading(true);
    try {
      const data = await api.insights.get(location.id);
      setInsights(data);
    } catch (error) {
      console.error("Error loading insights:", error);
    }
    setInsightsLoading(false);
  }, [location.id]);

  // Track window width for responsive design
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Load insights when tab changes to insights
  useEffect(() => {
    if (activeTab === "insights") {
      loadInsights();
    }
  }, [activeTab, loadInsights]);

  // Function to generate new insights
  const handleGenerateInsights = async () => {
    if (!location?.id) return;
    setGeneratingInsights(true);
    try {
      await api.insights.generate(location.id, selectedModel);
      await loadInsights(); // Refresh the list after generation
    } catch (error) {
      console.error("Error generating insights:", error);
    }
    setGeneratingInsights(false);
  };

  // Helper functions for data validation
  const formatCurrency = useCallback(
    (value: number | null | undefined) => {
      if (value === null || value === undefined) return t("no_data");
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(value);
    },
    [t]
  );

  const formatNumber = (
    value: number | null | undefined,
    decimals: number = 2
  ) => {
    if (value === null || value === undefined) return t("no_data");
    return new Intl.NumberFormat("en-US", {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(value);
  };

  const formatPercentage = (value: number | null | undefined) => {
    if (value === null || value === undefined) return t("no_data");
    return new Intl.NumberFormat("en-US", {
      style: "percent",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value / 100);
  };

  const hasChartData = (data: any[] | null | undefined) => {
    return Array.isArray(data) && data.length > 0;
  };

  // Safely prepare chart data
  // Chart data preparation
  const revenueData = useMemo(
    () => [
      {
        label: "Revenue",
        data:
          dashboardData?.charts?.revenue?.map((item: any) => ({
            date: new Date(item.order_date),
            value: item.revenue,
          })) || [],
      },
    ],
    [dashboardData?.charts?.revenue]
  );

  const salesData = useMemo(
    () => [
      {
        label: "Sales",
        data:
          dashboardData?.charts?.sales?.map((item: any) => ({
            date: new Date(item.order_date),
            value: item.total_orders,
          })) || [],
      },
    ],
    [dashboardData?.charts?.sales]
  );

  // Axis configurations
  const primaryAxis = useMemo(
    () => ({
      getValue: (datum: Metric) => datum.date,
    }),
    []
  );

  const secondaryAxes = useMemo(
    () => [
      {
        getValue: (datum: Metric) => datum.value,
        elementType: "line",
        tickCount: 5,
        shouldNice: true,
        formatters: {
          scale: (value: number) => formatCurrency(value),
        },
      },
    ],
    [formatCurrency]
  );

  // Timeframe options
  const timeframeOptions = ["7d", "30d", "90d", "1y", "all"];

  const getTimeframeLabel = (timeframe: String) => {
    switch (timeframe) {
      case "7d":
        return t("last_7_days");
      case "30d":
        return t("last_30_days");
      case "90d":
        return t("last_90_days");
      case "1y":
        return t("last_year");
      case "all":
        return t("all_time");
      default:
        return timeframe;
    }
  };

  // Agent UI metadata mapping
  const agentUIMetadata = useMemo(
    () =>
      ({
        "1": { icon: smokey_icon },
        "2": { icon: CampaignsIcon },
        "3": { icon: InsightsIcon },
        "4": { icon: SegmentationIcon },
        "5": { icon: ChartBarIcon },
        "6": { icon: UsersIcon },
        "7": { icon: DashboardIcon },
      } as Record<string, { icon: any }>),
    []
  );

  // Get agents with combined API data and UI metadata
  const agents = useMemo(() => {
    if (!agentData || agentsLoading) {
      return [];
    }

    // Combine available and unavailable agents
    const allApiAgents = [
      ...(agentData.available || []),
      ...(agentData.unavailable || []),
    ];

    return allApiAgents.map((agent: any) => {
      const uiMeta = agentUIMetadata[agent.id.toString()] || {};

      // Convert missing requirements to user-friendly labels
      const requirements =
        agent.missingRequirements?.map((req: string) => {
          // Parse requirement strings like "Missing pos integration"
          if (req.includes("pos"))
            return { label: "POS Data", connected: false };
          if (req.includes("customer_data") || req.includes("user"))
            return { label: "Customer Data", connected: false };
          if (req.includes("market_data"))
            return { label: "Market Data", connected: false };
          if (req.includes("gov_db") || req.includes("government"))
            return { label: "Government Database", connected: false };
          if (req.includes("lab_data"))
            return { label: "Lab Data", connected: false };
          if (req.includes("business_docs"))
            return { label: "Business Documents", connected: false };
          if (req.includes("competitor"))
            return { label: "Competitor Data", connected: false };
          return { label: req, connected: false };
        }) || [];

      // Add connected requirements from integration status
      if (agent.metadata?.integrationStatus) {
        Object.entries(agent.metadata.integrationStatus).forEach(
          ([key, status]: [string, any]) => {
            if (status.connected) {
              let label = key
                .replace(/_/g, " ")
                .replace(/\b\w/g, (l: string) => l.toUpperCase());
              if (key === "pos") label = "POS Data";
              if (key === "customer_data") label = "Customer Data";
              if (key === "product_data") label = "Product Data";
              if (key === "competitor_data") label = "Competitor Data";
              if (key === "market_data") label = "Market Data";
              if (key === "gov_db" || key === "government_db")
                label = "Government Database";
              if (key === "lab_data") label = "Lab Data";
              if (key === "business_docs") label = "Business Documents";

              // Only add if not already in requirements as missing
              if (!requirements.find((r: any) => r.label === label)) {
                requirements.push({ label, connected: true });
              }
            }
          }
        );
      }

      return {
        id: agent.id,
        name: agent.name,
        role: agent.role,
        description: agent.description,
        icon: uiMeta.icon || agent.icon,
        requirements,
        unlocked: agent.unlocked,
        missingRequirements: agent.missingRequirements || [],
        comingSoon: false, // Add this field for compatibility
        unlockCondition: () => {
          if (agent.unlocked) return "unlocked";
          if (agent.missingRequirements?.length > 0) {
            // Check if some requirements are met (partial unlock)
            const hasConnectedData = requirements.some((r: any) => r.connected);
            return hasConnectedData ? "partial" : "locked";
          }
          return "locked";
        },
      };
    });
  }, [agentData, agentUIMetadata, agentsLoading]);

  // Define chart colors based on theme
  const COLORS = useMemo(() => {
    return preferences.mode === "dark"
      ? ["#60a5fa", "#34d399", "#fbbf24", "#f87171"]
      : ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"];
  }, [preferences.mode]);

  if (loading) {
    return (
      <PageContent title={t("dashboard")}>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">{t("loading")}</div>
        </div>
      </PageContent>
    );
  }

  const OnboardingProcessing = () => {
    return (
      <Modal
        open={onboardingProcessing}
        onClose={() => {}}
        title={t("onboarding_processing")}
      >
        <div className="p-4">
          <p className="mb-4">{t("onboarding_processing_description")}</p>
          <p className="mb-4">{t("onboarding_files_uploading")}</p>
          <p className="mb-4">{t("onboarding_files_background")}</p>

          {processingDetails && (
            <div className="w-full mt-4">
              {processingDetails.processing_jobs > 0 && (
                <div className="flex justify-between text-sm mb-2">
                  <span>{t("onboarding_processing_jobs")}:</span>
                  <span className="font-semibold">
                    {processingDetails.processing_jobs}
                  </span>
                </div>
              )}

              {processingDetails.completed_jobs > 0 && (
                <div className="flex justify-between text-sm mb-2">
                  <span>{t("onboarding_completed_jobs")}:</span>
                  <span className="font-semibold">
                    {processingDetails.completed_jobs}
                  </span>
                </div>
              )}

              {processingDetails.failed_jobs > 0 && (
                <div className="flex justify-between text-sm mb-2 text-red-500">
                  <span>{t("onboarding_failed_jobs")}:</span>
                  <span className="font-semibold">
                    {processingDetails.failed_jobs}
                  </span>
                </div>
              )}

              {processingDetails.total_jobs > 0 && (
                <div className="mt-2 mb-2">
                  <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                    <div
                      className="bg-primary h-2.5 rounded-full"
                      style={{
                        width: `${Math.round(
                          (processingDetails.completed_jobs /
                            processingDetails.total_jobs) *
                            100
                        )}%`,
                      }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1 text-center">
                    {Math.round(
                      (processingDetails.completed_jobs /
                        processingDetails.total_jobs) *
                        100
                    )}
                    % {t("complete")}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </Modal>
    );
  };
  // Check if we have data to display
  if (!dashboardData) {
    return (
      <PageContent title={t("dashboard")}>
        <div className="flex items-center justify-center h-64">
          <Spinner />
        </div>
      </PageContent>
    );
  }

  const agentsAreLocked = agents.some(
    (a: any) => a.unlockCondition() !== "unlocked"
  );

  return (
    <PageContent title={""} className="no-horizontal-padding">
      <div className="dashboard-main">
        {/* Welcome Header */}
        {!dashboardData?.hasPosData && (
          <div className="welcome-dashboard">
            <div className="welcome-header">
              <div className="text-content">
                <h1 className="welcome-title">
                  Welcome to BakedBot Marketing Automation
                </h1>
                <p className="welcome-subtitle">
                  Your AI-powered cannabis marketing assistant
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Timeframe Selector */}
        {dashboardData?.hasPosData && (
          <div className="mb-6">
            <SingleSelect
              value={timeframe}
              onChange={handleTimeframeChange}
              getOptionDisplay={(value) => getTimeframeLabel(value)}
              options={timeframeOptions}
            />
          </div>
        )}

        {/* Top KPI Cards */}
        {dashboardData?.hasPosData ? (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 mt-8">
            <div className="bg-emerald-500 dark:bg-emerald-600 text-white p-6 rounded-lg shadow">
              <h3 className="text-sm opacity-80 mb-2">{t("revenue")}</h3>
              <div className="text-xl font-bold mb-2">
                {formatCurrency(dashboardData?.kpi?.revenue || 0)}
              </div>
              <div className="text-sm">
                {formatPercentage(
                  dashboardData?.analytics?.profitabilityAnalysis
                    ?.profit_margin || 0
                )}{" "}
                profit margin
              </div>
            </div>

            <div className="bg-surface shadow p-6 rounded-lg">
              <h3 className="text-sm  mb-2">{t("active_customers")}</h3>
              <div className="text-xl font-bold  mb-2">
                +
                {formatNumber(
                  dashboardData?.customerMetrics?.purchasedInTimeframe || 0
                )}
              </div>
              <div className="text-sm text-emerald-600 dark:text-emerald-400">
                {formatCurrency(dashboardData?.kpi?.customerValue || 0)}{" "}
                customer lifetime value
              </div>
            </div>

            <div className="bg-surface shadow p-6 rounded-lg">
              <h3 className="text-sm  mb-2">{t("average_order")}</h3>
              <div className="text-xl font-bold  mb-2">
                {formatCurrency(dashboardData?.kpi?.averageOrder || 0)}
              </div>
              <div className="text-sm text-emerald-600 dark:text-emerald-400">
                {formatNumber(
                  dashboardData?.analytics?.profitabilityAnalysis
                    ?.total_profit || 0,
                  2
                )}{" "}
                total profit
              </div>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 mt-8">
            <div className="bg-surface shadow p-6 rounded-lg">
              <div className="flex items-center">
                <div className="mr-4">
                  <div className="w-6 h-6">
                    <UsersIcon />
                  </div>
                </div>
                <div>
                  <h3 className="text-sm mb-1">{t("total_customers")}</h3>
                  <div className="text-xl font-bold">
                    {formatNumber(dashboardData?.stats?.totalUsers || 0, 0)}
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-surface shadow p-6 rounded-lg">
              <div className="flex items-center">
                <div className="mr-4">
                  <div className="w-6 h-6">
                    <CubeIcon />
                  </div>
                </div>
                <div>
                  <h3 className="text-sm mb-1">{t("total_products")}</h3>
                  <div className="text-xl font-bold">
                    {formatNumber(dashboardData?.stats?.totalProducts || 0, 0)}
                  </div>
                </div>
              </div>
            </div>
            <ProductQualityCard
              quality={dashboardData?.stats?.productQuality}
              title="Product Data Quality"
              icon={DocumentIcon}
            />
          </div>
        )}

        {/* Main Content: Charts or Onboarding Steps */}
        {dashboardData?.hasPosData && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div className="bg-surface shadow p-6 rounded-lg">
              <div className="flex justify-between items-center mb-6">
                <h4 className="font-semibold ">REVENUE & SALES</h4>
              </div>
              <div
                className="chart-container rounded-lg overflow-hidden"
                style={{ width: "100%", height: 300 }}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={
                      dashboardData?.charts?.revenue?.map((item: any) => ({
                        ...item,
                        // Don't artificially cap data values, handle with appropriate scaling
                        revenue: Math.max(item.revenue, 0),
                      })) || []
                    }
                    margin={{ top: 10, right: 30, left: 20, bottom: 25 }}
                  >
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke={
                        preferences.mode === "dark" ? "#374151" : "#e5e7eb"
                      }
                      opacity={0.6}
                    />
                    <XAxis
                      dataKey="order_date"
                      stroke={
                        preferences.mode === "dark" ? "#9ca3af" : "#6b7280"
                      }
                      tick={{
                        fill:
                          preferences.mode === "dark" ? "#9ca3af" : "#6b7280",
                        fontSize: 11,
                      }}
                      tickFormatter={(tick) => {
                        const date = new Date(tick);
                        return new Intl.DateTimeFormat("en-US", {
                          month: "short",
                          day: "numeric",
                        }).format(date);
                      }}
                      interval={Math.min(
                        Math.floor(
                          dashboardData?.charts?.revenue?.length / 10
                        ) || 0,
                        30
                      )}
                      angle={-30}
                      textAnchor="end"
                      height={60}
                      axisLine={{
                        stroke:
                          preferences.mode === "dark" ? "#4b5563" : "#d1d5db",
                      }}
                      padding={{ left: 10, right: 10 }}
                    />
                    <YAxis
                      stroke={
                        preferences.mode === "dark" ? "#9ca3af" : "#6b7280"
                      }
                      tick={{
                        fill:
                          preferences.mode === "dark" ? "#9ca3af" : "#6b7280",
                        fontSize: 11,
                      }}
                      tickFormatter={(tick) =>
                        formatCurrency(tick).replace(".00", "")
                      }
                      domain={["auto", "auto"]}
                      allowDataOverflow={false}
                      padding={{ top: 20 }}
                      axisLine={{
                        stroke:
                          preferences.mode === "dark" ? "#4b5563" : "#d1d5db",
                      }}
                      tickLine={{
                        stroke:
                          preferences.mode === "dark" ? "#4b5563" : "#d1d5db",
                      }}
                      minTickGap={5}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor:
                          preferences.mode === "dark" ? "#1f2937" : "#fff",
                        borderColor:
                          preferences.mode === "dark" ? "#374151" : "#e5e7eb",
                        color: preferences.mode === "dark" ? "#fff" : "#000",
                        borderRadius: "6px",
                        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
                        padding: "10px 14px",
                        fontSize: "12px",
                      }}
                      formatter={(value) => [
                        formatCurrency(Number(value)),
                        "Revenue",
                      ]}
                      labelFormatter={(label) => {
                        const date = new Date(label);
                        return new Intl.DateTimeFormat("en-US", {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        }).format(date);
                      }}
                      cursor={{
                        stroke: COLORS[0],
                        strokeWidth: 1,
                        strokeDasharray: "5 5",
                      }}
                    />
                    <Legend
                      wrapperStyle={{ paddingTop: 15 }}
                      iconType="circle"
                      iconSize={8}
                    />
                    <Line
                      type="monotone"
                      dataKey="revenue"
                      name="Revenue"
                      stroke={COLORS[0]}
                      strokeWidth={2.5}
                      activeDot={{ r: 6, stroke: "#fff", strokeWidth: 2 }}
                      isAnimationActive={true}
                      animationDuration={1000}
                      dot={false}
                      connectNulls={true}
                    />
                    {/* Add a reference line for average revenue if available */}
                    {dashboardData?.analytics?.profitabilityAnalysis
                      ?.average_revenue && (
                      <ReferenceLine
                        y={
                          dashboardData.analytics.profitabilityAnalysis
                            .average_revenue
                        }
                        stroke={
                          preferences.mode === "dark" ? "#9ca3af" : "#6b7280"
                        }
                        strokeDasharray="3 3"
                        label={{
                          value: "Avg Revenue",
                          position: "insideBottomRight",
                          fill:
                            preferences.mode === "dark" ? "#9ca3af" : "#6b7280",
                          fontSize: 10,
                        }}
                      />
                    )}
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>

            <div className="bg-surface shadow p-6 rounded-lg">
              <div className="flex justify-between items-center mb-6">
                <h4 className="font-semibold ">SALES & CUSTOMERS</h4>
              </div>
              <div
                className="chart-container rounded-lg overflow-hidden"
                style={{ width: "100%", height: 300 }}
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={dashboardData?.charts?.sales || []}
                    margin={{ top: 10, right: 30, left: 20, bottom: 25 }}
                  >
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke={
                        preferences.mode === "dark" ? "#374151" : "#e5e7eb"
                      }
                      opacity={0.6}
                    />
                    <XAxis
                      dataKey="order_date"
                      stroke={
                        preferences.mode === "dark" ? "#9ca3af" : "#6b7280"
                      }
                      tick={{
                        fill:
                          preferences.mode === "dark" ? "#9ca3af" : "#6b7280",
                        fontSize: 11,
                      }}
                      tickFormatter={(tick) => {
                        const date = new Date(tick);
                        return new Intl.DateTimeFormat("en-US", {
                          month: "short",
                          day: "numeric",
                        }).format(date);
                      }}
                      interval={Math.min(
                        Math.floor(dashboardData?.charts?.sales?.length / 10) ||
                          0,
                        30
                      )}
                      angle={-30}
                      textAnchor="end"
                      height={60}
                      axisLine={{
                        stroke:
                          preferences.mode === "dark" ? "#4b5563" : "#d1d5db",
                      }}
                      padding={{ left: 10, right: 10 }}
                    />
                    <YAxis
                      stroke={
                        preferences.mode === "dark" ? "#9ca3af" : "#6b7280"
                      }
                      tick={{
                        fill:
                          preferences.mode === "dark" ? "#9ca3af" : "#6b7280",
                        fontSize: 11,
                      }}
                      domain={["auto", "auto"]}
                      allowDataOverflow={false}
                      padding={{ top: 20 }}
                      axisLine={{
                        stroke:
                          preferences.mode === "dark" ? "#4b5563" : "#d1d5db",
                      }}
                      tickLine={{
                        stroke:
                          preferences.mode === "dark" ? "#4b5563" : "#d1d5db",
                      }}
                      minTickGap={5}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor:
                          preferences.mode === "dark" ? "#1f2937" : "#fff",
                        borderColor:
                          preferences.mode === "dark" ? "#374151" : "#e5e7eb",
                        color: preferences.mode === "dark" ? "#fff" : "#000",
                        borderRadius: "6px",
                        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
                        padding: "10px 14px",
                        fontSize: "12px",
                      }}
                      labelFormatter={(label) => {
                        const date = new Date(label);
                        return new Intl.DateTimeFormat("en-US", {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        }).format(date);
                      }}
                      formatter={(value, name) => [
                        formatNumber(Number(value)),
                        name,
                      ]}
                      cursor={{
                        stroke: "#9ca3af",
                        strokeWidth: 1,
                        strokeDasharray: "5 5",
                      }}
                    />
                    <Legend
                      wrapperStyle={{ paddingTop: 15 }}
                      iconType="circle"
                      iconSize={8}
                    />
                    <Line
                      type="monotone"
                      dataKey="total_orders"
                      name="Orders"
                      stroke={COLORS[2]}
                      strokeWidth={2.5}
                      activeDot={{ r: 6, stroke: "#fff", strokeWidth: 2 }}
                      isAnimationActive={true}
                      animationDuration={1000}
                      dot={false}
                      connectNulls={true}
                    />
                    <Line
                      type="monotone"
                      dataKey="unique_customers"
                      name="Unique Customers"
                      stroke={COLORS[3]}
                      strokeWidth={2.5}
                      activeDot={{ r: 6, stroke: "#fff", strokeWidth: 2 }}
                      isAnimationActive={true}
                      animationDuration={700}
                      dot={false}
                      connectNulls={true}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        )}

        {/* AI Agents Section */}
        {agentsAreLocked && (
          <div className="agents-section enhanced mt-8">
            <div className="agents-header">
              <div className="agents-title-section">
                <h2>Your AI Marketing Agents</h2>
                <div className="agents-stats">
                  <div className="agent-stat">
                    <span className="stat-number">
                      {
                        agents.filter((a) => a.unlockCondition() === "unlocked")
                          .length
                      }
                    </span>
                    <span className="stat-label">Unlocked</span>
                  </div>
                  <div className="agent-stat">
                    <span className="stat-number">
                      {
                        agents.filter((a) => a.unlockCondition() === "partial")
                          .length
                      }
                    </span>
                    <span className="stat-label">Partial</span>
                  </div>
                  <div className="agent-stat">
                    <span className="stat-number">
                      {
                        agents.filter((a) => a.unlockCondition() === "locked")
                          .length
                      }
                    </span>
                    <span className="stat-label">Locked</span>
                  </div>
                </div>
              </div>
              <p className="agents-description enhanced">
                Connect your data to unlock powerful AI agents that will help
                grow your business. Each agent specializes in different aspects
                of your cannabis retail operation.
              </p>
            </div>

            <div className="agents-grid enhanced">
              {agents.map((agent) => {
                const status = agent.unlockCondition();
                const requirementsMet = agent.requirements.filter(
                  (req: any) => req.connected
                ).length;
                const totalRequirements = agent.requirements.length;
                const progressPercentage =
                  totalRequirements > 0
                    ? Math.round((requirementsMet / totalRequirements) * 100)
                    : 100;

                return (
                  <div
                    key={agent.id}
                    className={`agent-card-new enhanced ${status}`}
                  >
                    <div className="agent-main-info">
                      <div className="agent-header-new">
                        <div className="agent-icon-new">
                          {typeof agent.icon === "string" ? (
                            <img src={agent.icon} alt={agent.name} />
                          ) : (
                            <agent.icon />
                          )}
                        </div>
                        <div className="agent-title-group">
                          <h3 className="agent-name-new">{agent.name}</h3>
                          <p className="agent-role-new">{agent.role}</p>
                        </div>
                      </div>

                      <div className="agent-status-new">
                        <div className={`status-badge-new ${status}`}>
                          {status === "unlocked" && "Ready"}
                          {status === "partial" && "Partial"}
                          {status === "locked" && "Locked"}
                        </div>
                        <div className="progress-circle-new">
                          <svg
                            viewBox="0 0 36 36"
                            className="circular-chart-new"
                          >
                            <path
                              className="circle-bg-new"
                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                            />
                            <path
                              className={`circle-new ${status}`}
                              strokeDasharray={`${progressPercentage}, 100`}
                              d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                            />
                          </svg>
                          <div className="progress-text-new">
                            {progressPercentage}%
                          </div>
                        </div>
                      </div>

                      <p className="agent-description-new">
                        {agent.description}
                      </p>
                    </div>

                    {agent.requirements.length > 0 && (
                      <div className="agent-requirements-new">
                        <h4 className="requirements-title-new">
                          Data Requirements ({requirementsMet}/
                          {totalRequirements})
                        </h4>
                        <div className="requirements-list-new">
                          {agent.requirements.map((req: any, index: number) => (
                            <div
                              key={index}
                              className={`requirement-new ${
                                req.connected ? "met" : "unmet"
                              }`}
                            >
                              <div className="requirement-icon-new">
                                {req.connected ? "✓" : "×"}
                              </div>
                              <span className="requirement-label-new">
                                {req.label}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Campaigns Section - Using the new CampaignCalendar component */}
        <div className="bg-surface shadow rounded-lg p-6 mt-8">
          <CampaignCalendar />
        </div>
      </div>
    </PageContent>
  );
}
