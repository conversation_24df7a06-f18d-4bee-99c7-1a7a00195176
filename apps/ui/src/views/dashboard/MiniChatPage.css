/* Mini Chat Page Styles */
.mini-chat-container,
.mini-insights-container {
  /* display: flex; */
  /* flex-direction: column; */
  height: 100vh;
  background-color: white;
  border-radius: 0;
  overflow: hidden;
  box-shadow: none;
  position: relative; /* Added for resize handle positioning */
  /* flex: 1; */
  /* max-height: 100%; */
}

[data-theme="dark"] .mini-chat-container,
[data-theme="dark"] .mini-insights-container {
  background-color: var(--color-surface-elevated, #1e2538);
  box-shadow: none;
}

/* Drag Resize Handle */
.mini-chat-resize-handle {
  position: absolute;
  left: -6px;
  top: 0;
  width: 12px;
  height: 100%;
  cursor: ew-resize;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mini-chat-resize-handle::after {
  content: "";
  display: block;
  width: 4px;
  height: 50px;
  border-radius: 2px;
  background-color: var(--color-brunswick-green, #23504A);
  opacity: 0;
  transition: opacity 0.2s ease;
}

/* Drag button that appears on hover */
.mini-chat-resize-handle::before {
  content: "⋮";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%) rotate(90deg);
  width: 24px;
  height: 24px;
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  opacity: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: opacity 0.2s ease, transform 0.2s ease;
}

.mini-chat-resize-handle:hover::before {
  opacity: 0.8;
  transform: translateY(-50%) rotate(90deg) scale(1.1);
}

.mini-chat-resize-handle:active::before {
  opacity: 1;
  transform: translateY(-50%) rotate(90deg) scale(1);
}

.mini-chat-resize-handle:hover::after {
  opacity: 0.5;
}

.mini-chat-resize-handle:active::after {
  opacity: 0.7;
}

/* Header Styles */
.mini-chat-header {
  display: flex;
  flex-direction: column;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border, #e5e7eb);
  background-color: var(--color-surface);
  flex-shrink: 0;
  gap: 8px;
  z-index: 2;
}

[data-theme="dark"] .mini-chat-header {
  background-color: var(--color-surface-elevated, #1e2538);
  border-bottom-color: var(--color-divider, #374151);
}

.mini-chat-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.mini-chat-agents-row {
  display: flex;
  align-items: center;
  padding-top: 4px;
}

.mini-chat-dropdown {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  width: 100%;
}

.mini-chat-selected {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

[data-theme="dark"] .mini-chat-dropdown {
  color: var(--color-primary, #e5e7eb);
}

.mini-chat-dropdown-icon {
  width: 16px;
  height: 16px;
  color: var(--color-text-muted, #6b7280);
}

.mini-chat-dropdown-content {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-top: 8px;
  max-height: 300px;
  overflow-y: auto;
}

[data-theme="dark"] .mini-chat-dropdown-content {
  background-color: var(--color-surface-elevated, #1e2538);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.mini-chat-dropdown-item {
  padding: 8px 16px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.mini-chat-dropdown-item:hover {
  background-color: var(--color-bg-hover, #f3f4f6);
}

[data-theme="dark"] .mini-chat-dropdown-item:hover {
  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));
}

.mini-chat-dropdown-divider {
  height: 1px;
  background-color: var(--color-border, #e5e7eb);
  margin: 4px 0;
}

[data-theme="dark"] .mini-chat-dropdown-divider {
  background-color: var(--color-divider, #374151);
}

/* Agents Styles */
.mini-chat-agents {
  display: flex;
  align-items: center;
}

.mini-chat-agent-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: var(--color-brunswick-green, #23504A);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  font-size: 12px;
  position: relative;
  border: 2px solid white;
  box-sizing: content-box;
}

[data-theme="dark"] .mini-chat-agent-avatar {
  border-color: var(--color-surface-elevated, #1e2538);
}

.mini-chat-agent-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.mini-avatar-placeholder {
  font-size: 12px;
  font-weight: 500;
}

.mini-chat-agent-more {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1a2540;
  color: white;
  font-weight: 600;
  font-size: 13px;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  margin-left: -8px;
  box-sizing: content-box;
  border: 2px solid white;
  z-index: 1;
}

[data-theme="dark"] .mini-chat-agent-more {
  border-color: var(--color-surface-elevated, #1e2538);
}

.mini-chat-agent-avatar:hover .mini-agent-tooltip {
  display: block;
}

.mini-agent-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: white;
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  white-space: nowrap;
  display: none;
  margin-bottom: 6px;
  z-index: 10;
}

.mini-agent-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 4px;
  border-style: solid;
  border-color: #333 transparent transparent transparent;
}

.mini-agent-name {
  font-weight: 500;
}

.mini-agent-role {
  font-size: 10px;
  opacity: 0.8;
}

/* Messages Area */
.mini-chat-messages {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: calc(100% - 100px);
}

.mini-chat-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px;
  height: 100%;
}

[data-theme="dark"] .mini-chat-welcome {
  color: var(--color-text-muted, #9ca3af);
}

.mini-smokey-image {
  height: 60px;
  overflow: hidden;
  margin-bottom: 16px;
}

.mini-smokey-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.mini-chat-welcome h3 {
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 8px;
  color: var(--color-emerald-green);
}

.mini-chat-welcome p {
  font-size: 14px;
  color: var(--color-primary-soft);
  margin: 0 0 20px;
  max-width: 280px;
}

/* Thought cloud styles */
.mini-thought-cloud {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
  max-width: 100%;
  margin-top: 20px;
}

.mini-thought-bubble {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 10px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  min-height: 50px;
  gap: 3px;
  font-size: 13px;
}

.mini-thought-bubble:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.mini-thought-bubble.loading {
  opacity: 0.7;
  cursor: default;
  animation: pulse 1.5s ease-in-out infinite;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.mini-thought-bubble .bubble-text {
  font-size: 13px;
  line-height: 1.3;
  color: #1f2937;
  font-weight: 500;
}

.mini-thought-bubble .bubble-agent {
  font-size: 10px;
  color: #6b7280;
  font-weight: 400;
  opacity: 0.8;
  margin-top: 1px;
}

/* Pulse animation for loading states */
@keyframes pulse {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 0.5;
  }
}

/* Loading state for thought clouds */
.mini-thought-cloud .loading {
  text-align: center;
  grid-column: 1 / -1;
  padding: 15px;
  color: #6b7280;
  font-style: italic;
  font-size: 12px;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  .mini-thought-cloud {
    grid-template-columns: 1fr;
    gap: 8px;
  }
  
  .mini-thought-bubble {
    padding: 8px 10px;
    min-height: 45px;
  }
  
  .mini-thought-bubble .bubble-text {
    font-size: 12px;
  }
  
  .mini-thought-bubble .bubble-agent {
    font-size: 9px;
  }
}

/* Message Styling */
.mini-chat-message-row {
  display: flex;
  margin-bottom: 12px;
}

.user-message-row {
  justify-content: flex-end;
}

.bot-message-row {
  justify-content: flex-start;
}

.mini-message-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.bot-avatar {
  background-color: var(--color-secondary, #8b5cf6);
  color: white;
  margin-right: 8px;
}

.user-avatar {
  background-color: var(--color-primary, #3b82f6);
  color: var(--color-surface-elevated, #1e2538);
  margin-left: 8px;
}

.mini-message-avatar-placeholder {
  font-size: 12px;
  font-weight: 500;
}

.mini-chat-message-content {
  max-width: 75%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-message-row .mini-chat-message-content {
  align-items: flex-end;
}

/* Message header styles - WhatsApp style */
.mini-message-header {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 2px;
  padding: 0 3px;
}

.mini-message-header.bot-header {
  color: var(--color-brunswick-green, #23504A);
}

.mini-message-header.user-header {
  color: var(--color-emerald-green, #22AD85);
  text-align: right;
}

.mini-message-title {
  font-size: 10px;
  font-weight: normal;
  opacity: 0.8;
}

/* Message timestamp - WhatsApp style */
.mini-message-timestamp {
  font-size: 11px;
  opacity: 0.7;
  display: inline-block;
  margin-top: 2px;
  user-select: none;
  position: absolute;
  bottom: 2px;
  right: 12px;
}

.mini-chat-message {
  padding: 8px 12px;
  padding-bottom: 20px;
  border-radius: 16px;
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  margin-bottom: 4px;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 100px;

}

.mini-bot-message {
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  border-top-left-radius: 4px;
  align-self: flex-start;
  max-width: 100%;
}

[data-theme="dark"] .mini-bot-message {
  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));
  color: var(--color-primary, #e5e7eb);
}

/* Typing Indicator */
.mini-typing-indicator {
  display: flex;
  gap: 4px;
  padding: 12px 16px;
}

.mini-typing-indicator span {
  width: 8px;
  height: 8px;
  background-color: var(--color-honeydew, #DFF4E9);
  opacity: 0.7;
  border-radius: 50%;
  animation: typing-animation 1.4s infinite ease-in-out;
}

.mini-typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.mini-typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.mini-typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-animation {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

[data-theme="dark"] .mini-typing-indicator span {
  background-color: var(--color-text-muted, #9ca3af);
}

/* Feedback Buttons */
.mini-feedback-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 4px;
  padding-right: 0;
  opacity: 0.6;
  transition: opacity 0.2s ease;
  width: 100%;
  align-self: flex-start;
}

.mini-chat-message-content:hover .mini-feedback-buttons {
  opacity: 1;
}

.mini-feedback-button {
  width: 24px;
  height: 24px;
  border: none;
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 0;
  margin: 0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.mini-feedback-button:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.2);
}

.mini-feedback-button.feedback-given {
  color: white;
  background-color: var(--color-emerald-green, #22AD85);
}

/* Input Area */
.mini-message-input-container {
  padding: 12px 16px;
  background-color: white;
  border-top: 1px solid var(--color-border, #e5e7eb);
  display: flex;
  gap: 8px;
  align-items: center;
  flex-shrink: 0;
}

[data-theme="dark"] .mini-message-input-container {
  background-color: var(--color-surface-elevated, #1e2538);
  border-top-color: var(--color-divider, #374151);
}

.mini-message-input {
  flex: 1;
  border: 1px solid var(--color-border, #e5e7eb);
  border-radius: 24px;
  padding: 8px 16px;
  font-size: 14px;
  outline: none;
  background-color: var(--color-bg-secondary, #f3f4f6);
  color: var(--color-primary, #1e2538);
}

[data-theme="dark"] .mini-message-input {
  border-color: var(--color-divider, #374151);
  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));
  color: var(--color-secondary, #d8dce7);
}

.mini-message-input:focus {
  border-color: var(--color-primary, #3b82f6);
}

.mini-send-button {
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.mini-send-button:hover {
  background-color: var(--color-dark-jungle-green, #0D211D);
}

.mini-send-button:disabled {
  background-color: var(--color-honeydew, #DFF4E9);
  cursor: not-allowed;
  opacity: 0.7;
}

[data-theme="dark"] .mini-send-button:disabled {
  background-color: var(--color-divider, #374151);
}

/* Add any missing styles needed for insights view */

/* Mini Insights Container */
.mini-insights-container {
  height: 100%;
  overflow: hidden;
}

/* Insights list */
.insights-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.insights-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0.5rem;
  max-height: 100%;
}

/* Empty state for insights */
.insights-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 1rem;
}

.insights-empty-state h3 {
  color: var(--color-brunswick-green, #23504A);
}

.insights-empty-icon {
  width: 64px;
  height: 64px;
  margin-bottom: 1rem;
}

.insights-empty-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Loading spinner */
.spinner-small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  display: inline-block;
  vertical-align: middle;
  margin-right: 0.25rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Make sure the loading state centers properly */
.flex.flex-col.items-center.justify-center.h-full.py-8 {
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Product Table Responsive Styles */
.overflow-x-auto {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 500px;
  border-radius: 8px;
  margin: 8px 0;
  scrollbar-width: thin;
  -ms-overflow-style: none;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
  width: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Responsive table styles */
.overflow-x-auto table {
  width: 100%;
  border-collapse: collapse;
}

.overflow-x-auto thead {
  position: sticky;
  top: 0;
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  z-index: 1;
}

.overflow-x-auto th {
  padding: 10px;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
}

.overflow-x-auto td {
  padding: 10px;
  border-bottom: 1px solid var(--color-border, #e5e7eb);
}

.overflow-x-auto tr:nth-child(even) {
  background-color: var(--color-honeydew, #DFF4E9);
}

[data-theme="dark"] .overflow-x-auto thead {
  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));
}

[data-theme="dark"] .overflow-x-auto tr:nth-child(even) {
  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.3));
}

/* Mobile and small screen optimizations for product table */
@media (max-width: 639px) {
  .overflow-x-auto {
    margin: 4px 0;
    max-height: 300px; /* Smaller height on mobile */
    border-radius: 6px;
  }
  
  .overflow-x-auto table {
    font-size: 0.75rem; /* Smaller font on mobile */
  }
  
  .overflow-x-auto th,
  .overflow-x-auto td {
    padding: 8px 6px;
  }
  
  /* Ensure the description doesn't make rows too tall */
  .overflow-x-auto .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100px;
  }
}

/* Updating agent colors with brand palette */
:root {
  --color-agent-0: var(--color-brunswick-green, #23504A);
  --color-agent-1: var(--color-pine-green, #00766D);
  --color-agent-2: var(--color-emerald-green, #22AD85);
  --color-agent-3: var(--color-dark-jungle-green, #0D211D);
  --color-agent-4: var(--color-brunswick-green, #23504A);
}

/* Message content wrapper */
.mini-message-content {
  width: 100%;
  word-break: break-word;
}

.mini-message-content p {
  margin: 0;
}

.mini-message-content p:first-child {
  margin-top: 0;
}

.mini-message-content p:last-child {
  margin-bottom: 0;
}

.mini-user-message {
  background-color: #E1F5EE;
  color: var(--color-dark-jungle-green, #0D211D);
  border-top-right-radius: 4px;
  align-self: flex-end;
  max-width: 100%;
}

/* Mobile and Tablet Responsive Styles */
@media (max-width: 767px) {
  .mini-chat-container,
  .mini-insights-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .mini-chat-messages {
    max-height: calc(100% - 120px);
    padding: 12px;
    gap: 12px;
  }
  
  .mini-chat-message-content {
    max-width: 85%;
  }
  
  .mini-message-input-container {
    padding: 10px 12px;
    position: sticky;
    bottom: 0;
  }
  
  .mini-smokey-image {
    height: 80px;
  }
  
  /* Make touch targets larger for mobile */
  .mini-feedback-button {
    width: 32px;
    height: 32px;
  }
  
  .mini-send-button {
    width: 44px;
    height: 44px;
  }
  
  .mini-message-input {
    padding: 10px 16px;
    font-size: 16px;
  }
  
  /* Improve mobile resize handle */
  .mini-chat-resize-handle {
    width: 100%;
    height: 20px;
    top: 0;
    bottom: auto;
    left: 0;
    right: 0;
    cursor: ns-resize;
  }
  
  .mini-chat-resize-handle::after {
    height: 4px;
    width: 50px;
  }
  
  /* Vertical drag button */
  .mini-chat-resize-handle::before {
    content: "≡";
    left: 50%;
    top: 0;
    margin-top: 4px;
    transform: translate(-50%, 0);
    rotate: 0deg;
  }
  
  .mini-chat-resize-handle:hover::before {
    opacity: 0.8;
    transform: translate(-50%, 0) scale(1.1);
  }
  
  .mini-chat-resize-handle:active::before {
    opacity: 1;
    transform: translate(-50%, 0) scale(1);
  }
}

/* Tablet specific styles */
@media (min-width: 768px) and (max-width: 1023px) {
  .mini-chat-container,
  .mini-insights-container {
    height: 100%;
  }
  
  .mini-chat-message-content {
    max-width: 80%;
  }
}

/* Handle different orientations */
@media (max-width: 767px) and (orientation: landscape) {
  .mini-chat-messages {
    max-height: calc(100% - 100px);
  }
  
  .mini-smokey-image {
    height: 60px;
  }
}

/* Insights Notification Styles */
.mini-insights-notifications {
  border-bottom: 1px solid var(--color-border, #e5e7eb);
  background-color: var(--color-surface-muted, #f9fafb);
  overflow: hidden;
}

[data-theme="dark"] .mini-insights-notifications {
  background-color: var(--color-surface-elevated, #2b3245);
  border-color: var(--color-divider, #374151);
}

.mini-insights-header {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  cursor: pointer;
  transition: background-color 0.15s ease;
}

.mini-insights-header:hover {
  background-color: var(--color-bg-hover, #f3f4f6);
}

[data-theme="dark"] .mini-insights-header:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.mini-insights-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 12px;
}

.mini-smokey-icon {
  width: 24px;
  height: 24px;
}

.mini-insights-title {
  flex: 1;
  font-weight: 500;
  font-size: 14px;
  color: var(--color-brunswick-green, #23504A);
}

[data-theme="dark"] .mini-insights-title {
  color: var(--color-primary, #6ee7b7);
}

.mini-insights-toggle {
  width: 20px;
  height: 20px;
  color: var(--color-text-muted, #6b7280);
}

.mini-insights-list {
  padding: 8px 16px 16px;
}

.mini-insights-item {
  margin-bottom: 8px;
}

.mini-insights-more {
  display: flex;
  justify-content: center;
  margin-top: 8px;
  margin-bottom: 12px;
}

.mini-insights-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.mini-insights-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  background-color: var(--color-surface-muted, #f9fafb);
  border-bottom: 1px solid var(--color-border, #e5e7eb);
  font-size: 14px;
  color: var(--color-text-muted, #6b7280);
}

[data-theme="dark"] .mini-insights-loading {
  background-color: var(--color-surface-elevated, #2b3245);
  border-color: var(--color-divider, #374151);
  color: var(--color-text-muted, #9ca3af);
}

.mini-insights-loading span {
  margin-left: 8px;
}

/* Enhanced formatting styles */
.highlight {
  background-color: rgba(255, 235, 59, 0.2);
  padding: 0.5rem;
  border-left: 3px solid #ffc107;
  margin: 1rem 0;
}

.mini-chat-message details {
  margin: 1rem 0;
}

.mini-chat-message summary {
  font-weight: bold;
  cursor: pointer;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.mini-chat-message summary:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.mini-chat-message .checklist {
  list-style-type: none;
  padding-left: 0.5rem;
}

.mini-chat-message .checklist li {
  margin-bottom: 0.5rem;
}

.mini-chat-message table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
  font-size: 0.9rem;
}

.mini-chat-message th, 
.mini-chat-message td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.mini-chat-message th {
  background-color: rgba(0, 0, 0, 0.05);
  font-weight: bold;
}

.mini-chat-message tr:nth-child(even) {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Markdown improvements */
.mini-chat-message h2 {
  font-size: 1.4rem;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 0.25rem;
}

.mini-chat-message h3 {
  font-size: 1.2rem;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

.mini-chat-message blockquote {
  border-left: 4px solid #6B7280;
  padding-left: 1rem;
  margin-left: 0;
  color: #4B5563;
  font-style: italic;
}

.mini-chat-message hr {
  margin: 1.5rem 0;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.mini-chat-message code {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.9em;
}

.mini-chat-message pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 1rem;
  border-radius: 4px;
  overflow-x: auto;
}

.mini-chat-message pre code {
  background-color: transparent;
  padding: 0;
}

/* File upload styles */
.mini-attachment-button {
  background-color: transparent;
  color: var(--color-brunswick-green, #23504A);
  border: none;
  cursor: pointer;
  padding: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mini-attachment-button:hover {
  color: var(--color-emerald-green, #22AD85);
  transform: scale(1.1);
}

[data-theme="dark"] .mini-attachment-button {
  color: var(--color-emerald-green, #22AD85);
}

.mini-attachment-button:disabled {
  color: var(--color-text-muted, #9ca3af);
  cursor: not-allowed;
}

/* Attachments preview styles */
.attachments-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 8px;
  width: 100%;
  max-height: 100px;
  overflow-y: auto;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  margin-bottom: 8px;
}

[data-theme="dark"] .attachments-preview {
  background-color: rgba(255, 255, 255, 0.05);
}

.attachment-preview-item {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  max-width: 180px;
  position: relative;
}

[data-theme="dark"] .attachment-preview-item {
  background-color: var(--color-surface-muted, rgba(43, 50, 69, 0.5));
}

.attachment-preview-icon {
  margin-right: 6px;
  color: var(--color-brunswick-green, #23504A);
  flex-shrink: 0;
}

.attachment-preview-image {
  width: 24px;
  height: 24px;
  border-radius: 2px;
  overflow: hidden;
}

.attachment-preview-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.attachment-preview-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.attachment-remove-btn {
  background: none;
  border: none;
  color: var(--color-text-muted, #9ca3af);
  cursor: pointer;
  padding: 2px;
  margin-left: 6px;
  font-size: 12px;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.attachment-remove-btn:hover {
  opacity: 1;
  color: #f87171;
}

/* Upload progress bar */
.upload-progress {
  width: 100%;
  height: 4px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  margin-bottom: 8px;
  position: relative;
}

.upload-progress-bar {
  height: 100%;
  background-color: var(--color-brunswick-green, #23504A);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.upload-progress-text {
  position: absolute;
  top: -18px;
  right: 0;
  font-size: 10px;
  color: var(--color-text-muted, #9ca3af);
}

/* Message attachments display styles */
.message-attachments {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 8px;
  width: 100%;
}

.attachment-item {
  display: flex;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
}

[data-theme="dark"] .attachment-item {
  background-color: rgba(255, 255, 255, 0.05);
}

.attachment-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .attachment-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.attachment-image {
  width: 100%;
  max-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.attachment-image img {
  max-width: 100%;
  max-height: 192px;
  object-fit: contain;
  border-radius: 4px;
}

.attachment-file {
  display: flex;
  padding: 12px;
  width: 100%;
  align-items: center;
}

.attachment-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  background-color: var(--color-brunswick-green, #23504A);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
}

.attachment-info {
  flex: 1;
  overflow: hidden;
}

.attachment-name {
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attachment-size {
  font-size: 12px;
  color: var(--color-text-muted, #9ca3af);
}

/* Responsive attachment styles */
@media (max-width: 767px) {
  .attachments-preview {
    max-height: 80px;
  }
  
  .attachment-preview-item {
    max-width: 140px;
    padding: 3px 6px;
  }
  
  .attachment-preview-name {
    max-width: 80px;
  }
  
  .attachment-image {
    max-height: 160px;
  }
  
  .attachment-image img {
    max-height: 152px;
  }
}

/* Agent selection information for mini chat */
.mini-agent-selection-info {
  font-size: 0.7rem;
  color: #6b7280;
  font-weight: normal;
  margin-left: 6px;
}

.mini-agent-role {
  font-size: 0.7rem;
  color: #9ca3af;
  font-weight: normal;
  margin-left: 4px;
}

/* Enhanced message header for better agent visibility */
.mini-message-header.bot-header {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 3px;
  margin-bottom: 3px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #374151;
}
