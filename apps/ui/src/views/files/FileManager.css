/* FileManager.css */
.file-list {
  margin-top: 20px;
  width: 100%;
  overflow-x: auto;
}

.file-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.file-table th {
  text-align: left;
  padding: 12px 15px;
  background-color: var(--color-background-secondary);
  border-bottom: 1px solid var(--color-border);
  font-weight: 600;
}

.file-table td {
  padding: 12px 15px;
  border-bottom: 1px solid var(--color-border);
}

.file-name-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-type-icon {
  width: 20px;
  height: 20px;
  margin-right: 6px;
}

.actions-cell {
  display: flex;
  gap: 8px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

/* Status badge colors */
.status-pending {
  background-color: #fef9c3;
  color: #854d0e;
}

.status-processing {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-completed {
  background-color: #dcfce7;
  color: #166534;
}

.status-failed {
  background-color: #fee2e2;
  color: #b91c1c;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--color-text-secondary);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-state-content {
  max-width: 500px;
}

.empty-state-icon {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;
}

.empty-state-icon svg {
  width: 60px;
  height: 60px;
  color: var(--color-text-tertiary);
}

.empty-state-title {
  font-size: 1.5rem;
  margin-bottom: 12px;
  color: var(--color-text-primary);
}

.empty-state-description {
  color: var(--color-text-secondary);
  margin-bottom: 24px;
}

.upload-modal-content {
  padding: 20px 0;
}

.upload-instructions {
  margin-bottom: 20px;
  color: var(--color-text-secondary);
}

.upload-dropzone {
  margin-bottom: 20px;
}

.file-input {
  display: none;
}

.dropzone {
  border: 2px dashed var(--color-border);
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.dropzone:hover {
  border-color: var(--color-primary);
  background-color: var(--color-background-hover);
}

.dropzone svg {
  width: 40px;
  height: 40px;
  margin-bottom: 16px;
  color: var(--color-text-tertiary);
  display: block;
}

.dropzone p {
  margin-bottom: 8px;
  color: var(--color-text-primary);
}

.file-types {
  display: block;
  font-size: 0.85rem;
  color: var(--color-text-tertiary);
}

.selected-file {
  margin: 16px 0;
  padding: 12px 16px;
  background-color: var(--color-background-secondary);
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.selected-file span {
  font-weight: 500;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.delete-modal-content {
  padding: 10px 0;
}

.delete-file-info {
  margin: 16px 0;
  padding: 12px 16px;
  background-color: var(--color-background-secondary);
  border-radius: 6px;
}

/* Add a spinner for processing status */
.status-spinner {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin-right: 6px;
  border: 2px solid rgba(30, 64, 175, 0.3);
  border-radius: 50%;
  border-top-color: #1e40af;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Add styling for the polling indicator */
.polling-indicator {
  font-size: 0.8rem;
  color: var(--color-text-tertiary);
  margin-bottom: 10px;
  text-align: right;
  font-style: italic;
}

/* Add styling for the error details button */
.error-details-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: none;
  border: none;
  color: inherit;
  font-size: 12px;
  margin-left: 6px;
  cursor: pointer;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.error-details-button:hover {
  opacity: 1;
}

/* Add styles for the analysis modal */
.analysis-modal-content {
  padding: 20px;
}

.analysis-modal-content h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
}

.analysis-content {
  margin-top: 20px;
}

.analysis-content h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
}

.analysis-json {
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  padding: 15px;
  overflow: auto;
  max-height: 400px;
  font-family: monospace;
  white-space: pre-wrap;
  font-size: 13px;
  line-height: 1.4;
}

/* Enhanced analysis modal styles */
.analysis-section {
  margin-bottom: 24px;
}

.analysis-section h5 {
  font-size: 16px;
  margin-bottom: 10px;
  color: var(--color-text-primary);
  font-weight: 600;
}

.analysis-text {
  white-space: pre-wrap;
  line-height: 1.5;
  margin-bottom: 10px;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e1e4e8;
}

.agent-contribution {
  padding: 12px;
  border: 1px solid #e1e4e8;
  border-radius: 6px;
  margin-bottom: 12px;
}

.agent-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e1e4e8;
  font-size: 14px;
}

.relevance-score {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.key-insights {
  margin-top: 8px;
}

.insight {
  white-space: pre-wrap;
  line-height: 1.5;
  margin-bottom: 8px;
  font-size: 14px;
}

.completion-percentage {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-primary);
} 

/* Vectorization Status Styles */
.vectorization-content {
  margin-top: 16px;
}

.vectorization-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: var(--color-background-secondary);
  border-radius: 6px;
  font-size: 0.9rem;
}

.stat-item strong {
  color: var(--color-text-primary);
}

.vectorization-status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
}

.vectorization-status.status-completed {
  background-color: #dcfce7;
  color: #166534;
}

.vectorization-status.status-partial {
  background-color: #fef9c3;
  color: #854d0e;
}

.vectorization-status.status-failed {
  background-color: #fee2e2;
  color: #b91c1c;
}

.failed-count {
  color: #b91c1c;
  font-weight: 600;
}

.vectorization-progress {
  margin-top: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background-color: var(--color-border);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: var(--color-primary);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  white-space: nowrap;
}

.vectorization-error {
  margin-top: 16px;
  padding: 16px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
}

.error-message {
  color: #b91c1c;
  margin: 0 0 8px 0;
  line-height: 1.5;
}

.error-time {
  color: #6b7280;
  margin: 0;
  font-size: 0.9rem;
}

.vectorization-pending {
  margin-top: 16px;
  padding: 16px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.vectorization-pending p {
  color: #6b7280;
  margin: 0;
  line-height: 1.5;
}