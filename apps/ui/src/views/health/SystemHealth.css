/* System Health Page Styles */

/* Tabs */
.health-tabs {
  display: flex;
  border-bottom: 1px solid var(--color-divider);
  margin-bottom: 1.5rem;
}

.health-tab {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--color-text-secondary);
}

.health-tab:hover {
  color: var(--color-text);
}

.health-tab.active {
  color: var(--color-primary);
  border-bottom-color: var(--color-primary);
}

/* Content section */
.health-content {
  margin-bottom: 2rem;
}

/* Summary Cards */
.health-summary {
  margin-bottom: 1.5rem;
}

.summary-card {
  background-color: var(--color-background-primary);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.summary-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.summary-timestamp {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  font-weight: 500;
}

.summary-stats {
  display: flex;
  justify-content: space-around;
  gap: 1rem;
}

.health-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border-radius: 0.5rem;
  min-width: 120px;
}

.health-stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.health-stat-label {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

.stat-healthy {
  background-color: rgba(var(--color-success-rgb), 0.1);
  color: var(--color-success);
}

.stat-warning {
  background-color: rgba(var(--color-warning-rgb), 0.1);
  color: var(--color-warning);
}

.stat-error {
  background-color: rgba(var(--color-error-rgb), 0.1);
  color: var(--color-error);
}

/* Agent health cards */
.agents-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.health-agent-card {
  background-color: var(--color-surface);
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-left: 4px solid transparent;
}

.health-agent-card.available {
  border-left-color: var(--color-success);
}

.health-agent-card.partial {
  border-left-color: var(--color-warning);
}

.health-agent-card.unavailable {
  border-left-color: var(--color-error);
}

.health-agent-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.health-agent-header:hover {
  background-color: rgba(var(--color-primary-rgb), 0.05);
}

.health-agent-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.health-agent-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  background-color: rgba(var(--color-primary-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.health-agent-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.health-agent-details {
  flex: 1;
}

.health-agent-details h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.health-agent-status-bar {
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 250px;
}

.progress-bar {
  height: 0.5rem;
  background-color: var(--color-surface-muted);
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 0.25rem;
}

.progress-fill {
  height: 100%;
  border-radius: 1rem;
  background-color: var(--color-primary);
  transition: width 0.3s ease;
}

.percentage {
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

.health-agent-status-icon {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.health-status-icon {
  width: 1.5rem;
  height: 1.5rem;
}

.health-status-healthy {
  color: var(--color-success);
}

.health-status-warning {
  color: var(--color-warning);
}

.health-status-error {
  color: var(--color-error);
}

.health-status-unknown {
  color: var(--color-text-secondary);
}

.chevron {
  transition: transform 0.3s ease;
}

.chevron.expanded {
  transform: rotate(90deg);
}

/* Agent expanded details */
.health-agent-details-expanded {
  padding: 0 1.5rem 1.5rem;
  border-top: 1px solid var(--color-divider);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Agent description */
.health-agent-description {
  margin-bottom: 1rem;
  margin-top: 1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--color-text-secondary);
  padding: 0.75rem;
  background-color: var(--color-surface-elevated);
  border-radius: 0.5rem;
}

/* Agent capabilities */
.health-agent-capabilities {
  margin-bottom: 1rem;
}

.health-agent-capabilities h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
}

.capabilities-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.capability-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: rgba(var(--color-primary-rgb), 0.05);
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
}

.capability-icon {
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Requirements list */
.requirements-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.requirements-list h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
}

.requirement-item {
  display: flex;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background-color: var(--color-surface-elevated);
}

.requirement-item.met {
  background-color: rgba(var(--color-success-rgb), 0.05);
}

.requirement-item.unmet {
  background-color: rgba(var(--color-error-rgb), 0.05);
}

.requirement-details {
  flex: 1;
}

.requirement-name {
  font-weight: 500;
  font-size: 0.875rem;
  text-transform: capitalize;
  display: block;
  margin-bottom: 0.25rem;
}

.requirement-progress {
  display: block;
  font-size: 0.75rem;
  color: var(--color-text-secondary);
  margin-bottom: 0.25rem;
}

.missing-columns, .missing-types {
  font-size: 0.75rem;
  color: var(--color-error);
  margin-top: 0.25rem;
}

/* System grid */
.system-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.system-card {
  display: flex;
  background-color: var(--color-background-primary);
  border-radius: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  border-left: 4px solid transparent;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.system-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.system-card.healthy {
  border-left-color: var(--color-success);
}

.system-card.warning {
  border-left-color: var(--color-warning);
}

.system-card.error {
  border-left-color: var(--color-error);
}

.system-icon {
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background-color: rgba(var(--color-primary-rgb), 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.25rem;
  flex-shrink: 0;
  color: var(--color-primary);
  transition: all 0.3s ease;
}

.system-icon-healthy {
  background-color: rgba(var(--color-success-rgb), 0.15);
  color: var(--color-success);
}

.system-icon-warning {
  background-color: rgba(var(--color-warning-rgb), 0.15);
  color: var(--color-warning);
}

.system-icon-error {
  background-color: rgba(var(--color-error-rgb), 0.15);
  color: var(--color-error);
}

.system-details {
  flex: 1;
}

.system-details h3 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.system-metrics {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.metric-label {
  font-weight: 500;
}

.metric-value {
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Status icon size adjustment */
.metric .health-status-icon {
  width: 1.25rem;
  height: 1.25rem;
}

/* Data sources */
.data-health-container {
  padding: 1rem 0;
}

.data-sources-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.data-sources-list h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.data-source-card {
  background-color: var(--color-surface);
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.data-source-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.data-source-header h4 {
  font-size: 1.125rem;
  font-weight: 600;
}

.data-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.data-stat {
  display: flex;
  flex-direction: column;
}

.data-stat-label {
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
}

.data-stat-value {
  font-size: 1.125rem;
  font-weight: 600;
}

.data-issues {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--color-divider);
}

.issue-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .summary-stats {
    flex-direction: column;
  }
  
  .system-grid {
    grid-template-columns: 1fr;
  }
  
  .data-stats {
    grid-template-columns: 1fr;
  }
}

/* Vector Data Health Styles */
.vector-data-health {
  margin-bottom: 2rem;
  background-color: var(--color-background-secondary);
  border-radius: 0.5rem;
  padding: 1.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.refresh-button {
  background-color: var(--color-primary-lighter);
  color: var(--color-primary);
  border: none;
  border-radius: 0.25rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.refresh-button:hover {
  background-color: var(--color-primary-lightest);
}

.refresh-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.health-summary-card {
  background-color: var(--color-background-primary);
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.health-overall-status {
  display: flex;
  align-items: center;
  font-weight: 600;
}

.health-overall-status.healthy {
  color: var(--color-success);
}

.health-overall-status.warning {
  color: var(--color-warning);
}

.health-overall-status.error {
  color: var(--color-error);
}

.health-overall-status .health-status-icon {
  margin-right: 0.5rem;
}

.last-checked {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

.vector-services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.service-card {
  background-color: var(--color-background-primary);
  border-radius: 0.375rem;
  padding: 1rem;
  border-left: 4px solid transparent;
}

.service-card.healthy {
  border-left-color: var(--color-success);
}

.service-card.warning {
  border-left-color: var(--color-warning);
}

.service-card.error {
  border-left-color: var(--color-error);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.service-header h4 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.service-details {
  margin-bottom: 0.75rem;
}

.service-metric {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.service-metric-label {
  color: var(--color-text-secondary);
}

.service-metric-value {
  font-weight: 500;
}

.service-message {
  background-color: var(--color-background-secondary);
  border-radius: 0.25rem;
  padding: 0.75rem;
  font-size: 0.875rem;
  color: var(--color-text-secondary);
}

.health-error-message {
  background-color: var(--color-error-lightest);
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  color: var(--color-error);
}

.health-error-message .health-status-icon {
  margin-right: 0.75rem;
}

.health-error-message span {
  flex: 1;
}

.health-error-message .refresh-button {
  margin-left: 1rem;
  background-color: var(--color-error-lighter);
  color: var(--color-error);
}

.health-error-message .refresh-button:hover {
  background-color: var(--color-error-light);
} 