/* Empty Insights State */
.empty-insights {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.empty-insights-content {
  text-align: center;
  max-width: 800px;
  padding: 0 20px;
}

.empty-insights-icon {
  margin-bottom: 20px;
}

.empty-insights-icon img {
  width: 120px;
}

.empty-circle {
  stroke: var(--color-primary-soft);
  stroke-width: 2;
  fill: var(--color-success-translucent);
}

.empty-insights-title {
  font-size: 28px;
  margin-bottom: 12px;
  color: var(--color-text);
}

.empty-insights-description {
  margin-bottom: 32px;
  color: var(--color-text-secondary);
  font-size: 16px;
  line-height: 1.6;
}

/* System Explanation */
.system-explanation {
  background: var(--color-background-subtle);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
  border: 1px solid var(--color-border);
}

.system-explanation-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--color-text);
}

.system-explanation-description {
  color: var(--color-text-secondary);
  margin-bottom: 20px;
  line-height: 1.6;
}

/* Agents Grid */
.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.agent-card {
  display: flex;
  align-items: center;
  background: var(--color-surface-elevated);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s;
}

.agent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.agent-icon {
  font-size: 28px;
  margin-right: 12px;
}

.agent-info {
  flex: 1;
}

.agent-name {
  font-weight: 600;
  font-size: 14px;
  margin: 0;
  color: var(--color-text);
}

.agent-role {
  font-size: 12px;
  color: var(--color-text-subtle);
  margin: 0;
}

/* Model Selector */
.model-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  width: 100%;
  max-width: 400px;
}

.model-selector label {
  font-weight: 600;
  color: var(--color-text);
  white-space: nowrap;
}

/* Generate Button */
.generate-insights-button {
  background: linear-gradient(90deg, var(--color-primary), var(--color-success));
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  color: var(--color-surface-elevated);
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.generate-insights-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px var(--color-primary-shadow);
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;
}

.loading-text {
  margin-top: 20px;
  font-size: 16px;
  color: var(--color-text-subtle);
}

.loading-agents {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.loading-agent {
  font-size: 32px;
  animation: bounce 1.4s ease-in-out infinite;
}

.loading-agent:nth-child(1) { animation-delay: 0s; }
.loading-agent:nth-child(2) { animation-delay: 0.2s; }
.loading-agent:nth-child(3) { animation-delay: 0.4s; }
.loading-agent:nth-child(4) { animation-delay: 0.6s; }

@keyframes bounce {
  0%, 80%, 100% { transform: scale(1) translateY(0); }
  40% { transform: scale(1.2) translateY(-8px); }
}

/* Insights Header */
.insights-header {
  margin-bottom: 24px;
}

.insights-header-text {
  font-size: 16px;
  color: var(--color-text-secondary);
}

/* Insights List */
.insights-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 20px;
  position: relative;
  justify-content: start;
  max-width: calc(100vw - 40px);
}

.insights-list > div {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Premium Container */
.insights-premium-container {
  position: relative;
  margin-top: 32px;
}

.premium-insights-blur {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  filter: blur(6px);
  opacity: 0.7;
  pointer-events: none;
  justify-content: start;
  max-width: calc(100vw - 40px);
}

.blurred-insight-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Remove old placeholder styles - keeping shimmer for potential future use */
.premium-placeholder-grid,
.premium-placeholder-card,
.placeholder-content,
.placeholder-header,
.placeholder-avatar,
.placeholder-text-lines,
.placeholder-body,
.placeholder-line {
  display: none;
}

/* Dark theme support */
[data-theme="dark"] .system-explanation {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--color-border);
}

[data-theme="dark"] .agent-card {
  background: var(--color-surface-elevated);
}

[data-theme="dark"] .agent-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .upgrade-overlay {
  background: rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .upgrade-content {
  background: var(--color-surface-elevated);
  color: var(--color-text);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .empty-insights {
    padding: 40px 16px;
  }
  
  .empty-insights-icon img {
    width: 80px;
  }
  
  .empty-insights-title {
    font-size: 24px;
  }
  
  .empty-insights-description {
    font-size: 14px;
  }
  
  .model-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .agents-grid {
    grid-template-columns: 1fr;
  }
  
  .system-explanation {
    padding: 16px;
  }
  
  .insights-list {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .premium-insights-blur {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

.upgrade-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  z-index: 10;
}

.upgrade-content {
  text-align: center;
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 90%;
  width: 400px;
}

.upgrade-content h3 {
  font-size: 20px;
  margin-bottom: 12px;
  color: var(--color-text);
}

.upgrade-content p {
  margin-bottom: 20px;
  color: var(--color-text-secondary);
}

.upgrade-button {
  width: 100%;
} 