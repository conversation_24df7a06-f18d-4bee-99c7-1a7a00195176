/* .app-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden; /* Prevent any scrolling at this level *
  position: fixed; /* Fix the entire layout to viewport *
  top: 0;
  left: 225px;
  right: 0;
  bottom: 0;
  z-index: 0; /* Ensure it's below other fixed elements *
} */


/* 
.app-layout-content {
  display: grid;
  grid-template-columns: 1fr auto; /* Middle content takes available space, sidebar fixed width *
  height: 100vh;
  overflow: hidden;
  position: relative;
} */

/* .app-layout-main {
  position: relative;
  overflow-y: auto; /* Only this area should scroll *
  overflow-x: hidden;
  padding: 0 1rem;
  height: 100%;
  width: 100%;
} */

/* Layout and transition styles */
.app-layout-container {
  display: flex;
  width: 100%;
  height: 100vh;
  overflow: hidden; /* Prevent scrolling on the main container */
  position: fixed; /* Fix the entire layout to viewport */
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.app-layout-nav {
  width: 12%;
  height: 100%;
  flex-shrink: 0;
  overflow-y: hidden; /* Prevent scrolling in nav */
}

.app-layout-middle {
  transition: width 0.3s ease;
  height: 100vh;
  overflow-y: auto; /* Allow scrolling in content */
  overflow-x: hidden;
}

.app-layout-middle.with-sidebar {
  width: 66%;
}

.app-layout-middle.full-width {
  width: 88%;
}

.app-layout-chat-sidebar {
  width: 20%;
  background-color: var(--color-surface);
  border-left: 1px solid var(--color-border);
  overflow: hidden; /* No scrolling in the sidebar container itself */
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100vh; /* Match viewport height */
  position: fixed; /* Fixed position to ensure it stays in place */
  top: 0;
  right: 0;
}

.app-layout-chat-sidebar.closed {
  width: 32px; /* Just enough for the toggle button */
  right: 0;
  z-index: 10; /* Ensure it stays above content when collapsed */
}

.app-layout-sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent scrolling at this level */
}

/* Content area inside the sidebar should be scrollable */
.app-layout-sidebar-content .mini-chat-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Sidebar toggle button */
.sidebar-toggle-button {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 11;
  width: 24px;
  height: 48px;
  border: none;
  background-color: var(--color-surface);
  color: var(--color-text-muted);
  cursor: pointer;
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  padding: 0;
}

/* Tabs in sidebar */
.sidebar-tabs {
  display: flex;
  border-bottom: 1px solid var(--color-border);
  flex-shrink: 0;
}

.sidebar-tab {
  flex: 1;
  text-align: center;
  padding: 10px;
  cursor: pointer;
  font-size: 14px;
  color: var(--color-text-muted);
  transition: all 0.2s ease;
}

.sidebar-tab:hover {
  background-color: var(--color-hover);
}

.sidebar-tab.active {
  color: var(--color-primary);
  border-bottom: 2px solid var(--color-primary);
}

/* Responsive behavior */
@media (max-width: 1280px) {
  .app-layout-chat-sidebar {
    width: 300px;
  }
  
  .app-layout-content {
    grid-template-columns: 1fr 300px;
  }
  
  .app-layout-content.sidebar-closed {
    grid-template-columns: 1fr 32px;
  }
}

@media (max-width: 768px) {
  .app-layout-chat-sidebar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 350px;
    border-left: none;
    border-top: 1px solid var(--color-border);
    grid-row: 2;
    overflow: hidden; /* Ensure no scrolling in the container itself */
  }
  
  .app-layout-chat-sidebar.closed {
    height: 32px;
    width: 100%;
  }
  
  .app-layout-content.sidebar-closed .app-layout-main {
    padding-bottom: 32px;
  }
  
  .sidebar-toggle-button {
    top: 0;
    left: 50%;
    transform: translateX(-50%) rotate(90deg);
    border-radius: 0 0 4px 4px;
  }
}

/* Base layout styles */
.app-layout {
  display: grid;
  grid-template-areas:
    "header header header"
    "sidebar main minichat";
  grid-template-columns: auto 1fr auto;
  grid-template-rows: auto 1fr;
  height: 100vh;
  overflow: hidden;
}

/* For screens smaller than 1440px, adjust the grid to not reserve space for mini-chat */
@media (max-width: 1439px) {
  .app-layout {
    grid-template-areas:
      "header header"
      "sidebar main";
    grid-template-columns: auto 1fr;
  }
}
.app-header-bg{
  background-color: var(--color-background);
}
/* Header */
.app-header {
  grid-area: header;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm) var(--space-md);
  background-color: var(--color-background);
  border-bottom: 1px solid var(--color-grey);
  z-index: var(--z-header);
  height: var(--header-height);
  position: relative;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.header-left, .header-right {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.header-logo {
  display: flex;
  align-items: center;
  margin-right: 1rem;
}

/* Updated text logo styles */
.text-logo {
  display: flex;
  align-items: center;
  position: relative;
}

.brand-name {
  color: var(--color-emerald-green); /* Using the emerald green from the theme */
  font-size: 1.25rem;
  font-weight: bold;
  letter-spacing: 0.5px;
  margin-left: 0.25rem;
}

.beta-tag {
  font-size: 0.6rem;
  color: var(--color-white);
  background-color: var(--color-red);
  padding: 2px 4px;
  border-radius: 3px;
  position: relative;
  top: -8px;
  margin-left: 4px;
  font-weight: bold;
  text-transform: uppercase;
}

/* Keep the existing image styles for backwards compatibility */
.header-logo img {
  height: 36px;
  max-width: 180px;
  object-fit: contain;
}

/* Modern sidebar toggle */
.sidebar-toggle-header.modern-toggle {
  background: transparent;
  border: none;
  color: var(--color-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.sidebar-toggle-header.modern-toggle:hover {
  background-color: var(--color-grey-soft);
  color: var(--color-emerald-green);
}

/* Header buttons */
.sidebar-toggle-header, .mini-chat-toggle-header {
  background: transparent;
  border: none;
  color: var(--color-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  padding: var(--space-xs) var(--space-sm);
  border-radius: 4px;
  transition: background-color var(--transition-fast);
}

.sidebar-toggle-header:hover, .mini-chat-toggle-header:hover {
  background-color: var(--color-grey);
}

.mini-chat-toggle-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-xs);
  border-radius: 8px;
  transition: all var(--transition-fast);
  position: relative;
  width: auto;
  height: 40px;
  background-color: transparent;
  border: none;
  cursor: pointer;
  gap: 8px;
  padding: 0 12px;
}

.mini-chat-toggle-header:hover {
  background-color: var(--color-grey-soft);
  transform: scale(1.05);
}

.mini-chat-toggle-header.active {
  background-color: rgba(0, 0, 0, 0.05);
}

.mini-chat-toggle-header.active:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.toggle-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-primary);
  display: inline-block;
}

.smokey-icon {
  height: 36px;
  margin-right: 0px;
  padding: 2px 0px;
  object-fit: contain;
  transition: transform 0.2s ease;
}

.mini-chat-toggle-header:hover .smokey-icon {
  transform: scale(1.05);
}

.header-icon {
  width: 24px;
  height: 24px;
  color: var(--color-red);
}

/* Sidebar Container */
.app-sidebar {
  grid-area: sidebar;
  position: relative;
  background-color: var(--color-background);
  width: var(--sidebar-width);
  overflow-y: auto; /* Allow scrolling for the content */
  transition: width var(--transition-normal);
  border-right: 1px solid var(--color-grey);
  height: calc(100vh - var(--header-height));
  z-index: var(--z-sidebar);
  display: flex;
  flex-direction: column; /* Enable vertical layout */
}

.app-sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

/* Main navigation area - takes available space */
.app-sidebar nav {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.app-sidebar.collapsed nav {
  padding: 20px 0;
  align-items: center;
}

/* Navigation links */
.app-sidebar nav a {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: var(--border-radius);
  margin-bottom: 5px;
  text-decoration: none;
  color: var(--color-primary);
  transition: background-color 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
}

.app-sidebar nav a:hover {
  background-color: var(--color-grey);
}

.app-sidebar nav a.selected {
  color: var(--color-on-primary);
  background: var(--color-primary);
}

/* Icon styling */
.app-sidebar nav a .nav-icon {
  min-width: 24px;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

/* Text styling */
.app-sidebar nav a span {
  transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.2s ease;
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

/* Collapsed state styles */
.app-sidebar.collapsed nav a {
  padding: 12px 0;
  width: 44px;
  justify-content: center;
  margin-bottom: 10px;
}

.app-sidebar.collapsed nav a .nav-icon {
  margin-right: 0;
}

.app-sidebar.collapsed nav a span {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  transform: translateX(10px);
}

/* Profile section at the bottom */
.sidebar-profile {
  border-top: 1px solid var(--color-grey);
  margin-top: auto; /* Push to the bottom */
  width: 100%;
  flex-shrink: 0;
}

.sidebar-profile-inner {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.sidebar-profile-inner:hover {
  background-color: var(--color-background-soft);
}

.profile-image {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.profile-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Profile text wrapper - may not exist in current HTML structure */
.profile-info {
  margin-left: 12px;
  overflow: hidden;
  flex-grow: 1;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

.profile-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
  margin-left: 12px;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

.profile-role {
  font-size: 12px;
  color: var(--color-primary-soft);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 8px;
  flex-shrink: 0;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

.profile-caret {
  margin-left: 8px;
  flex-shrink: 0;
  transition: opacity 0.2s ease, visibility 0.2s ease;
}

/* Collapsed state profile */
.app-sidebar.collapsed .sidebar-profile-inner {
  justify-content: center;
  padding: 15px 0;
}

.app-sidebar.collapsed .profile-name,
.app-sidebar.collapsed .profile-role,
.app-sidebar.collapsed .profile-caret,
.app-sidebar.collapsed .profile-info {
  opacity: 0;
  visibility: hidden;
  width: 0;
  margin: 0;
}

/* Main Content */
.app-main-content {
  grid-area: main;
  overflow-y: auto;
  padding: var(--space-lg);
  transition: margin-left var(--transition-normal), margin-right var(--transition-normal);
  height: calc(100vh - var(--header-height));
  margin-left: 0 !important;
  margin-right: 0 !important;
  width: 100%;
}

.app-main-content.with-header {
  height: calc(100vh - var(--header-height));
}

.app-main-content.sidebar-collapsed {
  margin-left: 0;
}

.app-main-content.chat-expanded {
  margin-right: 0;
}

.app-main-content.chat-collapsed {
  margin-right: 0;
}

/* Mini Chat */
.app-mini-chat {
  grid-area: minichat;
  background-color: white;
  transition: width 0.2s ease, transform 0.3s ease;
  height: calc(100vh - var(--header-height));
  overflow: hidden;
  border-left: 1px solid var(--color-border, #e5e7eb);
  position: relative;
  box-sizing: border-box;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: var(--header-height);
  right: 0;
  height: calc(100vh - var(--header-height));
  z-index: 99;
}

/* Visual cue animation for new users to show the panel is resizable */
.app-mini-chat.open.show-resize-hint::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  height: 50px;
  width: 3px;
  background-color: var(--color-brunswick-green, #23504A);
  transform: translateY(-50%);
  animation: resizeHint 2s ease-in-out;
  opacity: 0;
  pointer-events: none;
}

@keyframes resizeHint {
  0%, 100% { 
    opacity: 0;
    left: 0;
  }
  20%, 80% { 
    opacity: 0.7;
    left: -10px;
  }
  50% {
    opacity: 0.7;
    left: 0;
  }
}

/* Mobile version of the visual cue */
@media (max-width: 767px) {
  .app-mini-chat.open.show-resize-hint::before {
    left: 50%;
    top: 0;
    height: 3px;
    width: 50px;
    transform: translateX(-50%);
    animation: resizeHintMobile 2s ease-in-out;
  }
  
  @keyframes resizeHintMobile {
    0%, 100% { 
      opacity: 0;
      top: 0;
    }
    20%, 80% { 
      opacity: 0.7;
      top: -10px;
    }
    50% {
      opacity: 0.7;
      top: 0;
    }
  }
}

[data-theme="dark"] .app-mini-chat {
  background-color: var(--color-surface-elevated, #1e2538);
  border-left-color: var(--color-divider, #374151);
}

.app-mini-chat.open {
  width: 350px; /* Default width, will be overridden by inline style */
  transform: translateX(0);
}

.app-mini-chat.closed {
  width: 0;
  transform: translateX(100%);
  border-left: none;
  overflow: hidden;
}

.app-mini-chat-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100%;
}

/* Mini-chat tabs at top */
.mini-chat-tabs {
  display: flex;
  border-bottom: 1px solid var(--color-border, #e5e7eb);
  flex-shrink: 0;
}

.mini-chat-tab {
  flex: 1;
  text-align: center;
  padding: 10px;
  cursor: pointer;
  font-size: 14px;
  color: var(--color-text-muted, #6b7280);
  transition: all 0.2s ease;
}

.mini-chat-tab.active {
  color: var(--color-brunswick-green, #23504A);
  border-bottom: 2px solid var(--color-brunswick-green, #23504A);
  font-weight: 500;
}

.mini-chat-tab:hover:not(.active) {
  background-color: var(--color-honeydew, #DFF4E9);
  color: var(--color-brunswick-green, #23504A);
}

/* Mobile Hamburger Menu */
.mobile-menu-toggle {
  display: none;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: transparent;
  border: none;
  padding: 0;
  width: 40px;
  height: 40px;
}

.hamburger {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 20px;
  height: 16px;
}

.hamburger span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: var(--color-primary);
  border-radius: 2px;
  transition: all 0.3s ease;
}

.hamburger.active span:nth-child(1) {
  transform: translateY(7px) rotate(45deg);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: translateY(-7px) rotate(-45deg);
}

/* Mobile menu overlay */
.mobile-menu-overlay {
  position: fixed;
  top: var(--header-height);
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 20;
  display: none;
}

/* Header Location Picker */
.header-location-switcher.select-button {
  height: 40px;
  padding: 5px 12px;
  border-radius: var(--border-radius);
  border: 1px solid var(--color-grey);
  background-color: var(--color-background);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.header-location-switcher.select-button:hover {
  background-color: var(--color-grey-soft);
  border-color: var(--color-emerald-green);
}

.location-switcher-label {
  color: var(--color-primary-soft);
  font-size: 12px;
  line-height: 1;
  margin-bottom: 2px;
}

.location-switcher-value {
  color: var(--color-primary);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
}

/* Update the dropdown icon to make it more modern */
.header-location-switcher .dropdown-icon {
  margin-left: 8px;
  color: var(--color-emerald-green);
  transition: transform 0.2s ease;
}

.header-location-switcher:hover .dropdown-icon {
  transform: translateY(2px);
}

/* Mobile styles (767px and below) */
@media (max-width: 767px) {
  /* Layout changes for mobile */
  .app-layout {
    grid-template-areas:
      "header header"
      "main main";
    grid-template-columns: 1fr;
  }
  
  /* Mobile menu button */
  .mobile-menu-toggle {
    display: flex;
    margin-right: 10px;
  }
  
  .mobile-menu-overlay {
    display: block;
  }
  
  /* Smaller app title for mobile */
  .brand-name {
    font-size: 1rem;
    letter-spacing: 0.3px;
  }
  
  .beta-tag {
    font-size: 0.5rem;
    top: -6px;
    padding: 1px 3px;
  }
  
  /* Sidebar for mobile - hidden by default */
  .app-sidebar {
    position: fixed;
    left: 0;
    top: var(--header-height);
    bottom: 0;
    z-index: 50; /* Higher than mini-chat */
    transform: translateX(-100%);
    width: 100%;
    max-width: 300px;
    background-color: var(--color-background);
    border-right: 1px solid var(--color-grey);
    height: calc(100vh - var(--header-height));
    display: flex;
    flex-direction: column;
  }
  
  /* Mobile sidebar content */
  .app-sidebar nav {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
  }
  
  /* Keep profile at bottom */
  .sidebar-profile {
    margin-top: auto;
    border-top: 1px solid var(--color-grey);
  }
  
  /* Show sidebar when menu is open */
  .app-sidebar.mobile-open {
    transform: translateX(0);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.15);
  }
  
  /* Mobile sidebar location picker */
  .sidebar-location-picker {
    padding: 12px 20px;
    border-bottom: 1px solid var(--color-grey);
  }
  
  .sidebar-location-picker .header-location-switcher.select-button {
    width: 100%;
    max-width: 100%;
    margin-left: 0;
  }
  
  .mobile-location-switcher.select-button {
    border: 1px solid var(--color-grey);
    background-color: var(--color-background-soft);
    padding: 10px 15px;
    height: auto;
  }
  
  .mobile-location-switcher .location-switcher-label {
    font-size: 13px;
    opacity: 0.8;
  }
  
  .mobile-location-switcher .location-switcher-value {
    font-size: 16px;
    font-weight: 500;
    line-height: 1.3;
  }
  
  /* Header adjustments */
  .header-left {
    flex: 1;
  }
  
  .header-logo img {
    height: 30px;
    margin-left: 5px;
  }
  
  .app-header {
    padding: var(--space-xs) var(--space-sm);
  }
  
  .app-main-content {
    margin-left: 0 !important;
    margin-right: 0 !important;
    width: 100%;
    padding: var(--space-md);
  }
  
  /* Mini chat on mobile */
  .app-mini-chat {
    position: fixed;
    top: auto;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100% !important; /* Override inline style on mobile */
    height: 50vh; /* Half the screen height on mobile */
    max-height: 500px;
    border-top: 1px solid var(--color-border, #e5e7eb);
    border-left: none;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transform: translateY(0);
  }
  
  .app-mini-chat.closed {
    transform: translateY(100%);
  }
  
  .app-mini-chat.open {
    transform: translateY(0);
  }

  /* Move the resize handle to the top edge on mobile */
  .mini-chat-resize-handle {
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 20px;
    cursor: ns-resize;
  }

  .mini-chat-resize-handle::after {
    width: 50px;
    height: 4px;
    border-radius: 2px;
  }

  /* Responsive header styles */
  .header-location-switcher.select-button {
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .location-switcher-value {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Hide toggle text on mobile */
  .toggle-text {
    display: none;
  }
  
  .mini-chat-toggle-header {
    width: 40px;
    padding: var(--space-xs);
  }
}

/* Dark mode styles */
@media (prefers-color-scheme: dark) {
  .app-sidebar,
  .app-mini-chat, 
  .mini-chat-tabs {
    background-color: var(--color-background-dark, var(--color-background));
  }

  .hamburger span {
    background-color: var(--color-primary);
  }
}

/* Media query for screens smaller than 1440px - Overlay mini-chat */
@media (max-width: 1439px) {
  .app-mini-chat {
    position: fixed;
    right: 0;
    top: var(--header-height);
    width: var(--minichat-width);
    z-index: 45; /* Higher than main content but lower than mobile sidebar */
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  }
  
  /* Remove right margin adjustment from main content when mini-chat is open */
  .app-main-content.chat-expanded {
    margin-right: 0;
  }
  
  /* When mini-chat is open, let it overlay the main content */
  .app-mini-chat.open {
    transform: translateX(0);
  }
  
  .app-mini-chat.closed {
    transform: translateX(100%);
    box-shadow: none;
  }
}

/* New custom sidebar styles */
.custom-sidebar {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 10px 0;
}

.primary-links,
.standalone-links {
  padding: 0 12px;
}

/* Nav link styling */
.nav-link {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: 8px;
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.nav-link:hover {
  background-color: var(--color-hover-light);
}

.nav-link.active,
.nav-link.selected {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  font-weight: 500;
}

.nav-link .nav-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.nav-link.collapsed {
  justify-content: center;
  padding: 12px 8px;
}

.nav-link.collapsed .nav-icon {
  margin-right: 0;
}

/* Collapsible menu section styling */
.collapsible-menu-section {
  margin: 10px 0;
}

.collapsible-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin: 4px 12px;
  border-radius: 8px;
  cursor: pointer;
  color: var(--color-text-secondary);
  font-weight: 500;
  transition: background-color 0.2s ease;
  position: relative;
}

.collapsible-header:hover {
  background-color: var(--color-hover-light);
}

.collapsible-header .nav-icon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.collapsible-header.collapsed {
  justify-content: center;
  padding: 12px 8px;
}

.collapsible-header.collapsed .nav-icon {
  margin-right: 0;
}

.section-title {
  flex-grow: 1;
}

.toggle-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.collapsible-content {
  padding: 0 12px 0 24px;
}

.nav-link.sublink {
  padding: 10px 16px;
  font-size: 0.95em;
}

/* Mobile styles */
@media (max-width: 767px) {
  .collapsible-header {
    padding: 14px 16px;
  }
  
  .nav-link {
    padding: 14px 16px;
  }
  
  .collapsible-content {
    padding: 0 12px 0 20px;
  }
}

/* Insights Notification Styles */
.insights-notification {
  position: relative;
  margin-right: 8px;
}

.insights-bell-button {
  background: none;
  border: none;
  cursor: pointer;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.insights-bell-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .insights-bell-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.bell-icon {
  width: 22px;
  height: 22px;
  color: var(--color-brunswick-green, #23504A);
}

[data-theme="dark"] .bell-icon {
  color: var(--color-primary, #d1d5db);
}

.insights-count {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--color-emerald-green, #22AD85);
  color: white;
  font-size: 11px;
  font-weight: 600;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.insights-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 360px;
  max-width: 90vw;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
  margin-top: 8px;
}

[data-theme="dark"] .insights-dropdown {
  background-color: var(--color-surface-elevated, #1e2538);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.insights-dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border, #e5e7eb);
}

[data-theme="dark"] .insights-dropdown-header {
  border-bottom-color: var(--color-divider, #374151);
}

.insights-dropdown-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-brunswick-green, #23504A);
}

[data-theme="dark"] .insights-dropdown-header h3 {
  color: var(--color-primary, #e5e7eb);
}

.insights-dropdown-subtitle {
  margin: 2px 0 0 0;
  font-size: 12px;
  color: var(--color-text-muted, #6b7280);
}

[data-theme="dark"] .insights-dropdown-subtitle {
  color: var(--color-text-muted, #9ca3af);
}

.generate-insights-button {
  /* Keep these styles to adjust size */
  margin: 0 !important;
  padding: 4px 10px !important;
  font-size: 12px !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

/* Ensure the Button component's styling is preserved */
.generate-insights-button.btn-small {
  height: auto !important;
  min-height: unset !important;
}

.insights-dropdown-content {
  max-height: 400px;
  overflow-y: auto;
}

.insights-list-compact {
  padding: 0px 0px;
}

.insights-list-compact .insight-item {
  margin-bottom: 0px;
}

.insights-loading,
.no-insights {
  padding: 20px;
  text-align: center;
  color: var(--color-text-muted, #6b7280);
}

[data-theme="dark"] .insights-loading,
[data-theme="dark"] .no-insights {
  color: var(--color-text-muted, #9ca3af);
}

.insights-loading p,
.no-insights p {
  margin: 6px 0;
  font-size: 13px;
}

.no-insights-description {
  color: var(--color-text-muted);
  font-size: 14px;
  margin-top: 8px;
}

/* Loading agents animation */
.loading-agents {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-top: 12px;
}

.loading-agent {
  font-size: 24px;
  animation: bounce 1.4s ease-in-out infinite;
}

.loading-agent:nth-child(1) { animation-delay: 0s; }
.loading-agent:nth-child(2) { animation-delay: 0.2s; }
.loading-agent:nth-child(3) { animation-delay: 0.4s; }

@keyframes bounce {
  0%, 80%, 100% { transform: scale(1) translateY(0); }
  40% { transform: scale(1.1) translateY(-6px); }
}

/* Mini agents preview */
.mini-agents-preview {
  display: flex;
  gap: 6px;
  justify-content: center;
  margin: 12px 0 16px 0;
}

.mini-agent-icon {
  font-size: 20px;
  opacity: 0.8;
  transition: opacity 0.2s, transform 0.2s;
  cursor: help;
}

.mini-agent-icon:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Dropdown generate button */
.dropdown-generate-button {
  margin: 0 auto !important;
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
}

/* Insights dropdown footer styling */
.insights-dropdown-footer {
  padding: 10px 15px;
  border-top: 1px solid var(--color-border);
  text-align: center;
}

.view-all-insights-link {
  font-size: 14px;
  color: var(--color-primary);
  text-decoration: none;
  display: block;
  transition: opacity 0.2s ease;
}

.view-all-insights-link:hover {
  opacity: 0.8;
  text-decoration: underline;
}

[data-theme="dark"] .insights-dropdown-footer {
  border-top: 1px solid var(--color-border-dark, #333);
}

/* Mobile styles for insights dropdown */
@media (max-width: 767px) {
  .insights-dropdown {
    width: calc(100vw - 24px);
    max-width: 360px;
    right: -12px; /* Adjust position to align properly */
  }

  .insights-bell-button {
    height: 42px;
    width: 42px;
  }

  .bell-icon {
    width: 24px;
    height: 24px;
  }

  .compact-insight-title {
    max-width: 260px;
  }

  .compact-insight-description {
    max-width: 260px;
  }
}

/* Ensure the dropdown has a good max height on smaller screens */
@media (max-height: 700px) {
  .insights-dropdown-content {
    max-height: 300px;
  }
}

/* Processing indicator in header */
.processing-indicator {
  position: relative;
  margin-right: 12px;
}

.processing-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 16px;
  background-color: var(--color-surface-hover);
  color: var(--color-text);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.processing-button:hover {
  background-color: var(--color-surface-active);
}

.processing-spinner {
  animation: spin 1s linear infinite;
  color: var(--color-primary);
}

.processing-text {
  white-space: nowrap;
}

.processing-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 300px;
  max-width: 90vw;
  background-color: var(--color-surface);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 100;
  overflow: hidden;
}

.processing-dropdown-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.processing-dropdown-header h3 {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.processing-dropdown-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.processing-dropdown-content p {
  margin-bottom: 12px;
  font-size: 0.875rem;
  line-height: 1.5;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Job Status Notification Styles */
.job-status-notification {
  position: relative;
}

.job-status-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  margin-right: 8px;
}

.job-status-button:hover {
  background: var(--color-hover);
  border-color: var(--color-primary);
}

[data-theme="dark"] .job-status-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.job-status-icon {
  width: 16px;
  height: 16px;
  color: var(--color-text-muted);
}

.job-status-icon.processing {
  color: var(--color-primary);
  animation: spin 1s linear infinite;
}

.job-status-icon.completed {
  color: var(--color-success);
  font-weight: bold;
}

.job-status-icon.failed {
  color: var(--color-error);
  font-weight: bold;
}

[data-theme="dark"] .job-status-icon {
  color: rgba(255, 255, 255, 0.7);
}

.job-status-count {
  position: absolute;
  top: -4px;
  right: -4px;
  background: var(--color-primary);
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 10px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
  text-align: center;
}

.job-status-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 1000;
  min-width: 320px;
  max-width: 400px;
  max-height: 500px;
  overflow: hidden;
}

[data-theme="dark"] .job-status-dropdown {
  background: var(--color-surface-dark);
  border-color: var(--color-border-dark);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
}

.job-status-dropdown-header {
  padding: 16px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-background);
}

[data-theme="dark"] .job-status-dropdown-header {
  border-bottom-color: var(--color-border-dark);
  background: var(--color-background-dark);
}

.job-status-dropdown-header h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text);
}

[data-theme="dark"] .job-status-dropdown-header h3 {
  color: var(--color-text-dark);
}

.job-status-dropdown-subtitle {
  margin: 0;
  font-size: 12px;
  color: var(--color-text-muted);
}

[data-theme="dark"] .job-status-dropdown-subtitle {
  color: var(--color-text-muted-dark);
}

.job-status-dropdown-content {
  padding: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.job-status-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.job-status-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  transition: all 0.2s ease;
}

[data-theme="dark"] .job-status-item {
  background: var(--color-background-dark);
  border-color: var(--color-border-dark);
}

.job-status-item.processing {
  border-left: 3px solid var(--color-primary);
}

.job-status-item.completed {
  border-left: 3px solid var(--color-success);
}

.job-status-item.failed {
  border-left: 3px solid var(--color-error);
}

.job-status-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 2px;
}

.job-status-details {
  flex: 1;
  min-width: 0;
}

.job-status-name {
  font-weight: 500;
  color: var(--color-text);
  margin-bottom: 4px;
  font-size: 14px;
}

[data-theme="dark"] .job-status-name {
  color: var(--color-text-dark);
}

.job-status-time {
  font-size: 12px;
  color: var(--color-text-muted);
}

[data-theme="dark"] .job-status-time {
  color: var(--color-text-muted-dark);
}

.processing-text {
  color: var(--color-primary);
  font-weight: 500;
}

.status-text.completed {
  color: var(--color-success);
}

.status-text.failed {
  color: var(--color-error);
}

.no-jobs {
  text-align: center;
  padding: 20px;
  color: var(--color-text-muted);
}

[data-theme="dark"] .no-jobs {
  color: var(--color-text-muted-dark);
}

.job-status-summary {
  padding: 12px 16px;
  border-top: 1px solid var(--color-border);
  background: var(--color-background);
}

[data-theme="dark"] .job-status-summary {
  border-top-color: var(--color-border-dark);
  background: var(--color-background-dark);
}

.progress-bar-container {
  width: 100%;
  height: 4px;
  background: var(--color-border);
  border-radius: 2px;
  margin-bottom: 8px;
  overflow: hidden;
}

[data-theme="dark"] .progress-bar-container {
  background: var(--color-border-dark);
}

.progress-bar {
  height: 100%;
  background: var(--color-primary);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: var(--color-text-muted);
  margin: 0;
}

[data-theme="dark"] .progress-text {
  color: var(--color-text-muted-dark);
}

.failed-count {
  color: var(--color-error);
}

/* Schedule Call Button styles */
.schedule-call-button {
  display: flex;
  align-items: center;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  margin-right: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s ease;
}


.schedule-call-button .calendar-icon {
  margin-right: 6px;
  font-size: 16px;
}

.schedule-call-button .schedule-text {
  white-space: nowrap;
}

@media (max-width: 767px) {
  .job-status-dropdown {
    min-width: 280px;
    max-width: calc(100vw - 32px);
    right: -8px;
  }

  .job-status-button {
    padding: 6px;
    margin-right: 4px;
  }

  .job-status-icon {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 560px) {
  .schedule-call-button {
    padding: 4px 8px;
    font-size: 12px;
  }
  .schedule-call-button .button-icon svg {
    margin-right: 0px;
  }
  
  
  .schedule-call-button .button-text {
    display: none;
  }
  
  .schedule-call-button .calendar-icon {
    margin-right: 0;
  }
}

/* Premium feature badge */
.nav-link-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.premium-badge {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--color-accent, #f59e0b);
  color: white;
  font-size: 0.6rem;
  font-weight: 600;
  padding: 0.1rem 0.3rem;
  border-radius: 0.25rem;
  z-index: 1;
}

.premium-badge-small {
  position: absolute;
  right: 2px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.75rem;
  color: var(--color-accent, #f59e0b);
}

.premium-feature {
  color: var(--color-text-secondary, #6b7280);
  opacity: 0.8;
}

/* Upgrade CTA */
.upgrade-cta-section {
  margin: 1rem;
  padding: 1rem;
  background-color: rgba(245, 158, 11, 0.1);
  border-radius: 0.75rem;
  text-align: center;
}

.upgrade-button {
  width: 100%;
  padding: 0.5rem 1rem;
  font-weight: 600;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  background-color: var(--color-accent, #f59e0b);
  color: white;
  border: none;
  cursor: pointer;
}

.upgrade-text {
  margin: 0;
  font-size: 0.75rem;
  color: var(--color-text-secondary, #6b7280);
}

.upgrade-cta-collapsed {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 1rem 0;
  padding: 0.5rem;
  background-color: rgba(245, 158, 11, 0.1);
  border-radius: 0.5rem;
  cursor: pointer;
}

.upgrade-icon {
  font-size: 1.25rem;
  color: var(--color-accent, #f59e0b);
} 