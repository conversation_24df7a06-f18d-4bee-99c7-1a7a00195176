import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  Outlet,
  useMatch,
  useLocation,
  Link,
  useNavigate,
} from "react-router-dom";
import MiniChatPage from "../dashboard/MiniChatPage";
import "./AppLayout.css";
import { Translation, useTranslation } from "react-i18next";
import LocationSidebar from "../location/LocationSidebar";
import LocationPicker from "../location/LocationPicker";
import {
  CampaignsIcon,
  ChatIcon,
  DashboardIcon,
  InsightsIcon,
  JourneysIcon,
  PosIcon,
  ProductsIcon,
  SegmentationIcon,
  SettingsIcon,
  UsersIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  BellIcon,
  SpinnerIcon,
  OrdersIcon,
} from "../../ui/icons";
import { XIcon } from "../../ui/icons/index";
import smokeyIcon from "../../assets/smokey_icon.png";
import useValidateSubscription from "../../hooks/useValidateSubscription";
import { FiMenu, FiActivity, FiCalendar } from "react-icons/fi";
import api from "../../api";
import { Insight } from "../../types";
import InsightCard from "../../ui/InsightCard";
import Spinner from "../../ui/Spinner";
import { Button } from "../../ui";
import { AGENTS } from "../../utils/agents";

const HeartPulseIcon = () => <FiActivity className="icon" />;

const FileManagerIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className="icon"
  >
    <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"
    />
  </svg>
);

// New component for collapsible menu section
const CollapsibleMenuSection = ({
  title,
  icon,
  children,
  collapsed = false,
  isOpen,
  toggleOpen,
}: {
  title: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  collapsed: boolean;
  isOpen: boolean;
  toggleOpen: () => void;
}) => {
  return (
    <div className="collapsible-menu-section">
      <div
        className={`collapsible-header ${collapsed ? "collapsed" : ""}`}
        onClick={toggleOpen}
      >
        {icon && <div className="nav-icon">{icon}</div>}
        {!collapsed && (
          <>
            <span className="section-title">{title}</span>
            <div className="toggle-icon">
              {isOpen ? <ChevronDownIcon /> : <ChevronRightIcon />}
            </div>
          </>
        )}
      </div>
      {isOpen && !collapsed && (
        <div className="collapsible-content">{children}</div>
      )}
    </div>
  );
};

/**
 * AppLayout provides a responsive layout with:
 * - Persistent header with navigation controls
 * - Collapsible navigation sidebar
 * - Main content area
 * - Optional MiniChatPage with insights
 */
const AppLayout: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const [showSidebar, setShowSidebar] = useState(true);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [activeTab] = useState<"chat" | "insights">("chat");
  const [showMiniChat, setShowMiniChat] = useState(false);
  const isChatRoute = useMatch("/locations/:locationId/chat");
  const isSettingsRoute = useMatch("/locations/:locationId/settings/*");
  const [windowWidth, setWindowWidth] = useState(window.innerWidth);
  const [isHoveringNav, setIsHoveringNav] = useState(false);
  const [isExplicitlyToggled, setIsExplicitlyToggled] = useState(true);
  const [isDataHubOpen, setIsDataHubOpen] = useState(false);
  const {
    isSubscribed,
    isLoading,
    isPro,
    isTrialActive,
    trialEndDate,
    isArtificialTrial,
  } = useValidateSubscription();
  const [open, setOpen] = useState(false);

  // Mini chat resizing
  const [miniChatWidth, setMiniChatWidth] = useState(350); // Default width
  const minChatWidth = 300; // Minimum width
  const maxChatWidth = 600; // Maximum width

  // Mobile height resizing
  const [miniChatHeight, setMiniChatHeight] = useState(400); // Default height for mobile
  const minChatHeight = 200; // Minimum height
  const maxChatHeight = 600; // Maximum height

  const isDraggingRef = useRef(false);
  const startXRef = useRef(0);
  const startYRef = useRef(0);
  const startWidthRef = useRef(0);
  const startHeightRef = useRef(0);
  const resizeDirectionRef = useRef<"horizontal" | "vertical">("horizontal");

  // Insights state
  const [insights, setInsights] = useState<Insight[]>([]);
  const [insightsLoading, setInsightsLoading] = useState(false);
  const [showInsightsDropdown, setShowInsightsDropdown] = useState(false);
  const [generatingInsights, setGeneratingInsights] = useState(false);
  const [selectedModel, setSelectedModel] = useState("gpt-4.1-mini");

  // Track if resize hint has been shown
  const [showResizeHint, setShowResizeHint] = useState(false);

  // Processing state
  const [onboardingProcessing, setOnboardingProcessing] = useState(false);
  const [processingDetails, setProcessingDetails] = useState<any>(null);
  const [showProcessingDropdown, setShowProcessingDropdown] = useState(false);
  const [showJobStatusDropdown, setShowJobStatusDropdown] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Add state for checking if location has POS/product data
  const [hasNoData, setHasNoData] = useState(false);
  const [checkingData, setCheckingData] = useState(false);

  // Function to load insights
  const loadInsights = useCallback(async () => {
    if (!location?.pathname) return;
    const locationId = location.pathname.split("/")[2]; // Extract location ID from URL
    if (!locationId) return;

    setInsightsLoading(true);
    try {
      const data = await api.insights.get(parseInt(locationId));
      setInsights(data);
    } catch (error) {
      console.error("Error loading insights:", error);
    }
    setInsightsLoading(false);
  }, [location.pathname]); // Only depend on pathname, not the entire location object

  // Load insights when component mounts
  useEffect(() => {
    loadInsights();
  }, [loadInsights]);

  // Function to generate new insights
  const handleGenerateInsights = async () => {
    if (!location?.pathname) return;
    const locationId = location.pathname.split("/")[2]; // Extract location ID from URL
    if (!locationId) return;

    setGeneratingInsights(true);
    try {
      await api.insights.generate(parseInt(locationId), selectedModel);
      await loadInsights(); // Refresh the list after generation
    } catch (error) {
      console.error("Error generating insights:", error);
    }
    setGeneratingInsights(false);
  };

  // Toggle insights dropdown
  const toggleInsightsDropdown = () => {
    setShowInsightsDropdown(!showInsightsDropdown);
  };

  // Toggle sidebar visibility
  const toggleSidebar = () => {
    const newSidebarState = !showSidebar;
    setShowSidebar(newSidebarState);
    setIsExplicitlyToggled(true); // Always set this to true when explicitly toggled
  };

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setShowMobileMenu(!showMobileMenu);
  };

  // Toggle mini chat visibility
  const toggleMiniChat = () => {
    const newState = !showMiniChat;
    setShowMiniChat(newState);

    // Show resize hint animation when opening
    if (newState) {
      setShowResizeHint(true);
      // Reset the hint after animation completes
      setTimeout(() => {
        setShowResizeHint(false);
      }, 2000);
    }
  };

  // Toggle Data Hub section
  const toggleDataHub = () => {
    setIsDataHubOpen(!isDataHubOpen);
    setShowSidebar(true);
  };

  // Handle mouse enter on sidebar
  const handleMouseEnter = () => {
    if (!isMobile) {
      setIsHoveringNav(true);
    }
  };

  // Handle mouse leave on sidebar
  const handleMouseLeave = () => {
    if (!isMobile) {
      // Only reset hover state - the effective sidebar state depends on isExplicitlyToggled
      setIsHoveringNav(false);
    }
  };

  // Calculate effective sidebar state
  // If explicitly toggled, use the toggled state (showSidebar)
  // Otherwise, use the hover state (isHoveringNav)
  const effectiveShowSidebar = isExplicitlyToggled
    ? showSidebar
    : isHoveringNav;

  // Update window width state when resized
  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
      // Auto-close mobile menu on resize to larger screen
      if (window.innerWidth >= 768) {
        setShowMobileMenu(false);
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Update sidebar state in localStorage
  useEffect(() => {
    localStorage.setItem(
      "app_layout_show_sidebar",
      showSidebar ? "true" : "false"
    );

    if (!isLoading && isSubscribed && isPro) {
      setOpen(false);
    } else {
      setOpen(true);
    }
  }, [showSidebar, isSubscribed, isLoading, isPro]);

  // Update mini chat state in localStorage
  useEffect(() => {
    localStorage.setItem(
      "app_layout_show_mini_chat",
      showMiniChat ? "true" : "false"
    );
  }, [showMiniChat]);

  // Save Data Hub open state to localStorage
  useEffect(() => {
    localStorage.setItem(
      "app_layout_data_hub_open",
      isDataHubOpen ? "true" : "false"
    );
  }, [isDataHubOpen]);

  // Initialize from localStorage
  useEffect(() => {
    const savedSidebarState = localStorage.getItem("app_layout_show_sidebar");
    if (savedSidebarState !== null) {
      setShowSidebar(savedSidebarState === "true");
      setIsExplicitlyToggled(true); // Always start with explicit toggle to respect saved settings
    }

    const savedMiniChatState = localStorage.getItem(
      "app_layout_show_mini_chat"
    );
    if (savedMiniChatState !== null) {
      setShowMiniChat(savedMiniChatState === "true");
    }

    const savedDataHubState = localStorage.getItem("app_layout_data_hub_open");
    if (savedDataHubState !== null) {
      setIsDataHubOpen(savedDataHubState === "true");
    }
  }, []);

  // Close mobile menu when changing routes
  useEffect(() => {
    setShowMobileMenu(false);
    setShowJobStatusDropdown(false);
    setShowInsightsDropdown(false);
    setShowProcessingDropdown(false);
  }, [location.pathname]);

  // Handle clicking outside dropdowns to close them
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;

      // Close job status dropdown if clicking outside
      if (
        showJobStatusDropdown &&
        !target.closest(".job-status-notification")
      ) {
        setShowJobStatusDropdown(false);
      }

      // Close insights dropdown if clicking outside
      if (showInsightsDropdown && !target.closest(".insights-notification")) {
        setShowInsightsDropdown(false);
      }

      // Close processing dropdown if clicking outside
      if (showProcessingDropdown && !target.closest(".processing-indicator")) {
        setShowProcessingDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showJobStatusDropdown, showInsightsDropdown, showProcessingDropdown]);

  // Determine if we're on mobile
  const isMobile = windowWidth < 768;

  // Determine if mini chat should be shown
  const shouldShowMiniChat = !(isChatRoute || isSettingsRoute);

  // Close any open mobile menus when clicking overlay
  const handleOverlayClick = () => {
    setShowMobileMenu(false);
    setShowJobStatusDropdown(false);
    setShowInsightsDropdown(false);
    setShowProcessingDropdown(false);
    if (isMobile && showMiniChat) {
      setShowMiniChat(false);
    }
  };

  // Define interface for link items
  interface NavLinkItem {
    key: string;
    to: string;
    children: React.ReactNode;
    icon: React.ReactNode;
    minRole?: string;
    premiumFeature?: boolean;
  }

  // Primary navigation links
  const primaryLinks: NavLinkItem[] = [
    {
      key: "dashboard",
      to: "dashboard",
      children: <Translation>{(t) => t("dashboard")}</Translation>,
      icon: <DashboardIcon />,
    },
    {
      key: "chat",
      to: "chat",
      children: <Translation>{(t) => t("chat")}</Translation>,
      icon: <ChatIcon />,
    },
    {
      key: "insights",
      to: "insights",
      children: "Smokey Growth Engine",
      icon: <InsightsIcon />,
    },
    {
      key: "campaigns",
      to: "campaigns",
      children: <Translation>{(t) => t("campaigns")}</Translation>,
      icon: <CampaignsIcon />,
      minRole: "editor",
      premiumFeature: true,
    },
    {
      key: "journeys",
      to: "automations",
      children: <Translation>{(t) => t("automations")}</Translation>,
      icon: <JourneysIcon />,
      minRole: "editor",
      premiumFeature: true,
    },
    // {
    //   key: "health",
    //   to: "health",
    //   children: <Translation>{(t) => t("system_health")}</Translation>,
    //   icon: <HeartPulseIcon />,
    // },
  ];

  // Data Hub links
  const dataHubLinks: NavLinkItem[] = [
    {
      key: "products",
      to: "products",
      children: <Translation>{(t) => t("products")}</Translation>,
      icon: <ProductsIcon />,
    },
    {
      key: "orders",
      to: "orders",
      children: <Translation>{(t) => t("orders")}</Translation>,
      icon: <OrdersIcon />,
    },
    {
      key: "events",
      to: "events",
      children: <Translation>{(t) => t("events")}</Translation>,
      icon: <FiCalendar />,
    },
    {
      key: "competitors.title",
      to: "competitors",
      children: <Translation>{(t) => t("competitors.title")}</Translation>,
      icon: <InsightsIcon />,
    },
    {
      key: "files",
      to: "files",
      children: <Translation>{(t) => t("documents_and_files")}</Translation>,
      icon: <FileManagerIcon />,
    },
    {
      key: "sales-data",
      to: "sales-data",
      children: <Translation>{(t) => t("sales_data")}</Translation>,
      premiumFeature: true,
      icon: <PosIcon />,
    },
    {
      key: "users",
      to: "users",
      children: <Translation>{(t) => t("customers")}</Translation>,
      premiumFeature: true,
      icon: <UsersIcon />,
    },
    {
      key: "lists",
      to: "lists",
      children: <Translation>{(t) => t("customer_segments")}</Translation>,
      icon: <SegmentationIcon />,
      premiumFeature: true,
    },
    {
      key: "insight-patterns",
      to: "insight-patterns",
      children: "Insight Patterns",
      icon: <InsightsIcon />,
      minRole: "admin",
    },
  ];

  // Settings link
  const settingsLink: NavLinkItem = {
    key: "settings",
    to: "settings",
    children: <Translation>{(t) => t("settings")}</Translation>,
    icon: <SettingsIcon />,
    minRole: "admin",
  };

  // Custom renderer for sidebar links - inside the component to access isSubscribed
  const renderCustomSidebar = () => {
    const collapsed = !effectiveShowSidebar && !isMobile;

    return (
      <div className="custom-sidebar">
        {/* Primary Navigation Links */}
        <div className="primary-links">
          {primaryLinks.map((link) => (
            <div key={link.key} className="nav-link-wrapper">
              <Link
                key={link.key}
                to={link.to}
                className={`nav-link ${collapsed ? "collapsed" : ""} ${
                  link.premiumFeature && !isPro ? "premium-feature" : ""
                }`}
              >
                <div className="nav-icon">{link.icon}</div>
                {!collapsed && <div className="nav-text">{link.children}</div>}
              </Link>
              {link.premiumFeature && !isPro && !collapsed && (
                <span className="premium-badge" title={t("upgrade_required")}>
                  PRO
                </span>
              )}
              {link.premiumFeature && !isPro && collapsed && (
                <span
                  className="premium-badge-small"
                  title={t("upgrade_required")}
                >
                  ⭐
                </span>
              )}
            </div>
          ))}
        </div>

        {/* Data Hub Section */}
        <CollapsibleMenuSection
          title={t("data_hub")}
          icon={<PosIcon />}
          collapsed={collapsed}
          isOpen={isDataHubOpen}
          toggleOpen={toggleDataHub}
        >
          {dataHubLinks.map((link) => (
            <div key={link.key} className="nav-link-wrapper">
              <Link
                key={link.key}
                to={link.to}
                className={`nav-link sublink ${collapsed ? "collapsed" : ""} ${
                  link.premiumFeature && !isPro ? "premium-feature" : ""
                }`}
              >
                <div className="nav-icon">{link.icon}</div>
                {!collapsed && <div className="nav-text">{link.children}</div>}
              </Link>
              {link.premiumFeature && !isPro && !collapsed && (
                <span className="premium-badge" title={t("upgrade_required")}>
                  PRO
                </span>
              )}
              {link.premiumFeature && !isPro && collapsed && (
                <span
                  className="premium-badge-small"
                  title={t("upgrade_required")}
                >
                  ⭐
                </span>
              )}
            </div>
          ))}
        </CollapsibleMenuSection>

        {/* Settings Link */}
        <div className="standalone-links">
          <Link
            key={settingsLink.key}
            to={settingsLink.to}
            className={`nav-link ${collapsed ? "collapsed" : ""}`}
          >
            <div className="nav-icon">{settingsLink.icon}</div>
            {!collapsed && (
              <div className="nav-text">{settingsLink.children}</div>
            )}
          </Link>
        </div>
      </div>
    );
  };

  // Handle resize start
  const handleResizeStart = (
    e: React.MouseEvent<HTMLDivElement> | React.TouchEvent<HTMLDivElement>
  ) => {
    isDraggingRef.current = true;

    // Determine resize direction based on screen size
    resizeDirectionRef.current = isMobile ? "vertical" : "horizontal";

    // Set cursor style and prevent text selection
    document.body.style.cursor =
      resizeDirectionRef.current === "horizontal" ? "ew-resize" : "ns-resize";
    document.body.style.userSelect = "none";

    // Get the starting position
    if ("clientX" in e) {
      startXRef.current = e.clientX;
      startYRef.current = e.clientY;
    } else {
      startXRef.current = e.touches[0].clientX;
      startYRef.current = e.touches[0].clientY;
    }

    // Store starting dimensions
    startWidthRef.current = miniChatWidth;
    startHeightRef.current = miniChatHeight;

    // Add event listeners for dragging
    window.addEventListener("mousemove", handleResizeMove);
    window.addEventListener("touchmove", handleTouchMove);
    window.addEventListener("mouseup", handleResizeEnd);
    window.addEventListener("touchend", handleResizeEnd);
  };

  // Handle resize during drag
  const handleResizeMove = (e: MouseEvent) => {
    if (!isDraggingRef.current) return;

    if (resizeDirectionRef.current === "horizontal") {
      const deltaX = e.clientX - startXRef.current;
      // Invert the delta since we're dragging from right to left
      let newWidth = startWidthRef.current - deltaX;

      // Apply boundaries
      newWidth = Math.max(minChatWidth, Math.min(maxChatWidth, newWidth));

      setMiniChatWidth(newWidth);
    } else {
      const deltaY = e.clientY - startYRef.current;
      // Invert the delta for vertical dragging (drag up to increase height)
      let newHeight = startHeightRef.current - deltaY;

      // Apply boundaries
      newHeight = Math.max(minChatHeight, Math.min(maxChatHeight, newHeight));

      setMiniChatHeight(newHeight);
    }
  };

  // Handle touch move (for mobile)
  const handleTouchMove = (e: TouchEvent) => {
    if (!isDraggingRef.current) return;

    if (resizeDirectionRef.current === "horizontal") {
      const deltaX = e.touches[0].clientX - startXRef.current;
      // Invert the delta since we're dragging from right to left
      let newWidth = startWidthRef.current - deltaX;

      // Apply boundaries
      newWidth = Math.max(minChatWidth, Math.min(maxChatWidth, newWidth));

      setMiniChatWidth(newWidth);
    } else {
      const deltaY = e.touches[0].clientY - startYRef.current;
      // Invert the delta for vertical dragging
      let newHeight = startHeightRef.current - deltaY;

      // Apply boundaries
      newHeight = Math.max(minChatHeight, Math.min(maxChatHeight, newHeight));

      setMiniChatHeight(newHeight);
    }
  };

  // Handle resize end
  const handleResizeEnd = () => {
    isDraggingRef.current = false;
    document.body.style.cursor = "";
    document.body.style.userSelect = "";

    // Remove event listeners
    window.removeEventListener("mousemove", handleResizeMove);
    window.removeEventListener("touchmove", handleTouchMove);
    window.removeEventListener("mouseup", handleResizeEnd);
    window.removeEventListener("touchend", handleResizeEnd);

    // Save dimensions to localStorage
    localStorage.setItem("mini_chat_width", miniChatWidth.toString());
    localStorage.setItem("mini_chat_height", miniChatHeight.toString());
  };

  // Initialize dimensions from localStorage
  useEffect(() => {
    const savedWidth = localStorage.getItem("mini_chat_width");
    if (savedWidth) {
      const width = parseInt(savedWidth);
      if (!isNaN(width) && width >= minChatWidth && width <= maxChatWidth) {
        setMiniChatWidth(width);
      }
    }

    const savedHeight = localStorage.getItem("mini_chat_height");
    if (savedHeight) {
      const height = parseInt(savedHeight);
      if (
        !isNaN(height) &&
        height >= minChatHeight &&
        height <= maxChatHeight
      ) {
        setMiniChatHeight(height);
      }
    }
  }, []);

  // Function to check onboarding status
  const checkOnboardingStatus = useCallback(async () => {
    try {
      if (!location?.pathname) return;
      const locationId = location.pathname.split("/")[2]; // Extract location ID from URL
      if (!locationId) return;

      const response = await api.locations.checkOnboardingStatus(locationId);

      if (locationId != response.location_id) return;

      if (response.is_processing || response.recently_created) {
        setOnboardingProcessing(true);
        setProcessingDetails(response.processing_details);
      } else {
        setOnboardingProcessing(false);
        setProcessingDetails(response.processing_details);

        // Clear the interval when processing is complete
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      }
    } catch (error) {
      console.error("Error checking onboarding status:", error);
    }
  }, [location.pathname]); // Only depend on pathname, not the entire location object

  // Check onboarding status when component mounts or location changes
  useEffect(() => {
    // Clear any existing interval first
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    // Only set up polling if we have a valid location ID
    const locationId = location.pathname.split("/")[2];
    if (!locationId) return;

    // Initial check
    checkOnboardingStatus();

    // Set up interval to check status every 10 seconds
    intervalRef.current = setInterval(checkOnboardingStatus, 10000);

    // Clean up interval on unmount or when location changes
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [location.pathname, checkOnboardingStatus]);

  // Toggle processing dropdown
  const toggleProcessingDropdown = () => {
    setShowProcessingDropdown(!showProcessingDropdown);
  };

  // Toggle job status dropdown
  const toggleJobStatusDropdown = () => {
    setShowJobStatusDropdown(!showJobStatusDropdown);
  };

  // Get relevant jobs (processing or recently completed within 24 hours)
  const getRelevantJobs = () => {
    if (!processingDetails?.jobs) return [];
    const now = new Date();
    const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    return processingDetails.jobs.filter((job: any) => {
      // Show if job is processing
      if (job.status === "processing") return true;

      // Show if job completed within last 24 hours
      if (job.status === "completed" && job.endTime) {
        const endTime = new Date(job.endTime);
        return endTime > twentyFourHoursAgo;
      }

      // Show if job failed within last 24 hours
      if (job.status === "failed" && job.endTime) {
        const endTime = new Date(job.endTime);
        return endTime > twentyFourHoursAgo;
      }

      return false;
    });
  };

  // Format job type for display
  const formatJobType = (jobType: string) => {
    const jobNames: Record<string, string> = {
      competitors_import: t("importing_competitors", "Importing Competitors"),
      supabase_product_import: t("importing_products", "Importing Products"),
      marketplace_sync: t("syncing_marketplace", "Syncing Marketplace"),
      pos_integration: t("pos_integration", "POS Integration"),
      pos_data_file_0: t("importing_pos_data", "Importing POS Data"),
      pos_data_file_1: t("importing_pos_data", "Importing POS Data"),
      pos_data_file_2: t("importing_pos_data", "Importing POS Data"),
      document_upload_0: t("uploading_documents", "Uploading Documents"),
      document_upload_1: t("uploading_documents", "Uploading Documents"),
      document_upload_2: t("uploading_documents", "Uploading Documents"),
    };

    // Handle AI enhancement jobs with dynamic product counts
    if (
      jobType.startsWith("ai_enhancement_") &&
      jobType.endsWith("_products")
    ) {
      const productCount = jobType.match(/ai_enhancement_(\d+)_products/)?.[1];
      if (productCount) {
        return t(
          "ai_enhancing_n_products",
          `AI Enhancing ${productCount} Products`,
          { count: parseInt(productCount) }
        );
      }
    }

    return (
      jobNames[jobType] ||
      jobType.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())
    );
  };

  // Get job status icon and color
  const getJobStatusIcon = (status: string) => {
    switch (status) {
      case "processing":
        return <SpinnerIcon className="job-status-icon processing" />;
      case "completed":
        return <span className="job-status-icon completed">✓</span>;
      case "failed":
        return <span className="job-status-icon failed">✗</span>;
      default:
        return null;
    }
  };

  // Check if location has POS or product data
  const checkLocationData = useCallback(async () => {
    if (!location?.pathname) return;

    const locationId = location.pathname.split("/")[2]; // Extract location ID from URL
    if (!locationId) return;

    setCheckingData(true);
    try {
      // Check for product data
      const productData = await api.products.search(parseInt(locationId), {
        limit: 1,
      });

      // Check for POS data
      const posData = await api.pos.search(parseInt(locationId), { limit: 1 });

      // Set hasNoData if both are empty
      setHasNoData(
        (productData?.results?.length === 0 || !productData?.results) &&
          (posData?.results?.length === 0 || !posData?.results)
      );
    } catch (error) {
      console.error("Error checking location data:", error);
      setHasNoData(true); // Assume no data on error
    }
    setCheckingData(false);
  }, [location.pathname]); // Only depend on pathname, not the entire location object

  // Check for data when location changes
  useEffect(() => {
    checkLocationData();
  }, [checkLocationData]);

  // Function to handle scheduling a call
  const handleScheduleCall = () => {
    window.open("https://calendly.com/baked-martez", "_blank");
  };

  return (
    <div className="app-layout">
      {/* App Header - Always visible */}
      <header className="app-header">
        <div className="header-left">
          {isMobile ? (
            <button
              className="mobile-menu-toggle"
              onClick={toggleMobileMenu}
              aria-label={showMobileMenu ? t("close_menu") : t("open_menu")}
            >
              <div className={`hamburger ${showMobileMenu ? "active" : ""}`}>
                <span></span>
                <span></span>
                <span></span>
              </div>
            </button>
          ) : (
            <button
              className="sidebar-toggle-header modern-toggle"
              onClick={toggleSidebar}
              aria-label={
                showSidebar ? t("collapse_sidebar") : t("expand_sidebar")
              }
            >
              <FiMenu size={20} />
            </button>
          )}
          <Link to="/" className="header-logo">
            <div className="text-logo">
              <span className="brand-name">BakedBot.AI</span>
              <span className="beta-tag">Beta</span>
            </div>
          </Link>
        </div>

        <div className="header-right">
          {!isMobile && <LocationPicker />}

          {/* Schedule Call Button */}
          {/* {hasNoData && ( */}
          <Button
            className="schedule-call-button"
            onClick={handleScheduleCall}
            icon={<FiCalendar />}
            aria-label={t("schedule_call")}
          >
            <span className="schedule-text">
              {t("schedule_onboarding_call")}
            </span>
          </Button>
          {/* )} */}

          {/* Job Status Indicator */}
          {getRelevantJobs().length > 0 && (
            <div className="job-status-notification">
              <button
                className="job-status-button"
                onClick={toggleJobStatusDropdown}
                aria-label={t("job_status")}
              >
                {processingDetails?.processing_jobs > 0 ? (
                  <SpinnerIcon className="job-status-icon processing" />
                ) : (
                  <FiActivity className="job-status-icon completed" />
                )}
                {getRelevantJobs().length > 0 && (
                  <span className="job-status-count">
                    {getRelevantJobs().length}
                  </span>
                )}
              </button>

              {/* Job Status Dropdown */}
              {showJobStatusDropdown && (
                <div className="job-status-dropdown">
                  <div className="job-status-dropdown-header">
                    <h3>{t("onboarding_status", "Onboarding Status")}</h3>
                    <p className="job-status-dropdown-subtitle">
                      {t(
                        "data_processing_progress",
                        "Data processing progress"
                      )}
                    </p>
                  </div>

                  <div className="job-status-dropdown-content">
                    {getRelevantJobs().length > 0 ? (
                      <div className="job-status-list">
                        {getRelevantJobs().map((job: any, index: number) => (
                          <div
                            key={index}
                            className={`job-status-item ${job.status}`}
                          >
                            <div className="job-status-icon-wrapper">
                              {getJobStatusIcon(job.status)}
                            </div>
                            <div className="job-status-details">
                              <div className="job-status-name">
                                {formatJobType(job.jobType)}
                              </div>
                              <div className="job-status-time">
                                {job.status === "processing" ? (
                                  <span className="processing-text">
                                    {t("processing_since", "Processing since")}{" "}
                                    {new Date(
                                      job.startTime
                                    ).toLocaleTimeString()}
                                  </span>
                                ) : (
                                  <span className={`status-text ${job.status}`}>
                                    {job.status === "completed"
                                      ? t("completed", "Completed")
                                      : t("failed", "Failed")}{" "}
                                    at{" "}
                                    {new Date(job.endTime).toLocaleTimeString()}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="no-jobs">
                        <p>{t("no_recent_jobs", "No recent jobs")}</p>
                      </div>
                    )}
                  </div>

                  {/* Progress Summary */}
                  {processingDetails && processingDetails.total_jobs > 0 && (
                    <div className="job-status-summary">
                      <div className="progress-bar-container">
                        <div
                          className="progress-bar"
                          style={{
                            width: `${Math.round(
                              (processingDetails.completed_jobs /
                                processingDetails.total_jobs) *
                                100
                            )}%`,
                          }}
                        ></div>
                      </div>
                      <p className="progress-text">
                        {processingDetails.completed_jobs} of{" "}
                        {processingDetails.total_jobs} tasks completed
                        {processingDetails.failed_jobs > 0 && (
                          <span className="failed-count">
                            {" "}
                            ({processingDetails.failed_jobs} failed)
                          </span>
                        )}
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Insights Notification Bell */}
          <div className="insights-notification">
            <button
              className="insights-bell-button"
              onClick={toggleInsightsDropdown}
              aria-label={t("insights_notifications")}
            >
              <BellIcon className="bell-icon" />
              {insights.length > 0 && (
                <span className="insights-count">{insights.length}</span>
              )}
            </button>

            {/* Insights Dropdown */}
            {showInsightsDropdown && (
              <div className="insights-dropdown">
                <div className="insights-dropdown-header">
                  <div>
                    <h3>{t("growth_opportunities", "Growth Opportunities")}</h3>
                    <p className="insights-dropdown-subtitle">
                      {t("powered_by_ai_agents", "Powered by AI agents")}
                    </p>
                  </div>
                  <Button
                    size="small"
                    onClick={handleGenerateInsights}
                    disabled={generatingInsights}
                    className="generate-insights-button"
                  >
                    {generatingInsights ? (
                      <>
                        <Spinner size="small" />
                        <span>{t("generating")}</span>
                      </>
                    ) : (
                      t("refresh", "Refresh")
                    )}
                  </Button>
                </div>

                <div className="insights-dropdown-content">
                  {insightsLoading && insights.length === 0 ? (
                    <div className="insights-loading">
                      <Spinner size="medium" />
                      <p>
                        {t(
                          "analyzing_with_agents",
                          "Our AI agents are analyzing your data..."
                        )}
                      </p>
                      <div className="loading-agents">
                        {Object.entries(AGENTS)
                          .slice(0, 3)
                          .map(([key, agent]) => (
                            <span key={key} className="loading-agent">
                              {agent.icon}
                            </span>
                          ))}
                      </div>
                    </div>
                  ) : insights.length > 0 ? (
                    <div className="insights-list-compact">
                      {insights.slice(0, 3).map((insight) => (
                        <div key={insight.id} className="insight-item">
                          <InsightCard
                            insight={insight}
                            onRefresh={loadInsights}
                            compact={true}
                          />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="no-insights">
                      <p>{t("no_insights_yet", "No insights yet")}</p>
                      <p className="no-insights-description">
                        {t(
                          "insights_dropdown_description",
                          "Your AI agents are ready to analyze your business and find growth opportunities."
                        )}
                      </p>
                      <div className="mini-agents-preview">
                        {Object.entries(AGENTS)
                          .slice(0, 4)
                          .map(([key, agent]) => (
                            <span
                              key={key}
                              className="mini-agent-icon"
                              title={`${agent.name} - ${agent.role}`}
                            >
                              {agent.icon}
                            </span>
                          ))}
                      </div>
                      <Button
                        size="small"
                        onClick={handleGenerateInsights}
                        disabled={generatingInsights}
                        className="dropdown-generate-button"
                      >
                        {generatingInsights ? (
                          <>
                            <Spinner size="small" />
                            <span>{t("generating")}</span>
                          </>
                        ) : (
                          t("generate_insights")
                        )}
                      </Button>
                    </div>
                  )}
                </div>

                {/* View All Insights footer link */}
                <div className="insights-dropdown-footer">
                  {insights.length > 0 && (
                    <Link
                      to={`/locations/${
                        location.pathname.split("/")[2]
                      }/insights`}
                      onClick={() => setShowInsightsDropdown(false)}
                      className="view-all-insights-link"
                    >
                      {t("view_all_insights")} ({insights.length})
                    </Link>
                  )}
                </div>
              </div>
            )}
          </div>

          {!isChatRoute && !isSettingsRoute && (
            <button
              className={`mini-chat-toggle-header ${
                showMiniChat ? "active" : ""
              }`}
              onClick={toggleMiniChat}
              aria-label={showMiniChat ? t("hide_chat") : t("show_chat")}
            >
              {showMiniChat ? (
                <>
                  <XIcon className="header-icon" />
                  <span className="toggle-text">{t("hide_chat")}</span>
                </>
              ) : (
                <>
                  <img src={smokeyIcon} alt="Smokey" className="smokey-icon" />
                  <span className="toggle-text">{t("show_chat")}</span>
                </>
              )}
            </button>
          )}
        </div>
      </header>

      {/* Mobile menu overlay - show only on mobile */}
      {showMobileMenu && isMobile && (
        <div className="mobile-menu-overlay" onClick={handleOverlayClick}></div>
      )}

      {/* Navigation sidebar */}
      <div
        className={`app-sidebar ${
          isMobile && showMobileMenu ? "mobile-open" : ""
        } ${!effectiveShowSidebar && !isMobile ? "collapsed" : ""}`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <LocationSidebar
          prepend={
            isMobile ? (
              <div className="sidebar-location-picker">
                <LocationPicker />
              </div>
            ) : undefined
          }
          customContent={renderCustomSidebar()}
          collapsed={!effectiveShowSidebar && !isMobile}
        />
      </div>

      {/* Main content in the middle */}
      <main
        className={`app-main-content ${
          !effectiveShowSidebar ? "sidebar-collapsed" : ""
        } ${
          shouldShowMiniChat && showMiniChat
            ? "chat-expanded"
            : "chat-collapsed"
        } with-header`}
      >
        <Outlet />
      </main>

      {/* Chat sidebar on the right - always mounted but conditionally displayed */}
      <div
        className={`app-mini-chat ${
          shouldShowMiniChat && showMiniChat ? "open" : "closed"
        } ${showResizeHint ? "show-resize-hint" : ""}`}
        style={{
          display: shouldShowMiniChat ? "block" : "none",
          width: isMobile ? "100%" : `${miniChatWidth}px`,
          height: isMobile
            ? `${miniChatHeight}px`
            : "calc(100vh - var(--header-height))",
        }}
      >
        <div
          className="mini-chat-resize-handle"
          onMouseDown={handleResizeStart}
          onTouchStart={handleResizeStart}
        ></div>
        <div className="app-mini-chat-content">
          {/* Tabs */}
          {/* <div className="mini-chat-tabs">
            <div
              className={`mini-chat-tab ${
                activeTab === "chat" ? "active" : ""
              }`}
              onClick={() => setActiveTab("chat")}
            >
              {t("chat")}
            </div>
            <div
              className={`mini-chat-tab ${
                activeTab === "insights" ? "active" : ""
              }`}
              onClick={() => setActiveTab("insights")}
            >
              {t("insights")}
            </div>
          </div> */}

          {/* Content based on active tab */}
          <MiniChatPage activeTab={activeTab} />
        </div>
      </div>
    </div>
  );
};

export default AppLayout;
