import { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";

interface Competitor {
  place_id: string;
  name: string;
  address: string;
  location: {
    lat: number;
    lng: number;
  };
  distance: number;
}

interface CompetitorSearchProps {
  businessLocation: {
    lat: number;
    lng: number;
  };
  onCompetitorSelect: (competitors: Competitor[]) => void;
}

export default function CompetitorSearch({
  businessLocation,
  onCompetitorSelect,
}: CompetitorSearchProps) {
  const { t } = useTranslation();
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [searchResults, setSearchResults] = useState<Competitor[]>([]);
  const [selectedCompetitors, setSelectedCompetitors] = useState<Set<string>>(
    new Set()
  );
  const mapRef = useRef<HTMLDivElement>(null);
  const markersRef = useRef<google.maps.Marker[]>([]);
  const businessMarkerRef = useRef<google.maps.Marker | null>(null);

  useEffect(() => {
    const initMap = async () => {
      if (!mapRef.current) return;

      try {
        const newMap = new google.maps.Map(mapRef.current, {
          center: businessLocation,
          zoom: 13,
          mapTypeControl: false,
        });

        // Add marker for the business location
        businessMarkerRef.current = new google.maps.Marker({
          position: businessLocation,
          map: newMap,
          icon: {
            url: "https://maps.google.com/mapfiles/ms/icons/blue-dot.png",
          },
          title: "Your Location",
        });

        setMap(newMap);
      } catch (error) {
        console.error("Failed to initialize map:", error);
      }
    };

    initMap();

    return () => {
      // Cleanup markers
      markersRef.current.forEach((marker) => marker.setMap(null));
      if (businessMarkerRef.current) {
        businessMarkerRef.current.setMap(null);
      }
    };
  }, [businessLocation]);

  const searchNearbyCompetitors = async () => {
    if (!map) return;

    const service = new google.maps.places.PlacesService(map);
    const request = {
      location: businessLocation,
      radius: 5000, // 5km radius
      type: "cannabis_store", // This is a custom type, might need to adjust based on available types
      keyword: "cannabis dispensary",
    };

    service.nearbySearch(request, (results, status) => {
      if (status === google.maps.places.PlacesServiceStatus.OK && results) {
        // Clear existing markers
        markersRef.current.forEach((marker) => marker.setMap(null));
        markersRef.current = [];

        const competitors: Competitor[] = results.map((place) => ({
          place_id: place.place_id!,
          name: place.name!,
          address: place.vicinity!,
          location: {
            lat: place.geometry!.location!.lat(),
            lng: place.geometry!.location!.lng(),
          },
          distance: google.maps.geometry.spherical.computeDistanceBetween(
            new google.maps.LatLng(businessLocation),
            place.geometry!.location!
          ),
        }));

        // Add markers for each competitor
        competitors.forEach((competitor) => {
          const marker = new google.maps.Marker({
            position: competitor.location,
            map,
            title: competitor.name,
            icon: {
              url: selectedCompetitors.has(competitor.place_id)
                ? "https://maps.google.com/mapfiles/ms/icons/green-dot.png"
                : "https://maps.google.com/mapfiles/ms/icons/red-dot.png",
            },
          });

          marker.addListener("click", () => {
            toggleCompetitorSelection(competitor);
          });

          markersRef.current.push(marker);
        });

        setSearchResults(competitors);
      }
    });
  };

  const toggleCompetitorSelection = (competitor: Competitor) => {
    const newSelection = new Set(selectedCompetitors);
    if (newSelection.has(competitor.place_id)) {
      newSelection.delete(competitor.place_id);
    } else {
      newSelection.add(competitor.place_id);
    }
    setSelectedCompetitors(newSelection);

    // Update marker color
    const marker = markersRef.current.find(
      (m) =>
        m.getPosition()?.lat() === competitor.location.lat &&
        m.getPosition()?.lng() === competitor.location.lng
    );
    if (marker) {
      marker.setIcon({
        url: newSelection.has(competitor.place_id)
          ? "https://maps.google.com/mapfiles/ms/icons/green-dot.png"
          : "https://maps.google.com/mapfiles/ms/icons/red-dot.png",
      });
    }

    // Update selected competitors
    const selectedCompetitorsList = searchResults.filter((c) =>
      newSelection.has(c.place_id)
    );
    onCompetitorSelect(selectedCompetitorsList);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">
          {t("onboarding.steps.competitors.title")}
        </h3>
        <button
          onClick={searchNearbyCompetitors}
          className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
        >
          {t("onboarding.steps.competitors.search_button")}
        </button>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div ref={mapRef} className="h-[400px] rounded-lg border" />

        <div className="space-y-2">
          <p className="text-sm text-gray-600 mb-4">
            {t("onboarding.steps.competitors.description")}
          </p>

          <div className="space-y-2 max-h-[350px] overflow-y-auto">
            {searchResults.map((competitor) => (
              <div
                key={competitor.place_id}
                className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                  selectedCompetitors.has(competitor.place_id)
                    ? "bg-green-50 border-green-200"
                    : "bg-white hover:bg-gray-50"
                }`}
                onClick={() => toggleCompetitorSelection(competitor)}
              >
                <h4 className="font-medium">{competitor.name}</h4>
                <p className="text-sm text-gray-600">{competitor.address}</p>
                <p className="text-xs text-gray-500">
                  {(competitor.distance! / 1000).toFixed(1)}km away
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
