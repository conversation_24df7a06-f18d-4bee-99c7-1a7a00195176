import React from "react";
import { useNavigate } from "react-router-dom";
import bannerLogo from "../../assets/BakedBot_banner_logo.png";
import mcbaLogo from "../../assets/MCBA_logo.png";
import linkedinLogo from "../../assets/black-instagram-icon.png";
import instagramLogo from "../../assets/linkedin-square-icon.png";
import ultraCannabisLogo from "../../assets/ultracannabis_logo.avif";

const MCBALandingPage: React.FC = () => {
  const navigate = useNavigate();

  const handleGetStarted = () => {
    navigate("/login?action=register");
  };

  return (
    <div className="mcba-landing-page">
      <style>
        {`
          :root {
            --primary: #2DD36F;
            --secondary: #34495e;
            --dark: #121212;
            --light: #ffffff;
            --accent: #8E44AD;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-600: #6c757d;
            --gray-700: #495057;
            --gray-800: #343a40;
          }
          * { margin:0; padding:0; box-sizing:border-box }
          .mcba-landing-page {
            font-family:'Inter',sans-serif;
            line-height:1.6;
            color:var(--gray-800);
            background:var(--gray-100);
          }
          .container { max-width:1200px; margin:0 auto; padding:20px; }
          header {
            background:var(--light);
            color:var(--dark);
            padding:20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.06);
          }
          .logo-container {
            display:flex;
            align-items:center;
            justify-content:space-between;
          }
          .logos {
            display:flex;
            align-items:center;
            gap:20px;
          }
          .logo { height:40px }
          .partnership-badge {
            background:var(--primary);
            color:var(--dark);
            padding:8px 16px;
            border-radius:30px;
            font-weight:600;
            font-size:14px;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 8px rgba(45,211,111,0.3);
          }
          .hero {
            padding:100px 0 80px;
            background:linear-gradient(135deg,var(--dark) 0%,#2c3e50 100%);
            color:var(--light);
            text-align:center;
          }
          .hero h1 {
            font-size:44px;
            margin-bottom:24px;
            line-height:1.2;
            font-weight:700;
            letter-spacing: -0.5px;
          }
          .hero p {
            font-size:19px;
            max-width:800px;
            margin:0 auto 50px;
            line-height:1.7;
            color:rgba(255,255,255,0.9);
          }
          .get-started-container {
            background:rgba(255,255,255,0.12);
            backdrop-filter:blur(10px);
            padding:45px;
            border-radius:16px;
            max-width:600px;
            margin:0 auto;
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
          }
          .form-heading {
            font-size:26px;
            margin-bottom:30px;
            color:var(--primary);
            font-weight:600;
          }
          .get-started-button {
            background:var(--primary);
            color:var(--dark);
            border:none;
            padding:16px 45px;
            border-radius:10px;
            font-weight:600;
            font-size:18px;
            cursor:pointer;
            transition:all .3s ease;
            box-shadow: 0 4px 15px rgba(45,211,111,0.3);
          }
          .get-started-button:hover {
            background:#25b15c;
            transform:translateY(-3px);
            box-shadow: 0 6px 20px rgba(45,211,111,0.4);
          }
          .get-started-button:active {
            transform:translateY(-1px);
          }
          .benefits {
            padding:100px 0;
            text-align:center;
            background: var(--light);
          }
          .section-title {
            font-size:36px;
            margin-bottom:60px;
            position:relative;
            display:inline-block;
            font-weight:700;
            color: var(--gray-800);
          }
          .section-title:after {
            content:'';
            position:absolute;
            bottom:-15px; left:50%;
            transform:translateX(-50%);
            width:80px; height:4px;
            background:var(--primary);
            border-radius: 2px;
          }
          .benefits-grid {
            display:grid;
            grid-template-columns:repeat(3,1fr);
            gap:30px;
            margin-top:40px;
          }
          .benefit-card {
            background:var(--light);
            padding:35px 25px;
            border-radius:14px;
            box-shadow:0 5px 20px rgba(0,0,0,0.05);
            transition:all .4s ease;
            border: 1px solid var(--gray-200);
          }
          .benefit-card:hover {
            transform:translateY(-10px);
            box-shadow:0 10px 30px rgba(0,0,0,0.12);
            border-color: var(--gray-300);
          }
          .benefit-icon { 
            font-size:40px; 
            margin-bottom:25px; 
            color:var(--primary);
            display: inline-block;
          }
          .benefit-title { 
            font-size:20px; 
            margin-bottom:15px; 
            color:var(--gray-800);
            font-weight: 600;
          }
          .benefit-desc { 
            color:var(--gray-600); 
            font-size:16px;
            line-height: 1.7;
          }
          .testimonials {
            padding:100px 0;
            background:var(--gray-100);
            text-align:center;
          }
          .testimonial {
            background:var(--light);
            padding:40px;
            border-radius:16px;
            box-shadow:0 5px 25px rgba(0,0,0,0.06);
            max-width:750px;
            margin:0 auto;
            position:relative;
            border: 1px solid var(--gray-200);
          }
          .testimonial::before {
            content:'"';
            position:absolute;
            font-size:120px;
            top:-20px; left:10px;
            color:rgba(45,211,111,0.1);
            font-family:Georgia,serif;
          }
          .quote {
            font-style:italic;
            margin-bottom:25px;
            font-size:19px;
            position:relative;
            z-index:1;
            line-height: 1.8;
            color: var(--gray-700);
          }
          .author {
            display:flex;
            align-items:center;
            justify-content:center;
            margin-top: 10px;
          }
          .author-img {
            width:55px; 
            height:55px;
            border-radius:50%;
            margin-right:15px;
            object-fit:cover;
            border: 3px solid var(--gray-200);
          }
          .company-logo {
            width: auto;
            height: 45px;
            border-radius: 8px;
            margin-right: 15px;
            object-fit: contain;
            background-color: black;
            padding: 5px;
            border: 2px solid var(--gray-300);
            box-shadow: 0 2px 6px rgba(0,0,0,0.08);
          }
          .author-info { line-height:1.4; text-align: left; }
          .author-name { font-weight:600; color:var(--gray-800); font-size: 17px; }
          .author-title { font-size:14px; color:var(--gray-600); }
          .cta {
            padding:100px 0;
            background:linear-gradient(135deg,var(--dark) 0%,#2c3e50 100%);
            color:var(--light);
            text-align:center;
          }
          .cta h2 { font-size:40px; margin-bottom:24px; font-weight: 700; }
          .cta p { max-width:700px; margin:0 auto 35px; font-size:19px; line-height: 1.7; color: rgba(255,255,255,0.9); }
          .cta-button {
            background:var(--primary);
            color:var(--dark);
            padding:16px 45px;
            border-radius:30px;
            font-weight:600;
            font-size:18px;
            text-decoration:none;
            transition:all .3s ease;
            display: inline-block;
            border: none;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(45,211,111,0.4);
          }
          .cta-button:hover {
            background:#25b15c;
            transform:translateY(-3px);
            box-shadow: 0 8px 25px rgba(45,211,111,0.5);
          }
          .cta-button:active {
            transform:translateY(-1px);
          }
          .social-proof {
            padding:70px 0;
            text-align:center;
            background: var(--light);
          }
          .social-proof h3 {
            font-size: 24px;
            margin-bottom: 35px;
            color: var(--gray-800);
            font-weight: 600;
          }
          .brands {
            display:flex;
            justify-content:center;
            align-items:center;
            flex-wrap:wrap;
            gap:40px;
            margin-top:30px;
          }
          .brands a {
            display: flex;
            align-items: center;
            color: var(--gray-700);
            text-decoration: none;
            transition: transform 0.3s ease, color 0.3s ease;
            padding: 10px 15px;
            border-radius: 8px;
          }
          .brands a:hover {
            transform: translateY(-5px);
            color: var(--primary);
          }
          .brand-logo { 
            height:28px; 
            margin-right: 10px;
            transition: opacity .3s;
          }
          .brands a span {
            font-weight: 500;
            font-size: 15px;
          }
          footer {
            background:var(--dark);
            color:var(--light);
            padding:60px 0 30px;
          }
          .footer-content {
            display:grid;
            grid-template-columns:2fr 1fr 1fr;
            gap:40px;
            margin-bottom:40px;
          }
          .footer-logo { height:35px; margin-bottom:20px }
          .footer-desc { margin-bottom:20px; color:#aaa; line-height: 1.7; }
          .footer-social {
            display:flex;
            gap:15px;
            margin-bottom:20px;
          }
          .social-icon {
            width:38px; height:38px;
            border-radius:50%;
            background:rgba(255,255,255,0.1);
            display:flex;
            align-items:center;
            justify-content:center;
            transition:all .3s;
          }
          .social-icon:hover { 
            background:var(--primary);
            transform: translateY(-3px);
          }
          .footer-links h4 {
            font-size:18px; 
            margin-bottom:25px;
            position:relative; 
            display:inline-block;
            font-weight: 600;
          }
          .footer-links h4:after {
            content:'';
            position:absolute;
            bottom:-8px; left:0;
            width:30px; height:3px;
            background:var(--primary);
            border-radius: 1.5px;
          }
          .footer-links ul { list-style:none }
          .footer-links li { margin-bottom:12px }
          .footer-links a {
            color:#aaa;
            text-decoration:none;
            transition:color .3s;
            font-size: 15px;
          }
          .footer-links a:hover { color:var(--primary) }
          .copyright {
            text-align:center;
            padding-top:30px;
            border-top:1px solid rgba(255,255,255,0.1);
            color:#777;
            font-size:14px;
          }
          @media(max-width:992px){
            .benefits-grid{grid-template-columns:repeat(2,1fr)}
            .footer-content{grid-template-columns:1fr 1fr}
            .hero h1{font-size:38px}
          }
          @media(max-width:768px){
            .hero h1{font-size:32px}
            .hero{padding:70px 0 60px}
            .benefits-grid{grid-template-columns:1fr}
            .footer-content{grid-template-columns:1fr;gap:30px}
            .section-title{font-size:30px}
            .benefit-card{padding:30px 20px}
            .get-started-container{padding:35px 25px}
            .testimonial{padding:35px 25px}
            .cta{padding:70px 0}
            .cta h2{font-size:32px}
            .benefits, .testimonials{padding:70px 0}
          }
          @media(max-width:576px){
            .logo-container{flex-direction:column;gap:15px}
            .partnership-badge{margin-top:10px}
            .get-started-container{padding:30px 20px}
            .hero h1{font-size:28px}
            .hero p{font-size:17px; margin-bottom:40px}
            .get-started-button, .cta-button{padding:14px 28px; font-size:16px}
            .brands{gap:25px}
            .brands a{flex-direction:column; padding:8px}
            .brand-logo{margin-right:0; margin-bottom:8px}
          }
        `}
      </style>
      <header>
        <div className="container logo-container">
          <div className="logos">
            <img src={bannerLogo} alt="BakedBot AI Logo" className="logo" />
            <span
              style={{
                color: "var(--gray-700)",
                fontSize: "24px",
                margin: "0 5px",
              }}
            >
              ×
            </span>
            <img src={mcbaLogo} alt="MCBA Logo" className="logo" />
          </div>
          <div className="partnership-badge">OFFICIAL PARTNERS</div>
        </div>
      </header>

      <section className="hero">
        <div className="container">
          <h1>
            Leveling the Playing Field:
            <br />
            BakedBot AI × MCBA Partnership
          </h1>
          <p>
            We've teamed up with the Minority Cannabis Business Association to
            bring 50% off cutting-edge AI marketing, compliance & analytics
            tools to minority-owned cannabis businesses. Join today for
            exclusive access.
          </p>

          <div className="get-started-container">
            <h3 className="form-heading">Ready to grow your business?</h3>
            <button className="get-started-button" onClick={handleGetStarted}>
              Get Started
            </button>
          </div>
        </div>
      </section>

      <section className="benefits">
        <div className="container">
          <h2 className="section-title">Partnership Benefits</h2>
          <div className="benefits-grid">
            <div className="benefit-card">
              <div className="benefit-icon">💰</div>
              <h3 className="benefit-title">50% Discount</h3>
              <p className="benefit-desc">
                All MCBA members receive 50% off every BakedBot AI plan—no tiers
                held back.
              </p>
            </div>
            <div className="benefit-card">
              <div className="benefit-icon">🤖</div>
              <h3 className="benefit-title">AI-Powered Growth</h3>
              <p className="benefit-desc">
                Leverage Smokey, Craig, Pops & more to boost sales, retention &
                customer insights.
              </p>
            </div>
            <div className="benefit-card">
              <div className="benefit-icon">📊</div>
              <h3 className="benefit-title">Priority Analytics</h3>
              <p className="benefit-desc">
                Special BI dashboards for minority-owned businesses, powered by
                Pops agent.
              </p>
            </div>
            <div className="benefit-card">
              <div className="benefit-icon">🔍</div>
              <h3 className="benefit-title">SEO Advantage</h3>
              <p className="benefit-desc">
                Our headless menu architecture makes your products rank higher
                on Google.
              </p>
            </div>
            <div className="benefit-card">
              <div className="benefit-icon">🛡️</div>
              <h3 className="benefit-title">Compliance Protection</h3>
              <p className="benefit-desc">
                Deebo keeps you 100% compliant with ever-changing state
                regulations.
              </p>
            </div>
            <div className="benefit-card">
              <div className="benefit-icon">🌱</div>
              <h3 className="benefit-title">Community Support</h3>
              <p className="benefit-desc">
                20% of this partnership's revenue funds MCBA social equity
                programs.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section className="testimonials">
        <div className="container">
          <h2 className="section-title">Success Story</h2>
          <div className="testimonial">
            <p className="quote">
              "We've been running Smokey AI for a few months now, and it's
              already making a huge impact. Customers love getting instant
              recommendations based on their preferences, and our online
              engagement has skyrocketed. Seeing a 50% drop in abandoned carts
              and more repeat buyers. Game-changer!"
            </p>
            <div className="author">
              <img
                src={ultraCannabisLogo}
                alt="AJ – Ultra Cannabis"
                className="company-logo"
              />
              <div className="author-info">
                <div className="author-name">AJ</div>
                <div className="author-title">Ultra Cannabis</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="cta">
        <div className="container">
          <h2>Ready to Transform Your Business?</h2>
          <p>
            Get started now to unlock half-price access to the most powerful AI
            suite in cannabis retail.
          </p>
          <button className="cta-button" onClick={handleGetStarted}>
            Get Started Now
          </button>
        </div>
      </section>

      <section className="social-proof">
        <div className="container">
          <h3>Follow Us</h3>
          <div className="brands">
            <a
              href="https://www.linkedin.com/company/bakedbot-ai/?mcbalandingpage"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img
                src={linkedinLogo}
                alt="BakedBot AI LinkedIn"
                className="brand-logo"
              />
              <span>BakedBot AI</span>
            </a>
            <a
              href="https://instagram.com/bakedbotai"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img
                src={instagramLogo}
                alt="BakedBot AI Instagram"
                className="brand-logo"
              />
              <span>BakedBot AI</span>
            </a>
            <a
              href="https://www.linkedin.com/company/minority-cannabis"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img
                src={linkedinLogo}
                alt="MCBA LinkedIn"
                className="brand-logo"
              />
              <span>MCBA</span>
            </a>
            <a
              href="https://www.instagram.com/mincannbusassoc/"
              target="_blank"
              rel="noopener noreferrer"
            >
              <img
                src={instagramLogo}
                alt="MCBA Instagram"
                className="brand-logo"
              />
              <span>MCBA</span>
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default MCBALandingPage;
