.order-detail-container {
  padding: 20px;
  max-width: 100%;
}

.order-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.order-detail-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.order-detail-metadata {
  display: flex;
  align-items: center;
  gap: 16px;
  color: var(--text-secondary);
}

.order-detail-actions {
  display: flex;
  gap: 8px;
}

.order-detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.order-detail-info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.order-detail-card {
  height: 100%;
}

.card-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.card-content {
  padding: 16px;
}

.user-info-item {
  margin-bottom: 8px;
}

.info-label {
  font-weight: 500;
  margin-right: 8px;
}

.address-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.order-items-card {
  width: 100%;
}

.order-items-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.order-items-table th {
  text-align: left;
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  font-weight: 500;
  color: var(--text-secondary);
}

.order-items-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  vertical-align: top;
}

.order-items-table tfoot td {
  border-top: 1px solid var(--border-color);
  border-bottom: none;
  padding-top: 16px;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-details {
  display: flex;
  flex-direction: column;
}

.product-name {
  font-weight: 500;
  margin: 0 0 4px 0;
}

.product-variant {
  color: var(--text-secondary);
  font-size: 13px;
  margin: 0;
}

.order-total td {
  font-weight: 600;
  font-size: 16px;
}

.order-history-content {
  margin-top: 16px;
}

.order-timeline {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.timeline-item {
  display: flex;
  gap: 16px;
}

.timeline-icon {
  width: 32px;
  height: 32px;
  min-width: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--secondary-bg);
  color: var(--text-secondary);
}

.timeline-icon.completed {
  background-color: rgba(var(--success-rgb), 0.15);
  color: var(--success);
}

.timeline-icon.pending {
  background-color: rgba(var(--warning-rgb), 0.15);
  color: var(--warning);
}

.timeline-icon.cancelled {
  background-color: rgba(var(--danger-rgb), 0.15);
  color: var(--danger);
}

.timeline-content {
  flex: 1;
}

.timeline-content h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
}

.timeline-content p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 13px;
}

.update-status-content {
  margin-bottom: 24px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

.order-detail-loading,
.order-detail-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: 16px;
}

@media (max-width: 768px) {
  .order-detail-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .order-detail-actions {
    width: 100%;
  }
} 