import React, { useCallback, useEffect, useState, useContext } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { LocationContext } from "../../contexts";
import api from "../../api";
import { Order } from "../../types";
import PageContent from "../../ui/PageContent";
import Button from "../../ui/Button";
import Modal from "../../ui/Modal";
import Spinner from "../../ui/Spinner";
import FormWrapper from "../../ui/form/FormWrapper";
import {
  CheckIcon,
  XIcon,
  RefreshIcon,
  EditIcon,
  SearchIcon,
} from "../../ui/icons";
import { formatCurrency, formatDateString } from "../../utils/format";
import { toast } from "react-hot-toast";
import "./Orders.css";

const OrderDetail: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { locationId, orderId } = useParams<{
    locationId: string;
    orderId: string;
  }>();
  const [location] = useContext(LocationContext);
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [newStatus, setNewStatus] = useState<Order["status"]>("pending");
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelling, setCancelling] = useState(false);
  const [showConfirmStatusModal, setShowConfirmStatusModal] = useState(false);
  const [pendingStatus, setPendingStatus] = useState<Order["status"] | null>(
    null
  );

  // Fetch order data
  const fetchOrder = useCallback(async () => {
    if (!locationId || !orderId) return;

    try {
      setLoading(true);
      const data = await api.orders.getById(
        parseInt(locationId),
        parseInt(orderId)
      );
      setOrder(data);
      setNewStatus(data.status);
    } catch (error) {
      console.error("Error fetching order:", error);
      toast.error(t("error_fetching_order"));
    } finally {
      setLoading(false);
    }
  }, [locationId, orderId, t]);

  // Load order on component mount
  useEffect(() => {
    fetchOrder();
  }, [fetchOrder]);

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!order) return;

    // Check if the status change requires confirmation
    const requiresConfirmation =
      newStatus === "completed" || newStatus === "cancelled";

    if (requiresConfirmation) {
      setPendingStatus(newStatus);
      setShowStatusModal(false);
      setShowConfirmStatusModal(true);
      return;
    }

    try {
      setUpdatingStatus(true);

      await api.orders.updateStatus(location.id, order.id, {
        status: newStatus,
      });

      toast.success(t("order_status_updated"));
      setShowStatusModal(false);
      fetchOrder(); // Reload order data
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error(t("error_updating_order_status"));
    } finally {
      setUpdatingStatus(false);
    }
  };

  // Handle confirmed status update for irreversible changes
  const handleConfirmedStatusUpdate = async () => {
    if (!order || !pendingStatus) return;

    try {
      setUpdatingStatus(true);

      await api.orders.updateStatus(location.id, order.id, {
        status: pendingStatus,
      });

      toast.success(t("order_status_updated"));
      setShowConfirmStatusModal(false);
      setPendingStatus(null);
      fetchOrder(); // Reload order data
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error(t("error_updating_order_status"));
    } finally {
      setUpdatingStatus(false);
    }
  };

  // Handle order cancellation
  const handleCancelOrder = async () => {
    if (!order) return;

    try {
      setCancelling(true);
      await api.orders.cancel(location.id, order.id);

      toast.success(t("order_cancelled"));
      setShowCancelModal(false);
      fetchOrder(); // Reload order data
    } catch (error) {
      console.error("Error cancelling order:", error);
      toast.error(t("error_cancelling_order"));
    } finally {
      setCancelling(false);
    }
  };

  // Go back to orders list
  const goBack = () => {
    navigate(`/locations/${locationId}/orders`);
  };

  // Get status badge class based on status
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "pending":
        return "pending";
      case "processing":
        return "processing";
      case "completed":
        return "completed";
      case "cancelled":
        return "cancelled";
      default:
        return "";
    }
  };

  // Get valid status transitions based on current status
  const getValidStatusTransitions = (currentStatus: string) => {
    const validTransitions: Record<string, string[]> = {
      pending: ["processing", "completed", "cancelled"],
      processing: ["completed", "cancelled"],
      completed: [], // No transitions allowed FROM completed
      cancelled: [], // No transitions allowed FROM cancelled
    };
    return validTransitions[currentStatus] || [];
  };

  // Render status options for dropdown
  const renderStatusOptions = () => {
    if (!order) return null;

    const validTransitions = getValidStatusTransitions(order.status);

    return (
      <>
        {/* Always show current status as selected */}
        <option value={order.status}>
          {t(order.status)} ({t("current")})
        </option>

        {/* Show valid transitions */}
        {validTransitions.map((status) => (
          <option key={status} value={status}>
            {t(status)}
            {(status === "completed" || status === "cancelled") && " ⚠️"}
          </option>
        ))}
      </>
    );
  };

  // Check if status can be updated
  const canUpdateStatus =
    order && order.status !== "completed" && order.status !== "cancelled";

  // Check if order can be cancelled
  const canCancel =
    order && order.status !== "completed" && order.status !== "cancelled";

  if (loading) {
    return (
      <PageContent title={t("order_details")}>
        <div className="flex justify-center items-center h-64">
          <Spinner size="large" />
        </div>
      </PageContent>
    );
  }

  if (!order) {
    return (
      <PageContent title={t("order_details")}>
        <div className="text-center py-8">
          <p>{t("order_not_found")}</p>
          <Button
            variant="secondary"
            icon={<RefreshIcon />}
            onClick={goBack}
            className="mt-4"
          >
            {t("back_to_orders")}
          </Button>
        </div>
      </PageContent>
    );
  }

  return (
    <PageContent
      title={`${t("order")} #${order.id}`}
      actions={
        <Button variant="secondary" icon={<RefreshIcon />} onClick={goBack}>
          {t("back_to_orders")}
        </Button>
      }
    >
      <div className="order-detail-container">
        {/* Left column */}
        <div className="order-main">
          {/* Order Status Section */}
          <div className="order-detail-section">
            <div className="section-header">
              <h3>{t("order_status")}</h3>
            </div>
            <div className="section-content">
              <div className="flex items-center gap-4">
                <span
                  className={`order-status ${getStatusBadgeClass(
                    order.status
                  )}`}
                >
                  {t(order.status)}
                </span>
                <span className="text-sm text-gray-500">
                  {t("updated")}: {formatDateString(order.updated_at)}
                </span>
              </div>

              <div className="order-status-timeline">
                <div className="timeline-item">
                  <div
                    className={`timeline-icon ${
                      order.status !== "cancelled" ? "active" : ""
                    }`}
                  >
                    <CheckIcon />
                  </div>
                  <div>
                    <div className="font-medium">{t("order_placed")}</div>
                    <div className="text-sm text-gray-500">
                      {formatDateString(order.created_at)}
                    </div>
                  </div>
                </div>

                {order.status !== "cancelled" && (
                  <>
                    <div className="timeline-item">
                      <div
                        className={`timeline-icon ${
                          order.status === "processing" ||
                          order.status === "completed"
                            ? "active"
                            : ""
                        }`}
                      >
                        {order.status === "processing" ||
                        order.status === "completed" ? (
                          <CheckIcon />
                        ) : (
                          "2"
                        )}
                      </div>
                      <div>
                        <div className="font-medium">{t("processing")}</div>
                        {order.status === "processing" ||
                        order.status === "completed" ? (
                          <div className="text-sm text-gray-500">
                            {t("order_being_processed")}
                          </div>
                        ) : (
                          <div className="text-sm text-gray-500">
                            {t("waiting_to_be_processed")}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="timeline-item">
                      <div
                        className={`timeline-icon ${
                          order.status === "completed" ? "active" : ""
                        }`}
                      >
                        {order.status === "completed" ? <CheckIcon /> : "3"}
                      </div>
                      <div>
                        <div className="font-medium">{t("completed")}</div>
                        {order.status === "completed" ? (
                          <div className="text-sm text-gray-500">
                            {t("order_completed")}
                          </div>
                        ) : (
                          <div className="text-sm text-gray-500">
                            {t("waiting_to_be_completed")}
                          </div>
                        )}
                      </div>
                    </div>
                  </>
                )}

                {order.status === "cancelled" && (
                  <div className="timeline-item">
                    <div
                      className="timeline-icon active"
                      style={{ backgroundColor: "#ef4444" }}
                    >
                      <XIcon />
                    </div>
                    <div>
                      <div className="font-medium">{t("cancelled")}</div>
                      <div className="text-sm text-gray-500">
                        {formatDateString(order.updated_at)}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {(canUpdateStatus || canCancel) && (
                <div className="status-actions">
                  {canUpdateStatus && (
                    <Button
                      variant="primary"
                      onClick={() => setShowStatusModal(true)}
                    >
                      {t("update_status")}
                    </Button>
                  )}

                  {canCancel && (
                    <Button
                      variant="secondary"
                      onClick={() => setShowCancelModal(true)}
                    >
                      {t("cancel_order")}
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Order Items Section */}
          <div className="order-detail-section">
            <div className="section-header">
              <h3>{t("order_items")}</h3>
            </div>
            <div className="section-content">
              <table className="order-items-table">
                <thead>
                  <tr>
                    <th>{t("product")}</th>
                    <th>{t("price")}</th>
                    <th>{t("quantity")}</th>
                    <th>{t("total")}</th>
                  </tr>
                </thead>
                <tbody>
                  {order.items &&
                    order.items.map((item) => (
                      <tr key={item.id}>
                        <td>
                          <Link
                            to={`/locations/${locationId}/products/${item.product_data?.id}`}
                          >
                            {item.product_data?.product_name}
                          </Link>
                          {item.product_data?.category && (
                            <div className="text-sm text-gray-500">
                              {item.product_data.category}
                            </div>
                          )}
                        </td>
                        <td>{formatCurrency(item.unit_price)}</td>
                        <td>{item.quantity}</td>
                        <td>{formatCurrency(item.total_price)}</td>
                      </tr>
                    ))}
                </tbody>
                <tfoot>
                  <tr>
                    <td colSpan={3} className="text-right font-medium">
                      {t("total")}:
                    </td>
                    <td className="font-medium">
                      {formatCurrency(order.total_amount)}
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>

        {/* Right column */}
        <div className="order-sidebar">
          {/* Customer Information */}
          <div className="order-detail-section">
            <div className="section-header">
              <h3>{t("customer_information")}</h3>
            </div>
            <div className="section-content">
              {order.user ? (
                <div className="info-grid">
                  <div className="info-label">{t("user_id")}:</div>
                  <div className="info-value">#{order.user.id}</div>

                  <div className="info-label">{t("name")}:</div>
                  <div className="info-value">
                    {order.user.first_name} {order.user.last_name}
                  </div>

                  <div className="info-label">{t("email")}:</div>
                  <div className="info-value">
                    <div className="customer-email-link">
                      <span>{order.user.email || t("not_provided")}</span>
                      {order.user.email && (
                        <Button
                          variant="secondary"
                          onClick={() => {
                            navigate(
                              `/locations/${locationId}/users/${order.user_id}/orders`
                            );
                          }}
                          title={t("view_orders_for_this_email")}
                        >
                          <SearchIcon />
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="info-label">{t("phone")}:</div>
                  <div className="info-value">
                    {order.user.phone || t("not_provided")}
                  </div>
                </div>
              ) : (
                <p>{t("guest_checkout")}</p>
              )}
            </div>
          </div>

          {/* Shipping Address */}
          {order.shipping_address && (
            <div className="order-detail-section">
              <div className="section-header">
                <h3>{t("shipping_address")}</h3>
              </div>
              <div className="section-content">
                <div className="address-info">
                  <p>{order.shipping_address.name}</p>
                  <p>{order.shipping_address.line1}</p>
                  {order.shipping_address.line2 && (
                    <p>{order.shipping_address.line2}</p>
                  )}
                  <p>
                    {order.shipping_address.city},{" "}
                    {order.shipping_address.state}{" "}
                    {order.shipping_address.postal_code}
                  </p>
                  <p>{order.shipping_address.country}</p>
                  {order.shipping_address.phone && (
                    <p>{order.shipping_address.phone}</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Order Information */}
          <div className="order-detail-section">
            <div className="section-header">
              <h3>{t("order_information")}</h3>
            </div>
            <div className="section-content">
              <div className="info-grid">
                <div className="info-label">{t("order_id")}:</div>
                <div className="info-value">#{order.id}</div>

                <div className="info-label">{t("date_placed")}:</div>
                <div className="info-value">
                  {formatDateString(order.created_at)}
                </div>

                <div className="info-label">{t("total")}:</div>
                <div className="info-value">
                  {formatCurrency(order.total_amount)}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Update Status Modal */}
      <Modal
        title={t("update_order_status")}
        open={showStatusModal}
        onClose={() => !updatingStatus && setShowStatusModal(false)}
      >
        <FormWrapper
          onSubmit={handleStatusUpdate}
          submitLabel={t("update")}
          disabled={updatingStatus || newStatus === order.status}
        >
          {() => (
            <>
              <p>
                {t("update_status_for_order")} #{order.id}
              </p>

              <select
                value={newStatus}
                onChange={(e) =>
                  setNewStatus(e.target.value as Order["status"])
                }
                className="status-select"
              >
                {renderStatusOptions()}
              </select>

              {(newStatus === "completed" || newStatus === "cancelled") &&
                newStatus !== order.status && (
                  <div className="text-sm text-amber-600 mt-2 p-2 bg-amber-50 rounded">
                    <strong>⚠️ {t("warning")}:</strong>{" "}
                    {t("status_change_irreversible_note")}
                  </div>
                )}

              {updatingStatus && (
                <div className="flex justify-center my-4">
                  <Spinner size="medium" />
                </div>
              )}
            </>
          )}
        </FormWrapper>
      </Modal>

      {/* Cancel Order Modal */}
      <Modal
        title={t("cancel_order")}
        open={showCancelModal}
        onClose={() => !cancelling && setShowCancelModal(false)}
      >
        <FormWrapper
          onSubmit={handleCancelOrder}
          submitLabel={t("cancel_order")}
          disabled={cancelling}
        >
          {() => (
            <>
              <p>
                {t("cancel_order_confirmation")} #{order.id}?
              </p>
              <p className="text-sm text-gray-500 mt-2">
                {t("cancel_order_warning")}
              </p>

              {cancelling && (
                <div className="flex justify-center my-4">
                  <Spinner size="medium" />
                </div>
              )}
            </>
          )}
        </FormWrapper>
      </Modal>

      {/* Confirm Irreversible Status Change Modal */}
      <Modal
        title={t("confirm_status_change")}
        open={showConfirmStatusModal}
        onClose={() => !updatingStatus && setShowConfirmStatusModal(false)}
      >
        <FormWrapper
          onSubmit={handleConfirmedStatusUpdate}
          submitLabel={t("confirm_change")}
          disabled={updatingStatus}
        >
          {() => (
            <>
              <p>
                {t("status_change_warning")} #{order.id} {t("to")}{" "}
                <strong>{pendingStatus && t(pendingStatus)}</strong>?
              </p>
              <p className="text-sm text-gray-500 mt-2">
                <strong>{t("warning")}:</strong>{" "}
                {t("irreversible_action_warning")}
              </p>

              {updatingStatus && (
                <div className="flex justify-center my-4">
                  <Spinner size="medium" />
                </div>
              )}
            </>
          )}
        </FormWrapper>
      </Modal>
    </PageContent>
  );
};

export default OrderDetail;
