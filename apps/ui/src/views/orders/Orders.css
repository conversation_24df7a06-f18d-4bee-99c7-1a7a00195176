.orders-container {
  padding: 20px;
  max-width: 100%;
}

.orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.orders-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.orders-actions {
  display: flex;
  gap: 10px;
}

.orders-filters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 16px;
  flex-wrap: wrap;
}

.search-form {
  display: flex;
  gap: 8px;
  flex: 1;
  min-width: 300px;
}

.status-filter {
  min-width: 200px;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--input-bg);
  color: var(--text-color);
}

.status-select {
  width: 100%;
  padding: 8px 12px;
  margin-top: 8px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--input-bg);
  color: var(--text-color);
}

.orders-table-container {
  background-color: var(--card-background);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.orders-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.orders-table th {
  background-color: var(--secondary-bg);
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: var(--text-secondary);
  border-bottom: 1px solid var(--border-color);
}

.orders-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
}

.orders-table tr:last-child td {
  border-bottom: none;
}

.orders-table tr:hover td {
  background-color: var(--hover-bg);
}

.order-status {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
}

.order-status.pending {
  background-color: #fef3c7;
  color: #92400e;
}

.order-status.processing {
  background-color: #dbeafe;
  color: #1e40af;
}

.order-status.completed {
  background-color: #d1fae5;
  color: #065f46;
}

.order-status.cancelled {
  background-color: #fee2e2;
  color: #b91c1c;
}

.order-actions {
  display: flex;
  gap: 8px;
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 10px 16px;
  background-color: var(--card-background);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
}

.pagination-info {
  color: var(--text-secondary);
}

.pagination-controls {
  display: flex;
  gap: 8px;
}

/* Order detail page styling */
.order-detail-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.order-detail-section {
  margin-bottom: 24px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.section-header {
  padding: 16px;
  background-color: var(--section-header-bg);
  border-bottom: 1px solid var(--border-color);
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.section-content {
  padding: 16px;
}

.order-status-timeline {
  display: flex;
  flex-direction: column;
  margin-top: 16px;
  gap: 8px;
}

.timeline-item {
  display: flex;
  gap: 16px;
  padding: 8px 0;
}

.timeline-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 9999px;
  background-color: #e5e7eb;
  color: #4b5563;
}

.timeline-icon.active {
  background-color: #3b82f6;
  color: white;
}

.order-items-table {
  width: 100%;
  border-collapse: collapse;
}

.order-items-table th {
  text-align: left;
  padding: 12px 16px;
  background-color: var(--section-header-bg);
  border-bottom: 1px solid var(--border-color);
}

.order-items-table td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
}

.order-items-table tr:last-child td {
  border-bottom: none;
}

.order-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.summary-row {
  display: flex;
  justify-content: space-between;
}

.summary-total {
  font-weight: 600;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--border-color);
}

.address-info {
  margin-top: 16px;
}

.info-grid {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px 16px;
  margin-top: 16px;
}

.info-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.info-value {
  color: var(--text-color);
}

.status-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

/* Customer email link button */
.customer-email-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.customer-email-link button {
  min-width: auto;
  padding: 0.25rem;
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.customer-email-link button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Responsive styling */
@media (max-width: 768px) {
  .order-detail-container {
    grid-template-columns: 1fr;
  }
  
  .orders-filters {
    flex-direction: column;
  }
  
  .search-form {
    width: 100%;
  }
  
  .orders-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .orders-actions {
    width: 100%;
  }
  
  .orders-table-container {
    overflow-x: auto;
  }
} 