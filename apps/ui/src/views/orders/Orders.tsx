import React, { useCallback, useState, useContext, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";
import { LocationContext } from "../../contexts";
import api from "../../api";
import { Order, SearchResult } from "../../types";
import PageContent from "../../ui/PageContent";
import Button from "../../ui/Button";
import Modal from "../../ui/Modal";
import Spinner from "../../ui/Spinner";
import TextInput from "../../ui/form/TextInput";
import FormWrapper from "../../ui/form/FormWrapper";
import { SearchTable, useSearchTableQueryState } from "../../ui/SearchTable";
import { SearchIcon, EditIcon, RefreshIcon } from "../../ui/icons";
import { formatCurrency, formatDateString } from "../../utils/format";
import { toast } from "react-hot-toast";
import { EmptyStateCard } from "../../ui/EmptyCard";
import "./Orders.css";
import { FiShoppingCart } from "react-icons/fi";

interface OrderItem {
  id: number;
  order_id: number;
  product_id: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  product_data: any;
}

const Orders: React.FC = () => {
  const { t } = useTranslation();
  const [location] = useContext(LocationContext);
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [newStatus, setNewStatus] = useState<Order["status"]>("pending");
  const [updatingStatus, setUpdatingStatus] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // State for search form
  const [statusFilter, setStatusFilter] = useState("");

  // Initialize filters from URL parameters
  useEffect(() => {
    const qParam = searchParams.get("q");
    const statusParam = searchParams.get("status");

    if (qParam) {
      setSearchQuery(qParam);
    }

    if (statusParam) {
      setStatusFilter(statusParam);
    }
  }, [searchParams]);

  // Initialize search state with the API search function
  const state = useSearchTableQueryState<Order>(
    useCallback(
      async (params) => {
        // Add status filter if selected
        const filter = statusFilter ? { status: statusFilter } : {};

        // Search query will be passed to q parameter
        const q = searchQuery.trim() || undefined;

        // Use the simplified search method
        return await api.orders.search(location.id, {
          ...params,
          limit: 25,
          sort: params.sort || "created_at",
          direction: params.direction || "desc",
          filter,
          q,
        });
      },
      [location.id, statusFilter, searchQuery]
    )
  );

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Update URL parameters
    const newParams = new URLSearchParams(searchParams);
    if (searchQuery.trim()) {
      newParams.set("q", searchQuery.trim());
    } else {
      newParams.delete("q");
    }
    setSearchParams(newParams);

    state.reload();
  };

  // Handle order status update
  const handleStatusUpdate = async () => {
    if (!selectedOrder) return;

    try {
      setUpdatingStatus(true);

      if (newStatus === "cancelled") {
        await api.orders.cancel(location.id, selectedOrder.id);
      } else {
        await api.orders.updateStatus(location.id, selectedOrder.id, {
          status: newStatus,
        });
      }

      toast.success(t("order_status_updated"));
      setShowStatusModal(false);
      state.reload();
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error(t("error_updating_order_status"));
    } finally {
      setUpdatingStatus(false);
    }
  };

  // Navigate to order details page
  const handleViewOrder = (order: Order) => {
    window.location.href = `/locations/${location.id}/orders/${order.id}`;
  };

  // Open status update modal
  const handleOpenStatusModal = (order: Order) => {
    setSelectedOrder(order);
    setNewStatus(order.status);
    setShowStatusModal(true);
  };

  // Get status badge class based on status
  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "pending":
        return "pending";
      case "processing":
        return "processing";
      case "completed":
        return "completed";
      case "cancelled":
        return "cancelled";
      default:
        return "";
    }
  };

  // Render status options for dropdown
  const renderStatusOptions = () => (
    <>
      <option value="pending">{t("pending")}</option>
      <option value="processing">{t("processing")}</option>
      <option value="completed">{t("completed")}</option>
      <option value="cancelled">{t("cancelled")}</option>
    </>
  );

  return (
    <>
      <PageContent
        title={t("orders")}
        actions={
          <div className="orders-actions">
            <Button
              variant="secondary"
              icon={<RefreshIcon />}
              onClick={() => state.reload()}
            >
              {t("refresh")}
            </Button>
          </div>
        }
      >
        <div className="orders-filters">
          <form onSubmit={handleSearch} className="search-form">
            <TextInput
              hideLabel
              name="search"
              placeholder={t("search_orders")}
              value={searchQuery}
              onChange={(value) => setSearchQuery(value)}
            />
            <Button type="submit">{t("search")}</Button>
          </form>

          <select
            value={statusFilter}
            onChange={(e) => {
              const newStatus = e.target.value;
              setStatusFilter(newStatus);

              // Update URL parameters
              const newParams = new URLSearchParams(searchParams);
              if (newStatus) {
                newParams.set("status", newStatus);
              } else {
                newParams.delete("status");
              }
              setSearchParams(newParams);

              state.reload();
            }}
            className="status-filter"
          >
            <option value="">{t("all_statuses")}</option>
            {renderStatusOptions()}
          </select>
        </div>

        <SearchTable
          {...state}
          enableSearch={false}
          results={state.results as SearchResult<Order>}
          columns={[
            {
              key: "id",
              title: t("order_id"),
              sortable: true,
              cell: ({ item }) => `#${item.id}`,
            },
            {
              key: "user",
              title: t("customer"),
              sortable: false,
              cell: ({ item }) =>
                item.user ? (
                  <div>
                    <div>
                      {item.user.first_name} {item.user.last_name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {item.user.email || item.user.phone}
                    </div>
                  </div>
                ) : (
                  t("guest")
                ),
            },
            {
              key: "total_amount",
              title: t("total"),
              sortable: true,
              cell: ({ item }) => formatCurrency(item.total_amount),
            },
            {
              key: "status",
              title: t("status"),
              sortable: true,
              cell: ({ item }) => (
                <span
                  className={`order-status ${getStatusBadgeClass(item.status)}`}
                >
                  {t(item.status)}
                </span>
              ),
            },
            {
              key: "created_at",
              title: t("order_date"),
              sortable: true,
              cell: ({ item }) => formatDateString(item.created_at),
            },
            {
              key: "actions",
              title: t("actions"),
              sortable: false,
              cell: ({ item }) => (
                <div className="order-actions">
                  <Button
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleViewOrder(item);
                    }}
                    title={t("view_order")}
                  >
                    <SearchIcon />
                  </Button>
                  <Button
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleOpenStatusModal(item);
                    }}
                    title={t("update_status")}
                  >
                    <EditIcon />
                  </Button>
                </div>
              ),
            },
          ]}
          onRowClick={handleViewOrder}
          searchPlaceholder={t("search_orders")}
          emptyStateMessage={t(
            "no_orders_found",
            "No orders found. Orders will appear here when customers make purchases."
          )}
        />
      </PageContent>

      {/* Update Status Modal */}
      {selectedOrder && (
        <Modal
          title={t("update_order_status")}
          open={showStatusModal}
          onClose={() => !updatingStatus && setShowStatusModal(false)}
        >
          <FormWrapper
            onSubmit={handleStatusUpdate}
            submitLabel={t("update")}
            disabled={updatingStatus || newStatus === selectedOrder.status}
          >
            {() => (
              <>
                <p>
                  {t("update_status_for_order")} #{selectedOrder.id}
                </p>

                <select
                  value={newStatus}
                  onChange={(e) =>
                    setNewStatus(e.target.value as Order["status"])
                  }
                  className="status-select"
                >
                  {renderStatusOptions()}
                </select>

                {updatingStatus && (
                  <div className="flex justify-center my-4">
                    <Spinner size="medium" />
                  </div>
                )}
              </>
            )}
          </FormWrapper>
        </Modal>
      )}
    </>
  );
};

export default Orders;
