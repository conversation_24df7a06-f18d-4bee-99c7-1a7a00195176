import { useCallback, useContext, useState } from "react";
import PageContent from "../../ui/PageContent";
import { SearchTable, useSearchTableQueryState } from "../../ui/SearchTable";
import api from "../../api";
import { Button, Menu, MenuItem, Modal } from "../../ui";
import { AdminContext } from "../../contexts";
import { useTranslation } from "react-i18next";
import FormWrapper from "../../ui/form/FormWrapper";
import { Admin, OrganizationRole, organizationRoles } from "../../types";
import TextInput from "../../ui/form/TextInput";
import { checkOrganizationRole, snakeToTitle } from "../../utils";
import { SingleSelect } from "../../ui/form/SingleSelect";
import { ArchiveIcon, EditIcon, EmailIcon } from "../../ui/icons";
import { toast } from "react-hot-toast";

export default function Admins() {
  const { t } = useTranslation();
  const state = useSearchTableQueryState(
    useCallback(async (params) => await api.admins.search(params), [])
  );
  const admin = useContext(AdminContext);
  const [editing, setEditing] = useState<Partial<Admin>>();
  const [resendingInvitation, setResendingInvitation] = useState<number | null>(null);

  const handleDelete = async (id: number) => {
    if (confirm(t("delete_admin_confirmation"))) {
      await api.admins.delete(id);
      await state.reload();
    }
  };

  const handleResendInvitation = async (targetAdmin: Admin) => {
    // Prevent resending invitation to current user
    if (targetAdmin.id === admin?.id) {
      toast.error(t("cannot_resend_invitation_to_yourself"));
      return;
    }

    setResendingInvitation(targetAdmin.id);
    try {
      const result = await api.admins.resendInvitation(targetAdmin.id);
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Failed to resend invitation:", error);
      toast.error(t("failed_to_resend_invitation"));
    } finally {
      setResendingInvitation(null);
    }
  };

  return (
    <>
      <PageContent
        title={t("admins")}
        actions={
          <Button onClick={() => setEditing({})}>{t("add_admin")}</Button>
        }
      >
        <SearchTable
          {...state}
          columns={[
            { key: "first_name", title: t("first_name") },
            { key: "last_name", title: t("last_name") },
            { key: "email", title: t("email") },
            {
              key: "role",
              title: t("role"),
              cell: ({ item }) => snakeToTitle(item.role),
            },
            {
              key: "options",
              title: t("options"),
              cell: ({ item }) => (
                <Menu size="small">
                  {/* Only show resend invitation for other admins, not current user */}
                  {item.id !== admin?.id && (
                    <MenuItem
                      onClick={() => {
                        if (resendingInvitation !== item.id) {
                          handleResendInvitation(item);
                        }
                      }}
                    >
                      <EmailIcon />
                      {resendingInvitation === item.id
                        ? t("sending_invitation")
                        : t("resend_invitation")
                      }
                    </MenuItem>
                  )}
                  <MenuItem onClick={async () => await handleDelete(item.id)}>
                    <ArchiveIcon /> {t("delete")}
                  </MenuItem>
                  <MenuItem onClick={() => setEditing(item)}>
                    <EditIcon /> {t("edit")}
                  </MenuItem>
                </Menu>
              ),
            },
          ]}
          onSelectRow={setEditing}
        />
      </PageContent>

      <Modal
        open={Boolean(editing)}
        onClose={() => setEditing(undefined)}
        title={editing?.id ? t("edit_admin") : t("add_admin")}
        size="small"
        description={
          editing?.id ? t("edit_admin_description") : t("add_admin_description")
        }
      >
        {editing && (
          <FormWrapper<Admin>
            onSubmit={async (member) => {
              member.id != null
                ? await api.admins.update(member.id, member)
                : await api.admins.create(member);
              setEditing(undefined);
              await state.reload();
            }}
            defaultValues={editing}
          >
            {(form) => (
              <>
                <TextInput.Field
                  form={form}
                  name="email"
                  label={t("email")}
                  required
                />
                <TextInput.Field
                  form={form}
                  name="first_name"
                  label={t("first_name")}
                />
                <TextInput.Field
                  form={form}
                  name="last_name"
                  label={t("last_name")}
                />
                <SingleSelect.Field
                  form={form}
                  name="role"
                  label={t("role")}
                  disabled={checkOrganizationRole(
                    admin?.role as OrganizationRole,
                    editing.role
                  )}
                  options={organizationRoles}
                  getOptionDisplay={snakeToTitle}
                  required
                />
              </>
            )}
          </FormWrapper>
        )}
      </Modal>
    </>
  );
}
