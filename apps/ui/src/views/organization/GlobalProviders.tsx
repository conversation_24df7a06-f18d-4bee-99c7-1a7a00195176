import { useCallback, useState } from "react";
import { Provider } from "../../types";
import Button from "../../ui/Button";
import { PlusIcon } from "../../ui/icons";
import { SearchTable, useSearchTableState } from "../../ui/SearchTable";
import IntegrationModal from "../settings/IntegrationModal";
import { useTranslation } from "react-i18next";
import api from "../../api";

export default function GlobalProviders() {
  const { t } = useTranslation();
  const state = useSearchTableState(
    useCallback(async (params) => await api.providers.searchGlobal(params), [])
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [provider, setProvider] = useState<Provider>();

  return (
    <>
      <div
        style={{
          display: "flex",
          justifyContent: "flex-end",
          marginBottom: "1rem",
        }}
      >
        <Button
          icon={<PlusIcon />}
          size="small"
          onClick={() => {
            setProvider(undefined);
            setIsModalOpen(true);
          }}
        >
          Add Global Provider
        </Button>
      </div>
      <SearchTable
        {...state}
        columns={[
          { key: "name", title: t("name") },
          { key: "type", title: t("type") },
          { key: "group", title: t("group") },
          { key: "created_at", title: t("created_at") },
        ]}
        itemKey={({ item }) => item.id}
        onSelectRow={(provider: Provider) => {
          setProvider(provider);
          setIsModalOpen(true);
        }}
      />
      <IntegrationModal
        open={isModalOpen}
        onClose={setIsModalOpen}
        provider={provider}
        isGlobal={true}
        onChange={async () => await state.reload()}
      />
    </>
  );
}
