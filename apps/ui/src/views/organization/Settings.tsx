import { useContext, useState, useEffect } from "react";
import PageContent from "../../ui/PageContent";
import FormWrapper from "../../ui/form/FormWrapper";
import { AdminContext, OrganizationContext } from "../../contexts";
import TextInput from "../../ui/form/TextInput";
import { Organization } from "../../types";
import Heading from "../../ui/Heading";
import api, { InviteCode } from "../../api";
import { toast } from "react-hot-toast/headless";
import { Button } from "../../ui";
import { useTranslation } from "react-i18next";
import GlobalProviders from "./GlobalProviders";
import { useNavigate } from "react-router-dom";

export default function Settings() {
  const { t } = useTranslation();
  const profile = useContext(AdminContext);
  const [organization] = useContext(OrganizationContext);
  const [verifyingEmail, setVerifyingEmail] = useState(false);
  const [subscription, setSubscription] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isCanceling, setIsCanceling] = useState(false);
  const navigate = useNavigate();

  // Invite code states
  const [inviteCodes, setInviteCodes] = useState<InviteCode[]>([]);
  const [newInviteName, setNewInviteName] = useState("");
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [loadingInvites, setLoadingInvites] = useState(false);

  // Fetch subscription data when component mounts
  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        if (organization?.id) {
          const data = await api.subscriptions.getStripeSubscription(
            organization.id
          );
          setSubscription(data);
        }
      } catch (error) {
        console.error("Failed to fetch subscription:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSubscription();
  }, [organization]);

  // Fetch invite codes
  useEffect(() => {
    const fetchInviteCodes = async () => {
      setLoadingInvites(true);
      try {
        const codes = await api.invites.getAll();
        setInviteCodes(codes);
      } catch (error) {
        console.error("Failed to fetch invite codes:", error);
        toast.error("Failed to load invite codes");
      } finally {
        setLoadingInvites(false);
      }
    };

    fetchInviteCodes();
  }, []);

  const deleteOrganization = async () => {
    if (confirm("Are you sure you want to delete this organization?")) {
      await api.organizations.delete();
      await api.auth.logout();
      window.location.href = "/";
    }
  };

  const verifyEmailSender = async (email: string) => {
    try {
      setVerifyingEmail(true);
      await api.organizations.verifySender({ email });
      toast.success(
        "Verification email sent! Please check your inbox to complete verification."
      );
    } catch (error) {
      toast.error("Failed to initiate email verification. Please try again.");
    } finally {
      setVerifyingEmail(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscription?.subscription_id) return;

    if (
      confirm(
        "Are you sure you want to cancel your subscription? You will lose access to premium features."
      )
    ) {
      try {
        setIsCanceling(true);
        await api.subscriptions.cancelSubscription(
          subscription.subscription_id
        );
        toast.success("Subscription canceled successfully");

        // Refresh subscription data
        const updated = await api.subscriptions.getStripeSubscription(
          organization.id
        );
        setSubscription(updated);
      } catch (error) {
        console.error("Failed to cancel subscription:", error);
        toast.error("Failed to cancel subscription. Please try again.");
      } finally {
        setIsCanceling(false);
      }
    }
  };

  const handleUpgradeSubscription = () => {
    navigate("/upgrade");
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "";
    return new Date(dateString).toLocaleDateString();
  };

  const getSubscriptionStatusDisplay = () => {
    if (!subscription) return "";

    switch (subscription.status) {
      case "active":
        return "Active";
      case "trialing":
        return "Trial";
      case "canceled":
        return "Canceled";
      case "past_due":
        return "Past Due";
      default:
        return subscription.status;
    }
  };

  // Function to generate new invite code
  const handleGenerateInviteCode = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newInviteName.trim()) {
      toast.error("Please enter a name for the invite code");
      return;
    }

    setIsGeneratingCode(true);
    try {
      const newCode = await api.invites.create(newInviteName);

      // Make sure we have all required properties before adding to state
      if (newCode && newCode.id && newCode.code) {
        // Create a properly formatted invite code object with all expected properties
        const formattedCode: InviteCode = {
          ...newCode,
          // Ensure these properties exist, even if API response doesn't include them
          created_at: newCode.created_at || new Date().toISOString(),
          updated_at: newCode.updated_at || new Date().toISOString(),
          used: newCode.used || false,
          organization_id: newCode.organization_id || organization.id,
        };

        setInviteCodes([formattedCode, ...inviteCodes]);
        setNewInviteName("");
        toast.success("Invite code generated successfully!");
      } else {
        console.error("Invalid response format from API:", newCode);
        toast.error(
          "Generated code had invalid format. Try refreshing the page."
        );
      }
    } catch (error) {
      console.error("Failed to generate invite code:", error);
      toast.error("Failed to generate invite code");
    } finally {
      setIsGeneratingCode(false);
    }
  };

  // Function to copy invite code to clipboard
  const copyToClipboard = (code: string) => {
    navigator.clipboard
      .writeText(code)
      .then(() => toast.success("Code copied to clipboard!"))
      .catch(() => toast.error("Failed to copy code"));
  };

  // Function to delete an invite code
  const handleDeleteInviteCode = async (codeId: number) => {
    if (confirm("Are you sure you want to delete this invite code?")) {
      try {
        await api.invites.delete(codeId);
        setInviteCodes(inviteCodes.filter((code) => code.id !== codeId));
        toast.success("Invite code deleted successfully!");
      } catch (error) {
        console.error("Failed to delete invite code:", error);
        toast.error("Failed to delete invite code");
      }
    }
  };

  const owner = profile?.role === "owner";
  const isSuperAdmin = profile?.role === "superAdmin";
  const canManageInvites = owner || isSuperAdmin;

  return (
    <>
      <PageContent title="Settings">
        {isSuperAdmin && (
          <>
            <Heading size="h3" title="Global Providers" />
            <p>
              Configure global providers like Twilio, SendGrid, etc. that will
              be available to all organizations.
            </p>
            <GlobalProviders />
            <br />
            <br />
          </>
        )}

        {/* Invite Code Management Section */}
        {canManageInvites && (
          <>
            <Heading size="h3" title="Invite Codes" />
            <p className="mb-4">
              Generate and manage invite codes for new users during the beta
              period.
            </p>

            <div className="mb-6">
              <form
                onSubmit={handleGenerateInviteCode}
                className="flex flex-col md:flex-row gap-3 mb-4"
              >
                <div className="flex-grow">
                  <label htmlFor="inviteName" className="sr-only">
                    Name for invite
                  </label>
                  <input
                    type="text"
                    id="inviteName"
                    value={newInviteName}
                    onChange={(e) => setNewInviteName(e.target.value)}
                    placeholder="Enter name for new invite code"
                    className="w-full px-4 py-2 border border-gray-300 rounded-md"
                    disabled={isGeneratingCode}
                  />
                </div>
                <Button
                  type="submit"
                  disabled={isGeneratingCode || !newInviteName.trim()}
                >
                  {isGeneratingCode ? "Generating..." : "Generate Code"}
                </Button>
              </form>
            </div>

            {loadingInvites ? (
              <p>Loading invite codes...</p>
            ) : inviteCodes.length === 0 ? (
              <p className="text-gray-500">No invite codes generated yet.</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full border-collapse">
                  <thead>
                    <tr className="">
                      <th className="py-2 px-4 text-left border border-gray-200">
                        Code
                      </th>
                      <th className="py-2 px-4 text-left border border-gray-200">
                        Generated for
                      </th>
                      <th className="py-2 px-4 text-left border border-gray-200">
                        Created
                      </th>
                      <th className="py-2 px-4 text-left border border-gray-200">
                        Status
                      </th>
                      <th className="py-2 px-4 text-left border border-gray-200">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {inviteCodes.map((code) => (
                      <tr key={code.id} className="border-b">
                        <td className="py-2 px-4 border border-gray-200 font-mono">
                          {code.code}
                        </td>
                        <td className="py-2 px-4 border border-gray-200">
                          {code.name}
                        </td>
                        <td className="py-2 px-4 border border-gray-200">
                          {formatDate(code.created_at)}
                        </td>
                        <td className="py-2 px-4 border border-gray-200">
                          <span
                            className={`px-2 py-1 rounded text-xs ${
                              code.used
                                ? "bg-green-100 text-green-800"
                                : "bg-blue-100 text-blue-800"
                            }`}
                          >
                            {code.used ? "Used" : "Available"}
                          </span>
                          {code.used && code.used_by && (
                            <div className="text-xs text-gray-500 mt-1">
                              By: {code.used_by}
                            </div>
                          )}
                        </td>
                        <td className="py-2 px-4 border border-gray-200">
                          <button
                            onClick={() => copyToClipboard(code.code)}
                            className="text-blue-600 hover:text-blue-800 mr-2"
                            disabled={code.used}
                          >
                            Copy
                          </button>
                          {!code.used && (
                            <button
                              onClick={() => handleDeleteInviteCode(code.id)}
                              className="text-red-600 hover:text-red-800"
                              title="Delete invite code"
                            >
                              Delete
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}

            <br />
            <br />
          </>
        )}

        {/* Subscription Management Section */}
        <Heading size="h3" title="Subscription Management" />
        {isLoading ? (
          <p>Loading subscription information...</p>
        ) : !subscription ? (
          <div>
            <p>No active subscription found.</p>
            <Button className="mt-3" onClick={handleUpgradeSubscription}>
              Upgrade Now
            </Button>
          </div>
        ) : (
          <div className="mt-4 p-4 border border-gray-200 rounded-lg">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm text-gray-500">Status</h4>
                <p className="font-medium">{getSubscriptionStatusDisplay()}</p>
              </div>
              {subscription.current_period_end && (
                <div>
                  <h4 className="text-sm text-gray-500">Current Period Ends</h4>
                  <p className="font-medium">
                    {formatDate(subscription.current_period_end)}
                  </p>
                </div>
              )}
              {subscription.quantity && (
                <div>
                  <h4 className="text-sm text-gray-500">Location Allowance</h4>
                  <p className="font-medium">
                    {subscription.quantity} location
                    {subscription.quantity !== 1 ? "s" : ""}
                  </p>
                </div>
              )}
            </div>

            <div className="mt-6 flex space-x-4">
              <Button
                onClick={handleUpgradeSubscription}
                disabled={subscription.status === "canceled"}
              >
                Change Plan
              </Button>

              {subscription.status === "active" && (
                <Button
                  variant="destructive"
                  onClick={handleCancelSubscription}
                  disabled={isCanceling}
                >
                  {isCanceling ? "Canceling..." : "Cancel Subscription"}
                </Button>
              )}
            </div>
          </div>
        )}

        <div style={{ display: "none" }}>
          <FormWrapper<Organization>
            defaultValues={organization}
            disabled={!owner && !isSuperAdmin}
            onSubmit={async ({
              username,
              domain,
              tracking_deeplink_mirror_url,
              sender_email,
            }) => {
              await api.organizations.update(organization.id, {
                username,
                domain,
                tracking_deeplink_mirror_url,
                sender_email,
              });

              toast.success("Saved organization settings");
            }}
            submitLabel="Save Settings"
          >
            {(form) => (
              <>
                <TextInput.Field
                  form={form}
                  disabled={!owner && !isSuperAdmin}
                  name="username"
                  subtitle="The organization username. Used for the subdomain that the organization is hosted under."
                />
                <TextInput.Field
                  form={form}
                  name="domain"
                  disabled={!owner && !isSuperAdmin}
                  subtitle="If filled, users who log in with SSO and have this domain will be automatically joined to the organization."
                />

                <Heading size="h3" title="Email Settings" />
                <TextInput.Field
                  form={form}
                  name="sender_email"
                  required={true}
                  label="Sender Email"
                  disabled={!owner && !isSuperAdmin}
                  subtitle="The email address that will be used to send emails from your organization."
                />
                <Button
                  className="mt-3"
                  onClick={() =>
                    verifyEmailSender(form.getValues("sender_email") as string)
                  }
                  disabled={
                    verifyingEmail ||
                    (!owner && !isSuperAdmin) ||
                    !form.getValues("sender_email")
                  }
                >
                  {verifyingEmail ? "Verifying..." : "Verify Sender Email"}
                </Button>
                <p className="text-sm text-gray-500 mt-2">
                  You must verify your sender email address before you can send
                  emails. Click the button above to start the verification
                  process.
                </p>

                <Heading size="h3" title="Tracking" className="mt-8" />
                <TextInput.Field
                  form={form}
                  disabled={!owner && !isSuperAdmin}
                  name="tracking_deeplink_mirror_url"
                  label="Tracking Deeplink Mirror URL"
                  subtitle="The URL to clone universal link settings from."
                />
              </>
            )}
          </FormWrapper>
        </div>
        {owner && (
          <>
            <br />
            <br />
            <Heading size="h3" title="Danger Zone" />
            <p>
              Deleting your organization will completely remove it from the
              system along with all associated accounts, locations and data.
            </p>
            <Button
              className="mt-3"
              variant="destructive"
              onClick={async () => await deleteOrganization()}
            >
              Delete Organization
            </Button>
          </>
        )}
      </PageContent>
    </>
  );
}
