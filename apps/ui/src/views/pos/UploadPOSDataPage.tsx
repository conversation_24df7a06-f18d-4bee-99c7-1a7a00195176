import React, { useState, useContext } from "react";
import { useNavigate } from "react-router-dom";
import api from "../../api";
import { LocationContext } from "../../contexts";
import PageContent from "../../ui/PageContent";
import Button from "../../ui/Button";
import { UploadIcon } from "../../ui/icons";
import { useTranslation } from "react-i18next";
import styled from "styled-components";

const SpinningUploadIcon = styled(UploadIcon)<{ $spinning: boolean }>`
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  animation: ${(props) =>
    props.$spinning ? "spin 1s linear infinite" : "none"};
`;

const UploadButton = styled(Button)<{ $uploading: boolean }>`
  min-width: 120px;
  position: relative;

  .upload-text {
    opacity: ${(props) => (props.$uploading ? "0.7" : "1")};
    display: flex;
    align-items: center;
    gap: 8px;
  }
`;

export default function UploadPOSDataPage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [location] = useContext(LocationContext);
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState<boolean>(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log("$$$");

    if (e.target.files && e.target.files.length > 0) {
      console.log(e.target.files[0], "$$$");
      setFile(e.target.files[0]);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      alert(t("please_select_a_file"));
      return;
    }

    const formData = new FormData();
    formData.append("posData", file);

    setUploading(true);
    try {
      await api.pos.upload(location.id, formData);
      alert(t("pos_data_uploaded_successfully"));
      navigate("/pos-data");
    } catch (error) {
      console.error("Error uploading POS data:", error);
      alert(t("failed_to_upload_pos_data"));
    } finally {
      setUploading(false);
    }
  };

  return (
    <PageContent title={t("upload_pos_data")}>
      <div>
        <input
          type="file"
          accept=".csv"
          onChange={(e) => {
            console.log("$$$");
            handleFileChange(e);
          }}
          //   disabled={uploading}
        />
      </div>
      <div style={{ marginTop: "20px" }}>
        <UploadButton
          $uploading={uploading}
          onClick={handleUpload}
          disabled={uploading || !file}
        >
          <div className="upload-text">
            <SpinningUploadIcon $spinning={uploading} />
            {uploading ? t("uploading") : t("upload")}
          </div>
        </UploadButton>
      </div>
    </PageContent>
  );
}
