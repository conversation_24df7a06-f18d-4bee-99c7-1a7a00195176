.product-detail-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 24px;
}

.product-image-container {
  display: flex;
  flex-direction: column;
}
.product-info-section .checkbox-label {
    margin-right: 1em;
}

.product-image-wrapper {
  width: 100%;
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  padding: 16px;
}

.product-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.product-info-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.card-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.card-header h3 {
  margin: 0;
  color: #374151;
  font-size: 16px;
  font-weight: 600;
}

.card-content {
  padding: 16px;
}

.product-url {
  color: #3b82f6;
  text-decoration: none;
  word-break: break-all;
}

.product-url:hover {
  text-decoration: underline;
}

/* Edit form styles */
.edit-form {
  width: 100%;
}

.form-section {
  margin-bottom: 24px;
  border-radius: 8px;
}

.form-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

.form-section-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
  width: 100%;
}

.form-row > * {
  flex: 1;
}

.expandable-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding-bottom: 8px;
}

.expandable-icon {
  color: #6b7280;
}

/* Image upload styles */
.image-upload-container {
  border: 1px dashed #d1d5db;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.image-upload-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.image-upload-buttons {
  display: flex;
  gap: 8px;
}

.hidden-file-input {
  display: none;
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  height: 200px;
  border-radius: 4px;
}

.image-preview {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.image-placeholder {
  color: #9ca3af;
}

/* Ensure the modal is wide enough */
:global(.modal-large) {
  width: 800px;
  max-width: 90vw;
}

/* Modal action buttons */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  position: sticky;
  bottom: 0;
}

.checkbox-container {
  display: flex;
  gap: 20px;
  margin-top: 8px;
}

.delete-confirmation {
  padding: 16px;
  text-align: center;
}

.delete-confirmation p {
  margin: 8px 0;
}

.delete-confirmation strong {
  color: #ef4444;
}

/* Action footer for page layout */
.page-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding: 16px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

/* Make sure modal large size is appropriate */
:global(.modal.large .modal-inner) {
  width: 90%;
  max-width: 900px !important;
}

/* Responsive styles */
@media (max-width: 768px) {
  .product-detail-container {
    grid-template-columns: 1fr;
  }
  
  .form-row {
    flex-direction: column;
    gap: 8px;
  }
} 

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {

  .page-actions {
    background-color: #1f2937;
    border-color: #374151;
  }
} 

/* Add styles for AI badge */
.ai-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  margin-left: 0.5rem;
  background-color: #818cf8; /* Indigo-400 */
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.375rem;
}

/* Enhancement status badge */
.ai-enhancement-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  color: white;
  font-size: 0.75rem;
  font-weight: 500;
}

/* Spinner for loading state */
.spinner {
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #6366f1; /* Indigo-500 */
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Product detail grid layout */
.product-detail-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 1rem;
}

@media (max-width: 768px) {
  .product-detail-grid {
    grid-template-columns: 1fr;
  }
}

/* Product image styling */
.product-image-container {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
}

.product-image {
  max-width: 100%;
  max-height: 20rem;
  object-fit: contain;
}

/* Action group styling */
.action-group {
  display: flex;
  gap: 0.5rem;
} 