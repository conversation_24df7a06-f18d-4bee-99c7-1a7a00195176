.action-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.action-divider {
  width: 1px;
  height: 24px;
  background-color: var(--color-divider);
  margin: 0 0.25rem;
}

.secondary-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.dropdown-container {
  position: relative;
}

.dropdown-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.icon-wrapper {
  display: inline-flex;
  transition: transform 0.2s ease;
}

.icon-wrapper.rotate-icon {
  transform: rotate(180deg);
}

.dropdown-button svg {
  transition: transform 0.2s ease;
}

.dropdown-button svg.rotate-180 {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  margin-left: -40px;
  top: 100%;
  margin-top: 0.25rem;
  z-index: 1000;
  min-width: 200px;
  background-color: var(--color-surface);
  border: 1px solid var(--color-divider);
  border-radius: var(--border-radius);
  box-shadow: 0 4px 6px var(--color-shadow-soft);
}

.dropdown-item {
  display: block;
  width: 100%;
  text-align: left;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--color-primary);
  font-size: 0.875rem;
  transition: background-color 0.2s;
}

.dropdown-item:not(:last-child) {
  border-bottom: 1px solid var(--color-divider);
}

.dropdown-item:hover {
  background-color: var(--color-background-soft);
}

.modal-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-message {
  color: var(--color-primary-soft);
  font-size: 0.875rem;
  line-height: 1.5;
}

.info-message strong {
  font-weight: 600;
}

.warning-message {
  background-color: rgba(var(--color-yellow), 0.1);
  border: 1px solid var(--color-yellow-soft);
  color: var(--color-yellow-hard);
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius-inner);
  font-size: 0.875rem;
}

.sync-progress {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 0;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

/* Add responsive styles for action buttons */
@media (max-width: 767px) {
  /* Make the dropdown menu positioned better on mobile */
  .absolute.right-0.mt-2.w-56 {
    right: 0;
    left: auto;
    width: 240px;
    max-width: calc(100vw - 32px);
  }
  
  /* Make content in dropdown more readable on mobile */
  .px-3.py-2 {
    padding-top: 10px;
    padding-bottom: 10px;
  }
  
  /* Ensure the loading status indicator has proper spacing */
  .flex.items-center.gap-2.text-blue-600.bg-blue-50 {
    margin-top: 8px;
    width: 100%;
    justify-content: center;
  }
}

/* Make tooltip content readable on mobile */
@media (max-width: 500px) {
  .absolute.bottom-full.left-1\/2.transform.-translate-x-1\/2.-translate-y-2 {
    max-width: 200px;
    white-space: normal;
  }
}

/* AI Readiness Indicator Styles */
.ai-readiness-indicator {
  margin: 1rem 0;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid;
  transition: all 0.3s ease;
}

.ai-readiness-indicator.ai-ready {
  background-color: #f0f9ff;
  border-color: #10b981;
  color: #065f46;
}

.ai-readiness-indicator.ai-needs-improvement {
  background-color: #fff7ed;
  border-color: #f59e0b;
  color: #92400e;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.ai-status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  font-size: 1rem;
  font-weight: bold;
}

.ai-status-icon.ready {
  background-color: #10b981;
  color: white;
}

.ai-status-icon.warning {
  background-color: #f59e0b;
  color: white;
}

.ai-status-text {
  flex: 1;
  font-size: 0.9rem;
  line-height: 1.4;
}

.ai-improvement-hint {
  margin-top: 0.5rem;
  font-size: 0.85rem;
  opacity: 0.85;
  line-height: 1.4;
} 