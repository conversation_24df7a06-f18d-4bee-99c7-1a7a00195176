import {
  createBrowserRouter,
  Outlet,
  redirect,
  RouteObject,
  useNavigate,
  useParams,
} from "react-router-dom";
import { LinkedInCallback } from "react-linkedin-login-oauth2";
import api from "../api";
import { FiFile } from "react-icons/fi";

import ErrorPage from "./ErrorPage";
import { SidebarLink } from "../ui/Sidebar";
import {
  LoaderContextProvider,
  StatefulLoaderContextProvider,
} from "./LoaderContextProvider";
import {
  AdminContext,
  CampaignContext,
  JourneyContext,
  ListContext,
  LocationContext,
  UserContext,
  ProductContext,
  OrganizationContext,
} from "../contexts";
import { useState, useEffect, ReactNode } from "react";
import { Organization as OrganizationType } from "../types";
import ApiKeys from "./settings/ApiKeys";
import EmailEditor from "./campaign/editor/EmailEditor";
import Lists from "./users/Lists";
import ListDetail from "./users/ListDetail";
import Users from "./users/Users";
import Teams from "./settings/Teams";
import Subscriptions from "./settings/Subscriptions";
import UserDetail from "./users/UserDetail";
import { createStatefulRoute } from "./createStatefulRoute";
import UserDetailAttrs from "./users/UserDetailAttrs";
import UserDetailEvents from "./users/UserDetailEvents";
import UserDetailLists from "./users/UserDetailLists";
import UserDetailSubscriptions from "./users/UserDetailSubscriptions";
import UserOrders from "./users/UserOrders";
import CampaignDetail from "./campaign/CampaignDetail";
import Campaigns from "./campaign/Campaigns";
import CampaignDelivery from "./campaign/CampaignDelivery";
import CampaignPreview from "./campaign/CampaignPreview";
import CampaignOverview from "./campaign/CampaignOverview";
import CampaignDesign from "./campaign/CampaignDesign";
import Journeys from "./automation/Journeys";
import JourneyEditor from "./automation/JourneyEditor";
import LocationSettings from "./settings/LocationSettings";
import Integrations from "./settings/Integrations";
import Tags from "./settings/Tags";
import Login from "./auth/Login";
import OnboardingStart from "./auth/OnboardingStart";
import Onboarding from "./auth/Onboarding";
import OnboardingLocation from "./auth/OnboardingLocation";
import OnboardingPathSelection from "./auth/OnboardingPathSelection";
import PricingPage from "../components/onboarding/PricingPage";
import { ComingSoonPage } from "./ErrorPage";
import Dashboard from "./dashboard/Dashboard";
import { Locations } from "./location/Locations";
import { getRecentLocations, pushRecentLocation } from "../utils";
import Performance from "./organization/Performance";
import Settings from "./settings/Settings";
import LocationSidebar from "./location/LocationSidebar";
import Admins from "./organization/Admins";
import OrganizationSettings from "./organization/Settings";
import Locales from "./settings/Locales";
import JourneyUserEntrances from "./automation/JourneyUserEntrances";
import UserDetailJourneys from "./users/UserDetailJourneys";
import EntranceDetails from "./automation/EntranceDetails";
import { Translation } from "react-i18next";
import ChatPage from "./chat/ChatPage";
import POSDataPage from "./pos/POSDataPage";
import AdvancedSettings from "./settings/AdvancedSettings";
import Insights from "./insights/Insights";
import Products from "./products/Products";
import ProductDetail from "./products/ProductDetail";
import AddProduct from "./products/AddProduct";
import { FileManager } from "./files";
import AppLayout from "./layouts/AppLayout";
import SystemHealth from "./health/SystemHealth";
import ChatSettings from "./settings/ChatSettings";
import ApiKeysPage from "./settings/ApiKeysPage";
import Organization from "./organization/Organization";
import CompetitorsPage from "./competitors";
import MCBALandingPage from "./mcba/MCBALandingPage";
import Orders from "./orders/Orders";
import OrderDetail from "./orders/OrderDetail";
import Events from "./events/Events";
import InsightPatterns from "./InsightPatterns";

export const useRoute = (includeLocation = true) => {
  const { locationId = "" } = useParams();
  const navigate = useNavigate();
  const parts: string[] = [];
  if (includeLocation) {
    parts.push("locations", locationId);
  }
  return (path: string) => {
    parts.push(path);
    navigate("/" + parts.join("/"));
  };
};

export interface RouterProps {
  routes?: (routes: RouteObject[]) => RouteObject[];
  locationSidebarLinks?: <T extends SidebarLink>(links: T[]) => T[];
  orgSidebarLinks?: <T extends SidebarLink>(links: T[]) => T[];
}

// Add a new OrganizationContextProvider component
const OrganizationContextProvider = ({ children }: { children: ReactNode }) => {
  const [organization, setOrganization] = useState<OrganizationType>(
    {} as OrganizationType
  );
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchOrganization = async () => {
      try {
        const data = await api.organizations.get();
        setOrganization(data);
      } catch (error) {
        console.error("Failed to fetch organization:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganization();
  }, []);

  if (isLoading) {
    // You could add a loading spinner here if needed
    return null;
  }

  return (
    <OrganizationContext.Provider value={[organization, setOrganization]}>
      {children}
    </OrganizationContext.Provider>
  );
};

export const createRouter = ({
  routes = (routes) => routes,
  locationSidebarLinks = (links) => links,
  orgSidebarLinks = (links) => links,
}: RouterProps) =>
  createBrowserRouter(
    routes([
      {
        path: "/login",
        element: <Login />,
      },
      {
        path: "/mcba",
        element: <MCBALandingPage />,
      },
      {
        path: "oauth/linkedin",
        element: <LinkedInCallback />,
      },
      {
        path: "upgrade",
        loader: async () => await api.profile.get(),
        element: (
          <LoaderContextProvider context={AdminContext}>
            <OrganizationContextProvider>
              <PricingPage />
            </OrganizationContextProvider>
          </LoaderContextProvider>
        ),
      },
      {
        path: "*",
        errorElement: <ErrorPage />,
        loader: async () => await api.profile.get(),
        element: (
          <LoaderContextProvider context={AdminContext}>
            <OrganizationContextProvider>
              <Outlet />
            </OrganizationContextProvider>
          </LoaderContextProvider>
        ),
        children: [
          {
            index: true,
            loader: async () => {
              const recents = getRecentLocations();
              if (recents.length) {
                return redirect(`locations/${recents[0].id}`);
              }
              return redirect("organization");
            },
            element: <Locations />,
          },
          {
            path: "onboarding",
            element: <Onboarding />,
            children: [
              {
                index: true,
                element: <OnboardingStart />,
              },
              {
                path: "path-selection",
                element: <OnboardingPathSelection />,
              },
              {
                path: "location",
                element: <OnboardingLocation />,
              },
            ],
          },
          {
            path: "organization",
            loader: async () => await api.organizations.get(),
            element: <Organization filter={orgSidebarLinks} />,
            children: [
              {
                index: true,
                loader: async () => {
                  return redirect("locations");
                },
              },
              {
                path: "locations",
                element: <Locations />,
              },
              {
                path: "admins",
                element: <Admins />,
              },
              {
                path: "performance",
                element: <Performance />,
              },
              {
                path: "settings",
                element: <OrganizationSettings />,
              },
            ],
          },
          {
            path: "locations/:locationId",
            loader: async ({ params: { locationId = "" } }) => {
              const location = await api.locations.get(locationId);
              pushRecentLocation(location.id);
              return location;
            },
            element: (
              <StatefulLoaderContextProvider context={LocationContext}>
                <AppLayout />
              </StatefulLoaderContextProvider>
            ),
            children: [
              {
                index: true, //default page
                loader: async () => {
                  return redirect("dashboard");
                },
              },
              createStatefulRoute({
                path: "chat",
                apiPath: api.campaigns,
                element: <ChatPage />,
              }),
              createStatefulRoute({
                path: "dashboard",
                apiPath: api.campaigns,
                element: <Dashboard />,
              }),
              createStatefulRoute({
                path: "health",
                apiPath: api.campaigns,
                element: <SystemHealth />,
              }),
              createStatefulRoute({
                path: "campaigns",
                apiPath: api.campaigns,
                element: <Campaigns />,
              }),
              createStatefulRoute({
                path: "campaigns/:entityId",
                apiPath: api.campaigns,
                context: CampaignContext,
                element: <CampaignDetail />,
                children: [
                  {
                    index: true,
                    element: <CampaignOverview />,
                  },
                  {
                    path: "design",
                    element: <CampaignDesign />,
                  },
                  {
                    path: "delivery",
                    element: <CampaignDelivery />,
                  },
                  {
                    path: "preview",
                    element: <CampaignPreview />,
                  },
                ],
              }),
              createStatefulRoute({
                path: "campaigns/:entityId/editor",
                apiPath: api.campaigns,
                context: CampaignContext,
                element: <EmailEditor />,
              }),
              createStatefulRoute({
                path: "automations",
                apiPath: api.automations,
                element: <Journeys />,
              }),
              createStatefulRoute({
                path: "automations/:entityId",
                apiPath: api.automations,
                context: JourneyContext,
                element: <JourneyEditor />,
              }),
              createStatefulRoute({
                path: "automations/:entityId/users",
                apiPath: api.automations,
                context: JourneyContext,
                element: <JourneyUserEntrances />,
              }),
              createStatefulRoute({
                path: "automations/:entityId/users/:entranceId",
                apiPath: api.automations,
                context: JourneyContext,
                element: <EntranceDetails />,
              }),
              createStatefulRoute({
                path: "sales-data",
                apiPath: api.campaigns,
                element: <POSDataPage />,
              }),
              createStatefulRoute({
                path: "insights",
                apiPath: api.campaigns,
                element: <Insights />,
              }),
              createStatefulRoute({
                path: "competitors",
                apiPath: api.campaigns,
                element: <CompetitorsPage />,
              }),
              createStatefulRoute({
                path: "users",
                apiPath: api.users,
                element: <Users />,
              }),
              createStatefulRoute({
                path: "users/:entityId",
                apiPath: api.users,
                context: UserContext,
                element: <UserDetail />,
                children: [
                  {
                    index: true,
                    element: <UserDetailAttrs />,
                  },
                  {
                    path: "events",
                    element: <UserDetailEvents />,
                  },
                  {
                    path: "lists",
                    element: <UserDetailLists />,
                  },
                  {
                    path: "subscriptions",
                    element: <UserDetailSubscriptions />,
                  },
                  {
                    path: "orders",
                    element: <UserOrders />,
                  },
                  {
                    path: "automations",
                    element: <UserDetailJourneys />,
                  },
                ],
              }),
              createStatefulRoute({
                path: "lists",
                apiPath: api.lists,
                element: <Lists />,
              }),
              createStatefulRoute({
                path: "lists/:entityId",
                apiPath: api.lists,
                context: ListContext,
                element: <ListDetail />,
              }),
              createStatefulRoute({
                path: "products",
                apiPath: api.products,
                element: <Products />,
              }),
              createStatefulRoute({
                path: "products/add",
                apiPath: api.products,
                element: <AddProduct />,
              }),
              createStatefulRoute({
                path: "products/:entityId",
                apiPath: api.products,
                context: ProductContext,
                element: <ProductDetail />,
              }),
              createStatefulRoute({
                path: "orders",
                apiPath: api.orders,
                element: <Orders />,
              }),
              createStatefulRoute({
                path: "orders/:orderId",
                apiPath: api.orders,
                element: <OrderDetail />,
              }),
              {
                path: "events",
                element: <Events />,
              },
              {
                path: "insight-patterns",
                element: <InsightPatterns />,
              },
              createStatefulRoute({
                path: "files",
                apiPath: api.campaigns,
                element: <FileManager />,
              }),
              {
                path: "settings",
                element: <Settings />,
                children: [
                  {
                    index: true,
                    element: <LocationSettings />,
                  },
                  {
                    path: "team",
                    element: <Teams />,
                  },
                  {
                    path: "locales",
                    element: <Locales />,
                  },
                  {
                    path: "api-keys",
                    element: <ApiKeysPage />,
                  },
                  {
                    path: "integrations",
                    element: <Integrations />,
                  },
                  {
                    path: "subscriptions",
                    element: <Subscriptions />,
                  },
                  {
                    path: "tags",
                    element: <Tags />,
                  },
                  {
                    path: "performance",
                    element: <Performance />,
                  },
                  {
                    path: "advanced",
                    element: <AdvancedSettings />,
                  },
                  {
                    path: "chat",
                    element: <ChatSettings />,
                  },
                ],
              },
            ],
          },
          {
            path: "*",
            element: <ErrorPage status={404} />,
            errorElement: <ErrorPage />,
          },
        ],
      },
    ])
  );
