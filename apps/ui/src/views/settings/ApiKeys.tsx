import { useCallback, useContext, useState, useMemo } from "react";
import api from "../../api";
import { LocationContext } from "../../contexts";
import { LocationApiKey, locationRoles } from "../../types";
import Button from "../../ui/Button";
import RadioInput from "../../ui/form/RadioInput";
import TextInput from "../../ui/form/TextInput";
import FormWrapper from "../../ui/form/FormWrapper";
import Modal from "../../ui/Modal";
import { SearchTable, useSearchTableState } from "../../ui/SearchTable";
import { ArchiveIcon, CopyIcon, PlusIcon } from "../../ui/icons";
import Menu, { MenuItem } from "../../ui/Menu";
import { SingleSelect } from "../../ui/form/SingleSelect";
import { snakeToTitle } from "../../utils";
import { toast } from "react-hot-toast/headless";
import Alert from "../../ui/Alert";
import { useTranslation } from "react-i18next";
import { createSafeApiCall } from "../../utils/api";

export default function LocationApiKeys() {
  const { t } = useTranslation();
  const [location] = useContext(LocationContext);
  const [editing, setEditing] = useState<null | Partial<LocationApiKey>>(null);

  // Create safe API functions - memoized to prevent recreation on each render
  const safeApiFunctions = useMemo(
    () => ({
      search: createSafeApiCall(location, api.apiKeys.search),
      delete: createSafeApiCall(location, api.apiKeys.delete),
      update: createSafeApiCall(location, api.apiKeys.update),
      create: createSafeApiCall(location, api.apiKeys.create),
    }),
    [location]
  );

  // Use the memoized function in the callback
  const searchCallback = useCallback(
    async (params: any) => {
      return await safeApiFunctions.search(params);
    },
    [safeApiFunctions]
  );

  const state = useSearchTableState(searchCallback);

  const handleArchive = async (id: number) => {
    if (
      confirm(
        "Are you sure you want to archive this key? All clients using the key will immediately be unable to access the API."
      )
    ) {
      await safeApiFunctions.delete(id);
      await state.reload();
    }
  };

  const handleCopy = async (event: React.MouseEvent, value: string) => {
    await navigator.clipboard.writeText(value);
    toast.success("Copied API Key");
  };

  return (
    <>
      <SearchTable
        {...state}
        columns={[
          { key: "name" },
          {
            key: "scope",
            cell: ({ item }) => snakeToTitle(item.scope),
          },
          {
            key: "role",
            cell: ({ item }) =>
              item.scope === "public"
                ? undefined
                : snakeToTitle(item.role ?? ""),
          },
          {
            key: "value",
            cell: ({ item }) => (
              <div className="cell-content flex items-center">
                <code className="px-2 py-1 rounded font-mono text-xs truncate max-w-[200px]">
                  {item.value}
                </code>
                <Button
                  icon={<CopyIcon />}
                  size="small"
                  variant="plain"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCopy(e, item.value);
                  }}
                  title={t("copy_to_clipboard")}
                />
              </div>
            ),
          },
          { key: "description" },
          {
            key: "options",
            cell: ({ item: { id } }) => (
              <Menu size="small">
                <MenuItem onClick={async () => await handleArchive(id)}>
                  <ArchiveIcon />
                  Archive
                </MenuItem>
              </Menu>
            ),
          },
        ]}
        itemKey={({ item }) => item.id}
        onSelectRow={setEditing}
        title="API Keys"
        actions={
          <Button
            icon={<PlusIcon />}
            size="small"
            onClick={() => setEditing({ scope: "public", role: "support" })}
          >
            Create Key
          </Button>
        }
      />
      <Modal
        title={editing ? "Update API Key" : "Create API Key"}
        open={Boolean(editing)}
        onClose={() => setEditing(null)}
      >
        {editing?.value && (
          <Alert variant="plain" title="API Key Value">
            <div className="flex items-center justify-between mt-2">
              <code className=" px-3 py-2 rounded font-mono text-sm break-all">
                {editing.value}
              </code>
              <Button
                icon={<CopyIcon />}
                size="small"
                variant="secondary"
                onClick={(event) => {
                  event.preventDefault();
                  event.stopPropagation();
                  if (editing.value) {
                    navigator.clipboard.writeText(editing.value).then(() => {
                      toast.success("Copied API Key");
                    });
                  }
                }}
                className="ml-2"
              >
                {t("copy")}
              </Button>
            </div>
            <p className="text-sm text-gray-600 mt-2">
              {t(
                "api_key_security_warning",
                "This key is only shown once. Make sure to copy it now as you won't be able to retrieve it later."
              )}
            </p>
          </Alert>
        )}
        {editing && (
          <FormWrapper<LocationApiKey>
            onSubmit={async ({ id, name, description, scope, role }) => {
              if (id) {
                await safeApiFunctions.update(id, {
                  name,
                  description,
                  role,
                });
              } else {
                await safeApiFunctions.create({
                  name,
                  description,
                  scope,
                  role,
                });
              }
              await state.reload();
              setEditing(null);
            }}
            defaultValues={editing}
            submitLabel={editing?.id ? "Update Key" : "Create Key"}
          >
            {(form) => {
              const scope = form.watch("scope");
              return (
                <>
                  <TextInput.Field
                    form={form}
                    name="name"
                    label="Name"
                    required
                  />
                  <TextInput.Field
                    form={form}
                    name="description"
                    label="Description"
                  />
                  <RadioInput.Field
                    form={form}
                    name="scope"
                    label="Scope"
                    options={[
                      { key: "public", label: "Public" },
                      { key: "secret", label: "Secret" },
                    ]}
                    disabled={!!editing?.id}
                  />
                  <div className="text-sm text-gray-600 mb-4 -mt-2">
                    {scope === "public"
                      ? t(
                          "public_key_description",
                          "Public keys have read-only access to public endpoints. Use these for displaying data in applications."
                        )
                      : t(
                          "secret_key_description",
                          "Secret keys have write access based on role. Use these for admin operations and data uploads."
                        )}
                  </div>
                  {scope === "secret" && (
                    <SingleSelect.Field
                      form={form}
                      name="role"
                      label="Role"
                      options={locationRoles}
                      getOptionDisplay={snakeToTitle}
                      required
                    />
                  )}
                </>
              );
            }}
          </FormWrapper>
        )}
      </Modal>
    </>
  );
}
