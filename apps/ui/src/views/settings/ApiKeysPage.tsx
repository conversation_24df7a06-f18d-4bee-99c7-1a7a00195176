import { useTranslation } from "react-i18next";
import Heading from "../../ui/Heading";
import LocationApiKeys from "./ApiKeys";

export default function ApiKeysPage() {
  const { t } = useTranslation();

  return (
    <div className="space-y-6">
      <div>
        <Heading size="h3" title={t("api_keys")} />
        <p className="text-sm text-gray-600">
          {t(
            "api_keys_description",
            "API keys allow external applications to access data from this location. Public keys have read-only access, while secret keys have write access based on their assigned role."
          )}
        </p>
      </div>

      <div className="mt-4">
        <LocationApiKeys />
      </div>
    </div>
  );
}
