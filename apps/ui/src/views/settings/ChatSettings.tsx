import React from "react";
import SystemPromptEditor from "../../components/SystemPromptEditor";

const ChatSettings: React.FC = () => {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold">Chat Settings</h1>

      <div className="rounded-lg shadow-sm mb-6">
        <div className="border-b pb-3 mb-3">
          <h2 className="text-xl font-semibold">Custom System Prompts</h2>
          <p className="text-gray-600 text-sm">
            Customize the system prompts used in AI conversations. Active
            prompts will be used for all your chats.
          </p>
        </div>
        <div className="p-2">
          <SystemPromptEditor />
        </div>
      </div>

      <div className="mt-6">
        <h3 className="text-lg font-medium mb-2">About System Prompts</h3>
        <p className="text-gray-600">
          System prompts provide foundational instructions to the AI about how
          it should behave and respond. Customizing these prompts allows you to
          tailor the AI's knowledge, tone, and behavior to your specific needs.
        </p>

        <div className="bg-blue-50 p-4 rounded-lg mt-4">
          <h4 className="font-medium text-blue-800">
            Tips for effective prompts:
          </h4>
          <ul className="list-disc pl-5 mt-2 text-blue-800">
            <li>Be specific about the AI's role and purpose</li>
            <li>Include relevant domain knowledge and background</li>
            <li>Set clear boundaries for what is and isn't allowed</li>
            <li>Define the tone and style of communication</li>
            <li>Explain how to handle specific types of queries</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ChatSettings;
