import { useCallback, useContext, useState } from "react";
import api from "../../api";
import { AdminContext, LocationContext } from "../../contexts";
import { LocationAdmin } from "../../types";
import Button from "../../ui/Button";
import { ArchiveIcon, EditIcon, PlusIcon, EmailIcon } from "../../ui/icons";
import Menu, { MenuItem } from "../../ui/Menu";
import { SearchTable, useSearchTableState } from "../../ui/SearchTable";
import { snakeToTitle } from "../../utils";
import TeamInvite from "./TeamInvite";
import { useTranslation } from "react-i18next";
import { toast } from "react-hot-toast";

type EditFormData = Pick<LocationAdmin, "admin_id" | "role"> & { id?: number };

export default function Teams() {
  const { t } = useTranslation();
  const admin = useContext(AdminContext);
  const [location] = useContext(LocationContext);
  const state = useSearchTableState(
    useCallback(
      async (params) => await api.locationAdmins.search(location.id, params),
      [location]
    )
  );
  const [editing, setEditing] = useState<Partial<EditFormData>>();
  const [resendingInvitation, setResendingInvitation] = useState<number | null>(null);

  const handleResendInvitation = async (targetLocationAdmin: LocationAdmin) => {
    // Prevent resending invitation to current user
    if (targetLocationAdmin.admin_id === admin?.id) {
      toast.error(t("cannot_resend_invitation_to_yourself"));
      return;
    }

    setResendingInvitation(targetLocationAdmin.admin_id);
    try {
      const result = await api.locationAdmins.resendInvitation(location.id, targetLocationAdmin.admin_id);
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Failed to resend location invitation:", error);
      toast.error(t("failed_to_resend_invitation"));
    } finally {
      setResendingInvitation(null);
    }
  };

  return (
    <>
      <SearchTable
        {...state}
        title={t("team")}
        actions={
          <Button
            icon={<PlusIcon />}
            size="small"
            onClick={() =>
              setEditing({
                admin_id: undefined,
                role: "support",
              })
            }
          >
            {t("add_team_member")}
          </Button>
        }
        columns={[
          { key: "first_name", title: t("first_name") },
          { key: "last_name", title: t("last_name") },
          { key: "email", title: t("email") },
          {
            key: "role",
            title: t("role"),
            cell: ({ item }) => snakeToTitle(item.role),
          },
          {
            key: "options",
            title: t("options"),
            cell: ({ item }) => (
              <Menu size="small">
                {/* Only show resend invitation for other admins, not current user */}
                {item.admin_id !== admin?.id && (
                  <MenuItem
                    onClick={() => {
                      if (resendingInvitation !== item.admin_id) {
                        handleResendInvitation(item);
                      }
                    }}
                  >
                    <EmailIcon />
                    {resendingInvitation === item.admin_id
                      ? t("sending_invitation")
                      : t("resend_invitation")
                    }
                  </MenuItem>
                )}
                <MenuItem
                  onClick={async () => {
                    await api.locationAdmins.remove(
                      item.location_id,
                      item.admin_id
                    );
                    await state.reload();
                  }}
                >
                  <ArchiveIcon /> {t("remove")}
                </MenuItem>
                <MenuItem onClick={() => setEditing(item)}>
                  <EditIcon /> {t("edit")}
                </MenuItem>
              </Menu>
            ),
          },
        ]}
        itemKey={({ item }) => item.id}
        onSelectRow={setEditing}
        enableSearch
      />

      <TeamInvite
        member={editing}
        onMember={async () => {
          await state.reload();
          setEditing(undefined);
        }}
        open={Boolean(editing)}
        onClose={() => setEditing(undefined)}
      />
    </>
  );
}
