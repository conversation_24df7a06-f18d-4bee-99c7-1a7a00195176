/** @type {import('tailwindcss').Config} */
export const content = ["./src/**/*.{js,jsx,ts,tsx}", "./public/index.html"];
export const theme = {
  extend: {
    fontFamily: {
      sans: [
        "Inter",
        "Helvetica Neue",
        "-apple-system",
        "BlinkMacSystemFont",
        "Segoe UI",
        "Roboto",
        "sans-serif",
      ],
      roboto: ["Roboto", "sans-serif"],
    },
    colors: {
      primary: "var(--color-primary)",
      "primary-soft": "var(--color-primary-soft)",
      background: "var(--color-background)",
      "background-soft": "var(--color-background-soft)",
      surface: "var(--color-surface)",
      "surface-secondary": "var(--color-surface-secondary)",
      divider: "var(--color-divider)",
      grey: {
        DEFAULT: "var(--color-grey)",
        soft: "var(--color-grey-soft)",
        hard: "var(--color-grey-hard)",
      },
      blue: {
        DEFAULT: "var(--color-blue)",
        soft: "var(--color-blue-soft)",
        hard: "var(--color-blue-hard)",
      },
      red: {
        DEFAULT: "var(--color-red)",
        soft: "var(--color-red-soft)",
        hard: "var(--color-red-hard)",
      },
      yellow: {
        DEFAULT: "var(--color-yellow)",
        soft: "var(--color-yellow-soft)",
        hard: "var(--color-yellow-hard)",
      },
      green: {
        DEFAULT: "var(--color-green)",
        soft: "var(--color-green-soft)",
        hard: "var(--color-green-hard)",
      },
    },
    borderRadius: {
      DEFAULT: "var(--border-radius)",
      inner: "var(--border-radius-inner)",
      outer: "var(--border-radius-outer)",
    },
    screens: {
      xs: "200px",
    },
  },
};
export const plugins = [];
