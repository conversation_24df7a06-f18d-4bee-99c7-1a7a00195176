-- Enable PostGIS extension if not already enabled
CREATE EXTENSION IF NOT EXISTS postgis;

-- Function to find retailers within a radius
CREATE OR REPLACE FUNCTION find_retailers_within_radius(
  lat float,
  lng float,
  radius_miles float,
  max_results int
)
RETURNS TABLE (
  -- Include all columns from retailers table here
  retailer_id text,
  name text,
  dispensary_name text,
  address text,
  city text,
  state text,
  zip_code text,
  phone text,
  email text,
  website text,
  logo_url text,
  latitude text,
  longitude text,
  created_at timestamp,
  updated_at timestamp,
  -- Add any other columns from your retailers table
  -- ...
  
  -- Include the distance as a separate column
  distance float
) AS $$
DECLARE
  meters float;
BEGIN
  -- Convert miles to meters (1 mile ≈ 1609.34 meters)
  meters := radius_miles * 1609.34;
  
  -- Create a geography point from the input coordinates
  RETURN QUERY
  SELECT 
    r.*,
    -- Calculate distance in miles and include it in the results
    ST_Distance(
      ST_SetSRID(ST_MakePoint(lng, lat), 4326)::geography,
      ST_SetSRID(ST_MakePoint(r.longitude::float, r.latitude::float), 4326)::geography
    ) / 1609.34 as distance
  FROM retailers r
  WHERE 
    -- Use ST_DWithin for efficient radius search
    ST_DWithin(
      ST_SetSRID(ST_MakePoint(lng, lat), 4326)::geography, 
      ST_SetSRID(ST_MakePoint(r.longitude::float, r.latitude::float), 4326)::geography,
      meters
    )
  ORDER BY distance ASC
  LIMIT max_results;
END;
$$ LANGUAGE plpgsql;

-- Function to find retailers IDs within a radius with distances
CREATE OR REPLACE FUNCTION find_retailer_ids_within_radius(
  lat float,
  lng float,
  radius_miles float,
  max_results int
)
RETURNS TABLE (
  retailer_id text,
  distance float
) AS $$
DECLARE
  meters float;
BEGIN
  -- Convert miles to meters (1 mile ≈ 1609.34 meters)
  meters := radius_miles * 1609.34;
  
  RETURN QUERY
  SELECT 
    r.retailer_id,
    -- Calculate distance in miles
    ST_Distance(
      ST_SetSRID(ST_MakePoint(lng, lat), 4326)::geography,
      ST_SetSRID(ST_MakePoint(r.longitude::float, r.latitude::float), 4326)::geography
    ) / 1609.34 as distance
  FROM retailers r
  WHERE 
    -- Use ST_DWithin for efficient radius search
    ST_DWithin(
      ST_SetSRID(ST_MakePoint(lng, lat), 4326)::geography, 
      ST_SetSRID(ST_MakePoint(r.longitude::float, r.latitude::float), 4326)::geography,
      meters
    )
  ORDER BY distance ASC
  LIMIT max_results;
END;
$$ LANGUAGE plpgsql; 