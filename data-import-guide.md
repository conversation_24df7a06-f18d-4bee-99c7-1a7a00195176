# Data Import Tool: Technical Guide

## Overview

Our data import tool is designed to streamline the process of importing various types of data into our platform. The system intelligently detects, normalizes, and processes your data files to ensure seamless integration with our database.

## Supported File Formats

We currently support the following file formats:

- CSV files (`.csv`)
- Excel spreadsheets (`.xlsx`, `.xls`)
- Direct JSON data uploads via API

## Data Types We Support

Our system can import six specific types of data:

1. **Products**: Cannabis product inventory and details
2. **POS (Point of Sale)**: Sales transaction data
3. **Customers**: User profiles and account information
4. **Reviews**: Customer feedback and ratings
5. **Retailers**: Dispensary/store information
6. **COA (Certificate of Analysis)**: Laboratory test results for cannabis products

## How The Import Process Works

1. **File Upload**: You upload your data file through our interface or API
2. **Auto-Detection**: Our system analyzes your file headers to determine data type
3. **Field Mapping**: We automatically map your columns to our standardized fields
4. **Validation**: Each row is validated for required fields and data types
5. **Processing**: Valid records are processed in batches for optimal performance
6. **Error Reporting**: Detailed error reports identify problematic rows and issues
7. **Vectorization**: Data is indexed in our vector database for enhanced search capabilities

## Field Mappings

Our system recognizes various column names for each data type. Here are the key fields for each type:

### Product Data Fields

| Your Column Name (examples)      | Our Field           | Required | Type    |
| -------------------------------- | ------------------- | -------- | ------- |
| SKU, Product SKU, meta_sku       | meta_sku            | Yes      | String  |
| Retailer ID, Store ID            | retailer_id         | Yes      | String  |
| Raw Product Name, Original Name  | raw_product_name    | Yes      | String  |
| Product Name, Name               | product_name        | Yes      | String  |
| Medical, Is Medical              | medical             | Yes      | Boolean |
| Recreational, Is Recreational    | recreational        | Yes      | Boolean |
| Location ID                      | location_id         | Yes      | Number  |
| Category, Product Category       | category            | No       | String  |
| Subcategory, Product Subcategory | subcategory         | No       | String  |
| THC %, THC Percentage            | percentage_thc      | No       | Number  |
| CBD %, CBD Percentage            | percentage_cbd      | No       | Number  |
| Product Description, description | product_description | No       | String  |
| Price, Latest Price              | latest_price        | No       | Number  |
| Wholesale Price                  | wholesale_price     | No       | Number  |
| Retail Price                     | retail_price        | No       | Number  |
| MSRP                             | msrp                | No       | Number  |
| Profit Margin                    | profit_margin       | No       | Number  |
| Grower, Cultivator               | grower_name         | No       | String  |
| Cultivar, Strain                 | cultivar            | No       | String  |
| Batch Number, Lot ID             | batch_number        | No       | String  |
| Harvest Date                     | harvest_date        | No       | Date    |
| COA Link, Lab Results URL        | coa_url             | No       | String  |
| Brand Name                       | brand_name          | No       | String  |
| Brand ID                         | brand_id            | No       | Number  |
| External ID                      | external_id         | No       | String  |

### POS Data Fields

| Your Column Name (examples)  | Our Field       | Required | Type   |
| ---------------------------- | --------------- | -------- | ------ |
| Order Date, date             | order_date      | Yes      | Date   |
| Location Name, location_name | location_name   | No       | String |
| Master Category, category    | master_category | No       | String |
| Customer Type, customer_type | customer_type   | No       | String |
| Budtender Name, employee     | budtender_name  | No       | String |
| Gross Sales, sales           | gross_sales     | No       | Number |
| Net Sales, net_sales         | net_sales       | No       | Number |
| Invoice Total, total         | invoice_total   | No       | Number |
| Customer Name, customer      | customer_name   | No       | String |
| Product Name, product        | product_name    | No       | String |
| Wholesale Cost               | wholesale_cost  | No       | Number |
| Profit Margin                | profit_margin   | No       | Number |

### COA (Certificate of Analysis) Data Fields

| Your Column Name (examples) | Our Field             | Required | Type    |
| --------------------------- | --------------------- | -------- | ------- |
| Product Name, Name          | product_name          | Yes      | String  |
| Product Type, Type          | product_type          | Yes      | String  |
| Batch Number, Lot Number    | batch_number          | Yes      | String  |
| Sample ID                   | sample_id             | Yes      | String  |
| Production Date             | production_date       | No       | Date    |
| Test Date                   | test_date             | Yes      | Date    |
| Lab Name                    | lab_name              | Yes      | String  |
| Lab License Number          | lab_license           | No       | String  |
| COA URL, Certificate URL    | coa_url               | No       | String  |
| THC, D9-THC                 | thc_percent           | No       | Number  |
| THCA                        | thca_percent          | No       | Number  |
| CBD                         | cbd_percent           | No       | Number  |
| CBDA                        | cbda_percent          | No       | Number  |
| CBG                         | cbg_percent           | No       | Number  |
| CBGA                        | cbga_percent          | No       | Number  |
| CBN                         | cbn_percent           | No       | Number  |
| CBC                         | cbc_percent           | No       | Number  |
| Total THC                   | total_thc             | No       | Number  |
| Total CBD                   | total_cbd             | No       | Number  |
| Total Cannabinoids          | total_cannabinoids    | No       | Number  |
| Myrcene                     | terpene_myrcene       | No       | Number  |
| Limonene                    | terpene_limonene      | No       | Number  |
| Caryophyllene               | terpene_caryophyllene | No       | Number  |
| Linalool                    | terpene_linalool      | No       | Number  |
| Pinene                      | terpene_pinene        | No       | Number  |
| Humulene                    | terpene_humulene      | No       | Number  |
| Terpinolene                 | terpene_terpinolene   | No       | Number  |
| Total Terpenes              | total_terpenes        | No       | Number  |
| Butane                      | solvent_butane        | No       | Number  |
| Propane                     | solvent_propane       | No       | Number  |
| Ethanol                     | solvent_ethanol       | No       | Number  |
| Solvents Pass/Fail          | solvents_pass         | No       | Boolean |
| Lead                        | metal_lead            | No       | Number  |
| Mercury                     | metal_mercury         | No       | Number  |
| Arsenic                     | metal_arsenic         | No       | Number  |
| Cadmium                     | metal_cadmium         | No       | Number  |
| Metals Pass/Fail            | metals_pass           | No       | Boolean |
| E. coli                     | microbial_ecoli       | No       | Boolean |
| Salmonella                  | microbial_salmonella  | No       | Boolean |
| Aspergillus                 | microbial_aspergillus | No       | Boolean |
| Total Yeast & Mold          | yeast_mold_count      | No       | Number  |
| Microbials Pass/Fail        | microbials_pass       | No       | Boolean |
| Moisture Content            | moisture_content      | No       | Number  |
| Water Activity              | water_activity        | No       | Number  |
| Overall Pass/Fail           | overall_pass          | No       | Boolean |

## Error Handling and Reporting

Our system provides detailed error reporting for problematic data:

1. **Row-Level Errors**: Each error is linked to the specific row number in your file
2. **Missing Required Fields**: We identify when required fields are missing
3. **Data Type Validation**: We report when data doesn't match expected types (dates, numbers, etc.)
4. **Batch Processing**: Even if some rows fail, others will still be processed

Example error report:

Data validation errors:
Row 2: Missing required field: meta_sku
Row 5: Invalid date format in field: order_date
Row 8: Invalid number format in field: gross_sales

## AI Enhancement (Product Data Only)

For product data, we offer optional AI enhancement that automatically fills in missing information:

| Fields Enhanced by AI | Description                            |
| --------------------- | -------------------------------------- |
| product_name          | Standardized product name              |
| category              | Standard cannabis category             |
| subcategory           | Specific type within category          |
| product_description   | Detailed product description           |
| product_tags          | Relevant tags for the product          |
| percentage_thc        | Estimated THC percentage               |
| percentage_cbd        | Estimated CBD percentage               |
| mood                  | Array of moods this product may induce |
| effects               | Detailed effects information           |

The AI enhancement process:

1. Identifies which fields are missing in your product data
2. Uses existing product information to predict missing values
3. Generates appropriate content in proper format
4. Adds an enhancement status to track which products were enhanced

## Wholesale and Retail Price Tracking

Our system supports tracking both wholesale and retail pricing, enabling:

1. **Margin Calculation**: Automated calculation of profit margins
2. **Cost Analysis**: Track cost of goods against retail pricing
3. **Price Optimization**: Analyze price points across products and categories
4. **Historical Tracking**: Monitor price changes over time

## Vector Database Integration

Imported data is automatically indexed in our vector database (unless disabled), which enables:

1. **Semantic search** across your product catalog
2. **Intelligent product recommendations**
3. **Content-based similarity matching**
4. **Enhanced searchability** for customers

The vectorization process:

1. Each product is processed and converted into high-dimensional vector embeddings
2. These embeddings capture the semantic meaning of product descriptions and attributes
3. Products can be retrieved based on semantic similarity, not just keyword matching
4. You can check vectorization status through the API to ensure all products are indexed

> **Note:** You can disable vectorization during import by setting `reindex=false` in your API request.
> If you need to manually re-vectorize your products later, you can do so through the `/products/re-vectorize` endpoint.

## Importing Tips

1. **Required Fields**: Ensure your file includes all required fields for the data type
2. **Location ID**: Every product must be associated with a location ID
3. **Header Names**: You don't need exact column names - our system recognizes variations
4. **Dates**: We support common date formats (MM/DD/YYYY, YYYY-MM-DD, etc.)
5. **File Size**: There's no hard limit on file size, but larger files (>10,000 rows) may take longer to process
6. **Failed Rows**: Even if some rows fail validation, valid rows will still be imported
7. **COA Data**: When importing lab results, linking to the original product using batch numbers ensures proper association

## API Integration

For programmatic imports, our API endpoints support:

- Direct JSON data uploads
- File uploads via multipart/form-data
- Control parameters for AI enhancement and reindexing

Example API request:

```
POST /products/upload?enhance_with_ai=true&reindex=true
```

For direct JSON data imports:

```
POST /products/upload
Content-Type: application/json

{
  "product_data": [
    {
      "meta_sku": "SKU12345",
      "retailer_id": "STORE001",
      "raw_product_name": "Blue Dream 3.5g",
      "product_name": "Blue Dream",
      "medical": true,
      "recreational": true,
      "location_id": 123,
      ...other fields...
    }
  ],
  "enhance_with_ai": true,
  "reindex": true
}
```

## Key Benefits

- **Universal Format Support**: Import data from CSV files, Excel spreadsheets, or direct JSON uploads
- **Intelligent Mapping**: Automatic detection of data types and field matching
- **Zero Downtime**: Background processing ensures your platform remains responsive
- **Error Resilience**: Problematic records are flagged without failing the entire import
- **AI Enhancement**: Optional AI-powered enrichment of product data to fill gaps and improve quality
- **Automatic Vectorization**: Products are automatically indexed for enhanced searchability

## Supported Data Types

Our platform seamlessly imports:

- **Product Catalogs**: Complete inventory with details, categories, and pricing
- **POS Transactions**: Sales data including orders, revenue, and customer interactions
- **Customer Records**: User profiles and purchase history
- **Reviews & Feedback**: Customer sentiments and ratings
- **Retailer Information**: Store details and location data

## Getting Started

To import your data:

1. Prepare your CSV or Excel file with appropriate headers
2. Navigate to the import section for your data type
3. Upload your file and select any options (like AI enhancement)
4. Review validation results
5. Confirm the import to process valid records
