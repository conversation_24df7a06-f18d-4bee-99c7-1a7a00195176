-- Create admin user and set password
CREATE USER IF NOT EXISTS 'admin'@'%' IDENTIFIED WITH mysql_native_password BY 'bakedbotpassword';

-- Create the database if it doesn't exist
CREATE DATABASE IF NOT EXISTS bakedbot;

-- Grant privileges to admin user
GRANT ALL PRIVILEGES ON bakedbot.* TO 'admin'@'%';

-- Set root user to use mysql_native_password
ALTER USER 'root'@'%' IDENTIFIED WITH mysql_native_password BY 'bakedbotpassword';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES; 