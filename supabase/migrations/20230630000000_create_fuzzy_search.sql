-- Enable the pg_trgm extension for fuzzy text matching if not already enabled
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Function to search retailers with fuzzy matching
-- This uses trigram similarity which is excellent for catching typos and misspellings
CREATE OR REPLACE FUNCTION search_retailers_fuzzy(
  search_term TEXT,
  similarity_threshold FLOAT DEFAULT 0.3,
  max_results INTEGER DEFAULT 10
)
RETURNS SETOF retailers
LANGUAGE plpgsql
AS $$
BEGIN
  -- Search across multiple fields with similarity scoring
  RETURN QUERY
  SELECT r.*
  FROM retailers r
  WHERE
    -- Check name similarity (higher weight - primary field)
    (similarity(LOWER(r.name), LOWER(search_term)) > similarity_threshold OR
     similarity(LOWER(r.dispensary_name), LOWER(search_term)) > similarity_threshold) OR
    -- Check address similarity (lower threshold - secondary fields)  
    (similarity(LOWER(r.address), LOWER(search_term)) > similarity_threshold - 0.1 OR
     similarity(LOWER(r.city), LOWER(search_term)) > similarity_threshold - 0.1)
  -- Use pattern matching as a fallback
  OR r.name ILIKE '%' || search_term || '%'
  OR r.dispensary_name ILIKE '%' || search_term || '%'
  -- Order by most relevant
  ORDER BY 
    -- Primary sort - higher weight for name fields
    GREATEST(
      similarity(LOWER(r.name), LOWER(search_term)),
      similarity(LOWER(r.dispensary_name), LOWER(search_term))
    ) DESC,
    -- Secondary sort - exact matches first
    CASE WHEN 
      r.name ILIKE '%' || search_term || '%' OR 
      r.dispensary_name ILIKE '%' || search_term || '%' 
    THEN 1 ELSE 0 END DESC
  LIMIT max_results;
END;
$$; 