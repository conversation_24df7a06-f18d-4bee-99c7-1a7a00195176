const mysql = require('mysql2');

const connection = mysql.createConnection({
  host: '127.0.0.1',
  port: 3306,
  user: 'root',
  password: 'bakedbotpassword',
  database: 'bakedbot',
  connectTimeout: 10000,
  acquireTimeout: 10000,
  timeout: 10000
});

console.log('Attempting to connect to MySQL...');

connection.connect((err) => {
  if (err) {
    console.error('Connection failed:', err);
    process.exit(1);
  }
  
  console.log('Connected to MySQL successfully!');
  
  connection.query('SELECT 1 as test', (err, results) => {
    if (err) {
      console.error('Query failed:', err);
    } else {
      console.log('Query successful:', results);
    }
    
    connection.end();
    process.exit(0);
  });
});
