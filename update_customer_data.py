import csv
import random
import pandas as pd
from datetime import datetime, timedelta
import numpy as np


def generate_email(name):
    name = name.lower().replace(" ", ".")
    domains = ["gmail.com", "yahoo.com", "hotmail.com", "outlook.com", "icloud.com"]
    return f"{name}@{random.choice(domains)}"


def generate_phone():
    area_codes = ["415", "510", "650", "925", "707"]
    return f"({random.choice(area_codes)}) {random.randint(100,999)}-{random.randint(1000,9999)}"


# Read existing CSV
df = pd.read_csv(
    "Synthetic Customer Data for BakedBot Upload - Synthetic Customer Data for BakedBot Upload.csv"
)

# Add email and phone columns
df["Email"] = df["Customer Name"].apply(generate_email)
df["Phone"] = [generate_phone() for _ in range(len(df))]

# Generate evenly distributed dates from 2023-01-01 to 2025-12-31
start_date = datetime(2023, 1, 1)
end_date = datetime(2025, 12, 31)
date_range = (end_date - start_date).days
num_records = len(df)

# Create evenly spaced indices
base_indices = np.linspace(0, date_range, num_records)
random_offset = np.random.randint(-5, 6, size=num_records)  # Add/subtract up to 5 days
indices = np.clip(base_indices + random_offset, 0, date_range)

# Generate dates and times
new_dates = [start_date + timedelta(days=int(idx)) for idx in indices]
new_dates.sort()  # Sort the dates in ascending order
hours = [random.randint(0, 23) for _ in range(num_records)]
minutes = [random.randint(0, 59) for _ in range(num_records)]

# Combine dates with random times
datetime_values = [
    d.replace(hour=h, minute=m) for d, h, m in zip(new_dates, hours, minutes)
]
datetime_values.sort()  # Sort including the times

# Update Order Date column
df["Order Date"] = [d.strftime("%Y-%m-%d %H:%M:%S") for d in datetime_values]

# Sort the entire dataframe by Order Date
df = df.sort_values("Order Date")

# Save the new CSV
df.to_csv("Synthetic_Customer_Data_01-01-2023_to_12-31-2025.csv", index=False)
print("Created new CSV with updated data")
