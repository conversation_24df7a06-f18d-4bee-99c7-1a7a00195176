import pandas as pd
import numpy as np

# Read the CSV file
df = pd.read_csv("Synthetic_Customer_Data_01-01-2023_to_12-31-2025 US.csv")

# Get the number of rows
num_rows = len(df)

# Create lists of email addresses for each person
dashon_emails = [f"dashon+{i}@bakedbot.ai" for i in range(1, num_rows + 1)]
martez_emails = [f"martez+{i}@bakedbot.ai" for i in range(1, num_rows + 1)]
alex_emails = [f"alex+{i}@bakedbot.ai" for i in range(1, num_rows + 1)]

# Combine all email lists
all_emails = dashon_emails + martez_emails + alex_emails

# Randomly select emails for each row
np.random.seed(42)  # For reproducibility
df["Email"] = np.random.choice(all_emails, size=num_rows, replace=False)

# Save the updated CSV
output_file = "Synthetic_Customer_Data_Updated.csv"
df.to_csv(output_file, index=False)

print(f"Updated CSV file saved as {output_file}")
